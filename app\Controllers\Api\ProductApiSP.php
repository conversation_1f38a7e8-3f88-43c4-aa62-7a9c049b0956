<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class ProductApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener productos con filtros usando SP
     */
    public function index()
    {
        try {
            $categoryId = $this->request->getGet('category_id');
            $brandId = $this->request->getGet('brand_id');
            $search = $this->request->getGet('search');
            $minPrice = $this->request->getGet('min_price');
            $maxPrice = $this->request->getGet('max_price');
            $isFeatured = $this->request->getGet('featured');
            $sortBy = $this->request->getGet('sort_by') ?? 'created';
            $sortOrder = $this->request->getGet('sort_order') ?? 'DESC';
            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);
            $offset = ($page - 1) * $limit;

            // Convertir featured a boolean
            $isFeatured = $isFeatured === 'true' ? 1 : ($isFeatured === 'false' ? 0 : null);

            $query = $this->db->query("CALL sp_get_products(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $categoryId, $brandId, $search, $minPrice, $maxPrice,
                $isFeatured, $sortBy, $sortOrder, $limit, $offset
            ]);

            $products = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'products' => $products,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($products)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::index: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener producto por ID usando SP
     */
    public function show($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('ID de producto requerido', 400);
            }

            $query = $this->db->query("CALL sp_get_product_by_id(?)", [(int) $id]);
            $product = $query->getRowArray();

            if (!$product) {
                return $this->failNotFound('Producto no encontrado');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::show: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Crear producto usando SP
     */
    public function create()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'sku' => 'required|max_length[50]',
                'name' => 'required|max_length[200]',
                'slug' => 'required|max_length[220]',
                'price_regular' => 'required|decimal|greater_than[0]',
                'stock_quantity' => 'required|integer|greater_than_equal_to[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_create_product(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @product_id, @result)", [
                $json['sku'],
                $json['name'],
                $json['slug'],
                $json['description'] ?? '',
                $json['short_description'] ?? '',
                $json['category_id'] ?? null,
                $json['brand_id'] ?? null,
                $json['vendor_id'] ?? null,
                $json['price_regular'],
                $json['price_sale'] ?? null,
                $json['stock_quantity'],
                $json['featured_image'] ?? '',
                $json['is_featured'] ?? false
            ]);

            $result = $this->db->query("SELECT @product_id as product_id, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => ['product_id' => $result->product_id]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::create: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Actualizar producto usando SP
     */
    public function update($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('ID de producto requerido', 400);
            }

            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'sku' => 'required|max_length[50]',
                'name' => 'required|max_length[200]',
                'slug' => 'required|max_length[220]',
                'price_regular' => 'required|decimal|greater_than[0]',
                'stock_quantity' => 'required|integer|greater_than_equal_to[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_update_product(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @result)", [
                (int) $id,
                $json['sku'],
                $json['name'],
                $json['slug'],
                $json['description'] ?? '',
                $json['short_description'] ?? '',
                $json['category_id'] ?? null,
                $json['brand_id'] ?? null,
                $json['price_regular'],
                $json['price_sale'] ?? null,
                $json['stock_quantity'],
                $json['featured_image'] ?? '',
                $json['is_featured'] ?? false,
                $json['is_active'] ?? true
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result)
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::update: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Búsqueda de productos
     */
    public function search()
    {
        try {
            $search = $this->request->getGet('q');
            $limit = (int) ($this->request->getGet('limit') ?? 10);
            
            if (!$search) {
                return $this->fail('Parámetro de búsqueda requerido', 400);
            }

            $query = $this->db->query("CALL sp_get_products(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                null, null, $search, null, null, null, 'name', 'ASC', $limit, 0
            ]);

            $products = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::search: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Productos por categoría
     */
    public function byCategory($categoryId = null)
    {
        try {
            if (!$categoryId || !is_numeric($categoryId)) {
                return $this->fail('ID de categoría requerido', 400);
            }

            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);
            $offset = ($page - 1) * $limit;

            $query = $this->db->query("CALL sp_get_products(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                (int) $categoryId, null, null, null, null, null, 'created', 'DESC', $limit, $offset
            ]);

            $products = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'products' => $products,
                    'category_id' => (int) $categoryId,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($products)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::byCategory: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Productos destacados
     */
    public function featured()
    {
        try {
            $limit = (int) ($this->request->getGet('limit') ?? 8);

            $query = $this->db->query("CALL sp_get_products(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                null, null, null, null, null, 1, 'featured', 'DESC', $limit, 0
            ]);

            $products = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductApiSP::featured: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }
}
