<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestWhatsApp extends BaseCommand
{
    protected $group       = 'WhatsApp';
    protected $name        = 'whatsapp:test';
    protected $description = 'Probar notificaciones de WhatsApp';

    public function run(array $params)
    {
        CLI::write('=== PRUEBA DE NOTIFICACIONES WHATSAPP ===', 'yellow');
        CLI::newLine();

        $testType = $params[0] ?? 'all';
        $phone = $params[1] ?? '+50688888888'; // Número de prueba

        try {
            $whatsappService = new \App\Services\WhatsAppService();

            switch ($testType) {
                case 'registration':
                case 'registro':
                    $this->testRegistrationNotification($whatsappService, $phone);
                    break;

                case 'order':
                case 'orden':
                    $this->testOrderNotification($whatsappService, $phone);
                    break;

                case 'all':
                case 'todo':
                default:
                    $this->testRegistrationNotification($whatsappService, $phone);
                    CLI::newLine();
                    $this->testOrderNotification($whatsappService, $phone);
                    break;
            }

        } catch (\Exception $e) {
            CLI::error('Error inicializando servicio de WhatsApp: ' . $e->getMessage());
            return;
        }

        CLI::newLine();
        CLI::write('=== PRUEBA COMPLETADA ===', 'yellow');
    }

    private function testRegistrationNotification($whatsappService, $phone)
    {
        CLI::write('🧪 Probando notificación de registro...', 'white');

        $userData = [
            'name' => 'Usuario de Prueba',
            'email' => '<EMAIL>',
            'phone' => $phone
        ];

        try {
            $result = $whatsappService->sendCustomerRegistrationNotification($userData);

            if ($result['success']) {
                CLI::write('   ✅ Notificación de registro enviada exitosamente', 'green');
                CLI::write('   📱 Enviado a: ' . $phone, 'white');
                if (isset($result['message_id'])) {
                    CLI::write('   🆔 ID del mensaje: ' . $result['message_id'], 'white');
                }
            } else {
                CLI::write('   ❌ Error enviando notificación de registro', 'red');
                CLI::write('   📝 Error: ' . ($result['error'] ?? 'Error desconocido'), 'red');
            }

        } catch (\Exception $e) {
            CLI::write('   ❌ Excepción enviando notificación de registro', 'red');
            CLI::write('   📝 Error: ' . $e->getMessage(), 'red');
        }
    }

    private function testOrderNotification($whatsappService, $phone)
    {
        CLI::write('🧪 Probando notificación de orden...', 'white');

        $orderData = [
            'id' => 999,
            'order_number' => 'MRC-TEST-' . date('Ymd-His'),
            'customer_name' => 'Cliente de Prueba',
            'customer_email' => '<EMAIL>',
            'customer_phone' => $phone,
            'total' => 299.99,
            'status' => 'pending'
        ];

        $customerData = [
            'name' => 'Cliente de Prueba',
            'phone' => $phone,
            'email' => '<EMAIL>'
        ];

        try {
            $result = $whatsappService->sendOrderCreatedNotification($orderData, $customerData);

            if ($result['success']) {
                CLI::write('   ✅ Notificación de orden enviada exitosamente', 'green');
                CLI::write('   📱 Enviado a: ' . $phone, 'white');
                CLI::write('   🛒 Orden: ' . $orderData['order_number'], 'white');
                if (isset($result['message_id'])) {
                    CLI::write('   🆔 ID del mensaje: ' . $result['message_id'], 'white');
                }
            } else {
                CLI::write('   ❌ Error enviando notificación de orden', 'red');
                CLI::write('   📝 Error: ' . ($result['error'] ?? 'Error desconocido'), 'red');
            }

        } catch (\Exception $e) {
            CLI::write('   ❌ Excepción enviando notificación de orden', 'red');
            CLI::write('   📝 Error: ' . $e->getMessage(), 'red');
        }
    }
}
