<?php

namespace App\Libraries;

/**
 * Generador de Documentación Automática
 * Sistema completo de documentación automática del código y APIs
 */
class DocumentationGenerator
{
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('DOCUMENTATION_ENABLED', true),
            'output_path' => WRITEPATH . 'documentation/',
            'formats' => ['html', 'markdown', 'json'],
            'include_private' => env('DOC_INCLUDE_PRIVATE', false),
            'include_examples' => env('DOC_INCLUDE_EXAMPLES', true),
            'auto_generate' => env('DOC_AUTO_GENERATE', true),
            'scan_paths' => [
                APPPATH . 'Controllers/',
                APPPATH . 'Libraries/',
                APPPATH . 'Models/'
            ]
        ];
        
        $this->ensureOutputDirectory();
    }
    
    /**
     * Generar documentación completa
     */
    public function generateFullDocumentation(): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'Documentation generation disabled'];
        }
        
        try {
            $startTime = microtime(true);
            
            $documentation = [
                'project' => $this->generateProjectInfo(),
                'api' => $this->generateApiDocumentation(),
                'controllers' => $this->generateControllersDocumentation(),
                'libraries' => $this->generateLibrariesDocumentation(),
                'models' => $this->generateModelsDocumentation(),
                'database' => $this->generateDatabaseDocumentation(),
                'configuration' => $this->generateConfigurationDocumentation(),
                'deployment' => $this->generateDeploymentDocumentation()
            ];
            
            $generationTime = microtime(true) - $startTime;
            
            // Generar archivos en diferentes formatos
            $files = [];
            foreach ($this->config['formats'] as $format) {
                $filename = $this->saveDocumentation($documentation, $format);
                if ($filename) {
                    $files[] = $filename;
                }
            }
            
            $this->logger->info("Documentation generated successfully", [
                'generation_time' => $generationTime,
                'files_generated' => count($files),
                'formats' => $this->config['formats']
            ]);
            
            return [
                'success' => true,
                'generation_time' => round($generationTime, 2),
                'files' => $files,
                'documentation' => $documentation
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Documentation generation error: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generar información del proyecto
     */
    private function generateProjectInfo(): array
    {
        return [
            'name' => 'MrCell Guatemala E-commerce System',
            'version' => '2.0.0',
            'description' => 'Sistema completo de ecommerce con funcionalidades enterprise avanzadas',
            'framework' => 'CodeIgniter 4',
            'php_version' => PHP_VERSION,
            'features' => [
                'PWA (Progressive Web App)',
                'Sistema de notificaciones push',
                'Automatizaciones 24/7',
                'Sistema de backup avanzado',
                'Optimización SEO completa',
                'Sistema de logs enterprise',
                'Cache inteligente multinivel',
                'Configuración dinámica',
                'Reportes automáticos',
                'Seguridad avanzada',
                'API REST completa',
                'Sistema de webhooks',
                'Integraciones con terceros',
                'Documentación automática'
            ],
            'architecture' => [
                'pattern' => 'MVC (Model-View-Controller)',
                'database' => 'MySQL',
                'cache' => 'File/Redis/Memcached',
                'session' => 'Database/File',
                'logging' => 'File/Database'
            ],
            'requirements' => [
                'php' => '>=7.4',
                'mysql' => '>=5.7',
                'extensions' => ['curl', 'json', 'mbstring', 'openssl', 'gd']
            ]
        ];
    }
    
    /**
     * Generar documentación de API
     */
    private function generateApiDocumentation(): array
    {
        return [
            'base_url' => base_url('api/v1'),
            'authentication' => [
                'type' => 'API Key',
                'header' => 'X-API-Key',
                'description' => 'Incluir API key en el header X-API-Key'
            ],
            'endpoints' => [
                'products' => [
                    'path' => '/products',
                    'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                    'description' => 'Gestión de productos',
                    'examples' => [
                        'GET /products' => [
                            'description' => 'Obtener lista de productos',
                            'parameters' => [
                                'page' => 'Número de página (opcional)',
                                'limit' => 'Límite de resultados (opcional)',
                                'category' => 'Filtrar por categoría (opcional)'
                            ],
                            'response' => [
                                'success' => true,
                                'data' => [
                                    'products' => [
                                        [
                                            'id' => 1,
                                            'name' => 'iPhone 15 Pro',
                                            'price' => 999.99,
                                            'category' => 'Smartphones'
                                        ]
                                    ],
                                    'pagination' => [
                                        'page' => 1,
                                        'limit' => 20,
                                        'total' => 150
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'orders' => [
                    'path' => '/orders',
                    'methods' => ['GET', 'POST'],
                    'description' => 'Gestión de órdenes'
                ],
                'users' => [
                    'path' => '/users',
                    'methods' => ['GET'],
                    'description' => 'Gestión de usuarios'
                ]
            ],
            'error_codes' => [
                '400' => 'Bad Request - Parámetros inválidos',
                '401' => 'Unauthorized - API key inválida o faltante',
                '403' => 'Forbidden - Permisos insuficientes',
                '404' => 'Not Found - Endpoint no encontrado',
                '429' => 'Too Many Requests - Límite de velocidad excedido',
                '500' => 'Internal Server Error - Error interno del servidor'
            ]
        ];
    }
    
    /**
     * Generar documentación de controladores
     */
    private function generateControllersDocumentation(): array
    {
        $controllers = [];
        
        $controllerFiles = $this->scanDirectory(APPPATH . 'Controllers/');
        
        foreach ($controllerFiles as $file) {
            $className = $this->getClassNameFromFile($file);
            if ($className) {
                $controllers[$className] = $this->analyzeController($file, $className);
            }
        }
        
        return $controllers;
    }
    
    /**
     * Generar documentación de librerías
     */
    private function generateLibrariesDocumentation(): array
    {
        $libraries = [];
        
        $libraryFiles = $this->scanDirectory(APPPATH . 'Libraries/');
        
        foreach ($libraryFiles as $file) {
            $className = $this->getClassNameFromFile($file);
            if ($className) {
                $libraries[$className] = $this->analyzeLibrary($file, $className);
            }
        }
        
        return $libraries;
    }
    
    /**
     * Generar documentación de modelos
     */
    private function generateModelsDocumentation(): array
    {
        $models = [];
        
        $modelFiles = $this->scanDirectory(APPPATH . 'Models/');
        
        foreach ($modelFiles as $file) {
            $className = $this->getClassNameFromFile($file);
            if ($className) {
                $models[$className] = $this->analyzeModel($file, $className);
            }
        }
        
        return $models;
    }
    
    /**
     * Generar documentación de base de datos
     */
    private function generateDatabaseDocumentation(): array
    {
        return [
            'tables' => [
                'users' => [
                    'description' => 'Tabla de usuarios del sistema',
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
                        'name' => 'VARCHAR(255) NOT NULL',
                        'email' => 'VARCHAR(255) UNIQUE NOT NULL',
                        'password' => 'VARCHAR(255) NOT NULL',
                        'is_active' => 'TINYINT(1) DEFAULT 1',
                        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                    ],
                    'indexes' => ['email', 'is_active']
                ],
                'products' => [
                    'description' => 'Tabla de productos',
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
                        'name' => 'VARCHAR(255) NOT NULL',
                        'description' => 'TEXT',
                        'price' => 'DECIMAL(10,2) NOT NULL',
                        'stock' => 'INT DEFAULT 0',
                        'category_id' => 'INT',
                        'is_active' => 'TINYINT(1) DEFAULT 1'
                    ],
                    'foreign_keys' => [
                        'category_id' => 'categories(id)'
                    ]
                ],
                'orders' => [
                    'description' => 'Tabla de órdenes',
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
                        'user_id' => 'INT NOT NULL',
                        'total' => 'DECIMAL(10,2) NOT NULL',
                        'status' => 'VARCHAR(50) DEFAULT "pending"',
                        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                    ],
                    'foreign_keys' => [
                        'user_id' => 'users(id)'
                    ]
                ]
            ],
            'relationships' => [
                'users -> orders' => 'One to Many',
                'categories -> products' => 'One to Many',
                'orders -> order_items' => 'One to Many'
            ]
        ];
    }
    
    /**
     * Generar documentación de configuración
     */
    private function generateConfigurationDocumentation(): array
    {
        return [
            'environment_variables' => [
                'database' => [
                    'DB_HOST' => 'Host de la base de datos',
                    'DB_NAME' => 'Nombre de la base de datos',
                    'DB_USER' => 'Usuario de la base de datos',
                    'DB_PASS' => 'Contraseña de la base de datos'
                ],
                'cache' => [
                    'CACHE_DRIVER' => 'Driver de cache (file, redis, memcached)',
                    'CACHE_TTL_DEFAULT' => 'TTL por defecto en segundos',
                    'REDIS_HOST' => 'Host de Redis (si se usa)',
                    'REDIS_PORT' => 'Puerto de Redis'
                ],
                'security' => [
                    'SECURITY_ENABLED' => 'Habilitar sistema de seguridad',
                    'BRUTE_FORCE_PROTECTION' => 'Protección contra fuerza bruta',
                    'MAX_LOGIN_ATTEMPTS' => 'Máximo intentos de login',
                    'RATE_LIMITING_ENABLED' => 'Habilitar rate limiting'
                ],
                'integrations' => [
                    'PAYPAL_ENABLED' => 'Habilitar integración PayPal',
                    'STRIPE_ENABLED' => 'Habilitar integración Stripe',
                    'WHATSAPP_ENABLED' => 'Habilitar WhatsApp Business',
                    'MAILCHIMP_ENABLED' => 'Habilitar Mailchimp'
                ]
            ],
            'configuration_files' => [
                'app/Config/App.php' => 'Configuración principal de la aplicación',
                'app/Config/Database.php' => 'Configuración de base de datos',
                'app/Config/Routes.php' => 'Definición de rutas',
                'app/Config/Filters.php' => 'Filtros de la aplicación'
            ]
        ];
    }
    
    /**
     * Generar documentación de despliegue
     */
    private function generateDeploymentDocumentation(): array
    {
        return [
            'requirements' => [
                'server' => [
                    'php' => '>=7.4',
                    'mysql' => '>=5.7',
                    'apache/nginx' => 'Servidor web',
                    'ssl' => 'Certificado SSL recomendado'
                ],
                'php_extensions' => [
                    'curl', 'json', 'mbstring', 'openssl', 'gd', 'zip'
                ]
            ],
            'installation_steps' => [
                '1. Clonar repositorio',
                '2. Instalar dependencias con Composer',
                '3. Configurar archivo .env',
                '4. Crear base de datos',
                '5. Ejecutar migraciones',
                '6. Configurar permisos de directorios',
                '7. Configurar cron jobs',
                '8. Configurar servidor web'
            ],
            'cron_jobs' => [
                '*/5 * * * *' => 'Verificación del sistema',
                '*/15 * * * *' => 'Automatizaciones',
                '*/30 * * * *' => 'Notificaciones push',
                '0 2 * * *' => 'Backup automático',
                '0 3 * * *' => 'Mantenimiento del sistema'
            ],
            'security_considerations' => [
                'Configurar firewall',
                'Habilitar SSL/TLS',
                'Configurar headers de seguridad',
                'Restringir acceso a archivos sensibles',
                'Configurar rate limiting',
                'Monitorear logs de seguridad'
            ]
        ];
    }
    
    /**
     * Guardar documentación en formato específico
     */
    private function saveDocumentation(array $documentation, string $format): ?string
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "mrcell_documentation_{$timestamp}.{$format}";
            $filepath = $this->config['output_path'] . $filename;
            
            switch ($format) {
                case 'json':
                    file_put_contents($filepath, json_encode($documentation, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    break;
                case 'markdown':
                    $content = $this->convertToMarkdown($documentation);
                    file_put_contents($filepath, $content);
                    break;
                case 'html':
                    $content = $this->convertToHTML($documentation);
                    file_put_contents($filepath, $content);
                    break;
                default:
                    return null;
            }
            
            return $filename;
            
        } catch (\Exception $e) {
            $this->logger->error("Documentation save error: " . $e->getMessage(), [
                'format' => $format
            ]);
            return null;
        }
    }
    
    /**
     * Convertir documentación a Markdown
     */
    private function convertToMarkdown(array $documentation): string
    {
        $md = "# MrCell Guatemala - Documentación del Sistema\n\n";
        $md .= "Generado automáticamente el " . date('Y-m-d H:i:s') . "\n\n";
        
        // Información del proyecto
        $md .= "## Información del Proyecto\n\n";
        $md .= "- **Nombre:** {$documentation['project']['name']}\n";
        $md .= "- **Versión:** {$documentation['project']['version']}\n";
        $md .= "- **Framework:** {$documentation['project']['framework']}\n";
        $md .= "- **PHP:** {$documentation['project']['php_version']}\n\n";
        
        // Características
        $md .= "### Características Principales\n\n";
        foreach ($documentation['project']['features'] as $feature) {
            $md .= "- $feature\n";
        }
        $md .= "\n";
        
        // API
        $md .= "## Documentación de API\n\n";
        $md .= "**URL Base:** {$documentation['api']['base_url']}\n\n";
        $md .= "### Autenticación\n\n";
        $md .= "- **Tipo:** {$documentation['api']['authentication']['type']}\n";
        $md .= "- **Header:** {$documentation['api']['authentication']['header']}\n\n";
        
        // Endpoints
        $md .= "### Endpoints Disponibles\n\n";
        foreach ($documentation['api']['endpoints'] as $name => $endpoint) {
            $md .= "#### $name\n\n";
            $md .= "- **Path:** {$endpoint['path']}\n";
            $md .= "- **Métodos:** " . implode(', ', $endpoint['methods']) . "\n";
            $md .= "- **Descripción:** {$endpoint['description']}\n\n";
        }
        
        return $md;
    }
    
    /**
     * Convertir documentación a HTML
     */
    private function convertToHTML(array $documentation): string
    {
        $html = "<!DOCTYPE html>\n<html>\n<head>\n";
        $html .= "<title>MrCell Guatemala - Documentación</title>\n";
        $html .= "<meta charset='utf-8'>\n";
        $html .= "<style>body{font-family:Arial,sans-serif;margin:40px;} h1,h2,h3{color:#333;} code{background:#f4f4f4;padding:2px 4px;}</style>\n";
        $html .= "</head>\n<body>\n";
        
        $html .= "<h1>MrCell Guatemala - Documentación del Sistema</h1>\n";
        $html .= "<p><em>Generado automáticamente el " . date('Y-m-d H:i:s') . "</em></p>\n";
        
        $html .= "<h2>Información del Proyecto</h2>\n";
        $html .= "<ul>\n";
        $html .= "<li><strong>Nombre:</strong> {$documentation['project']['name']}</li>\n";
        $html .= "<li><strong>Versión:</strong> {$documentation['project']['version']}</li>\n";
        $html .= "<li><strong>Framework:</strong> {$documentation['project']['framework']}</li>\n";
        $html .= "</ul>\n";
        
        $html .= "<h3>Características</h3>\n<ul>\n";
        foreach ($documentation['project']['features'] as $feature) {
            $html .= "<li>$feature</li>\n";
        }
        $html .= "</ul>\n";
        
        $html .= "</body>\n</html>";
        
        return $html;
    }
    
    /**
     * Métodos auxiliares
     */
    private function scanDirectory(string $path): array
    {
        $files = [];
        if (is_dir($path)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $files[] = $file->getPathname();
                }
            }
        }
        return $files;
    }
    
    private function getClassNameFromFile(string $file): ?string
    {
        $content = file_get_contents($file);
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    private function analyzeController(string $file, string $className): array
    {
        return [
            'file' => $file,
            'description' => 'Controlador del sistema',
            'methods' => $this->extractMethods($file),
            'routes' => $this->extractRoutes($className)
        ];
    }
    
    private function analyzeLibrary(string $file, string $className): array
    {
        return [
            'file' => $file,
            'description' => 'Librería del sistema',
            'methods' => $this->extractMethods($file),
            'dependencies' => $this->extractDependencies($file)
        ];
    }
    
    private function analyzeModel(string $file, string $className): array
    {
        return [
            'file' => $file,
            'description' => 'Modelo de datos',
            'table' => $this->extractTableName($file),
            'methods' => $this->extractMethods($file)
        ];
    }
    
    private function extractMethods(string $file): array
    {
        $content = file_get_contents($file);
        $methods = [];
        
        if (preg_match_all('/(?:public|private|protected)\s+function\s+(\w+)\s*\([^)]*\)/', $content, $matches)) {
            foreach ($matches[1] as $method) {
                if (!in_array($method, ['__construct', '__destruct'])) {
                    $methods[] = $method;
                }
            }
        }
        
        return $methods;
    }
    
    private function extractRoutes(string $className): array
    {
        // Simular extracción de rutas
        return [];
    }
    
    private function extractDependencies(string $file): array
    {
        $content = file_get_contents($file);
        $dependencies = [];
        
        if (preg_match_all('/use\s+([^;]+);/', $content, $matches)) {
            $dependencies = $matches[1];
        }
        
        return $dependencies;
    }
    
    private function extractTableName(string $file): ?string
    {
        $content = file_get_contents($file);
        if (preg_match('/protected\s+\$table\s*=\s*[\'"]([^\'"]+)[\'"]/', $content, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    private function ensureOutputDirectory(): void
    {
        if (!is_dir($this->config['output_path'])) {
            mkdir($this->config['output_path'], 0755, true);
        }
    }
    
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'output_path' => $this->config['output_path'],
            'formats' => $this->config['formats'],
            'auto_generate' => $this->config['auto_generate']
        ];
    }
}
