<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AdvancedLogger;

class SocialManagerController extends BaseController
{
    protected $db;
    protected $logger;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->logger = new AdvancedLogger();
    }

    /**
     * Mostrar configuración de Social Manager
     */
    public function index()
    {
        try {
            // Verificar permisos de administrador
            if (!session('admin_id')) {
                return redirect()->to('/admin/login');
            }

            // Obtener configuración actual
            $config = $this->getSocialConfig();
            
            // Obtener estadísticas de publicaciones
            $stats = $this->getPublishingStats();

            $data = [
                'title' => 'Configuración de Social Manager',
                'config' => $config,
                'stats' => $stats,
                'test_connection_result' => session()->getFlashdata('test_result')
            ];

            return view('admin/social/config', $data);

        } catch (\Exception $e) {
            $this->logger->error('Error loading social config: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error al cargar la configuración');
            return redirect()->to('/admin/dashboard');
        }
    }

    /**
     * Actualizar configuración de Social Manager
     */
    public function update()
    {
        try {
            // Validar datos de entrada
            $validation = \Config\Services::validation();
            $validation->setRules([
                'api_url' => 'required|valid_url',
                'api_key' => 'required|min_length[10]',
                'enabled_platforms' => 'permit_empty',
                'auto_publish' => 'permit_empty|in_list[1]',
                'publish_new_products' => 'permit_empty|in_list[1]',
                'publish_promotions' => 'permit_empty|in_list[1]',
                'default_hashtags' => 'permit_empty|max_length[500]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                session()->setFlashdata('error', 'Datos inválidos: ' . implode(', ', $validation->getErrors()));
                return redirect()->back()->withInput();
            }

            // Procesar plataformas habilitadas
            $enabledPlatforms = $this->request->getPost('enabled_platforms') ?: [];
            
            // Procesar horarios de publicación
            $postingSchedule = $this->processPostingSchedule();
            
            // Procesar plantillas de contenido
            $contentTemplates = $this->processContentTemplates();

            // Preparar datos de configuración
            $configData = [
                'api_url' => $this->request->getPost('api_url'),
                'api_key' => $this->request->getPost('api_key'),
                'enabled_platforms' => json_encode($enabledPlatforms),
                'auto_publish' => $this->request->getPost('auto_publish') ? 1 : 0,
                'publish_new_products' => $this->request->getPost('publish_new_products') ? 1 : 0,
                'publish_promotions' => $this->request->getPost('publish_promotions') ? 1 : 0,
                'default_hashtags' => $this->request->getPost('default_hashtags'),
                'posting_schedule' => json_encode($postingSchedule),
                'content_templates' => json_encode($contentTemplates),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_by' => session('admin_id')
            ];

            // Actualizar o insertar configuración
            $existingConfig = $this->getSocialConfig();
            
            if ($existingConfig) {
                $result = $this->db->table('social_manager_config')
                                  ->where('id', $existingConfig['id'])
                                  ->update($configData);
            } else {
                $configData['created_at'] = date('Y-m-d H:i:s');
                $configData['created_by'] = session('admin_id');
                $result = $this->db->table('social_manager_config')->insert($configData);
            }

            if ($result) {
                // Log de la acción
                $this->logger->userAction('social_config_updated', session('admin_id'), [
                    'platforms' => $enabledPlatforms,
                    'auto_publish' => $configData['auto_publish']
                ]);

                session()->setFlashdata('success', 'Configuración actualizada exitosamente');
            } else {
                session()->setFlashdata('error', 'Error al actualizar la configuración');
            }

            return redirect()->to('/admin/social/config');

        } catch (\Exception $e) {
            $this->logger->error('Error updating social config: ' . $e->getMessage(), [
                'admin_id' => session('admin_id')
            ]);
            
            session()->setFlashdata('error', 'Error interno: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Probar conexión con Social Manager API
     */
    public function testConnection()
    {
        try {
            $config = $this->getSocialConfig();
            
            if (!$config) {
                session()->setFlashdata('test_result', [
                    'success' => false,
                    'message' => 'No hay configuración guardada'
                ]);
                return redirect()->back();
            }

            // Realizar prueba de conexión
            $testResult = $this->performConnectionTest($config);
            
            session()->setFlashdata('test_result', $testResult);
            return redirect()->back();

        } catch (\Exception $e) {
            session()->setFlashdata('test_result', [
                'success' => false,
                'message' => 'Error en la prueba: ' . $e->getMessage()
            ]);
            return redirect()->back();
        }
    }

    /**
     * Ver publicaciones programadas
     */
    public function scheduledPosts()
    {
        try {
            $page = $this->request->getGet('page') ?: 1;
            $perPage = 20;
            
            // Obtener publicaciones con paginación
            $posts = $this->db->table('scheduled_posts sp')
                             ->select('sp.*, p.name as product_name, pc.name as promotion_name')
                             ->join('products p', 'p.id = sp.product_id', 'left')
                             ->join('promotional_campaigns pc', 'pc.id = sp.promotion_id', 'left')
                             ->orderBy('sp.scheduled_for', 'ASC')
                             ->paginate($perPage, 'default', $page);

            $pager = $this->db->table('scheduled_posts')->pager;

            $data = [
                'title' => 'Publicaciones Programadas',
                'posts' => $posts,
                'pager' => $pager
            ];

            return view('admin/social/scheduled', $data);

        } catch (\Exception $e) {
            $this->logger->error('Error loading scheduled posts: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error al cargar las publicaciones');
            return redirect()->to('/admin/social/config');
        }
    }

    /**
     * Crear nueva publicación programada
     */
    public function createPost()
    {
        try {
            // Obtener productos y promociones para el formulario
            $products = $this->db->table('products')
                                ->select('id, name, price, image')
                                ->where('is_active', 1)
                                ->orderBy('name')
                                ->get()
                                ->getResultArray();

            $promotions = $this->db->table('promotional_campaigns')
                                  ->select('id, name, description, value, type')
                                  ->where('is_active', 1)
                                  ->where('end_date >', date('Y-m-d H:i:s'))
                                  ->orderBy('name')
                                  ->get()
                                  ->getResultArray();

            $config = $this->getSocialConfig();
            $enabledPlatforms = $config ? json_decode($config['enabled_platforms'], true) : [];

            $data = [
                'title' => 'Crear Publicación',
                'products' => $products,
                'promotions' => $promotions,
                'enabled_platforms' => $enabledPlatforms,
                'default_hashtags' => $config['default_hashtags'] ?? ''
            ];

            return view('admin/social/create_post', $data);

        } catch (\Exception $e) {
            $this->logger->error('Error loading create post form: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error al cargar el formulario');
            return redirect()->to('/admin/social/scheduled');
        }
    }

    /**
     * Guardar nueva publicación programada
     */
    public function storePost()
    {
        try {
            // Validar datos
            $validation = \Config\Services::validation();
            $validation->setRules([
                'post_type' => 'required|in_list[product,promotion,custom]',
                'platforms' => 'required',
                'content' => 'required|min_length[10]',
                'scheduled_for' => 'required|valid_date'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                session()->setFlashdata('error', 'Datos inválidos: ' . implode(', ', $validation->getErrors()));
                return redirect()->back()->withInput();
            }

            // Preparar datos de la publicación
            $postData = [
                'post_type' => $this->request->getPost('post_type'),
                'product_id' => $this->request->getPost('product_id') ?: null,
                'promotion_id' => $this->request->getPost('promotion_id') ?: null,
                'platforms' => json_encode($this->request->getPost('platforms')),
                'content' => $this->request->getPost('content'),
                'hashtags' => $this->request->getPost('hashtags'),
                'scheduled_for' => $this->request->getPost('scheduled_for'),
                'status' => 'pending',
                'created_by' => session('admin_id'),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Procesar imágenes si las hay
            $images = $this->processPostImages();
            if (!empty($images)) {
                $postData['images'] = json_encode($images);
            }

            $result = $this->db->table('scheduled_posts')->insert($postData);

            if ($result) {
                session()->setFlashdata('success', 'Publicación programada exitosamente');
                return redirect()->to('/admin/social/scheduled');
            } else {
                session()->setFlashdata('error', 'Error al programar la publicación');
                return redirect()->back()->withInput();
            }

        } catch (\Exception $e) {
            $this->logger->error('Error storing scheduled post: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error interno: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Métodos privados auxiliares
     */
    private function getSocialConfig(): ?array
    {
        return $this->db->table('social_manager_config')
                       ->orderBy('id', 'DESC')
                       ->limit(1)
                       ->get()
                       ->getRowArray();
    }

    private function getPublishingStats(): array
    {
        $stats = [
            'total_posts' => $this->db->table('scheduled_posts')->countAllResults(),
            'pending_posts' => $this->db->table('scheduled_posts')->where('status', 'pending')->countAllResults(),
            'published_posts' => $this->db->table('scheduled_posts')->where('status', 'published')->countAllResults(),
            'failed_posts' => $this->db->table('scheduled_posts')->where('status', 'failed')->countAllResults()
        ];

        return $stats;
    }

    private function performConnectionTest(array $config): array
    {
        try {
            $client = \Config\Services::curlrequest();
            
            $response = $client->request('GET', $config['api_url'] . '/status', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $config['api_key'],
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 30
            ]);

            if ($response->getStatusCode() === 200) {
                $body = json_decode($response->getBody(), true);
                
                return [
                    'success' => true,
                    'message' => 'Conexión exitosa con Social Manager API',
                    'details' => $body['message'] ?? 'API respondió correctamente'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Error de conexión: HTTP ' . $response->getStatusCode(),
                    'details' => $response->getBody()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error de conexión: ' . $e->getMessage(),
                'details' => 'Verifique la URL y credenciales de la API'
            ];
        }
    }

    private function processPostingSchedule(): array
    {
        // Procesar horarios de publicación desde el formulario
        $schedule = [];
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        foreach ($days as $day) {
            $times = $this->request->getPost($day . '_times') ?: [];
            if (!empty($times)) {
                $schedule[$day] = $times;
            }
        }
        
        return $schedule;
    }

    private function processContentTemplates(): array
    {
        // Procesar plantillas de contenido
        $templates = [
            'new_product' => $this->request->getPost('template_new_product') ?: '',
            'promotion' => $this->request->getPost('template_promotion') ?: '',
            'sale' => $this->request->getPost('template_sale') ?: ''
        ];
        
        return array_filter($templates);
    }

    private function processPostImages(): array
    {
        // Procesar imágenes subidas para la publicación
        $images = [];
        
        if ($this->request->getFiles()) {
            foreach ($this->request->getFiles()['images'] as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $newName = $file->getRandomName();
                    $file->move(WRITEPATH . 'uploads/social/', $newName);
                    $images[] = $newName;
                }
            }
        }
        
        return $images;
    }
}
