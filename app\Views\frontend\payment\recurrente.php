<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? '<PERSON><PERSON> con Recurrente - MrCell Guatemala' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --recurrente-primary: #6366f1;
            --recurrente-secondary: #8b5cf6;
            --recurrente-success: #10b981;
            --recurrente-warning: #f59e0b;
        }
        
        .payment-container {
            background: linear-gradient(135deg, var(--recurrente-primary) 0%, var(--recurrente-secondary) 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .payment-header {
            background: linear-gradient(135deg, var(--recurrente-primary), var(--recurrente-secondary));
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .recurrente-logo {
            font-size: 2rem;
            color: white;
            margin-bottom: 10px;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .amount-display {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--recurrente-primary);
            text-align: center;
            margin: 20px 0;
        }
        
        .payment-form {
            padding: 30px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--recurrente-primary);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .btn-recurrente {
            background: linear-gradient(135deg, var(--recurrente-primary), var(--recurrente-secondary));
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-recurrente:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
            color: white;
        }
        
        .security-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 300px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--recurrente-primary);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .card-icons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        
        .card-icon {
            width: 40px;
            height: 25px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .visa { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwNTFBNSIvPgo8cGF0aCBkPSJNMTYuNzUgN0gxNC4yNUwxMi41IDE3SDE1TDE2Ljc1IDdaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'); }
        .mastercard { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI0VCMDAxQiIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjEyIiByPSI3IiBmaWxsPSIjRkY1RjAwIi8+CjxjaXJjbGUgY3g9IjI1IiBjeT0iMTIiIHI9IjciIGZpbGw9IiNGRkY1RjAiLz4KPC9zdmc+Cg=='); }
        .amex { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwNkZDRiIvPgo8dGV4dCB4PSIyMCIgeT0iMTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QU1FWDwvdGV4dD4KPC9zdmc+Cg=='); }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="container">
            <div class="payment-card">
                <!-- Header -->
                <div class="payment-header">
                    <div class="recurrente-logo">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h2 class="mb-0">Pago Seguro con Recurrente</h2>
                    <p class="mb-0">Procesamiento seguro de pagos</p>
                </div>

                <!-- Resumen del pedido -->
                <div class="payment-form">
                    <div class="order-summary">
                        <h5><i class="fas fa-shopping-cart me-2"></i>Resumen del Pedido</h5>
                        <div class="d-flex justify-content-between">
                            <span>Pedido #<?= esc($order['order_number']) ?></span>
                            <strong>Q <?= number_format($order['total'], 2) ?></strong>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <span>Cliente:</span>
                            <span><?= esc($order['customer_name']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Email:</span>
                            <span><?= esc($order['customer_email']) ?></span>
                        </div>
                    </div>

                    <div class="amount-display">
                        Q <?= number_format($order['total'], 2) ?>
                    </div>

                    <!-- Iconos de tarjetas aceptadas -->
                    <div class="card-icons">
                        <div class="card-icon visa"></div>
                        <div class="card-icon mastercard"></div>
                        <div class="card-icon amex"></div>
                    </div>

                    <!-- Formulario de pago -->
                    <form id="recurrentePaymentForm">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="cardNumber" class="form-label">Número de Tarjeta</label>
                                    <input type="text" class="form-control" id="cardNumber" name="card_number" 
                                           placeholder="1234 5678 9012 3456" maxlength="19" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="cardHolder" class="form-label">Nombre del Titular</label>
                                    <input type="text" class="form-control" id="cardHolder" name="card_holder" 
                                           placeholder="Nombre como aparece en la tarjeta" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="expMonth" class="form-label">Mes</label>
                                    <select class="form-control" id="expMonth" name="exp_month" required>
                                        <option value="">MM</option>
                                        <?php for($i = 1; $i <= 12; $i++): ?>
                                            <option value="<?= sprintf('%02d', $i) ?>"><?= sprintf('%02d', $i) ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="expYear" class="form-label">Año</label>
                                    <select class="form-control" id="expYear" name="exp_year" required>
                                        <option value="">AAAA</option>
                                        <?php for($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                            <option value="<?= $i ?>"><?= $i ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="cvv" class="form-label">CVV</label>
                                    <input type="text" class="form-control" id="cvv" name="cvv" 
                                           placeholder="123" maxlength="4" required>
                                </div>
                            </div>
                        </div>

                        <!-- Información de seguridad -->
                        <div class="security-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                <small>
                                    <strong>Pago 100% Seguro:</strong> Tus datos están protegidos con encriptación SSL de 256 bits.
                                    Recurrente cumple con los estándares PCI DSS.
                                </small>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-recurrente">
                            <i class="fas fa-lock me-2"></i>Procesar Pago Seguro
                        </button>

                        <div class="text-center mt-3">
                            <a href="<?= base_url('checkout') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver al Checkout
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h5>Procesando Pago</h5>
            <p>Por favor espera mientras procesamos tu pago de forma segura...</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuración
        const orderData = <?= json_encode($order) ?>;
        const recurrenteConfig = <?= json_encode($recurrente_config ?? []) ?>;

        // Formatear número de tarjeta
        document.getElementById('cardNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
            e.target.value = formattedValue;
        });

        // Solo números en CVV
        document.getElementById('cvv').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // Procesar formulario
        document.getElementById('recurrentePaymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            processPayment();
        });

        function processPayment() {
            showLoading();

            const formData = new FormData(document.getElementById('recurrentePaymentForm'));
            const paymentData = {
                order_id: orderData.id,
                card_number: formData.get('card_number').replace(/\s/g, ''),
                card_holder: formData.get('card_holder'),
                exp_month: parseInt(formData.get('exp_month')),
                exp_year: parseInt(formData.get('exp_year')),
                cvv: formData.get('cvv'),
                amount: orderData.total
            };

            fetch('<?= base_url('api/payments/recurrente/process') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData)
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.status === 'success') {
                    window.location.href = '<?= base_url('payment/success/') ?>' + orderData.id;
                } else {
                    alert('Error al procesar el pago: ' + (data.message || 'Error desconocido'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                alert('Error de conexión. Por favor intenta de nuevo.');
            });
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
    </script>
</body>
</html>
