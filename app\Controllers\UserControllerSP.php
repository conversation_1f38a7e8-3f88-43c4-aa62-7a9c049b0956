<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\OrderModel;
use App\Models\ProductModel;

class UserControllerSP extends BaseController
{
    protected $userModel;
    protected $orderModel;
    protected $productModel;
    protected $session;

    public function __construct()
    {
        // Constructor simplificado - models will be initialized in initController
    }

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        // Call parent initController
        parent::initController($request, $response, $logger);

        // Initialize models and session
        $this->userModel = new UserModel();
        $this->orderModel = new OrderModel();
        $this->productModel = new ProductModel();
        $this->session = session();
    }

    /**
     * Página de login
     */
    public function login()
    {
        log_message('debug', 'UserControllerSP::login method called');

        // Si ya está logueado, redirigir al dashboard
        if ($this->session->get('user_id')) {
            return redirect()->to('/cuenta');
        }

        $data = [
            'title' => 'Iniciar Sesión - MrCell Guatemala',
            'page_title' => 'Iniciar Sesión'
        ];

        log_message('debug', 'Loading frontend/auth/login view');
        return view('frontend/auth/login', $data);
    }

    /**
     * Procesar login usando SP
     */
    public function authenticate()
    {
        log_message('debug', 'UserControllerSP::authenticate method called');
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        try {
            $db = \Config\Database::connect();

            // Usar stored procedure para login
            $query = $db->query("CALL sp_login_user(?, @user_id, @user_data, @result)", [$email]);
            $result = $db->query("SELECT @user_id as user_id, @user_data as user_data, @result as result")->getRow();

            log_message('debug', "Login attempt for email: $email");
            log_message('debug', "SP Result: " . $result->result);

            if (strpos($result->result, 'SUCCESS') === 0 && $result->user_id > 0) {
                // Obtener usuario completo para verificar contraseña
                $user = $this->userModel->find($result->user_id);

                if ($user && password_verify($password, $user['password'])) {
                    // Login exitoso
                    $fullName = trim($user['first_name'] . ' ' . $user['last_name']);
                    $sessionData = [
                        'user_id' => $user['id'],
                        'user_name' => $fullName,
                        'user_email' => $user['email'],
                        'is_logged_in' => true
                    ];

                    $this->session->set($sessionData);
                    log_message('debug', "Login successful for user: $fullName");
                    return redirect()->to('/cuenta')->with('success', 'Bienvenido de vuelta, ' . $fullName);
                } else {
                    log_message('debug', "Password verification failed");
                    return redirect()->back()->withInput()->with('error', 'Email o contraseña incorrectos');
                }
            } else {
                // Manejar errores específicos del SP
                $errorMessage = str_replace('ERROR: ', '', $result->result);
                if (strpos($errorMessage, 'pendiente de verificación') !== false) {
                    return redirect()->to('/verify-phone')->with('error', $errorMessage);
                }
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en login: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al iniciar sesión. Inténtalo de nuevo.');
        }
    }

    /**
     * Página de registro
     */
    public function register()
    {
        $data = [
            'title' => 'Crear Cuenta - MrCell Guatemala',
            'page_title' => 'Crear Cuenta'
        ];

        return view('frontend/auth/register', $data);
    }

    /**
     * Test endpoint para debug de base de datos
     */
    public function testDebug()
    {
        echo "<h1>Test de Base de Datos y Stored Procedure</h1>";

        try {
            // Conectar a la base de datos usando CodeIgniter
            $db = \Config\Database::connect();
            echo "<p>✅ Conexión a BD exitosa</p>";

            // Datos de prueba
            $name = "Enghelbert Calderon";
            $email = "<EMAIL>";
            $password = password_hash("Clairo!23", PASSWORD_DEFAULT);
            $phone = "50230100452";

            echo "<p>📝 Datos de prueba:</p>";
            echo "<ul>";
            echo "<li>Nombre: $name</li>";
            echo "<li>Email: $email</li>";
            echo "<li>Teléfono: $phone</li>";
            echo "<li>Password hash: " . substr($password, 0, 20) . "...</li>";
            echo "</ul>";

            // Verificar si el email ya existe y eliminarlo
            $existing = $db->query("SELECT COUNT(*) as count FROM users WHERE email = ? AND deleted_at IS NULL", [$email])->getRow();
            if ($existing->count > 0) {
                echo "<p>⚠️ Email ya existe. Eliminando...</p>";
                $db->query("DELETE FROM users WHERE email = ?", [$email]);
                echo "<p>✅ Registro anterior eliminado</p>";
            }

            // Probar el stored procedure
            echo "<p>🔄 Ejecutando stored procedure...</p>";

            $query = $db->query("CALL sp_register_user(?, ?, ?, ?, @user_id, @result)", [
                $name, $email, $password, $phone
            ]);

            echo "<p>✅ Stored procedure ejecutado</p>";

            // Obtener resultados
            $result = $db->query("SELECT @user_id as user_id, @result as result")->getRow();

            echo "<p>📊 Resultados del SP:</p>";
            echo "<ul>";
            echo "<li>User ID: " . $result->user_id . "</li>";
            echo "<li>Result: " . $result->result . "</li>";
            echo "</ul>";

            if (strpos($result->result, 'SUCCESS') === 0) {
                echo "<p>🎉 ¡REGISTRO EXITOSO!</p>";

                // Verificar usuario creado
                $user = $db->query("SELECT id, name, email, phone, status, created_at FROM users WHERE id = ?", [$result->user_id])->getRow();
                if ($user) {
                    echo "<p>✅ Usuario verificado en BD:</p>";
                    echo "<ul>";
                    echo "<li>ID: " . $user->id . "</li>";
                    echo "<li>Nombre: " . $user->name . "</li>";
                    echo "<li>Email: " . $user->email . "</li>";
                    echo "<li>Teléfono: " . $user->phone . "</li>";
                    echo "<li>Estado: " . $user->status . "</li>";
                    echo "<li>Creado: " . $user->created_at . "</li>";
                    echo "</ul>";
                }
            } else {
                echo "<p>❌ Error en el registro: " . $result->result . "</p>";
            }

        } catch (\Exception $e) {
            echo "<p>❌ Error: " . $e->getMessage() . "</p>";
            echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
        }
    }

    /**
     * Procesar registro usando SP
     */
    public function store()
    {
        echo "¡FUNCIONA! Método store ejecutado correctamente";
        die();
    }

    /**
     * Página de verificación de teléfono
     */
    public function verifyPhone()
    {
        $userId = $this->request->getGet('user_id');

        if ($this->request->getMethod() === 'POST') {
            return $this->processPhoneVerification();
        }

        $data = [
            'title' => 'Verificar Teléfono - MrCell Guatemala',
            'page_title' => 'Verificar Teléfono',
            'user_id' => $userId
        ];

        return view('frontend/auth/verify_phone', $data);
    }

    /**
     * Procesar verificación de teléfono
     */
    public function processPhoneVerification()
    {
        $validation = \Config\Services::validation();

        $validation->setRules([
            'user_id' => 'required|integer',
            'verification_code' => 'required|exact_length[6]|numeric'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        try {
            $db = \Config\Database::connect();

            // Usar stored procedure para verificación
            $query = $db->query("CALL sp_verify_phone(?, ?, @result)", [
                $this->request->getPost('user_id'),
                $this->request->getPost('verification_code')
            ]);

            $result = $db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return redirect()->to('/login')->with('success', 'Teléfono verificado exitosamente. Ya puedes iniciar sesión.');
            } else {
                return redirect()->back()->withInput()->with('error', str_replace('ERROR: ', '', $result->result));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error en verificación de teléfono: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al verificar el teléfono. Inténtalo de nuevo.');
        }
    }

    /**
     * Dashboard del usuario usando SP
     */
    public function dashboard()
    {
        // Verificar autenticación ANTES de cualquier operación de base de datos
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login')->with('error', 'Debes iniciar sesión para acceder a tu cuenta');
        }

        $userId = $this->session->get('user_id');

        try {
            $db = \Config\Database::connect();

            // Obtener perfil del usuario con SP
            $query = $db->query("CALL sp_get_user_profile(?)", [$userId]);
            $user = $query->getRowArray();

            if (!$user) {
                $this->session->destroy();
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Obtener estadísticas del usuario con SP
            $query2 = $db->query("CALL sp_get_user_stats(?)", [$userId]);
            $stats = $query2->getRowArray();

            // Obtener pedidos recientes con SP
            $query3 = $db->query("CALL sp_get_user_orders(?, ?, ?)", [$userId, 5, 0]);
            $recentOrders = $query3->getResultArray();

            $data = [
                'title' => 'Mi Cuenta - MrCell Guatemala',
                'page_title' => 'Mi Cuenta',
                'user' => $user,
                'stats' => $stats,
                'recent_orders' => $recentOrders
            ];

            return view('frontend/user_dashboard', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en dashboard de usuario: ' . $e->getMessage());
            // Redirigir a login en lugar de a la página principal
            return redirect()->to('/login')->with('error', 'Error al cargar el dashboard. Por favor, inicia sesión nuevamente.');
        }
    }

    /**
     * Perfil del usuario usando SP
     */
    public function profile()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');

        try {
            $db = \Config\Database::connect();
            
            // Obtener perfil del usuario con SP
            $query = $db->query("CALL sp_get_user_profile(?)", [$userId]);
            $user = $query->getRowArray();

            if (!$user) {
                $this->session->destroy();
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            $data = [
                'title' => 'Mi Perfil - MrCell Guatemala',
                'page_title' => 'Mi Perfil',
                'user' => $user
            ];

            return view('frontend/user/profile', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en perfil de usuario: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar el perfil');
        }
    }

    /**
     * Actualizar perfil usando SP
     */
    public function updateProfile()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');
        
        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'name' => 'required|min_length[2]|max_length[100]',
            'email' => 'required|valid_email',
            'phone' => 'permit_empty|min_length[8]|max_length[15]',
            'address' => 'permit_empty|max_length[255]',
            'city' => 'permit_empty|max_length[100]',
            'department' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        try {
            $db = \Config\Database::connect();
            
            // Usar stored procedure para actualizar perfil
            $query = $db->query("CALL sp_update_user_profile(?, ?, ?, ?, ?, ?, ?, @result)", [
                $userId,
                $this->request->getPost('name'),
                $this->request->getPost('email'),
                $this->request->getPost('phone'),
                $this->request->getPost('address'),
                $this->request->getPost('city'),
                $this->request->getPost('department')
            ]);

            // Obtener resultado
            $result = $db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                // Actualizar datos de sesión
                $this->session->set([
                    'user_name' => $this->request->getPost('name'),
                    'user_email' => $this->request->getPost('email')
                ]);

                return redirect()->back()->with('success', 'Perfil actualizado correctamente');
            } else {
                return redirect()->back()->withInput()->with('error', str_replace('ERROR: ', '', $result->result));
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al actualizar perfil: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al actualizar el perfil');
        }
    }

    /**
     * Historial de pedidos usando SP
     */
    public function orders()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');
        $perPage = 10;
        $page = $this->request->getGet('page') ?? 1;
        $offset = ($page - 1) * $perPage;

        try {
            $db = \Config\Database::connect();
            
            // Obtener pedidos del usuario con SP
            $query = $db->query("CALL sp_get_user_orders(?, ?, ?)", [$userId, $perPage, $offset]);
            $orders = $query->getResultArray();

            $data = [
                'title' => 'Mis Pedidos - MrCell Guatemala',
                'page_title' => 'Mis Pedidos',
                'orders' => $orders,
                'current_page' => $page
            ];

            return view('frontend/user/orders', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en pedidos de usuario: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar los pedidos');
        }
    }

    /**
     * Lista de deseos del usuario
     */
    public function wishlist()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');

        try {
            $db = \Config\Database::connect();

            // Obtener perfil del usuario
            $query = $db->query("CALL sp_get_user_profile(?)", [$userId]);
            $user = $query->getRowArray();

            // TODO: Implementar SP para wishlist cuando esté disponible
            $wishlistItems = [];

            $data = [
                'title' => 'Lista de Deseos - MrCell Guatemala',
                'page_title' => 'Lista de Deseos',
                'user' => $user,
                'wishlist_items' => $wishlistItems
            ];

            return view('frontend/user/wishlist', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en wishlist: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar la lista de deseos');
        }
    }

    /**
     * Direcciones del usuario
     */
    public function addresses()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');

        try {
            $db = \Config\Database::connect();

            // Obtener perfil del usuario
            $query = $db->query("CALL sp_get_user_profile(?)", [$userId]);
            $user = $query->getRowArray();

            // Obtener direcciones del usuario
            $addresses = $db->query("
                SELECT id, address_name, address, city, state, zip_code, country, is_default
                FROM user_addresses
                WHERE user_id = ? AND deleted_at IS NULL
                ORDER BY is_default DESC, created_at DESC
            ", [$userId])->getResultArray();

            $data = [
                'title' => 'Mis Direcciones - MrCell Guatemala',
                'page_title' => 'Mis Direcciones',
                'user' => $user,
                'addresses' => $addresses
            ];

            return view('frontend/user/addresses', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en addresses: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar las direcciones');
        }
    }

    /**
     * Guardar nueva dirección del usuario
     */
    public function saveAddress()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        if ($this->request->getMethod() !== 'POST') {
            return redirect()->to('/cuenta/direcciones');
        }

        $userId = $this->session->get('user_id');

        // Validar datos del formulario
        $validation = \Config\Services::validation();
        $validation->setRules([
            'address_name' => 'required|min_length[2]|max_length[100]',
            'recipient_name' => 'required|min_length[2]|max_length[100]',
            'phone' => 'required|min_length[8]|max_length[20]',
            'department' => 'required|min_length[2]|max_length[50]',
            'municipality' => 'required|min_length[2]|max_length[50]',
            'postal_code' => 'required|min_length[4]|max_length[10]',
            'full_address' => 'required|min_length[10]|max_length[500]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        try {
            $db = \Config\Database::connect();

            $addressData = [
                'user_id' => $userId,
                'name' => $this->request->getPost('address_name'),
                'recipient_name' => $this->request->getPost('recipient_name'),
                'phone' => $this->request->getPost('phone'),
                'department' => $this->request->getPost('department'),
                'municipality' => $this->request->getPost('municipality'),
                'postal_code' => $this->request->getPost('postal_code'),
                'full_address' => $this->request->getPost('full_address'),
                'is_default' => $this->request->getPost('is_default') ? 1 : 0
            ];

            // TODO: Implementar SP para guardar dirección cuando esté disponible
            // Por ahora, simular el guardado exitoso

            // Si es dirección predeterminada, actualizar las demás
            if ($addressData['is_default']) {
                // TODO: Actualizar otras direcciones para que no sean predeterminadas
            }

            // Ensure user_addresses table exists
            $this->ensureAddressTableExists($db);

            // If this is set as default, unset other defaults
            if (!empty($addressData['is_default'])) {
                $db->query("
                    UPDATE user_addresses
                    SET is_default = 0
                    WHERE user_id = ? AND deleted_at IS NULL
                ", [$userId]);
            }

            // Insert new address
            $insertData = [
                'user_id' => $userId,
                'address_name' => $addressData['name'],
                'address' => $addressData['full_address'],
                'city' => $addressData['municipality'],
                'state' => $addressData['department'],
                'zip_code' => $addressData['postal_code'],
                'country' => 'Guatemala',
                'is_default' => !empty($addressData['is_default']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->table('user_addresses')->insert($insertData);

            return redirect()->to('/cuenta/direcciones')->with('success', 'Dirección guardada correctamente');

        } catch (\Exception $e) {
            log_message('error', 'Error al guardar dirección: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al guardar la dirección. Inténtalo de nuevo.');
        }
    }

    /**
     * Configuración de seguridad
     */
    public function security()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to('/login');
        }

        $userId = $this->session->get('user_id');

        try {
            $db = \Config\Database::connect();

            // Obtener perfil del usuario
            $query = $db->query("CALL sp_get_user_profile(?)", [$userId]);
            $user = $query->getRowArray();

            $data = [
                'title' => 'Seguridad - MrCell Guatemala',
                'page_title' => 'Configuración de Seguridad',
                'user' => $user
            ];

            return view('frontend/user/security', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en security: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar la configuración de seguridad');
        }
    }





    /**
     * Cerrar sesión
     */
    public function logout()
    {
        $this->session->destroy();
        return redirect()->to('/')->with('success', 'Sesión cerrada correctamente');
    }

    /**
     * Verificar si una IP está en lista negra
     */
    private function isBlacklistedIp(string $ip): bool
    {
        // Lista de IPs bloqueadas (puedes mover esto a base de datos)
        $blacklistedIps = [
            // Agregar IPs problemáticas aquí
        ];

        // También verificar rangos de IP sospechosos
        $suspiciousRanges = [
            '10.0.0.0/8',     // Red privada
            '**********/12',  // Red privada
            '***********/16', // Red privada
        ];

        if (in_array($ip, $blacklistedIps)) {
            return true;
        }

        // Verificar rangos (implementación básica)
        foreach ($suspiciousRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                // Log pero no bloquear redes privadas por defecto
                log_message('info', "Registro desde red privada: {$ip}");
            }
        }

        return false;
    }

    /**
     * Verificar si una IP está en un rango
     */
    private function ipInRange(string $ip, string $range): bool
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    /**
     * Verificar fortaleza de contraseña
     */
    private function isStrongPassword(string $password): bool
    {
        // Al menos 8 caracteres, una mayúscula, una minúscula y un número
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/', $password);
    }

    /**
     * Ensure user_addresses table exists
     */
    private function ensureAddressTableExists($db)
    {
        try {
            $tableExists = $db->query("SHOW TABLES LIKE 'user_addresses'")->getNumRows() > 0;

            if (!$tableExists) {
                $db->query("
                    CREATE TABLE user_addresses (
                        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                        user_id INT(11) UNSIGNED NOT NULL,
                        address_name VARCHAR(100) NOT NULL,
                        address TEXT NOT NULL,
                        city VARCHAR(100) NOT NULL,
                        state VARCHAR(100) NOT NULL,
                        zip_code VARCHAR(20) NULL,
                        country VARCHAR(100) DEFAULT 'Guatemala',
                        is_default TINYINT(1) DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        deleted_at TIMESTAMP NULL,
                        INDEX idx_user_id (user_id),
                        INDEX idx_is_default (is_default),
                        INDEX idx_deleted_at (deleted_at)
                    )
                ");

                log_message('info', 'user_addresses table created successfully');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating user_addresses table: ' . $e->getMessage());
            throw $e;
        }
    }
}
