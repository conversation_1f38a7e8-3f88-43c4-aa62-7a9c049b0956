<?php

namespace App\Controllers;

use App\Models\ProductModel;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use CodeIgniter\Controller;

class Checkout extends BaseController
{
    protected $productModel;
    protected $orderModel;
    protected $orderItemModel;
    protected $session;

    public function __construct()
    {
        $this->productModel = new ProductModel();
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->session = session();
    }

    /**
     * Página principal de checkout
     */
    public function index()
    {
        // Verificar que el usuario esté autenticado
        if (!session()->get('user_id')) {
            // Guardar la URL de destino para redirigir después del login
            session()->set('redirect_after_login', current_url());
            return redirect()->to('/login')->with('info', 'Debes iniciar sesión para continuar con tu compra');
        }

        // Verificar que hay items en el carrito
        $cartItems = $this->getCartItems();

        if (empty($cartItems)) {
            return redirect()->to('/carrito')->with('error', 'Tu carrito está vacío');
        }

        $data = [
            'title' => 'Checkout - MrCell Guatemala',
            'page_title' => 'Finalizar Compra',
            'cart_items' => $cartItems,
            'cart_totals' => $this->calculateCartTotals($cartItems),
            'shipping_methods' => $this->getShippingMethods(),
            'departments' => $this->getDepartments()
        ];

        return view('frontend/checkout', $data);
    }

    /**
     * Procesar el checkout
     */
    public function process()
    {
        // Verificar que el usuario esté autenticado
        if (!session()->get('user_id')) {
            return redirect()->to('/login')->with('error', 'Debes iniciar sesión para procesar tu compra');
        }

        $validation = \Config\Services::validation();
        
        // Reglas de validación
        $validation->setRules([
            'customer_name' => 'required|min_length[2]|max_length[100]',
            'customer_email' => 'required|valid_email',
            'customer_phone' => 'required|min_length[8]|max_length[15]',
            'shipping_address' => 'required|min_length[10]|max_length[255]',
            'shipping_city' => 'required|min_length[2]|max_length[100]',
            'shipping_department' => 'required',
            'shipping_method' => 'required',
            'payment_method' => 'required|in_list[transfer,cash,card]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Obtener datos del formulario
        $customerData = [
            'name' => $this->request->getPost('customer_name'),
            'email' => $this->request->getPost('customer_email'),
            'phone' => $this->request->getPost('customer_phone')
        ];

        $shippingData = [
            'address' => $this->request->getPost('shipping_address'),
            'city' => $this->request->getPost('shipping_city'),
            'department' => $this->request->getPost('shipping_department'),
            'method' => $this->request->getPost('shipping_method'),
            'notes' => $this->request->getPost('shipping_notes') ?? ''
        ];

        $paymentMethod = $this->request->getPost('payment_method');

        // Verificar carrito
        $cartItems = $this->getCartItems();
        if (empty($cartItems)) {
            return redirect()->to('/carrito')->with('error', 'Tu carrito está vacío');
        }

        // Calcular totales
        $totals = $this->calculateCartTotals($cartItems);
        $shippingCost = $this->calculateShippingCost($shippingData['method'], $shippingData['department']);
        
        $finalTotals = [
            'subtotal' => $totals['subtotal'],
            'tax' => $totals['tax'],
            'shipping' => $shippingCost,
            'total' => $totals['subtotal'] + $totals['tax'] + $shippingCost
        ];

        try {
            // Crear el pedido
            $orderData = [
                'order_number' => $this->generateOrderNumber(),
                'customer_id' => 0, // Guest checkout
                'customer_name' => $customerData['name'],
                'customer_email' => $customerData['email'],
                'customer_phone' => $customerData['phone'],
                'shipping_address' => $shippingData['address'] . ', ' . $shippingData['city'] . ', ' . $shippingData['department'],
                'customer_notes' => $shippingData['notes'],
                'payment_method' => $paymentMethod,
                'subtotal' => $finalTotals['subtotal'],
                'tax_amount' => $finalTotals['tax'],
                'shipping_cost' => $finalTotals['shipping'],
                'total' => $finalTotals['total'],
                'status' => 'pending',
                'payment_status' => 'pending',
                'source' => 'web',
                'created_by' => 0
            ];

            $orderId = $this->orderModel->insert($orderData);

            if (!$orderId) {
                throw new \Exception('Error al crear el pedido');
            }

            // Disparar evento de orden creada para WhatsApp
            $orderDataForEvent = array_merge($orderData, ['id' => $orderId]);
            \App\Libraries\WhatsAppEventHandler::triggerOrderCreated($orderDataForEvent);

            // Crear los items del pedido
            foreach ($cartItems as $item) {
                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['name'],
                    'product_sku' => $item['sku'] ?? '',
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'subtotal' => $item['price'] * $item['quantity']
                ];

                $this->orderItemModel->insert($orderItemData);

                // Actualizar stock del producto
                $this->updateProductStock($item['product_id'], $item['quantity']);
            }

            // Limpiar carrito
            $this->session->remove('cart');

            // Redirigir al procesamiento de pago
            return redirect()->to('/payment/process/' . $orderId)
                           ->with('success', 'Pedido creado exitosamente. Procede con el pago.');

        } catch (\Exception $e) {
            log_message('error', 'Error en checkout: ' . $e->getMessage());
            return redirect()->back()->withInput()
                           ->with('error', 'Error al procesar el pedido. Inténtalo de nuevo.');
        }
    }

    /**
     * Página de confirmación
     */
    public function confirmation($orderId = null)
    {
        if (!$orderId) {
            return redirect()->to('/');
        }

        $order = $this->orderModel->find($orderId);
        
        if (!$order) {
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        $orderItems = $this->orderItemModel->where('order_id', $orderId)->findAll();

        $data = [
            'title' => 'Confirmación de Pedido - MrCell Guatemala',
            'page_title' => 'Pedido Confirmado',
            'order' => $order,
            'order_items' => $orderItems
        ];

        return view('frontend/checkout_confirmation', $data);
    }

    /**
     * Obtener items del carrito
     */
    private function getCartItems()
    {
        $cart = $this->session->get('cart') ?? [];
        $cartItems = [];

        foreach ($cart as $productId => $quantity) {
            $product = $this->productModel->find($productId);
            if ($product && $product['is_active']) {
                $price = $product['price_sale'] ?: $product['price_regular'];
                $cartItems[] = [
                    'product_id' => $product['id'],
                    'name' => $product['name'],
                    'sku' => $product['sku'],
                    'price' => $price,
                    'quantity' => $quantity,
                    'image' => $product['featured_image'],
                    'stock' => $product['stock_quantity']
                ];
            }
        }

        return $cartItems;
    }

    /**
     * Calcular totales del carrito
     */
    private function calculateCartTotals($cartItems)
    {
        $subtotal = 0;
        
        foreach ($cartItems as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        $tax = $subtotal * 0.12; // IVA 12%
        
        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'total_before_shipping' => $subtotal + $tax
        ];
    }

    /**
     * Obtener métodos de envío
     */
    private function getShippingMethods()
    {
        return [
            'standard' => [
                'name' => 'Envío Estándar',
                'description' => '3-5 días hábiles',
                'base_cost' => 25.00
            ],
            'express' => [
                'name' => 'Envío Express',
                'description' => '1-2 días hábiles',
                'base_cost' => 50.00
            ],
            'pickup' => [
                'name' => 'Recoger en Tienda',
                'description' => 'Sin costo adicional',
                'base_cost' => 0.00
            ]
        ];
    }

    /**
     * Obtener departamentos de Guatemala
     */
    private function getDepartments()
    {
        return [
            'guatemala' => 'Guatemala',
            'sacatepequez' => 'Sacatepéquez',
            'chimaltenango' => 'Chimaltenango',
            'escuintla' => 'Escuintla',
            'santa_rosa' => 'Santa Rosa',
            'solola' => 'Sololá',
            'totonicapan' => 'Totonicapán',
            'quetzaltenango' => 'Quetzaltenango',
            'suchitepequez' => 'Suchitepéquez',
            'retalhuleu' => 'Retalhuleu',
            'san_marcos' => 'San Marcos',
            'huehuetenango' => 'Huehuetenango',
            'quiche' => 'Quiché',
            'baja_verapaz' => 'Baja Verapaz',
            'alta_verapaz' => 'Alta Verapaz',
            'peten' => 'Petén',
            'izabal' => 'Izabal',
            'zacapa' => 'Zacapa',
            'chiquimula' => 'Chiquimula',
            'jalapa' => 'Jalapa',
            'jutiapa' => 'Jutiapa',
            'el_progreso' => 'El Progreso'
        ];
    }

    /**
     * Calcular costo de envío
     */
    private function calculateShippingCost($method, $department)
    {
        $methods = $this->getShippingMethods();
        $baseCost = $methods[$method]['base_cost'] ?? 25.00;

        // Costo adicional por departamento
        $departmentMultiplier = 1.0;
        if (in_array($department, ['peten', 'izabal', 'alta_verapaz'])) {
            $departmentMultiplier = 1.5; // 50% más caro para departamentos lejanos
        } elseif (!in_array($department, ['guatemala', 'sacatepequez', 'chimaltenango'])) {
            $departmentMultiplier = 1.2; // 20% más caro para otros departamentos
        }

        return $baseCost * $departmentMultiplier;
    }

    /**
     * Generar número de pedido único
     */
    private function generateOrderNumber()
    {
        return 'MRC-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
    }

    /**
     * Actualizar stock del producto
     */
    private function updateProductStock($productId, $quantity)
    {
        $product = $this->productModel->find($productId);
        if ($product) {
            $newStock = max(0, $product['stock_quantity'] - $quantity);
            $this->productModel->update($productId, ['stock_quantity' => $newStock]);
        }
    }
}
