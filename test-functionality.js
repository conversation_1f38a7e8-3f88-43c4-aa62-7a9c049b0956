const { chromium } = require('playwright');

async function testFunctionality() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('🧪 INICIANDO PRUEBAS DE FUNCIONALIDAD...\n');

        // ========================================
        // PRUEBA 1: LOGIN USUARIO
        // ========================================
        console.log('📝 PRUEBA 1: Login de Usuario');
        await page.goto('http://localhost:8080/login');
        await page.waitForLoadState('networkidle');

        // Llenar formulario de login
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'Clairo!23');
        await page.click('button[type="submit"]');
        
        // Esperar redirección
        await page.waitForURL('**/cuenta**', { timeout: 10000 });
        console.log('✅ Login de usuario exitoso');

        // ========================================
        // PRUEBA 2: DASHBOARD - ESTADÍSTICAS REALES
        // ========================================
        console.log('\n📊 PRUEBA 2: Dashboard con Estadísticas Reales');
        
        // Verificar que las estadísticas no sean 0
        const totalOrders = await page.textContent('.stat-card:has-text("Pedidos") .stat-number');
        const totalSpent = await page.textContent('.stat-card:has-text("Gastado") .stat-number');
        const wishlistItems = await page.textContent('.stat-card:has-text("Lista") .stat-number');
        
        console.log(`📈 Pedidos totales: ${totalOrders}`);
        console.log(`💰 Total gastado: ${totalSpent}`);
        console.log(`❤️ Items en wishlist: ${wishlistItems}`);
        
        if (totalOrders !== '0' || totalSpent !== 'Q0.00') {
            console.log('✅ Estadísticas reales cargadas correctamente');
        } else {
            console.log('⚠️ Las estadísticas siguen mostrando valores por defecto');
        }

        // ========================================
        // PRUEBA 3: PÁGINA DE PEDIDOS
        // ========================================
        console.log('\n📦 PRUEBA 3: Página de Pedidos');
        await page.click('a[href*="pedidos"]');
        await page.waitForLoadState('networkidle');

        // Verificar contadores en pestañas
        const allOrdersCount = await page.textContent('.nav-link:has-text("Todos") .badge');
        const pendingCount = await page.textContent('.nav-link:has-text("Pendientes") .badge');
        
        console.log(`📋 Todos los pedidos: ${allOrdersCount}`);
        console.log(`⏳ Pedidos pendientes: ${pendingCount}`);
        
        // Verificar si hay pedidos o mensaje vacío
        const hasOrders = await page.locator('.order-card').count() > 0;
        const hasEmptyMessage = await page.locator('.empty-orders').isVisible();
        
        if (hasOrders) {
            console.log('✅ Pedidos reales mostrados');
            
            // Probar click en primer pedido
            const firstOrderLink = page.locator('.order-card a[href*="pedidos/"]').first();
            if (await firstOrderLink.count() > 0) {
                const orderUrl = await firstOrderLink.getAttribute('href');
                console.log(`🔗 Probando enlace a detalles: ${orderUrl}`);
                
                await firstOrderLink.click();
                await page.waitForLoadState('networkidle');
                
                // Verificar que la página de detalles carga
                const orderTitle = await page.textContent('h1, .page-title');
                console.log(`📄 Página de detalles cargada: ${orderTitle}`);
                console.log('✅ Enlace a detalles de pedido funciona');
                
                // Volver a pedidos
                await page.goBack();
            }
        } else if (hasEmptyMessage) {
            console.log('ℹ️ No hay pedidos - mensaje vacío mostrado correctamente');
        }

        // ========================================
        // PRUEBA 4: FILTRO DE MARCAS EN TIENDA
        // ========================================
        console.log('\n🛍️ PRUEBA 4: Filtro de Marcas en Tienda');
        await page.goto('http://localhost:8080/tienda');
        await page.waitForLoadState('networkidle');
        
        // Esperar a que se carguen las marcas
        await page.waitForTimeout(3000);
        
        const brandsCount = await page.locator('.brands-list .form-check').count();
        console.log(`🏷️ Marcas disponibles: ${brandsCount}`);
        
        if (brandsCount > 0) {
            console.log('✅ Marcas cargadas correctamente');
            
            // Probar filtro por primera marca
            const firstBrand = page.locator('.brands-list .form-check-input').first();
            const brandName = await page.locator('.brands-list .form-check-label').first().textContent();
            
            console.log(`🔍 Probando filtro por marca: ${brandName}`);
            await firstBrand.check();
            
            // Esperar a que se aplique el filtro
            await page.waitForTimeout(2000);
            
            const productsAfterFilter = await page.locator('.product-card').count();
            console.log(`📦 Productos después del filtro: ${productsAfterFilter}`);
            console.log('✅ Filtro de marcas funciona');
        } else {
            console.log('❌ No se cargaron marcas - filtro no funciona');
        }

        // ========================================
        // PRUEBA 5: BOTÓN DE WISHLIST EN PRODUCTO
        // ========================================
        console.log('\n❤️ PRUEBA 5: Botón de Wishlist en Producto');
        
        // Ir a un producto específico
        const firstProduct = page.locator('.product-card a').first();
        if (await firstProduct.count() > 0) {
            await firstProduct.click();
            await page.waitForLoadState('networkidle');
            
            // Buscar botón de wishlist
            const wishlistButton = page.locator('button[onclick*="toggleWishlist"]');
            const hasWishlistButton = await wishlistButton.count() > 0;
            
            if (hasWishlistButton) {
                console.log('✅ Botón de wishlist encontrado en producto');
                
                // Probar click (si está logueado)
                try {
                    await wishlistButton.click();
                    await page.waitForTimeout(2000);
                    console.log('✅ Botón de wishlist clickeable');
                } catch (error) {
                    console.log('⚠️ Error al hacer click en wishlist:', error.message);
                }
            } else {
                console.log('❌ Botón de wishlist NO encontrado en producto');
            }
        }

        // ========================================
        // PRUEBA 6: CONFIGURACIONES DINÁMICAS
        // ========================================
        console.log('\n⚙️ PRUEBA 6: Configuraciones Dinámicas en Footer');
        await page.goto('http://localhost:8080/');
        await page.waitForLoadState('networkidle');
        
        // Verificar footer
        const siteName = await page.textContent('footer h5');
        const contactInfo = await page.textContent('footer p:has-text("@")');
        
        console.log(`🏢 Nombre del sitio en footer: ${siteName}`);
        console.log(`📧 Información de contacto: ${contactInfo}`);
        
        if (siteName && siteName !== 'MrCell Guatemala') {
            console.log('✅ Configuraciones dinámicas funcionando');
        } else {
            console.log('⚠️ Footer usando valores por defecto');
        }

        console.log('\n🎉 PRUEBAS COMPLETADAS');

    } catch (error) {
        console.error('❌ Error durante las pruebas:', error);
    } finally {
        await browser.close();
    }
}

// Ejecutar pruebas
testFunctionality().catch(console.error);
