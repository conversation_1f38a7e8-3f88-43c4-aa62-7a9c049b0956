<?php
/**
 * SCRIPT PARA VERIFICAR LA ESTRUCTURA REAL DE LA BASE DE DATOS
 */

echo "🔍 VERIFICANDO ESTRUCTURA DE LA BASE DE DATOS\n";
echo "=============================================\n\n";

try {
    // Configuración de la base de datos
    $host = '**************';
    $dbname = 'mayansourcecom_mrcell';
    $username = 'mayansourcecom_mrcell';
    $password = 'Clairo!23';
    
    echo "🔗 Conectando a la base de datos...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conectado exitosamente\n\n";
    
    // 1. Verificar si existe la tabla system_settings
    echo "📋 VERIFICANDO TABLA system_settings:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabla system_settings existe\n";
        
        // Mostrar estructura
        $stmt = $pdo->query("DESCRIBE system_settings");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📊 Estructura de system_settings:\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        }
        echo "\n";
        
        // Mostrar algunos registros
        $stmt = $pdo->query("SELECT * FROM system_settings LIMIT 5");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📄 Primeros 5 registros:\n";
        foreach ($records as $record) {
            echo "  - " . json_encode($record) . "\n";
        }
        echo "\n";
        
    } else {
        echo "❌ Tabla system_settings NO existe\n\n";
        
        // Buscar tablas similares
        echo "🔍 Buscando tablas similares:\n";
        $stmt = $pdo->query("SHOW TABLES LIKE '%setting%'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($tables)) {
            foreach ($tables as $table) {
                echo "  - $table\n";
                
                // Mostrar estructura de cada tabla
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "    Columnas: ";
                foreach ($columns as $column) {
                    echo $column['Field'] . " ";
                }
                echo "\n\n";
            }
        } else {
            echo "  No se encontraron tablas con 'setting' en el nombre\n\n";
        }
    }
    
    // 2. Verificar tabla cron_executions
    echo "📋 VERIFICANDO TABLA cron_executions:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'cron_executions'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabla cron_executions existe\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM cron_executions");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Registros en cron_executions: {$count['count']}\n\n";
    } else {
        echo "❌ Tabla cron_executions NO existe\n\n";
    }
    
    // 3. Verificar tablas de productos y pedidos
    echo "📋 VERIFICANDO TABLAS PRINCIPALES:\n";
    $mainTables = ['products', 'orders', 'users'];
    
    foreach ($mainTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ $table: {$count['count']} registros\n";
        } else {
            echo "❌ $table: NO existe\n";
        }
    }
    echo "\n";
    
    // 4. Buscar configuraciones de WhatsApp
    echo "📱 BUSCANDO CONFIGURACIONES DE WHATSAPP:\n";
    
    // Buscar en diferentes tablas posibles
    $possibleTables = ['settings', 'system_settings', 'config', 'configurations', 'whatsapp_settings'];
    
    foreach ($possibleTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "🔍 Buscando en tabla $table:\n";
            
            try {
                $stmt = $pdo->query("SELECT * FROM $table WHERE setting_key LIKE '%whatsapp%' OR `key` LIKE '%whatsapp%' OR name LIKE '%whatsapp%'");
                $whatsappConfigs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($whatsappConfigs)) {
                    foreach ($whatsappConfigs as $config) {
                        echo "  - " . json_encode($config) . "\n";
                    }
                } else {
                    echo "  - No se encontraron configuraciones de WhatsApp\n";
                }
            } catch (Exception $e) {
                echo "  - Error consultando $table: " . $e->getMessage() . "\n";
            }
            echo "\n";
        }
    }
    
    // 5. CREAR LA CONFIGURACIÓN CORRECTA
    echo "🔧 CREANDO CONFIGURACIÓN DE WHATSAPP:\n";
    
    // Intentar insertar en la tabla que funcione
    $inserted = false;
    
    // Intentar con system_settings (estructura básica)
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    if ($stmt->rowCount() > 0) {
        try {
            // Verificar si ya existe
            $stmt = $pdo->query("SELECT * FROM system_settings WHERE setting_key = 'whatsapp_alerts_group'");
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$existing) {
                // Intentar inserción básica
                $pdo->exec("
                    INSERT INTO system_settings (setting_key, setting_value) 
                    VALUES ('whatsapp_alerts_group', '120363416393766854')
                ");
                echo "✅ Configuración insertada en system_settings (básica)\n";
                $inserted = true;
            } else {
                echo "✅ Configuración ya existe en system_settings\n";
                $inserted = true;
            }
        } catch (Exception $e) {
            echo "❌ Error insertando en system_settings: " . $e->getMessage() . "\n";
        }
    }
    
    // Si no funcionó, intentar con settings
    if (!$inserted) {
        $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
        if ($stmt->rowCount() > 0) {
            try {
                $stmt = $pdo->query("SELECT * FROM settings WHERE `key` = 'whatsapp_alerts_group'");
                $existing = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$existing) {
                    $pdo->exec("
                        INSERT INTO settings (`key`, `value`) 
                        VALUES ('whatsapp_alerts_group', '120363416393766854')
                    ");
                    echo "✅ Configuración insertada en settings\n";
                    $inserted = true;
                } else {
                    echo "✅ Configuración ya existe en settings\n";
                    $inserted = true;
                }
            } catch (Exception $e) {
                echo "❌ Error insertando en settings: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Si nada funcionó, crear tabla propia
    if (!$inserted) {
        echo "🔨 Creando tabla propia para configuraciones:\n";
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS mrcell_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                config_key VARCHAR(100) NOT NULL UNIQUE,
                config_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        $pdo->exec("
            INSERT INTO mrcell_config (config_key, config_value) 
            VALUES ('whatsapp_alerts_group', '120363416393766854')
            ON DUPLICATE KEY UPDATE config_value = '120363416393766854'
        ");
        
        echo "✅ Tabla mrcell_config creada y configuración insertada\n";
    }
    
    echo "\n🎉 CONFIGURACIÓN COMPLETADA\n";
    echo "============================\n";
    echo "Número de grupo WhatsApp: 120363416393766854\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
    echo "Archivo: " . $e->getFile() . "\n";
}

echo "\n📅 Verificación completada: " . date('Y-m-d H:i:s') . "\n";
?>
