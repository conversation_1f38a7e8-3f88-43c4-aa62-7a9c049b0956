<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clock"></i> Configuración de Cron Jobs
        </h1>
        <a href="<?= base_url('admin/automation') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Volver al Dashboard
        </a>
    </div>

    <!-- Información del Servidor -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-server"></i> Información del Servidor
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>PHP Version:</strong></td>
                                    <td><?= $server_info['php_version'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Servidor:</strong></td>
                                    <td><?= $server_info['server_software'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong>URL Base:</strong></td>
                                    <td><?= $server_info['base_url'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Zona Horaria:</strong></td>
                                    <td><?= $server_info['timezone'] ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Memory Limit:</strong></td>
                                    <td><?= $server_info['memory_limit'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Max Execution Time:</strong></td>
                                    <td><?= $server_info['max_execution_time'] ?>s</td>
                                </tr>
                                <tr>
                                    <td><strong>Document Root:</strong></td>
                                    <td><small><?= $server_info['document_root'] ?></small></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instrucciones para cPanel -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-book"></i> Configuración Automática de Cron Jobs
                    </h6>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-success" onclick="copyAllCronCommands()">
                            <i class="fas fa-copy"></i> Copiar Todos
                        </button>
                        <button class="btn btn-sm btn-info" onclick="showCpanelInstructions()">
                            <i class="fas fa-question-circle"></i> ¿Cómo configurar?
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Configuración por Prioridad -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> CRÍTICAS</h6>
                                    <small>Configurar primero - Esenciales para el funcionamiento</small>
                                </div>
                                <div class="card-body p-2">
                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Verificación del sistema (cada 5 min)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='*/5 * * * * wget -q -O - "<?= base_url('cron/run-system-check') ?>"'
                                                   id="critical-1">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('critical-1')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Alertas de stock (cada 15 min)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='*/15 * * * * wget -q -O - "<?= base_url('cron/run-stock') ?>"'
                                                   id="critical-2">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('critical-2')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command">
                                        <small class="text-muted">Backup diario (2:00 AM)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 2 * * * wget -q -O - "<?= base_url('cron/run-backup') ?>"'
                                                   id="critical-3">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('critical-3')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-star"></i> IMPORTANTES</h6>
                                    <small>Configurar después - Mejoran la funcionalidad</small>
                                </div>
                                <div class="card-body p-2">
                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Monitor de precios (cada 30 min)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='*/30 * * * * wget -q -O - "<?= base_url('cron/run-prices') ?>"'
                                                   id="important-1">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('important-1')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Automatizaciones (cada hora)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 * * * * wget -q -O - "<?= base_url('cron/run-all') ?>"'
                                                   id="important-2">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('important-2')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command">
                                        <small class="text-muted">Sincronización analytics (cada 2h)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 */2 * * * wget -q -O - "<?= base_url('cron/run-analytics') ?>"'
                                                   id="important-3">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('important-3')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-plus-circle"></i> OPCIONALES</h6>
                                    <small>Si tienes suficientes cron jobs disponibles</small>
                                </div>
                                <div class="card-body p-2">
                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Limpieza de datos (3:00 AM)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 3 * * * wget -q -O - "<?= base_url('cron/run-cleanup') ?>"'
                                                   id="optional-1">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('optional-1')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command mb-2">
                                        <small class="text-muted">Recordatorios wishlist (Dom 10 AM)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 10 * * 0 wget -q -O - "<?= base_url('cron/run-wishlist') ?>"'
                                                   id="optional-2">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('optional-2')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cron-command">
                                        <small class="text-muted">Mantenimiento cache (cada 6h)</small>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control" readonly
                                                   value='0 */6 * * * wget -q -O - "<?= base_url('cron/run-cache') ?>"'
                                                   id="optional-3">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" onclick="copyCommand('optional-3')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instrucciones Rápidas -->
                    <div class="alert alert-primary mt-3" id="quick-instructions" style="display: none;">
                        <h6><i class="fas fa-rocket"></i> Configuración Rápida en cPanel:</h6>
                        <ol class="mb-0">
                            <li><strong>Accede a cPanel</strong> → Busca "Cron Jobs"</li>
                            <li><strong>Copia y pega</strong> cada comando en una nueva tarea cron</li>
                            <li><strong>Configura email</strong> de notificaciones (opcional)</li>
                            <li><strong>Guarda</strong> cada tarea</li>
                        </ol>
                        <div class="mt-2">
                            <small class="text-muted">
                                💡 <strong>Tip:</strong> Empieza con las tareas CRÍTICAS, luego agrega las IMPORTANTES según tu plan de hosting.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Cron Jobs -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i> Tareas Programadas
                    </h6>
                    <button class="btn btn-sm btn-info" onclick="copyAllCommands()">
                        <i class="fas fa-copy"></i> Copiar Todos los Comandos
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Tarea</th>
                                    <th>Horario (Cron)</th>
                                    <th>Descripción</th>
                                    <th>Prioridad</th>
                                    <th>Comando</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cron_jobs as $job): ?>
                                    <tr>
                                        <td>
                                            <strong><?= $job['name'] ?></strong>
                                        </td>
                                        <td>
                                            <code><?= $job['schedule'] ?></code>
                                        </td>
                                        <td><?= $job['description'] ?></td>
                                        <td>
                                            <?php
                                            $badgeClass = 'secondary';
                                            if ($job['priority'] === 'high') $badgeClass = 'danger';
                                            elseif ($job['priority'] === 'medium') $badgeClass = 'warning';
                                            elseif ($job['priority'] === 'low') $badgeClass = 'info';
                                            ?>
                                            <span class="badge badge-<?= $badgeClass ?>">
                                                <?= ucfirst($job['priority']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-sm" 
                                                       value='wget -q -O - "<?= $job['url'] ?>"' 
                                                       readonly id="cmd-<?= md5($job['name']) ?>">
                                                <div class="input-group-append">
                                                    <button class="btn btn-sm btn-outline-secondary" 
                                                            onclick="copyCommand('cmd-<?= md5($job['name']) ?>')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" 
                                                    onclick="testCronJob('<?= $job['url'] ?>')">
                                                <i class="fas fa-play"></i> Probar
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Explicación de Horarios Cron -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-question-circle"></i> Formato de Horarios Cron
                    </h6>
                </div>
                <div class="card-body">
                    <p>Los horarios cron siguen el formato: <code>minuto hora día mes día_semana</code></p>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><code>*/5 * * * *</code></td>
                            <td>Cada 5 minutos</td>
                        </tr>
                        <tr>
                            <td><code>*/15 * * * *</code></td>
                            <td>Cada 15 minutos</td>
                        </tr>
                        <tr>
                            <td><code>*/30 * * * *</code></td>
                            <td>Cada 30 minutos</td>
                        </tr>
                        <tr>
                            <td><code>0 * * * *</code></td>
                            <td>Cada hora</td>
                        </tr>
                        <tr>
                            <td><code>0 2 * * *</code></td>
                            <td>Diario a las 2:00 AM</td>
                        </tr>
                        <tr>
                            <td><code>0 10 * * 0</code></td>
                            <td>Domingos a las 10:00 AM</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-lightbulb"></i> Alternativas sin Cron Jobs
                    </h6>
                </div>
                <div class="card-body">
                    <p>Si tu hosting no permite cron jobs, puedes:</p>
                    
                    <div class="list-group">
                        <div class="list-group-item">
                            <h6>🔄 Ejecución Manual</h6>
                            <p class="mb-1">Ejecuta las tareas manualmente desde el dashboard de automatizaciones.</p>
                        </div>
                        
                        <div class="list-group-item">
                            <h6>🌐 Servicios Externos</h6>
                            <p class="mb-1">Usa servicios como <strong>cron-job.org</strong> o <strong>easycron.com</strong> para llamar las URLs.</p>
                        </div>
                        
                        <div class="list-group-item">
                            <h6>📱 Notificaciones Push</h6>
                            <p class="mb-1">Configura notificaciones para recordarte ejecutar las tareas críticas.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado de Prueba -->
<div class="modal fade" id="testModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resultado de la Prueba</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="testResult"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script>
function copyCommand(inputId) {
    const input = document.getElementById(inputId);
    input.select();
    input.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Mostrar notificación
    showToast('Comando copiado al portapapeles', 'success');
}

function copyAllCommands() {
    let allCommands = '';
    const inputs = document.querySelectorAll('input[id^="cmd-"]');

    inputs.forEach(input => {
        allCommands += input.value + '\n';
    });

    // Crear elemento temporal para copiar
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = allCommands;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);

    showToast('Todos los comandos copiados al portapapeles', 'success');
}

function copyAllCronCommands() {
    let allCommands = '';

    // Obtener todos los comandos de cron
    const cronInputs = document.querySelectorAll('input[id^="critical-"], input[id^="important-"], input[id^="optional-"]');

    cronInputs.forEach(input => {
        allCommands += input.value + '\n';
    });

    // Crear elemento temporal para copiar
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = allCommands;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);

    showToast('Todos los comandos de cron copiados al portapapeles', 'success');
}

function showCpanelInstructions() {
    const instructions = document.getElementById('quick-instructions');
    if (instructions.style.display === 'none') {
        instructions.style.display = 'block';
        showToast('Instrucciones mostradas', 'info');
    } else {
        instructions.style.display = 'none';
    }
}

function testCronJob(url) {
    document.getElementById('testResult').innerHTML = 
        '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>Probando tarea...</p></div>';
    $('#testModal').modal('show');
    
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(data => {
        let content = '<div class="alert alert-success"><i class="fas fa-check"></i> Tarea ejecutada correctamente</div>';
        content += '<h6>Respuesta del servidor:</h6>';
        content += '<pre class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">' + data + '</pre>';
        
        document.getElementById('testResult').innerHTML = content;
    })
    .catch(error => {
        document.getElementById('testResult').innerHTML = 
            '<div class="alert alert-danger"><i class="fas fa-times"></i> Error: ' + error.message + '</div>';
    });
}

function showToast(message, type = 'info') {
    // Crear toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i> ${message}
        <button type="button" class="close" onclick="this.parentElement.remove()">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove después de 3 segundos
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
<?= $this->endSection() ?>
