<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AdvancedLogger;

class BillingConfigController extends BaseController
{
    protected $db;
    protected $logger;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->logger = new AdvancedLogger();
    }

    /**
     * Mostrar configuración de facturación electrónica
     */
    public function index()
    {
        try {
            // Verificar permisos de administrador
            if (!session('admin_id')) {
                return redirect()->to('/admin/login');
            }

            // Obtener configuración actual
            $config = $this->getBillingConfig();
            
            $data = [
                'title' => 'Configuración de Facturación Electrónica',
                'config' => $config,
                'test_connection_result' => session()->getFlashdata('test_result')
            ];

            return view('admin/billing/config', $data);

        } catch (\Exception $e) {
            $this->logger->error('Error loading billing config: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error al cargar la configuración');
            return redirect()->to('/admin/dashboard');
        }
    }

    /**
     * Actualizar configuración de facturación
     */
    public function update()
    {
        try {
            // Validar datos de entrada
            $validation = \Config\Services::validation();
            $validation->setRules([
                'api_url' => 'required|valid_url',
                'api_key' => 'required|min_length[10]',
                'company_nit' => 'required|min_length[8]',
                'company_name' => 'required|min_length[3]',
                'company_address' => 'required|min_length[10]',
                'environment' => 'required|in_list[sandbox,production]',
                'auto_generate' => 'permit_empty|in_list[1]',
                'send_email' => 'permit_empty|in_list[1]',
                'timeout' => 'permit_empty|integer|greater_than[0]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                session()->setFlashdata('error', 'Datos inválidos: ' . implode(', ', $validation->getErrors()));
                return redirect()->back()->withInput();
            }

            // Preparar datos de configuración
            $configData = [
                'api_url' => $this->request->getPost('api_url'),
                'api_key' => $this->request->getPost('api_key'),
                'company_nit' => $this->request->getPost('company_nit'),
                'company_name' => $this->request->getPost('company_name'),
                'company_address' => $this->request->getPost('company_address'),
                'company_phone' => $this->request->getPost('company_phone'),
                'company_email' => $this->request->getPost('company_email'),
                'environment' => $this->request->getPost('environment'),
                'auto_generate' => $this->request->getPost('auto_generate') ? 1 : 0,
                'send_email' => $this->request->getPost('send_email') ? 1 : 0,
                'timeout' => $this->request->getPost('timeout') ?: 30,
                'retry_attempts' => $this->request->getPost('retry_attempts') ?: 3,
                'webhook_url' => $this->request->getPost('webhook_url'),
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_by' => session('admin_id')
            ];

            // Actualizar o insertar configuración
            $existingConfig = $this->getBillingConfig();
            
            if ($existingConfig) {
                $result = $this->db->table('billing_config')
                                  ->where('id', $existingConfig['id'])
                                  ->update($configData);
            } else {
                $configData['created_at'] = date('Y-m-d H:i:s');
                $configData['created_by'] = session('admin_id');
                $result = $this->db->table('billing_config')->insert($configData);
            }

            if ($result) {
                // Log de la acción
                $this->logger->userAction('billing_config_updated', session('admin_id'), [
                    'environment' => $configData['environment'],
                    'auto_generate' => $configData['auto_generate']
                ]);

                session()->setFlashdata('success', 'Configuración actualizada exitosamente');
            } else {
                session()->setFlashdata('error', 'Error al actualizar la configuración');
            }

            return redirect()->to('/admin/billing/config');

        } catch (\Exception $e) {
            $this->logger->error('Error updating billing config: ' . $e->getMessage(), [
                'admin_id' => session('admin_id')
            ]);
            
            session()->setFlashdata('error', 'Error interno: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Probar conexión con la API de facturación
     */
    public function testConnection()
    {
        try {
            $config = $this->getBillingConfig();
            
            if (!$config) {
                session()->setFlashdata('test_result', [
                    'success' => false,
                    'message' => 'No hay configuración guardada'
                ]);
                return redirect()->back();
            }

            // Realizar prueba de conexión
            $testResult = $this->performConnectionTest($config);
            
            session()->setFlashdata('test_result', $testResult);
            return redirect()->back();

        } catch (\Exception $e) {
            session()->setFlashdata('test_result', [
                'success' => false,
                'message' => 'Error en la prueba: ' . $e->getMessage()
            ]);
            return redirect()->back();
        }
    }

    /**
     * Ver historial de facturas generadas
     */
    public function invoiceHistory()
    {
        try {
            $page = $this->request->getGet('page') ?: 1;
            $perPage = 20;
            
            // Obtener facturas con paginación
            $invoices = $this->db->table('electronic_invoices ei')
                                ->select('ei.*, o.order_number, u.email as customer_email')
                                ->join('orders o', 'o.id = ei.order_id', 'left')
                                ->join('users u', 'u.id = o.user_id', 'left')
                                ->orderBy('ei.created_at', 'DESC')
                                ->paginate($perPage, 'default', $page);

            $pager = $this->db->table('electronic_invoices')->pager;

            $data = [
                'title' => 'Historial de Facturación Electrónica',
                'invoices' => $invoices,
                'pager' => $pager
            ];

            return view('admin/billing/history', $data);

        } catch (\Exception $e) {
            $this->logger->error('Error loading invoice history: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error al cargar el historial');
            return redirect()->to('/admin/billing/config');
        }
    }

    /**
     * Regenerar factura
     */
    public function regenerateInvoice($invoiceId)
    {
        try {
            $invoice = $this->db->table('electronic_invoices')
                               ->where('id', $invoiceId)
                               ->get()
                               ->getRowArray();

            if (!$invoice) {
                session()->setFlashdata('error', 'Factura no encontrada');
                return redirect()->back();
            }

            // Cargar el servicio de facturación
            $billingService = new \App\Libraries\ElectronicBillingService();
            
            $result = $billingService->regenerateInvoice($invoiceId);

            if ($result['success']) {
                session()->setFlashdata('success', 'Factura regenerada exitosamente');
            } else {
                session()->setFlashdata('error', 'Error al regenerar factura: ' . $result['error']);
            }

            return redirect()->back();

        } catch (\Exception $e) {
            $this->logger->error('Error regenerating invoice: ' . $e->getMessage(), [
                'invoice_id' => $invoiceId
            ]);
            
            session()->setFlashdata('error', 'Error interno al regenerar factura');
            return redirect()->back();
        }
    }

    /**
     * Descargar PDF de factura
     */
    public function downloadInvoice($invoiceId)
    {
        try {
            $invoice = $this->db->table('electronic_invoices')
                               ->where('id', $invoiceId)
                               ->get()
                               ->getRowArray();

            if (!$invoice || !$invoice['pdf_path']) {
                session()->setFlashdata('error', 'Archivo PDF no encontrado');
                return redirect()->back();
            }

            $filePath = WRITEPATH . 'uploads/invoices/' . $invoice['pdf_path'];
            
            if (!file_exists($filePath)) {
                session()->setFlashdata('error', 'Archivo PDF no existe en el servidor');
                return redirect()->back();
            }

            return $this->response->download($filePath, null);

        } catch (\Exception $e) {
            $this->logger->error('Error downloading invoice: ' . $e->getMessage(), [
                'invoice_id' => $invoiceId
            ]);
            
            session()->setFlashdata('error', 'Error al descargar factura');
            return redirect()->back();
        }
    }

    /**
     * Métodos privados auxiliares
     */
    private function getBillingConfig(): ?array
    {
        return $this->db->table('billing_config')
                       ->orderBy('id', 'DESC')
                       ->limit(1)
                       ->get()
                       ->getRowArray();
    }

    private function performConnectionTest(array $config): array
    {
        try {
            $client = \Config\Services::curlrequest();
            
            $response = $client->request('GET', $config['api_url'] . '/health', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $config['api_key'],
                    'Content-Type' => 'application/json'
                ],
                'timeout' => $config['timeout'] ?? 30
            ]);

            if ($response->getStatusCode() === 200) {
                $body = json_decode($response->getBody(), true);
                
                return [
                    'success' => true,
                    'message' => 'Conexión exitosa con la API de facturación',
                    'details' => $body['message'] ?? 'API respondió correctamente'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Error de conexión: HTTP ' . $response->getStatusCode(),
                    'details' => $response->getBody()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error de conexión: ' . $e->getMessage(),
                'details' => 'Verifique la URL y credenciales de la API'
            ];
        }
    }
}
