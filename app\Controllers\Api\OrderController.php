<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use App\Models\UserModel;

/**
 * API Controller para Pedidos
 */
class OrderController extends ResourceController
{
    use ResponseTrait;

    protected $format = 'json';
    protected $orderModel;
    protected $orderItemModel;
    protected $userModel;

    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->userModel = new UserModel();
    }

    /**
     * POST /api/orders
     * Crear nuevo pedido
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true) ?? $this->request->getPost();
            
            // Validación básica
            $validation = \Config\Services::validation();
            $validation->setRules([
                'customer_name' => 'required|min_length[2]',
                'customer_email' => 'required|valid_email',
                'customer_phone' => 'required|min_length[8]',
                'shipping_address' => 'required|min_length[10]',
                'payment_method' => 'required|in_list[cash,card,transfer]',
                'items' => 'required'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Obtener items del carrito
            $cart = session()->get('cart') ?? [];
            if (empty($cart)) {
                return $this->failValidationError('El carrito está vacío');
            }

            // Calcular totales
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }
            
            $taxAmount = $subtotal * 0.12; // 12% IVA
            $shippingAmount = $subtotal > 500 ? 0 : 50; // Envío gratis sobre Q500
            $totalAmount = $subtotal + $taxAmount + $shippingAmount;

            // Crear pedido
            $orderData = [
                'user_id' => session()->get('user_id') ?? null,
                'status' => 'pending',
                'total_amount' => $totalAmount,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'payment_method' => $data['payment_method'],
                'payment_status' => 'pending',
                'shipping_address' => json_encode([
                    'name' => $data['customer_name'],
                    'email' => $data['customer_email'],
                    'phone' => $data['customer_phone'],
                    'address' => $data['shipping_address'],
                    'city' => $data['city'] ?? 'Guatemala',
                    'country' => 'Guatemala'
                ]),
                'billing_address' => json_encode([
                    'name' => $data['customer_name'],
                    'email' => $data['customer_email'],
                    'phone' => $data['customer_phone'],
                    'address' => $data['billing_address'] ?? $data['shipping_address'],
                    'city' => $data['city'] ?? 'Guatemala',
                    'country' => 'Guatemala'
                ]),
                'notes' => $data['notes'] ?? null
            ];

            $orderId = $this->orderModel->insert($orderData);
            
            if (!$orderId) {
                return $this->failServerError('Error al crear el pedido');
            }

            // Crear items del pedido
            foreach ($cart as $productId => $item) {
                $itemData = [
                    'order_id' => $orderId,
                    'product_id' => $productId,
                    'product_name' => $item['name'],
                    'product_sku' => $item['sku'] ?? '',
                    'quantity' => $item['quantity'],
                    'price' => $item['price']
                ];
                
                $this->orderItemModel->insert($itemData);
            }

            // Limpiar carrito
            session()->remove('cart');

            // Obtener número de pedido
            $order = $this->orderModel->find($orderId);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Pedido creado exitosamente',
                'data' => [
                    'order_id' => $orderId,
                    'order_number' => $order['order_number'],
                    'total' => $totalAmount,
                    'status' => 'pending'
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderController::create: ' . $e->getMessage());
            return $this->failServerError('Error al procesar el pedido');
        }
    }

    /**
     * GET /api/orders/{id}
     * Obtener pedido específico
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de pedido requerido');
            }

            $order = $this->orderModel->getOrderWithItems($id);
            
            if (!$order) {
                return $this->failNotFound('Pedido no encontrado');
            }

            // Verificar permisos (solo el usuario propietario o admin)
            $userId = session()->get('user_id');
            $isAdmin = session()->get('admin_logged_in');
            
            if (!$isAdmin && $order['user_id'] != $userId) {
                return $this->failForbidden('No tienes permisos para ver este pedido');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $order
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderController::show: ' . $e->getMessage());
            return $this->failServerError('Error al obtener el pedido');
        }
    }

    /**
     * GET /api/orders/user/{user_id}
     * Obtener pedidos de un usuario
     */
    public function userOrders($userId = null)
    {
        try {
            // Si no se proporciona user_id, usar el de la sesión
            if (!$userId) {
                $userId = session()->get('user_id');
            }

            if (!$userId) {
                return $this->failUnauthorized('Usuario no autenticado');
            }

            // Verificar permisos
            $sessionUserId = session()->get('user_id');
            $isAdmin = session()->get('admin_logged_in');
            
            if (!$isAdmin && $sessionUserId != $userId) {
                return $this->failForbidden('No tienes permisos para ver estos pedidos');
            }

            $orders = $this->orderModel->getOrdersByUser($userId);

            return $this->respond([
                'status' => 'success',
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderController::userOrders: ' . $e->getMessage());
            return $this->failServerError('Error al obtener pedidos del usuario');
        }
    }

    /**
     * PUT /api/orders/{id}/status
     * Actualizar estado del pedido (solo admin)
     */
    public function updateStatus($id = null)
    {
        try {
            // Verificar permisos de admin
            if (!session()->get('admin_logged_in')) {
                return $this->failForbidden('Solo administradores pueden actualizar el estado');
            }

            if (!$id) {
                return $this->failValidationError('ID de pedido requerido');
            }

            $data = $this->request->getJSON(true) ?? $this->request->getPost();
            
            if (!isset($data['status'])) {
                return $this->failValidationError('Estado requerido');
            }

            $validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($data['status'], $validStatuses)) {
                return $this->failValidationError('Estado inválido');
            }

            $updated = $this->orderModel->updateOrderStatus($id, $data['status']);
            
            if (!$updated) {
                return $this->failServerError('Error al actualizar el estado');
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Estado del pedido actualizado'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderController::updateStatus: ' . $e->getMessage());
            return $this->failServerError('Error al actualizar estado del pedido');
        }
    }

    /**
     * GET /api/orders/track/{order_number}
     * Rastrear pedido por número
     */
    public function track($orderNumber = null)
    {
        try {
            if (!$orderNumber) {
                return $this->failValidationError('Número de pedido requerido');
            }

            $order = $this->orderModel->where('order_number', $orderNumber)->first();
            
            if (!$order) {
                return $this->failNotFound('Pedido no encontrado');
            }

            // Información básica de rastreo (sin datos sensibles)
            $trackingInfo = [
                'order_number' => $order['order_number'],
                'status' => $order['status'],
                'created_at' => $order['created_at'],
                'shipped_at' => $order['shipped_at'],
                'delivered_at' => $order['delivered_at']
            ];

            return $this->respond([
                'status' => 'success',
                'data' => $trackingInfo
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderController::track: ' . $e->getMessage());
            return $this->failServerError('Error al rastrear el pedido');
        }
    }
}
