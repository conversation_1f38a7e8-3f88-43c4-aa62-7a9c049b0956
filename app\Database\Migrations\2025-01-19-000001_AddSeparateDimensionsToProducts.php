<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddSeparateDimensionsToProducts extends Migration
{
    public function up()
    {
        // Agregar campos separados para dimensiones
        $fields = [
            'dimension_length' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'null' => true,
                'comment' => 'Largo en centímetros'
            ],
            'dimension_width' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'null' => true,
                'comment' => 'Ancho en centímetros'
            ],
            'dimension_height' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'null' => true,
                'comment' => 'Alto en centímetros'
            ],
            'dimension_unit' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'default' => 'cm',
                'comment' => 'Unidad de medida: cm, in, mm'
            ]
        ];

        $this->forge->addColumn('products', $fields);

        // Migrar datos existentes del campo dimensions al nuevo formato
        $this->migrateExistingDimensions();
    }

    public function down()
    {
        // Eliminar los nuevos campos
        $this->forge->dropColumn('products', [
            'dimension_length',
            'dimension_width', 
            'dimension_height',
            'dimension_unit'
        ]);
    }

    private function migrateExistingDimensions()
    {
        $db = \Config\Database::connect();
        
        // Obtener productos con dimensiones existentes
        $products = $db->query("
            SELECT id, dimensions 
            FROM products 
            WHERE dimensions IS NOT NULL 
              AND dimensions != '' 
              AND deleted_at IS NULL
        ")->getResultArray();

        foreach ($products as $product) {
            $dimensions = trim($product['dimensions']);
            
            if (empty($dimensions)) {
                continue;
            }

            // Intentar parsear diferentes formatos comunes
            $length = null;
            $width = null;
            $height = null;

            // Formato: "L x A x H" o "largo x ancho x alto"
            if (preg_match('/(\d+(?:\.\d+)?)\s*[x×]\s*(\d+(?:\.\d+)?)\s*[x×]\s*(\d+(?:\.\d+)?)/i', $dimensions, $matches)) {
                $length = (float) $matches[1];
                $width = (float) $matches[2];
                $height = (float) $matches[3];
            }
            // Formato: "L x A" (solo largo y ancho)
            elseif (preg_match('/(\d+(?:\.\d+)?)\s*[x×]\s*(\d+(?:\.\d+)?)/i', $dimensions, $matches)) {
                $length = (float) $matches[1];
                $width = (float) $matches[2];
            }
            // Formato: solo un número (asumir que es largo)
            elseif (preg_match('/(\d+(?:\.\d+)?)/i', $dimensions, $matches)) {
                $length = (float) $matches[1];
            }

            // Actualizar el producto con las dimensiones separadas
            if ($length || $width || $height) {
                $updateData = [];
                if ($length) $updateData['dimension_length'] = $length;
                if ($width) $updateData['dimension_width'] = $width;
                if ($height) $updateData['dimension_height'] = $height;
                $updateData['dimension_unit'] = 'cm'; // Asumir centímetros por defecto

                $builder = $db->table('products');
                $builder->where('id', $product['id']);
                $builder->update($updateData);

                echo "Producto ID {$product['id']}: '{$dimensions}' -> L:{$length}, A:{$width}, H:{$height}\n";
            }
        }
    }
}
