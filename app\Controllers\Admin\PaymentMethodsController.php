<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class PaymentMethodsController extends BaseController
{
    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    private function checkAuth()
    {
        if (!session()->get('admin_id') || !session()->get('is_admin_logged_in')) {
            return redirect()->to('/admin/login');
        }
        return true;
    }

    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            // Obtener métodos de pago con sus cuentas bancarias y puntos de recogida
            $paymentMethods = $this->db->query("
                SELECT pm.*, 
                       COUNT(DISTINCT ba.id) as bank_accounts_count,
                       COUNT(DISTINCT pl.id) as pickup_locations_count
                FROM payment_methods pm
                LEFT JOIN bank_accounts ba ON pm.id = ba.payment_method_id AND ba.is_active = 1
                LEFT JOIN pickup_locations pl ON pm.id = pl.payment_method_id AND pl.is_active = 1
                GROUP BY pm.id
                ORDER BY pm.sort_order, pm.name
            ")->getResultArray();

            $data = [
                'title' => 'Gestión de Métodos de Pago',
                'paymentMethods' => $paymentMethods
            ];

            return view('admin/payment_methods/index', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin')->with('error', 'Error al cargar métodos de pago: ' . $e->getMessage());
        }
    }

    public function view($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            // Obtener método de pago
            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$id])->getRowArray();
            
            if (!$paymentMethod) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Método de pago no encontrado');
            }

            // Obtener cuentas bancarias
            $bankAccounts = $this->db->query("
                SELECT * FROM bank_accounts 
                WHERE payment_method_id = ? AND is_active = 1 
                ORDER BY bank_name
            ", [$id])->getResultArray();

            // Obtener puntos de recogida
            $pickupLocations = $this->db->query("
                SELECT * FROM pickup_locations 
                WHERE payment_method_id = ? AND is_active = 1 
                ORDER BY name
            ", [$id])->getResultArray();

            $data = [
                'title' => 'Detalles de ' . $paymentMethod['name'],
                'paymentMethod' => $paymentMethod,
                'bankAccounts' => $bankAccounts,
                'pickupLocations' => $pickupLocations
            ];

            return view('admin/payment_methods/view', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error al cargar detalles: ' . $e->getMessage());
        }
    }

    public function toggleStatus($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$id])->getRowArray();
            
            if (!$paymentMethod) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Método de pago no encontrado');
            }

            $newStatus = $paymentMethod['is_active'] ? 0 : 1;
            $this->db->query("UPDATE payment_methods SET is_active = ? WHERE id = ?", [$newStatus, $id]);

            $statusText = $newStatus ? 'activado' : 'desactivado';
            return redirect()->to('/admin/payment-methods')->with('success', "Método de pago {$statusText} correctamente");

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error al cambiar estado: ' . $e->getMessage());
        }
    }

    public function addBankAccount($paymentMethodId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->storeBankAccount($paymentMethodId);
        }

        try {
            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$paymentMethodId])->getRowArray();
            
            if (!$paymentMethod) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Método de pago no encontrado');
            }

            $data = [
                'title' => 'Agregar Cuenta Bancaria',
                'paymentMethod' => $paymentMethod
            ];

            return view('admin/payment_methods/add_bank_account', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error: ' . $e->getMessage());
        }
    }

    private function storeBankAccount($paymentMethodId)
    {
        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $bankName = trim($this->request->getPost('bank_name'));
            $accountNumber = trim($this->request->getPost('account_number'));
            $accountType = trim($this->request->getPost('account_type'));
            $accountHolder = trim($this->request->getPost('account_holder'));
            $accountHolderId = trim($this->request->getPost('account_holder_id'));
            $instructions = trim($this->request->getPost('instructions'));

            // Validaciones
            if (empty($bankName) || empty($accountNumber) || empty($accountHolder)) {
                return redirect()->back()->with('error', 'Todos los campos obligatorios deben ser completados');
            }

            $this->db->query("
                INSERT INTO bank_accounts (payment_method_id, bank_name, account_number, account_type, account_holder, account_holder_id, instructions)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [$paymentMethodId, $bankName, $accountNumber, $accountType, $accountHolder, $accountHolderId, $instructions]);

            return redirect()->to("/admin/payment-methods/view/{$paymentMethodId}")->with('success', 'Cuenta bancaria agregada correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al agregar cuenta bancaria: ' . $e->getMessage());
        }
    }

    public function addPickupLocation($paymentMethodId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->storePickupLocation($paymentMethodId);
        }

        try {
            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$paymentMethodId])->getRowArray();
            
            if (!$paymentMethod) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Método de pago no encontrado');
            }

            $data = [
                'title' => 'Agregar Punto de Recogida',
                'paymentMethod' => $paymentMethod
            ];

            return view('admin/payment_methods/add_pickup_location', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error: ' . $e->getMessage());
        }
    }

    private function storePickupLocation($paymentMethodId)
    {
        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $name = trim($this->request->getPost('name'));
            $address = trim($this->request->getPost('address'));
            $phone = trim($this->request->getPost('phone'));
            $schedule = trim($this->request->getPost('schedule'));
            $deliveryFee = trim($this->request->getPost('delivery_fee'));
            $coverageZones = trim($this->request->getPost('coverage_zones'));
            $instructions = trim($this->request->getPost('instructions'));

            // Validaciones
            if (empty($name) || empty($address)) {
                return redirect()->back()->with('error', 'El nombre y la dirección son obligatorios');
            }

            $this->db->query("
                INSERT INTO pickup_locations (payment_method_id, name, address, phone, schedule, delivery_fee, coverage_zones, instructions)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ", [$paymentMethodId, $name, $address, $phone, $schedule, $deliveryFee, $coverageZones, $instructions]);

            return redirect()->to("/admin/payment-methods/view/{$paymentMethodId}")->with('success', 'Punto de recogida agregado correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al agregar punto de recogida: ' . $e->getMessage());
        }
    }

    public function editBankAccount($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->updateBankAccount($id);
        }

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $bankAccount = $this->db->query("SELECT * FROM bank_accounts WHERE id = ?", [$id])->getRowArray();

            if (!$bankAccount) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Cuenta bancaria no encontrada');
            }

            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$bankAccount['payment_method_id']])->getRowArray();

            $data = [
                'title' => 'Editar Cuenta Bancaria',
                'bankAccount' => $bankAccount,
                'paymentMethod' => $paymentMethod
            ];

            return view('admin/payment_methods/edit_bank_account', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error: ' . $e->getMessage());
        }
    }

    private function updateBankAccount($id)
    {
        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $bankAccount = $this->db->query("SELECT * FROM bank_accounts WHERE id = ?", [$id])->getRowArray();

            if (!$bankAccount) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Cuenta bancaria no encontrada');
            }

            $bankName = trim($this->request->getPost('bank_name'));
            $accountNumber = trim($this->request->getPost('account_number'));
            $accountType = trim($this->request->getPost('account_type'));
            $accountHolder = trim($this->request->getPost('account_holder'));
            $accountHolderId = trim($this->request->getPost('account_holder_id'));
            $instructions = trim($this->request->getPost('instructions'));

            // Validaciones
            if (empty($bankName) || empty($accountNumber) || empty($accountHolder)) {
                return redirect()->back()->with('error', 'Todos los campos obligatorios deben ser completados');
            }

            $this->db->query("
                UPDATE bank_accounts
                SET bank_name = ?, account_number = ?, account_type = ?, account_holder = ?, account_holder_id = ?, instructions = ?, updated_at = NOW()
                WHERE id = ?
            ", [$bankName, $accountNumber, $accountType, $accountHolder, $accountHolderId, $instructions, $id]);

            return redirect()->to("/admin/payment-methods/view/{$bankAccount['payment_method_id']}")->with('success', 'Cuenta bancaria actualizada correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al actualizar cuenta bancaria: ' . $e->getMessage());
        }
    }

    public function deleteBankAccount($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $bankAccount = $this->db->query("SELECT * FROM bank_accounts WHERE id = ?", [$id])->getRowArray();

            if (!$bankAccount) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Cuenta bancaria no encontrada');
            }

            $this->db->query("DELETE FROM bank_accounts WHERE id = ?", [$id]);

            return redirect()->to("/admin/payment-methods/view/{$bankAccount['payment_method_id']}")->with('success', 'Cuenta bancaria eliminada correctamente');

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error al eliminar cuenta bancaria: ' . $e->getMessage());
        }
    }

    public function editPickupLocation($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->updatePickupLocation($id);
        }

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $pickupLocation = $this->db->query("SELECT * FROM pickup_locations WHERE id = ?", [$id])->getRowArray();

            if (!$pickupLocation) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Punto de recogida no encontrado');
            }

            $paymentMethod = $this->db->query("SELECT * FROM payment_methods WHERE id = ?", [$pickupLocation['payment_method_id']])->getRowArray();

            $data = [
                'title' => 'Editar Punto de Recogida',
                'pickupLocation' => $pickupLocation,
                'paymentMethod' => $paymentMethod
            ];

            return view('admin/payment_methods/edit_pickup_location', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error: ' . $e->getMessage());
        }
    }

    private function updatePickupLocation($id)
    {
        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $pickupLocation = $this->db->query("SELECT * FROM pickup_locations WHERE id = ?", [$id])->getRowArray();

            if (!$pickupLocation) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Punto de recogida no encontrado');
            }

            $name = trim($this->request->getPost('name'));
            $address = trim($this->request->getPost('address'));
            $phone = trim($this->request->getPost('phone'));
            $schedule = trim($this->request->getPost('schedule'));
            $deliveryFee = trim($this->request->getPost('delivery_fee'));
            $coverageZones = trim($this->request->getPost('coverage_zones'));
            $instructions = trim($this->request->getPost('instructions'));

            // Validaciones
            if (empty($name) || empty($address)) {
                return redirect()->back()->with('error', 'El nombre y la dirección son obligatorios');
            }

            $this->db->query("
                UPDATE pickup_locations
                SET name = ?, address = ?, phone = ?, schedule = ?, delivery_fee = ?, coverage_zones = ?, instructions = ?, updated_at = NOW()
                WHERE id = ?
            ", [$name, $address, $phone, $schedule, $deliveryFee, $coverageZones, $instructions, $id]);

            return redirect()->to("/admin/payment-methods/view/{$pickupLocation['payment_method_id']}")->with('success', 'Punto de recogida actualizado correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al actualizar punto de recogida: ' . $e->getMessage());
        }
    }

    public function deletePickupLocation($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $pickupLocation = $this->db->query("SELECT * FROM pickup_locations WHERE id = ?", [$id])->getRowArray();

            if (!$pickupLocation) {
                return redirect()->to('/admin/payment-methods')->with('error', 'Punto de recogida no encontrado');
            }

            $this->db->query("DELETE FROM pickup_locations WHERE id = ?", [$id]);

            return redirect()->to("/admin/payment-methods/view/{$pickupLocation['payment_method_id']}")->with('success', 'Punto de recogida eliminado correctamente');

        } catch (\Exception $e) {
            return redirect()->to('/admin/payment-methods')->with('error', 'Error al eliminar punto de recogida: ' . $e->getMessage());
        }
    }

    public function store()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Configurar codificación UTF-8
            $this->db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");

            $name = trim($this->request->getPost('name'));
            $type = trim($this->request->getPost('type'));
            $description = trim($this->request->getPost('description'));
            $icon = trim($this->request->getPost('icon'));
            $instructions = trim($this->request->getPost('instructions'));

            // Validaciones
            if (empty($name) || empty($type)) {
                return redirect()->back()->with('error', 'El nombre y el tipo son obligatorios');
            }

            // Crear slug
            $slug = strtolower(str_replace(' ', '-', $name));
            $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);

            $this->db->query("
                INSERT INTO payment_methods (name, slug, description, type, icon, instructions, is_active, sort_order)
                VALUES (?, ?, ?, ?, ?, ?, 1, 0)
            ", [$name, $slug, $description, $type, $icon, $instructions]);

            return redirect()->to('/admin/payment-methods')->with('success', 'Método de pago creado correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al crear método de pago: ' . $e->getMessage());
        }
    }
}
