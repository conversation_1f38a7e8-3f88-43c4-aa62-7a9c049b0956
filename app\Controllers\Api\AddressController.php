<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class AddressController extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $session;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->session = session();
    }

    /**
     * Get user addresses
     */
    public function getUserAddresses()
    {
        try {
            // For now, we'll use session-based user identification
            // In a real app, you'd get this from JWT token or session
            $userId = $this->session->get('user_id');
            
            if (!$userId) {
                // Return empty addresses for guest users
                return $this->respond([
                    'success' => true,
                    'addresses' => []
                ]);
            }

            $addresses = $this->db->query("
                SELECT id, address_name, address, city, state, zip_code, country, is_default
                FROM user_addresses 
                WHERE user_id = ? AND deleted_at IS NULL
                ORDER BY is_default DESC, created_at DESC
            ", [$userId])->getResultArray();

            return $this->respond([
                'success' => true,
                'addresses' => $addresses
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting user addresses: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'message' => 'Error al obtener direcciones'
            ], 500);
        }
    }

    /**
     * Save new address
     */
    public function saveAddress()
    {
        try {
            $userId = $this->session->get('user_id');
            
            if (!$userId) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Debe iniciar sesión para guardar direcciones'
                ], 401);
            }

            $data = $this->request->getPost();
            
            // Validate required fields
            $required = ['address_name', 'address', 'city', 'state'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->respond([
                        'success' => false,
                        'message' => "El campo {$field} es requerido"
                    ], 400);
                }
            }

            // Check if table exists, if not create it
            $this->ensureAddressTableExists();

            // If this is set as default, unset other defaults
            if (!empty($data['is_default'])) {
                $this->db->query("
                    UPDATE user_addresses 
                    SET is_default = 0 
                    WHERE user_id = ? AND deleted_at IS NULL
                ", [$userId]);
            }

            // Insert new address
            $addressData = [
                'user_id' => $userId,
                'address_name' => trim($data['address_name']),
                'address' => trim($data['address']),
                'city' => trim($data['city']),
                'state' => trim($data['state']),
                'zip_code' => trim($data['zip_code'] ?? ''),
                'country' => trim($data['country'] ?? 'Guatemala'),
                'is_default' => !empty($data['is_default']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->db->table('user_addresses')->insert($addressData);
            $addressId = $this->db->insertID();

            return $this->respond([
                'success' => true,
                'message' => 'Dirección guardada correctamente',
                'address_id' => $addressId
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error saving address: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'message' => 'Error al guardar la dirección: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update address
     */
    public function updateAddress($addressId)
    {
        try {
            $userId = $this->session->get('user_id');
            
            if (!$userId) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Debe iniciar sesión'
                ], 401);
            }

            $data = $this->request->getJSON(true);
            
            // Verify address belongs to user
            $address = $this->db->query("
                SELECT id FROM user_addresses 
                WHERE id = ? AND user_id = ? AND deleted_at IS NULL
            ", [$addressId, $userId])->getRow();

            if (!$address) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Dirección no encontrada'
                ], 404);
            }

            // If this is set as default, unset other defaults
            if (!empty($data['is_default'])) {
                $this->db->query("
                    UPDATE user_addresses 
                    SET is_default = 0 
                    WHERE user_id = ? AND id != ? AND deleted_at IS NULL
                ", [$userId, $addressId]);
            }

            // Update address
            $updateData = [
                'address_name' => trim($data['address_name']),
                'address' => trim($data['address']),
                'city' => trim($data['city']),
                'state' => trim($data['state']),
                'zip_code' => trim($data['zip_code'] ?? ''),
                'country' => trim($data['country'] ?? 'Guatemala'),
                'is_default' => !empty($data['is_default']) ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->db->table('user_addresses')
                     ->where('id', $addressId)
                     ->update($updateData);

            return $this->respond([
                'success' => true,
                'message' => 'Dirección actualizada correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error updating address: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'message' => 'Error al actualizar la dirección'
            ], 500);
        }
    }

    /**
     * Delete address
     */
    public function deleteAddress($addressId)
    {
        try {
            $userId = $this->session->get('user_id');
            
            if (!$userId) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Debe iniciar sesión'
                ], 401);
            }

            // Verify address belongs to user
            $address = $this->db->query("
                SELECT id FROM user_addresses 
                WHERE id = ? AND user_id = ? AND deleted_at IS NULL
            ", [$addressId, $userId])->getRow();

            if (!$address) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Dirección no encontrada'
                ], 404);
            }

            // Soft delete
            $this->db->table('user_addresses')
                     ->where('id', $addressId)
                     ->update(['deleted_at' => date('Y-m-d H:i:s')]);

            return $this->respond([
                'success' => true,
                'message' => 'Dirección eliminada correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error deleting address: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'message' => 'Error al eliminar la dirección'
            ], 500);
        }
    }

    /**
     * Ensure user_addresses table exists
     */
    private function ensureAddressTableExists()
    {
        try {
            $tableExists = $this->db->query("SHOW TABLES LIKE 'user_addresses'")->getNumRows() > 0;
            
            if (!$tableExists) {
                $this->db->query("
                    CREATE TABLE user_addresses (
                        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                        user_id INT(11) UNSIGNED NOT NULL,
                        address_name VARCHAR(100) NOT NULL,
                        address TEXT NOT NULL,
                        city VARCHAR(100) NOT NULL,
                        state VARCHAR(100) NOT NULL,
                        zip_code VARCHAR(20) NULL,
                        country VARCHAR(100) DEFAULT 'Guatemala',
                        is_default TINYINT(1) DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        deleted_at TIMESTAMP NULL,
                        INDEX idx_user_id (user_id),
                        INDEX idx_is_default (is_default),
                        INDEX idx_deleted_at (deleted_at)
                    )
                ");
                
                log_message('info', 'user_addresses table created successfully');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating user_addresses table: ' . $e->getMessage());
            throw $e;
        }
    }
}
