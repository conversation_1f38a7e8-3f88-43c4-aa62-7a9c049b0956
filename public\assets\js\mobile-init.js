/**
 * Inicializador del Sistema Móvil de MrCell
 * PWA, Notificaciones Push, Gestos Táctiles y Optimizaciones
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class MrCellMobileSystem {
    constructor() {
        this.config = null;
        this.isOnline = navigator.onLine;
        this.isPWA = window.matchMedia('(display-mode: standalone)').matches;
        this.deviceType = this.detectDeviceType();
        this.pushSubscription = null;
        
        this.init();
    }
    
    /**
     * Inicializar sistema móvil
     */
    async init() {
        console.log('🚀 MrCell Mobile System: Initializing...');
        
        try {
            // Cargar configuración
            await this.loadConfig();
            
            // Registrar Service Worker
            await this.registerServiceWorker();
            
            // Inicializar funcionalidades
            this.initPWA();
            this.initPushNotifications();
            this.initTouchGestures();
            this.initImageOptimization();
            this.initOfflineSupport();
            this.initAnalytics();
            
            // Registrar sesión
            this.registerSession();
            
            console.log('✅ MrCell Mobile System: Initialized successfully');
            
        } catch (error) {
            console.error('❌ MrCell Mobile System: Initialization error:', error);
        }
    }
    
    /**
     * Cargar configuración móvil
     */
    async loadConfig() {
        try {
            const response = await fetch('/api/mobile/config');
            this.config = await response.json();
            
            console.log('📱 Device detected:', this.config.device.device_type);
            
        } catch (error) {
            console.error('Error loading mobile config:', error);
            // Configuración por defecto
            this.config = {
                device: { is_mobile: this.deviceType === 'mobile' },
                push_notifications: { enabled: false },
                features: {}
            };
        }
    }
    
    /**
     * Registrar Service Worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('✅ Service Worker registered:', registration);
                
                // Escuchar actualizaciones
                registration.addEventListener('updatefound', () => {
                    console.log('🔄 Service Worker update found');
                    this.showUpdateNotification();
                });
                
                return registration;
                
            } catch (error) {
                console.error('❌ Service Worker registration failed:', error);
            }
        }
    }
    
    /**
     * Inicializar PWA
     */
    initPWA() {
        // Detectar si es PWA
        if (this.isPWA) {
            document.body.classList.add('pwa-mode');
            console.log('📱 Running as PWA');
        }
        
        // Manejar evento de instalación
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallPrompt(deferredPrompt);
        });
        
        // Detectar instalación exitosa
        window.addEventListener('appinstalled', () => {
            console.log('🎉 PWA installed successfully');
            this.trackEvent('pwa_installed');
        });
    }
    
    /**
     * Mostrar prompt de instalación PWA
     */
    showInstallPrompt(deferredPrompt) {
        // Solo mostrar en móviles
        if (!this.config.device.is_mobile) return;
        
        const installBanner = document.createElement('div');
        installBanner.className = 'pwa-install-banner';
        installBanner.innerHTML = `
            <div class="pwa-install-content">
                <div class="pwa-install-icon">📱</div>
                <div class="pwa-install-text">
                    <strong>¡Instala MrCell!</strong>
                    <p>Acceso rápido y notificaciones de ofertas</p>
                </div>
                <button class="pwa-install-btn" id="pwa-install">Instalar</button>
                <button class="pwa-install-close" id="pwa-close">×</button>
            </div>
        `;
        
        document.body.appendChild(installBanner);
        
        // Manejar instalación
        document.getElementById('pwa-install').addEventListener('click', async () => {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('✅ PWA installation accepted');
                this.trackEvent('pwa_install_accepted');
            } else {
                console.log('❌ PWA installation dismissed');
                this.trackEvent('pwa_install_dismissed');
            }
            
            installBanner.remove();
            deferredPrompt = null;
        });
        
        // Cerrar banner
        document.getElementById('pwa-close').addEventListener('click', () => {
            installBanner.remove();
            this.trackEvent('pwa_install_closed');
        });
    }
    
    /**
     * Inicializar notificaciones push
     */
    async initPushNotifications() {
        if (!this.config.push_notifications.enabled) {
            console.log('📵 Push notifications disabled');
            return;
        }
        
        if (!('Notification' in window) || !('serviceWorker' in navigator)) {
            console.log('📵 Push notifications not supported');
            return;
        }
        
        // Verificar permisos
        if (Notification.permission === 'granted') {
            await this.subscribeToPush();
        } else if (Notification.permission !== 'denied') {
            this.showPushPermissionPrompt();
        }
    }
    
    /**
     * Mostrar prompt de permisos push
     */
    showPushPermissionPrompt() {
        const pushPrompt = document.createElement('div');
        pushPrompt.className = 'push-permission-prompt';
        pushPrompt.innerHTML = `
            <div class="push-prompt-content">
                <div class="push-prompt-icon">🔔</div>
                <div class="push-prompt-text">
                    <strong>¡No te pierdas las ofertas!</strong>
                    <p>Recibe notificaciones de descuentos y productos nuevos</p>
                </div>
                <button class="push-prompt-allow" id="push-allow">Permitir</button>
                <button class="push-prompt-deny" id="push-deny">Ahora no</button>
            </div>
        `;
        
        document.body.appendChild(pushPrompt);
        
        document.getElementById('push-allow').addEventListener('click', async () => {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                await this.subscribeToPush();
                this.trackEvent('push_permission_granted');
            } else {
                this.trackEvent('push_permission_denied');
            }
            
            pushPrompt.remove();
        });
        
        document.getElementById('push-deny').addEventListener('click', () => {
            pushPrompt.remove();
            this.trackEvent('push_permission_dismissed');
        });
    }
    
    /**
     * Suscribirse a notificaciones push
     */
    async subscribeToPush() {
        try {
            const registration = await navigator.serviceWorker.ready;
            
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(this.config.push_notifications.vapid_public_key)
            });
            
            // Enviar suscripción al servidor
            const response = await fetch('/api/mobile/push/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(subscription)
            });
            
            if (response.ok) {
                this.pushSubscription = subscription;
                console.log('✅ Push subscription successful');
                this.trackEvent('push_subscribed');
            } else {
                console.error('❌ Push subscription failed');
            }
            
        } catch (error) {
            console.error('❌ Push subscription error:', error);
        }
    }
    
    /**
     * Inicializar gestos táctiles
     */
    initTouchGestures() {
        if (!this.config.device.is_mobile) return;
        
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe(startX, startY, endX, endY);
        }, { passive: true });
        
        // Pull to refresh
        this.initPullToRefresh();
        
        console.log('👆 Touch gestures initialized');
    }
    
    /**
     * Manejar gestos de deslizamiento
     */
    handleSwipe(startX, startY, endX, endY) {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Swipe horizontal
            if (Math.abs(deltaX) > minSwipeDistance) {
                if (deltaX > 0) {
                    this.onSwipeRight();
                } else {
                    this.onSwipeLeft();
                }
            }
        } else {
            // Swipe vertical
            if (Math.abs(deltaY) > minSwipeDistance) {
                if (deltaY > 0) {
                    this.onSwipeDown();
                } else {
                    this.onSwipeUp();
                }
            }
        }
    }
    
    /**
     * Swipe hacia la derecha
     */
    onSwipeRight() {
        // Ir atrás en el historial
        if (window.history.length > 1) {
            window.history.back();
        }
    }
    
    /**
     * Swipe hacia la izquierda
     */
    onSwipeLeft() {
        // Siguiente imagen en galería
        const nextBtn = document.querySelector('.product-gallery .next');
        if (nextBtn) nextBtn.click();
    }
    
    /**
     * Swipe hacia arriba
     */
    onSwipeUp() {
        // Mostrar más contenido
        const showMoreBtn = document.querySelector('.show-more');
        if (showMoreBtn) showMoreBtn.click();
    }
    
    /**
     * Swipe hacia abajo
     */
    onSwipeDown() {
        // Pull to refresh si está en el top
        if (window.scrollY === 0) {
            this.triggerRefresh();
        }
    }
    
    /**
     * Inicializar pull to refresh
     */
    initPullToRefresh() {
        let startY = 0;
        let pullDistance = 0;
        const threshold = 100;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (window.scrollY === 0 && startY > 0) {
                pullDistance = e.touches[0].clientY - startY;
                if (pullDistance > 0) {
                    this.showPullIndicator(pullDistance);
                }
            }
        });
        
        document.addEventListener('touchend', () => {
            if (pullDistance > threshold) {
                this.triggerRefresh();
            }
            this.hidePullIndicator();
            startY = 0;
            pullDistance = 0;
        });
    }
    
    /**
     * Mostrar indicador de pull to refresh
     */
    showPullIndicator(distance) {
        let indicator = document.getElementById('pull-refresh-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'pull-refresh-indicator';
            indicator.innerHTML = '↓ Desliza para actualizar';
            indicator.style.cssText = `
                position: fixed;
                top: -50px;
                left: 50%;
                transform: translateX(-50%);
                background: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                z-index: 9999;
                transition: top 0.3s ease;
                font-size: 14px;
            `;
            document.body.appendChild(indicator);
        }
        
        const progress = Math.min(distance / 100, 1);
        indicator.style.top = (progress * 50 - 50) + 'px';
        
        if (progress >= 1) {
            indicator.innerHTML = '↑ Suelta para actualizar';
            indicator.style.background = '#28a745';
        }
    }
    
    /**
     * Ocultar indicador de pull to refresh
     */
    hidePullIndicator() {
        const indicator = document.getElementById('pull-refresh-indicator');
        if (indicator) {
            indicator.style.top = '-50px';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }
    }
    
    /**
     * Activar refresh
     */
    triggerRefresh() {
        console.log('🔄 Pull to refresh triggered');
        
        // Mostrar loading
        const indicator = document.getElementById('pull-refresh-indicator');
        if (indicator) {
            indicator.innerHTML = '⟳ Actualizando...';
            indicator.style.background = '#ffc107';
            indicator.style.top = '10px';
        }
        
        // Recargar página después de 1 segundo
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
        this.trackEvent('pull_to_refresh');
    }
    
    /**
     * Inicializar optimización de imágenes
     */
    initImageOptimization() {
        if (!this.config.features.supports_webp) return;
        
        // Lazy loading para imágenes
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback para navegadores sin IntersectionObserver
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
            });
        }
        
        console.log('🖼️ Image optimization initialized');
    }
    
    /**
     * Inicializar soporte offline
     */
    initOfflineSupport() {
        // Detectar cambios de conectividad
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showConnectivityStatus('online');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showConnectivityStatus('offline');
        });
        
        console.log('📶 Offline support initialized');
    }
    
    /**
     * Mostrar estado de conectividad
     */
    showConnectivityStatus(status) {
        const statusBar = document.createElement('div');
        statusBar.className = `connectivity-status ${status}`;
        statusBar.innerHTML = status === 'online' ? 
            '✅ Conectado' : '📵 Sin conexión - Modo offline';
        
        document.body.appendChild(statusBar);
        
        setTimeout(() => {
            statusBar.remove();
        }, 3000);
    }
    
    /**
     * Sincronizar datos offline
     */
    async syncOfflineData() {
        if ('serviceWorker' in navigator) {
            const registration = await navigator.serviceWorker.ready;
            
            // Sincronizar wishlist
            registration.sync.register('wishlist-sync');
            
            // Sincronizar carrito
            registration.sync.register('cart-sync');
            
            // Sincronizar analytics
            registration.sync.register('analytics-sync');
        }
    }
    
    /**
     * Inicializar analytics móviles
     */
    initAnalytics() {
        // Registrar sesión móvil
        this.registerSession();
        
        // Trackear eventos importantes
        this.trackPageView();
        
        console.log('📊 Mobile analytics initialized');
    }
    
    /**
     * Registrar sesión móvil
     */
    async registerSession() {
        try {
            const sessionData = {
                screen_width: screen.width,
                screen_height: screen.height,
                is_pwa: this.isPWA,
                connection_type: this.getConnectionType()
            };
            
            await fetch('/api/mobile/session/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sessionData)
            });
            
        } catch (error) {
            console.error('Error registering mobile session:', error);
        }
    }
    
    /**
     * Trackear evento
     */
    trackEvent(eventName, data = {}) {
        console.log(`📊 Event tracked: ${eventName}`, data);
        
        // Enviar a analytics (implementar según necesidades)
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, data);
        }
    }
    
    /**
     * Trackear vista de página
     */
    trackPageView() {
        this.trackEvent('page_view', {
            device_type: this.deviceType,
            is_pwa: this.isPWA,
            is_online: this.isOnline
        });
    }
    
    /**
     * Detectar tipo de dispositivo
     */
    detectDeviceType() {
        const userAgent = navigator.userAgent;
        
        if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
            return 'tablet';
        } else if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
            return 'mobile';
        } else {
            return 'desktop';
        }
    }
    
    /**
     * Obtener tipo de conexión
     */
    getConnectionType() {
        if ('connection' in navigator) {
            return navigator.connection.effectiveType || 'unknown';
        }
        return 'unknown';
    }
    
    /**
     * Mostrar notificación de actualización
     */
    showUpdateNotification() {
        const updateNotification = document.createElement('div');
        updateNotification.className = 'update-notification';
        updateNotification.innerHTML = `
            <div class="update-content">
                <span>🔄 Nueva versión disponible</span>
                <button id="update-reload">Actualizar</button>
            </div>
        `;
        
        document.body.appendChild(updateNotification);
        
        document.getElementById('update-reload').addEventListener('click', () => {
            window.location.reload();
        });
    }
    
    /**
     * Convertir VAPID key
     */
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        
        return outputArray;
    }
}

// Inicializar sistema móvil cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new MrCellMobileSystem();
    });
} else {
    new MrCellMobileSystem();
}
