<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Transferencia Bancaria - MrCell Guatemala' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        .payment-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .payment-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .bank-account {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .bank-account:hover {
            border-color: var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,123,255,0.2);
        }
        
        .bank-account.selected {
            border-color: var(--primary-color);
            background: #f0f8ff;
        }
        
        .bank-logo {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .instructions {
            background: #e7f3ff;
            border-left: 4px solid var(--info-color);
            padding: 20px;
            border-radius: 0 10px 10px 0;
            margin-bottom: 30px;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .btn-confirm {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            border: none;
            border-radius: 10px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }
    </style>
</head>
<body>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-mobile-alt me-2"></i>MrCell
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?= base_url() ?>">
                    <i class="fas fa-home me-1"></i>Inicio
                </a>
            </div>
        </div>
    </nav>

    <div class="payment-container">
        <div class="container">
            <div class="payment-card">
                <div class="payment-header">
                    <h2><i class="fas fa-university me-2"></i>Transferencia Bancaria</h2>
                    <p class="mb-0">Completa tu pago mediante transferencia bancaria</p>
                </div>

                <div class="p-4">
                    <!-- Resumen del Pedido -->
                    <div class="order-summary">
                        <h5><i class="fas fa-receipt me-2"></i>Resumen del Pedido</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Número de Pedido:</strong> <?= $order['order_number'] ?></p>
                                <p><strong>Cliente:</strong> <?= $order['customer_name'] ?></p>
                                <p><strong>Email:</strong> <?= $order['customer_email'] ?></p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <p><strong>Subtotal:</strong> Q<?= number_format($order['subtotal'], 2) ?></p>
                                <p><strong>IVA:</strong> Q<?= number_format($order['tax_amount'], 2) ?></p>
                                <p><strong>Envío:</strong> Q<?= number_format($order['shipping_cost'], 2) ?></p>
                                <h5 class="text-primary"><strong>Total: Q<?= number_format($order['total'], 2) ?></strong></h5>
                            </div>
                        </div>
                    </div>

                    <!-- Instrucciones -->
                    <div class="instructions">
                        <h6><i class="fas fa-info-circle me-2"></i>Instrucciones para la Transferencia</h6>
                        <ol class="mb-0">
                            <?php foreach ($payment_instructions as $instruction): ?>
                                <li><?= $instruction ?></li>
                            <?php endforeach; ?>
                        </ol>
                    </div>

                    <!-- Cuentas Bancarias -->
                    <h5 class="mb-3"><i class="fas fa-building-columns me-2"></i>Selecciona una Cuenta Bancaria</h5>
                    
                    <div id="bank-accounts">
                        <?php foreach ($bank_accounts as $key => $account): ?>
                            <div class="bank-account" data-bank="<?= $key ?>">
                                <div class="d-flex align-items-center">
                                    <div class="bank-logo">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?= $account['name'] ?></h6>
                                        <p class="mb-1"><strong>Cuenta:</strong> <?= $account['account_number'] ?></p>
                                        <p class="mb-1"><strong>Tipo:</strong> <?= $account['account_type'] ?></p>
                                        <p class="mb-0"><strong>Titular:</strong> <?= $account['account_holder'] ?></p>
                                    </div>
                                    <div class="text-end">
                                        <i class="fas fa-check-circle text-success d-none selected-icon"></i>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Formulario de Confirmación -->
                    <div class="mt-4">
                        <h5><i class="fas fa-file-upload me-2"></i>Confirmar Transferencia</h5>
                        
                        <?php if (session()->getFlashdata('errors')): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                        <li><?= $error ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form action="<?= base_url('payment/confirm-transfer') ?>" method="POST" id="transferForm">
                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                            <input type="hidden" name="bank_account" id="selected_bank">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="transfer_reference" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i>Número de Referencia *
                                    </label>
                                    <input type="text" class="form-control" id="transfer_reference" 
                                           name="transfer_reference" required
                                           placeholder="Ej: *********" 
                                           value="<?= old('transfer_reference') ?>">
                                    <small class="text-muted">Número de referencia de la transferencia</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="transfer_amount" class="form-label">
                                        <i class="fas fa-dollar-sign me-1"></i>Monto Transferido *
                                    </label>
                                    <input type="number" class="form-control" id="transfer_amount" 
                                           name="transfer_amount" step="0.01" required
                                           value="<?= $order['total'] ?>" readonly>
                                    <small class="text-muted">Debe coincidir exactamente con el total del pedido</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="transfer_date" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Fecha de Transferencia *
                                    </label>
                                    <input type="date" class="form-control" id="transfer_date" 
                                           name="transfer_date" required
                                           value="<?= old('transfer_date', date('Y-m-d')) ?>">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="transfer_time" class="form-label">
                                        <i class="fas fa-clock me-1"></i>Hora de Transferencia
                                    </label>
                                    <input type="time" class="form-control" id="transfer_time" 
                                           name="transfer_time"
                                           value="<?= old('transfer_time') ?>">
                                </div>

                                <div class="col-12 mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-comment me-1"></i>Notas Adicionales
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"
                                              placeholder="Información adicional sobre la transferencia (opcional)"><?= old('notes') ?></textarea>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-success btn-confirm" id="confirmBtn" disabled>
                                    <i class="fas fa-check me-2"></i>Confirmar Transferencia
                                </button>
                            </div>

                            <div class="text-center mt-3">
                                <a href="<?= base_url('checkout') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Volver al Checkout
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const bankAccounts = document.querySelectorAll('.bank-account');
            const selectedBankInput = document.getElementById('selected_bank');
            const confirmBtn = document.getElementById('confirmBtn');

            // Manejar selección de cuenta bancaria
            bankAccounts.forEach(account => {
                account.addEventListener('click', function() {
                    // Remover selección anterior
                    bankAccounts.forEach(acc => {
                        acc.classList.remove('selected');
                        acc.querySelector('.selected-icon').classList.add('d-none');
                    });

                    // Seleccionar cuenta actual
                    this.classList.add('selected');
                    this.querySelector('.selected-icon').classList.remove('d-none');

                    // Actualizar input hidden
                    selectedBankInput.value = this.dataset.bank;

                    // Habilitar botón de confirmación
                    confirmBtn.disabled = false;
                });
            });

            // Validar formulario antes de enviar
            document.getElementById('transferForm').addEventListener('submit', function(e) {
                if (!selectedBankInput.value) {
                    e.preventDefault();
                    alert('Por favor selecciona una cuenta bancaria');
                    return false;
                }

                const reference = document.getElementById('transfer_reference').value;
                if (reference.length < 5) {
                    e.preventDefault();
                    alert('El número de referencia debe tener al menos 5 caracteres');
                    return false;
                }

                return confirm('¿Confirmas que has realizado la transferencia con los datos proporcionados?');
            });

            // Auto-completar fecha actual
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('transfer_date').max = today;
        });
    </script>
</body>
</html>
