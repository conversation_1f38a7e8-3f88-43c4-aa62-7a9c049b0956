<?php

namespace App\Models;

use CodeIgniter\Model;

class WhatsAppMessageLogModel extends BaseModel
{
    protected $table = 'whatsapp_message_log';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'phone_number',
        'template_key',
        'message_content',
        'status',
        'api_response',
        'error_message',
        'sent_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'phone_number' => 'required|max_length[20]',
        'message_content' => 'required',
        'status' => 'permit_empty|in_list[sent,failed,pending]'
    ];

    protected $validationMessages = [
        'phone_number' => [
            'required' => 'El número de teléfono es requerido'
        ],
        'message_content' => [
            'required' => 'El contenido del mensaje es requerido'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['processApiResponse'];
    protected $beforeUpdate = ['processApiResponse'];
    protected $afterFind = ['parseApiResponse'];

    /**
     * Procesar respuesta de API antes de guardar
     */
    protected function processApiResponse(array $data)
    {
        if (isset($data['data']['api_response']) && is_array($data['data']['api_response'])) {
            $data['data']['api_response'] = json_encode($data['data']['api_response']);
        }
        return $data;
    }

    /**
     * Parsear respuesta de API después de obtener
     */
    protected function parseApiResponse(array $data)
    {
        if (isset($data['data'])) {
            // Caso de un solo registro
            if (isset($data['data']['api_response']) && is_string($data['data']['api_response'])) {
                $data['data']['api_response'] = json_decode($data['data']['api_response'], true) ?: [];
            }
        } else {
            // Caso de múltiples registros
            foreach ($data['data'] as &$row) {
                if (isset($row['api_response']) && is_string($row['api_response'])) {
                    $row['api_response'] = json_decode($row['api_response'], true) ?: [];
                }
            }
        }
        return $data;
    }

    /**
     * Registrar mensaje enviado
     */
    public function logMessage($data)
    {
        $logData = [
            'user_id' => $data['user_id'] ?? null,
            'phone_number' => $data['phone_number'],
            'template_key' => $data['template_key'] ?? null,
            'message_content' => $data['message_content'],
            'status' => $data['status'] ?? 'pending',
            'api_response' => $data['api_response'] ?? null,
            'error_message' => $data['error_message'] ?? null,
            'sent_at' => $data['sent_at'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->insert($logData);
    }

    /**
     * Actualizar estado del mensaje
     */
    public function updateMessageStatus($messageId, $status, $apiResponse = null, $errorMessage = null)
    {
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($status === 'sent') {
            $updateData['sent_at'] = date('Y-m-d H:i:s');
        }

        if ($apiResponse) {
            $updateData['api_response'] = $apiResponse;
        }

        if ($errorMessage) {
            $updateData['error_message'] = $errorMessage;
        }

        return $this->update($messageId, $updateData);
    }

    /**
     * Obtener estadísticas de mensajes
     */
    public function getMessageStats($dateFrom = null, $dateTo = null)
    {
        $builder = $this->builder();

        if ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('created_at <=', $dateTo);
        }

        $stats = $builder->select('
            COUNT(*) as total_messages,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_messages,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_messages,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_messages
        ')->get()->getRowArray();

        $stats['success_rate'] = $stats['total_messages'] > 0 
            ? round(($stats['sent_messages'] / $stats['total_messages']) * 100, 2) 
            : 0;

        return $stats;
    }

    /**
     * Obtener mensajes por usuario
     */
    public function getMessagesByUser($userId, $limit = 50, $offset = 0)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Obtener mensajes por plantilla
     */
    public function getMessagesByTemplate($templateKey, $limit = 50, $offset = 0)
    {
        return $this->where('template_key', $templateKey)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Obtener mensajes fallidos
     */
    public function getFailedMessages($limit = 50, $offset = 0)
    {
        return $this->where('status', 'failed')
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Obtener mensajes pendientes
     */
    public function getPendingMessages($limit = 50, $offset = 0)
    {
        return $this->where('status', 'pending')
                   ->orderBy('created_at', 'ASC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Limpiar logs antiguos
     */
    public function cleanOldLogs($daysToKeep = 90)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Obtener resumen de mensajes por día
     */
    public function getDailyMessageSummary($days = 30)
    {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->select('
            DATE(created_at) as date,
            COUNT(*) as total_messages,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_messages,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_messages
        ')
        ->where('created_at >=', $dateFrom)
        ->groupBy('DATE(created_at)')
        ->orderBy('date', 'DESC')
        ->findAll();
    }
}
