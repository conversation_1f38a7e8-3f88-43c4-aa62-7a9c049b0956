<?php

namespace App\Libraries;

/**
 * Optimizador de Assets
 * Sistema de compresión y minificación de CSS, JS e imágenes
 */
class AssetOptimizer
{
    private $config;
    private $cacheDir;
    
    public function __construct()
    {
        $this->config = [
            'enabled' => env('ASSET_OPTIMIZATION_ENABLED', true),
            'minify_css' => env('MINIFY_CSS', true),
            'minify_js' => env('MINIFY_JS', true),
            'optimize_images' => env('OPTIMIZE_IMAGES', true),
            'gzip_compression' => env('GZIP_COMPRESSION', true),
            'cache_duration' => env('ASSET_CACHE_DURATION', 86400), // 24 horas
            'quality_jpeg' => env('IMAGE_QUALITY_JPEG', 85),
            'quality_png' => env('IMAGE_QUALITY_PNG', 9),
            'max_image_width' => env('MAX_IMAGE_WIDTH', 1920),
            'max_image_height' => env('MAX_IMAGE_HEIGHT', 1080)
        ];
        
        $this->cacheDir = WRITEPATH . 'cache/assets/';
        $this->ensureCacheDirectory();
    }
    
    /**
     * Optimizar archivo CSS
     */
    public function optimizeCSS(string $cssPath): array
    {
        if (!$this->config['enabled'] || !$this->config['minify_css']) {
            return ['success' => false, 'error' => 'CSS optimization disabled'];
        }
        
        try {
            if (!file_exists($cssPath)) {
                throw new \Exception("CSS file not found: $cssPath");
            }
            
            $originalContent = file_get_contents($cssPath);
            $originalSize = strlen($originalContent);
            
            // Generar hash para cache
            $hash = md5($originalContent);
            $cachedFile = $this->cacheDir . 'css_' . $hash . '.min.css';
            
            // Verificar si ya está en cache
            if (file_exists($cachedFile) && (time() - filemtime($cachedFile)) < $this->config['cache_duration']) {
                $optimizedContent = file_get_contents($cachedFile);
            } else {
                // Minificar CSS
                $optimizedContent = $this->minifyCSS($originalContent);
                
                // Guardar en cache
                file_put_contents($cachedFile, $optimizedContent);
            }
            
            $optimizedSize = strlen($optimizedContent);
            $savings = $originalSize - $optimizedSize;
            $savingsPercent = ($savings / $originalSize) * 100;
            
            return [
                'success' => true,
                'original_size' => $originalSize,
                'optimized_size' => $optimizedSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2),
                'cached_file' => $cachedFile,
                'hash' => $hash
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimizar archivo JavaScript
     */
    public function optimizeJS(string $jsPath): array
    {
        if (!$this->config['enabled'] || !$this->config['minify_js']) {
            return ['success' => false, 'error' => 'JS optimization disabled'];
        }
        
        try {
            if (!file_exists($jsPath)) {
                throw new \Exception("JS file not found: $jsPath");
            }
            
            $originalContent = file_get_contents($jsPath);
            $originalSize = strlen($originalContent);
            
            // Generar hash para cache
            $hash = md5($originalContent);
            $cachedFile = $this->cacheDir . 'js_' . $hash . '.min.js';
            
            // Verificar si ya está en cache
            if (file_exists($cachedFile) && (time() - filemtime($cachedFile)) < $this->config['cache_duration']) {
                $optimizedContent = file_get_contents($cachedFile);
            } else {
                // Minificar JavaScript
                $optimizedContent = $this->minifyJS($originalContent);
                
                // Guardar en cache
                file_put_contents($cachedFile, $optimizedContent);
            }
            
            $optimizedSize = strlen($optimizedContent);
            $savings = $originalSize - $optimizedSize;
            $savingsPercent = ($savings / $originalSize) * 100;
            
            return [
                'success' => true,
                'original_size' => $originalSize,
                'optimized_size' => $optimizedSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2),
                'cached_file' => $cachedFile,
                'hash' => $hash
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimizar imagen
     */
    public function optimizeImage(string $imagePath, ?string $outputPath = null): array
    {
        if (!$this->config['enabled'] || !$this->config['optimize_images']) {
            return ['success' => false, 'error' => 'Image optimization disabled'];
        }
        
        try {
            if (!file_exists($imagePath)) {
                throw new \Exception("Image file not found: $imagePath");
            }
            
            $originalSize = filesize($imagePath);
            $imageInfo = getimagesize($imagePath);
            
            if (!$imageInfo) {
                throw new \Exception("Invalid image file: $imagePath");
            }
            
            $outputPath = $outputPath ?? $this->generateOptimizedImagePath($imagePath);
            
            // Verificar si ya está optimizada
            if (file_exists($outputPath) && filemtime($outputPath) >= filemtime($imagePath)) {
                $optimizedSize = filesize($outputPath);
            } else {
                $optimizedSize = $this->processImageOptimization($imagePath, $outputPath, $imageInfo);
            }
            
            $savings = $originalSize - $optimizedSize;
            $savingsPercent = ($savings / $originalSize) * 100;
            
            return [
                'success' => true,
                'original_size' => $originalSize,
                'optimized_size' => $optimizedSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2),
                'output_path' => $outputPath,
                'original_dimensions' => ['width' => $imageInfo[0], 'height' => $imageInfo[1]],
                'mime_type' => $imageInfo['mime']
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Combinar múltiples archivos CSS
     */
    public function combineCSS(array $cssPaths, string $outputPath): array
    {
        try {
            $combinedContent = '';
            $totalOriginalSize = 0;
            
            foreach ($cssPaths as $cssPath) {
                if (file_exists($cssPath)) {
                    $content = file_get_contents($cssPath);
                    $combinedContent .= "/* File: " . basename($cssPath) . " */\n";
                    $combinedContent .= $content . "\n\n";
                    $totalOriginalSize += strlen($content);
                }
            }
            
            // Minificar el contenido combinado
            if ($this->config['minify_css']) {
                $combinedContent = $this->minifyCSS($combinedContent);
            }
            
            // Guardar archivo combinado
            file_put_contents($outputPath, $combinedContent);
            
            $optimizedSize = strlen($combinedContent);
            $savings = $totalOriginalSize - $optimizedSize;
            $savingsPercent = ($savings / $totalOriginalSize) * 100;
            
            return [
                'success' => true,
                'files_combined' => count($cssPaths),
                'original_size' => $totalOriginalSize,
                'optimized_size' => $optimizedSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2),
                'output_path' => $outputPath
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Combinar múltiples archivos JavaScript
     */
    public function combineJS(array $jsPaths, string $outputPath): array
    {
        try {
            $combinedContent = '';
            $totalOriginalSize = 0;
            
            foreach ($jsPaths as $jsPath) {
                if (file_exists($jsPath)) {
                    $content = file_get_contents($jsPath);
                    $combinedContent .= "/* File: " . basename($jsPath) . " */\n";
                    $combinedContent .= $content . "\n;\n\n"; // Agregar ; para evitar errores
                    $totalOriginalSize += strlen($content);
                }
            }
            
            // Minificar el contenido combinado
            if ($this->config['minify_js']) {
                $combinedContent = $this->minifyJS($combinedContent);
            }
            
            // Guardar archivo combinado
            file_put_contents($outputPath, $combinedContent);
            
            $optimizedSize = strlen($combinedContent);
            $savings = $totalOriginalSize - $optimizedSize;
            $savingsPercent = ($savings / $totalOriginalSize) * 100;
            
            return [
                'success' => true,
                'files_combined' => count($jsPaths),
                'original_size' => $totalOriginalSize,
                'optimized_size' => $optimizedSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2),
                'output_path' => $outputPath
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generar versiones WebP de imágenes
     */
    public function generateWebP(string $imagePath): array
    {
        try {
            if (!function_exists('imagewebp')) {
                throw new \Exception('WebP support not available');
            }
            
            $imageInfo = getimagesize($imagePath);
            if (!$imageInfo) {
                throw new \Exception('Invalid image file');
            }
            
            $webpPath = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $imagePath);
            
            // Crear imagen desde el archivo original
            switch ($imageInfo['mime']) {
                case 'image/jpeg':
                    $image = imagecreatefromjpeg($imagePath);
                    break;
                case 'image/png':
                    $image = imagecreatefrompng($imagePath);
                    break;
                default:
                    throw new \Exception('Unsupported image format for WebP conversion');
            }
            
            // Convertir a WebP
            $result = imagewebp($image, $webpPath, $this->config['quality_jpeg']);
            imagedestroy($image);
            
            if (!$result) {
                throw new \Exception('Failed to create WebP image');
            }
            
            $originalSize = filesize($imagePath);
            $webpSize = filesize($webpPath);
            $savings = $originalSize - $webpSize;
            $savingsPercent = ($savings / $originalSize) * 100;
            
            return [
                'success' => true,
                'original_path' => $imagePath,
                'webp_path' => $webpPath,
                'original_size' => $originalSize,
                'webp_size' => $webpSize,
                'savings' => $savings,
                'savings_percent' => round($savingsPercent, 2)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Limpiar cache de assets
     */
    public function clearCache(): array
    {
        try {
            $files = glob($this->cacheDir . '*');
            $deletedCount = 0;
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $deletedCount++;
                }
            }
            
            return [
                'success' => true,
                'deleted_files' => $deletedCount
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de optimización
     */
    public function getOptimizationStats(): array
    {
        try {
            $stats = [
                'cache_files' => 0,
                'cache_size' => 0,
                'css_files' => 0,
                'js_files' => 0,
                'image_files' => 0
            ];
            
            $files = glob($this->cacheDir . '*');
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $stats['cache_files']++;
                    $stats['cache_size'] += filesize($file);
                    
                    if (strpos($file, 'css_') !== false) {
                        $stats['css_files']++;
                    } elseif (strpos($file, 'js_') !== false) {
                        $stats['js_files']++;
                    } elseif (strpos($file, 'img_') !== false) {
                        $stats['image_files']++;
                    }
                }
            }
            
            return [
                'success' => true,
                'stats' => $stats
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Minificar CSS
     */
    private function minifyCSS(string $css): string
    {
        // Remover comentarios
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remover espacios en blanco innecesarios
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remover espacios alrededor de caracteres especiales
        $css = str_replace([' {', '{ ', ' }', '} ', '; ', ' ;', ': ', ' :', ', ', ' ,'], 
                          ['{', '{', '}', '}', ';', ';', ':', ':', ',', ','], $css);
        
        // Remover último punto y coma antes de }
        $css = str_replace(';}', '}', $css);
        
        return trim($css);
    }
    
    /**
     * Minificar JavaScript
     */
    private function minifyJS(string $js): string
    {
        // Remover comentarios de línea
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // Remover comentarios de bloque
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remover espacios en blanco innecesarios
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remover espacios alrededor de operadores y caracteres especiales
        $js = str_replace([' = ', ' + ', ' - ', ' * ', ' / ', ' { ', ' } ', ' ( ', ' ) ', ' [ ', ' ] ', ' ; ', ' , '], 
                         ['=', '+', '-', '*', '/', '{', '}', '(', ')', '[', ']', ';', ','], $js);
        
        return trim($js);
    }
    
    /**
     * Procesar optimización de imagen
     */
    private function processImageOptimization(string $inputPath, string $outputPath, array $imageInfo): int
    {
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Calcular nuevas dimensiones si es necesario
        $newDimensions = $this->calculateNewDimensions($width, $height);
        
        // Crear imagen desde el archivo original
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($inputPath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($inputPath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($inputPath);
                break;
            default:
                throw new \Exception('Unsupported image format');
        }
        
        // Redimensionar si es necesario
        if ($newDimensions['width'] !== $width || $newDimensions['height'] !== $height) {
            $resizedImage = imagecreatetruecolor($newDimensions['width'], $newDimensions['height']);
            
            // Preservar transparencia para PNG
            if ($mimeType === 'image/png') {
                imagealphablending($resizedImage, false);
                imagesavealpha($resizedImage, true);
            }
            
            imagecopyresampled($resizedImage, $image, 0, 0, 0, 0, 
                             $newDimensions['width'], $newDimensions['height'], $width, $height);
            
            imagedestroy($image);
            $image = $resizedImage;
        }
        
        // Guardar imagen optimizada
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($image, $outputPath, $this->config['quality_jpeg']);
                break;
            case 'image/png':
                imagepng($image, $outputPath, $this->config['quality_png']);
                break;
            case 'image/gif':
                imagegif($image, $outputPath);
                break;
        }
        
        imagedestroy($image);
        
        return filesize($outputPath);
    }
    
    /**
     * Calcular nuevas dimensiones para imagen
     */
    private function calculateNewDimensions(int $width, int $height): array
    {
        $maxWidth = $this->config['max_image_width'];
        $maxHeight = $this->config['max_image_height'];
        
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return ['width' => $width, 'height' => $height];
        }
        
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        
        return [
            'width' => (int)($width * $ratio),
            'height' => (int)($height * $ratio)
        ];
    }
    
    /**
     * Generar ruta para imagen optimizada
     */
    private function generateOptimizedImagePath(string $originalPath): string
    {
        $pathInfo = pathinfo($originalPath);
        $hash = substr(md5_file($originalPath), 0, 8);
        
        return $this->cacheDir . 'img_' . $hash . '_' . $pathInfo['filename'] . '.' . $pathInfo['extension'];
    }
    
    /**
     * Asegurar que existe el directorio de cache
     */
    private function ensureCacheDirectory(): void
    {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'minify_css' => $this->config['minify_css'],
            'minify_js' => $this->config['minify_js'],
            'optimize_images' => $this->config['optimize_images'],
            'cache_dir' => $this->cacheDir,
            'cache_duration' => $this->config['cache_duration'],
            'image_quality_jpeg' => $this->config['quality_jpeg'],
            'image_quality_png' => $this->config['quality_png'],
            'max_image_dimensions' => [
                'width' => $this->config['max_image_width'],
                'height' => $this->config['max_image_height']
            ]
        ];
    }
}
