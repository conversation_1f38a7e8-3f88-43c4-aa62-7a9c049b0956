<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\GoogleAnalytics4Manager;
use App\Libraries\SocialMediaManager;
use App\Libraries\SystemMonitor;
use App\Libraries\AutomationManager;

/**
 * Controlador API para Integraciones y Automatizaciones
 * Gestiona GA4, redes sociales, monitoreo y automatizaciones
 */
class IntegrationsController extends ResourceController
{
    protected $format = 'json';
    protected $ga4Manager;
    protected $socialManager;
    protected $systemMonitor;
    protected $automationManager;
    
    public function __construct()
    {
        $this->ga4Manager = new GoogleAnalytics4Manager();
        $this->socialManager = new SocialMediaManager();
        $this->systemMonitor = new SystemMonitor();
        $this->automationManager = new AutomationManager();
    }
    
    /**
     * Obtener configuración de integraciones
     */
    public function config()
    {
        try {
            $config = [
                'google_analytics' => $this->ga4Manager->getPublicConfig(),
                'social_media' => [
                    'enabled_platforms' => $this->socialManager->getEnabledPlatforms(),
                    'configuration_status' => $this->socialManager->validateConfiguration()
                ],
                'monitoring' => [
                    'enabled' => env('SYSTEM_MONITORING_ENABLED', true),
                    'check_interval' => env('MONITORING_CHECK_INTERVAL', 300)
                ],
                'automation' => [
                    'enabled' => env('AUTOMATION_ENABLED', true),
                    'last_run' => $this->getLastAutomationRun()
                ]
            ];
            
            return $this->respond($config);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting integrations config: ' . $e->getMessage());
        }
    }
    
    /**
     * Trackear evento en Google Analytics 4
     */
    public function trackEvent()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (empty($data['event_name'])) {
                return $this->failValidationError('Event name is required');
            }
            
            $eventName = $data['event_name'];
            $eventParams = $data['params'] ?? [];
            
            $result = $this->ga4Manager->trackCustomEvent($eventName, $eventParams);
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error tracking event: ' . $e->getMessage());
        }
    }
    
    /**
     * Trackear compra en GA4
     */
    public function trackPurchase()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (empty($data['order_data']) || empty($data['items'])) {
                return $this->failValidationError('Order data and items are required');
            }
            
            $result = $this->ga4Manager->trackPurchase($data['order_data'], $data['items']);
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error tracking purchase: ' . $e->getMessage());
        }
    }
    
    /**
     * Publicar producto en redes sociales
     */
    public function publishToSocial()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (empty($data['product_id'])) {
                return $this->failValidationError('Product ID is required');
            }
            
            // Obtener datos del producto
            $db = \Config\Database::connect();
            $product = $db->table('products p')
                         ->select('p.*, c.name as category_name, b.name as brand_name')
                         ->join('categories c', 'p.category_id = c.id', 'left')
                         ->join('brands b', 'p.brand_id = b.id', 'left')
                         ->where('p.id', $data['product_id'])
                         ->get()
                         ->getRowArray();
            
            if (!$product) {
                return $this->failNotFound('Product not found');
            }
            
            $options = $data['options'] ?? [];
            $result = $this->socialManager->publishProduct($product, $options);
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error publishing to social media: ' . $e->getMessage());
        }
    }
    
    /**
     * Programar publicación en redes sociales
     */
    public function schedulePost()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (empty($data['product_id']) || empty($data['platforms']) || empty($data['scheduled_at'])) {
                return $this->failValidationError('Product ID, platforms and scheduled_at are required');
            }
            
            $scheduledAt = new \DateTime($data['scheduled_at']);
            $options = $data['options'] ?? [];
            
            $result = $this->socialManager->schedulePost(
                $data['product_id'],
                $data['platforms'],
                $scheduledAt,
                $options
            );
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error scheduling post: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas de redes sociales
     */
    public function socialStats()
    {
        try {
            $days = (int) ($this->request->getGet('days') ?? 30);
            
            $stats = $this->socialManager->getSocialMediaStats($days);
            
            return $this->respond($stats);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting social media stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Ejecutar verificación del sistema
     */
    public function systemCheck()
    {
        try {
            $result = $this->systemMonitor->runSystemCheck();
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error running system check: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas de monitoreo
     */
    public function monitoringStats()
    {
        try {
            $days = (int) ($this->request->getGet('days') ?? 7);
            
            $stats = $this->systemMonitor->getMonitoringStats($days);
            
            return $this->respond($stats);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting monitoring stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Ejecutar automatizaciones
     */
    public function runAutomations()
    {
        try {
            $task = $this->request->getGet('task');
            
            if ($task) {
                // Ejecutar tarea específica
                switch ($task) {
                    case 'prices':
                        $result = $this->automationManager->runPriceMonitoring();
                        break;
                    case 'stock':
                        $result = $this->automationManager->runStockAlerts();
                        break;
                    case 'wishlist':
                        $result = $this->automationManager->runWishlistReminders();
                        break;
                    case 'cleanup':
                        $result = $this->automationManager->runDataCleanup();
                        break;
                    case 'backup':
                        $result = $this->automationManager->runBackup();
                        break;
                    case 'performance':
                        $result = $this->automationManager->runPerformanceAnalysis();
                        break;
                    case 'analytics':
                        $result = $this->automationManager->runAnalyticsSync();
                        break;
                    case 'cache':
                        $result = $this->automationManager->runCacheMaintenance();
                        break;
                    default:
                        return $this->failValidationError('Invalid task specified');
                }
            } else {
                // Ejecutar todas las tareas
                $result = $this->automationManager->runScheduledTasks();
            }
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error running automations: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas de automatizaciones
     */
    public function automationStats()
    {
        try {
            $days = (int) ($this->request->getGet('days') ?? 7);
            
            $stats = $this->automationManager->getAutomationStats($days);
            
            return $this->respond($stats);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting automation stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener alertas activas del sistema
     */
    public function activeAlerts()
    {
        try {
            $db = \Config\Database::connect();
            
            $alerts = $db->table('system_alerts')
                        ->where('is_resolved', 0)
                        ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                        ->orderBy('severity DESC, created_at DESC')
                        ->limit(50)
                        ->get()
                        ->getResultArray();
            
            return $this->respond([
                'alerts' => $alerts,
                'total_count' => count($alerts),
                'critical_count' => count(array_filter($alerts, function($alert) {
                    return $alert['severity'] === 'critical';
                })),
                'warning_count' => count(array_filter($alerts, function($alert) {
                    return $alert['severity'] === 'warning';
                }))
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting active alerts: ' . $e->getMessage());
        }
    }
    
    /**
     * Resolver alerta
     */
    public function resolveAlert($alertId)
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('User not authenticated');
            }
            
            $db = \Config\Database::connect();
            
            $updated = $db->table('system_alerts')
                         ->where('id', $alertId)
                         ->update([
                             'is_resolved' => 1,
                             'resolved_at' => date('Y-m-d H:i:s'),
                             'resolved_by' => $userId
                         ]);
            
            if ($updated) {
                return $this->respond(['message' => 'Alert resolved successfully']);
            } else {
                return $this->failNotFound('Alert not found');
            }
            
        } catch (\Exception $e) {
            return $this->failServerError('Error resolving alert: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuraciones del sistema
     */
    public function systemSettings()
    {
        try {
            $category = $this->request->getGet('category');
            
            $db = \Config\Database::connect();
            $query = $db->table('system_settings');
            
            if ($category) {
                $query->where('category', $category);
            }
            
            $settings = $query->get()->getResultArray();
            
            // Formatear configuraciones
            $formattedSettings = [];
            foreach ($settings as $setting) {
                $value = $setting['value'];
                
                // Convertir según el tipo
                switch ($setting['type']) {
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'float':
                        $value = (float) $value;
                        break;
                    case 'json':
                    case 'array':
                        $value = json_decode($value, true);
                        break;
                }
                
                $formattedSettings[$setting['key']] = [
                    'value' => $value,
                    'type' => $setting['type'],
                    'description' => $setting['description'],
                    'is_editable' => (bool) $setting['is_editable']
                ];
            }
            
            return $this->respond([
                'category' => $category,
                'settings' => $formattedSettings
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting system settings: ' . $e->getMessage());
        }
    }
    
    /**
     * Actualizar configuración del sistema
     */
    public function updateSetting()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('User not authenticated');
            }
            
            $data = $this->request->getJSON(true);
            
            if (empty($data['category']) || empty($data['key'])) {
                return $this->failValidationError('Category and key are required');
            }
            
            $db = \Config\Database::connect();
            
            // Verificar que la configuración existe y es editable
            $setting = $db->table('system_settings')
                         ->where('category', $data['category'])
                         ->where('key', $data['key'])
                         ->get()
                         ->getRowArray();
            
            if (!$setting) {
                return $this->failNotFound('Setting not found');
            }
            
            if (!$setting['is_editable']) {
                return $this->failForbidden('Setting is not editable');
            }
            
            // Validar y convertir valor según el tipo
            $value = $data['value'];
            switch ($setting['type']) {
                case 'boolean':
                    $value = $value ? 'true' : 'false';
                    break;
                case 'integer':
                    if (!is_numeric($value)) {
                        return $this->failValidationError('Value must be an integer');
                    }
                    $value = (string) (int) $value;
                    break;
                case 'float':
                    if (!is_numeric($value)) {
                        return $this->failValidationError('Value must be a number');
                    }
                    $value = (string) (float) $value;
                    break;
                case 'json':
                case 'array':
                    $value = json_encode($value);
                    break;
                default:
                    $value = (string) $value;
            }
            
            // Actualizar configuración
            $updated = $db->table('system_settings')
                         ->where('category', $data['category'])
                         ->where('key', $data['key'])
                         ->update([
                             'value' => $value,
                             'updated_at' => date('Y-m-d H:i:s')
                         ]);
            
            if ($updated) {
                return $this->respond(['message' => 'Setting updated successfully']);
            } else {
                return $this->failServerError('Failed to update setting');
            }
            
        } catch (\Exception $e) {
            return $this->failServerError('Error updating setting: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener última ejecución de automatizaciones
     */
    private function getLastAutomationRun(): ?string
    {
        try {
            $db = \Config\Database::connect();
            
            $lastRun = $db->table('automation_log')
                         ->select('created_at')
                         ->where('task_name', 'scheduled_tasks')
                         ->orderBy('created_at', 'DESC')
                         ->limit(1)
                         ->get()
                         ->getRowArray();
            
            return $lastRun['created_at'] ?? null;
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Obtener ID del usuario actual
     */
    private function getUserId(): ?int
    {
        $session = session();
        return $session->get('user_id');
    }
}
