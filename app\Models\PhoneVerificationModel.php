<?php

namespace App\Models;

use CodeIgniter\Model;

class PhoneVerificationModel extends Model
{
    protected $table            = 'phone_verifications';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'phone',
        'verification_code',
        'is_verified',
        'attempts',
        'expires_at',
        'verified_at'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    /**
     * Generar código de verificación
     */
    public function generateVerificationCode($userId, $phone)
    {
        try {
            // Generar código de 6 dígitos
            $code = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
            
            // Expiración en 10 minutos
            $expiresAt = date('Y-m-d H:i:s', strtotime('+10 minutes'));

            // Invalidar códigos anteriores para este usuario
            $this->where('user_id', $userId)
                 ->where('is_verified', 0)
                 ->set(['is_verified' => -1]) // -1 = invalidado
                 ->update();

            // Crear nuevo código
            $data = [
                'user_id' => $userId,
                'phone' => $phone,
                'verification_code' => $code,
                'expires_at' => $expiresAt,
                'attempts' => 0,
                'is_verified' => 0
            ];

            $verificationId = $this->insert($data);

            if ($verificationId) {
                return [
                    'success' => true,
                    'code' => $code,
                    'verification_id' => $verificationId,
                    'expires_at' => $expiresAt
                ];
            }

            return ['success' => false, 'error' => 'Error al generar código'];

        } catch (\Exception $e) {
            log_message('error', 'Error generando código de verificación: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error interno del servidor'];
        }
    }

    /**
     * Verificar código
     */
    public function verifyCode($userId, $code)
    {
        try {
            // Buscar código válido
            $verification = $this->where('user_id', $userId)
                                ->where('verification_code', $code)
                                ->where('is_verified', 0)
                                ->where('expires_at >', date('Y-m-d H:i:s'))
                                ->first();

            if (!$verification) {
                return ['success' => false, 'error' => 'Código inválido o expirado'];
            }

            // Incrementar intentos
            $this->update($verification['id'], [
                'attempts' => $verification['attempts'] + 1
            ]);

            // Verificar límite de intentos (máximo 3)
            if ($verification['attempts'] >= 2) { // 2 porque ya incrementamos
                $this->update($verification['id'], ['is_verified' => -1]);
                return ['success' => false, 'error' => 'Demasiados intentos. Solicita un nuevo código'];
            }

            // Marcar como verificado
            $this->update($verification['id'], [
                'is_verified' => 1,
                'verified_at' => date('Y-m-d H:i:s')
            ]);

            // Actualizar usuario como verificado
            $userModel = new \App\Models\UserModel();
            $userModel->update($userId, [
                'phone_verified' => 1,
                'phone' => $verification['phone'] // Actualizar teléfono si cambió
            ]);

            return [
                'success' => true,
                'message' => 'Teléfono verificado exitosamente',
                'phone' => $verification['phone']
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error verificando código: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error interno del servidor'];
        }
    }

    /**
     * Obtener verificación pendiente para un usuario
     */
    public function getPendingVerification($userId)
    {
        return $this->where('user_id', $userId)
                   ->where('is_verified', 0)
                   ->where('expires_at >', date('Y-m-d H:i:s'))
                   ->orderBy('created_at', 'DESC')
                   ->first();
    }

    /**
     * Verificar si un usuario puede solicitar un nuevo código
     */
    public function canRequestNewCode($userId)
    {
        // Verificar si hay un código reciente (menos de 1 minuto)
        $recentCode = $this->where('user_id', $userId)
                          ->where('created_at >', date('Y-m-d H:i:s', strtotime('-1 minute')))
                          ->first();

        return !$recentCode;
    }

    /**
     * Limpiar códigos expirados
     */
    public function cleanExpiredCodes()
    {
        return $this->where('expires_at <', date('Y-m-d H:i:s'))
                   ->where('is_verified', 0)
                   ->set(['is_verified' => -1])
                   ->update();
    }

    /**
     * Obtener estadísticas de verificación para un usuario
     */
    public function getUserVerificationStats($userId)
    {
        $stats = $this->selectSum('attempts', 'total_attempts')
                     ->selectCount('id', 'total_codes')
                     ->where('user_id', $userId)
                     ->first();

        $verified = $this->where('user_id', $userId)
                        ->where('is_verified', 1)
                        ->countAllResults();

        return [
            'total_codes' => $stats['total_codes'] ?? 0,
            'total_attempts' => $stats['total_attempts'] ?? 0,
            'verified_codes' => $verified,
            'success_rate' => $stats['total_codes'] > 0 ? round(($verified / $stats['total_codes']) * 100, 2) : 0
        ];
    }
}
