<?php

/**
 * Script para probar el checkout directamente
 */

echo "=== PRUEBA DIRECTA DEL CHECKOUT ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "==================================\n\n";

// Hacer petición HTTP al checkout
function testCheckoutEndpoint($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding: gzip, deflate',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1'
        ]
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'info' => $info
    ];
}

// Probar diferentes URLs
$urls = [
    'https://mrcell.com.gt/checkout',
    'https://mrcell.com.gt/carrito',
    'https://mrcell.com.gt/',
];

foreach ($urls as $url) {
    echo "🌐 Probando: {$url}\n";
    
    $result = testCheckoutEndpoint($url);
    
    echo "   HTTP Code: {$result['http_code']}\n";
    echo "   Content Type: " . ($result['info']['content_type'] ?? 'N/A') . "\n";
    echo "   Total Time: " . number_format($result['info']['total_time'], 2) . "s\n";
    
    if ($result['error']) {
        echo "   ❌ cURL Error: {$result['error']}\n";
    }
    
    if ($result['http_code'] >= 400) {
        echo "   ❌ Error HTTP {$result['http_code']}\n";
        
        // Mostrar parte del contenido de error
        if ($result['response']) {
            $errorContent = substr($result['response'], 0, 500);
            echo "   📄 Contenido del error:\n";
            echo "   " . str_replace("\n", "\n   ", $errorContent) . "\n";
            
            // Buscar mensajes de error específicos
            if (strpos($result['response'], 'Fatal error') !== false) {
                echo "   🚨 FATAL ERROR detectado\n";
            }
            if (strpos($result['response'], 'Parse error') !== false) {
                echo "   🚨 PARSE ERROR detectado\n";
            }
            if (strpos($result['response'], 'Call to undefined') !== false) {
                echo "   🚨 UNDEFINED FUNCTION/METHOD detectado\n";
            }
            if (strpos($result['response'], 'Class not found') !== false) {
                echo "   🚨 CLASS NOT FOUND detectado\n";
            }
        }
    } else {
        echo "   ✅ Respuesta exitosa\n";
        
        // Verificar si es HTML válido
        if (strpos($result['response'], '<html') !== false) {
            echo "   📄 Contenido HTML detectado\n";
        }
        
        // Verificar si contiene el título esperado
        if (strpos($result['response'], 'Checkout') !== false) {
            echo "   🎯 Página de checkout detectada\n";
        }
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

// Probar también con una sesión simulada
echo "🍪 Probando con sesión simulada...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'https://mrcell.com.gt/checkout',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_COOKIEJAR => '/tmp/cookies.txt',
    CURLOPT_COOKIEFILE => '/tmp/cookies.txt',
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    CURLOPT_HTTPHEADER => [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding: gzip, deflate',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1',
        'Cookie: ci_session=test_session_' . time()
    ]
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "   HTTP Code con sesión: {$httpCode}\n";
if ($error) {
    echo "   ❌ Error: {$error}\n";
} else {
    echo "   ✅ Petición exitosa\n";
}

echo "\n";

// Verificar logs de error de PHP
echo "📋 Verificando logs de error...\n";

$logPaths = [
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    '/var/log/php_errors.log',
    '/tmp/php_errors.log',
    ini_get('error_log')
];

foreach ($logPaths as $logPath) {
    if ($logPath && file_exists($logPath) && is_readable($logPath)) {
        echo "   📄 Revisando: {$logPath}\n";
        
        // Leer las últimas líneas del log
        $lines = [];
        $file = new SplFileObject($logPath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        // Leer las últimas 20 líneas
        $startLine = max(0, $totalLines - 20);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = trim($file->fgets());
            if (!empty($line) && strpos($line, date('Y-m-d')) !== false) {
                $lines[] = $line;
            }
        }
        
        if (!empty($lines)) {
            echo "   🔍 Errores recientes:\n";
            foreach (array_slice($lines, -5) as $line) {
                echo "      " . substr($line, 0, 100) . "...\n";
            }
        } else {
            echo "   ✅ No hay errores recientes\n";
        }
        
        break; // Solo revisar el primer log encontrado
    }
}

echo "\n🎉 Prueba completada.\n";
echo "\n💡 RECOMENDACIONES:\n";
echo "==================\n";
echo "1. Si hay error 500, revisar logs de Apache/Nginx\n";
echo "2. Verificar permisos de archivos y directorios\n";
echo "3. Comprobar que todas las clases y métodos existen\n";
echo "4. Verificar configuración de PHP (memory_limit, etc.)\n";
echo "5. Probar en modo desarrollo con display_errors = On\n";
