<?php

namespace App\Libraries;

/**
 * Gestor de API Avanzado
 * Sistema completo de API REST con autenticación, rate limiting y documentación
 */
class ApiManager
{
    private $db;
    private $cache;
    private $logger;
    private $security;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        $this->security = new SecurityManager();
        
        $this->config = [
            'enabled' => env('API_ENABLED', true),
            'version' => env('API_VERSION', 'v1'),
            'rate_limit' => env('API_RATE_LIMIT', 1000), // requests per hour
            'auth_required' => env('API_AUTH_REQUIRED', true),
            'cors_enabled' => env('API_CORS_ENABLED', true),
            'allowed_origins' => env('API_ALLOWED_ORIGINS', '*'),
            'documentation_enabled' => env('API_DOCS_ENABLED', true),
            'cache_responses' => env('API_CACHE_RESPONSES', true),
            'cache_ttl' => env('API_CACHE_TTL', 300), // 5 minutos
            'max_page_size' => env('API_MAX_PAGE_SIZE', 100),
            'default_page_size' => env('API_DEFAULT_PAGE_SIZE', 20)
        ];
        
        $this->createApiTables();
    }
    
    /**
     * Procesar request de API
     */
    public function processRequest(string $endpoint, string $method, array $data = [], array $headers = []): array
    {
        try {
            if (!$this->config['enabled']) {
                return $this->errorResponse('API is disabled', 503);
            }
            
            // Verificar CORS
            if ($this->config['cors_enabled']) {
                $this->handleCors();
            }
            
            // Autenticación
            $authResult = $this->authenticateRequest($headers);
            if (!$authResult['success']) {
                return $this->errorResponse($authResult['error'], 401);
            }
            
            $apiKey = $authResult['api_key'] ?? null;
            
            // Rate limiting
            $rateLimitResult = $this->checkRateLimit($apiKey, $endpoint);
            if (!$rateLimitResult['allowed']) {
                return $this->errorResponse('Rate limit exceeded', 429, [
                    'retry_after' => $rateLimitResult['retry_after'] ?? 3600
                ]);
            }
            
            // Validar endpoint
            $endpointConfig = $this->getEndpointConfig($endpoint, $method);
            if (!$endpointConfig) {
                return $this->errorResponse('Endpoint not found', 404);
            }
            
            // Validar datos de entrada
            $validationResult = $this->validateRequestData($data, $endpointConfig['validation'] ?? []);
            if (!$validationResult['valid']) {
                return $this->errorResponse('Validation failed', 400, [
                    'errors' => $validationResult['errors']
                ]);
            }
            
            // Procesar request
            $response = $this->executeEndpoint($endpoint, $method, $data, $apiKey);
            
            // Log del request
            $this->logApiRequest($endpoint, $method, $apiKey, $response['status'] ?? 200);
            
            // Cache de respuesta si está habilitado
            if ($this->config['cache_responses'] && $method === 'GET' && ($response['status'] ?? 200) === 200) {
                $this->cacheResponse($endpoint, $data, $response);
            }
            
            return $response;
            
        } catch (\Exception $e) {
            $this->logger->error("API request error: " . $e->getMessage(), [
                'endpoint' => $endpoint,
                'method' => $method,
                'data' => $data
            ]);
            
            return $this->errorResponse('Internal server error', 500);
        }
    }
    
    /**
     * Generar API key
     */
    public function generateApiKey(int $userId, string $name, array $permissions = []): array
    {
        try {
            $apiKey = 'mrcell_' . bin2hex(random_bytes(32));
            
            $data = [
                'api_key' => hash('sha256', $apiKey),
                'user_id' => $userId,
                'name' => $name,
                'permissions' => json_encode($permissions),
                'rate_limit' => $this->config['rate_limit'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 year'))
            ];
            
            $keyId = $this->db->table('api_keys')->insert($data);
            
            if ($keyId) {
                $this->logger->info("API key generated", [
                    'user_id' => $userId,
                    'key_id' => $keyId,
                    'name' => $name
                ]);
                
                return [
                    'success' => true,
                    'api_key' => $apiKey,
                    'key_id' => $keyId,
                    'expires_at' => $data['expires_at']
                ];
            }
            
            return [
                'success' => false,
                'error' => 'Failed to generate API key'
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("API key generation error: " . $e->getMessage(), [
                'user_id' => $userId,
                'name' => $name
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Revocar API key
     */
    public function revokeApiKey(string $apiKey): bool
    {
        try {
            $hashedKey = hash('sha256', $apiKey);
            
            $result = $this->db->table('api_keys')
                              ->where('api_key', $hashedKey)
                              ->update([
                                  'is_active' => 0,
                                  'revoked_at' => date('Y-m-d H:i:s')
                              ]);
            
            if ($result) {
                $this->cache->delete("api_key:$hashedKey");
                $this->logger->info("API key revoked", ['api_key_hash' => $hashedKey]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("API key revocation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtener estadísticas de API
     */
    public function getApiStats(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Total de requests
            $totalRequests = $this->db->table('api_requests')
                                     ->where('created_at >=', $dateFrom)
                                     ->countAllResults();
            
            // Requests por endpoint
            $endpointStats = $this->db->query("
                SELECT endpoint, COUNT(*) as requests, 
                       AVG(response_time) as avg_response_time
                FROM api_requests 
                WHERE created_at >= ? 
                GROUP BY endpoint 
                ORDER BY requests DESC 
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Requests por API key
            $keyStats = $this->db->query("
                SELECT ak.name, COUNT(ar.id) as requests
                FROM api_keys ak
                LEFT JOIN api_requests ar ON ak.api_key = ar.api_key_hash
                WHERE ar.created_at >= ?
                GROUP BY ak.id, ak.name
                ORDER BY requests DESC
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Códigos de respuesta
            $statusStats = $this->db->query("
                SELECT status_code, COUNT(*) as count
                FROM api_requests 
                WHERE created_at >= ? 
                GROUP BY status_code 
                ORDER BY count DESC
            ", [$dateFrom])->getResultArray();
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => [
                    'total_requests' => $totalRequests,
                    'by_endpoint' => $endpointStats,
                    'by_api_key' => $keyStats,
                    'by_status' => $statusStats,
                    'average_response_time' => $this->getAverageResponseTime($dateFrom)
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generar documentación de API
     */
    public function generateDocumentation(): array
    {
        if (!$this->config['documentation_enabled']) {
            return ['success' => false, 'error' => 'Documentation disabled'];
        }
        
        $endpoints = $this->getAvailableEndpoints();
        
        $documentation = [
            'info' => [
                'title' => 'MrCell Guatemala API',
                'version' => $this->config['version'],
                'description' => 'API completa para el sistema de ecommerce MrCell Guatemala',
                'base_url' => base_url('api/' . $this->config['version']),
                'authentication' => 'API Key required in X-API-Key header'
            ],
            'endpoints' => $endpoints,
            'authentication' => [
                'type' => 'API Key',
                'header' => 'X-API-Key',
                'description' => 'Include your API key in the X-API-Key header'
            ],
            'rate_limiting' => [
                'limit' => $this->config['rate_limit'],
                'window' => '1 hour',
                'headers' => [
                    'X-RateLimit-Limit' => 'Request limit per hour',
                    'X-RateLimit-Remaining' => 'Remaining requests',
                    'X-RateLimit-Reset' => 'Time when limit resets'
                ]
            ],
            'error_codes' => [
                '400' => 'Bad Request - Invalid parameters',
                '401' => 'Unauthorized - Invalid or missing API key',
                '403' => 'Forbidden - Insufficient permissions',
                '404' => 'Not Found - Endpoint not found',
                '429' => 'Too Many Requests - Rate limit exceeded',
                '500' => 'Internal Server Error'
            ]
        ];
        
        return [
            'success' => true,
            'documentation' => $documentation
        ];
    }
    
    /**
     * Autenticar request
     */
    private function authenticateRequest(array $headers): array
    {
        if (!$this->config['auth_required']) {
            return ['success' => true];
        }
        
        $apiKey = $headers['X-API-Key'] ?? $headers['x-api-key'] ?? null;
        
        if (empty($apiKey)) {
            return [
                'success' => false,
                'error' => 'API key required'
            ];
        }
        
        $hashedKey = hash('sha256', $apiKey);
        
        // Verificar en cache primero
        $cacheKey = "api_key:$hashedKey";
        $keyData = $this->cache->get($cacheKey);
        
        if ($keyData === null) {
            // Buscar en base de datos
            $keyData = $this->db->table('api_keys')
                               ->where('api_key', $hashedKey)
                               ->where('is_active', 1)
                               ->where('expires_at >', date('Y-m-d H:i:s'))
                               ->get()
                               ->getRowArray();
            
            if ($keyData) {
                $this->cache->set($cacheKey, $keyData, 300); // Cache por 5 minutos
            }
        }
        
        if (!$keyData) {
            return [
                'success' => false,
                'error' => 'Invalid or expired API key'
            ];
        }
        
        return [
            'success' => true,
            'api_key' => $keyData
        ];
    }
    
    /**
     * Verificar rate limit
     */
    private function checkRateLimit(?array $apiKey, string $endpoint): array
    {
        if (!$apiKey) {
            return ['allowed' => true];
        }
        
        $limit = $apiKey['rate_limit'] ?? $this->config['rate_limit'];
        $cacheKey = "rate_limit:api:{$apiKey['id']}";
        
        $requests = $this->cache->get($cacheKey, 0);
        
        if ($requests >= $limit) {
            return [
                'allowed' => false,
                'retry_after' => 3600 // 1 hora
            ];
        }
        
        $this->cache->set($cacheKey, $requests + 1, 3600);
        
        return [
            'allowed' => true,
            'remaining' => $limit - $requests - 1
        ];
    }
    
    /**
     * Obtener configuración de endpoint
     */
    private function getEndpointConfig(string $endpoint, string $method): ?array
    {
        $endpoints = [
            'products' => [
                'GET' => [
                    'description' => 'Get products list',
                    'validation' => [
                        'page' => 'optional|integer|min:1',
                        'limit' => 'optional|integer|min:1|max:100',
                        'category' => 'optional|string',
                        'search' => 'optional|string'
                    ]
                ],
                'POST' => [
                    'description' => 'Create new product',
                    'validation' => [
                        'name' => 'required|string|max:255',
                        'price' => 'required|numeric|min:0',
                        'category_id' => 'required|integer',
                        'description' => 'optional|string'
                    ]
                ]
            ],
            'orders' => [
                'GET' => [
                    'description' => 'Get orders list',
                    'validation' => [
                        'page' => 'optional|integer|min:1',
                        'limit' => 'optional|integer|min:1|max:100',
                        'status' => 'optional|string',
                        'user_id' => 'optional|integer'
                    ]
                ],
                'POST' => [
                    'description' => 'Create new order',
                    'validation' => [
                        'user_id' => 'required|integer',
                        'items' => 'required|array',
                        'total' => 'required|numeric|min:0'
                    ]
                ]
            ],
            'users' => [
                'GET' => [
                    'description' => 'Get users list',
                    'validation' => [
                        'page' => 'optional|integer|min:1',
                        'limit' => 'optional|integer|min:1|max:100'
                    ]
                ]
            ]
        ];
        
        return $endpoints[$endpoint][$method] ?? null;
    }
    
    /**
     * Validar datos de request
     */
    private function validateRequestData(array $data, array $rules): array
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $rulesParts = explode('|', $rule);
            $isRequired = in_array('required', $rulesParts);
            $value = $data[$field] ?? null;
            
            if ($isRequired && empty($value)) {
                $errors[$field] = "Field $field is required";
                continue;
            }
            
            if (!empty($value)) {
                foreach ($rulesParts as $rulePart) {
                    if (strpos($rulePart, ':') !== false) {
                        [$ruleType, $ruleValue] = explode(':', $rulePart, 2);
                    } else {
                        $ruleType = $rulePart;
                        $ruleValue = null;
                    }
                    
                    if (!$this->validateRule($value, $ruleType, $ruleValue)) {
                        $errors[$field] = "Field $field failed validation: $rulePart";
                        break;
                    }
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validar regla específica
     */
    private function validateRule($value, string $rule, ?string $ruleValue): bool
    {
        switch ($rule) {
            case 'string':
                return is_string($value);
            case 'integer':
                return is_numeric($value) && (int)$value == $value;
            case 'numeric':
                return is_numeric($value);
            case 'array':
                return is_array($value);
            case 'min':
                return is_numeric($value) ? $value >= (float)$ruleValue : strlen($value) >= (int)$ruleValue;
            case 'max':
                return is_numeric($value) ? $value <= (float)$ruleValue : strlen($value) <= (int)$ruleValue;
            default:
                return true;
        }
    }
    
    /**
     * Ejecutar endpoint
     */
    private function executeEndpoint(string $endpoint, string $method, array $data, ?array $apiKey): array
    {
        switch ($endpoint) {
            case 'products':
                return $this->handleProductsEndpoint($method, $data);
            case 'orders':
                return $this->handleOrdersEndpoint($method, $data);
            case 'users':
                return $this->handleUsersEndpoint($method, $data);
            default:
                return $this->errorResponse('Endpoint not implemented', 501);
        }
    }
    
    /**
     * Manejar endpoint de productos
     */
    private function handleProductsEndpoint(string $method, array $data): array
    {
        switch ($method) {
            case 'GET':
                return $this->successResponse([
                    'products' => [
                        ['id' => 1, 'name' => 'iPhone 15 Pro', 'price' => 999.99],
                        ['id' => 2, 'name' => 'Samsung Galaxy S24', 'price' => 899.99]
                    ],
                    'pagination' => [
                        'page' => 1,
                        'limit' => 20,
                        'total' => 2
                    ]
                ]);
            case 'POST':
                return $this->successResponse([
                    'message' => 'Product created successfully',
                    'product_id' => 123
                ], 201);
            default:
                return $this->errorResponse('Method not allowed', 405);
        }
    }
    
    /**
     * Manejar endpoint de órdenes
     */
    private function handleOrdersEndpoint(string $method, array $data): array
    {
        switch ($method) {
            case 'GET':
                return $this->successResponse([
                    'orders' => [
                        ['id' => 1, 'user_id' => 1, 'total' => 999.99, 'status' => 'completed'],
                        ['id' => 2, 'user_id' => 2, 'total' => 1299.99, 'status' => 'pending']
                    ]
                ]);
            case 'POST':
                return $this->successResponse([
                    'message' => 'Order created successfully',
                    'order_id' => 456
                ], 201);
            default:
                return $this->errorResponse('Method not allowed', 405);
        }
    }
    
    /**
     * Manejar endpoint de usuarios
     */
    private function handleUsersEndpoint(string $method, array $data): array
    {
        if ($method === 'GET') {
            return $this->successResponse([
                'users' => [
                    ['id' => 1, 'name' => 'Juan Pérez', 'email' => '<EMAIL>'],
                    ['id' => 2, 'name' => 'María García', 'email' => '<EMAIL>']
                ]
            ]);
        }
        
        return $this->errorResponse('Method not allowed', 405);
    }
    
    /**
     * Respuesta de éxito
     */
    private function successResponse(array $data, int $status = 200): array
    {
        return [
            'success' => true,
            'status' => $status,
            'data' => $data,
            'timestamp' => date('c')
        ];
    }
    
    /**
     * Respuesta de error
     */
    private function errorResponse(string $message, int $status = 400, array $extra = []): array
    {
        return array_merge([
            'success' => false,
            'status' => $status,
            'error' => $message,
            'timestamp' => date('c')
        ], $extra);
    }
    
    /**
     * Manejar CORS
     */
    private function handleCors(): void
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $allowedOrigins = explode(',', $this->config['allowed_origins']);
        
        if ($this->config['allowed_origins'] === '*' || in_array($origin, $allowedOrigins)) {
            header("Access-Control-Allow-Origin: $origin");
        }
        
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, X-API-Key, Authorization');
        header('Access-Control-Max-Age: 86400');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * Log de request de API
     */
    private function logApiRequest(string $endpoint, string $method, ?array $apiKey, int $status): void
    {
        try {
            $this->db->table('api_requests')->insert([
                'endpoint' => $endpoint,
                'method' => $method,
                'api_key_hash' => $apiKey['api_key'] ?? null,
                'status_code' => $status,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'response_time' => (microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Ignorar errores de logging
        }
    }
    
    /**
     * Cache de respuesta
     */
    private function cacheResponse(string $endpoint, array $data, array $response): void
    {
        $cacheKey = "api_response:" . md5($endpoint . serialize($data));
        $this->cache->set($cacheKey, $response, $this->config['cache_ttl']);
    }
    
    /**
     * Obtener endpoints disponibles
     */
    private function getAvailableEndpoints(): array
    {
        return [
            'products' => [
                'path' => '/products',
                'methods' => ['GET', 'POST'],
                'description' => 'Manage products'
            ],
            'orders' => [
                'path' => '/orders',
                'methods' => ['GET', 'POST'],
                'description' => 'Manage orders'
            ],
            'users' => [
                'path' => '/users',
                'methods' => ['GET'],
                'description' => 'Manage users'
            ]
        ];
    }
    
    /**
     * Obtener tiempo promedio de respuesta
     */
    private function getAverageResponseTime(string $dateFrom): float
    {
        $result = $this->db->query("
            SELECT AVG(response_time) as avg_time 
            FROM api_requests 
            WHERE created_at >= ?
        ", [$dateFrom])->getRowArray();
        
        return round($result['avg_time'] ?? 0, 2);
    }
    
    /**
     * Crear tablas de API
     */
    private function createApiTables(): void
    {
        try {
            // Tabla de API keys
            $this->db->query("
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    api_key VARCHAR(255) NOT NULL UNIQUE,
                    user_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    permissions JSON,
                    rate_limit INT DEFAULT 1000,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    revoked_at TIMESTAMP NULL,
                    INDEX idx_api_key (api_key),
                    INDEX idx_user_id (user_id)
                )
            ");
            
            // Tabla de requests de API
            $this->db->query("
                CREATE TABLE IF NOT EXISTS api_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    endpoint VARCHAR(255) NOT NULL,
                    method VARCHAR(10) NOT NULL,
                    api_key_hash VARCHAR(255),
                    status_code INT NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    response_time DECIMAL(10,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_endpoint (endpoint),
                    INDEX idx_api_key_hash (api_key_hash),
                    INDEX idx_created_at (created_at)
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("API tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'version' => $this->config['version'],
            'rate_limit' => $this->config['rate_limit'],
            'auth_required' => $this->config['auth_required'],
            'cors_enabled' => $this->config['cors_enabled'],
            'documentation_enabled' => $this->config['documentation_enabled'],
            'cache_responses' => $this->config['cache_responses']
        ];
    }
}
