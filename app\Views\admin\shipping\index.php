<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-truck me-2"></i>Gestión de Envíos</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShippingModal">
            <i class="fas fa-plus me-2"></i>Nuevo Método de Envío
        </button>
    </div>
</div>

<div class="row">
    <!-- Métodos de Envío -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>Métodos de Envío</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Método</th>
                                <th>Costo</th>
                                <th>Tiempo Estimado</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <strong>Envío Estándar</strong><br>
                                    <small class="text-muted">Entrega en zona metropolitana</small>
                                </td>
                                <td>Q25.00</td>
                                <td>2-3 días hábiles</td>
                                <td><span class="badge bg-success">Activo</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" title="Desactivar">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Envío Express</strong><br>
                                    <small class="text-muted">Entrega rápida en zona metropolitana</small>
                                </td>
                                <td>Q50.00</td>
                                <td>1 día hábil</td>
                                <td><span class="badge bg-success">Activo</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" title="Desactivar">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Envío Nacional</strong><br>
                                    <small class="text-muted">Entrega a nivel nacional</small>
                                </td>
                                <td>Q75.00</td>
                                <td>3-5 días hábiles</td>
                                <td><span class="badge bg-success">Activo</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" title="Desactivar">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Configuración de Envíos -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuración</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Envío Gratis a partir de:</label>
                    <div class="input-group">
                        <span class="input-group-text">Q</span>
                        <input type="number" class="form-control" value="500" step="0.01">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Zona de Cobertura:</label>
                    <select class="form-select">
                        <option>Ciudad de Guatemala</option>
                        <option>Zona Metropolitana</option>
                        <option>Todo el País</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableTracking" checked>
                        <label class="form-check-label" for="enableTracking">
                            Habilitar seguimiento de envíos
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="requireSignature">
                        <label class="form-check-label" for="requireSignature">
                            Requerir firma en entrega
                        </label>
                    </div>
                </div>
                
                <button class="btn btn-primary w-100">
                    <i class="fas fa-save me-2"></i>Guardar Configuración
                </button>
            </div>
        </div>
        
        <!-- Estadísticas de Envíos -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Estadísticas</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Envíos Pendientes:</span>
                    <strong class="text-warning">5</strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>En Tránsito:</span>
                    <strong class="text-info">12</strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Entregados Hoy:</span>
                    <strong class="text-success">8</strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Total del Mes:</span>
                    <strong class="text-primary">156</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para agregar método de envío -->
<div class="modal fade" id="addShippingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Nuevo Método de Envío</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="shippingName" class="form-label">Nombre del Método</label>
                        <input type="text" class="form-control" id="shippingName" required>
                    </div>
                    <div class="mb-3">
                        <label for="shippingDescription" class="form-label">Descripción</label>
                        <textarea class="form-control" id="shippingDescription" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shippingCost" class="form-label">Costo (Q)</label>
                                <input type="number" class="form-control" id="shippingCost" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shippingTime" class="form-label">Tiempo Estimado</label>
                                <input type="text" class="form-control" id="shippingTime" placeholder="ej: 2-3 días">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="shippingActive" checked>
                            <label class="form-check-label" for="shippingActive">
                                Método activo
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary">Guardar Método</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para gestión de envíos
    console.log('Gestión de envíos cargada');
</script>
<?= $this->endSection() ?>
