<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddExpirationDateToProducts extends Migration
{
    public function up()
    {
        $fields = [
            'has_expiration' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => 'Indica si el producto tiene fecha de caducidad'
            ],
            'expiration_date' => [
                'type' => 'DATE',
                'null' => true,
                'comment' => 'Fecha de caducidad del producto'
            ],
            'expiration_alert_days' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 30,
                'comment' => 'Días antes de la caducidad para mostrar alerta'
            ]
        ];

        $this->forge->addColumn('products', $fields);
        
        // Agregar índice para consultas de productos próximos a caducar
        $this->forge->addKey(['has_expiration', 'expiration_date'], false, false, 'idx_product_expiration');
        $this->db->query('ALTER TABLE products ADD INDEX idx_product_expiration (has_expiration, expiration_date)');
    }

    public function down()
    {
        $this->forge->dropColumn('products', ['has_expiration', 'expiration_date', 'expiration_alert_days']);
        
        try {
            $this->db->query('ALTER TABLE products DROP INDEX idx_product_expiration');
        } catch (\Exception $e) {
            // Índice puede no existir
        }
    }
}
