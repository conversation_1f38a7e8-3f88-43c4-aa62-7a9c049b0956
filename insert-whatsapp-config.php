<?php

// <PERSON>ript simple para insertar la configuración de WhatsApp
echo "⚙️ CONFIGURANDO NÚMERO DE GRUPO WHATSAPP...\n\n";

try {
    // Configuración de la base de datos (ajustar según tu configuración)
    $host = 'localhost';
    $dbname = 'mrcell_db'; // Ajustar nombre de la base de datos
    $username = 'root';    // Ajustar usuario
    $password = '';        // Ajustar contraseña
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Conectado a la base de datos\n";
    
    // Insertar o actualizar en system_settings
    $stmt = $pdo->prepare("
        INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
        VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
            setting_value = '120363416393766854',
            updated_at = NOW()
    ");
    
    $stmt->execute();
    echo "✅ Configuración insertada/actualizada en system_settings\n";
    
    // Verificar si existe tabla settings
    $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
    if ($stmt->rowCount() > 0) {
        // Insertar o actualizar en settings
        $stmt = $pdo->prepare("
            INSERT INTO settings (`key`, `value`, description, created_at, updated_at)
            VALUES ('whatsapp_alerts_group', '120363416393766854', 'Número del grupo de WhatsApp para alertas automáticas', NOW(), NOW())
            ON DUPLICATE KEY UPDATE 
                `value` = '120363416393766854',
                updated_at = NOW()
        ");
        
        $stmt->execute();
        echo "✅ Configuración insertada/actualizada en settings\n";
    } else {
        echo "⚠️ Tabla settings no existe, solo se configuró system_settings\n";
    }
    
    // Verificar que se insertó correctamente
    echo "\n🔍 Verificando configuración...\n";
    
    $stmt = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'whatsapp_alerts_group'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✅ Configuración verificada: {$result['setting_value']}\n";
    } else {
        echo "❌ No se pudo verificar la configuración\n";
    }
    
    echo "\n🎉 CONFIGURACIÓN COMPLETADA\n";
    echo "📱 Número de grupo configurado: 120363416393766854\n";
    echo "⚙️ Ahora puedes editarlo desde: /admin/settings?tab=notifications\n";
    echo "🔄 El sistema de cron enviará alertas a este grupo\n";
    
} catch (PDOException $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "\n";
    echo "\n💡 Asegúrate de ajustar la configuración de la base de datos en este script:\n";
    echo "   - Host: $host\n";
    echo "   - Base de datos: $dbname\n";
    echo "   - Usuario: $username\n";
    echo "   - Contraseña: [configurada]\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";
