<?php

namespace App\Libraries;

use Config\Database;
use Config\Services;

class QueryOptimizer
{
    protected $db;
    protected $cache;
    protected $queryLog = [];
    
    public function __construct()
    {
        $this->db = Database::connect();
        $this->cache = Services::cache();
    }

    /**
     * Obtener cupones con cache optimizado
     */
    public function getAvailableCoupons(array $filters = []): array
    {
        $cacheKey = 'available_coupons_' . md5(serialize($filters));
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        
        $builder = $this->db->table('coupons c');
        $builder->select('c.*, COUNT(cu.id) as usage_count');
        $builder->join('coupon_usage cu', 'cu.coupon_id = c.id', 'left');
        $builder->where('c.is_active', 1);
        $builder->where('c.valid_from <=', date('Y-m-d H:i:s'));
        $builder->where('(c.valid_until IS NULL OR c.valid_until >=', date('Y-m-d H:i:s') . ')');
        
        // Aplicar filtros
        if (!empty($filters['category_id'])) {
            $builder->where('(c.applicable_categories IS NULL OR JSON_CONTAINS(c.applicable_categories, ?)', '"' . $filters['category_id'] . '"');
        }
        
        if (!empty($filters['min_amount'])) {
            $builder->where('(c.min_order_amount IS NULL OR c.min_order_amount <=', $filters['min_amount']);
        }
        
        if (!empty($filters['user_id'])) {
            // Subconsulta optimizada para verificar límite por usuario
            $subQuery = $this->db->table('coupon_usage')
                                ->select('coupon_id, COUNT(*) as user_usage')
                                ->where('user_id', $filters['user_id'])
                                ->groupBy('coupon_id')
                                ->getCompiledSelect();
            
            $builder->join("({$subQuery}) uu", 'uu.coupon_id = c.id', 'left');
            $builder->where('(c.usage_limit_per_user IS NULL OR COALESCE(uu.user_usage, 0) < c.usage_limit_per_user)');
        }
        
        $builder->groupBy('c.id');
        $builder->having('(c.usage_limit IS NULL OR usage_count < c.usage_limit)');
        $builder->orderBy('c.priority', 'DESC');
        $builder->orderBy('c.created_at', 'DESC');
        
        $result = $builder->get()->getResultArray();
        
        $this->logQuery('getAvailableCoupons', microtime(true) - $startTime, count($result));
        
        // Cache por 5 minutos
        $this->cache->save($cacheKey, $result, 300);
        
        return $result;
    }

    /**
     * Obtener estadísticas de cupones optimizadas
     */
    public function getCouponStats(int $couponId = null, string $period = '30 days'): array
    {
        $cacheKey = "coupon_stats_{$couponId}_{$period}";
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        // Consulta optimizada con agregaciones
        $query = "
            SELECT 
                c.id,
                c.code,
                c.name,
                c.type,
                c.value,
                COUNT(cu.id) as total_usage,
                COUNT(DISTINCT cu.user_id) as unique_users,
                SUM(cu.discount_amount) as total_savings,
                AVG(cu.discount_amount) as avg_discount,
                MIN(cu.used_at) as first_used,
                MAX(cu.used_at) as last_used,
                COUNT(CASE WHEN cu.used_at >= ? THEN 1 END) as recent_usage
            FROM coupons c
            LEFT JOIN coupon_usage cu ON cu.coupon_id = c.id
        ";
        
        $params = [$startDate];
        
        if ($couponId) {
            $query .= " WHERE c.id = ?";
            $params[] = $couponId;
        }
        
        $query .= " GROUP BY c.id ORDER BY total_usage DESC";
        
        $result = $this->db->query($query, $params)->getResultArray();
        
        // Agregar datos adicionales
        foreach ($result as &$coupon) {
            // Calcular tendencia de uso
            $coupon['usage_trend'] = $this->calculateUsageTrend($coupon['id']);
            
            // Calcular efectividad
            $coupon['effectiveness'] = $coupon['total_usage'] > 0 ? 
                round(($coupon['unique_users'] / $coupon['total_usage']) * 100, 2) : 0;
        }
        
        $this->logQuery('getCouponStats', microtime(true) - $startTime, count($result));
        
        // Cache por 15 minutos
        $this->cache->save($cacheKey, $result, 900);
        
        return $result;
    }

    /**
     * Obtener información de seguimiento optimizada
     */
    public function getTrackingInfo(string $trackingNumber, string $company = null): ?array
    {
        $cacheKey = "tracking_{$trackingNumber}_{$company}";
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        
        // Consulta optimizada con joins
        $builder = $this->db->table('shipments s');
        $builder->select('
            s.*,
            sc.name as company_name,
            sc.tracking_url,
            sc.phone as company_phone,
            COUNT(te.id) as event_count,
            MAX(te.created_at) as last_update
        ');
        $builder->join('shipping_companies sc', 'sc.id = s.company_id');
        $builder->join('tracking_events te', 'te.shipment_id = s.id', 'left');
        $builder->where('s.tracking_number', $trackingNumber);
        
        if ($company) {
            $builder->where('sc.code', $company);
        }
        
        $builder->groupBy('s.id');
        
        $shipment = $builder->get()->getRowArray();
        
        if (!$shipment) {
            return null;
        }
        
        // Obtener eventos de seguimiento
        $events = $this->db->table('tracking_events')
                          ->where('shipment_id', $shipment['id'])
                          ->orderBy('created_at', 'DESC')
                          ->get()
                          ->getResultArray();
        
        $result = [
            'shipment' => $shipment,
            'events' => $events
        ];
        
        $this->logQuery('getTrackingInfo', microtime(true) - $startTime, 1);
        
        // Cache por 30 minutos (tracking no cambia tan frecuentemente)
        $this->cache->save($cacheKey, $result, 1800);
        
        return $result;
    }

    /**
     * Calcular costos de envío optimizado
     */
    public function calculateShippingCosts(string $department, float $weight, array $options = []): array
    {
        $cacheKey = "shipping_costs_{$department}_{$weight}_" . md5(serialize($options));
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        
        // Consulta optimizada con cálculos en SQL
        $query = "
            SELECT 
                sc.id,
                sc.name,
                sc.code,
                sr.service_type,
                sr.base_cost + (sr.cost_per_kg * ?) as total_cost,
                sr.estimated_days_min,
                sr.estimated_days_max,
                sr.max_weight,
                CASE 
                    WHEN ? >= sc.free_shipping_threshold THEN 1 
                    ELSE 0 
                END as free_shipping_eligible
            FROM shipping_companies sc
            INNER JOIN shipping_rates sr ON sr.company_id = sc.id
            WHERE sc.is_active = 1 
            AND sr.is_active = 1
            AND sr.max_weight >= ?
            AND (sr.coverage_areas IS NULL OR JSON_CONTAINS(sr.coverage_areas, ?))
            ORDER BY total_cost ASC, estimated_days_min ASC
        ";
        
        $orderAmount = $options['order_amount'] ?? 0;
        $params = [$weight, $orderAmount, $weight, '"' . $department . '"'];
        
        $rates = $this->db->query($query, $params)->getResultArray();
        
        // Procesar resultados
        $result = [
            'rates' => $rates,
            'cheapest' => !empty($rates) ? $rates[0] : null,
            'fastest' => null
        ];
        
        // Encontrar el más rápido
        if (!empty($rates)) {
            $fastest = $rates[0];
            foreach ($rates as $rate) {
                if ($rate['estimated_days_min'] < $fastest['estimated_days_min']) {
                    $fastest = $rate;
                }
            }
            $result['fastest'] = $fastest;
        }
        
        $this->logQuery('calculateShippingCosts', microtime(true) - $startTime, count($rates));
        
        // Cache por 1 hora
        $this->cache->save($cacheKey, $result, 3600);
        
        return $result;
    }

    /**
     * Obtener KPIs optimizados
     */
    public function getKPIMetrics(string $period = '30 days', array $metrics = []): array
    {
        $cacheKey = "kpi_metrics_{$period}_" . md5(serialize($metrics));
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        $result = [];
        
        // Métricas de ventas
        if (empty($metrics) || in_array('sales', $metrics)) {
            $result['sales'] = $this->getSalesMetrics($startDate);
        }
        
        // Métricas de cupones
        if (empty($metrics) || in_array('coupons', $metrics)) {
            $result['coupons'] = $this->getCouponMetrics($startDate);
        }
        
        // Métricas de envíos
        if (empty($metrics) || in_array('shipping', $metrics)) {
            $result['shipping'] = $this->getShippingMetrics($startDate);
        }
        
        // Métricas de inventario
        if (empty($metrics) || in_array('inventory', $metrics)) {
            $result['inventory'] = $this->getInventoryMetrics();
        }
        
        $this->logQuery('getKPIMetrics', microtime(true) - $startTime, count($result));
        
        // Cache por 10 minutos
        $this->cache->save($cacheKey, $result, 600);
        
        return $result;
    }

    /**
     * Optimizar consultas de inventario
     */
    public function getInventoryStatus(array $filters = []): array
    {
        $cacheKey = 'inventory_status_' . md5(serialize($filters));
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }

        $startTime = microtime(true);
        
        // Consulta optimizada con agregaciones
        $query = "
            SELECT 
                p.id,
                p.name,
                p.sku,
                p.price,
                SUM(i.quantity) as total_stock,
                COUNT(DISTINCT i.warehouse_id) as warehouse_count,
                MIN(i.quantity) as min_stock_location,
                MAX(i.quantity) as max_stock_location,
                AVG(i.quantity) as avg_stock_per_location,
                p.low_stock_threshold,
                CASE 
                    WHEN SUM(i.quantity) <= p.low_stock_threshold THEN 'low'
                    WHEN SUM(i.quantity) = 0 THEN 'out'
                    ELSE 'normal'
                END as stock_status
            FROM products p
            LEFT JOIN inventory i ON i.product_id = p.id
            WHERE p.is_active = 1
        ";
        
        $params = [];
        
        if (!empty($filters['category_id'])) {
            $query .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['warehouse_id'])) {
            $query .= " AND i.warehouse_id = ?";
            $params[] = $filters['warehouse_id'];
        }
        
        if (!empty($filters['stock_status'])) {
            $query .= " HAVING stock_status = ?";
            $params[] = $filters['stock_status'];
        }
        
        $query .= " GROUP BY p.id ORDER BY total_stock ASC";
        
        $result = $this->db->query($query, $params)->getResultArray();
        
        $this->logQuery('getInventoryStatus', microtime(true) - $startTime, count($result));
        
        // Cache por 5 minutos
        $this->cache->save($cacheKey, $result, 300);
        
        return $result;
    }

    /**
     * Métodos auxiliares para KPIs
     */
    private function getSalesMetrics(string $startDate): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                AVG(total_amount) as avg_order_value,
                COUNT(DISTINCT customer_id) as unique_customers,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders
            FROM orders 
            WHERE created_at >= ?
        ";
        
        return $this->db->query($query, [$startDate])->getRowArray();
    }

    private function getCouponMetrics(string $startDate): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_usage,
                COUNT(DISTINCT coupon_id) as active_coupons,
                COUNT(DISTINCT user_id) as users_with_coupons,
                SUM(discount_amount) as total_savings,
                AVG(discount_amount) as avg_discount
            FROM coupon_usage 
            WHERE used_at >= ?
        ";
        
        return $this->db->query($query, [$startDate])->getRowArray();
    }

    private function getShippingMetrics(string $startDate): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_shipments,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                AVG(shipping_cost) as avg_shipping_cost,
                AVG(DATEDIFF(delivered_at, shipped_at)) as avg_delivery_days
            FROM shipments 
            WHERE created_at >= ?
        ";
        
        return $this->db->query($query, [$startDate])->getRowArray();
    }

    private function getInventoryMetrics(): array
    {
        $query = "
            SELECT 
                COUNT(DISTINCT p.id) as total_products,
                SUM(i.quantity) as total_inventory,
                COUNT(CASE WHEN SUM(i.quantity) <= p.low_stock_threshold THEN 1 END) as low_stock_products,
                COUNT(CASE WHEN SUM(i.quantity) = 0 THEN 1 END) as out_of_stock_products,
                AVG(i.quantity) as avg_stock_per_product
            FROM products p
            LEFT JOIN inventory i ON i.product_id = p.id
            WHERE p.is_active = 1
            GROUP BY p.id
        ";
        
        return $this->db->query($query)->getRowArray();
    }

    private function calculateUsageTrend(int $couponId): array
    {
        $query = "
            SELECT 
                DATE(used_at) as date,
                COUNT(*) as usage_count
            FROM coupon_usage 
            WHERE coupon_id = ? 
            AND used_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(used_at)
            ORDER BY date ASC
        ";
        
        return $this->db->query($query, [$couponId])->getResultArray();
    }

    /**
     * Log de consultas para análisis de rendimiento
     */
    private function logQuery(string $operation, float $executionTime, int $resultCount): void
    {
        $this->queryLog[] = [
            'operation' => $operation,
            'execution_time' => round($executionTime * 1000, 2), // en milisegundos
            'result_count' => $resultCount,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log consultas lentas (más de 100ms)
        if ($executionTime > 0.1) {
            log_message('info', "Slow query detected: {$operation} took " . round($executionTime * 1000, 2) . "ms");
        }
    }

    /**
     * Obtener estadísticas de rendimiento
     */
    public function getPerformanceStats(): array
    {
        return [
            'total_queries' => count($this->queryLog),
            'avg_execution_time' => count($this->queryLog) > 0 ? 
                round(array_sum(array_column($this->queryLog, 'execution_time')) / count($this->queryLog), 2) : 0,
            'slow_queries' => array_filter($this->queryLog, function($log) {
                return $log['execution_time'] > 100; // más de 100ms
            }),
            'queries_by_operation' => array_count_values(array_column($this->queryLog, 'operation'))
        ];
    }

    /**
     * Limpiar cache específico
     */
    public function clearCache(string $pattern = null): bool
    {
        if ($pattern) {
            // Limpiar cache específico por patrón
            $keys = [
                'available_coupons_*',
                'coupon_stats_*',
                'tracking_*',
                'shipping_costs_*',
                'kpi_metrics_*',
                'inventory_status_*'
            ];
            
            foreach ($keys as $key) {
                if (strpos($key, $pattern) !== false) {
                    $this->cache->deleteMatching($key);
                }
            }
        } else {
            // Limpiar todo el cache
            $this->cache->clean();
        }
        
        return true;
    }
}
