<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\SystemMonitor;

/**
 * Comando para monitoreo del sistema
 * Ejecuta verificaciones de salud y envía alertas
 */
class MonitorCommand extends BaseCommand
{
    protected $group = 'MrCell';
    protected $name = 'monitor:check';
    protected $description = 'Ejecutar monitoreo del sistema y enviar alertas';
    protected $usage = 'monitor:check [options]';
    protected $options = [
        '--verbose' => 'Mostrar información detallada',
        '--stats' => 'Mostrar estadísticas de monitoreo',
        '--alerts-only' => 'Solo mostrar alertas activas',
        '--test-alert' => 'Enviar alerta de prueba',
        '--days' => 'Días para estadísticas (default: 7)'
    ];
    
    private $monitor;
    
    public function run(array $params)
    {
        $this->monitor = new SystemMonitor();
        
        CLI::write('🔍 MrCell System Monitor', 'green');
        CLI::write('========================', 'green');
        CLI::newLine();
        
        $verbose = CLI::getOption('verbose');
        $statsOnly = CLI::getOption('stats');
        $alertsOnly = CLI::getOption('alerts-only');
        $testAlert = CLI::getOption('test-alert');
        $days = (int) (CLI::getOption('days') ?? 7);
        
        try {
            // Enviar alerta de prueba
            if ($testAlert) {
                $this->sendTestAlert();
                return;
            }
            
            // Mostrar solo estadísticas
            if ($statsOnly) {
                $this->showStats($days);
                return;
            }
            
            // Mostrar solo alertas activas
            if ($alertsOnly) {
                $this->showActiveAlerts();
                return;
            }
            
            // Ejecutar monitoreo completo
            CLI::write('🚀 Ejecutando verificación del sistema...', 'cyan');
            CLI::newLine();
            
            $startTime = microtime(true);
            $result = $this->monitor->runSystemCheck();
            $totalTime = round(microtime(true) - $startTime, 2);
            
            // Mostrar resumen
            $this->showSummary($result, $totalTime);
            
            // Mostrar detalles si es verbose
            if ($verbose) {
                $this->showDetailedResults($result);
            }
            
            // Mostrar alertas
            if (!empty($result['alerts'])) {
                $this->showAlerts($result['alerts']);
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ Error en monitoreo: ' . $e->getMessage());
            if ($verbose) {
                CLI::error('Stack trace: ' . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Mostrar resumen del monitoreo
     */
    private function showSummary(array $result, float $totalTime): void
    {
        $statusColors = [
            'healthy' => 'green',
            'warning' => 'yellow',
            'critical' => 'red'
        ];
        
        $statusEmojis = [
            'healthy' => '✅',
            'warning' => '⚠️',
            'critical' => '🚨'
        ];
        
        $status = $result['overall_status'];
        $color = $statusColors[$status] ?? 'white';
        $emoji = $statusEmojis[$status] ?? '📊';
        
        CLI::write("📊 RESUMEN DEL MONITOREO", 'blue');
        CLI::write("========================", 'blue');
        CLI::write("{$emoji} Estado general: " . strtoupper($status), $color);
        CLI::write("⏱️  Duración: {$result['duration_ms']}ms (Total: {$totalTime}s)");
        CLI::write("🚨 Problemas críticos: {$result['critical_issues']}", $result['critical_issues'] > 0 ? 'red' : 'green');
        CLI::write("⚠️  Advertencias: {$result['warnings']}", $result['warnings'] > 0 ? 'yellow' : 'green');
        CLI::write("📅 Timestamp: {$result['timestamp']}");
        CLI::newLine();
        
        // Mostrar estado de cada verificación
        CLI::write("🔍 ESTADO DE VERIFICACIONES", 'blue');
        CLI::write("===========================", 'blue');
        
        foreach ($result['checks'] as $checkName => $checkResult) {
            $checkStatus = $checkResult['status'];
            $checkColor = $statusColors[$checkStatus] ?? 'white';
            $checkEmoji = $statusEmojis[$checkStatus] ?? '📊';
            
            $alertCount = count($checkResult['alerts'] ?? []);
            $alertText = $alertCount > 0 ? " ({$alertCount} alertas)" : "";
            
            CLI::write("{$checkEmoji} " . ucfirst(str_replace('_', ' ', $checkName)) . ": " . 
                      strtoupper($checkStatus) . $alertText, $checkColor);
        }
        
        CLI::newLine();
    }
    
    /**
     * Mostrar resultados detallados
     */
    private function showDetailedResults(array $result): void
    {
        CLI::write("📋 MÉTRICAS DETALLADAS", 'blue');
        CLI::write("======================", 'blue');
        
        foreach ($result['checks'] as $checkName => $checkResult) {
            CLI::write(strtoupper(str_replace('_', ' ', $checkName)), 'cyan');
            CLI::write(str_repeat('-', strlen($checkName) + 2), 'cyan');
            
            if (!empty($checkResult['metrics'])) {
                foreach ($checkResult['metrics'] as $metric => $value) {
                    if (is_array($value)) {
                        CLI::write("  {$metric}:");
                        foreach ($value as $subKey => $subValue) {
                            CLI::write("    {$subKey}: {$subValue}");
                        }
                    } else {
                        CLI::write("  {$metric}: {$value}");
                    }
                }
            }
            
            CLI::newLine();
        }
    }
    
    /**
     * Mostrar alertas
     */
    private function showAlerts(array $alerts): void
    {
        CLI::write("🚨 ALERTAS ACTIVAS", 'red');
        CLI::write("==================", 'red');
        
        $criticalAlerts = array_filter($alerts, function($alert) {
            return $alert['severity'] === 'critical';
        });
        
        $warningAlerts = array_filter($alerts, function($alert) {
            return $alert['severity'] === 'warning';
        });
        
        // Mostrar alertas críticas primero
        if (!empty($criticalAlerts)) {
            CLI::write("🚨 CRÍTICAS:", 'red');
            foreach ($criticalAlerts as $alert) {
                CLI::write("  • {$alert['message']}", 'red');
                if (isset($alert['metric']) && isset($alert['value'])) {
                    CLI::write("    Métrica: {$alert['metric']} = {$alert['value']}", 'red');
                }
                if (isset($alert['threshold'])) {
                    CLI::write("    Umbral: {$alert['threshold']}", 'red');
                }
                CLI::newLine();
            }
        }
        
        // Mostrar advertencias
        if (!empty($warningAlerts)) {
            CLI::write("⚠️  ADVERTENCIAS:", 'yellow');
            foreach ($warningAlerts as $alert) {
                CLI::write("  • {$alert['message']}", 'yellow');
                if (isset($alert['metric']) && isset($alert['value'])) {
                    CLI::write("    Métrica: {$alert['metric']} = {$alert['value']}", 'yellow');
                }
                if (isset($alert['threshold'])) {
                    CLI::write("    Umbral: {$alert['threshold']}", 'yellow');
                }
                CLI::newLine();
            }
        }
    }
    
    /**
     * Mostrar estadísticas de monitoreo
     */
    private function showStats(int $days): void
    {
        CLI::write("📊 ESTADÍSTICAS DE MONITOREO ({$days} días)", 'cyan');
        CLI::write(str_repeat('=', 35 + strlen((string)$days)), 'cyan');
        CLI::newLine();
        
        $stats = $this->monitor->getMonitoringStats($days);
        
        if (isset($stats['error'])) {
            CLI::error("❌ Error obteniendo estadísticas: {$stats['error']}");
            return;
        }
        
        CLI::write("📈 Total de verificaciones: {$stats['total_checks']}", 'green');
        CLI::write("🚨 Total de alertas: {$stats['total_alerts']}", 'red');
        CLI::newLine();
        
        // Estadísticas de alertas por tipo y severidad
        if (!empty($stats['alert_stats'])) {
            CLI::write("🔍 ALERTAS POR TIPO Y SEVERIDAD:", 'blue');
            
            $alertsByType = [];
            foreach ($stats['alert_stats'] as $alert) {
                $type = $alert['alert_type'];
                $severity = $alert['severity'];
                $count = $alert['count'];
                
                if (!isset($alertsByType[$type])) {
                    $alertsByType[$type] = [];
                }
                $alertsByType[$type][$severity] = $count;
            }
            
            foreach ($alertsByType as $type => $severities) {
                CLI::write("  " . ucfirst($type) . ":");
                foreach ($severities as $severity => $count) {
                    $color = $severity === 'critical' ? 'red' : ($severity === 'warning' ? 'yellow' : 'green');
                    CLI::write("    {$severity}: {$count}", $color);
                }
                CLI::newLine();
            }
        }
        
        // Estadísticas de verificaciones de salud
        if (!empty($stats['health_checks'])) {
            CLI::write("💚 VERIFICACIONES DE SALUD POR DÍA:", 'blue');
            
            $healthByDate = [];
            foreach ($stats['health_checks'] as $check) {
                $date = $check['date'];
                $status = trim($check['status'], '"'); // Remover comillas JSON
                $count = $check['checks'];
                
                if (!isset($healthByDate[$date])) {
                    $healthByDate[$date] = [];
                }
                $healthByDate[$date][$status] = $count;
            }
            
            foreach ($healthByDate as $date => $statuses) {
                CLI::write("  {$date}:");
                foreach ($statuses as $status => $count) {
                    $color = $status === 'critical' ? 'red' : ($status === 'warning' ? 'yellow' : 'green');
                    CLI::write("    {$status}: {$count}", $color);
                }
                CLI::newLine();
            }
        }
    }
    
    /**
     * Mostrar alertas activas
     */
    private function showActiveAlerts(): void
    {
        CLI::write("🚨 ALERTAS ACTIVAS", 'red');
        CLI::write("==================", 'red');
        CLI::newLine();
        
        try {
            $db = \Config\Database::connect();
            
            $activeAlerts = $db->table('system_alerts')
                             ->where('is_resolved', 0)
                             ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                             ->orderBy('severity DESC, created_at DESC')
                             ->limit(20)
                             ->get()
                             ->getResultArray();
            
            if (empty($activeAlerts)) {
                CLI::write("✅ No hay alertas activas", 'green');
                return;
            }
            
            foreach ($activeAlerts as $alert) {
                $severityColor = $alert['severity'] === 'critical' ? 'red' : 
                               ($alert['severity'] === 'warning' ? 'yellow' : 'green');
                
                $severityEmoji = $alert['severity'] === 'critical' ? '🚨' : 
                               ($alert['severity'] === 'warning' ? '⚠️' : 'ℹ️');
                
                CLI::write("{$severityEmoji} [{$alert['severity']}] {$alert['title']}", $severityColor);
                CLI::write("   Tipo: {$alert['alert_type']}");
                CLI::write("   Creada: {$alert['created_at']}");
                CLI::newLine();
            }
            
            CLI::write("Total de alertas activas: " . count($activeAlerts), 'blue');
            
        } catch (\Exception $e) {
            CLI::error("❌ Error obteniendo alertas activas: " . $e->getMessage());
        }
    }
    
    /**
     * Enviar alerta de prueba
     */
    private function sendTestAlert(): void
    {
        CLI::write("🧪 ENVIANDO ALERTA DE PRUEBA", 'yellow');
        CLI::write("=============================", 'yellow');
        CLI::newLine();
        
        try {
            $testAlert = [
                'type' => 'test',
                'severity' => 'warning',
                'message' => 'Esta es una alerta de prueba del sistema de monitoreo MrCell',
                'metric' => 'test_metric',
                'value' => 'test_value',
                'check' => 'manual_test'
            ];
            
            // Crear instancia del monitor y enviar alerta
            $monitor = new SystemMonitor();
            
            // Usar reflexión para acceder al método privado
            $reflection = new \ReflectionClass($monitor);
            $sendAlertMethod = $reflection->getMethod('sendAlert');
            $sendAlertMethod->setAccessible(true);
            
            $sendAlertMethod->invoke($monitor, $testAlert);
            
            CLI::write("✅ Alerta de prueba enviada correctamente", 'green');
            CLI::write("📱 Verifica tu WhatsApp para confirmar la recepción", 'blue');
            
        } catch (\Exception $e) {
            CLI::error("❌ Error enviando alerta de prueba: " . $e->getMessage());
        }
    }
}
