<?php

namespace App\Services;

use App\Models\WhatsAppSettingsModel;
use App\Models\WhatsAppTemplateModel;
use App\Models\UserNotificationPreferencesModel;
use App\Models\WhatsAppMessageLogModel;
use CodeIgniter\HTTP\CURLRequest;

class WhatsAppService
{
    protected $settingsModel;
    protected $templateModel;
    protected $preferencesModel;
    protected $messageLogModel;
    protected $client;

    public function __construct()
    {
        $this->settingsModel = new WhatsAppSettingsModel();
        $this->templateModel = new WhatsAppTemplateModel();
        $this->preferencesModel = new UserNotificationPreferencesModel();
        $this->messageLogModel = new WhatsAppMessageLogModel();
        $this->client = \Config\Services::curlrequest();
    }

    /**
     * Enviar mensaje usando plantilla
     */
    public function sendTemplateMessage($templateKey, $phoneNumber, $variables = [], $userId = null)
    {
        try {
            // Verificar si WhatsApp está habilitado
            if (!$this->settingsModel->isEnabled()) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp está deshabilitado'
                ];
            }

            // Verificar si el usuario tiene habilitada esta notificación
            if ($userId && !$this->preferencesModel->isNotificationEnabled($userId, $templateKey)) {
                return [
                    'success' => false,
                    'error' => 'Usuario ha deshabilitado esta notificación'
                ];
            }

            // Procesar plantilla
            $processedTemplate = $this->templateModel->processTemplate($templateKey, $variables);
            if (!$processedTemplate) {
                return [
                    'success' => false,
                    'error' => 'Plantilla no encontrada: ' . $templateKey
                ];
            }

            // Validar variables
            $validation = $this->templateModel->validateVariables($templateKey, $variables);
            if (isset($validation['error'])) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // Enviar mensaje
            return $this->sendMessage($phoneNumber, $processedTemplate['message'], $templateKey, $userId);

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp sendTemplateMessage: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error interno: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Enviar mensaje directo
     */
    public function sendMessage($phoneNumber, $message, $templateKey = null, $userId = null)
    {
        try {
            // Limpiar número de teléfono
            $phoneNumber = $this->cleanPhoneNumber($phoneNumber);
            if (!$phoneNumber) {
                return [
                    'success' => false,
                    'error' => 'Número de teléfono inválido'
                ];
            }

            // Validar número antes de enviar (opcional, configurable)
            if (env('VALIDATE_PHONE_BEFORE_WHATSAPP', true)) {
                $phoneValidationService = new \App\Services\PhoneValidationService();
                $validation = $phoneValidationService->validatePhoneNumber($phoneNumber);

                if (!$validation['valid']) {
                    log_message('warning', "Intento de envío a número inválido: {$phoneNumber} - Error: " . ($validation['error'] ?? 'Número no válido'));

                    return [
                        'success' => false,
                        'error' => 'Número de teléfono no válido: ' . ($validation['error'] ?? 'No existe o está inactivo'),
                        'validation_details' => $validation
                    ];
                }

                log_message('info', "Número validado para WhatsApp: {$phoneNumber} - Operadora: " . ($validation['carrier'] ?? 'Desconocida'));
            }

            // Obtener configuración de API
            $config = $this->settingsModel->getApiConfig();
            $validation = $this->settingsModel->validateApiConfig();
            
            if ($validation !== true) {
                return [
                    'success' => false,
                    'error' => 'Configuración incompleta: ' . implode(', ', $validation)
                ];
            }

            // Verificar que el usuario existe antes de registrar en log
            if ($userId) {
                $userModel = new \App\Models\UserModel();
                $userExists = $userModel->find($userId);

                if (!$userExists) {
                    // Si el usuario no existe, esperar un poco y reintentar
                    usleep(500000); // 0.5 segundos
                    $userExists = $userModel->find($userId);
                }

                if (!$userExists) {
                    log_message('error', "Usuario ID $userId no encontrado para envío de WhatsApp");
                    return [
                        'success' => false,
                        'error' => 'Usuario no encontrado'
                    ];
                }
            }

            // Registrar mensaje en log
            $logId = $this->messageLogModel->logMessage([
                'user_id' => $userId,
                'phone_number' => $phoneNumber,
                'template_key' => $templateKey,
                'message_content' => $message,
                'status' => 'pending'
            ]);

            // Preparar datos para la API
            $apiData = [
                'phone' => $phoneNumber,
                'messageType' => 1, // Texto
                'token' => $config['device_token'],
                'chat' => $message
            ];

            // Enviar a la API
            $response = $this->client->post($config['api_url'], [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'X-API-Key' => $config['api_key']
                ],
                'json' => $apiData,
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody();
            $responseData = json_decode($responseBody, true);

            if ($statusCode === 200 && isset($responseData['status']) && $responseData['status'] === true) {
                // Éxito
                $this->messageLogModel->updateMessageStatus(
                    $logId,
                    'sent',
                    $responseData,
                    null
                );

                return [
                    'success' => true,
                    'message_id' => $responseData['data']['messageId'] ?? null,
                    'log_id' => $logId,
                    'response' => $responseData
                ];
            } else {
                // Error
                $errorMessage = $responseData['message'] ?? 'Error desconocido de la API';
                
                $this->messageLogModel->updateMessageStatus(
                    $logId,
                    'failed',
                    $responseData,
                    $errorMessage
                );

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'log_id' => $logId,
                    'response' => $responseData
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp sendMessage: ' . $e->getMessage());
            
            if (isset($logId)) {
                $this->messageLogModel->updateMessageStatus(
                    $logId,
                    'failed',
                    null,
                    $e->getMessage()
                );
            }

            return [
                'success' => false,
                'error' => 'Error de conexión: ' . $e->getMessage(),
                'log_id' => $logId ?? null
            ];
        }
    }

    /**
     * Limpiar número de teléfono
     */
    protected function cleanPhoneNumber($phoneNumber)
    {
        // Remover espacios, guiones y otros caracteres
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);
        
        // Remover el + si existe
        $cleaned = ltrim($cleaned, '+');
        
        // Validar que tenga al menos 8 dígitos
        if (strlen($cleaned) < 8) {
            return false;
        }

        return $cleaned;
    }

    /**
     * Verificar si el usuario tiene notificaciones habilitadas
     */
    private function canSendNotification($userId, $phone)
    {
        if (!$userId) {
            return true; // Para usuarios sin ID (casos especiales), permitir envío
        }

        try {
            $db = \Config\Database::connect();
            $user = $db->table('users')
                      ->select('phone_verified, whatsapp_notifications_enabled')
                      ->where('id', $userId)
                      ->get()
                      ->getRowArray();

            if (!$user) {
                return false; // Usuario no encontrado
            }

            // Verificar si el teléfono está verificado y las notificaciones están habilitadas
            return $user['phone_verified'] && $user['whatsapp_notifications_enabled'];

        } catch (\Exception $e) {
            log_message('error', 'Error verificando preferencias de notificación: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Enviar notificación de registro de cliente
     */
    public function sendCustomerRegistrationNotification($customerData)
    {
        if (empty($customerData['phone']) || empty($customerData['name'])) {
            return ['success' => false, 'error' => 'Datos de cliente incompletos'];
        }

        $variables = [
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'] ?? 'No proporcionado'
        ];

        return $this->sendTemplateMessage(
            'customer_registration',
            $customerData['phone'],
            $variables,
            $customerData['id'] ?? null
        );
    }

    /**
     * Enviar notificación de orden creada
     */
    public function sendOrderCreatedNotification($orderData, $customerData)
    {
        if (empty($customerData['phone']) || empty($orderData['order_number'])) {
            return ['success' => false, 'error' => 'Datos de orden incompletos'];
        }

        // Verificar si el usuario tiene notificaciones habilitadas
        if (!$this->canSendNotification($customerData['id'] ?? null, $customerData['phone'])) {
            return ['success' => false, 'error' => 'Usuario no tiene notificaciones habilitadas o teléfono no verificado'];
        }

        $variables = [
            'customer_name' => $customerData['name'] ?? $customerData['customer_name'],
            'order_number' => $orderData['order_number'],
            'order_total' => number_format($orderData['total'], 2)
        ];

        return $this->sendTemplateMessage(
            'order_created',
            $customerData['phone'],
            $variables,
            $customerData['id'] ?? null
        );
    }

    /**
     * Enviar notificación de nueva orden a administradores
     */
    public function sendAdminOrderNotification($orderData, $customerData)
    {
        try {
            // Obtener plantilla para administradores
            $template = $this->templateModel->getTemplate('admin_new_order');
            if (!$template) {
                // Si no existe la plantilla específica, usar una genérica
                $message = "🛒 *Nueva Orden Recibida*\n\n" .
                          "📋 Orden: {$orderData['order_number']}\n" .
                          "👤 Cliente: {$customerData['name']}\n" .
                          "💰 Total: Q" . number_format($orderData['total'], 2) . "\n" .
                          "📱 Teléfono: {$customerData['phone']}\n" .
                          "📧 Email: {$customerData['email']}\n\n" .
                          "🔗 Ver en admin: " . base_url('admin/orders');
            } else {
                $variables = [
                    'customer_name' => $customerData['name'],
                    'customer_phone' => $customerData['phone'],
                    'customer_email' => $customerData['email'],
                    'order_number' => $orderData['order_number'],
                    'order_total' => number_format($orderData['total'], 2),
                    'admin_url' => base_url('admin/orders')
                ];
                $message = $this->templateModel->processTemplate($template['message_template'], $variables);
            }

            // Obtener administradores con teléfono válido
            $db = \Config\Database::connect();
            $admins = $db->query("
                SELECT DISTINCT u.phone, u.first_name, u.last_name
                FROM users u
                INNER JOIN user_roles ur ON ur.user_id = u.id
                INNER JOIN roles r ON r.id = ur.role_id
                WHERE r.name IN ('admin', 'super_admin')
                AND u.phone IS NOT NULL
                AND u.phone != ''
                AND u.phone REGEXP '^\\+502[0-9]{8}$'
                AND u.status = 'active'
            ")->getResultArray();

            $results = [];
            foreach ($admins as $admin) {
                $result = $this->sendMessage($admin['phone'], $message);
                $results[] = [
                    'admin' => $admin['first_name'] . ' ' . $admin['last_name'],
                    'phone' => $admin['phone'],
                    'success' => $result['success'],
                    'error' => $result['error'] ?? null
                ];
            }

            return [
                'success' => true,
                'sent_to' => count($admins),
                'results' => $results
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error enviando notificación a administradores: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Enviar notificación de cambio de estado de orden
     */
    public function sendOrderStatusChangeNotification($orderData, $customerData, $oldStatus, $newStatus)
    {
        if (empty($customerData['phone']) || empty($orderData['order_number'])) {
            return ['success' => false, 'error' => 'Datos de orden incompletos'];
        }

        // Verificar si el usuario tiene notificaciones habilitadas
        if (!$this->canSendNotification($customerData['id'] ?? null, $customerData['phone'])) {
            return ['success' => false, 'error' => 'Usuario no tiene notificaciones habilitadas o teléfono no verificado'];
        }

        $variables = [
            'customer_name' => $customerData['name'] ?? $customerData['customer_name'],
            'order_number' => $orderData['order_number'],
            'old_status' => $this->translateStatus($oldStatus),
            'new_status' => $this->translateStatus($newStatus)
        ];

        return $this->sendTemplateMessage(
            'order_status_change',
            $customerData['phone'],
            $variables,
            $customerData['id'] ?? null
        );
    }

    /**
     * Enviar notificación de producto agregado
     */
    public function sendProductAddedNotification($productData, $userIds = [])
    {
        if (empty($productData['name']) || empty($productData['price'])) {
            return ['success' => false, 'error' => 'Datos de producto incompletos'];
        }

        $variables = [
            'product_name' => $productData['name'],
            'product_price' => number_format($productData['price'], 2)
        ];

        $results = [];
        
        // Si no se especifican usuarios, obtener todos los que tienen habilitada esta notificación
        if (empty($userIds)) {
            $userIds = $this->getUsersWithNotificationEnabled('product_added');
        }

        foreach ($userIds as $userId) {
            $userModel = new \App\Models\UserModel();
            $user = $userModel->find($userId);
            
            if ($user && !empty($user['phone'])) {
                $result = $this->sendTemplateMessage(
                    'product_added',
                    $user['phone'],
                    $variables,
                    $userId
                );
                $results[] = $result;
            }
        }

        return $results;
    }

    /**
     * Enviar notificación de descuento en producto
     */
    public function sendProductDiscountNotification($productData, $oldPrice, $newPrice, $userIds = [])
    {
        if (empty($productData['name']) || empty($oldPrice) || empty($newPrice)) {
            return ['success' => false, 'error' => 'Datos de descuento incompletos'];
        }

        $discountAmount = $oldPrice - $newPrice;
        
        $variables = [
            'product_name' => $productData['name'],
            'old_price' => number_format($oldPrice, 2),
            'new_price' => number_format($newPrice, 2),
            'discount_amount' => number_format($discountAmount, 2)
        ];

        $results = [];
        
        // Si no se especifican usuarios, obtener todos los que tienen habilitada esta notificación
        if (empty($userIds)) {
            $userIds = $this->getUsersWithNotificationEnabled('product_discount');
        }

        foreach ($userIds as $userId) {
            $userModel = new \App\Models\UserModel();
            $user = $userModel->find($userId);
            
            if ($user && !empty($user['phone'])) {
                $result = $this->sendTemplateMessage(
                    'product_discount',
                    $user['phone'],
                    $variables,
                    $userId
                );
                $results[] = $result;
            }
        }

        return $results;
    }

    /**
     * Obtener usuarios que tienen habilitada una notificación
     */
    protected function getUsersWithNotificationEnabled($templateKey)
    {
        $userModel = new \App\Models\UserModel();
        
        // Obtener usuarios que tienen la notificación habilitada explícitamente
        $enabledUsers = $this->preferencesModel
            ->where('template_key', $templateKey)
            ->where('is_enabled', 1)
            ->findColumn('user_id');

        // Obtener usuarios que no tienen preferencia (usar por defecto)
        $allUsers = $userModel->where('phone !=', '')
                             ->where('phone IS NOT NULL', null, false)
                             ->findColumn('id');

        $usersWithPreference = $this->preferencesModel
            ->where('template_key', $templateKey)
            ->findColumn('user_id');

        $usersWithoutPreference = array_diff($allUsers, $usersWithPreference);

        return array_merge($enabledUsers, $usersWithoutPreference);
    }

    /**
     * Traducir estados
     */
    protected function translateStatus($status)
    {
        $translations = [
            'pending' => 'Pendiente',
            'confirmed' => 'Confirmado',
            'processing' => 'Procesando',
            'shipped' => 'Enviado',
            'delivered' => 'Entregado',
            'cancelled' => 'Cancelado'
        ];

        return $translations[$status] ?? $status;
    }

    /**
     * Probar configuración de API
     */
    public function testApiConnection()
    {
        return $this->sendMessage(
            '50200000000', // Número de prueba
            'Mensaje de prueba desde MrCell - ' . date('Y-m-d H:i:s')
        );
    }
}
