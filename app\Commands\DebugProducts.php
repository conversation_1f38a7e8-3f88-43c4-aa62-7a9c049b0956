<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\ProductModel;

class DebugProducts extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'debug:products';
    protected $description = 'Debug productos problemáticos';
    protected $usage       = 'debug:products [product_ids]';
    protected $arguments   = [
        'product_ids' => 'IDs de productos separados por coma (ej: 95,97,100)'
    ];

    public function run(array $params)
    {
        $productIds = $params[0] ?? '95,97,100,101,102,103,104';
        $ids = array_map('trim', explode(',', $productIds));

        CLI::write('=== Debug de Productos Problemáticos ===', 'yellow');
        CLI::newLine();

        $productModel = new ProductModel();

        foreach ($ids as $id) {
            CLI::write("=== Producto ID: {$id} ===", 'cyan');
            
            $product = $productModel->find($id);
            
            if (!$product) {
                CLI::write("Producto {$id} no encontrado", 'red');
                CLI::newLine();
                continue;
            }

            CLI::write("Nombre: " . $product['name'], 'white');
            CLI::write("SKU: " . ($product['sku'] ?? 'N/A'), 'white');
            CLI::write("Precio regular: " . ($product['price_regular'] ?? 'N/A'), 'white');
            CLI::write("Precio oferta: " . ($product['price_sale'] ?? 'N/A'), 'white');
            CLI::write("Stock: " . ($product['stock_quantity'] ?? 'N/A'), 'white');
            CLI::write("Imagen: " . ($product['featured_image'] ?? 'N/A'), 'white');
            CLI::write("Descripción: " . substr($product['description'] ?? 'N/A', 0, 100) . "...", 'white');
            
            // Verificar si hay campos que son arrays
            foreach ($product as $field => $value) {
                if (is_array($value)) {
                    CLI::write("CAMPO ARRAY DETECTADO - {$field}: " . json_encode($value), 'red');
                }
            }

            // Simular preparación de datos para Recurrente
            try {
                $price = !empty($product['price_sale']) && $product['price_sale'] > 0 
                    ? $product['price_sale'] 
                    : $product['price_regular'];

                $imageUrl = null;
                if (!empty($product['featured_image'])) {
                    $productionUrl = 'https://mrcell.com.gt';
                    
                    if (strpos($product['featured_image'], 'http') === 0) {
                        $imageUrl = $product['featured_image'];
                    } elseif (strpos($product['featured_image'], 'assets/') === 0) {
                        $imageUrl = $productionUrl . '/' . $product['featured_image'];
                    } else {
                        $imageUrl = $productionUrl . '/assets/img/products/' . $product['featured_image'];
                    }
                }

                $productData = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'description' => $product['description'] ?? $product['short_description'] ?? '',
                    'sku' => $product['sku'],
                    'price' => $price,
                    'stock_quantity' => $product['stock_quantity'] ?? null,
                    'image_url' => $imageUrl
                ];

                CLI::write("Datos preparados correctamente:", 'green');
                CLI::write("  - Precio: {$price}", 'white');
                CLI::write("  - URL imagen: " . ($imageUrl ?? 'N/A'), 'white');

            } catch (\Exception $e) {
                CLI::write("ERROR preparando datos: " . $e->getMessage(), 'red');
            }

            CLI::newLine();
        }
    }
}
