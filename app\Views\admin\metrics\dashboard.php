<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .metrics-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .metric-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .real-time-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .progress-ring {
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: #e9ecef;
        }
        
        .progress-ring .progress {
            stroke: #28a745;
            stroke-dasharray: 314;
            stroke-dashoffset: 314;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }
        
        .alert-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-critical {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="metrics-container">
        <!-- Header -->
        <div class="metric-header">
            <h1><i class="fas fa-chart-line"></i> Dashboard de Métricas</h1>
            <h3>MrCell Guatemala - Monitoreo en Tiempo Real</h3>
            <p class="mb-0">Sistema de análisis avanzado con métricas en vivo</p>
            <div class="real-time-indicator">
                <i class="fas fa-circle"></i> EN VIVO
            </div>
        </div>
        
        <!-- Métricas Principales -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-primary"><?= number_format($current_metrics['total_users']) ?></div>
                    <div class="metric-label">Usuarios Totales</div>
                    <small class="text-muted"><i class="fas fa-users"></i> Registrados</small>
                </div>
            </div>
            
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-success"><?= number_format($current_metrics['total_products']) ?></div>
                    <div class="metric-label">Productos</div>
                    <small class="text-muted"><i class="fas fa-box"></i> En catálogo</small>
                </div>
            </div>
            
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-warning"><?= number_format($current_metrics['total_orders']) ?></div>
                    <div class="metric-label">Pedidos</div>
                    <small class="text-muted"><i class="fas fa-shopping-cart"></i> Totales</small>
                </div>
            </div>
            
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-info"><?= number_format($current_metrics['active_carts']) ?></div>
                    <div class="metric-label">Carritos Activos</div>
                    <small class="text-muted"><i class="fas fa-clock"></i> 24h</small>
                </div>
            </div>
            
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-danger"><?= number_format($current_metrics['wishlist_items']) ?></div>
                    <div class="metric-label">Wishlist</div>
                    <small class="text-muted"><i class="fas fa-heart"></i> Items</small>
                </div>
            </div>
            
            <div class="col-md-2">
                <div class="metric-card text-center">
                    <div class="metric-value text-secondary"><?= number_format($current_metrics['whatsapp_messages']) ?></div>
                    <div class="metric-label">WhatsApp</div>
                    <small class="text-muted"><i class="fas fa-comment"></i> 24h</small>
                </div>
            </div>
        </div>
        
        <!-- Estado del Sistema -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="metric-card">
                    <h5><i class="fas fa-heartbeat"></i> Estado del Sistema</h5>
                    <div class="d-flex align-items-center mb-3">
                        <span class="status-indicator status-<?= $system_health['overall_status'] ?>"></span>
                        <strong class="text-capitalize"><?= $system_health['overall_status'] ?></strong>
                        <span class="ms-auto text-muted"><?= $system_health['response_time'] ?></span>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-danger h4"><?= $system_health['critical_issues'] ?></div>
                            <small>Críticos</small>
                        </div>
                        <div class="col-4">
                            <div class="text-warning h4"><?= $system_health['warnings'] ?></div>
                            <small>Advertencias</small>
                        </div>
                        <div class="col-4">
                            <div class="text-muted h6"><?= date('H:i', strtotime($system_health['last_check'])) ?></div>
                            <small>Última verificación</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="metric-card">
                    <h5><i class="fas fa-memory"></i> Rendimiento</h5>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Memoria</span>
                            <span><?= $performance_stats['memory_usage']['current'] ?> MB</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: <?= min(($performance_stats['memory_usage']['current'] / 128) * 100, 100) ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Tiempo de Ejecución</span>
                            <span><?= $performance_stats['execution_time']['current'] ?> ms</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?= min(($performance_stats['execution_time']['current'] / 1000) * 100, 100) ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-database"></i> <?= $performance_stats['database']['queries_today'] ?> consultas hoy
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="metric-card text-center">
                    <h5><i class="fas fa-tachometer-alt"></i> Score de Optimización</h5>
                    <div class="progress-ring">
                        <svg width="100" height="100">
                            <circle class="background" cx="50" cy="50" r="45"></circle>
                            <circle class="progress" cx="50" cy="50" r="45" id="optimization-circle"></circle>
                        </svg>
                    </div>
                    <div class="mt-2">
                        <div class="h3" id="optimization-score">85</div>
                        <small class="text-muted">Puntuación de optimización</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Gráficos en Tiempo Real -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="metric-card">
                    <h5><i class="fas fa-chart-area"></i> Métricas del Sistema</h5>
                    <div class="chart-container">
                        <canvas id="systemMetricsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="metric-card">
                    <h5><i class="fas fa-chart-line"></i> Actividad de Usuarios</h5>
                    <div class="chart-container">
                        <canvas id="userActivityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Automatizaciones y Alertas -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="metric-card">
                    <h5><i class="fas fa-robot"></i> Estado de Automatizaciones</h5>
                    <div id="automation-status">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2">Cargando métricas de automatización...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="metric-card">
                    <h5><i class="fas fa-exclamation-triangle"></i> Alertas Recientes</h5>
                    <div id="recent-alerts" style="max-height: 300px; overflow-y: auto;">
                        <?php if (empty($recent_alerts)): ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <p>No hay alertas recientes</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_alerts as $alert): ?>
                                <div class="alert-item alert-<?= $alert['severity'] ?>">
                                    <strong><?= $alert['title'] ?></strong><br>
                                    <small><?= $alert['message'] ?></small>
                                    <div class="text-end">
                                        <small class="text-muted"><?= date('H:i', strtotime($alert['created_at'])) ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Enlaces Rápidos -->
        <div class="row">
            <div class="col-md-12">
                <div class="metric-card">
                    <h5><i class="fas fa-link"></i> Acciones Rápidas</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <a href="<?= base_url('admin/automation') ?>" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-tachometer-alt"></i> Panel Principal
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?= base_url('test') ?>" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-vial"></i> Ejecutar Pruebas
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?= base_url('cron/status') ?>" class="btn btn-info w-100 mb-2" target="_blank">
                                <i class="fas fa-heartbeat"></i> Estado Sistema
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100 mb-2" onclick="refreshMetrics()">
                                <i class="fas fa-sync-alt"></i> Actualizar Métricas
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Variables globales para gráficos
        let systemMetricsChart;
        let userActivityChart;
        let metricsInterval;
        
        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            startRealTimeUpdates();
            updateOptimizationScore(85);
        });
        
        // Inicializar gráficos
        function initializeCharts() {
            // Gráfico de métricas del sistema
            const systemCtx = document.getElementById('systemMetricsChart').getContext('2d');
            systemMetricsChart = new Chart(systemCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU %',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Memoria %',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
            
            // Gráfico de actividad de usuarios
            const userCtx = document.getElementById('userActivityChart').getContext('2d');
            userActivityChart = new Chart(userCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Usuarios Online', 'Sesiones Activas', 'Nuevos Registros'],
                    datasets: [{
                        data: [25, 45, 8],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        // Iniciar actualizaciones en tiempo real
        function startRealTimeUpdates() {
            metricsInterval = setInterval(updateRealTimeMetrics, 30000); // Cada 30 segundos
            updateRealTimeMetrics(); // Primera actualización inmediata
        }
        
        // Actualizar métricas en tiempo real
        function updateRealTimeMetrics() {
            fetch('<?= base_url('admin/metrics/real-time-metrics') ?>', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCharts(data.metrics);
                    updateAutomationStatus(data.metrics.automation);
                    updateSystemMetrics(data.metrics.system);
                }
            })
            .catch(error => {
                console.error('Error actualizando métricas:', error);
            });
        }
        
        // Actualizar gráficos con nuevos datos
        function updateCharts(metrics) {
            const now = new Date().toLocaleTimeString();
            
            // Actualizar gráfico de sistema
            if (systemMetricsChart.data.labels.length > 10) {
                systemMetricsChart.data.labels.shift();
                systemMetricsChart.data.datasets[0].data.shift();
                systemMetricsChart.data.datasets[1].data.shift();
            }
            
            systemMetricsChart.data.labels.push(now);
            systemMetricsChart.data.datasets[0].data.push(metrics.system.cpu_usage);
            systemMetricsChart.data.datasets[1].data.push((metrics.system.memory_usage / 128) * 100);
            systemMetricsChart.update('none');
            
            // Actualizar gráfico de usuarios
            userActivityChart.data.datasets[0].data = [
                metrics.users.online_users,
                metrics.users.active_sessions,
                metrics.users.new_registrations_today
            ];
            userActivityChart.update('none');
        }
        
        // Actualizar estado de automatizaciones
        function updateAutomationStatus(automation) {
            const statusHtml = `
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="h4 text-primary">${automation.automations_run_today}</div>
                        <small>Ejecutadas Hoy</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-success">${automation.success_rate}%</div>
                        <small>Tasa de Éxito</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-info">${automation.whatsapp_messages_sent}</div>
                        <small>WhatsApp Enviados</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-warning">${automation.price_alerts_sent + automation.stock_alerts_sent}</div>
                        <small>Alertas Enviadas</small>
                    </div>
                </div>
            `;
            
            document.getElementById('automation-status').innerHTML = statusHtml;
        }
        
        // Actualizar métricas del sistema
        function updateSystemMetrics(system) {
            // Actualizar indicadores en tiempo real si es necesario
        }
        
        // Actualizar score de optimización
        function updateOptimizationScore(score) {
            const circle = document.getElementById('optimization-circle');
            const scoreElement = document.getElementById('optimization-score');
            
            const circumference = 2 * Math.PI * 45;
            const offset = circumference - (score / 100) * circumference;
            
            circle.style.strokeDashoffset = offset;
            scoreElement.textContent = score;
            
            // Cambiar color según el score
            if (score >= 80) {
                circle.style.stroke = '#28a745';
            } else if (score >= 60) {
                circle.style.stroke = '#ffc107';
            } else {
                circle.style.stroke = '#dc3545';
            }
        }
        
        // Refrescar métricas manualmente
        function refreshMetrics() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            button.disabled = true;
            
            updateRealTimeMetrics();
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }
        
        // Limpiar intervalos al salir
        window.addEventListener('beforeunload', function() {
            if (metricsInterval) {
                clearInterval(metricsInterval);
            }
        });
    </script>
</body>
</html>
