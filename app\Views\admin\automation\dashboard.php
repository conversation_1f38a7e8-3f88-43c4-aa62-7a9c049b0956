<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-robot"></i> Panel de Automatizaciones
        </h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary" onclick="runSystemCheck()">
                <i class="fas fa-heartbeat"></i> Verificar Sistema
            </button>
            <button type="button" class="btn btn-success" onclick="runAllAutomations()">
                <i class="fas fa-play"></i> Ejecutar Todo
            </button>
        </div>
    </div>

    <!-- Estado del Sistema -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-<?= $system_status['status'] === 'healthy' ? 'success' : 'warning' ?> shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-<?= $system_status['status'] === 'healthy' ? 'success' : 'warning' ?> text-uppercase mb-1">
                                Estado del Sistema
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $system_status['status'] === 'healthy' ? 'Saludable' : 'Advertencias' ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-<?= $system_status['status'] === 'healthy' ? 'check-circle' : 'exclamation-triangle' ?> fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Alertas Activas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $system_status['active_alerts'] ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Última Verificación
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?php if (isset($system_status['last_check']['created_at'])): ?>
                                    <?= date('H:i', strtotime($system_status['last_check']['created_at'])) ?>
                                <?php else: ?>
                                    Nunca
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Automatizaciones
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?= isset($automation_stats['total_executions']) ? $automation_stats['total_executions'] : 0 ?> (7d)
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-play"></i> Acciones Rápidas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary btn-block" onclick="runTask('prices')">
                                <i class="fas fa-dollar-sign"></i> Monitor de Precios
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-success btn-block" onclick="runTask('stock')">
                                <i class="fas fa-boxes"></i> Alertas de Stock
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-info btn-block" onclick="runTask('wishlist')">
                                <i class="fas fa-heart"></i> Recordatorios Wishlist
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-warning btn-block" onclick="runTask('cleanup')">
                                <i class="fas fa-broom"></i> Limpieza de Datos
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-secondary btn-block" onclick="runTask('backup')">
                                <i class="fas fa-database"></i> Backup Manual
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-dark btn-block" onclick="runTask('performance')">
                                <i class="fas fa-chart-line"></i> Análisis Rendimiento
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> Enlaces Rápidos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="<?= base_url('admin/automation/cron-jobs') ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-clock"></i> Configurar Cron Jobs
                        </a>
                        <a href="<?= base_url('admin/automation/logs') ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-alt"></i> Ver Logs
                        </a>
                        <a href="<?= base_url('admin/automation/alerts') ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell"></i> Gestionar Alertas
                        </a>
                        <a href="<?= base_url('admin/automation/settings') ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog"></i> Configuraciones
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Recientes -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history"></i> Actividad Reciente
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_logs)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Tarea</th>
                                        <th>Estado</th>
                                        <th>Duración</th>
                                        <th>Fecha</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_logs as $log): ?>
                                        <tr>
                                            <td>
                                                <strong><?= ucfirst(str_replace('_', ' ', $log['task_name'])) ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($log['success']): ?>
                                                    <span class="badge badge-success">Exitoso</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Fallido</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= number_format($log['duration'], 2) ?>s</td>
                                            <td><?= date('d/m/Y H:i', strtotime($log['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No hay logs recientes disponibles</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultados -->
<div class="modal fade" id="resultModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resultado de la Operación</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="resultContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script>
// Funciones JavaScript para las automatizaciones
function runTask(task) {
    showLoading('Ejecutando ' + task + '...');
    
    fetch('<?= base_url('admin/automation/run-task') ?>/' + task, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showResult(data, 'Resultado: ' + task);
    })
    .catch(error => {
        hideLoading();
        showError('Error ejecutando la tarea: ' + error.message);
    });
}

function runAllAutomations() {
    runTask('all');
}

function runSystemCheck() {
    showLoading('Verificando sistema...');
    
    fetch('<?= base_url('admin/automation/system-check') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showResult(data, 'Verificación del Sistema');
        
        // Recargar página después de 3 segundos para actualizar estadísticas
        setTimeout(() => {
            location.reload();
        }, 3000);
    })
    .catch(error => {
        hideLoading();
        showError('Error verificando el sistema: ' + error.message);
    });
}

function showResult(data, title) {
    let content = '<h6>' + title + '</h6>';
    
    if (data.success !== false) {
        content += '<div class="alert alert-success"><i class="fas fa-check"></i> Operación completada exitosamente</div>';
        
        if (data.duration_ms) {
            content += '<p><strong>Duración:</strong> ' + data.duration_ms + 'ms</p>';
        }
        
        if (data.total_success !== undefined) {
            content += '<p><strong>Tareas exitosas:</strong> ' + data.total_success + '</p>';
        }
        
        if (data.total_failed !== undefined && data.total_failed > 0) {
            content += '<p><strong>Tareas fallidas:</strong> ' + data.total_failed + '</p>';
        }
        
        // Mostrar detalles específicos según el tipo de tarea
        if (data.products_processed) {
            content += '<p><strong>Productos procesados:</strong> ' + data.products_processed + '</p>';
        }
        
        if (data.notifications_sent) {
            content += '<p><strong>Notificaciones enviadas:</strong> ' + data.notifications_sent + '</p>';
        }
        
        if (data.cleaned && data.total_records_cleaned) {
            content += '<p><strong>Registros limpiados:</strong> ' + data.total_records_cleaned + '</p>';
        }
        
        if (data.backup_file) {
            content += '<p><strong>Archivo de backup:</strong> ' + data.backup_file + '</p>';
        }
        
    } else {
        content += '<div class="alert alert-danger"><i class="fas fa-times"></i> Error: ' + data.error + '</div>';
    }
    
    document.getElementById('resultContent').innerHTML = content;
    $('#resultModal').modal('show');
}

function showError(message) {
    document.getElementById('resultContent').innerHTML = 
        '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + message + '</div>';
    $('#resultModal').modal('show');
}

function showLoading(message) {
    document.getElementById('resultContent').innerHTML = 
        '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>' + message + '</p></div>';
    $('#resultModal').modal('show');
}

function hideLoading() {
    // El modal se mantiene abierto para mostrar el resultado
}

// Auto-refresh cada 5 minutos
setInterval(() => {
    location.reload();
}, 300000);
</script>
<?= $this->endSection() ?>
