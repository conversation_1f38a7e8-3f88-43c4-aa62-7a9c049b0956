-- =====================================================
-- STORED PROCEDURES PARA MRCELL E-COMMERCE
-- =====================================================

-- Crear tabla de configuraciones del sistema
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_group VARCHAR(50) DEFAULT 'general',
    display_name VARCHAR(200),
    description TEXT,
    setting_type ENUM('text', 'textarea', 'select', 'checkbox', 'number', 'email', 'url') DEFAULT 'text',
    options JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar configuraciones por defecto
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_group, display_name, description, setting_type) VALUES
-- Configuración General
('site_name', 'MrCell', 'general', 'Nombre del Sitio', 'Nombre principal del sitio web', 'text'),
('site_description', 'Tu tienda de tecnología móvil de confianza', 'general', 'Descripción', 'Descripción del sitio web', 'textarea'),
('contact_email', '<EMAIL>', 'general', 'Email de Contacto', 'Email principal de contacto', 'email'),
('contact_phone', '+502 2345-6789', 'general', 'Teléfono de Contacto', 'Teléfono principal de contacto', 'text'),
('contact_address', 'Ciudad de Guatemala, Guatemala', 'general', 'Dirección', 'Dirección física de la empresa', 'textarea'),

-- Métodos de Pago
('payment_cash', '1', 'payment', 'Pago en Efectivo', 'Permitir pago contra entrega', 'checkbox'),
('payment_transfer', '1', 'payment', 'Transferencia Bancaria', 'Permitir depósito o transferencia', 'checkbox'),
('payment_card', '0', 'payment', 'Tarjeta de Crédito/Débito', 'Permitir pagos con tarjeta', 'checkbox'),
('payment_paypal', '0', 'payment', 'PayPal', 'Permitir pagos con PayPal', 'checkbox'),

-- Notificaciones
('notifications_email', '1', 'notifications', 'Notificaciones por Email', 'Enviar notificaciones por email', 'checkbox'),
('notifications_sms', '0', 'notifications', 'Notificaciones por SMS', 'Enviar notificaciones por SMS', 'checkbox'),
('notify_new_orders', '1', 'notifications', 'Notificar nuevos pedidos', 'Notificar cuando hay nuevos pedidos', 'checkbox'),
('notify_low_stock', '1', 'notifications', 'Alertas de stock bajo', 'Notificar cuando el stock está bajo', 'checkbox'),

-- Configuración Avanzada
('timezone', 'America/Guatemala', 'advanced', 'Zona Horaria', 'Zona horaria del sistema', 'select'),
('currency', 'GTQ', 'advanced', 'Moneda', 'Moneda principal del sitio', 'select'),
('language', 'es', 'advanced', 'Idioma', 'Idioma principal del sitio', 'select'),
('products_per_page', '12', 'advanced', 'Productos por Página', 'Número de productos por página', 'select'),
('maintenance_mode', '0', 'advanced', 'Modo Mantenimiento', 'Activar modo mantenimiento', 'checkbox');

DELIMITER $$

-- =====================================================
-- SP: Estadísticas del Dashboard Administrativo
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_admin_dashboard_stats$$
CREATE PROCEDURE sp_get_admin_dashboard_stats()
BEGIN
    SELECT
        (SELECT COUNT(*) FROM users WHERE deleted_at IS NULL) as total_users,
        (SELECT COUNT(*) FROM orders WHERE deleted_at IS NULL) as total_orders,
        (SELECT COUNT(*) FROM products WHERE deleted_at IS NULL) as total_products,
        (SELECT COALESCE(SUM(total), 0) FROM orders WHERE status != 'cancelled' AND deleted_at IS NULL) as total_revenue,
        (SELECT COUNT(*) FROM orders WHERE status = 'pending' AND deleted_at IS NULL) as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'confirmed' AND deleted_at IS NULL) as confirmed_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'shipped' AND deleted_at IS NULL) as shipped_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'delivered' AND deleted_at IS NULL) as delivered_orders;
END$$

-- =====================================================
-- SP: Obtener Pedidos Recientes
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_recent_orders$$
CREATE PROCEDURE sp_get_recent_orders(
    IN p_limit INT
)
BEGIN
    SELECT
        o.id,
        o.order_number,
        o.customer_name,
        o.customer_email,
        o.total,
        o.status,
        o.payment_status,
        o.created_at,
        COALESCE(u.name, o.customer_name) as user_name
    FROM orders o
    LEFT JOIN users u ON u.id = o.customer_id
    WHERE o.deleted_at IS NULL
    ORDER BY o.created_at DESC
    LIMIT p_limit;
END$$

-- =====================================================
-- SP: Crear Pedido Completo
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_order$$
CREATE PROCEDURE sp_create_order(
    IN p_order_number VARCHAR(50),
    IN p_customer_id INT,
    IN p_customer_name VARCHAR(255),
    IN p_customer_email VARCHAR(255),
    IN p_customer_phone VARCHAR(20),
    IN p_shipping_address TEXT,
    IN p_customer_notes TEXT,
    IN p_payment_method VARCHAR(50),
    IN p_subtotal DECIMAL(10,2),
    IN p_tax_amount DECIMAL(10,2),
    IN p_shipping_cost DECIMAL(10,2),
    IN p_total DECIMAL(10,2),
    IN p_source VARCHAR(20),
    OUT p_order_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear el pedido';
        SET p_order_id = 0;
    END;

    START TRANSACTION;

    -- Validar datos requeridos
    IF p_order_number IS NULL OR p_customer_name IS NULL OR p_total <= 0 THEN
        SET p_result = 'ERROR: Datos requeridos faltantes';
        SET p_order_id = 0;
        ROLLBACK;
    ELSE
        -- Insertar pedido
        INSERT INTO orders (
            order_number, customer_id, customer_name, customer_email, 
            customer_phone, shipping_address, customer_notes, payment_method,
            subtotal, tax_amount, shipping_cost, total, status, 
            payment_status, source, created_by, created_at
        ) VALUES (
            p_order_number, p_customer_id, p_customer_name, p_customer_email,
            p_customer_phone, p_shipping_address, p_customer_notes, p_payment_method,
            p_subtotal, p_tax_amount, p_shipping_cost, p_total, 'pending',
            'pending', p_source, p_customer_id, NOW()
        );

        SET p_order_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS: Pedido creado correctamente';
        
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Agregar Item al Pedido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_add_order_item$$
CREATE PROCEDURE sp_add_order_item(
    IN p_order_id INT,
    IN p_product_id INT,
    IN p_quantity INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_product_name VARCHAR(255);
    DECLARE v_product_sku VARCHAR(100);
    DECLARE v_product_price DECIMAL(10,2);
    DECLARE v_stock_quantity INT;
    DECLARE v_subtotal DECIMAL(10,2);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo agregar el item';
    END;

    START TRANSACTION;

    -- Obtener información del producto
    SELECT name, sku, 
           COALESCE(price_sale, price_regular) as price,
           stock_quantity
    INTO v_product_name, v_product_sku, v_product_price, v_stock_quantity
    FROM products 
    WHERE id = p_product_id AND is_active = 1;

    -- Validar stock disponible
    IF v_stock_quantity < p_quantity THEN
        SET p_result = 'ERROR: Stock insuficiente';
        ROLLBACK;
    ELSE
        -- Calcular subtotal
        SET v_subtotal = v_product_price * p_quantity;

        -- Insertar item del pedido
        INSERT INTO order_items (
            order_id, product_id, product_name, product_sku,
            quantity, price, subtotal, created_at
        ) VALUES (
            p_order_id, p_product_id, v_product_name, v_product_sku,
            p_quantity, v_product_price, v_subtotal, NOW()
        );

        -- Actualizar stock del producto
        UPDATE products 
        SET stock_quantity = stock_quantity - p_quantity,
            updated_at = NOW()
        WHERE id = p_product_id;

        SET p_result = 'SUCCESS: Item agregado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Estado del Pedido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_order_status$$
CREATE PROCEDURE sp_update_order_status(
    IN p_order_id INT,
    IN p_new_status VARCHAR(50),
    IN p_tracking_number VARCHAR(100),
    IN p_notes TEXT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_current_status VARCHAR(50);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el estado';
    END;

    START TRANSACTION;

    -- Obtener estado actual
    SELECT status INTO v_current_status
    FROM orders WHERE id = p_order_id;

    IF v_current_status IS NULL THEN
        SET p_result = 'ERROR: Pedido no encontrado';
        ROLLBACK;
    ELSE
        -- Actualizar pedido
        UPDATE orders SET
            status = p_new_status,
            tracking_number = COALESCE(p_tracking_number, tracking_number),
            notes = COALESCE(p_notes, notes),
            delivered_at = CASE WHEN p_new_status = 'delivered' THEN NOW() ELSE delivered_at END,
            cancelled_at = CASE WHEN p_new_status = 'cancelled' THEN NOW() ELSE cancelled_at END,
            updated_at = NOW()
        WHERE id = p_order_id;

        SET p_result = CONCAT('SUCCESS: Estado cambiado de ', v_current_status, ' a ', p_new_status);
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Estadísticas de Ventas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_sales_stats$$
CREATE PROCEDURE sp_get_sales_stats(
    IN p_start_date DATE,
    IN p_end_date DATE
)
BEGIN
    SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_orders,
        SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END) as shipped_orders,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_orders,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
        SUM(CASE WHEN status != 'cancelled' THEN total ELSE 0 END) as total_revenue,
        AVG(CASE WHEN status != 'cancelled' THEN total ELSE NULL END) as average_order_value,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders,
        SUM(CASE WHEN DATE(created_at) = CURDATE() AND status != 'cancelled' THEN total ELSE 0 END) as today_revenue
    FROM orders
    WHERE (p_start_date IS NULL OR DATE(created_at) >= p_start_date)
      AND (p_end_date IS NULL OR DATE(created_at) <= p_end_date);
END$$

-- =====================================================
-- SP: Obtener Productos Más Vendidos
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_top_products$$
CREATE PROCEDURE sp_get_top_products(
    IN p_limit INT,
    IN p_start_date DATE,
    IN p_end_date DATE
)
BEGIN
    SELECT 
        oi.product_id,
        oi.product_name,
        SUM(oi.quantity) as total_sold,
        SUM(oi.subtotal) as total_revenue,
        COUNT(DISTINCT oi.order_id) as total_orders,
        AVG(oi.price) as average_price
    FROM order_items oi
    INNER JOIN orders o ON o.id = oi.order_id
    WHERE o.status != 'cancelled'
      AND (p_start_date IS NULL OR DATE(o.created_at) >= p_start_date)
      AND (p_end_date IS NULL OR DATE(o.created_at) <= p_end_date)
    GROUP BY oi.product_id, oi.product_name
    ORDER BY total_sold DESC
    LIMIT p_limit;
END$$

-- =====================================================
-- SP: Validar y Procesar Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_validate_cart$$
CREATE PROCEDURE sp_validate_cart(
    IN p_cart_json JSON,
    OUT p_is_valid BOOLEAN,
    OUT p_total DECIMAL(10,2),
    OUT p_errors TEXT
)
BEGIN
    DECLARE v_product_id INT;
    DECLARE v_quantity INT;
    DECLARE v_stock INT;
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_is_active BOOLEAN;
    DECLARE v_item_total DECIMAL(10,2);
    DECLARE v_cart_total DECIMAL(10,2) DEFAULT 0;
    DECLARE v_error_msg TEXT DEFAULT '';
    DECLARE v_index INT DEFAULT 0;
    DECLARE v_cart_length INT;
    
    -- Obtener longitud del array JSON
    SET v_cart_length = JSON_LENGTH(p_cart_json);
    SET p_is_valid = TRUE;
    
    -- Iterar sobre cada item del carrito
    WHILE v_index < v_cart_length AND p_is_valid = TRUE DO
        -- Extraer datos del item
        SET v_product_id = JSON_UNQUOTE(JSON_EXTRACT(p_cart_json, CONCAT('$[', v_index, '].product_id')));
        SET v_quantity = JSON_UNQUOTE(JSON_EXTRACT(p_cart_json, CONCAT('$[', v_index, '].quantity')));
        
        -- Validar producto
        SELECT stock_quantity, 
               COALESCE(price_sale, price_regular) as price,
               is_active
        INTO v_stock, v_price, v_is_active
        FROM products 
        WHERE id = v_product_id;
        
        -- Verificar si el producto existe y está activo
        IF v_price IS NULL OR v_is_active = FALSE THEN
            SET p_is_valid = FALSE;
            SET v_error_msg = CONCAT(v_error_msg, 'Producto ID ', v_product_id, ' no disponible. ');
        ELSEIF v_stock < v_quantity THEN
            SET p_is_valid = FALSE;
            SET v_error_msg = CONCAT(v_error_msg, 'Stock insuficiente para producto ID ', v_product_id, '. ');
        ELSE
            -- Calcular total del item
            SET v_item_total = v_price * v_quantity;
            SET v_cart_total = v_cart_total + v_item_total;
        END IF;
        
        SET v_index = v_index + 1;
    END WHILE;
    
    SET p_total = v_cart_total;
    SET p_errors = v_error_msg;
END$$

-- =====================================================
-- SP: Registro de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_register_user$$
CREATE PROCEDURE sp_register_user(
    IN p_name VARCHAR(100),
    IN p_email VARCHAR(100),
    IN p_password VARCHAR(255),
    IN p_phone VARCHAR(20),
    OUT p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_email_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo registrar el usuario';
        SET p_user_id = 0;
    END;

    START TRANSACTION;

    -- Verificar si el email ya existe
    SELECT COUNT(*) INTO v_email_exists
    FROM users
    WHERE email = p_email AND deleted_at IS NULL;

    IF v_email_exists > 0 THEN
        SET p_result = 'ERROR: El email ya está registrado';
        SET p_user_id = 0;
        ROLLBACK;
    ELSE
        -- Insertar nuevo usuario
        INSERT INTO users (
            name, email, password, phone, status,
            email_verified, created_at
        ) VALUES (
            p_name, p_email, p_password, p_phone, 'active',
            0, NOW()
        );

        SET p_user_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS: Usuario registrado correctamente';

        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Login de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_login_user$$
CREATE PROCEDURE sp_login_user(
    IN p_email VARCHAR(100),
    OUT p_user_id INT,
    OUT p_user_data JSON,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_user_count INT DEFAULT 0;

    -- Verificar si el usuario existe y está activo
    SELECT COUNT(*) INTO v_user_count
    FROM users
    WHERE email = p_email
      AND status = 'active'
      AND deleted_at IS NULL;

    IF v_user_count = 0 THEN
        SET p_user_id = 0;
        SET p_user_data = NULL;
        SET p_result = 'ERROR: Usuario no encontrado o inactivo';
    ELSE
        -- Obtener datos del usuario
        SELECT
            id,
            JSON_OBJECT(
                'id', id,
                'name', name,
                'email', email,
                'phone', phone,
                'address', address,
                'city', city,
                'department', department,
                'status', status,
                'email_verified', email_verified,
                'last_login', last_login,
                'created_at', created_at
            ),
            'SUCCESS: Usuario encontrado'
        INTO p_user_id, p_user_data, p_result
        FROM users
        WHERE email = p_email
          AND status = 'active'
          AND deleted_at IS NULL;

        -- Actualizar último login
        UPDATE users
        SET last_login = NOW()
        WHERE id = p_user_id;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Perfil de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_profile$$
CREATE PROCEDURE sp_get_user_profile(
    IN p_user_id INT
)
BEGIN
    SELECT
        id,
        username,
        email,
        first_name,
        last_name,
        CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as name,
        status as is_active,
        created_at,
        updated_at
    FROM users
    WHERE id = p_user_id;
END$$

-- =====================================================
-- SP: Actualizar Perfil de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_user_profile$$
CREATE PROCEDURE sp_update_user_profile(
    IN p_user_id INT,
    IN p_name VARCHAR(100),
    IN p_email VARCHAR(100),
    IN p_phone VARCHAR(20),
    IN p_address VARCHAR(255),
    IN p_city VARCHAR(100),
    IN p_department VARCHAR(100),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_email_exists INT DEFAULT 0;
    DECLARE v_user_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el perfil';
    END;

    START TRANSACTION;

    -- Verificar si el usuario existe
    SELECT COUNT(*) INTO v_user_exists
    FROM users
    WHERE id = p_user_id AND deleted_at IS NULL;

    IF v_user_exists = 0 THEN
        SET p_result = 'ERROR: Usuario no encontrado';
        ROLLBACK;
    ELSE
        -- Verificar si el email ya existe en otro usuario
        SELECT COUNT(*) INTO v_email_exists
        FROM users
        WHERE email = p_email
          AND id != p_user_id
          AND deleted_at IS NULL;

        IF v_email_exists > 0 THEN
            SET p_result = 'ERROR: El email ya está en uso por otro usuario';
            ROLLBACK;
        ELSE
            -- Actualizar perfil
            UPDATE users SET
                name = p_name,
                email = p_email,
                phone = p_phone,
                address = p_address,
                city = p_city,
                department = p_department,
                updated_at = NOW()
            WHERE id = p_user_id;

            SET p_result = 'SUCCESS: Perfil actualizado correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Estadísticas de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_stats$$
CREATE PROCEDURE sp_get_user_stats(
    IN p_user_id INT
)
BEGIN
    SELECT
        (SELECT COUNT(*) FROM orders WHERE customer_id = p_user_id) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE customer_id = p_user_id AND status = 'pending') as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE customer_id = p_user_id AND status = 'delivered') as completed_orders,
        (SELECT COALESCE(SUM(total), 0) FROM orders WHERE customer_id = p_user_id AND status != 'cancelled') as total_spent,
        (SELECT COUNT(*) FROM orders WHERE customer_id = p_user_id AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as orders_last_30_days;
END$$

-- =====================================================
-- SP: Obtener Pedidos de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_orders$$
CREATE PROCEDURE sp_get_user_orders(
    IN p_user_id INT,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT
        o.id,
        o.order_number,
        o.total,
        o.status,
        o.payment_status,
        o.payment_method,
        o.created_at,
        o.shipped_at,
        o.delivered_at,
        COUNT(oi.id) as item_count
    FROM orders o
    LEFT JOIN order_items oi ON oi.order_id = o.id
    WHERE o.customer_id = p_user_id
      AND o.deleted_at IS NULL
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END$$

-- =====================================================
-- SP: Cambiar Contraseña de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_change_user_password$$
CREATE PROCEDURE sp_change_user_password(
    IN p_user_id INT,
    IN p_new_password VARCHAR(255),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_user_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo cambiar la contraseña';
    END;

    START TRANSACTION;

    -- Verificar si el usuario existe
    SELECT COUNT(*) INTO v_user_exists
    FROM users
    WHERE id = p_user_id AND deleted_at IS NULL;

    IF v_user_exists = 0 THEN
        SET p_result = 'ERROR: Usuario no encontrado';
        ROLLBACK;
    ELSE
        -- Actualizar contraseña
        UPDATE users SET
            password = p_new_password,
            updated_at = NOW()
        WHERE id = p_user_id;

        SET p_result = 'SUCCESS: Contraseña actualizada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Agregar Producto al Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_add_item$$
CREATE PROCEDURE sp_cart_add_item(
    IN p_session_id VARCHAR(128),
    IN p_product_id INT,
    IN p_quantity INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_product_exists INT DEFAULT 0;
    DECLARE v_stock_quantity INT DEFAULT 0;
    DECLARE v_price DECIMAL(10,2) DEFAULT 0;
    DECLARE v_cart_item_exists INT DEFAULT 0;
    DECLARE v_current_quantity INT DEFAULT 0;
    DECLARE v_new_quantity INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo agregar el producto al carrito';
    END;

    START TRANSACTION;

    -- Verificar que el producto existe y está activo
    SELECT COUNT(*), stock_quantity, COALESCE(price_sale, price_regular)
    INTO v_product_exists, v_stock_quantity, v_price
    FROM products
    WHERE id = p_product_id AND is_active = 1 AND deleted_at IS NULL;

    IF v_product_exists = 0 THEN
        SET p_result = 'ERROR: Producto no encontrado o inactivo';
        ROLLBACK;
    ELSEIF v_stock_quantity < p_quantity THEN
        SET p_result = CONCAT('ERROR: Stock insuficiente. Disponible: ', v_stock_quantity);
        ROLLBACK;
    ELSE
        -- Verificar si el producto ya está en el carrito
        SELECT COUNT(*), COALESCE(quantity, 0)
        INTO v_cart_item_exists, v_current_quantity
        FROM cart_items
        WHERE session_id = p_session_id AND product_id = p_product_id;

        SET v_new_quantity = v_current_quantity + p_quantity;

        -- Verificar que la nueva cantidad no exceda el stock
        IF v_new_quantity > v_stock_quantity THEN
            SET p_result = CONCAT('ERROR: Cantidad total excede stock. Máximo: ', v_stock_quantity);
            ROLLBACK;
        ELSE
            IF v_cart_item_exists > 0 THEN
                -- Actualizar cantidad existente
                UPDATE cart_items
                SET quantity = v_new_quantity,
                    price = v_price,
                    updated_at = NOW()
                WHERE session_id = p_session_id AND product_id = p_product_id;
            ELSE
                -- Insertar nuevo item
                INSERT INTO cart_items (
                    session_id, product_id, quantity, price, created_at
                ) VALUES (
                    p_session_id, p_product_id, p_quantity, v_price, NOW()
                );
            END IF;

            SET p_result = 'SUCCESS: Producto agregado al carrito';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Cantidad en Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_update_item$$
CREATE PROCEDURE sp_cart_update_item(
    IN p_session_id VARCHAR(128),
    IN p_product_id INT,
    IN p_quantity INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_stock_quantity INT DEFAULT 0;
    DECLARE v_price DECIMAL(10,2) DEFAULT 0;
    DECLARE v_cart_item_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el carrito';
    END;

    START TRANSACTION;

    -- Verificar que el item existe en el carrito
    SELECT COUNT(*) INTO v_cart_item_exists
    FROM cart_items
    WHERE session_id = p_session_id AND product_id = p_product_id;

    IF v_cart_item_exists = 0 THEN
        SET p_result = 'ERROR: Producto no encontrado en el carrito';
        ROLLBACK;
    ELSEIF p_quantity <= 0 THEN
        -- Eliminar item si cantidad es 0 o negativa
        DELETE FROM cart_items
        WHERE session_id = p_session_id AND product_id = p_product_id;

        SET p_result = 'SUCCESS: Producto eliminado del carrito';
        COMMIT;
    ELSE
        -- Verificar stock disponible
        SELECT stock_quantity, COALESCE(price_sale, price_regular)
        INTO v_stock_quantity, v_price
        FROM products
        WHERE id = p_product_id AND is_active = 1 AND deleted_at IS NULL;

        IF v_stock_quantity < p_quantity THEN
            SET p_result = CONCAT('ERROR: Stock insuficiente. Disponible: ', v_stock_quantity);
            ROLLBACK;
        ELSE
            -- Actualizar cantidad
            UPDATE cart_items
            SET quantity = p_quantity,
                price = v_price,
                updated_at = NOW()
            WHERE session_id = p_session_id AND product_id = p_product_id;

            SET p_result = 'SUCCESS: Cantidad actualizada';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Eliminar Producto del Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_remove_item$$
CREATE PROCEDURE sp_cart_remove_item(
    IN p_session_id VARCHAR(128),
    IN p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_cart_item_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo eliminar el producto';
    END;

    START TRANSACTION;

    -- Verificar que el item existe en el carrito
    SELECT COUNT(*) INTO v_cart_item_exists
    FROM cart_items
    WHERE session_id = p_session_id AND product_id = p_product_id;

    IF v_cart_item_exists = 0 THEN
        SET p_result = 'ERROR: Producto no encontrado en el carrito';
        ROLLBACK;
    ELSE
        -- Eliminar item
        DELETE FROM cart_items
        WHERE session_id = p_session_id AND product_id = p_product_id;

        SET p_result = 'SUCCESS: Producto eliminado del carrito';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Items del Carrito con Totales
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_get_items$$
CREATE PROCEDURE sp_cart_get_items(
    IN p_session_id VARCHAR(128)
)
BEGIN
    -- Obtener items del carrito con información del producto
    SELECT
        ci.product_id,
        ci.quantity,
        ci.price,
        (ci.quantity * ci.price) as subtotal,
        p.name,
        p.slug,
        p.sku,
        p.featured_image as image,
        p.stock_quantity,
        p.is_active,
        ci.created_at,
        ci.updated_at
    FROM cart_items ci
    INNER JOIN products p ON p.id = ci.product_id
    WHERE ci.session_id = p_session_id
      AND p.is_active = 1
      AND p.deleted_at IS NULL
    ORDER BY ci.created_at ASC;
END$$

-- =====================================================
-- SP: Calcular Totales del Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_calculate_totals$$
CREATE PROCEDURE sp_cart_calculate_totals(
    IN p_session_id VARCHAR(128),
    IN p_shipping_method VARCHAR(50),
    IN p_coupon_code VARCHAR(50)
)
BEGIN
    DECLARE v_subtotal DECIMAL(10,2) DEFAULT 0;
    DECLARE v_tax_rate DECIMAL(5,4) DEFAULT 0.12; -- 12% IVA
    DECLARE v_tax_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE v_shipping_cost DECIMAL(10,2) DEFAULT 0;
    DECLARE v_discount_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE v_total DECIMAL(10,2) DEFAULT 0;
    DECLARE v_item_count INT DEFAULT 0;

    -- Calcular subtotal
    SELECT
        COALESCE(SUM(ci.quantity * ci.price), 0),
        COUNT(*)
    INTO v_subtotal, v_item_count
    FROM cart_items ci
    INNER JOIN products p ON p.id = ci.product_id
    WHERE ci.session_id = p_session_id
      AND p.is_active = 1
      AND p.deleted_at IS NULL;

    -- Calcular impuestos
    SET v_tax_amount = v_subtotal * v_tax_rate;

    -- Calcular costo de envío usando el nuevo sistema
    IF p_shipping_method = 'pickup' THEN
        SET v_shipping_cost = 0.00;
    ELSE
        -- Usar SP para calcular envío basado en productos del carrito
        CALL sp_calculate_shipping_cost(p_session_id, @calc_shipping_cost, @shipping_details, @shipping_result);
        SELECT @calc_shipping_cost INTO v_shipping_cost;

        -- Si hay error en el cálculo, usar tarifa estándar
        IF v_shipping_cost IS NULL THEN
            SET v_shipping_cost = 25.00;
        END IF;
    END IF;

    -- Envío gratis para pedidos mayores a Q200
    IF v_subtotal >= 200.00 THEN
        SET v_shipping_cost = 0.00;
    END IF;

    -- Aplicar cupón de descuento (simplificado)
    IF p_coupon_code IS NOT NULL AND p_coupon_code != '' THEN
        CASE p_coupon_code
            WHEN 'DESCUENTO10' THEN SET v_discount_amount = v_subtotal * 0.10;
            WHEN 'ENVIOGRATIS' THEN SET v_shipping_cost = 0.00;
            WHEN 'BIENVENIDO' THEN SET v_discount_amount = 15.00;
            ELSE SET v_discount_amount = 0.00;
        END CASE;
    END IF;

    -- Calcular total
    SET v_total = v_subtotal + v_tax_amount + v_shipping_cost - v_discount_amount;

    -- Retornar totales
    SELECT
        v_item_count as item_count,
        v_subtotal as subtotal,
        v_tax_amount as tax_amount,
        v_shipping_cost as shipping_cost,
        v_discount_amount as discount_amount,
        v_total as total,
        v_tax_rate as tax_rate;
END$$

-- =====================================================
-- SP: Limpiar Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_clear$$
CREATE PROCEDURE sp_cart_clear(
    IN p_session_id VARCHAR(128),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_items_count INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo limpiar el carrito';
    END;

    START TRANSACTION;

    -- Contar items antes de eliminar
    SELECT COUNT(*) INTO v_items_count
    FROM cart_items
    WHERE session_id = p_session_id;

    -- Eliminar todos los items del carrito
    DELETE FROM cart_items
    WHERE session_id = p_session_id;

    SET p_result = CONCAT('SUCCESS: ', v_items_count, ' productos eliminados del carrito');
    COMMIT;
END$$

-- =====================================================
-- SP: Validar Carrito Completo
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cart_validate$$
CREATE PROCEDURE sp_cart_validate(
    IN p_session_id VARCHAR(128),
    OUT p_is_valid BOOLEAN,
    OUT p_errors JSON,
    OUT p_total DECIMAL(10,2)
)
BEGIN
    DECLARE v_error_list TEXT DEFAULT '';
    DECLARE v_item_count INT DEFAULT 0;
    DECLARE v_subtotal DECIMAL(10,2) DEFAULT 0;
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_product_id INT;
    DECLARE v_quantity INT;
    DECLARE v_stock INT;
    DECLARE v_is_active BOOLEAN;
    DECLARE v_product_name VARCHAR(255);

    -- Cursor para revisar cada item del carrito
    DECLARE cart_cursor CURSOR FOR
        SELECT ci.product_id, ci.quantity, p.stock_quantity, p.is_active, p.name
        FROM cart_items ci
        INNER JOIN products p ON p.id = ci.product_id
        WHERE ci.session_id = p_session_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    SET p_is_valid = TRUE;
    SET v_error_list = '';

    -- Verificar que el carrito no esté vacío
    SELECT COUNT(*) INTO v_item_count
    FROM cart_items
    WHERE session_id = p_session_id;

    IF v_item_count = 0 THEN
        SET p_is_valid = FALSE;
        SET v_error_list = 'El carrito está vacío';
    ELSE
        -- Revisar cada item del carrito
        OPEN cart_cursor;
        read_loop: LOOP
            FETCH cart_cursor INTO v_product_id, v_quantity, v_stock, v_is_active, v_product_name;
            IF done THEN
                LEAVE read_loop;
            END IF;

            -- Verificar si el producto está activo
            IF NOT v_is_active THEN
                SET p_is_valid = FALSE;
                SET v_error_list = CONCAT(v_error_list, 'Producto "', v_product_name, '" ya no está disponible. ');
            END IF;

            -- Verificar stock
            IF v_quantity > v_stock THEN
                SET p_is_valid = FALSE;
                SET v_error_list = CONCAT(v_error_list, 'Stock insuficiente para "', v_product_name, '". Disponible: ', v_stock, '. ');
            END IF;
        END LOOP;
        CLOSE cart_cursor;

        -- Calcular total si es válido
        IF p_is_valid THEN
            SELECT COALESCE(SUM(ci.quantity * ci.price), 0)
            INTO v_subtotal
            FROM cart_items ci
            INNER JOIN products p ON p.id = ci.product_id
            WHERE ci.session_id = p_session_id
              AND p.is_active = 1
              AND p.deleted_at IS NULL;

            SET p_total = v_subtotal * 1.12 + 25.00; -- IVA + envío básico
        ELSE
            SET p_total = 0;
        END IF;
    END IF;

    -- Convertir errores a JSON
    IF v_error_list = '' THEN
        SET p_errors = JSON_ARRAY();
    ELSE
        SET p_errors = JSON_ARRAY(v_error_list);
    END IF;
END$$

-- =====================================================
-- SP: Obtener Productos con Filtros
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_products$$
CREATE PROCEDURE sp_get_products(
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_search VARCHAR(255),
    IN p_min_price DECIMAL(10,2),
    IN p_max_price DECIMAL(10,2),
    IN p_is_featured BOOLEAN,
    IN p_sort_by VARCHAR(50),
    IN p_sort_order VARCHAR(10),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SET @sql = 'SELECT
        p.id,
        p.sku,
        p.name,
        p.slug,
        p.short_description,
        p.price_regular,
        p.price_sale,
        p.stock_quantity,
        p.stock_status,
        p.featured_image,
        p.is_featured,
        p.is_active,
        c.name as category_name,
        b.name as brand_name,
        COALESCE(p.price_sale, p.price_regular) as final_price
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id
    LEFT JOIN brands b ON b.id = p.brand_id
    WHERE p.deleted_at IS NULL';

    -- Filtros dinámicos
    IF p_category_id IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND p.category_id = ', p_category_id);
    END IF;

    IF p_brand_id IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND p.brand_id = ', p_brand_id);
    END IF;

    IF p_search IS NOT NULL AND p_search != '' THEN
        SET @sql = CONCAT(@sql, ' AND (p.name LIKE "%', p_search, '%" OR p.description LIKE "%', p_search, '%" OR p.sku LIKE "%', p_search, '%")');
    END IF;

    IF p_min_price IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND COALESCE(p.price_sale, p.price_regular) >= ', p_min_price);
    END IF;

    IF p_max_price IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND COALESCE(p.price_sale, p.price_regular) <= ', p_max_price);
    END IF;

    IF p_is_featured IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND p.is_featured = ', p_is_featured);
    END IF;

    -- Solo productos activos por defecto
    SET @sql = CONCAT(@sql, ' AND p.is_active = 1');

    -- Ordenamiento
    IF p_sort_by IS NOT NULL THEN
        CASE p_sort_by
            WHEN 'name' THEN SET @sql = CONCAT(@sql, ' ORDER BY p.name');
            WHEN 'price' THEN SET @sql = CONCAT(@sql, ' ORDER BY final_price');
            WHEN 'created' THEN SET @sql = CONCAT(@sql, ' ORDER BY p.created_at');
            WHEN 'featured' THEN SET @sql = CONCAT(@sql, ' ORDER BY p.is_featured DESC, p.created_at');
            ELSE SET @sql = CONCAT(@sql, ' ORDER BY p.created_at');
        END CASE;

        IF p_sort_order = 'DESC' THEN
            SET @sql = CONCAT(@sql, ' DESC');
        ELSE
            SET @sql = CONCAT(@sql, ' ASC');
        END IF;
    ELSE
        SET @sql = CONCAT(@sql, ' ORDER BY p.created_at DESC');
    END IF;

    -- Paginación
    IF p_limit IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' LIMIT ', p_limit);
        IF p_offset IS NOT NULL THEN
            SET @sql = CONCAT(@sql, ' OFFSET ', p_offset);
        END IF;
    END IF;

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- =====================================================
-- SP: Obtener Producto por ID
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_product_by_id$$
CREATE PROCEDURE sp_get_product_by_id(
    IN p_product_id INT
)
BEGIN
    SELECT
        p.*,
        c.name as category_name,
        c.slug as category_slug,
        b.name as brand_name,
        b.slug as brand_slug,
        v.name as vendor_name,
        COALESCE(p.price_sale, p.price_regular) as final_price,
        CASE
            WHEN p.stock_quantity > 0 THEN 'in_stock'
            ELSE 'out_of_stock'
        END as availability
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id
    LEFT JOIN brands b ON b.id = p.brand_id
    LEFT JOIN vendors v ON v.id = p.vendor_id
    WHERE p.id = p_product_id
      AND p.deleted_at IS NULL;
END$$

-- =====================================================
-- SP: Crear Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_product$$
CREATE PROCEDURE sp_create_product(
    IN p_sku VARCHAR(50),
    IN p_name VARCHAR(200),
    IN p_slug VARCHAR(220),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_vendor_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_featured_image VARCHAR(255),
    IN p_is_featured BOOLEAN,
    OUT p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_sku_exists INT DEFAULT 0;
    DECLARE v_slug_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear el producto';
        SET p_product_id = 0;
    END;

    START TRANSACTION;

    -- Verificar SKU único
    SELECT COUNT(*) INTO v_sku_exists
    FROM products
    WHERE sku = p_sku AND deleted_at IS NULL;

    -- Verificar slug único
    SELECT COUNT(*) INTO v_slug_exists
    FROM products
    WHERE slug = p_slug AND deleted_at IS NULL;

    IF v_sku_exists > 0 THEN
        SET p_result = 'ERROR: El SKU ya existe';
        SET p_product_id = 0;
        ROLLBACK;
    ELSEIF v_slug_exists > 0 THEN
        SET p_result = 'ERROR: El slug ya existe';
        SET p_product_id = 0;
        ROLLBACK;
    ELSE
        INSERT INTO products (
            uuid, sku, name, slug, description, short_description,
            category_id, brand_id, vendor_id, price_regular, price_sale,
            stock_quantity, featured_image, is_featured, is_active,
            created_at
        ) VALUES (
            UUID(), p_sku, p_name, p_slug, p_description, p_short_description,
            p_category_id, p_brand_id, p_vendor_id, p_price_regular, p_price_sale,
            p_stock_quantity, p_featured_image, COALESCE(p_is_featured, 0), 1,
            NOW()
        );

        SET p_product_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS: Producto creado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_product$$
CREATE PROCEDURE sp_update_product(
    IN p_product_id INT,
    IN p_sku VARCHAR(50),
    IN p_name VARCHAR(200),
    IN p_slug VARCHAR(220),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_featured_image VARCHAR(255),
    IN p_is_featured BOOLEAN,
    IN p_is_active BOOLEAN,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_product_exists INT DEFAULT 0;
    DECLARE v_sku_exists INT DEFAULT 0;
    DECLARE v_slug_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el producto';
    END;

    START TRANSACTION;

    -- Verificar que el producto existe
    SELECT COUNT(*) INTO v_product_exists
    FROM products
    WHERE id = p_product_id AND deleted_at IS NULL;

    IF v_product_exists = 0 THEN
        SET p_result = 'ERROR: Producto no encontrado';
        ROLLBACK;
    ELSE
        -- Verificar SKU único (excluyendo el producto actual)
        SELECT COUNT(*) INTO v_sku_exists
        FROM products
        WHERE sku = p_sku AND id != p_product_id AND deleted_at IS NULL;

        -- Verificar slug único (excluyendo el producto actual)
        SELECT COUNT(*) INTO v_slug_exists
        FROM products
        WHERE slug = p_slug AND id != p_product_id AND deleted_at IS NULL;

        IF v_sku_exists > 0 THEN
            SET p_result = 'ERROR: El SKU ya existe en otro producto';
            ROLLBACK;
        ELSEIF v_slug_exists > 0 THEN
            SET p_result = 'ERROR: El slug ya existe en otro producto';
            ROLLBACK;
        ELSE
            UPDATE products SET
                sku = p_sku,
                name = p_name,
                slug = p_slug,
                description = p_description,
                short_description = p_short_description,
                category_id = p_category_id,
                brand_id = p_brand_id,
                price_regular = p_price_regular,
                price_sale = p_price_sale,
                stock_quantity = p_stock_quantity,
                featured_image = p_featured_image,
                is_featured = COALESCE(p_is_featured, 0),
                is_active = COALESCE(p_is_active, 1),
                updated_at = NOW()
            WHERE id = p_product_id;

            SET p_result = 'SUCCESS: Producto actualizado correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Pedidos con Filtros Avanzados
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_orders$$
CREATE PROCEDURE sp_get_orders(
    IN p_status VARCHAR(50),
    IN p_payment_method VARCHAR(50),
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_customer_search VARCHAR(255),
    IN p_min_amount DECIMAL(10,2),
    IN p_max_amount DECIMAL(10,2),
    IN p_sort_by VARCHAR(50),
    IN p_sort_order VARCHAR(10),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SET @sql = 'SELECT
        o.id,
        o.order_number,
        o.status,
        o.total as total_amount,
        o.payment_method,
        o.payment_status,
        o.created_at,
        o.updated_at,
        o.customer_name,
        o.customer_email,
        o.customer_phone,
        COUNT(oi.id) as items_count,
        SUM(oi.quantity) as total_items
    FROM orders o
    LEFT JOIN order_items oi ON oi.order_id = o.id
    WHERE 1=1';

    -- Filtros dinámicos
    IF p_status IS NOT NULL AND p_status != '' THEN
        SET @sql = CONCAT(@sql, ' AND o.status = "', p_status, '"');
    END IF;

    IF p_payment_method IS NOT NULL AND p_payment_method != '' THEN
        SET @sql = CONCAT(@sql, ' AND o.payment_method = "', p_payment_method, '"');
    END IF;

    IF p_date_from IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND DATE(o.created_at) >= "', p_date_from, '"');
    END IF;

    IF p_date_to IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND DATE(o.created_at) <= "', p_date_to, '"');
    END IF;

    IF p_customer_search IS NOT NULL AND p_customer_search != '' THEN
        SET @sql = CONCAT(@sql, ' AND (o.customer_name LIKE "%', p_customer_search, '%" OR o.customer_email LIKE "%', p_customer_search, '%" OR o.order_number LIKE "%', p_customer_search, '%")');
    END IF;

    IF p_min_amount IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND o.total >= ', p_min_amount);
    END IF;

    IF p_max_amount IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND o.total <= ', p_max_amount);
    END IF;

    SET @sql = CONCAT(@sql, ' GROUP BY o.id');

    -- Ordenamiento
    IF p_sort_by IS NOT NULL THEN
        CASE p_sort_by
            WHEN 'order_number' THEN SET @sql = CONCAT(@sql, ' ORDER BY o.order_number');
            WHEN 'customer' THEN SET @sql = CONCAT(@sql, ' ORDER BY customer_name');
            WHEN 'total' THEN SET @sql = CONCAT(@sql, ' ORDER BY o.total');
            WHEN 'status' THEN SET @sql = CONCAT(@sql, ' ORDER BY o.status');
            WHEN 'created' THEN SET @sql = CONCAT(@sql, ' ORDER BY o.created_at');
            ELSE SET @sql = CONCAT(@sql, ' ORDER BY o.created_at');
        END CASE;

        IF p_sort_order = 'ASC' THEN
            SET @sql = CONCAT(@sql, ' ASC');
        ELSE
            SET @sql = CONCAT(@sql, ' DESC');
        END IF;
    ELSE
        SET @sql = CONCAT(@sql, ' ORDER BY o.created_at DESC');
    END IF;

    -- Paginación
    IF p_limit IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' LIMIT ', p_limit);
        IF p_offset IS NOT NULL THEN
            SET @sql = CONCAT(@sql, ' OFFSET ', p_offset);
        END IF;
    END IF;

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- =====================================================
-- SP: Obtener Detalles de Pedido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_order_details$$
CREATE PROCEDURE sp_get_order_details(
    IN p_order_id INT
)
BEGIN
    -- Información del pedido
    SELECT
        o.*,
        o.customer_name,
        o.shipping_address
    FROM orders o
    WHERE o.id = p_order_id;

    -- Items del pedido
    SELECT
        oi.*,
        p.name as product_name,
        p.sku as product_sku,
        p.featured_image as product_image,
        (oi.quantity * oi.price) as line_total
    FROM order_items oi
    INNER JOIN products p ON p.id = oi.product_id
    WHERE oi.order_id = p_order_id
    ORDER BY oi.id;
END$$

-- =====================================================
-- SP: Actualizar Estado de Pedido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_order_status$$
CREATE PROCEDURE sp_update_order_status(
    IN p_order_id INT,
    IN p_new_status VARCHAR(50),
    IN p_notes TEXT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_order_exists INT DEFAULT 0;
    DECLARE v_current_status VARCHAR(50);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el estado del pedido';
    END;

    START TRANSACTION;

    -- Verificar que el pedido existe
    SELECT COUNT(*), status INTO v_order_exists, v_current_status
    FROM orders
    WHERE id = p_order_id;

    IF v_order_exists = 0 THEN
        SET p_result = 'ERROR: Pedido no encontrado';
        ROLLBACK;
    ELSE
        -- Actualizar estado del pedido
        UPDATE orders
        SET status = p_new_status,
            updated_at = NOW()
        WHERE id = p_order_id;

        -- Insertar en historial de estados (si existe la tabla)
        INSERT IGNORE INTO order_status_history (
            order_id,
            old_status,
            new_status,
            notes,
            created_at
        ) VALUES (
            p_order_id,
            v_current_status,
            p_new_status,
            p_notes,
            NOW()
        );

        SET p_result = CONCAT('SUCCESS: Estado actualizado de "', v_current_status, '" a "', p_new_status, '"');
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Estadísticas de Pedidos
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_orders_stats$$
CREATE PROCEDURE sp_get_orders_stats(
    IN p_date_from DATE,
    IN p_date_to DATE
)
BEGIN
    DECLARE v_date_from DATE DEFAULT COALESCE(p_date_from, CURDATE() - INTERVAL 30 DAY);
    DECLARE v_date_to DATE DEFAULT COALESCE(p_date_to, CURDATE());

    SELECT
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
        COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
        COALESCE(SUM(total), 0) as total_revenue,
        COALESCE(AVG(total), 0) as average_order_value,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
        COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments
    FROM orders
    WHERE DATE(created_at) BETWEEN v_date_from AND v_date_to;

    -- Estadísticas por método de pago
    SELECT
        payment_method,
        COUNT(*) as order_count,
        COALESCE(SUM(total), 0) as total_amount
    FROM orders
    WHERE DATE(created_at) BETWEEN v_date_from AND v_date_to
    GROUP BY payment_method
    ORDER BY order_count DESC;

    -- Pedidos por día
    SELECT
        DATE(created_at) as order_date,
        COUNT(*) as order_count,
        COALESCE(SUM(total), 0) as daily_revenue
    FROM orders
    WHERE DATE(created_at) BETWEEN v_date_from AND v_date_to
    GROUP BY DATE(created_at)
    ORDER BY order_date DESC;
END$$

-- =====================================================
-- SP: Procesar Pago con Tarjeta
-- =====================================================
DROP PROCEDURE IF EXISTS sp_process_card_payment$$
CREATE PROCEDURE sp_process_card_payment(
    IN p_order_id INT,
    IN p_card_number VARCHAR(20),
    IN p_card_holder VARCHAR(100),
    IN p_expiry_month INT,
    IN p_expiry_year INT,
    IN p_cvv VARCHAR(4),
    IN p_amount DECIMAL(10,2),
    OUT p_transaction_id VARCHAR(50),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_order_exists INT DEFAULT 0;
    DECLARE v_order_total DECIMAL(10,2) DEFAULT 0;
    DECLARE v_order_status VARCHAR(50);
    DECLARE v_payment_status VARCHAR(50);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error al procesar el pago';
        SET p_transaction_id = NULL;
    END;

    START TRANSACTION;

    -- Verificar que el pedido existe y está pendiente
    SELECT COUNT(*), total, status, payment_status
    INTO v_order_exists, v_order_total, v_order_status, v_payment_status
    FROM orders
    WHERE id = p_order_id;

    IF v_order_exists = 0 THEN
        SET p_result = 'ERROR: Pedido no encontrado';
        SET p_transaction_id = NULL;
        ROLLBACK;
    ELSEIF v_payment_status = 'paid' THEN
        SET p_result = 'ERROR: El pedido ya está pagado';
        SET p_transaction_id = NULL;
        ROLLBACK;
    ELSEIF ABS(v_order_total - p_amount) > 0.01 THEN
        SET p_result = 'ERROR: El monto no coincide con el total del pedido';
        SET p_transaction_id = NULL;
        ROLLBACK;
    ELSE
        -- Generar ID de transacción
        SET p_transaction_id = CONCAT('TXN_', UNIX_TIMESTAMP(), '_', p_order_id);

        -- Simular procesamiento de tarjeta (en producción sería integración real)
        -- Por ahora, simulamos éxito si el CVV no es 000
        IF p_cvv = '000' THEN
            -- Simular fallo
            INSERT INTO payment_transactions (
                order_id, transaction_id, payment_method, amount, status,
                card_last_four, card_holder, response_code, response_message,
                created_at
            ) VALUES (
                p_order_id, p_transaction_id, 'card', p_amount, 'failed',
                RIGHT(p_card_number, 4), p_card_holder, 'DECLINED', 'Tarjeta declinada',
                NOW()
            );

            SET p_result = 'ERROR: Pago declinado por el banco';
            ROLLBACK;
        ELSE
            -- Simular éxito
            INSERT INTO payment_transactions (
                order_id, transaction_id, payment_method, amount, status,
                card_last_four, card_holder, response_code, response_message,
                created_at
            ) VALUES (
                p_order_id, p_transaction_id, 'card', p_amount, 'completed',
                RIGHT(p_card_number, 4), p_card_holder, 'APPROVED', 'Pago aprobado',
                NOW()
            );

            -- Actualizar estado del pedido
            UPDATE orders
            SET payment_status = 'paid',
                status = 'processing',
                updated_at = NOW()
            WHERE id = p_order_id;

            SET p_result = 'SUCCESS: Pago procesado correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Procesar Transferencia Bancaria
-- =====================================================
DROP PROCEDURE IF EXISTS sp_process_bank_transfer$$
CREATE PROCEDURE sp_process_bank_transfer(
    IN p_order_id INT,
    IN p_bank_name VARCHAR(100),
    IN p_account_holder VARCHAR(100),
    IN p_reference_number VARCHAR(50),
    IN p_amount DECIMAL(10,2),
    OUT p_transaction_id VARCHAR(50),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_order_exists INT DEFAULT 0;
    DECLARE v_order_total DECIMAL(10,2) DEFAULT 0;
    DECLARE v_payment_status VARCHAR(50);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error al procesar la transferencia';
        SET p_transaction_id = NULL;
    END;

    START TRANSACTION;

    -- Verificar que el pedido existe
    SELECT COUNT(*), total, payment_status
    INTO v_order_exists, v_order_total, v_payment_status
    FROM orders
    WHERE id = p_order_id;

    IF v_order_exists = 0 THEN
        SET p_result = 'ERROR: Pedido no encontrado';
        SET p_transaction_id = NULL;
        ROLLBACK;
    ELSEIF v_payment_status = 'paid' THEN
        SET p_result = 'ERROR: El pedido ya está pagado';
        SET p_transaction_id = NULL;
        ROLLBACK;
    ELSE
        -- Generar ID de transacción
        SET p_transaction_id = CONCAT('TRANSFER_', UNIX_TIMESTAMP(), '_', p_order_id);

        -- Registrar transferencia pendiente de verificación
        INSERT INTO payment_transactions (
            order_id, transaction_id, payment_method, amount, status,
            bank_name, account_holder, reference_number, response_message,
            created_at
        ) VALUES (
            p_order_id, p_transaction_id, 'bank_transfer', p_amount, 'pending',
            p_bank_name, p_account_holder, p_reference_number, 'Transferencia pendiente de verificación',
            NOW()
        );

        -- Actualizar estado del pedido
        UPDATE orders
        SET payment_status = 'pending',
            payment_method = 'bank_transfer',
            updated_at = NOW()
        WHERE id = p_order_id;

        SET p_result = 'SUCCESS: Transferencia registrada, pendiente de verificación';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Confirmar Transferencia
-- =====================================================
DROP PROCEDURE IF EXISTS sp_confirm_transfer$$
CREATE PROCEDURE sp_confirm_transfer(
    IN p_transaction_id VARCHAR(50),
    IN p_verified_amount DECIMAL(10,2),
    IN p_admin_notes TEXT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_transaction_exists INT DEFAULT 0;
    DECLARE v_order_id INT;
    DECLARE v_transaction_amount DECIMAL(10,2);
    DECLARE v_transaction_status VARCHAR(50);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error al confirmar la transferencia';
    END;

    START TRANSACTION;

    -- Verificar que la transacción existe
    SELECT COUNT(*), order_id, amount, status
    INTO v_transaction_exists, v_order_id, v_transaction_amount, v_transaction_status
    FROM payment_transactions
    WHERE transaction_id = p_transaction_id;

    IF v_transaction_exists = 0 THEN
        SET p_result = 'ERROR: Transacción no encontrada';
        ROLLBACK;
    ELSEIF v_transaction_status != 'pending' THEN
        SET p_result = 'ERROR: La transacción ya fue procesada';
        ROLLBACK;
    ELSEIF ABS(v_transaction_amount - p_verified_amount) > 0.01 THEN
        SET p_result = 'ERROR: El monto verificado no coincide';
        ROLLBACK;
    ELSE
        -- Actualizar transacción
        UPDATE payment_transactions
        SET status = 'completed',
            verified_amount = p_verified_amount,
            admin_notes = p_admin_notes,
            verified_at = NOW()
        WHERE transaction_id = p_transaction_id;

        -- Actualizar pedido
        UPDATE orders
        SET payment_status = 'paid',
            status = 'processing',
            updated_at = NOW()
        WHERE id = v_order_id;

        SET p_result = 'SUCCESS: Transferencia confirmada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Transacciones de Pago
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_payment_transactions$$
CREATE PROCEDURE sp_get_payment_transactions(
    IN p_order_id INT,
    IN p_status VARCHAR(50),
    IN p_payment_method VARCHAR(50),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SET @sql = 'SELECT
        pt.*,
        o.order_number,
        o.customer_name,
        o.customer_email
    FROM payment_transactions pt
    INNER JOIN orders o ON o.id = pt.order_id
    WHERE 1=1';

    IF p_order_id IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND pt.order_id = ', p_order_id);
    END IF;

    IF p_status IS NOT NULL AND p_status != '' THEN
        SET @sql = CONCAT(@sql, ' AND pt.status = "', p_status, '"');
    END IF;

    IF p_payment_method IS NOT NULL AND p_payment_method != '' THEN
        SET @sql = CONCAT(@sql, ' AND pt.payment_method = "', p_payment_method, '"');
    END IF;

    SET @sql = CONCAT(@sql, ' ORDER BY pt.created_at DESC');

    IF p_limit IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' LIMIT ', p_limit);
        IF p_offset IS NOT NULL THEN
            SET @sql = CONCAT(@sql, ' OFFSET ', p_offset);
        END IF;
    END IF;

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- =====================================================
-- SP: Calcular Costo de Envío por Carrito
-- =====================================================
DROP PROCEDURE IF EXISTS sp_calculate_shipping_cost$$
CREATE PROCEDURE sp_calculate_shipping_cost(
    IN p_session_id VARCHAR(128),
    OUT p_shipping_cost DECIMAL(10,2),
    OUT p_shipping_details JSON,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_total_weight DECIMAL(8,3) DEFAULT 0;
    DECLARE v_total_volume DECIMAL(10,3) DEFAULT 0;
    DECLARE v_shipping_cost DECIMAL(10,2) DEFAULT 0;
    DECLARE v_item_count INT DEFAULT 0;
    DECLARE v_shipping_details JSON;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        SET p_result = 'ERROR: Error al calcular costo de envío';
        SET p_shipping_cost = 0;
        SET p_shipping_details = JSON_ARRAY();
    END;

    -- Calcular peso y volumen total del carrito
    SELECT
        COUNT(*),
        COALESCE(SUM(ci.quantity * COALESCE(p.weight, 0)), 0) / 1000 as total_weight_kg,
        COALESCE(SUM(ci.quantity * COALESCE(
            CAST(SUBSTRING_INDEX(p.dimensions, 'x', 1) AS DECIMAL) *
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(p.dimensions, 'x', 2), 'x', -1) AS DECIMAL) *
            CAST(SUBSTRING_INDEX(p.dimensions, 'x', -1) AS DECIMAL), 0
        )), 0) as total_volume_cm3
    INTO v_item_count, v_total_weight, v_total_volume
    FROM cart_items ci
    INNER JOIN products p ON p.id = ci.product_id
    WHERE ci.session_id = p_session_id
      AND p.is_active = 1
      AND p.deleted_at IS NULL;

    IF v_item_count = 0 THEN
        SET p_shipping_cost = 0;
        SET p_shipping_details = JSON_OBJECT('message', 'Carrito vacío');
        SET p_result = 'SUCCESS: Carrito vacío';
    ELSE
        -- Obtener el método de envío más común en el carrito
        SELECT
            sm.id,
            sm.name,
            COUNT(*) as product_count
        FROM cart_items ci
        INNER JOIN products p ON p.id = ci.product_id
        INNER JOIN shipping_methods sm ON sm.id = p.shipping_method_id
        WHERE ci.session_id = p_session_id
          AND p.is_active = 1
          AND p.deleted_at IS NULL
          AND sm.is_active = 1
        GROUP BY sm.id, sm.name
        ORDER BY product_count DESC
        LIMIT 1;

        -- Buscar tarifa aplicable
        SELECT sr.rate
        INTO v_shipping_cost
        FROM shipping_rates sr
        INNER JOIN shipping_methods sm ON sm.id = sr.shipping_method_id
        WHERE sr.shipping_method_id = (
            SELECT sm.id
            FROM cart_items ci
            INNER JOIN products p ON p.id = ci.product_id
            INNER JOIN shipping_methods sm ON sm.id = p.shipping_method_id
            WHERE ci.session_id = p_session_id
              AND p.is_active = 1
              AND p.deleted_at IS NULL
              AND sm.is_active = 1
            GROUP BY sm.id
            ORDER BY COUNT(*) DESC
            LIMIT 1
        )
        AND sr.is_active = 1
        AND v_total_weight >= sr.min_weight
        AND (sr.max_weight IS NULL OR v_total_weight <= sr.max_weight)
        AND v_total_volume >= sr.min_volume
        AND (sr.max_volume IS NULL OR v_total_volume <= sr.max_volume)
        ORDER BY sr.rate ASC
        LIMIT 1;

        -- Si no se encuentra tarifa, usar tarifa estándar
        IF v_shipping_cost IS NULL THEN
            SET v_shipping_cost = 25.00;
        END IF;

        SET p_shipping_cost = v_shipping_cost;
        SET p_shipping_details = JSON_OBJECT(
            'total_weight_kg', v_total_weight,
            'total_volume_cm3', v_total_volume,
            'item_count', v_item_count,
            'shipping_cost', v_shipping_cost
        );
        SET p_result = 'SUCCESS: Costo calculado correctamente';
    END IF;
END$$

-- =====================================================
-- SP: Obtener Métodos de Envío
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_shipping_methods$$
CREATE PROCEDURE sp_get_shipping_methods(
    IN p_include_rates BOOLEAN
)
BEGIN
    -- Obtener métodos de envío
    SELECT
        sm.*,
        COUNT(sr.id) as rates_count
    FROM shipping_methods sm
    LEFT JOIN shipping_rates sr ON sr.shipping_method_id = sm.id AND sr.is_active = 1
    WHERE sm.is_active = 1
    GROUP BY sm.id
    ORDER BY sm.sort_order, sm.name;

    -- Si se solicitan las tarifas, obtenerlas también
    IF p_include_rates THEN
        SELECT
            sr.*,
            sm.name as method_name
        FROM shipping_rates sr
        INNER JOIN shipping_methods sm ON sm.id = sr.shipping_method_id
        WHERE sr.is_active = 1 AND sm.is_active = 1
        ORDER BY sm.sort_order, sr.min_weight, sr.min_volume;
    END IF;
END$$

-- =====================================================
-- SP: Crear Método de Envío
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_shipping_method$$
CREATE PROCEDURE sp_create_shipping_method(
    IN p_name VARCHAR(100),
    IN p_description TEXT,
    IN p_sort_order INT,
    OUT p_method_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_name_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear el método de envío';
        SET p_method_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el nombre no exista
    SELECT COUNT(*) INTO v_name_exists
    FROM shipping_methods
    WHERE name = p_name;

    IF v_name_exists > 0 THEN
        SET p_result = 'ERROR: Ya existe un método con ese nombre';
        SET p_method_id = 0;
        ROLLBACK;
    ELSE
        INSERT INTO shipping_methods (
            name, description, sort_order, is_active, created_at
        ) VALUES (
            p_name, p_description, COALESCE(p_sort_order, 0), 1, NOW()
        );

        SET p_method_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS: Método de envío creado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Crear Tarifa de Envío
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_shipping_rate$$
CREATE PROCEDURE sp_create_shipping_rate(
    IN p_shipping_method_id INT,
    IN p_min_weight DECIMAL(8,3),
    IN p_max_weight DECIMAL(8,3),
    IN p_min_volume DECIMAL(10,3),
    IN p_max_volume DECIMAL(10,3),
    IN p_rate DECIMAL(10,2),
    OUT p_rate_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_method_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear la tarifa de envío';
        SET p_rate_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el método existe
    SELECT COUNT(*) INTO v_method_exists
    FROM shipping_methods
    WHERE id = p_shipping_method_id AND is_active = 1;

    IF v_method_exists = 0 THEN
        SET p_result = 'ERROR: Método de envío no encontrado';
        SET p_rate_id = 0;
        ROLLBACK;
    ELSE
        INSERT INTO shipping_rates (
            shipping_method_id, min_weight, max_weight, min_volume, max_volume,
            rate, is_active, created_at
        ) VALUES (
            p_shipping_method_id, p_min_weight, p_max_weight, p_min_volume, p_max_volume,
            p_rate, 1, NOW()
        );

        SET p_rate_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS: Tarifa de envío creada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Estadísticas del Dashboard Admin
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_admin_dashboard_stats$$
CREATE PROCEDURE sp_get_admin_dashboard_stats()
BEGIN
    SELECT
        (SELECT COUNT(*) FROM orders) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'pending') as pending_orders,
        (SELECT COALESCE(SUM(total), 0) FROM orders) as total_revenue,
        (SELECT COUNT(*) FROM products WHERE deleted_at IS NULL) as total_products,
        (SELECT COUNT(*) FROM products WHERE stock_quantity <= 5 AND is_active = 1 AND deleted_at IS NULL) as low_stock_products,
        (SELECT COUNT(*) FROM users WHERE deleted_at IS NULL) as total_users,
        (SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL) as active_users,
        (SELECT COUNT(*) FROM shipping_methods WHERE is_active = 1) as shipping_methods_count;
END$$

-- =====================================================
-- SP: Pedidos Recientes para Dashboard
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_recent_orders$$
CREATE PROCEDURE sp_get_recent_orders(
    IN p_limit INT
)
BEGIN
    SELECT
        o.id,
        o.order_number,
        o.customer_name,
        o.customer_email,
        o.total,
        o.status,
        o.payment_status,
        o.created_at
    FROM orders o
    ORDER BY o.created_at DESC
    LIMIT 10;
END$$

-- =====================================================
-- SP: Obtener Categorías Jerárquicas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_categories_tree$$
CREATE PROCEDURE sp_get_categories_tree(
    IN p_parent_id INT,
    IN p_include_inactive BOOLEAN
)
BEGIN
    IF p_parent_id IS NULL THEN
        -- Obtener categorías raíz
        SELECT
            c.*,
            COUNT(sc.id) as children_count,
            COUNT(p.id) as products_count
        FROM categories c
        LEFT JOIN categories sc ON sc.parent_id = c.id AND sc.deleted_at IS NULL
        LEFT JOIN products p ON p.category_id = c.id AND p.deleted_at IS NULL AND p.is_active = 1
        WHERE c.parent_id IS NULL
          AND c.deleted_at IS NULL
          AND (p_include_inactive = TRUE OR c.is_active = 1)
        GROUP BY c.id
        ORDER BY c.sort_order, c.name;
    ELSE
        -- Obtener subcategorías
        SELECT
            c.*,
            COUNT(sc.id) as children_count,
            COUNT(p.id) as products_count
        FROM categories c
        LEFT JOIN categories sc ON sc.parent_id = c.id AND sc.deleted_at IS NULL
        LEFT JOIN products p ON p.category_id = c.id AND p.deleted_at IS NULL AND p.is_active = 1
        WHERE c.parent_id = p_parent_id
          AND c.deleted_at IS NULL
          AND (p_include_inactive = TRUE OR c.is_active = 1)
        GROUP BY c.id
        ORDER BY c.sort_order, c.name;
    END IF;
END$$

-- =====================================================
-- SP: Crear Categoría
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_category$$
CREATE PROCEDURE sp_create_category(
    IN p_name VARCHAR(100),
    IN p_slug VARCHAR(120),
    IN p_description TEXT,
    IN p_parent_id INT,
    IN p_image VARCHAR(255),
    IN p_icon VARCHAR(100),
    IN p_sort_order INT,
    IN p_meta_title VARCHAR(160),
    IN p_meta_description VARCHAR(320),
    OUT p_category_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_slug_exists INT DEFAULT 0;
    DECLARE v_parent_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear la categoría';
        SET p_category_id = 0;
    END;

    START TRANSACTION;

    -- Verificar slug único
    SELECT COUNT(*) INTO v_slug_exists
    FROM categories
    WHERE slug = p_slug AND deleted_at IS NULL;

    IF v_slug_exists > 0 THEN
        SET p_result = 'ERROR: El slug ya existe';
        SET p_category_id = 0;
        ROLLBACK;
    ELSE
        -- Verificar que la categoría padre existe (si se especifica)
        IF p_parent_id IS NOT NULL THEN
            SELECT COUNT(*) INTO v_parent_exists
            FROM categories
            WHERE id = p_parent_id AND deleted_at IS NULL;

            IF v_parent_exists = 0 THEN
                SET p_result = 'ERROR: La categoría padre no existe';
                SET p_category_id = 0;
                ROLLBACK;
            END IF;
        END IF;

        IF p_category_id = 0 THEN
            -- No hacer nada, ya hay error
            SELECT 1;
        ELSE
            INSERT INTO categories (
                uuid, name, slug, description, parent_id, image, icon,
                sort_order, is_active, meta_title, meta_description, created_at
            ) VALUES (
                UUID(), p_name, p_slug, p_description, p_parent_id, p_image, p_icon,
                COALESCE(p_sort_order, 0), 1, p_meta_title, p_meta_description, NOW()
            );

            SET p_category_id = LAST_INSERT_ID();
            SET p_result = 'SUCCESS: Categoría creada correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Categoría
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_category$$
CREATE PROCEDURE sp_update_category(
    IN p_category_id INT,
    IN p_name VARCHAR(100),
    IN p_slug VARCHAR(120),
    IN p_description TEXT,
    IN p_parent_id INT,
    IN p_image VARCHAR(255),
    IN p_icon VARCHAR(100),
    IN p_sort_order INT,
    IN p_is_active BOOLEAN,
    IN p_meta_title VARCHAR(160),
    IN p_meta_description VARCHAR(320),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_category_exists INT DEFAULT 0;
    DECLARE v_slug_exists INT DEFAULT 0;
    DECLARE v_parent_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar la categoría';
    END;

    START TRANSACTION;

    -- Verificar que la categoría existe
    SELECT COUNT(*) INTO v_category_exists
    FROM categories
    WHERE id = p_category_id AND deleted_at IS NULL;

    IF v_category_exists = 0 THEN
        SET p_result = 'ERROR: Categoría no encontrada';
        ROLLBACK;
    ELSE
        -- Verificar slug único (excluyendo la categoría actual)
        SELECT COUNT(*) INTO v_slug_exists
        FROM categories
        WHERE slug = p_slug AND id != p_category_id AND deleted_at IS NULL;

        IF v_slug_exists > 0 THEN
            SET p_result = 'ERROR: El slug ya existe en otra categoría';
            ROLLBACK;
        ELSE
            -- Verificar que la categoría padre existe (si se especifica)
            IF p_parent_id IS NOT NULL THEN
                SELECT COUNT(*) INTO v_parent_exists
                FROM categories
                WHERE id = p_parent_id AND deleted_at IS NULL;

                IF v_parent_exists = 0 THEN
                    SET p_result = 'ERROR: La categoría padre no existe';
                    ROLLBACK;
                ELSEIF p_parent_id = p_category_id THEN
                    SET p_result = 'ERROR: Una categoría no puede ser padre de sí misma';
                    ROLLBACK;
                END IF;
            END IF;

            IF p_result IS NULL THEN
                UPDATE categories SET
                    name = p_name,
                    slug = p_slug,
                    description = p_description,
                    parent_id = p_parent_id,
                    image = p_image,
                    icon = p_icon,
                    sort_order = COALESCE(p_sort_order, 0),
                    is_active = COALESCE(p_is_active, 1),
                    meta_title = p_meta_title,
                    meta_description = p_meta_description,
                    updated_at = NOW()
                WHERE id = p_category_id;

                SET p_result = 'SUCCESS: Categoría actualizada correctamente';
                COMMIT;
            END IF;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Eliminar Categoría (Soft Delete)
-- =====================================================
DROP PROCEDURE IF EXISTS sp_delete_category$$
CREATE PROCEDURE sp_delete_category(
    IN p_category_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_category_exists INT DEFAULT 0;
    DECLARE v_has_children INT DEFAULT 0;
    DECLARE v_has_products INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo eliminar la categoría';
    END;

    START TRANSACTION;

    -- Verificar que la categoría existe
    SELECT COUNT(*) INTO v_category_exists
    FROM categories
    WHERE id = p_category_id AND deleted_at IS NULL;

    IF v_category_exists = 0 THEN
        SET p_result = 'ERROR: Categoría no encontrada';
        ROLLBACK;
    ELSE
        -- Verificar si tiene subcategorías
        SELECT COUNT(*) INTO v_has_children
        FROM categories
        WHERE parent_id = p_category_id AND deleted_at IS NULL;

        -- Verificar si tiene productos
        SELECT COUNT(*) INTO v_has_products
        FROM products
        WHERE category_id = p_category_id AND deleted_at IS NULL;

        IF v_has_children > 0 THEN
            SET p_result = 'ERROR: No se puede eliminar una categoría que tiene subcategorías';
            ROLLBACK;
        ELSEIF v_has_products > 0 THEN
            SET p_result = 'ERROR: No se puede eliminar una categoría que tiene productos asignados';
            ROLLBACK;
        ELSE
            -- Soft delete
            UPDATE categories
            SET deleted_at = NOW()
            WHERE id = p_category_id;

            SET p_result = 'SUCCESS: Categoría eliminada correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Ruta de Categoría (Breadcrumb) - Versión Simple
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_category_breadcrumb$$
CREATE PROCEDURE sp_get_category_breadcrumb(
    IN p_category_id INT
)
BEGIN
    -- Obtener hasta 5 niveles de categorías padre
    SELECT
        c1.id, c1.name, c1.slug, 0 as level
    FROM categories c1
    WHERE c1.id = p_category_id AND c1.deleted_at IS NULL

    UNION ALL

    SELECT
        c2.id, c2.name, c2.slug, 1 as level
    FROM categories c1
    INNER JOIN categories c2 ON c2.id = c1.parent_id
    WHERE c1.id = p_category_id AND c1.deleted_at IS NULL AND c2.deleted_at IS NULL

    UNION ALL

    SELECT
        c3.id, c3.name, c3.slug, 2 as level
    FROM categories c1
    INNER JOIN categories c2 ON c2.id = c1.parent_id
    INNER JOIN categories c3 ON c3.id = c2.parent_id
    WHERE c1.id = p_category_id AND c1.deleted_at IS NULL
      AND c2.deleted_at IS NULL AND c3.deleted_at IS NULL

    ORDER BY level DESC;
END$$

-- =====================================================
-- SISTEMA DE INVENTARIO AVANZADO
-- =====================================================

-- =====================================================
-- SP: Registrar Movimiento de Inventario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_register_inventory_movement$$
CREATE PROCEDURE sp_register_inventory_movement(
    IN p_product_id INT,
    IN p_movement_type ENUM('in', 'out', 'adjustment', 'transfer'),
    IN p_quantity INT,
    IN p_reason VARCHAR(255),
    IN p_reference_type ENUM('order', 'purchase', 'adjustment', 'return', 'damage', 'transfer'),
    IN p_reference_id INT,
    IN p_user_id INT,
    IN p_notes TEXT,
    OUT p_movement_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_current_stock INT DEFAULT 0;
    DECLARE v_new_stock INT DEFAULT 0;
    DECLARE v_product_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo registrar el movimiento de inventario';
        SET p_movement_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el producto existe
    SELECT COUNT(*), stock_quantity INTO v_product_exists, v_current_stock
    FROM products
    WHERE id = p_product_id AND deleted_at IS NULL;

    IF v_product_exists = 0 THEN
        SET p_result = 'ERROR: Producto no encontrado';
        SET p_movement_id = 0;
        ROLLBACK;
    ELSE
        -- Calcular nuevo stock
        IF p_movement_type = 'in' THEN
            SET v_new_stock = v_current_stock + p_quantity;
        ELSEIF p_movement_type = 'out' THEN
            SET v_new_stock = v_current_stock - p_quantity;
            IF v_new_stock < 0 THEN
                SET p_result = 'ERROR: Stock insuficiente';
                SET p_movement_id = 0;
                ROLLBACK;
            END IF;
        ELSEIF p_movement_type = 'adjustment' THEN
            SET v_new_stock = p_quantity; -- p_quantity es el nuevo stock total
        ELSEIF p_movement_type = 'transfer' THEN
            SET v_new_stock = v_current_stock - p_quantity;
            IF v_new_stock < 0 THEN
                SET p_result = 'ERROR: Stock insuficiente para transferencia';
                SET p_movement_id = 0;
                ROLLBACK;
            END IF;
        END IF;

        IF p_movement_id = 0 THEN
            -- No hacer nada, ya hay error
            SELECT 1;
        ELSE
            -- Registrar movimiento
            INSERT INTO inventory_movements (
                uuid, product_id, movement_type, quantity, previous_stock, new_stock,
                reason, reference_type, reference_id, user_id, notes, created_at
            ) VALUES (
                UUID(), p_product_id, p_movement_type, p_quantity, v_current_stock, v_new_stock,
                p_reason, p_reference_type, p_reference_id, p_user_id, p_notes, NOW()
            );

            SET p_movement_id = LAST_INSERT_ID();

            -- Actualizar stock del producto
            UPDATE products
            SET stock_quantity = v_new_stock, updated_at = NOW()
            WHERE id = p_product_id;

            -- Verificar alertas de stock
            CALL sp_check_inventory_alerts(p_product_id, @alert_result);

            -- Crear notificación automática si el stock es bajo
            IF v_new_stock <= 5 THEN
                CALL sp_create_stock_alert_notification(p_product_id, v_new_stock, @notif_result);
            END IF;

            SET p_result = 'SUCCESS: Movimiento registrado correctamente';
            COMMIT;
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Verificar y Crear Alertas de Inventario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_check_inventory_alerts$$
CREATE PROCEDURE sp_check_inventory_alerts(
    IN p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_current_stock INT DEFAULT 0;
    DECLARE v_min_stock INT DEFAULT 5; -- Umbral por defecto
    DECLARE v_max_stock INT DEFAULT 1000; -- Umbral por defecto
    DECLARE v_existing_alert INT DEFAULT 0;

    -- Obtener stock actual
    SELECT stock_quantity INTO v_current_stock
    FROM products
    WHERE id = p_product_id AND deleted_at IS NULL;

    -- Resolver alertas existentes
    UPDATE inventory_alerts
    SET is_resolved = 1, resolved_at = NOW()
    WHERE product_id = p_product_id AND is_active = 1;

    -- Verificar stock bajo
    IF v_current_stock <= v_min_stock THEN
        IF v_current_stock = 0 THEN
            -- Alerta de sin stock
            INSERT INTO inventory_alerts (
                uuid, product_id, alert_type, threshold_value, current_stock,
                is_active, created_at
            ) VALUES (
                UUID(), p_product_id, 'out_of_stock', 0, v_current_stock, 1, NOW()
            );
        ELSE
            -- Alerta de stock bajo
            INSERT INTO inventory_alerts (
                uuid, product_id, alert_type, threshold_value, current_stock,
                is_active, created_at
            ) VALUES (
                UUID(), p_product_id, 'low_stock', v_min_stock, v_current_stock, 1, NOW()
            );
        END IF;
    ELSEIF v_current_stock > v_max_stock THEN
        -- Alerta de sobrestock
        INSERT INTO inventory_alerts (
            uuid, product_id, alert_type, threshold_value, current_stock,
            is_active, created_at
        ) VALUES (
            UUID(), p_product_id, 'overstock', v_max_stock, v_current_stock, 1, NOW()
        );
    END IF;

    SET p_result = 'SUCCESS: Alertas verificadas';
END$$

-- =====================================================
-- SP: Obtener Movimientos de Inventario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_inventory_movements$$
CREATE PROCEDURE sp_get_inventory_movements(
    IN p_product_id INT,
    IN p_movement_type VARCHAR(20),
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT
        im.id,
        im.uuid,
        im.product_id,
        im.movement_type,
        im.quantity,
        im.previous_stock,
        im.new_stock,
        im.reason,
        im.reference_type,
        im.reference_id,
        im.user_id,
        im.notes,
        im.created_at,
        p.name as product_name,
        p.sku as product_sku
    FROM inventory_movements im
    INNER JOIN products p ON p.id = im.product_id
    WHERE (p_product_id IS NULL OR im.product_id = p_product_id)
      AND (p_movement_type IS NULL OR im.movement_type = p_movement_type)
      AND (p_date_from IS NULL OR DATE(im.created_at) >= p_date_from)
      AND (p_date_to IS NULL OR DATE(im.created_at) <= p_date_to)
    ORDER BY im.created_at DESC
    LIMIT 50;
END$$

-- =====================================================
-- SP: Obtener Alertas de Inventario Activas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_inventory_alerts$$
CREATE PROCEDURE sp_get_inventory_alerts(
    IN p_alert_type VARCHAR(20),
    IN p_is_resolved BOOLEAN
)
BEGIN
    SELECT
        ia.*,
        p.name as product_name,
        p.sku as product_sku,
        p.stock_quantity as current_stock_real,
        c.name as category_name
    FROM inventory_alerts ia
    INNER JOIN products p ON p.id = ia.product_id
    LEFT JOIN categories c ON c.id = p.category_id
    WHERE (p_alert_type IS NULL OR ia.alert_type = p_alert_type)
      AND (p_is_resolved IS NULL OR ia.is_resolved = p_is_resolved)
      AND ia.is_active = 1
      AND p.deleted_at IS NULL
    ORDER BY ia.created_at DESC;
END$$

-- =====================================================
-- SISTEMA DE GESTIÓN DE IMÁGENES
-- =====================================================

-- =====================================================
-- SP: Subir Archivo de Media
-- =====================================================
DROP PROCEDURE IF EXISTS sp_upload_media_file$$
CREATE PROCEDURE sp_upload_media_file(
    IN p_filename VARCHAR(255),
    IN p_original_filename VARCHAR(255),
    IN p_file_path VARCHAR(500),
    IN p_file_url VARCHAR(500),
    IN p_file_size INT,
    IN p_mime_type VARCHAR(100),
    IN p_file_type ENUM('image', 'video', 'document', 'other'),
    IN p_width INT,
    IN p_height INT,
    IN p_alt_text VARCHAR(255),
    IN p_title VARCHAR(255),
    IN p_description TEXT,
    OUT p_media_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo subir el archivo';
        SET p_media_id = 0;
    END;

    START TRANSACTION;

    INSERT INTO media_files (
        uuid, filename, original_filename, file_path, file_url, file_size,
        mime_type, file_type, width, height, alt_text, title, description,
        is_active, created_at
    ) VALUES (
        UUID(), p_filename, p_original_filename, p_file_path, p_file_url, p_file_size,
        p_mime_type, p_file_type, p_width, p_height, p_alt_text, p_title, p_description,
        1, NOW()
    );

    SET p_media_id = LAST_INSERT_ID();
    SET p_result = 'SUCCESS: Archivo subido correctamente';
    COMMIT;
END$$

-- =====================================================
-- SP: Asociar Media con Entidad
-- =====================================================
DROP PROCEDURE IF EXISTS sp_associate_media$$
CREATE PROCEDURE sp_associate_media(
    IN p_media_id INT,
    IN p_entity_type ENUM('product', 'category', 'user', 'order', 'other'),
    IN p_entity_id INT,
    IN p_relation_type ENUM('featured', 'gallery', 'thumbnail', 'icon', 'other'),
    IN p_sort_order INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_media_exists INT DEFAULT 0;
    DECLARE v_existing_featured INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo asociar el archivo';
    END;

    START TRANSACTION;

    -- Verificar que el archivo existe
    SELECT COUNT(*) INTO v_media_exists
    FROM media_files
    WHERE id = p_media_id AND is_active = 1 AND deleted_at IS NULL;

    IF v_media_exists = 0 THEN
        SET p_result = 'ERROR: Archivo no encontrado';
        ROLLBACK;
    ELSE
        -- Si es imagen destacada, remover la anterior
        IF p_relation_type = 'featured' THEN
            DELETE FROM media_relations
            WHERE entity_type = p_entity_type
              AND entity_id = p_entity_id
              AND relation_type = 'featured';
        END IF;

        -- Insertar nueva relación
        INSERT INTO media_relations (
            media_id, entity_type, entity_id, relation_type, sort_order, created_at
        ) VALUES (
            p_media_id, p_entity_type, p_entity_id, p_relation_type,
            COALESCE(p_sort_order, 0), NOW()
        ) ON DUPLICATE KEY UPDATE
            sort_order = COALESCE(p_sort_order, sort_order),
            created_at = NOW();

        SET p_result = 'SUCCESS: Archivo asociado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Media de Entidad
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_entity_media$$
CREATE PROCEDURE sp_get_entity_media(
    IN p_entity_type ENUM('product', 'category', 'user', 'order', 'other'),
    IN p_entity_id INT,
    IN p_relation_type VARCHAR(20)
)
BEGIN
    SELECT
        mf.*,
        mr.relation_type,
        mr.sort_order
    FROM media_files mf
    INNER JOIN media_relations mr ON mr.media_id = mf.id
    WHERE mr.entity_type = p_entity_type
      AND mr.entity_id = p_entity_id
      AND (p_relation_type IS NULL OR mr.relation_type = p_relation_type)
      AND mf.is_active = 1
      AND mf.deleted_at IS NULL
    ORDER BY
        CASE mr.relation_type
            WHEN 'featured' THEN 1
            WHEN 'thumbnail' THEN 2
            WHEN 'gallery' THEN 3
            ELSE 4
        END,
        mr.sort_order,
        mf.created_at;
END$$

-- =====================================================
-- SP: Eliminar Media (Soft Delete)
-- =====================================================
DROP PROCEDURE IF EXISTS sp_delete_media$$
CREATE PROCEDURE sp_delete_media(
    IN p_media_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_media_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo eliminar el archivo';
    END;

    START TRANSACTION;

    -- Verificar que el archivo existe
    SELECT COUNT(*) INTO v_media_exists
    FROM media_files
    WHERE id = p_media_id AND deleted_at IS NULL;

    IF v_media_exists = 0 THEN
        SET p_result = 'ERROR: Archivo no encontrado';
        ROLLBACK;
    ELSE
        -- Soft delete del archivo
        UPDATE media_files
        SET deleted_at = NOW(), is_active = 0
        WHERE id = p_media_id;

        -- Eliminar relaciones
        DELETE FROM media_relations
        WHERE media_id = p_media_id;

        SET p_result = 'SUCCESS: Archivo eliminado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Galería de Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_product_gallery$$
CREATE PROCEDURE sp_get_product_gallery(
    IN p_product_id INT
)
BEGIN
    SELECT
        mf.id,
        mf.uuid,
        mf.filename,
        mf.file_url,
        mf.alt_text,
        mf.title,
        mf.width,
        mf.height,
        mr.relation_type,
        mr.sort_order,
        CASE
            WHEN mr.relation_type = 'featured' THEN 1
            ELSE 0
        END as is_featured
    FROM media_files mf
    INNER JOIN media_relations mr ON mr.media_id = mf.id
    WHERE mr.entity_type = 'product'
      AND mr.entity_id = p_product_id
      AND mf.is_active = 1
      AND mf.deleted_at IS NULL
    ORDER BY is_featured DESC, mr.sort_order, mf.created_at;
END$$

-- =====================================================
-- SP: Actualizar Orden de Imágenes
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_media_order$$
CREATE PROCEDURE sp_update_media_order(
    IN p_media_ids TEXT,
    IN p_entity_type ENUM('product', 'category', 'user', 'order', 'other'),
    IN p_entity_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_pos INT DEFAULT 1;
    DECLARE v_media_id INT;
    DECLARE v_done INT DEFAULT FALSE;
    DECLARE v_remaining TEXT DEFAULT p_media_ids;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el orden';
    END;

    START TRANSACTION;

    -- Procesar lista de IDs separados por coma
    WHILE LENGTH(v_remaining) > 0 DO
        SET v_media_id = CAST(SUBSTRING_INDEX(v_remaining, ',', 1) AS UNSIGNED);

        UPDATE media_relations
        SET sort_order = v_pos
        WHERE media_id = v_media_id
          AND entity_type = p_entity_type
          AND entity_id = p_entity_id;

        SET v_pos = v_pos + 1;

        IF LOCATE(',', v_remaining) > 0 THEN
            SET v_remaining = SUBSTRING(v_remaining, LOCATE(',', v_remaining) + 1);
        ELSE
            SET v_remaining = '';
        END IF;
    END WHILE;

    SET p_result = 'SUCCESS: Orden actualizado correctamente';
    COMMIT;
END$$

-- =====================================================
-- SISTEMA DE REPORTES AVANZADOS
-- =====================================================

-- =====================================================
-- SP: Reporte de Ventas por Período
-- =====================================================
DROP PROCEDURE IF EXISTS sp_sales_report_by_period$$
CREATE PROCEDURE sp_sales_report_by_period(
    IN p_period_type ENUM('daily', 'weekly', 'monthly', 'yearly'),
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_category_id INT,
    IN p_product_id INT
)
BEGIN
    DECLARE v_date_from DATE DEFAULT COALESCE(p_date_from, CURDATE() - INTERVAL 30 DAY);
    DECLARE v_date_to DATE DEFAULT COALESCE(p_date_to, CURDATE());

    IF p_period_type = 'daily' THEN
        SELECT
            DATE(o.created_at) as period_date,
            DATE_FORMAT(o.created_at, '%Y-%m-%d') as period_label,
            COUNT(DISTINCT o.id) as total_orders,
            COUNT(DISTINCT oi.product_id) as unique_products,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.quantity * oi.price) as total_revenue,
            AVG(o.total) as avg_order_value,
            SUM(o.shipping_cost) as total_shipping
        FROM orders o
        INNER JOIN order_items oi ON oi.order_id = o.id
        LEFT JOIN products p ON p.id = oi.product_id
        WHERE DATE(o.created_at) BETWEEN v_date_from AND v_date_to
          AND (p_category_id IS NULL OR p.category_id = p_category_id)
          AND (p_product_id IS NULL OR oi.product_id = p_product_id)
        GROUP BY DATE(o.created_at)
        ORDER BY period_date DESC;

    ELSEIF p_period_type = 'weekly' THEN
        SELECT
            YEARWEEK(o.created_at, 1) as period_week,
            CONCAT('Semana ', WEEK(o.created_at, 1), ' - ', YEAR(o.created_at)) as period_label,
            DATE(MIN(o.created_at)) as week_start,
            DATE(MAX(o.created_at)) as week_end,
            COUNT(DISTINCT o.id) as total_orders,
            COUNT(DISTINCT oi.product_id) as unique_products,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.quantity * oi.price) as total_revenue,
            AVG(o.total) as avg_order_value,
            SUM(o.shipping_cost) as total_shipping
        FROM orders o
        INNER JOIN order_items oi ON oi.order_id = o.id
        LEFT JOIN products p ON p.id = oi.product_id
        WHERE DATE(o.created_at) BETWEEN v_date_from AND v_date_to
          AND (p_category_id IS NULL OR p.category_id = p_category_id)
          AND (p_product_id IS NULL OR oi.product_id = p_product_id)
        GROUP BY YEARWEEK(o.created_at, 1)
        ORDER BY period_week DESC;

    ELSEIF p_period_type = 'monthly' THEN
        SELECT
            DATE_FORMAT(o.created_at, '%Y-%m') as period_month,
            DATE_FORMAT(o.created_at, '%M %Y') as period_label,
            COUNT(DISTINCT o.id) as total_orders,
            COUNT(DISTINCT oi.product_id) as unique_products,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.quantity * oi.price) as total_revenue,
            AVG(o.total) as avg_order_value,
            SUM(o.shipping_cost) as total_shipping
        FROM orders o
        INNER JOIN order_items oi ON oi.order_id = o.id
        LEFT JOIN products p ON p.id = oi.product_id
        WHERE DATE(o.created_at) BETWEEN v_date_from AND v_date_to
          AND (p_category_id IS NULL OR p.category_id = p_category_id)
          AND (p_product_id IS NULL OR oi.product_id = p_product_id)
        GROUP BY DATE_FORMAT(o.created_at, '%Y-%m')
        ORDER BY period_month DESC;

    ELSEIF p_period_type = 'yearly' THEN
        SELECT
            YEAR(o.created_at) as period_year,
            CONCAT('Año ', YEAR(o.created_at)) as period_label,
            COUNT(DISTINCT o.id) as total_orders,
            COUNT(DISTINCT oi.product_id) as unique_products,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.quantity * oi.price) as total_revenue,
            AVG(o.total) as avg_order_value,
            SUM(o.shipping_cost) as total_shipping
        FROM orders o
        INNER JOIN order_items oi ON oi.order_id = o.id
        LEFT JOIN products p ON p.id = oi.product_id
        WHERE DATE(o.created_at) BETWEEN v_date_from AND v_date_to
          AND (p_category_id IS NULL OR p.category_id = p_category_id)
          AND (p_product_id IS NULL OR oi.product_id = p_product_id)
        GROUP BY YEAR(o.created_at)
        ORDER BY period_year DESC;
    END IF;
END$$

-- =====================================================
-- SP: Top Productos Más Vendidos
-- =====================================================
DROP PROCEDURE IF EXISTS sp_top_selling_products_report$$
CREATE PROCEDURE sp_top_selling_products_report(
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_category_id INT,
    IN p_limit INT
)
BEGIN
    DECLARE v_date_from DATE DEFAULT COALESCE(p_date_from, CURDATE() - INTERVAL 30 DAY);
    DECLARE v_date_to DATE DEFAULT COALESCE(p_date_to, CURDATE());
    DECLARE v_limit INT DEFAULT COALESCE(p_limit, 20);

    SELECT
        p.id,
        p.name as product_name,
        p.sku,
        p.price_regular,
        p.price_sale,
        c.name as category_name,
        SUM(oi.quantity) as total_sold,
        COUNT(DISTINCT oi.order_id) as orders_count,
        SUM(oi.quantity * oi.price) as total_revenue,
        AVG(oi.price) as avg_selling_price,
        p.stock_quantity as current_stock,
        ROUND((SUM(oi.quantity * oi.price) /
               (SELECT SUM(oi2.quantity * oi2.price)
                FROM order_items oi2
                INNER JOIN orders o2 ON o2.id = oi2.order_id
                WHERE DATE(o2.created_at) BETWEEN v_date_from AND v_date_to)) * 100, 2) as revenue_percentage
    FROM products p
    INNER JOIN order_items oi ON oi.product_id = p.id
    INNER JOIN orders o ON o.id = oi.order_id
    LEFT JOIN categories c ON c.id = p.category_id
    WHERE DATE(o.created_at) BETWEEN v_date_from AND v_date_to
      AND (p_category_id IS NULL OR p.category_id = p_category_id)
      AND p.deleted_at IS NULL
    GROUP BY p.id, p.name, p.sku, p.price_regular, p.price_sale, c.name, p.stock_quantity
    ORDER BY total_sold DESC, total_revenue DESC
    LIMIT 50;
END$$

-- =====================================================
-- SP: Reporte de Inventario Detallado
-- =====================================================
DROP PROCEDURE IF EXISTS sp_inventory_detailed_report$$
CREATE PROCEDURE sp_inventory_detailed_report(
    IN p_category_id INT,
    IN p_stock_status ENUM('all', 'low', 'out', 'normal'),
    IN p_sort_by ENUM('name', 'stock', 'value', 'category')
)
BEGIN
    DECLARE v_sort_clause VARCHAR(100) DEFAULT 'p.name';

    -- Determinar ordenamiento
    IF p_sort_by = 'stock' THEN
        SET v_sort_clause = 'p.stock_quantity';
    ELSEIF p_sort_by = 'value' THEN
        SET v_sort_clause = 'inventory_value DESC';
    ELSEIF p_sort_by = 'category' THEN
        SET v_sort_clause = 'c.name, p.name';
    END IF;

    SELECT
        p.id,
        p.name as product_name,
        p.sku,
        p.stock_quantity,
        p.price_regular,
        p.price_sale,
        (p.stock_quantity * p.price_regular) as inventory_value,
        c.name as category_name,
        CASE
            WHEN p.stock_quantity = 0 THEN 'Sin Stock'
            WHEN p.stock_quantity <= 5 THEN 'Stock Bajo'
            WHEN p.stock_quantity <= 20 THEN 'Stock Normal'
            ELSE 'Stock Alto'
        END as stock_status,
        (SELECT COUNT(*) FROM inventory_movements im WHERE im.product_id = p.id AND DATE(im.created_at) >= CURDATE() - INTERVAL 30 DAY) as movements_30d,
        (SELECT SUM(oi.quantity) FROM order_items oi INNER JOIN orders o ON o.id = oi.order_id WHERE oi.product_id = p.id AND DATE(o.created_at) >= CURDATE() - INTERVAL 30 DAY) as sold_30d,
        p.created_at,
        p.updated_at
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id
    WHERE p.deleted_at IS NULL
      AND p.is_active = 1
      AND (p_category_id IS NULL OR p.category_id = p_category_id)
      AND (p_stock_status = 'all' OR
           (p_stock_status = 'out' AND p.stock_quantity = 0) OR
           (p_stock_status = 'low' AND p.stock_quantity > 0 AND p.stock_quantity <= 5) OR
           (p_stock_status = 'normal' AND p.stock_quantity > 5))
    ORDER BY
        CASE
            WHEN p_sort_by = 'name' THEN p.name
            WHEN p_sort_by = 'category' THEN c.name
            ELSE NULL
        END,
        CASE
            WHEN p_sort_by = 'stock' THEN p.stock_quantity
            WHEN p_sort_by = 'value' THEN (p.stock_quantity * p.price_regular)
            ELSE NULL
        END DESC;
END$$

-- =====================================================
-- SISTEMA DE NOTIFICACIONES EN TIEMPO REAL
-- =====================================================

-- =====================================================
-- SP: Crear Notificación
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_notification$$
CREATE PROCEDURE sp_create_notification(
    IN p_user_id INT,
    IN p_type ENUM('info', 'success', 'warning', 'error', 'stock_alert', 'order_alert', 'system'),
    IN p_title VARCHAR(255),
    IN p_message TEXT,
    IN p_data JSON,
    IN p_action_url VARCHAR(500),
    IN p_action_text VARCHAR(100),
    IN p_is_global BOOLEAN,
    IN p_priority ENUM('low', 'normal', 'high', 'urgent'),
    IN p_expires_at DATETIME,
    OUT p_notification_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo crear la notificación';
        SET p_notification_id = 0;
    END;

    START TRANSACTION;

    INSERT INTO notifications (
        uuid, user_id, type, title, message, data, action_url, action_text,
        is_global, priority, expires_at, created_at
    ) VALUES (
        UUID(), p_user_id, p_type, p_title, p_message, p_data, p_action_url, p_action_text,
        COALESCE(p_is_global, 0), COALESCE(p_priority, 'normal'), p_expires_at, NOW()
    );

    SET p_notification_id = LAST_INSERT_ID();
    SET p_result = 'SUCCESS: Notificación creada correctamente';
    COMMIT;
END$$

-- =====================================================
-- SP: Obtener Notificaciones de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_notifications$$
CREATE PROCEDURE sp_get_user_notifications(
    IN p_user_id INT,
    IN p_is_read BOOLEAN,
    IN p_type VARCHAR(20),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT
        n.*,
        CASE
            WHEN n.priority = 'urgent' THEN 4
            WHEN n.priority = 'high' THEN 3
            WHEN n.priority = 'normal' THEN 2
            ELSE 1
        END as priority_order
    FROM notifications n
    WHERE (n.user_id = p_user_id OR n.is_global = 1)
      AND (p_is_read IS NULL OR n.is_read = p_is_read)
      AND (p_type IS NULL OR n.type = p_type)
      AND (n.expires_at IS NULL OR n.expires_at > NOW())
      AND n.deleted_at IS NULL
    ORDER BY priority_order DESC, n.created_at DESC
    LIMIT 50 OFFSET 0;
END$$

-- =====================================================
-- SP: Marcar Notificación como Leída
-- =====================================================
DROP PROCEDURE IF EXISTS sp_mark_notification_read$$
CREATE PROCEDURE sp_mark_notification_read(
    IN p_notification_id INT,
    IN p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_notification_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo marcar la notificación como leída';
    END;

    START TRANSACTION;

    -- Verificar que la notificación existe y pertenece al usuario
    SELECT COUNT(*) INTO v_notification_exists
    FROM notifications
    WHERE id = p_notification_id
      AND (user_id = p_user_id OR is_global = 1)
      AND deleted_at IS NULL;

    IF v_notification_exists = 0 THEN
        SET p_result = 'ERROR: Notificación no encontrada';
        ROLLBACK;
    ELSE
        UPDATE notifications
        SET is_read = 1, read_at = NOW()
        WHERE id = p_notification_id;

        SET p_result = 'SUCCESS: Notificación marcada como leída';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Marcar Todas las Notificaciones como Leídas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_mark_all_notifications_read$$
CREATE PROCEDURE sp_mark_all_notifications_read(
    IN p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudieron marcar las notificaciones como leídas';
    END;

    START TRANSACTION;

    UPDATE notifications
    SET is_read = 1, read_at = NOW()
    WHERE (user_id = p_user_id OR is_global = 1)
      AND is_read = 0
      AND deleted_at IS NULL;

    SET p_result = 'SUCCESS: Todas las notificaciones marcadas como leídas';
    COMMIT;
END$$

-- =====================================================
-- SP: Eliminar Notificación
-- =====================================================
DROP PROCEDURE IF EXISTS sp_delete_notification$$
CREATE PROCEDURE sp_delete_notification(
    IN p_notification_id INT,
    IN p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_notification_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo eliminar la notificación';
    END;

    START TRANSACTION;

    -- Verificar que la notificación existe y pertenece al usuario
    SELECT COUNT(*) INTO v_notification_exists
    FROM notifications
    WHERE id = p_notification_id
      AND (user_id = p_user_id OR is_global = 1)
      AND deleted_at IS NULL;

    IF v_notification_exists = 0 THEN
        SET p_result = 'ERROR: Notificación no encontrada';
        ROLLBACK;
    ELSE
        UPDATE notifications
        SET deleted_at = NOW()
        WHERE id = p_notification_id;

        SET p_result = 'SUCCESS: Notificación eliminada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Estadísticas de Notificaciones
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_notification_stats$$
CREATE PROCEDURE sp_get_notification_stats(
    IN p_user_id INT
)
BEGIN
    SELECT
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count,
        COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count,
        COUNT(CASE WHEN priority = 'urgent' AND is_read = 0 THEN 1 END) as urgent_unread,
        COUNT(CASE WHEN priority = 'high' AND is_read = 0 THEN 1 END) as high_unread,
        COUNT(CASE WHEN type = 'stock_alert' AND is_read = 0 THEN 1 END) as stock_alerts,
        COUNT(CASE WHEN type = 'order_alert' AND is_read = 0 THEN 1 END) as order_alerts
    FROM notifications
    WHERE (user_id = p_user_id OR is_global = 1)
      AND (expires_at IS NULL OR expires_at > NOW())
      AND deleted_at IS NULL;
END$$

-- =====================================================
-- SP: Crear Notificación de Stock Bajo Automática
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_stock_alert_notification$$
CREATE PROCEDURE sp_create_stock_alert_notification(
    IN p_product_id INT,
    IN p_current_stock INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_product_name VARCHAR(255);
    DECLARE v_product_sku VARCHAR(100);
    DECLARE v_existing_alert INT DEFAULT 0;

    -- Obtener información del producto
    SELECT name, sku INTO v_product_name, v_product_sku
    FROM products
    WHERE id = p_product_id AND deleted_at IS NULL;

    IF v_product_name IS NULL THEN
        SET p_result = 'ERROR: Producto no encontrado';
    ELSE
        -- Verificar si ya existe una alerta similar reciente (últimas 24 horas)
        SELECT COUNT(*) INTO v_existing_alert
        FROM notifications
        WHERE type = 'stock_alert'
          AND JSON_EXTRACT(data, '$.product_id') = p_product_id
          AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
          AND deleted_at IS NULL;

        IF v_existing_alert = 0 THEN
            -- Crear notificación para todos los administradores
            INSERT INTO notifications (
                uuid, user_id, type, title, message, data, action_url, action_text,
                is_global, priority, created_at
            ) VALUES (
                UUID(), NULL, 'stock_alert',
                CONCAT('Stock Bajo: ', v_product_name),
                CONCAT('El producto ', v_product_name, ' (', v_product_sku, ') tiene stock bajo: ', p_current_stock, ' unidades'),
                JSON_OBJECT('product_id', p_product_id, 'current_stock', p_current_stock, 'product_name', v_product_name, 'product_sku', v_product_sku),
                CONCAT('/admin/products/', p_product_id),
                'Ver Producto',
                1, 'high', NOW()
            );

            SET p_result = 'SUCCESS: Alerta de stock creada';
        ELSE
            SET p_result = 'INFO: Alerta ya existe';
        END IF;
    END IF;
END$$

-- =====================================================
-- SP: Crear Notificación de Nuevo Pedido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_order_notification$$
CREATE PROCEDURE sp_create_order_notification(
    IN p_order_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_order_number VARCHAR(50);
    DECLARE v_customer_name VARCHAR(255);
    DECLARE v_total DECIMAL(10,2);

    -- Obtener información del pedido
    SELECT order_number, customer_name, total
    INTO v_order_number, v_customer_name, v_total
    FROM orders
    WHERE id = p_order_id;

    IF v_order_number IS NULL THEN
        SET p_result = 'ERROR: Pedido no encontrado';
    ELSE
        -- Crear notificación para todos los administradores
        INSERT INTO notifications (
            uuid, user_id, type, title, message, data, action_url, action_text,
            is_global, priority, created_at
        ) VALUES (
            UUID(), NULL, 'order_alert',
            CONCAT('Nuevo Pedido: #', v_order_number),
            CONCAT('Nuevo pedido de ', v_customer_name, ' por Q', FORMAT(v_total, 2)),
            JSON_OBJECT('order_id', p_order_id, 'order_number', v_order_number, 'customer_name', v_customer_name, 'total', v_total),
            CONCAT('/admin/orders/', p_order_id),
            'Ver Pedido',
            1, 'normal', NOW()
        );

        SET p_result = 'SUCCESS: Notificación de pedido creada';
    END IF;
END$$

-- =====================================================
-- SP: Limpiar Notificaciones Expiradas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_cleanup_expired_notifications$$
CREATE PROCEDURE sp_cleanup_expired_notifications(
    OUT p_deleted_count INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudieron limpiar las notificaciones';
        SET p_deleted_count = 0;
    END;

    START TRANSACTION;

    -- Eliminar notificaciones expiradas
    UPDATE notifications
    SET deleted_at = NOW()
    WHERE expires_at IS NOT NULL
      AND expires_at < NOW()
      AND deleted_at IS NULL;

    SET p_deleted_count = ROW_COUNT();

    -- También eliminar notificaciones leídas muy antiguas (más de 30 días)
    UPDATE notifications
    SET deleted_at = NOW()
    WHERE is_read = 1
      AND read_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND deleted_at IS NULL;

    SET p_deleted_count = p_deleted_count + ROW_COUNT();

    SET p_result = CONCAT('SUCCESS: ', p_deleted_count, ' notificaciones limpiadas');
    COMMIT;
END$$

-- =====================================================
-- SP: Obtener Resumen de Notificaciones por Tipo
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_notifications_summary$$
CREATE PROCEDURE sp_get_notifications_summary()
BEGIN
    SELECT
        type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count,
        COUNT(CASE WHEN priority = 'urgent' AND is_read = 0 THEN 1 END) as urgent_unread,
        COUNT(CASE WHEN priority = 'high' AND is_read = 0 THEN 1 END) as high_unread,
        MIN(created_at) as oldest_notification,
        MAX(created_at) as newest_notification
    FROM notifications
    WHERE deleted_at IS NULL
      AND (expires_at IS NULL OR expires_at > NOW())
    GROUP BY type
    ORDER BY unread_count DESC, total_count DESC;
END$$

-- =====================================================
-- SISTEMA DE AUTENTICACIÓN DE ADMINISTRADORES
-- =====================================================

-- =====================================================
-- SP: Autenticación de Administrador
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_authenticate$$
CREATE PROCEDURE sp_admin_authenticate(
    IN p_email VARCHAR(100),
    IN p_password VARCHAR(255),
    OUT p_admin_id INT,
    OUT p_admin_data JSON,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_admin_count INT DEFAULT 0;
    DECLARE v_stored_password VARCHAR(255);
    DECLARE v_is_active BOOLEAN DEFAULT FALSE;
    DECLARE v_bloqueado_hasta DATETIME;
    DECLARE v_intentos_login INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_admin_id = 0;
        SET p_admin_data = NULL;
        SET p_result = 'ERROR: Error en la autenticación';
    END;

    START TRANSACTION;

    -- Verificar si el administrador existe
    SELECT COUNT(*), password, is_active, bloqueado_hasta, intentos_login
    INTO v_admin_count, v_stored_password, v_is_active, v_bloqueado_hasta, v_intentos_login
    FROM administradores
    WHERE email = p_email AND deleted_at IS NULL;

    IF v_admin_count = 0 THEN
        SET p_admin_id = 0;
        SET p_admin_data = NULL;
        SET p_result = 'ERROR: Credenciales incorrectas';
        ROLLBACK;
    ELSEIF NOT v_is_active THEN
        SET p_admin_id = 0;
        SET p_admin_data = NULL;
        SET p_result = 'ERROR: Cuenta de administrador inactiva';
        ROLLBACK;
    ELSEIF v_bloqueado_hasta IS NOT NULL AND v_bloqueado_hasta > NOW() THEN
        SET p_admin_id = 0;
        SET p_admin_data = NULL;
        SET p_result = 'ERROR: Cuenta temporalmente bloqueada';
        ROLLBACK;
    ELSE
        -- Verificar contraseña (esto debe hacerse en PHP)
        -- Aquí solo obtenemos los datos del admin para verificación externa
        SELECT
            id,
            JSON_OBJECT(
                'id', id,
                'uuid', uuid,
                'nombre', nombre,
                'email', email,
                'rol_admin', rol_admin,
                'ultimo_login', ultimo_login,
                'session_timeout', session_timeout
            )
        INTO p_admin_id, p_admin_data
        FROM administradores
        WHERE email = p_email AND deleted_at IS NULL;

        SET p_result = 'VERIFY_PASSWORD';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Login Exitoso de Administrador
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_login_success$$
CREATE PROCEDURE sp_admin_login_success(
    IN p_admin_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el login';
    END;

    START TRANSACTION;

    UPDATE administradores
    SET ultimo_login = NOW(),
        intentos_login = 0,
        bloqueado_hasta = NULL
    WHERE id = p_admin_id;

    SET p_result = 'SUCCESS: Login actualizado';
    COMMIT;
END$$

-- =====================================================
-- SP: Registrar Intento de Login Fallido
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_login_failed$$
CREATE PROCEDURE sp_admin_login_failed(
    IN p_email VARCHAR(100),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_intentos INT DEFAULT 0;
    DECLARE v_admin_id INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo registrar el intento fallido';
    END;

    START TRANSACTION;

    -- Obtener intentos actuales
    SELECT id, intentos_login INTO v_admin_id, v_intentos
    FROM administradores
    WHERE email = p_email AND deleted_at IS NULL;

    IF v_admin_id > 0 THEN
        SET v_intentos = v_intentos + 1;

        -- Si supera 3 intentos, bloquear por 30 minutos
        IF v_intentos >= 3 THEN
            UPDATE administradores
            SET intentos_login = v_intentos,
                bloqueado_hasta = DATE_ADD(NOW(), INTERVAL 30 MINUTE)
            WHERE id = v_admin_id;
            SET p_result = 'BLOCKED: Cuenta bloqueada por 30 minutos';
        ELSE
            UPDATE administradores
            SET intentos_login = v_intentos
            WHERE id = v_admin_id;
            SET p_result = CONCAT('FAILED: Intento ', v_intentos, ' de 3');
        END IF;
    ELSE
        SET p_result = 'ERROR: Administrador no encontrado';
    END IF;

    COMMIT;
END$$

-- =====================================================
-- SISTEMA DE GESTIÓN AVANZADA DE USUARIOS Y ROLES
-- =====================================================

-- =====================================================
-- SP: Asignar Rol a Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_assign_role_to_user$$
CREATE PROCEDURE sp_assign_role_to_user(
    IN p_user_id INT,
    IN p_role_id INT,
    IN p_assigned_by INT,
    IN p_expires_at DATETIME,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_user_exists INT DEFAULT 0;
    DECLARE v_role_exists INT DEFAULT 0;
    DECLARE v_existing_assignment INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo asignar el rol';
    END;

    START TRANSACTION;

    -- Verificar que el usuario existe
    SELECT COUNT(*) INTO v_user_exists
    FROM users
    WHERE id = p_user_id AND deleted_at IS NULL;

    -- Verificar que el rol existe
    SELECT COUNT(*) INTO v_role_exists
    FROM roles
    WHERE id = p_role_id AND deleted_at IS NULL AND is_active = 1;

    -- Verificar si ya existe la asignación
    SELECT COUNT(*) INTO v_existing_assignment
    FROM user_roles
    WHERE user_id = p_user_id AND role_id = p_role_id AND is_active = 1;

    IF v_user_exists = 0 THEN
        SET p_result = 'ERROR: Usuario no encontrado';
        ROLLBACK;
    ELSEIF v_role_exists = 0 THEN
        SET p_result = 'ERROR: Rol no encontrado o inactivo';
        ROLLBACK;
    ELSEIF v_existing_assignment > 0 THEN
        SET p_result = 'ERROR: El usuario ya tiene este rol asignado';
        ROLLBACK;
    ELSE
        INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at, expires_at)
        VALUES (p_user_id, p_role_id, p_assigned_by, NOW(), p_expires_at);

        -- Registrar actividad
        INSERT INTO user_activity_log (user_id, action, module, description, created_at)
        VALUES (p_assigned_by, 'assign_role', 'users',
                CONCAT('Asignó rol ID ', p_role_id, ' al usuario ID ', p_user_id), NOW());

        SET p_result = 'SUCCESS: Rol asignado correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Remover Rol de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_remove_role_from_user$$
CREATE PROCEDURE sp_remove_role_from_user(
    IN p_user_id INT,
    IN p_role_id INT,
    IN p_removed_by INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_assignment_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo remover el rol';
    END;

    START TRANSACTION;

    -- Verificar que existe la asignación
    SELECT COUNT(*) INTO v_assignment_exists
    FROM user_roles
    WHERE user_id = p_user_id AND role_id = p_role_id AND is_active = 1;

    IF v_assignment_exists = 0 THEN
        SET p_result = 'ERROR: Asignación de rol no encontrada';
        ROLLBACK;
    ELSE
        UPDATE user_roles
        SET is_active = 0
        WHERE user_id = p_user_id AND role_id = p_role_id;

        -- Registrar actividad
        INSERT INTO user_activity_log (user_id, action, module, description, created_at)
        VALUES (p_removed_by, 'remove_role', 'users',
                CONCAT('Removió rol ID ', p_role_id, ' del usuario ID ', p_user_id), NOW());

        SET p_result = 'SUCCESS: Rol removido correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Roles de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_roles$$
CREATE PROCEDURE sp_get_user_roles(
    IN p_user_id INT
)
BEGIN
    SELECT
        r.id,
        r.uuid,
        r.name,
        r.slug,
        r.description,
        r.level,
        r.color,
        r.icon,
        ur.assigned_at,
        ur.expires_at,
        ur.is_active,
        CASE
            WHEN ur.expires_at IS NOT NULL AND ur.expires_at < NOW() THEN 1
            ELSE 0
        END as is_expired
    FROM user_roles ur
    INNER JOIN roles r ON r.id = ur.role_id
    WHERE ur.user_id = p_user_id
    ORDER BY r.level DESC, ur.assigned_at DESC;
END$$

-- =====================================================
-- SP: Verificar Permisos de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_check_user_permission$$
CREATE PROCEDURE sp_check_user_permission(
    IN p_user_id INT,
    IN p_module VARCHAR(50),
    IN p_action VARCHAR(50),
    IN p_resource VARCHAR(50),
    OUT p_has_permission BOOLEAN
)
BEGIN
    DECLARE v_permission_count INT DEFAULT 0;

    -- Verificar si el usuario tiene permisos a través de sus roles activos
    SELECT COUNT(*) INTO v_permission_count
    FROM user_roles ur
    INNER JOIN roles r ON r.id = ur.role_id
    INNER JOIN role_permissions rp ON rp.role_id = r.id
    INNER JOIN permissions p ON p.id = rp.permission_id
    WHERE ur.user_id = p_user_id
      AND ur.is_active = 1
      AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      AND r.is_active = 1
      AND r.deleted_at IS NULL
      AND p.module = p_module
      AND p.action = p_action
      AND (p_resource IS NULL OR p.resource = p_resource)
      AND p.is_active = 1
      AND p.deleted_at IS NULL;

    SET p_has_permission = (v_permission_count > 0);
END$$

-- =====================================================
-- SP: Registrar Actividad de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_log_user_activity$$
CREATE PROCEDURE sp_log_user_activity(
    IN p_user_id INT,
    IN p_action VARCHAR(100),
    IN p_module VARCHAR(50),
    IN p_resource_type VARCHAR(50),
    IN p_resource_id INT,
    IN p_description TEXT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    IN p_session_id VARCHAR(128),
    IN p_data JSON
)
BEGIN
    INSERT INTO user_activity_log (
        user_id, action, module, resource_type, resource_id, description,
        ip_address, user_agent, session_id, data, created_at
    ) VALUES (
        p_user_id, p_action, p_module, p_resource_type, p_resource_id, p_description,
        p_ip_address, p_user_agent, p_session_id, p_data, NOW()
    );
END$$

-- =====================================================
-- SP: Obtener Actividad de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_user_activity$$
CREATE PROCEDURE sp_get_user_activity(
    IN p_user_id INT,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT
        ual.*,
        CONCAT(u.first_name, ' ', u.last_name) as user_name,
        u.email as user_email,
        u.username
    FROM user_activity_log ual
    LEFT JOIN users u ON u.id = ual.user_id
    WHERE (p_user_id IS NULL OR ual.user_id = p_user_id)
    ORDER BY ual.created_at DESC
    LIMIT 50;
END$$

-- =====================================================
-- FRONTEND E-COMMERCE PARA CLIENTES
-- =====================================================

-- =====================================================
-- SP: Obtener Catálogo de Productos Público
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_public_product_catalog$$
CREATE PROCEDURE sp_get_public_product_catalog(
    IN p_category_id INT,
    IN p_search_term VARCHAR(255),
    IN p_min_price DECIMAL(10,2),
    IN p_max_price DECIMAL(10,2),
    IN p_sort_by ENUM('name', 'price_asc', 'price_desc', 'newest', 'popular'),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    DECLARE v_sort_clause VARCHAR(100) DEFAULT 'p.created_at DESC';

    -- Determinar ordenamiento
    IF p_sort_by = 'name' THEN
        SET v_sort_clause = 'p.name ASC';
    ELSEIF p_sort_by = 'price_asc' THEN
        SET v_sort_clause = 'p.price_sale ASC, p.price_regular ASC';
    ELSEIF p_sort_by = 'price_desc' THEN
        SET v_sort_clause = 'p.price_sale DESC, p.price_regular DESC';
    ELSEIF p_sort_by = 'newest' THEN
        SET v_sort_clause = 'p.created_at DESC';
    ELSEIF p_sort_by = 'popular' THEN
        SET v_sort_clause = 'p.is_featured DESC, p.created_at DESC';
    END IF;

    SELECT
        p.id,
        p.uuid,
        p.name,
        p.slug,
        p.description,
        p.short_description,
        p.sku,
        p.price_regular,
        p.price_sale,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale
            ELSE p.price_regular
        END as final_price,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN
                ROUND(((p.price_regular - p.price_sale) / p.price_regular) * 100, 0)
            ELSE 0
        END as discount_percentage,
        p.stock_quantity,
        p.weight,
        p.dimensions,
        p.is_featured,
        0 as views_count,
        0.0 as rating_average,
        0 as rating_count,
        p.created_at,
        c.name as category_name,
        c.slug as category_slug,
        -- Imagen principal directa del campo featured_image
        p.featured_image,
        -- Conteo de imágenes (simplificado)
        1 as images_count,
        -- Estado de stock
        CASE
            WHEN p.stock_quantity = 0 THEN 'out_of_stock'
            WHEN p.stock_quantity <= 5 THEN 'low_stock'
            ELSE 'in_stock'
        END as stock_status
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id AND c.deleted_at IS NULL
    WHERE p.deleted_at IS NULL
      AND p.is_active = 1
      AND (p_category_id IS NULL OR p.category_id = p_category_id)
      AND (p_search_term IS NULL OR p_search_term = '' OR
           p.name LIKE CONCAT('%', p_search_term, '%') OR
           p.description LIKE CONCAT('%', p_search_term, '%') OR
           p.sku LIKE CONCAT('%', p_search_term, '%'))
      AND (p_min_price IS NULL OR
           CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0
                THEN p.price_sale ELSE p.price_regular END >= p_min_price)
      AND (p_max_price IS NULL OR
           CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0
                THEN p.price_sale ELSE p.price_regular END <= p_max_price)
    ORDER BY
        CASE WHEN p_sort_by = 'name' THEN p.name END ASC,
        CASE WHEN p_sort_by = 'price_asc' THEN
            CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0
                 THEN p.price_sale ELSE p.price_regular END
        END ASC,
        CASE WHEN p_sort_by = 'price_desc' THEN
            CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0
                 THEN p.price_sale ELSE p.price_regular END
        END DESC,
        CASE WHEN p_sort_by = 'newest' THEN p.created_at END DESC,
        CASE WHEN p_sort_by = 'popular' THEN p.is_featured END DESC,
        p.created_at DESC
    LIMIT 24;
END$$

-- =====================================================
-- SP: Obtener Detalles de Producto Público
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_public_product_details$$
CREATE PROCEDURE sp_get_public_product_details(
    IN p_product_id INT,
    IN p_product_slug VARCHAR(255)
)
BEGIN
    DECLARE v_product_id INT DEFAULT p_product_id;

    -- Si se proporciona slug, obtener ID
    IF p_product_slug IS NOT NULL AND p_product_slug != '' THEN
        SELECT id INTO v_product_id
        FROM products
        WHERE slug = p_product_slug
          AND deleted_at IS NULL
          AND is_active = 1
        LIMIT 1;
    END IF;

    -- Registrar vista del producto (opcional, por ahora comentado)
    -- UPDATE products SET views_count = views_count + 1 WHERE id = v_product_id;

    -- Obtener detalles del producto
    SELECT
        p.id,
        p.uuid,
        p.name,
        p.slug,
        p.description,
        p.short_description,
        p.sku,
        p.price_regular,
        p.price_sale,
        p.currency,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale
            ELSE p.price_regular
        END as final_price,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN
                ROUND(((p.price_regular - p.price_sale) / p.price_regular) * 100, 0)
            ELSE 0
        END as discount_percentage,
        p.stock_quantity,
        p.weight,
        p.dimensions,
        p.dimension_length,
        p.dimension_width,
        p.dimension_height,
        p.dimension_unit,
        p.attributes,
        p.is_featured,
        0 as views_count,
        0.0 as rating_average,
        0 as rating_count,
        p.meta_title,
        p.meta_description,
        p.created_at,
        p.updated_at,
        c.id as category_id,
        c.name as category_name,
        c.slug as category_slug,
        c.description as category_description,
        -- Estado de stock
        CASE
            WHEN p.stock_quantity = 0 THEN 'out_of_stock'
            WHEN p.stock_quantity <= 5 THEN 'low_stock'
            ELSE 'in_stock'
        END as stock_status,
        -- Breadcrumb de categoría (simplificado)
        c.name as category_breadcrumb,
        -- Imagen principal directa del campo featured_image
        p.featured_image
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id AND c.deleted_at IS NULL
    WHERE p.id = v_product_id
      AND p.deleted_at IS NULL
      AND p.is_active = 1;
END$$

-- =====================================================
-- STORED PROCEDURES PARA ADMINISTRACIÓN DE USUARIOS
-- =====================================================

-- Listar usuarios con paginación, búsqueda y filtros
DROP PROCEDURE IF EXISTS sp_admin_list_users$$
CREATE PROCEDURE sp_admin_list_users(
    IN p_search VARCHAR(255),
    IN p_role VARCHAR(50),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    IF p_search IS NOT NULL AND p_search != '' AND p_role IS NOT NULL AND p_role != '' THEN
        SELECT u.id,
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as name,
               u.username, u.email, u.phone, u.status as is_active, u.created_at, u.updated_at,
               GROUP_CONCAT(DISTINCT r.name SEPARATOR ', ') as roles,
               GROUP_CONCAT(DISTINCT r.id SEPARATOR ',') as role_ids,
               COALESCE(COUNT(DISTINCT o.id), 0) as total_orders,
               COALESCE(SUM(o.total), 0) as total_spent
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        LEFT JOIN orders o ON o.customer_id = u.id
        WHERE 1=1
        AND (u.first_name LIKE CONCAT('%', p_search, '%')
             OR u.last_name LIKE CONCAT('%', p_search, '%')
             OR u.username LIKE CONCAT('%', p_search, '%')
             OR u.email LIKE CONCAT('%', p_search, '%'))
        AND r.name = p_role
        GROUP BY u.id ORDER BY u.created_at DESC
        LIMIT p_limit OFFSET p_offset;
    ELSEIF p_search IS NOT NULL AND p_search != '' THEN
        SELECT u.id,
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as name,
               u.username, u.email, u.phone, u.status as is_active, u.created_at, u.updated_at,
               GROUP_CONCAT(DISTINCT r.name SEPARATOR ', ') as roles,
               GROUP_CONCAT(DISTINCT r.id SEPARATOR ',') as role_ids,
               COALESCE(COUNT(DISTINCT o.id), 0) as total_orders,
               COALESCE(SUM(o.total), 0) as total_spent
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        LEFT JOIN orders o ON o.customer_id = u.id
        WHERE 1=1
        AND (u.first_name LIKE CONCAT('%', p_search, '%')
             OR u.last_name LIKE CONCAT('%', p_search, '%')
             OR u.username LIKE CONCAT('%', p_search, '%')
             OR u.email LIKE CONCAT('%', p_search, '%'))
        GROUP BY u.id ORDER BY u.created_at DESC
        LIMIT p_limit OFFSET p_offset;
    ELSEIF p_role IS NOT NULL AND p_role != '' THEN
        SELECT u.id,
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as name,
               u.username, u.email, u.phone, u.status as is_active, u.created_at, u.updated_at,
               GROUP_CONCAT(DISTINCT r.name SEPARATOR ', ') as roles,
               GROUP_CONCAT(DISTINCT r.id SEPARATOR ',') as role_ids,
               COALESCE(COUNT(DISTINCT o.id), 0) as total_orders,
               COALESCE(SUM(o.total), 0) as total_spent
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        LEFT JOIN orders o ON o.customer_id = u.id
        WHERE 1=1
        AND r.name = p_role
        GROUP BY u.id ORDER BY u.created_at DESC
        LIMIT p_limit OFFSET p_offset;
    ELSE
        SELECT u.id,
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as name,
               u.username, u.email, u.phone, u.status as is_active, u.created_at, u.updated_at,
               GROUP_CONCAT(DISTINCT r.name SEPARATOR ', ') as roles,
               GROUP_CONCAT(DISTINCT r.id SEPARATOR ',') as role_ids,
               COALESCE(COUNT(DISTINCT o.id), 0) as total_orders,
               COALESCE(SUM(o.total), 0) as total_spent
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        LEFT JOIN orders o ON o.customer_id = u.id
        WHERE 1=1
        GROUP BY u.id ORDER BY u.created_at DESC
        LIMIT p_limit OFFSET p_offset;
    END IF;
END$$

-- Contar total de usuarios con filtros
DROP PROCEDURE IF EXISTS sp_admin_count_users$$
CREATE PROCEDURE sp_admin_count_users(
    IN p_search VARCHAR(255),
    IN p_role VARCHAR(50)
)
BEGIN
    IF p_search IS NOT NULL AND p_search != '' AND p_role IS NOT NULL AND p_role != '' THEN
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        WHERE 1=1
        AND (u.first_name LIKE CONCAT('%', p_search, '%')
             OR u.last_name LIKE CONCAT('%', p_search, '%')
             OR u.username LIKE CONCAT('%', p_search, '%')
             OR u.email LIKE CONCAT('%', p_search, '%'))
        AND r.name = p_role;
    ELSEIF p_search IS NOT NULL AND p_search != '' THEN
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        WHERE 1=1
        AND (u.first_name LIKE CONCAT('%', p_search, '%')
             OR u.last_name LIKE CONCAT('%', p_search, '%')
             OR u.username LIKE CONCAT('%', p_search, '%')
             OR u.email LIKE CONCAT('%', p_search, '%'));
    ELSEIF p_role IS NOT NULL AND p_role != '' THEN
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.is_active = 1
        LEFT JOIN roles r ON r.id = ur.role_id
        WHERE 1=1
        AND r.name = p_role;
    ELSE
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        WHERE 1=1;
    END IF;
END$$

-- Verificar si email existe
DROP PROCEDURE IF EXISTS sp_admin_check_email_exists$$
CREATE PROCEDURE sp_admin_check_email_exists(
    IN p_email VARCHAR(100),
    IN p_exclude_user_id INT,
    OUT p_email_exists BOOLEAN
)
BEGIN
    DECLARE user_count INT DEFAULT 0;

    IF p_exclude_user_id IS NOT NULL THEN
        SELECT COUNT(*) INTO user_count
        FROM users
        WHERE email = p_email AND id != p_exclude_user_id;
    ELSE
        SELECT COUNT(*) INTO user_count
        FROM users
        WHERE email = p_email;
    END IF;

    SET p_email_exists = (user_count > 0);
END$$

-- Verificar si username existe
DROP PROCEDURE IF EXISTS sp_admin_check_username_exists$$
CREATE PROCEDURE sp_admin_check_username_exists(
    IN p_username VARCHAR(50),
    IN p_exclude_user_id INT,
    OUT p_username_exists BOOLEAN
)
BEGIN
    DECLARE user_count INT DEFAULT 0;

    IF p_exclude_user_id IS NOT NULL THEN
        SELECT COUNT(*) INTO user_count
        FROM users
        WHERE username = p_username AND id != p_exclude_user_id;
    ELSE
        SELECT COUNT(*) INTO user_count
        FROM users
        WHERE username = p_username;
    END IF;

    SET p_username_exists = (user_count > 0);
END$$

-- Crear usuario desde admin
DROP PROCEDURE IF EXISTS sp_admin_create_user$$
CREATE PROCEDURE sp_admin_create_user(
    IN p_uuid VARCHAR(36),
    IN p_username VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_password VARCHAR(255),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_phone VARCHAR(20),
    OUT p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_user_id = NULL;
    END;

    START TRANSACTION;

    INSERT INTO users (uuid, username, email, password, first_name, last_name, phone, status, created_at)
    VALUES (p_uuid, p_username, p_email, p_password, p_first_name, p_last_name, p_phone, 'active', NOW());

    SET p_user_id = LAST_INSERT_ID();
    SET p_result = 'SUCCESS';

    COMMIT;
END$$

-- Actualizar usuario desde admin
DROP PROCEDURE IF EXISTS sp_admin_update_user$$
CREATE PROCEDURE sp_admin_update_user(
    IN p_user_id INT,
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_username VARCHAR(50),
    IN p_phone VARCHAR(20),
    IN p_status VARCHAR(20),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    UPDATE users
    SET first_name = p_first_name,
        last_name = p_last_name,
        email = p_email,
        username = p_username,
        phone = p_phone,
        status = p_status,
        updated_at = NOW()
    WHERE id = p_user_id;

    SET p_result = 'SUCCESS';

    COMMIT;
END$$

-- Desactivar todos los roles de un usuario
DROP PROCEDURE IF EXISTS sp_admin_deactivate_user_roles$$
CREATE PROCEDURE sp_admin_deactivate_user_roles(
    IN p_user_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    UPDATE user_roles
    SET is_active = 0
    WHERE user_id = p_user_id;

    SET p_result = 'SUCCESS';

    COMMIT;
END$$

-- Verificar si existe relación usuario-rol
DROP PROCEDURE IF EXISTS sp_admin_check_user_role_exists$$
CREATE PROCEDURE sp_admin_check_user_role_exists(
    IN p_user_id INT,
    IN p_role_id INT,
    OUT p_role_exists BOOLEAN,
    OUT p_relation_id INT
)
BEGIN
    DECLARE relation_count INT DEFAULT 0;

    SELECT COUNT(*), COALESCE(MAX(id), 0)
    INTO relation_count, p_relation_id
    FROM user_roles
    WHERE user_id = p_user_id AND role_id = p_role_id;

    SET p_role_exists = (relation_count > 0);
END$$

-- Reactivar relación usuario-rol existente
DROP PROCEDURE IF EXISTS sp_admin_reactivate_user_role$$
CREATE PROCEDURE sp_admin_reactivate_user_role(
    IN p_user_id INT,
    IN p_role_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    UPDATE user_roles
    SET is_active = 1
    WHERE user_id = p_user_id AND role_id = p_role_id;

    SET p_result = 'SUCCESS';

    COMMIT;
END$$

-- Obtener todos los roles activos
DROP PROCEDURE IF EXISTS sp_admin_get_active_roles$$
CREATE PROCEDURE sp_admin_get_active_roles()
BEGIN
    SELECT * FROM roles WHERE is_active = 1 ORDER BY name;
END$$

-- Actualizar estado de pedido
DROP PROCEDURE IF EXISTS sp_admin_update_order$$
CREATE PROCEDURE sp_admin_update_order(
    IN p_order_id INT,
    IN p_status VARCHAR(50),
    IN p_payment_status VARCHAR(50),
    IN p_notes TEXT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que el pedido existe
    IF NOT EXISTS (SELECT 1 FROM orders WHERE id = p_order_id) THEN
        SET p_result = 'ORDER_NOT_FOUND';
        ROLLBACK;
    ELSE
        -- Actualizar el pedido
        IF p_payment_status IS NOT NULL THEN
            UPDATE orders
            SET status = p_status,
                payment_status = p_payment_status,
                updated_at = NOW()
            WHERE id = p_order_id;
        ELSE
            UPDATE orders
            SET status = p_status,
                updated_at = NOW()
            WHERE id = p_order_id;
        END IF;

        -- Agregar nota si se proporciona (solo si la tabla existe)
        IF p_notes IS NOT NULL AND p_notes != '' THEN
            -- Verificar si la tabla order_notes existe
            SET @table_exists = 0;
            SELECT COUNT(*) INTO @table_exists
            FROM information_schema.tables
            WHERE table_schema = DATABASE() AND table_name = 'order_notes';

            IF @table_exists > 0 THEN
                INSERT INTO order_notes (order_id, note, created_at)
                VALUES (p_order_id, p_notes, NOW());
            END IF;
        END IF;

        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- Obtener métodos de envío con conteo de tarifas
DROP PROCEDURE IF EXISTS sp_admin_get_shipping_methods$$
CREATE PROCEDURE sp_admin_get_shipping_methods()
BEGIN
    SELECT sm.*, COUNT(sr.id) as rates_count
    FROM shipping_methods sm
    LEFT JOIN shipping_rates sr ON sr.shipping_method_id = sm.id AND sr.is_active = 1
    GROUP BY sm.id
    ORDER BY sm.sort_order, sm.name;
END$$

-- Obtener tarifas de envío con nombres de métodos
DROP PROCEDURE IF EXISTS sp_admin_get_shipping_rates$$
CREATE PROCEDURE sp_admin_get_shipping_rates()
BEGIN
    SELECT sr.*, sm.name as method_name
    FROM shipping_rates sr
    INNER JOIN shipping_methods sm ON sm.id = sr.shipping_method_id
    WHERE sr.is_active = 1
    ORDER BY sm.sort_order, sr.min_weight, sr.min_volume;
END$$

-- ========================================
-- STORED PROCEDURES PARA CATEGORÍAS
-- ========================================

-- Listar categorías con conteo de productos (solo productos activos y no eliminados)
DROP PROCEDURE IF EXISTS sp_admin_list_categories$$
CREATE PROCEDURE sp_admin_list_categories()
BEGIN
    SELECT c.*,
           COUNT(p.id) as products_count,
           COALESCE(parent.name, 'Sin categoría padre') as parent_name
    FROM categories c
    LEFT JOIN products p ON p.category_id = c.id AND p.is_active = 1 AND p.deleted_at IS NULL
    LEFT JOIN categories parent ON parent.id = c.parent_id
    WHERE c.deleted_at IS NULL
    GROUP BY c.id
    ORDER BY c.parent_id, c.sort_order, c.name;
END$$

-- Obtener categoría por ID (solo productos activos y no eliminados)
DROP PROCEDURE IF EXISTS sp_admin_get_category$$
CREATE PROCEDURE sp_admin_get_category(
    IN p_category_id INT
)
BEGIN
    SELECT c.*,
           COUNT(p.id) as products_count,
           COALESCE(parent.name, 'Sin categoría padre') as parent_name
    FROM categories c
    LEFT JOIN products p ON p.category_id = c.id AND p.is_active = 1 AND p.deleted_at IS NULL
    LEFT JOIN categories parent ON parent.id = c.parent_id
    WHERE c.id = p_category_id AND c.deleted_at IS NULL
    GROUP BY c.id;
END$$

-- Verificar si nombre de categoría existe
DROP PROCEDURE IF EXISTS sp_admin_check_category_name_exists$$
CREATE PROCEDURE sp_admin_check_category_name_exists(
    IN p_name VARCHAR(100),
    IN p_exclude_id INT,
    OUT p_name_exists BOOLEAN
)
BEGIN
    DECLARE category_count INT DEFAULT 0;

    IF p_exclude_id IS NOT NULL THEN
        SELECT COUNT(*) INTO category_count
        FROM categories
        WHERE name = p_name AND id != p_exclude_id;
    ELSE
        SELECT COUNT(*) INTO category_count
        FROM categories
        WHERE name = p_name;
    END IF;

    SET p_name_exists = (category_count > 0);
END$$

-- Crear nueva categoría
DROP PROCEDURE IF EXISTS sp_admin_create_category$$
CREATE PROCEDURE sp_admin_create_category(
    IN p_name VARCHAR(100),
    IN p_description TEXT,
    IN p_parent_id INT,
    IN p_sort_order INT,
    IN p_is_active BOOLEAN,
    OUT p_category_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_uuid VARCHAR(36);
    DECLARE v_slug VARCHAR(120);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(120);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_category_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el nombre no exista
    CALL sp_admin_check_category_name_exists(p_name, NULL, @name_exists);
    IF @name_exists THEN
        SET p_result = 'NAME_EXISTS';
        SET p_category_id = 0;
        ROLLBACK;
    ELSE
        -- Generar UUID
        SET v_uuid = UUID();

        -- Generar slug único basado en el nombre
        SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
        SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
        SET v_slug = v_base_slug;

        -- Verificar que el slug sea único
        WHILE EXISTS (SELECT 1 FROM categories WHERE slug = v_slug) DO
            SET v_counter = v_counter + 1;
            SET v_slug = CONCAT(v_base_slug, '-', v_counter);
        END WHILE;

        -- Crear la categoría
        INSERT INTO categories (uuid, name, slug, description, parent_id, sort_order, is_active, created_at, updated_at)
        VALUES (v_uuid, p_name, v_slug, p_description, p_parent_id, p_sort_order, p_is_active, NOW(), NOW());

        SET p_category_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- Actualizar categoría
DROP PROCEDURE IF EXISTS sp_admin_update_category$$
CREATE PROCEDURE sp_admin_update_category(
    IN p_category_id INT,
    IN p_name VARCHAR(100),
    IN p_description TEXT,
    IN p_parent_id INT,
    IN p_sort_order INT,
    IN p_is_active BOOLEAN,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_slug VARCHAR(120);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(120);
    DECLARE v_current_name VARCHAR(100);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que la categoría existe y obtener el nombre actual
    SELECT name INTO v_current_name FROM categories WHERE id = p_category_id;
    IF v_current_name IS NULL THEN
        SET p_result = 'CATEGORY_NOT_FOUND';
        ROLLBACK;
    ELSE
        -- Verificar que el nombre no exista en otra categoría
        CALL sp_admin_check_category_name_exists(p_name, p_category_id, @name_exists);
        IF @name_exists THEN
            SET p_result = 'NAME_EXISTS';
            ROLLBACK;
        ELSE
            -- Si el nombre cambió, generar nuevo slug
            IF v_current_name != p_name THEN
                SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
                SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
                SET v_slug = v_base_slug;

                -- Verificar que el slug sea único
                WHILE EXISTS (SELECT 1 FROM categories WHERE slug = v_slug AND id != p_category_id) DO
                    SET v_counter = v_counter + 1;
                    SET v_slug = CONCAT(v_base_slug, '-', v_counter);
                END WHILE;

                -- Actualizar la categoría con nuevo slug
                UPDATE categories
                SET name = p_name,
                    slug = v_slug,
                    description = p_description,
                    parent_id = p_parent_id,
                    sort_order = p_sort_order,
                    is_active = p_is_active,
                    updated_at = NOW()
                WHERE id = p_category_id;
            ELSE
                -- Actualizar la categoría sin cambiar slug
                UPDATE categories
                SET description = p_description,
                    parent_id = p_parent_id,
                    sort_order = p_sort_order,
                    is_active = p_is_active,
                    updated_at = NOW()
                WHERE id = p_category_id;
            END IF;

            SET p_result = 'SUCCESS';
            COMMIT;
        END IF;
    END IF;
END$$

-- Eliminar categoría (soft delete)
DROP PROCEDURE IF EXISTS sp_admin_delete_category$$
CREATE PROCEDURE sp_admin_delete_category(
    IN p_category_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE product_count INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que la categoría existe
    IF NOT EXISTS (SELECT 1 FROM categories WHERE id = p_category_id) THEN
        SET p_result = 'CATEGORY_NOT_FOUND';
        ROLLBACK;
    ELSE
        -- Verificar si tiene productos asociados (solo productos activos y no eliminados)
        SELECT COUNT(*) INTO product_count
        FROM products
        WHERE category_id = p_category_id AND is_active = 1 AND deleted_at IS NULL;

        IF product_count > 0 THEN
            SET p_result = 'HAS_PRODUCTS';
            ROLLBACK;
        ELSE
            -- Verificar si tiene subcategorías activas
            SELECT COUNT(*) INTO product_count
            FROM categories
            WHERE parent_id = p_category_id AND deleted_at IS NULL;

            IF product_count > 0 THEN
                SET p_result = 'HAS_SUBCATEGORIES';
                ROLLBACK;
            ELSE
                -- Eliminar la categoría (soft delete si existe la columna, sino hard delete)
                SET @table_has_deleted_at = 0;
                SELECT COUNT(*) INTO @table_has_deleted_at
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = 'categories'
                AND column_name = 'deleted_at';

                IF @table_has_deleted_at > 0 THEN
                    UPDATE categories SET deleted_at = NOW() WHERE id = p_category_id;
                ELSE
                    DELETE FROM categories WHERE id = p_category_id;
                END IF;

                SET p_result = 'SUCCESS';
                COMMIT;
            END IF;
        END IF;
    END IF;
END$$

-- Obtener categorías para select (solo activas) con jerarquía
DROP PROCEDURE IF EXISTS sp_admin_get_active_categories$$
CREATE PROCEDURE sp_admin_get_active_categories()
BEGIN
    SELECT id, name, parent_id,
           CASE
               WHEN parent_id IS NULL THEN name
               ELSE CONCAT('— ', name)
           END as display_name,
           CASE
               WHEN parent_id IS NULL THEN 0
               ELSE 1
           END as level
    FROM categories c
    WHERE is_active = 1 AND deleted_at IS NULL
    ORDER BY
        CASE WHEN parent_id IS NULL THEN id ELSE parent_id END,
        parent_id IS NOT NULL,
        sort_order,
        name;
END$$

-- =====================================================
-- PRODUCTOS - STORED PROCEDURES PARA ADMIN
-- =====================================================

-- Listar productos con filtros para admin
DROP PROCEDURE IF EXISTS sp_admin_list_products$$
CREATE PROCEDURE sp_admin_list_products(
    IN p_search VARCHAR(255),
    IN p_category_id INT,
    IN p_status VARCHAR(20),
    IN p_stock_status VARCHAR(20),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    DECLARE v_sql TEXT;
    DECLARE v_where_conditions TEXT DEFAULT '';

    -- Construir condiciones WHERE dinámicamente
    SET v_where_conditions = 'WHERE p.deleted_at IS NULL';

    IF p_search IS NOT NULL AND p_search != '' THEN
        SET v_where_conditions = CONCAT(v_where_conditions,
            ' AND (p.name LIKE CONCAT("%", "', p_search, '", "%") OR p.sku LIKE CONCAT("%", "', p_search, '", "%") OR p.description LIKE CONCAT("%", "', p_search, '", "%"))');
    END IF;

    IF p_category_id IS NOT NULL AND p_category_id > 0 THEN
        SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.category_id = ', p_category_id);
    END IF;

    IF p_status IS NOT NULL AND p_status != '' AND p_status != 'all' THEN
        IF p_status = 'active' THEN
            SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.is_active = 1');
        ELSEIF p_status = 'inactive' THEN
            SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.is_active = 0');
        END IF;
    END IF;

    IF p_stock_status IS NOT NULL AND p_stock_status != '' AND p_stock_status != 'all' THEN
        IF p_stock_status = 'high' THEN
            SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.stock_quantity > 20');
        ELSEIF p_stock_status = 'medium' THEN
            SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.stock_quantity BETWEEN 5 AND 20');
        ELSEIF p_stock_status = 'low' THEN
            SET v_where_conditions = CONCAT(v_where_conditions, ' AND p.stock_quantity < 5');
        END IF;
    END IF;

    -- Construir consulta completa
    SET v_sql = CONCAT('
        SELECT p.*,
               c.name as category_name,
               CASE
                   WHEN p.stock_quantity > 20 THEN "high"
                   WHEN p.stock_quantity BETWEEN 5 AND 20 THEN "medium"
                   WHEN p.stock_quantity < 5 THEN "low"
                   ELSE "none"
               END as stock_level
        FROM products p
        LEFT JOIN categories c ON c.id = p.category_id AND c.deleted_at IS NULL
        ', v_where_conditions, '
        ORDER BY p.created_at DESC
        LIMIT ', IFNULL(p_limit, 20), ' OFFSET ', IFNULL(p_offset, 0));

    SET @sql = v_sql;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- Obtener producto por ID para admin
DROP PROCEDURE IF EXISTS sp_admin_get_product$$
CREATE PROCEDURE sp_admin_get_product(
    IN p_product_id INT
)
BEGIN
    SELECT p.*,
           c.name as category_name,
           b.name as brand_name,
           v.business_name as vendor_name
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id AND c.deleted_at IS NULL
    LEFT JOIN brands b ON b.id = p.brand_id AND b.deleted_at IS NULL
    LEFT JOIN vendors v ON v.id = p.vendor_id
    WHERE p.id = p_product_id AND p.deleted_at IS NULL;
END$$

-- Crear nuevo producto
DROP PROCEDURE IF EXISTS sp_admin_create_product$$
CREATE PROCEDURE sp_admin_create_product(
    IN p_name VARCHAR(200),
    IN p_sku VARCHAR(50),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_weight DECIMAL(8,2),
    IN p_dimensions VARCHAR(100),
    IN p_featured_image VARCHAR(255),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_featured BOOLEAN,
    OUT p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_uuid VARCHAR(36);
    DECLARE v_slug VARCHAR(220);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(220);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_product_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el SKU no exista
    IF EXISTS (SELECT 1 FROM products WHERE sku = p_sku AND deleted_at IS NULL) THEN
        SET p_result = 'SKU_EXISTS';
        SET p_product_id = 0;
        ROLLBACK;
    ELSE
        -- Generar UUID
        SET v_uuid = UUID();

        -- Generar slug único basado en el nombre
        SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
        SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
        SET v_slug = v_base_slug;

        -- Verificar que el slug sea único
        WHILE EXISTS (SELECT 1 FROM products WHERE slug = v_slug AND deleted_at IS NULL) DO
            SET v_counter = v_counter + 1;
            SET v_slug = CONCAT(v_base_slug, '-', v_counter);
        END WHILE;

        -- Crear el producto
        INSERT INTO products (
            uuid, sku, name, slug, description, short_description, category_id, brand_id,
            price_regular, price_sale, stock_quantity, stock_min, weight, dimensions,
            featured_image, gallery_images, is_active, is_featured, created_at, updated_at
        ) VALUES (
            v_uuid, p_sku, p_name, v_slug, p_description, p_short_description, p_category_id, p_brand_id,
            p_price_regular, p_price_sale, p_stock_quantity, p_stock_min, p_weight, p_dimensions,
            p_featured_image, p_gallery_images, p_is_active, p_is_featured, NOW(), NOW()
        );

        SET p_product_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- Actualizar producto
DROP PROCEDURE IF EXISTS sp_admin_update_product$$
CREATE PROCEDURE sp_admin_update_product(
    IN p_product_id INT,
    IN p_name VARCHAR(200),
    IN p_sku VARCHAR(50),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_currency VARCHAR(3),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_weight DECIMAL(8,2),
    IN p_dimensions VARCHAR(100),
    IN p_dimension_length DECIMAL(8,2),
    IN p_dimension_width DECIMAL(8,2),
    IN p_dimension_height DECIMAL(8,2),
    IN p_dimension_unit VARCHAR(10),
    IN p_featured_image VARCHAR(255),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_featured BOOLEAN,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_slug VARCHAR(220);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(220);
    DECLARE v_current_name VARCHAR(200);
    DECLARE v_current_sku VARCHAR(50);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que el producto existe y obtener datos actuales
    SELECT name, sku INTO v_current_name, v_current_sku
    FROM products WHERE id = p_product_id AND deleted_at IS NULL;

    IF v_current_name IS NULL THEN
        SET p_result = 'PRODUCT_NOT_FOUND';
        ROLLBACK;
    ELSE
        -- Verificar que el SKU no exista en otro producto
        IF EXISTS (SELECT 1 FROM products WHERE sku = p_sku AND id != p_product_id AND deleted_at IS NULL) THEN
            SET p_result = 'SKU_EXISTS';
            ROLLBACK;
        ELSE
            -- Si el nombre cambió, generar nuevo slug
            IF v_current_name != p_name THEN
                SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
                SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
                SET v_slug = v_base_slug;

                -- Verificar que el slug sea único
                WHILE EXISTS (SELECT 1 FROM products WHERE slug = v_slug AND id != p_product_id AND deleted_at IS NULL) DO
                    SET v_counter = v_counter + 1;
                    SET v_slug = CONCAT(v_base_slug, '-', v_counter);
                END WHILE;

                -- Actualizar el producto con nuevo slug
                UPDATE products
                SET name = p_name,
                    slug = v_slug,
                    sku = p_sku,
                    description = p_description,
                    short_description = p_short_description,
                    category_id = p_category_id,
                    brand_id = p_brand_id,
                    price_regular = p_price_regular,
                    price_sale = p_price_sale,
                    currency = p_currency,
                    stock_quantity = p_stock_quantity,
                    stock_min = p_stock_min,
                    weight = p_weight,
                    dimensions = p_dimensions,
                    dimension_length = p_dimension_length,
                    dimension_width = p_dimension_width,
                    dimension_height = p_dimension_height,
                    dimension_unit = p_dimension_unit,
                    featured_image = CASE
                        WHEN p_featured_image IS NOT NULL AND p_featured_image != '' THEN p_featured_image
                        ELSE featured_image
                    END,
                    gallery_images = CASE
                        WHEN p_gallery_images IS NOT NULL AND p_gallery_images != '' THEN p_gallery_images
                        ELSE gallery_images
                    END,
                    is_active = p_is_active,
                    is_featured = p_is_featured,
                    updated_at = NOW()
                WHERE id = p_product_id;
            ELSE
                -- Actualizar el producto sin cambiar slug
                UPDATE products
                SET sku = p_sku,
                    description = p_description,
                    short_description = p_short_description,
                    category_id = p_category_id,
                    brand_id = p_brand_id,
                    price_regular = p_price_regular,
                    price_sale = p_price_sale,
                    currency = p_currency,
                    stock_quantity = p_stock_quantity,
                    stock_min = p_stock_min,
                    weight = p_weight,
                    dimensions = p_dimensions,
                    dimension_length = p_dimension_length,
                    dimension_width = p_dimension_width,
                    dimension_height = p_dimension_height,
                    dimension_unit = p_dimension_unit,
                    featured_image = CASE
                        WHEN p_featured_image IS NOT NULL AND p_featured_image != '' THEN p_featured_image
                        ELSE featured_image
                    END,
                    gallery_images = CASE
                        WHEN p_gallery_images IS NOT NULL AND p_gallery_images != '' THEN p_gallery_images
                        ELSE gallery_images
                    END,
                    is_active = p_is_active,
                    is_featured = p_is_featured,
                    updated_at = NOW()
                WHERE id = p_product_id;
            END IF;

            SET p_result = 'SUCCESS';
            COMMIT;
        END IF;
    END IF;
END$$

-- Eliminar producto (soft delete)
DROP PROCEDURE IF EXISTS sp_admin_delete_product$$
CREATE PROCEDURE sp_admin_delete_product(
    IN p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que el producto existe
    IF NOT EXISTS (SELECT 1 FROM products WHERE id = p_product_id AND deleted_at IS NULL) THEN
        SET p_result = 'PRODUCT_NOT_FOUND';
        ROLLBACK;
    ELSE
        -- Verificar si tiene pedidos asociados (opcional - puedes comentar si no quieres esta validación)
        -- IF EXISTS (SELECT 1 FROM order_items WHERE product_id = p_product_id) THEN
        --     SET p_result = 'HAS_ORDERS';
        --     ROLLBACK;
        -- ELSE
            -- Eliminar el producto (soft delete)
            UPDATE products
            SET deleted_at = NOW(), updated_at = NOW()
            WHERE id = p_product_id;

            SET p_result = 'SUCCESS';
            COMMIT;
        -- END IF;
    END IF;
END$$

-- Duplicar producto
DROP PROCEDURE IF EXISTS sp_admin_duplicate_product$$
CREATE PROCEDURE sp_admin_duplicate_product(
    IN p_product_id INT,
    OUT p_new_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_uuid VARCHAR(36);
    DECLARE v_slug VARCHAR(220);
    DECLARE v_sku VARCHAR(50);
    DECLARE v_name VARCHAR(200);
    DECLARE v_counter INT DEFAULT 1;
    DECLARE v_base_slug VARCHAR(220);
    DECLARE v_base_sku VARCHAR(50);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_new_product_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el producto original existe
    SELECT name, sku INTO v_name, v_sku
    FROM products WHERE id = p_product_id AND deleted_at IS NULL;

    IF v_name IS NULL THEN
        SET p_result = 'PRODUCT_NOT_FOUND';
        SET p_new_product_id = 0;
        ROLLBACK;
    ELSE
        -- Generar UUID
        SET v_uuid = UUID();

        -- Generar SKU único
        SET v_base_sku = v_sku;
        SET v_sku = CONCAT(v_base_sku, '-COPY');
        WHILE EXISTS (SELECT 1 FROM products WHERE sku = v_sku AND deleted_at IS NULL) DO
            SET v_counter = v_counter + 1;
            SET v_sku = CONCAT(v_base_sku, '-COPY-', v_counter);
        END WHILE;

        -- Generar slug único
        SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CONCAT(v_name, ' Copy'), ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
        SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
        SET v_slug = v_base_slug;
        SET v_counter = 1;

        WHILE EXISTS (SELECT 1 FROM products WHERE slug = v_slug AND deleted_at IS NULL) DO
            SET v_counter = v_counter + 1;
            SET v_slug = CONCAT(v_base_slug, '-', v_counter);
        END WHILE;

        -- Duplicar el producto
        INSERT INTO products (
            uuid, sku, name, slug, description, short_description, category_id, brand_id,
            price_regular, price_sale, stock_quantity, stock_min, weight, dimensions,
            featured_image, gallery_images, is_active, is_featured, is_digital,
            tags, attributes, meta_title, meta_description, meta_keywords,
            created_at, updated_at
        )
        SELECT
            v_uuid, v_sku, CONCAT(name, ' - Copy'), v_slug, description, short_description, category_id, brand_id,
            price_regular, price_sale, 0, stock_min, weight, dimensions,
            featured_image, gallery_images, 0, 0, is_digital,
            tags, attributes, meta_title, meta_description, meta_keywords,
            NOW(), NOW()
        FROM products
        WHERE id = p_product_id;

        SET p_new_product_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- Obtener todas las configuraciones agrupadas
DROP PROCEDURE IF EXISTS sp_admin_get_settings$$
CREATE PROCEDURE sp_admin_get_settings()
BEGIN
    SELECT
        setting_key,
        setting_value,
        setting_group,
        display_name,
        description,
        setting_type,
        options
    FROM system_settings
    WHERE is_active = TRUE
    ORDER BY setting_group, display_name;
END$$

-- Actualizar una configuración específica
DROP PROCEDURE IF EXISTS sp_admin_update_setting$$
CREATE PROCEDURE sp_admin_update_setting(
    IN p_setting_key VARCHAR(100),
    IN p_setting_value TEXT
)
BEGIN
    DECLARE v_exists INT DEFAULT 0;

    -- Verificar si la configuración existe
    SELECT COUNT(*) INTO v_exists
    FROM system_settings
    WHERE setting_key = p_setting_key AND is_active = TRUE;

    IF v_exists > 0 THEN
        UPDATE system_settings
        SET setting_value = p_setting_value,
            updated_at = NOW()
        WHERE setting_key = p_setting_key;

        SELECT 'SUCCESS' as result, 'Configuración actualizada correctamente' as message;
    ELSE
        SELECT 'ERROR' as result, 'Configuración no encontrada' as message;
    END IF;
END$$

-- Actualizar múltiples configuraciones
DROP PROCEDURE IF EXISTS sp_admin_update_settings_group$$
CREATE PROCEDURE sp_admin_update_settings_group(
    IN p_settings_json JSON
)
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE settings_count INT;
    DECLARE setting_key VARCHAR(100);
    DECLARE setting_value TEXT;
    DECLARE done INT DEFAULT FALSE;

    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'ERROR' as result, 'Error al actualizar configuraciones' as message;
    END;

    START TRANSACTION;

    -- Obtener el número de configuraciones
    SET settings_count = JSON_LENGTH(p_settings_json);

    -- Iterar sobre cada configuración
    WHILE i < settings_count DO
        SET setting_key = JSON_UNQUOTE(JSON_EXTRACT(p_settings_json, CONCAT('$[', i, '].key')));
        SET setting_value = JSON_UNQUOTE(JSON_EXTRACT(p_settings_json, CONCAT('$[', i, '].value')));

        -- Actualizar la configuración si existe
        UPDATE system_settings
        SET setting_value = setting_value,
            updated_at = NOW()
        WHERE setting_key = setting_key AND is_active = TRUE;

        SET i = i + 1;
    END WHILE;

    COMMIT;
    SELECT 'SUCCESS' as result, 'Configuraciones actualizadas correctamente' as message;
END$$

-- =====================================================
-- SP: Actualizar Estado de Usuario
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_update_user_status$$
CREATE PROCEDURE sp_admin_update_user_status(
    IN p_user_id INT,
    IN p_status VARCHAR(20),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_user_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: No se pudo actualizar el estado';
    END;

    START TRANSACTION;

    -- Verificar si el usuario existe
    SELECT COUNT(*) INTO v_user_exists
    FROM users
    WHERE id = p_user_id AND deleted_at IS NULL;

    IF v_user_exists = 0 THEN
        SET p_result = 'ERROR: Usuario no encontrado';
        ROLLBACK;
    ELSE
        -- Actualizar estado
        UPDATE users
        SET status = p_status,
            updated_at = NOW()
        WHERE id = p_user_id;

        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- CREAR ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices para orders
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);

-- Índices para order_items
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- Índices para products
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_slug ON products(slug);

-- =====================================================
-- EJEMPLOS DE USO
-- =====================================================

/*
-- Crear un pedido
CALL sp_create_order(
    'MRC-20240813-ABC123', 0, 'Juan Pérez', '<EMAIL>', '12345678',
    'Zona 1, Guatemala', 'Entregar en horario de oficina', 'transfer',
    100.00, 12.00, 25.00, 137.00, 'web', @order_id, @result
);
SELECT @order_id, @result;

-- Agregar items al pedido
CALL sp_add_order_item(@order_id, 1, 2, @result);
SELECT @result;

-- Actualizar estado
CALL sp_update_order_status(@order_id, 'shipped', 'TRK123456', 'Enviado por DHL', @result);
SELECT @result;

-- Obtener estadísticas
CALL sp_get_sales_stats('2024-01-01', '2024-12-31');

-- Obtener productos más vendidos
CALL sp_get_top_products(10, '2024-01-01', '2024-12-31');

-- Validar carrito
SET @cart = '[{"product_id": 1, "quantity": 2}, {"product_id": 2, "quantity": 1}]';
CALL sp_validate_cart(@cart, @is_valid, @total, @errors);
SELECT @is_valid, @total, @errors;
*/

-- =====================================================
-- STORED PROCEDURES PARA VARIANTES DE PRODUCTOS
-- =====================================================

-- =====================================================
-- SP: Crear Variante de Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_create_product_variant$$
CREATE PROCEDURE sp_create_product_variant(
    IN p_product_id INT,
    IN p_name VARCHAR(255),
    IN p_sku VARCHAR(100),
    IN p_description TEXT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_featured_image VARCHAR(500),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_default BOOLEAN,
    IN p_sort_order INT,
    IN p_attributes JSON,
    OUT p_variant_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_sku_exists INT DEFAULT 0;
    DECLARE v_product_exists INT DEFAULT 0;
    DECLARE v_has_default INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error en la base de datos';
        SET p_variant_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el producto existe
    SELECT COUNT(*) INTO v_product_exists
    FROM products
    WHERE id = p_product_id AND deleted_at IS NULL;

    -- Verificar que el SKU no existe
    SELECT COUNT(*) INTO v_sku_exists
    FROM product_variants
    WHERE sku = p_sku AND deleted_at IS NULL;

    -- Si se marca como default, verificar si ya hay una variante default
    IF p_is_default = 1 THEN
        SELECT COUNT(*) INTO v_has_default
        FROM product_variants
        WHERE product_id = p_product_id AND is_default = 1 AND deleted_at IS NULL;
    END IF;

    IF v_product_exists = 0 THEN
        SET p_result = 'ERROR: El producto especificado no existe';
        SET p_variant_id = 0;
        ROLLBACK;
    ELSEIF v_sku_exists > 0 THEN
        SET p_result = 'ERROR: El SKU ya existe';
        SET p_variant_id = 0;
        ROLLBACK;
    ELSE
        -- Si se marca como default y ya hay una default, quitar la anterior
        IF p_is_default = 1 AND v_has_default > 0 THEN
            UPDATE product_variants
            SET is_default = 0
            WHERE product_id = p_product_id AND is_default = 1;
        END IF;

        -- Si es la primera variante del producto, hacerla default automáticamente
        IF p_is_default IS NULL OR p_is_default = 0 THEN
            SELECT COUNT(*) INTO v_has_default
            FROM product_variants
            WHERE product_id = p_product_id AND deleted_at IS NULL;

            IF v_has_default = 0 THEN
                SET p_is_default = 1;
            END IF;
        END IF;

        INSERT INTO product_variants (
            product_id, name, sku, description, price_regular, price_sale,
            stock_quantity, stock_min, featured_image, gallery_images,
            is_active, is_default, sort_order, attributes, created_at
        ) VALUES (
            p_product_id, p_name, p_sku, p_description, p_price_regular, p_price_sale,
            COALESCE(p_stock_quantity, 0), COALESCE(p_stock_min, 0), p_featured_image, p_gallery_images,
            COALESCE(p_is_active, 1), COALESCE(p_is_default, 0), COALESCE(p_sort_order, 0), p_attributes, NOW()
        );

        SET p_variant_id = LAST_INSERT_ID();

        -- Actualizar el campo has_variants del producto padre
        UPDATE products
        SET has_variants = 1, updated_at = NOW()
        WHERE id = p_product_id;

        SET p_result = 'SUCCESS: Variante creada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar Variante de Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_update_product_variant$$
CREATE PROCEDURE sp_update_product_variant(
    IN p_variant_id INT,
    IN p_name VARCHAR(255),
    IN p_sku VARCHAR(100),
    IN p_description TEXT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_featured_image VARCHAR(500),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_default BOOLEAN,
    IN p_sort_order INT,
    IN p_attributes JSON,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_sku_exists INT DEFAULT 0;
    DECLARE v_variant_exists INT DEFAULT 0;
    DECLARE v_product_id INT DEFAULT 0;
    DECLARE v_has_default INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error en la base de datos';
    END;

    START TRANSACTION;

    -- Verificar que la variante existe
    SELECT COUNT(*), product_id INTO v_variant_exists, v_product_id
    FROM product_variants
    WHERE id = p_variant_id AND deleted_at IS NULL;

    -- Verificar que el SKU no existe en otra variante
    SELECT COUNT(*) INTO v_sku_exists
    FROM product_variants
    WHERE sku = p_sku AND id != p_variant_id AND deleted_at IS NULL;

    IF v_variant_exists = 0 THEN
        SET p_result = 'ERROR: La variante especificada no existe';
        ROLLBACK;
    ELSEIF v_sku_exists > 0 THEN
        SET p_result = 'ERROR: El SKU ya existe en otra variante';
        ROLLBACK;
    ELSE
        -- Si se marca como default, quitar default de otras variantes
        IF p_is_default = 1 THEN
            UPDATE product_variants
            SET is_default = 0
            WHERE product_id = v_product_id AND id != p_variant_id;
        END IF;

        UPDATE product_variants SET
            name = p_name,
            sku = p_sku,
            description = p_description,
            price_regular = p_price_regular,
            price_sale = p_price_sale,
            stock_quantity = COALESCE(p_stock_quantity, 0),
            stock_min = COALESCE(p_stock_min, 0),
            featured_image = p_featured_image,
            gallery_images = p_gallery_images,
            is_active = COALESCE(p_is_active, 1),
            is_default = COALESCE(p_is_default, 0),
            sort_order = COALESCE(p_sort_order, 0),
            attributes = p_attributes,
            updated_at = NOW()
        WHERE id = p_variant_id;

        SET p_result = 'SUCCESS: Variante actualizada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Eliminar Variante de Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_delete_product_variant$$
CREATE PROCEDURE sp_delete_product_variant(
    IN p_variant_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_variant_exists INT DEFAULT 0;
    DECLARE v_product_id INT DEFAULT 0;
    DECLARE v_variant_count INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error en la base de datos';
    END;

    START TRANSACTION;

    -- Verificar que la variante existe
    SELECT COUNT(*), product_id INTO v_variant_exists, v_product_id
    FROM product_variants
    WHERE id = p_variant_id AND deleted_at IS NULL;

    IF v_variant_exists = 0 THEN
        SET p_result = 'ERROR: La variante especificada no existe';
        ROLLBACK;
    ELSE
        -- Eliminar la variante (soft delete)
        UPDATE product_variants
        SET deleted_at = NOW()
        WHERE id = p_variant_id;

        -- Verificar si quedan variantes activas
        SELECT COUNT(*) INTO v_variant_count
        FROM product_variants
        WHERE product_id = v_product_id AND deleted_at IS NULL;

        -- Actualizar el campo has_variants del producto padre
        UPDATE products
        SET has_variants = IF(v_variant_count > 0, 1, 0), updated_at = NOW()
        WHERE id = v_product_id;

        SET p_result = 'SUCCESS: Variante eliminada correctamente';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Obtener Variantes de un Producto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_product_variants$$
CREATE PROCEDURE sp_get_product_variants(
    IN p_product_id INT,
    IN p_active_only BOOLEAN
)
BEGIN
    SELECT
        pv.id,
        pv.product_id,
        pv.name,
        pv.sku,
        pv.description,
        pv.price_regular,
        pv.price_sale,
        pv.stock_quantity,
        pv.stock_min,
        pv.featured_image,
        pv.gallery_images,
        pv.is_active,
        pv.is_default,
        pv.sort_order,
        pv.attributes,
        pv.created_at,
        pv.updated_at,
        CASE
            WHEN pv.stock_quantity = 0 THEN 'out_of_stock'
            WHEN pv.stock_quantity <= pv.stock_min THEN 'low_stock'
            ELSE 'in_stock'
        END as stock_status
    FROM product_variants pv
    WHERE pv.product_id = p_product_id
      AND pv.deleted_at IS NULL
      AND (p_active_only = 0 OR pv.is_active = 1)
    ORDER BY pv.sort_order ASC, pv.name ASC;
END$$

-- =====================================================
-- SP: Obtener Variante por Defecto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_default_variant$$
CREATE PROCEDURE sp_get_default_variant(
    IN p_product_id INT
)
BEGIN
    SELECT
        pv.id,
        pv.product_id,
        pv.name,
        pv.sku,
        pv.description,
        pv.price_regular,
        pv.price_sale,
        pv.stock_quantity,
        pv.stock_min,
        pv.featured_image,
        pv.gallery_images,
        pv.is_active,
        pv.is_default,
        pv.sort_order,
        pv.attributes,
        pv.created_at,
        pv.updated_at,
        CASE
            WHEN pv.stock_quantity = 0 THEN 'out_of_stock'
            WHEN pv.stock_quantity <= pv.stock_min THEN 'low_stock'
            ELSE 'in_stock'
        END as stock_status
    FROM product_variants pv
    WHERE pv.product_id = p_product_id
      AND pv.is_default = 1
      AND pv.is_active = 1
      AND pv.deleted_at IS NULL
    LIMIT 1;
END$$

-- =====================================================
-- SP: Establecer Variante por Defecto
-- =====================================================
DROP PROCEDURE IF EXISTS sp_set_default_variant$$
CREATE PROCEDURE sp_set_default_variant(
    IN p_variant_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_variant_exists INT DEFAULT 0;
    DECLARE v_product_id INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: Error en la base de datos';
    END;

    START TRANSACTION;

    -- Verificar que la variante existe
    SELECT COUNT(*), product_id INTO v_variant_exists, v_product_id
    FROM product_variants
    WHERE id = p_variant_id AND deleted_at IS NULL;

    IF v_variant_exists = 0 THEN
        SET p_result = 'ERROR: La variante especificada no existe';
        ROLLBACK;
    ELSE
        -- Quitar default de todas las variantes del producto
        UPDATE product_variants
        SET is_default = 0
        WHERE product_id = v_product_id;

        -- Establecer la nueva variante por defecto
        UPDATE product_variants
        SET is_default = 1, updated_at = NOW()
        WHERE id = p_variant_id;

        SET p_result = 'SUCCESS: Variante establecida como predeterminada';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Eliminar Permanentemente Productos Eliminados (Hard Delete)
-- =====================================================
DROP PROCEDURE IF EXISTS sp_purge_deleted_products$$
CREATE PROCEDURE sp_purge_deleted_products(
    OUT p_result VARCHAR(200),
    OUT p_deleted_count INT
)
BEGIN
    DECLARE v_product_id INT DEFAULT 0;
    DECLARE v_done INT DEFAULT FALSE;
    DECLARE v_error_occurred INT DEFAULT FALSE;
    DECLARE v_error_message VARCHAR(500) DEFAULT '';

    -- Cursor para obtener productos eliminados (soft delete)
    DECLARE product_cursor CURSOR FOR
        SELECT id FROM products WHERE deleted_at IS NOT NULL;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET v_error_occurred = TRUE;
        GET DIAGNOSTICS CONDITION 1
            v_error_message = MESSAGE_TEXT;
        SET p_result = CONCAT('ERROR: ', v_error_message);
        SET p_deleted_count = 0;
    END;

    START TRANSACTION;

    SET p_deleted_count = 0;

    -- Abrir cursor
    OPEN product_cursor;

    read_loop: LOOP
        FETCH product_cursor INTO v_product_id;

        IF v_done THEN
            LEAVE read_loop;
        END IF;

        -- Eliminar dependencias relacionadas con el producto

        -- 1. Eliminar variantes del producto
        DELETE FROM product_variants WHERE product_id = v_product_id;

        -- 2. Eliminar relaciones de media
        DELETE FROM media_relations WHERE entity_type = 'product' AND entity_id = v_product_id;

        -- 3. Eliminar movimientos de inventario
        DELETE FROM inventory_movements WHERE product_id = v_product_id;

        -- 4. Eliminar registros de inventario
        DELETE FROM inventory WHERE product_id = v_product_id;

        -- 5. Eliminar alertas de inventario
        DELETE FROM inventory_alerts WHERE product_id = v_product_id;

        -- 6. Eliminar items de órdenes de reabastecimiento
        DELETE FROM restock_order_items WHERE product_id = v_product_id;

        -- 7. Eliminar de wishlist (si existe)
        DELETE FROM wishlist WHERE product_id = v_product_id;

        -- 8. Eliminar de wishlist compartida (si existe)
        DELETE FROM shared_wishlist_items WHERE product_id = v_product_id;

        -- 9. Eliminar items del carrito
        DELETE FROM cart_items WHERE product_id = v_product_id;

        -- 10. Eliminar historial de precios
        DELETE FROM price_history WHERE product_id = v_product_id;

        -- NOTA: NO eliminamos order_items porque son registros históricos importantes
        -- Solo actualizamos para marcar que el producto ya no existe
        UPDATE order_items
        SET product_name = CONCAT(product_name, ' [PRODUCTO ELIMINADO]')
        WHERE product_id = v_product_id
        AND product_name NOT LIKE '%[PRODUCTO ELIMINADO]%';

        -- Finalmente, eliminar el producto permanentemente
        DELETE FROM products WHERE id = v_product_id;

        SET p_deleted_count = p_deleted_count + 1;

    END LOOP;

    CLOSE product_cursor;

    IF v_error_occurred THEN
        ROLLBACK;
    ELSE
        COMMIT;
        SET p_result = CONCAT('SUCCESS: ', p_deleted_count, ' productos eliminados permanentemente');
    END IF;

END$$
