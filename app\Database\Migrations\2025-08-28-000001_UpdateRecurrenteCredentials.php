<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateRecurrenteCredentials extends Migration
{
    public function up()
    {
        // Agregar nuevas configuraciones para credenciales separadas de test y live
        $newSettings = [
            [
                'setting_key' => 'recurrente_public_key_test',
                'setting_value' => 'pk_test_JRZca6LgYaDcTlT1VwQQfoIfubWg6TyzSgFRNZH7bVUGdzRZY4vZ6xe7C',
                'setting_group' => 'payment',
                'display_name' => 'Clave Pública (Test)',
                'description' => 'Clave pública de Recurrente para modo de prueba',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'setting_key' => 'recurrente_secret_key_test',
                'setting_value' => 'sk_test_4taMNsXSHb2KkHMAVSvVmr0mQta9tSAs1Vc2CQVqC0tvkjRnkDHqgRh5v',
                'setting_group' => 'payment',
                'display_name' => 'Clave Secreta (Test)',
                'description' => 'Clave secreta de Recurrente para modo de prueba',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'setting_key' => 'recurrente_public_key_live',
                'setting_value' => 'pk_live_ENBlqhHVOqnztwPyJJrYJ196LLfrna06D3UT9nnfaU3weN9WVU9gUCQvw',
                'setting_group' => 'payment',
                'display_name' => 'Clave Pública (Producción)',
                'description' => 'Clave pública de Recurrente para modo de producción',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'setting_key' => 'recurrente_secret_key_live',
                'setting_value' => '*****************************************************************',
                'setting_group' => 'payment',
                'display_name' => 'Clave Secreta (Producción)',
                'description' => 'Clave secreta de Recurrente para modo de producción',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insertar las nuevas configuraciones
        foreach ($newSettings as $setting) {
            // Verificar si ya existe
            $existing = $this->db->table('system_settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();

            if (!$existing) {
                $this->db->table('system_settings')->insert($setting);
            }
        }

        // Migrar las claves existentes a las nuevas de test si existen
        $existingPublic = $this->db->table('system_settings')
            ->where('setting_key', 'recurrente_public_key')
            ->get()
            ->getRow();

        $existingSecret = $this->db->table('system_settings')
            ->where('setting_key', 'recurrente_secret_key')
            ->get()
            ->getRow();

        if ($existingPublic && !empty($existingPublic->setting_value)) {
            $this->db->table('system_settings')
                ->where('setting_key', 'recurrente_public_key_test')
                ->update(['setting_value' => $existingPublic->setting_value]);
        }

        if ($existingSecret && !empty($existingSecret->setting_value)) {
            $this->db->table('system_settings')
                ->where('setting_key', 'recurrente_secret_key_test')
                ->update(['setting_value' => $existingSecret->setting_value]);
        }
    }

    public function down()
    {
        // Eliminar las nuevas configuraciones
        $keysToRemove = [
            'recurrente_public_key_test',
            'recurrente_secret_key_test',
            'recurrente_public_key_live',
            'recurrente_secret_key_live'
        ];

        $this->db->table('system_settings')
            ->whereIn('setting_key', $keysToRemove)
            ->delete();
    }
}
