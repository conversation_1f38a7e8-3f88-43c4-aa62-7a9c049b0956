<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class PaymentApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Procesar pago con tarjeta usando SP
     */
    public function processCard()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'order_id' => 'required|integer',
                'card_number' => 'required|min_length[13]|max_length[19]',
                'card_holder' => 'required|max_length[100]',
                'expiry_month' => 'required|integer|greater_than[0]|less_than[13]',
                'expiry_year' => 'required|integer|greater_than[2024]',
                'cvv' => 'required|min_length[3]|max_length[4]',
                'amount' => 'required|decimal|greater_than[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            // Enmascarar número de tarjeta para logs
            $maskedCard = str_repeat('*', strlen($json['card_number']) - 4) . substr($json['card_number'], -4);
            log_message('info', "Procesando pago con tarjeta terminada en: {$maskedCard}");

            $query = $this->db->query("CALL sp_process_card_payment(?, ?, ?, ?, ?, ?, ?, @transaction_id, @result)", [
                $json['order_id'],
                $json['card_number'],
                $json['card_holder'],
                $json['expiry_month'],
                $json['expiry_year'],
                $json['cvv'],
                $json['amount']
            ]);

            $result = $this->db->query("SELECT @transaction_id as transaction_id, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'transaction_id' => $result->transaction_id,
                        'payment_method' => 'card',
                        'card_last_four' => substr($json['card_number'], -4)
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::processCard: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Procesar transferencia bancaria usando SP
     */
    public function processBankTransfer()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'order_id' => 'required|integer',
                'bank_name' => 'required|max_length[100]',
                'account_holder' => 'required|max_length[100]',
                'reference_number' => 'required|max_length[50]',
                'amount' => 'required|decimal|greater_than[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_process_bank_transfer(?, ?, ?, ?, ?, @transaction_id, @result)", [
                $json['order_id'],
                $json['bank_name'],
                $json['account_holder'],
                $json['reference_number'],
                $json['amount']
            ]);

            $result = $this->db->query("SELECT @transaction_id as transaction_id, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'transaction_id' => $result->transaction_id,
                        'payment_method' => 'bank_transfer',
                        'reference_number' => $json['reference_number']
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::processBankTransfer: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Confirmar transferencia bancaria usando SP
     */
    public function confirmTransfer()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'transaction_id' => 'required|max_length[50]',
                'verified_amount' => 'required|decimal|greater_than[0]',
                'admin_notes' => 'permit_empty|max_length[500]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_confirm_transfer(?, ?, ?, @result)", [
                $json['transaction_id'],
                $json['verified_amount'],
                $json['admin_notes'] ?? ''
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result)
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::confirmTransfer: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener transacciones de pago usando SP
     */
    public function transactions()
    {
        try {
            $orderId = $this->request->getGet('order_id');
            $status = $this->request->getGet('status');
            $paymentMethod = $this->request->getGet('payment_method');
            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);
            $offset = ($page - 1) * $limit;

            $query = $this->db->query("CALL sp_get_payment_transactions(?, ?, ?, ?, ?)", [
                $orderId, $status, $paymentMethod, $limit, $offset
            ]);

            $transactions = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'transactions' => $transactions,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($transactions)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::transactions: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener transacción específica
     */
    public function getTransaction($transactionId = null)
    {
        try {
            if (!$transactionId) {
                return $this->fail('ID de transacción requerido', 400);
            }

            $query = $this->db->query("
                SELECT 
                    pt.*,
                    o.order_number,
                    o.customer_name,
                    o.customer_email,
                    o.total as order_total
                FROM payment_transactions pt
                INNER JOIN orders o ON o.id = pt.order_id
                WHERE pt.transaction_id = ?
            ", [$transactionId]);

            $transaction = $query->getRowArray();

            if (!$transaction) {
                return $this->failNotFound('Transacción no encontrada');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $transaction
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::getTransaction: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Simular pago PayPal
     */
    public function simulatePayPal()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['order_id']) || !isset($json['amount'])) {
                return $this->fail('order_id y amount requeridos', 400);
            }

            $orderId = (int) $json['order_id'];
            $amount = (float) $json['amount'];

            // Simular procesamiento PayPal
            $transactionId = 'PAYPAL_' . uniqid() . '_' . $orderId;
            
            // Insertar transacción simulada
            $this->db->query("
                INSERT INTO payment_transactions (
                    order_id, transaction_id, payment_method, amount, status,
                    response_code, response_message, created_at
                ) VALUES (?, ?, 'paypal', ?, 'completed', 'SUCCESS', 'Pago PayPal simulado', NOW())
            ", [$orderId, $transactionId, $amount]);

            // Actualizar pedido
            $this->db->query("
                UPDATE orders 
                SET payment_status = 'paid', 
                    payment_method = 'paypal',
                    status = 'processing',
                    updated_at = NOW()
                WHERE id = ?
            ", [$orderId]);

            return $this->respond([
                'status' => 'success',
                'message' => 'Pago PayPal procesado correctamente',
                'data' => [
                    'transaction_id' => $transactionId,
                    'payment_method' => 'paypal'
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::simulatePayPal: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener métodos de pago disponibles
     */
    public function methods()
    {
        try {
            // Obtener información del carrito para validar Recurrente
            $cartTotal = $this->request->getGet('cart_total');
            $cartCurrency = $this->request->getGet('cart_currency') ?? 'GTQ';

            $methods = [
                [
                    'id' => 'card',
                    'name' => 'Tarjeta de Crédito/Débito',
                    'description' => 'Visa, MasterCard, American Express',
                    'enabled' => true,
                    'fee_percentage' => 3.5
                ],
                [
                    'id' => 'bank_transfer',
                    'name' => 'Transferencia Bancaria',
                    'description' => 'Transferencia a cuenta bancaria',
                    'enabled' => true,
                    'fee_percentage' => 0
                ],
                [
                    'id' => 'paypal',
                    'name' => 'PayPal',
                    'description' => 'Pago seguro con PayPal',
                    'enabled' => true,
                    'fee_percentage' => 4.0
                ],
                [
                    'id' => 'cash_on_delivery',
                    'name' => 'Contra Entrega',
                    'description' => 'Pago al recibir el producto',
                    'enabled' => true,
                    'fee_percentage' => 0
                ],
                [
                    'id' => 'recurrente',
                    'name' => 'Recurrente',
                    'description' => 'Pago seguro con tarjeta de crédito/débito',
                    'enabled' => $this->isRecurrenteEligible($cartTotal, $cartCurrency),
                    'fee_percentage' => $this->getRecurrenteFeePercentage(),
                    'price_limit_message' => $this->getRecurrentePriceLimitMessage($cartTotal, $cartCurrency)
                ]
            ];

            return $this->respond([
                'status' => 'success',
                'data' => $methods
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::methods: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Verificar si Recurrente está habilitado y es elegible para el carrito
     */
    private function isRecurrenteEligible($cartTotal = null, $cartCurrency = 'GTQ')
    {
        try {
            // Primero verificar si Recurrente está habilitado
            if (!$this->isRecurrenteEnabled()) {
                return false;
            }

            // Si no se proporciona total del carrito, solo verificar si está habilitado
            if ($cartTotal === null) {
                return true;
            }

            // Verificar límite de precio de Recurrente ($15,000 USD)
            return $this->isWithinRecurrentePriceLimit($cartTotal, $cartCurrency);

        } catch (\Exception $e) {
            log_message('error', 'Error verificando elegibilidad de Recurrente: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verificar si Recurrente está habilitado
     */
    private function isRecurrenteEnabled()
    {
        try {
            $db = \Config\Database::connect();
            $result = $db->query("
                SELECT setting_value
                FROM system_settings
                WHERE setting_key = 'recurrente_enabled'
                AND is_active = 1
            ")->getRow();

            return $result && $result->setting_value === '1';
        } catch (\Exception $e) {
            log_message('error', 'Error verificando si Recurrente está habilitado: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verificar si el precio está dentro del límite de Recurrente
     */
    private function isWithinRecurrentePriceLimit($amount, $currency = 'GTQ')
    {
        $maxPriceUSD = 15000; // Límite de Recurrente
        $amountInUSD = $amount;

        // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
        if ($currency === 'GTQ') {
            $amountInUSD = $amount / 7.8;
        }

        return $amountInUSD <= $maxPriceUSD;
    }

    /**
     * Obtener mensaje sobre límite de precio de Recurrente
     */
    private function getRecurrentePriceLimitMessage($cartTotal = null, $cartCurrency = 'GTQ')
    {
        if ($cartTotal === null) {
            return null;
        }

        if (!$this->isWithinRecurrentePriceLimit($cartTotal, $cartCurrency)) {
            $maxPriceUSD = 15000;
            $maxPriceGTQ = $maxPriceUSD * 7.8;

            if ($cartCurrency === 'GTQ') {
                return "El total del carrito excede el límite de Q" . number_format($maxPriceGTQ, 0) . " para pagos con Recurrente.";
            } else {
                return "El total del carrito excede el límite de $" . number_format($maxPriceUSD, 0) . " USD para pagos con Recurrente.";
            }
        }

        return null;
    }

    /**
     * Validar elegibilidad de Recurrente para un carrito específico
     */
    public function validateRecurrente()
    {
        try {
            $json = $this->request->getJSON(true);

            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'cart_total' => 'required|decimal|greater_than[0]',
                'currency' => 'permit_empty|in_list[GTQ,USD]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $cartTotal = $json['cart_total'];
            $currency = $json['currency'] ?? 'GTQ';

            $eligible = $this->isRecurrenteEligible($cartTotal, $currency);
            $message = $eligible ? null : $this->getRecurrentePriceLimitMessage($cartTotal, $currency);

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'eligible' => $eligible,
                    'cart_total' => $cartTotal,
                    'currency' => $currency,
                    'message' => $message,
                    'limit_usd' => 15000,
                    'limit_gtq' => 117000
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en PaymentApiSP::validateRecurrente: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener porcentaje de comisión de Recurrente
     */
    private function getRecurrenteFeePercentage()
    {
        try {
            $db = \Config\Database::connect();
            $result = $db->query("
                SELECT setting_value
                FROM system_settings
                WHERE setting_key = 'recurrente_fee_percentage'
                AND is_active = 1
            ")->getRow();

            return $result ? floatval($result->setting_value) : 3.9;
        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo porcentaje de comisión de Recurrente: ' . $e->getMessage());
            return 3.9;
        }
    }
}
