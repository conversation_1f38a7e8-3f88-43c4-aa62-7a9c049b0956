<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Load the system's routing file first
if (is_file(SYSTEMPATH . 'Config/Routes.php')) {
    require SYSTEMPATH . 'Config/Routes.php';
}

// Router Setup - only if $routes is available
if (isset($routes)) {
    $routes->setDefaultNamespace('App\Controllers');
    $routes->setDefaultController('Home');
    $routes->setDefaultMethod('index');
    $routes->setTranslateURIDashes(false);
    $routes->set404Override();
    $routes->setAutoRoute(false);

    // ========================================
    // MAIN ROUTES
    // ========================================

    // Home routes
    $routes->get('/', 'Home::index');
    $routes->get('dashboard', 'Home::dashboard');

    // Temporary route for database updates (remove in production)
    // $routes->get('DatabaseUpdate/addShippingFields', 'DatabaseUpdate::addShippingFields');
    // $routes->get('DatabaseUpdate/updateProductShippingData', 'DatabaseUpdate::updateProductShippingData');
    // $routes->get('DatabaseUpdate/createInstallmentTables', 'DatabaseUpdate::createInstallmentTables');
    // $routes->get('DatabaseUpdate/createSystemSettingsTable', 'DatabaseUpdate::createSystemSettingsTable');
    // $routes->get('DatabaseUpdate/toggleTax', 'DatabaseUpdate::toggleTax');

    // Temporary route to check admin account
    $routes->get('check-admin', 'DatabaseUpdate::checkAdmin');
    $routes->get('debug-admin-login', 'DatabaseUpdate::debugAdminLogin');
    $routes->get('test-admin-auth', 'Admin\AdminController::testAuth');
    $routes->get('test-admin-authenticate', 'Admin\AdminController::testAuthenticate');
    $routes->match(['get', 'post'], 'simple-admin-login', 'Admin\AdminController::simpleLogin');
    $routes->get('debug-session', 'DatabaseUpdate::debugSession');
    $routes->get('test-dashboard', 'DatabaseUpdate::testDashboard');
    $routes->get('debug-admin-auth', 'DatabaseUpdate::debugAdminAuth');
    $routes->get('update-admin-password', 'DatabaseUpdate::updateAdminPassword');
    $routes->get('simulate-admin-auth', 'DatabaseUpdate::simulateAdminAuth');
    $routes->get('test-direct-auth', 'DatabaseUpdate::testDirectAuth');



    // Rutas públicas del e-commerce (manteniendo las originales)

    // ========================================
    // Servir archivos de media
    $routes->get('writable/uploads/media/(:any)', function($filename) {
        $filepath = WRITEPATH . 'uploads/media/' . $filename;
        if (file_exists($filepath)) {
            $mime = mime_content_type($filepath);
            header('Content-Type: ' . $mime);
            header('Content-Length: ' . filesize($filepath));
            readfile($filepath);
            exit;
        } else {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
    });

    // ADMIN ROUTES
    // ========================================

    $routes->group('admin', function ($routes) {
        // Public routes (no authentication required)
        $routes->get('login', 'Admin\AdminController::login');
        $routes->post('login', 'Admin\AdminController::authenticate');
        $routes->post('authenticate', 'Admin\AdminController::authenticate');
        $routes->get('logout', 'Admin\AdminController::logout');
        $routes->get('test-route', 'Admin\AdminController::testRoute');

    });

    // Protected admin routes (require authentication)
    $routes->group('admin', ['filter' => 'adminauth'], function ($routes) {
        $routes->get('/', 'Admin\AdminController::dashboard');
        $routes->get('dashboard', 'Admin\AdminController::dashboard');
        $routes->post('keep-alive', 'Admin\AdminController::keepAlive');
        $routes->get('products', 'Admin\AdminController::products');
        $routes->get('products/view/(:num)', 'Admin\AdminController::viewProduct/$1');
        $routes->get('products/create', 'Admin\AdminController::createProduct');
        $routes->post('products/create', 'Admin\AdminController::createProduct');
        $routes->get('products/edit/(:num)', 'Admin\AdminController::editProduct/$1');
        $routes->post('products/edit/(:num)', 'Admin\AdminController::editProduct/$1');
        $routes->post('products/duplicate/(:num)', 'Admin\AdminController::duplicateProduct/$1');
        $routes->post('products/delete/(:num)', 'Admin\AdminController::deleteProduct/$1');
        $routes->post('products/remove-image', 'Admin\AdminController::removeProductImage');
        $routes->post('products/remove-featured-image', 'Admin\AdminController::removeFeaturedImage');
        $routes->get('products/(:num)/variants', 'Admin\AdminController::getProductVariants/$1');
        $routes->get('orders', 'Admin\AdminController::orders');
        $routes->get('orders/view/(:num)', 'Admin\AdminController::viewOrder/$1');
        $routes->get('orders/edit/(:num)', 'Admin\AdminController::editOrder/$1');
        $routes->post('orders/update/(:num)', 'Admin\AdminController::updateOrder/$1');
        $routes->get('users', 'Admin\AdminController::users');
        $routes->get('users/create', 'Admin\AdminController::createUser');
        $routes->post('users/create', 'Admin\AdminController::createUser');
        $routes->get('users/view/(:num)', 'Admin\AdminController::viewUser/$1');
        $routes->get('users/edit/(:num)', 'Admin\AdminController::editUser/$1');
        $routes->post('users/edit/(:num)', 'Admin\AdminController::editUser/$1');
        $routes->get('users/activity/(:num)', 'Admin\AdminController::getUserActivity/$1');
        $routes->post('users/toggle-status/(:num)', 'Admin\AdminController::toggleUserStatus/$1');
        $routes->post('users/reset-password/(:num)', 'Admin\AdminController::resetPassword/$1');
        $routes->post('users/delete/(:num)', 'Admin\AdminController::deleteUser/$1');

        // Setup routes (temporal)
        $routes->get('setup', 'Admin\SetupController::index');
        $routes->get('setup/run', 'Admin\SetupController::run');



        $routes->get('categories', 'Admin\AdminController::categories');
        $routes->get('categories/create', 'Admin\AdminController::createCategory');
        $routes->post('categories/create', 'Admin\AdminController::createCategory');
        $routes->get('categories/edit/(:num)', 'Admin\AdminController::editCategory/$1');
        $routes->post('categories/edit/(:num)', 'Admin\AdminController::editCategory/$1');
        $routes->post('categories/delete/(:num)', 'Admin\AdminController::deleteCategory/$1');

        // Rutas para gestión de marcas
        $routes->get('brands', 'Admin\BrandsController::index');
        $routes->get('brands/create', 'Admin\BrandsController::create');
        $routes->post('brands/create', 'Admin\BrandsController::create');
        $routes->get('brands/edit/(:num)', 'Admin\BrandsController::edit/$1');
        $routes->post('brands/edit/(:num)', 'Admin\BrandsController::edit/$1');
        $routes->post('brands/delete/(:num)', 'Admin\BrandsController::delete/$1');
        $routes->get('brands/toggle-status/(:num)', 'Admin\BrandsController::toggleStatus/$1');
        $routes->get('inventory', 'Admin\AdminController::inventory');
        $routes->get('reports', 'Admin\AdminController::reports');
        $routes->get('notifications', 'Admin\AdminController::notifications');

        // Contact messages routes
        $routes->get('contact', 'Admin\ContactController::index');

        // Optimization routes
        $routes->get('optimization', 'Admin\OptimizationController::index');
        $routes->get('optimization/seo', 'Admin\OptimizationController::seo');
        $routes->get('optimization/assets', 'Admin\OptimizationController::assets');
        $routes->get('optimization/performance', 'Admin\OptimizationController::performance');

        // SEO optimization
        $routes->post('optimization/generate-sitemap', 'Admin\OptimizationController::generateSitemap');
        $routes->post('optimization/generate-robots', 'Admin\OptimizationController::generateRobots');
        $routes->post('optimization/analyze-page', 'Admin\OptimizationController::analyzePage');

        // Asset optimization
        $routes->post('optimization/optimize-css', 'Admin\OptimizationController::optimizeCSS');
        $routes->post('optimization/optimize-js', 'Admin\OptimizationController::optimizeJS');
        $routes->post('optimization/optimize-images', 'Admin\OptimizationController::optimizeImages');
        $routes->post('optimization/combine-css', 'Admin\OptimizationController::combineCSS');
        $routes->post('optimization/combine-js', 'Admin\OptimizationController::combineJS');
        $routes->post('optimization/generate-webp', 'Admin\OptimizationController::generateWebP');
        $routes->post('optimization/clear-asset-cache', 'Admin\OptimizationController::clearAssetCache');

        // Performance optimization
        $routes->post('optimization/run-full-optimization', 'Admin\OptimizationController::runFullOptimization');

        // Logs management
        $routes->get('logs', 'Admin\LogsController::index');
        $routes->get('logs/view', 'Admin\LogsController::view');
        $routes->get('logs/realtime', 'Admin\LogsController::realtime');
        $routes->get('logs/settings', 'Admin\LogsController::settings');
        $routes->get('logs/detail/(:num)', 'Admin\LogsController::viewLog/$1');
        $routes->get('logs/stream', 'Admin\LogsController::stream');
        $routes->get('logs/download/(:any)', 'Admin\LogsController::download/$1');
        $routes->post('logs/get-logs', 'Admin\LogsController::getLogs');
        $routes->post('logs/get-stats', 'Admin\LogsController::getStats');
        $routes->post('logs/cleanup', 'Admin\LogsController::cleanup');
        $routes->post('logs/export', 'Admin\LogsController::export');
        $routes->post('logs/test-log', 'Admin\LogsController::testLog');
        $routes->post('logs/analyze', 'Admin\LogsController::analyze');
        $routes->post('logs/update-settings', 'Admin\LogsController::updateSettings');

        // System management
        $routes->get('system', 'Admin\SystemController::index');
        $routes->get('system/status', 'Admin\SystemController::status');
        $routes->get('system/info', 'Admin\SystemController::info');
        $routes->get('system/metrics', 'Admin\SystemController::metrics');
        $routes->get('system/config', 'Admin\SystemController::config');
        $routes->post('system/restart', 'Admin\SystemController::restart');
        $routes->post('system/maintenance', 'Admin\SystemController::maintenance');
        $routes->post('system/update-config', 'Admin\SystemController::updateConfig');
        $routes->post('system/generate-report', 'Admin\SystemController::generateReport');

        // API Routes
        $routes->group('api/v1', ['namespace' => 'App\Controllers\Api\V1'], function($routes) {
            $routes->get('status', 'ApiController::status');
            $routes->get('info', 'ApiController::info');
            $routes->get('docs', 'ApiController::documentation');
            $routes->get('usage', 'ApiController::usage');

            $routes->post('keys/generate', 'ApiController::generateKey');
            $routes->delete('keys/revoke', 'ApiController::revokeKey');

            $routes->resource('products', ['controller' => 'ApiController::products']);
            $routes->resource('orders', ['controller' => 'ApiController::orders']);
            $routes->resource('users', ['controller' => 'ApiController::users']);
            $routes->resource('categories', ['controller' => 'ApiController::categories']);
            $routes->get('stats', 'ApiController::stats');
        });

        // Blockchain management
        $routes->get('blockchain', 'Admin\BlockchainController::index');
        $routes->get('blockchain/crypto', 'Admin\BlockchainController::crypto');
        $routes->get('blockchain/nfts', 'Admin\BlockchainController::nfts');
        $routes->get('blockchain/loyalty-tokens', 'Admin\BlockchainController::loyaltyTokens');
        $routes->get('blockchain/settings', 'Admin\BlockchainController::settings');
        $routes->get('blockchain/stats', 'Admin\BlockchainController::stats');
        $routes->get('blockchain/prices', 'Admin\BlockchainController::getCryptoPrices');
        $routes->post('blockchain/process-payment', 'Admin\BlockchainController::processCryptoPayment');
        $routes->get('blockchain/check-transaction/(:any)', 'Admin\BlockchainController::checkTransaction/$1');
        $routes->post('blockchain/create-nft', 'Admin\BlockchainController::createNFT');
        $routes->post('blockchain/award-tokens', 'Admin\BlockchainController::awardTokens');
        $routes->post('blockchain/update-settings', 'Admin\BlockchainController::updateSettings');
        $routes->post('blockchain/export', 'Admin\BlockchainController::export');

        // Coupons management
        $routes->get('coupons', 'Admin\CouponsController::index');
        $routes->get('coupons/list', 'Admin\CouponsController::coupons');
        $routes->get('coupons/create', 'Admin\CouponsController::create');
        $routes->post('coupons/store', 'Admin\CouponsController::store');
        $routes->get('coupons/edit/(:num)', 'Admin\CouponsController::edit/$1');
        $routes->post('coupons/update/(:num)', 'Admin\CouponsController::update/$1');
        $routes->delete('coupons/delete/(:num)', 'Admin\CouponsController::delete/$1');
        $routes->get('coupons/bulk-discounts', 'Admin\CouponsController::bulkDiscounts');
        $routes->post('coupons/create-bulk-discount', 'Admin\CouponsController::createBulkDiscount');
        $routes->get('coupons/campaigns', 'Admin\CouponsController::campaigns');
        $routes->post('coupons/create-campaign', 'Admin\CouponsController::createCampaign');
        $routes->get('coupons/stats', 'Admin\CouponsController::stats');
        $routes->post('coupons/validate', 'Admin\CouponsController::validateCoupon');
        $routes->get('coupons/generate-code', 'Admin\CouponsController::generateCode');
        $routes->post('coupons/export', 'Admin\CouponsController::export');

        // Shipping management
        $routes->get('shipping', 'Admin\ShippingController::index');
        $routes->get('shipping/shipments', 'Admin\ShippingController::shipments');
        $routes->get('shipping/create/(:num)', 'Admin\ShippingController::create/$1');
        $routes->get('shipping/create', 'Admin\ShippingController::create');
        $routes->post('shipping/store', 'Admin\ShippingController::store');
        $routes->get('shipping/view/(:num)', 'Admin\ShippingController::view/$1');
        $routes->post('shipping/update-status', 'Admin\ShippingController::updateStatus');
        $routes->get('shipping/rates', 'Admin\ShippingController::rates');
        $routes->post('shipping/update-rate', 'Admin\ShippingController::updateRate');
        $routes->post('shipping/create-rate', 'Admin\ShippingController::createRate');
        $routes->post('shipping/calculate-cost', 'Admin\ShippingController::calculateCost');
        $routes->get('shipping/stats', 'Admin\ShippingController::stats');
        $routes->post('shipping/export', 'Admin\ShippingController::export');

        // TEMPLATES DE WHATSAPP - NUEVO SISTEMA
        $routes->get('whatsapp-templates', 'Admin\WhatsAppTemplates::index');
        $routes->get('whatsapp-templates/create', 'Admin\WhatsAppTemplates::create');
        $routes->post('whatsapp-templates/create', 'Admin\WhatsAppTemplates::store');
        $routes->get('whatsapp-templates/edit/(:num)', 'Admin\WhatsAppTemplates::edit/$1');
        $routes->post('whatsapp-templates/edit/(:num)', 'Admin\WhatsAppTemplates::update/$1');
        $routes->delete('whatsapp-templates/(:num)', 'Admin\WhatsAppTemplates::delete/$1');
        $routes->get('whatsapp-templates/test/(:num)', 'Admin\WhatsAppTemplates::test/$1');
        $routes->post('whatsapp-templates/test/(:num)', 'Admin\WhatsAppTemplates::test/$1');
        $routes->get('whatsapp-templates/settings', 'Admin\WhatsAppTemplates::settings');
        $routes->post('whatsapp-templates/settings', 'Admin\WhatsAppTemplates::saveSettings');

        // SISTEMA MÓVIL Y PWA - NUEVO
        $routes->get('mobile-stats', 'Admin\MobileStats::index');
        $routes->get('mobile-stats/analytics', 'Admin\MobileStats::analytics');
        $routes->get('mobile-stats/push-notifications', 'Admin\MobileStats::pushNotifications');
        $routes->get('contact/dashboard', 'Admin\ContactController::dashboard');
        $routes->get('contact/view/(:num)', 'Admin\ContactController::view/$1');
        $routes->post('contact/update-status/(:num)', 'Admin\ContactController::updateStatus/$1');
        $routes->delete('contact/delete/(:num)', 'Admin\ContactController::delete/$1');
        $routes->get('contact/export', 'Admin\ContactController::export');
        $routes->get('shipping', 'Admin\AdminController::shipping');
        $routes->get('settings', 'Admin\AdminController::settings');
        $routes->post('settings', 'Admin\AdminController::settings');

        // System Settings Routes
        $routes->get('system-settings', 'Admin\Settings::index');
        $routes->get('system-settings/taxes', 'Admin\Settings::taxes');
        $routes->post('system-settings/updateTaxes', 'Admin\Settings::updateTaxes');
        $routes->post('system-settings/update', 'Admin\Settings::update');
        $routes->post('system-settings/updateRecurrente', 'Admin\Settings::updateRecurrente');
        $routes->get('system-settings/getValue/(:segment)', 'Admin\Settings::getSettingValue/$1');

        // Rutas para gestión de métodos de pago
        $routes->get('payment-methods', 'Admin\PaymentMethodsController::index');
        $routes->post('payment-methods/store', 'Admin\PaymentMethodsController::store');
        $routes->get('payment-methods/view/(:num)', 'Admin\PaymentMethodsController::view/$1');
        $routes->get('payment-methods/toggle-status/(:num)', 'Admin\PaymentMethodsController::toggleStatus/$1');

        // Rutas para cuentas bancarias
        $routes->get('payment-methods/add-bank-account/(:num)', 'Admin\PaymentMethodsController::addBankAccount/$1');
        $routes->post('payment-methods/add-bank-account/(:num)', 'Admin\PaymentMethodsController::addBankAccount/$1');
        $routes->get('payment-methods/edit-bank-account/(:num)', 'Admin\PaymentMethodsController::editBankAccount/$1');
        $routes->post('payment-methods/edit-bank-account/(:num)', 'Admin\PaymentMethodsController::editBankAccount/$1');
        $routes->get('payment-methods/delete-bank-account/(:num)', 'Admin\PaymentMethodsController::deleteBankAccount/$1');

        // Rutas para puntos de recogida
        $routes->get('payment-methods/add-pickup-location/(:num)', 'Admin\PaymentMethodsController::addPickupLocation/$1');
        $routes->post('payment-methods/add-pickup-location/(:num)', 'Admin\PaymentMethodsController::addPickupLocation/$1');
        $routes->get('payment-methods/edit-pickup-location/(:num)', 'Admin\PaymentMethodsController::editPickupLocation/$1');
        $routes->post('payment-methods/edit-pickup-location/(:num)', 'Admin\PaymentMethodsController::editPickupLocation/$1');
        $routes->get('payment-methods/delete-pickup-location/(:num)', 'Admin\PaymentMethodsController::deletePickupLocation/$1');
        $routes->get('reviews', 'Admin\ReviewsController::index');
        $routes->get('reviews/stats', 'Admin\ReviewsController::getStats');
        $routes->get('reviews/all', 'Admin\ReviewsController::getAllReviews');
        $routes->get('reviews/approved', 'Admin\ReviewsController::getApprovedReviews');
        $routes->put('reviews/(:num)/approve', 'Admin\ReviewsController::approve/$1');
        $routes->delete('reviews/(:num)', 'Admin\ReviewsController::reject/$1');
        $routes->put('reviews/(:num)/response', 'Admin\ReviewsController::addResponse/$1');
        $routes->put('reviews/(:num)/featured', 'Admin\ReviewsController::toggleFeatured/$1');

        // WhatsApp Management
        $routes->group('whatsapp', function($routes) {
            $routes->get('/', 'Admin\WhatsAppController::index');
            $routes->get('settings', 'Admin\WhatsAppController::settings');
            $routes->post('settings', 'Admin\WhatsAppController::settings');
            $routes->get('templates', 'Admin\WhatsAppController::templates');
            $routes->post('templates/create', 'Admin\WhatsAppController::createTemplate');
            $routes->get('templates/edit/(:num)', 'Admin\WhatsAppController::editTemplate/$1');
            $routes->post('templates/edit/(:num)', 'Admin\WhatsAppController::editTemplate/$1');
            $routes->get('message-log', 'Admin\WhatsAppController::messageLog');
            $routes->get('test-connection', 'Admin\WhatsAppController::testConnection');
            $routes->post('test-connection', 'Admin\WhatsAppController::testConnection');
            $routes->get('send-message', 'Admin\WhatsAppController::sendMessage');
            $routes->post('send-message', 'Admin\WhatsAppController::sendMessage');
            $routes->get('statistics', 'Admin\WhatsAppController::statistics');
            $routes->get('api/stats', 'Admin\WhatsAppController::apiStats');
        });

        // Shipping API Routes
        $routes->group('api', function($routes) {
            $routes->post('calculate-shipping', 'Admin\ShippingApiController::calculateShipping');
            $routes->get('shipping/package-types', 'Admin\ShippingApiController::getPackageTypes');
            $routes->post('shipping/package-types', 'Admin\ShippingApiController::createPackageType');
            $routes->put('shipping/package-types/(:num)', 'Admin\ShippingApiController::updatePackageType/$1');
            $routes->delete('shipping/package-types/(:num)', 'Admin\ShippingApiController::deletePackageType/$1');
            $routes->get('shipping/stats', 'Admin\ShippingApiController::getShippingStats');
        });

        // Phone Validation Management
        $routes->group('phone-validation', function($routes) {
            $routes->get('/', 'Admin\PhoneValidationController::index');
            $routes->post('validate', 'Admin\PhoneValidationController::validateSingle');
            $routes->post('validate-batch', 'Admin\PhoneValidationController::validateBatch');
            $routes->post('validate-users', 'Admin\PhoneValidationController::validateExistingUsers');
            $routes->get('stats', 'Admin\PhoneValidationController::getStats');
            $routes->get('config', 'Admin\PhoneValidationController::config');
            $routes->post('config', 'Admin\PhoneValidationController::config');
        });

    });

    // ========================================
    // FRONTEND ROUTES
    // ========================================

    // Cart and Checkout Routes
    $routes->get('carrito', 'CartController::index');
    $routes->post('carrito/calculate-shipping', 'CartController::calculateShipping');
    $routes->post('carrito/vaciar', 'CartController::clear'); // Vaciar carrito
    $routes->get('carrito/vaciar', 'CartController::clear'); // Vaciar carrito (GET también)

    // New Checkout System
    $routes->get('checkout', 'CheckoutController::index');
    $routes->post('checkout/calculate-shipping', 'CheckoutController::calculateShippingForAddress');
    $routes->post('checkout/save-address', 'CheckoutController::saveAddress');
    $routes->post('checkout/process-order', 'CheckoutController::processOrder');

    // Rutas para webhooks de Recurrente (DEBEN IR ANTES que las rutas sin parámetros)
    $routes->get('checkout/success/(:segment)', 'PaymentController::recurrenteSuccess/$1');
    $routes->get('checkout/cancel/(:segment)', 'PaymentController::recurrenteCancel/$1');

    // Rutas de checkout sin parámetros (van después)
    $routes->get('checkout/confirmation/(:any)', 'CheckoutController::confirmation/$1');
    $routes->get('checkout/success', 'CheckoutController::success');
    $routes->get('checkout/cancel', 'CheckoutController::cancel');
    $routes->get('checkout/error', 'CheckoutController::error');

    // Recurrente payment routes (Frontend)
    $routes->post('payment/recurrente/process', 'RecurrenteController::processPayment');
    $routes->get('payment/recurrente/error', 'RecurrenteController::error');

    // Payment routes
    $routes->get('payment/process/(:num)', 'PaymentController::process/$1');
    $routes->post('payment/confirm-transfer', 'PaymentController::confirmTransfer');
    $routes->post('payment/card/process', 'PaymentController::processCardPayment');
    $routes->post('payment/paypal/confirm', 'PaymentController::confirmPayPal');
    $routes->post('payment/paypal/simulate', 'PaymentController::simulatePayPal');
    $routes->get('payment/success/(:num)', 'PaymentController::success/$1');
    // Rutas de respaldo para Recurrente (en caso de que las principales no funcionen)
    $routes->get('payment/recurrente/success/(:segment)', 'PaymentController::recurrenteSuccess/$1');
    $routes->get('payment/recurrente/cancel/(:segment)', 'PaymentController::recurrenteCancel/$1');

    // User Account routes (fixed)
    $routes->get('cuenta', 'UserAccountController::dashboard');
    $routes->get('cuenta/pedidos', 'UserAccountController::orders');
    $routes->get('cuenta/pedidos/(:num)', 'UserAccountController::orderDetail/$1');
    $routes->get('cuenta/wishlist', 'UserAccountController::wishlist');
    $routes->get('cuenta/direcciones', 'UserAccountController::addresses');
    $routes->post('cuenta/direcciones', 'UserAccountController::saveAddress');
    $routes->post('cuenta/direcciones/set-default', 'UserAccountController::setDefaultAddress');
    $routes->post('cuenta/direcciones/delete', 'UserAccountController::deleteAddress');
    $routes->get('cuenta/direcciones/edit/(:num)', 'UserAccountController::editAddress/$1');
    $routes->post('cuenta/direcciones/update/(:num)', 'UserAccountController::updateAddress/$1');
    $routes->get('cuenta/perfil', 'UserAccountController::profile');
    $routes->post('cuenta/perfil', 'UserAccountController::updateProfile');
    $routes->get('cuenta/seguridad', 'UserAccountController::security');
    $routes->post('cuenta/cambiar-password', 'UserAccountController::changePassword');

    // Phone verification routes
    $routes->group('phone-verification', function($routes) {
        $routes->get('modal', 'PhoneVerificationController::showModal');
        $routes->post('send-code', 'PhoneVerificationController::sendCode');
        $routes->post('verify-code', 'PhoneVerificationController::verifyCode');
        $routes->get('status', 'PhoneVerificationController::checkStatus');
        $routes->post('toggle-notifications', 'PhoneVerificationController::toggleNotifications');
    });

    // Mantener compatibilidad con la ruta anterior
    //$routes->get('cuenta', 'UserAccountController::dashboard');
    //$routes->get('cuenta/(:any)', 'UserAccountController::dashboard'); // Redirigir cualquier subruta antigua

    // Rutas de compatibilidad (mantener las existentes)
    $routes->get('perfil', 'UserControllerSP::profile');
    $routes->post('perfil', 'UserControllerSP::updateProfile');
    $routes->get('mis-pedidos', 'UserControllerSP::orders');
    $routes->get('login', 'UserControllerSP::login');
    $routes->post('login', 'SimpleLoginController::authenticate');
    $routes->get('test-simple-login', 'SimpleLoginController::test');
    $routes->post('test-simple-post', 'SimpleLoginController::testPost');
    $routes->get('register', 'RegisterController::index');
    $routes->post('register', 'RegisterController::store');
    $routes->get('registro-exitoso', 'RegisterController::success');
    $routes->get('api/detect-country', 'RegisterController::detectCountry');
    $routes->get('debug/country-detection', 'RegisterController::debugCountryDetection');

    $routes->get('debug-db', 'UserControllerSP::testDebug');
    $routes->get('test-simple', 'TestController::index');
    $routes->get('verify-phone', 'UserControllerSP::verifyPhone');
    $routes->post('verify-phone', 'UserControllerSP::processPhoneVerification', ['filter' => 'ratelimit:login']);
    $routes->get('logout', 'UserControllerSP::logout');

    // Shop routes
    $routes->get('tienda', 'Frontend::shop');
    $routes->get('producto/(:segment)', 'Shop::product/$1');
    $routes->get('producto/id/(:num)', 'Shop::product/$1'); // Ruta de respaldo por ID
    $routes->get('contacto', 'Frontend::contact');
    $routes->post('contacto', 'Frontend::sendContact', ['filter' => 'ratelimit:contact']);

    // API routes for variants
    $routes->get('api/variant/(:num)', 'Shop::getVariantData/$1');

    // Additional routes for navigation consistency
    $routes->get('categories', 'Shop::categories');
    $routes->get('contact', 'Frontend::contact');



    // ========================================
    // API ROUTES
    // ========================================

    $routes->group('api', function($routes) {
        // NUEVAS APIs OPTIMIZADAS para cPanel
        $routes->get('search', 'Api\SearchApiOptimized::index');
        $routes->get('search/suggestions', 'Api\SearchApiOptimized::suggestions');
        $routes->get('search/filters', 'Api\SearchApiOptimized::filters');

        // BÚSQUEDA AVANZADA - SISTEMA INTELIGENTE COMPLETO
        $routes->get('search/advanced', 'Api\AdvancedSearchApi::search');
        $routes->get('search/smart-filters', 'Api\AdvancedSearchApi::filters');
        $routes->get('search/smart-suggestions', 'Api\AdvancedSearchApi::suggestions');
        $routes->post('search/voice', 'Api\AdvancedSearchApi::voice');
        $routes->get('search/analytics', 'Api\AdvancedSearchApi::analytics');

        // Wishlist API - NUEVA FUNCIONALIDAD COMPLETA
        $routes->get('wishlist', 'Api\WishlistApi::index');
        $routes->post('wishlist', 'Api\WishlistApi::create');
        $routes->put('wishlist/(:num)', 'Api\WishlistApi::update/$1');
        $routes->delete('wishlist/(:num)', 'Api\WishlistApi::delete/$1');
        $routes->get('wishlist/check/(:num)', 'Api\WishlistApi::check/$1');
        $routes->get('wishlist/count', 'Api\WishlistApi::count');

        // Product API with Stored Procedures (mantener compatibilidad)
        $routes->get('products', 'Api\ProductApiSP::index');
        $routes->get('products/featured', 'Api\ProductApiSP::featured');
        $routes->get('products/search', 'Api\ProductApiSP::search'); // Deprecated - usar /api/search
        $routes->get('products/category/(:num)', 'Api\ProductApiSP::byCategory/$1');
        $routes->get('products/(:num)', 'Api\ProductApiSP::show/$1');
        $routes->post('products', 'Api\ProductApiSP::create');
        $routes->put('products/(:num)', 'Api\ProductApiSP::update/$1');

        // Simple Cart API (Session-based) - PRIORITY ROUTES
        $routes->get('cart/count', 'Api\CartController::count');
        $routes->get('cart', 'Api\CartController::items');
        $routes->post('cart/add', 'Api\CartController::add');
        $routes->post('cart/update', 'Api\CartController::update');
        $routes->delete('cart/remove/(:num)', 'Api\CartController::remove/$1');

        // Cart API with Stored Procedures (alternative endpoints)
        $routes->get('cart-sp/items', 'Api\CartApiSP::items');
        $routes->get('cart-sp/totals', 'Api\CartApiSP::totals');
        $routes->post('cart-sp/add', 'Api\CartApiSP::add');
        $routes->put('cart-sp/update', 'Api\CartApiSP::updateItem');
        $routes->delete('cart-sp/remove/(:num)', 'Api\CartApiSP::remove/$1');
        $routes->delete('cart-sp/clear', 'Api\CartApiSP::clear');
        $routes->get('cart-sp/validate', 'Api\CartApiSP::validateCart');


        // Order API with Stored Procedures
        $routes->get('orders', 'Api\OrderApiSP::index');
        $routes->get('orders/stats', 'Api\OrderApiSP::stats');
        $routes->get('orders/recent', 'Api\OrderApiSP::recent');
        $routes->get('orders/search', 'Api\OrderApiSP::search');
        $routes->get('orders/export', 'Api\OrderApiSP::export');
        $routes->get('orders/status/(:segment)', 'Api\OrderApiSP::byStatus/$1');
        $routes->get('orders/(:num)', 'Api\OrderApiSP::show/$1');
        $routes->put('orders/(:num)/status', 'Api\OrderApiSP::updateStatus/$1');

        // Payment API with Stored Procedures
        $routes->get('payments/methods', 'Api\PaymentApiSP::methods');
        $routes->get('payments/transactions', 'Api\PaymentApiSP::transactions');
        $routes->get('payments/transactions/(:segment)', 'Api\PaymentApiSP::getTransaction/$1');
        $routes->post('payments/card', 'Api\PaymentApiSP::processCard');
        $routes->post('payments/transfer', 'Api\PaymentApiSP::processBankTransfer');
        $routes->post('payments/transfer/confirm', 'Api\PaymentApiSP::confirmTransfer');
        $routes->post('payments/paypal/simulate', 'Api\PaymentApiSP::simulatePayPal');
        $routes->post('payments/recurrente/validate', 'Api\PaymentApiSP::validateRecurrente');

        // Recurrente API
        $routes->post('payments/recurrente/checkout', 'Api\RecurrenteCheckoutController::createCheckout');
        $routes->get('payments/recurrente/checkout/(:segment)', 'Api\RecurrenteCheckoutController::getCheckoutStatus/$1');
        $routes->post('payments/recurrente/process', 'Api\RecurrenteApiController::processPayment');
        $routes->post('webhooks/recurrente', 'Api\RecurrenteApiController::webhook');

        // Shipping API with Stored Procedures
        $routes->get('shipping/methods', 'Api\ShippingApiSP::methods');
        $routes->post('shipping/methods', 'Api\ShippingApiSP::createMethod');
        $routes->put('shipping/methods/(:num)', 'Api\ShippingApiSP::updateMethod/$1');
        $routes->get('shipping/methods/(:num)/rates', 'Api\ShippingApiSP::getRatesByMethod/$1');
        $routes->post('shipping/rates', 'Api\ShippingApiSP::createRate');
        $routes->get('shipping/calculate', 'Api\ShippingApiSP::calculateCost');

        // Cart API
        $routes->get('cart/count', 'Api\CartController::count');
        $routes->get('cart/items', 'Api\CartController::items');
        $routes->post('cart/add', 'Api\CartController::add');
        $routes->post('cart/update', 'Api\CartController::update');
        $routes->delete('cart/remove/(:segment)', 'Api\CartController::remove/$1');
        $routes->delete('cart/clear', 'Api\CartController::clear');

        // Wishlist API
        $routes->post('wishlist/toggle', 'Api\WishlistController::toggle');
        $routes->get('wishlist', 'Api\WishlistController::index');
        $routes->get('wishlist/check/(:num)', 'Api\WishlistController::check/$1');
        $routes->delete('wishlist/remove/(:num)', 'Api\WishlistController::remove/$1');

        // User addresses API
        $routes->get('user/addresses', 'Api\AddressController::getUserAddresses');
        $routes->post('user/addresses/save', 'Api\AddressController::saveAddress');
        $routes->put('user/addresses/(:num)', 'Api\AddressController::updateAddress/$1');
        $routes->delete('user/addresses/(:num)', 'Api\AddressController::deleteAddress/$1');

        // Auth API
        $routes->post('auth/login', 'AuthController::login', ['filter' => 'ratelimit:login']);
        $routes->post('auth/logout', 'AuthController::logout');
        $routes->post('auth/register', 'AuthController::register', ['filter' => 'ratelimit:register']);
        $routes->get('auth/me', 'AuthController::me');

        // Categories API with SP
        $routes->get('categories/select', 'Api\CategoriesApiSP::select');
        $routes->get('categories/(:num)/breadcrumb', 'Api\CategoriesApiSP::breadcrumb/$1');
        $routes->resource('categories', ['controller' => 'Api\CategoriesApiSP']);

        // Brands API with SP
        $routes->get('brands/by-categories', 'Api\BrandsApiSP::byCategories');
        $routes->resource('brands', ['controller' => 'Api\BrandsApiSP']);

        // Phone Validation API
        $routes->post('phone/validate', 'Api\PhoneValidationApiSP::validate');
        $routes->post('phone/validate-batch', 'Api\PhoneValidationApiSP::validateBatch');

        // Inventory API with SP
        $routes->post('inventory/movement', 'Api\InventoryApiSP::registerMovement');
        $routes->get('inventory/movements', 'Api\InventoryApiSP::getMovements');
        $routes->get('inventory/alerts', 'Api\InventoryApiSP::getAlerts');
        $routes->get('inventory/stats', 'Api\InventoryApiSP::getStats');
        $routes->post('inventory/adjust', 'Api\InventoryApiSP::adjustStock');
        $routes->put('inventory/alerts/(:num)/resolve', 'Api\InventoryApiSP::resolveAlert/$1');
        $routes->get('inventory/low-stock', 'Api\InventoryApiSP::getLowStock');

        // Media API with SP
        $routes->post('media/upload', 'Api\MediaApiSP::upload');
        $routes->post('media/associate', 'Api\MediaApiSP::associate');
        $routes->get('media/entity/(:segment)/(:num)', 'Api\MediaApiSP::getEntityMedia/$1/$2');
        $routes->get('media/product/(:num)/gallery', 'Api\MediaApiSP::getProductGallery/$1');
        $routes->put('media/order', 'Api\MediaApiSP::updateOrder');
        $routes->resource('media', ['controller' => 'Api\MediaApiSP']);

        // Reports API with SP
        $routes->get('reports/sales', 'Api\ReportsApiSP::salesReport');
        $routes->get('reports/top-products', 'Api\ReportsApiSP::topProductsReport');
        $routes->get('reports/inventory', 'Api\ReportsApiSP::inventoryReport');
        $routes->get('reports/dashboard', 'Api\ReportsApiSP::dashboardMetrics');
        $routes->get('reports/export/(:segment)', 'Api\ReportsApiSP::exportReport/$1');

        // Notifications API with SP
        $routes->get('notifications/stats', 'Api\NotificationsApiSP::getStats');
        $routes->put('notifications/read-all', 'Api\NotificationsApiSP::markAllAsRead');
        $routes->put('notifications/(:num)/read', 'Api\NotificationsApiSP::markAsRead/$1');
        $routes->post('notifications/stock-alert', 'Api\NotificationsApiSP::createStockAlert');
        $routes->post('notifications/order-alert', 'Api\NotificationsApiSP::createOrderAlert');
        $routes->resource('notifications', ['controller' => 'Api\NotificationsApiSP']);

        // Users and Roles API with SP
        $routes->get('roles', 'Api\UsersApiSP::getRoles');
        $routes->get('permissions', 'Api\UsersApiSP::getPermissions');
        $routes->post('users/(:num)/roles', 'Api\UsersApiSP::assignRole/$1');
        $routes->delete('users/(:num)/roles/(:num)', 'Api\UsersApiSP::removeRole/$1/$2');
        $routes->get('users/(:num)/roles', 'Api\UsersApiSP::getUserRoles/$1');
        $routes->get('users/(:num)/permissions/check', 'Api\UsersApiSP::checkPermission/$1');
        $routes->post('users/(:num)/activity', 'Api\UsersApiSP::logActivity/$1');
        $routes->get('users/(:num)/activity', 'Api\UsersApiSP::getUserActivity/$1');

        // Public Catalog API with SP
        $routes->get('catalog/products', 'Api\CatalogApiSP::getProducts');
        $routes->get('catalog/products/(:num)', 'Api\CatalogApiSP::getProduct/$1');
        $routes->get('catalog/products/slug/(:segment)', 'Api\CatalogApiSP::getProduct/null/$1');
        $routes->get('catalog/products/(:num)/gallery', 'Api\CatalogApiSP::getProductGallery/$1');
        $routes->get('catalog/products/(:num)/related', 'Api\CatalogApiSP::getRelatedProducts/$1');
        $routes->get('catalog/categories', 'Api\CatalogApiSP::getCategories');
        $routes->get('catalog/search/suggestions', 'Api\CatalogApiSP::getSearchSuggestions');

        // Reviews API
        $routes->get('reviews/product/(:num)', 'Api\ReviewsApiController::getProductReviews/$1');
        $routes->post('reviews', 'Api\ReviewsApiController::create', ['filter' => 'ratelimit:review']);
        $routes->get('reviews/pending', 'Api\ReviewsApiController::pending');

        // Status
        $routes->get('status', 'ApiDocController::status');

        // MOBILE API - SISTEMA MÓVIL AVANZADO
        $routes->get('mobile/config', 'Api\MobileController::config');
        $routes->post('mobile/push/subscribe', 'Api\MobileController::subscribePush');
        $routes->post('mobile/push/unsubscribe', 'Api\MobileController::unsubscribePush');
        $routes->post('mobile/push/test', 'Api\MobileController::testPush');
        $routes->post('mobile/session/register', 'Api\MobileController::registerSession');
        $routes->get('mobile/image/optimized', 'Api\MobileController::optimizedImage');
        $routes->get('mobile/image/responsive', 'Api\MobileController::responsiveImage');
        $routes->get('mobile/css/critical', 'Api\MobileController::criticalCSS');
        $routes->get('mobile/js/touch-gestures', 'Api\MobileController::touchGesturesJS');
        $routes->get('mobile/stats', 'Api\MobileController::stats');

        // INTEGRATIONS API - SISTEMA DE INTEGRACIONES AVANZADO
        $routes->get('integrations/config', 'Api\IntegrationsController::config');
        $routes->post('integrations/ga4/track-event', 'Api\IntegrationsController::trackEvent');
        $routes->post('integrations/ga4/track-purchase', 'Api\IntegrationsController::trackPurchase');
        $routes->post('integrations/social/publish', 'Api\IntegrationsController::publishToSocial');
        $routes->post('integrations/social/schedule', 'Api\IntegrationsController::schedulePost');
        $routes->get('integrations/social/stats', 'Api\IntegrationsController::socialStats');
        $routes->get('integrations/system/check', 'Api\IntegrationsController::systemCheck');
        $routes->get('integrations/monitoring/stats', 'Api\IntegrationsController::monitoringStats');
        $routes->post('integrations/automation/run', 'Api\IntegrationsController::runAutomations');
        $routes->get('integrations/automation/stats', 'Api\IntegrationsController::automationStats');
        $routes->get('integrations/alerts/active', 'Api\IntegrationsController::activeAlerts');
        $routes->put('integrations/alerts/resolve/(:num)', 'Api\IntegrationsController::resolveAlert/$1');
        $routes->get('integrations/settings', 'Api\IntegrationsController::systemSettings');
        $routes->put('integrations/settings', 'Api\IntegrationsController::updateSetting');
    });

    // ADMIN ROUTES - PANEL DE ADMINISTRACIÓN
    $routes->group('admin', ['filter' => 'adminauth'], function($routes) {
        // Dashboard de automatizaciones
        $routes->get('automation', 'Admin\AutomationController::index');
        $routes->post('automation/run-task/(:segment)', 'Admin\AutomationController::runTask/$1');
        $routes->post('automation/system-check', 'Admin\AutomationController::systemCheck');

        // Configuración de cron jobs
        $routes->get('automation/cron-jobs', 'Admin\AutomationController::cronJobs');

        // Logs y monitoreo
        $routes->get('automation/logs', 'Admin\AutomationController::logs');
        $routes->get('automation/alerts', 'Admin\AutomationController::alerts');
        $routes->post('automation/alerts/resolve/(:num)', 'Admin\AutomationController::resolveAlert/$1');

        // Configuraciones del sistema
        $routes->get('automation/settings', 'Admin\AutomationController::settings');
        $routes->post('automation/settings', 'Admin\AutomationController::settings');
    });

    // CRON JOBS ROUTES - PARA HOSTING COMPARTIDO
    $routes->group('cron', function($routes) {
        $routes->get('status', 'CronController::status');
        $routes->get('help', 'CronController::help');
        $routes->get('run-all', 'CronController::runAll');
        $routes->get('run-prices', 'CronController::runPrices');
        $routes->get('run-stock', 'CronController::runStock');
        $routes->get('run-wishlist', 'CronController::runWishlist');
        $routes->get('run-cleanup', 'CronController::runCleanup');
        $routes->get('run-backup', 'CronController::runBackup');
        $routes->get('run-performance', 'CronController::runPerformance');
        $routes->get('run-analytics', 'CronController::runAnalytics');
        $routes->get('run-cache', 'CronController::runCache');
        $routes->get('run-system-check', 'CronController::runSystemCheck');
    });

    // SETUP ROUTES - CONFIGURACIÓN INICIAL
    $routes->group('setup', function($routes) {
        $routes->get('/', 'SetupController::index');
        $routes->post('run-setup', 'SetupController::runSetup');
    });

    // TEST ROUTES - SISTEMA DE PRUEBAS
    $routes->group('test', function($routes) {
        $routes->get('/', 'TestController::index');
        $routes->get('payment-methods', 'TestController::paymentMethods');
        $routes->get('checkout', 'TestController::checkoutTest');
        $routes->post('run-all-tests', 'TestController::runAllTests');
    });

    // METRICS ROUTES - SISTEMA DE MÉTRICAS
    $routes->group('admin/metrics', function($routes) {
        $routes->get('/', 'Admin\MetricsController::index');
        $routes->get('real-time-metrics', 'Admin\MetricsController::realTimeMetrics');
    });

    // PUSH NOTIFICATION ROUTES - API DE NOTIFICACIONES PUSH
    $routes->group('api/push', function($routes) {
        $routes->post('subscribe', 'Api\PushController::subscribe');
        $routes->post('unsubscribe', 'Api\PushController::unsubscribe');
        $routes->post('send-test', 'Api\PushController::sendTest');
        $routes->get('stats', 'Api\PushController::getStats');
        $routes->get('subscriptions', 'Api\PushController::getSubscriptions');
        $routes->post('send-price-alert', 'Api\PushController::sendPriceAlert');
        $routes->post('send-stock-alert', 'Api\PushController::sendStockAlert');
        $routes->post('send-cart-reminder', 'Api\PushController::sendCartReminder');
        $routes->post('send-promotion', 'Api\PushController::sendPromotion');
        $routes->post('update-preferences', 'Api\PushController::updatePreferences');
        $routes->get('vapid-key', 'Api\PushController::getVapidKey');
    });



    // ========================================
    // MODULE ROUTES
    // ========================================

    // Include module routes if they exist and APPPATH is defined
    if (defined('APPPATH')) {
        $moduleRoutes = [
            APPPATH . 'Modules/Products/Config/Routes.php',
            APPPATH . 'Modules/Categories/Config/Routes.php',
            APPPATH . 'Modules/ShoppingCart/Config/Routes.php',
            APPPATH . 'Modules/Orders/Config/Routes.php',
            // APPPATH . 'Modules/Admin/Config/Routes.php', // Temporalmente deshabilitado
        ];

        foreach ($moduleRoutes as $routeFile) {
            if (file_exists($routeFile)) {
                require_once $routeFile;
            }
        }
    }

    // === RUTAS PÚBLICAS DE BLOCKCHAIN ===

    // Crypto Payment Routes
    $routes->group('crypto-payment', function($routes) {
        $routes->get('(:num)', 'CryptoPaymentController::index/$1');
        $routes->get('pay/(:any)', 'CryptoPaymentController::pay/$1');
        $routes->get('success/(:any)', 'CryptoPaymentController::success/$1');
        $routes->get('history', 'CryptoPaymentController::history');
        $routes->post('initiate', 'CryptoPaymentController::initiate');
        $routes->get('check-status/(:any)', 'CryptoPaymentController::checkStatus/$1');
        $routes->get('prices', 'CryptoPaymentController::getPrices');
        $routes->post('calculate-conversion', 'CryptoPaymentController::calculateConversion');
    });



    // Tracking Routes
    $routes->group('tracking', function($routes) {
        $routes->get('/', 'TrackingController::index');
        $routes->post('track', 'TrackingController::track');
        $routes->get('result/(:any)', 'TrackingController::result/$1');
        $routes->get('help', 'TrackingController::help');
        $routes->post('calculate-shipping', 'TrackingController::calculateShipping');
        $routes->get('departments', 'TrackingController::getDepartments');
        $routes->get('companies', 'TrackingController::getShippingCompanies');
        $routes->post('subscribe-notifications', 'TrackingController::subscribeNotifications');
        $routes->post('report-issue', 'TrackingController::reportIssue');
        $routes->get('info/(:any)', 'TrackingController::getShipmentInfo/$1');
    });

    // ========================================
    // CUPONES ROUTES
    // ========================================
    $routes->group('coupons', function($routes) {
        $routes->get('/', 'CouponController::index');
        $routes->get('available', 'CouponController::available');
        $routes->get('history', 'CouponController::history');
        $routes->post('validate', 'CouponController::validateCoupon');
        $routes->post('apply', 'CouponController::apply');
        $routes->post('remove', 'CouponController::remove');
        $routes->post('subscribe-newsletter', 'CouponController::subscribeNewsletter');
        $routes->get('check/(:any)', 'CouponController::checkCoupon/$1');
    });

    // ========================================
    // ADMIN CUPONES ROUTES
    // ========================================
    $routes->group('admin/coupons', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('/', 'CouponsController::index');
        $routes->get('coupons', 'CouponsController::index');
        $routes->get('create', 'CouponsController::create');
        $routes->post('store', 'CouponsController::store');
        $routes->get('edit/(:num)', 'CouponsController::edit/$1');
        $routes->post('update/(:num)', 'CouponsController::update/$1');
        $routes->get('view/(:num)', 'CouponsController::view/$1');
        $routes->delete('delete/(:num)', 'CouponsController::delete/$1');
        $routes->post('toggle-status', 'CouponsController::toggleStatus');
        $routes->post('delete-coupon', 'CouponsController::deleteCoupon');
        $routes->get('export', 'CouponsController::export');
        $routes->get('stats', 'CouponsController::getStats');
        $routes->get('usage-report', 'CouponsController::usageReport');
    });

    // ========================================
    // ADMIN SHIPPING ROUTES
    // ========================================
    $routes->group('admin/shipping', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('/', 'ShippingController::index');
        $routes->get('companies', 'ShippingController::companies');
        $routes->get('companies/create', 'ShippingController::createCompany');
        $routes->post('companies/store', 'ShippingController::storeCompany');
        $routes->get('companies/edit/(:num)', 'ShippingController::editCompany/$1');
        $routes->post('companies/update/(:num)', 'ShippingController::updateCompany/$1');
        $routes->delete('companies/delete/(:num)', 'ShippingController::deleteCompany/$1');

        $routes->get('rates', 'ShippingController::rates');
        $routes->get('rates/create', 'ShippingController::createRate');
        $routes->post('rates/store', 'ShippingController::storeRate');
        $routes->get('rates/edit/(:num)', 'ShippingController::editRate/$1');
        $routes->post('rates/update/(:num)', 'ShippingController::updateRate/$1');
        $routes->delete('rates/delete/(:num)', 'ShippingController::deleteRate/$1');

        $routes->get('shipments', 'ShippingController::shipments');
        $routes->get('shipments/view/(:num)', 'ShippingController::viewShipment/$1');
        $routes->post('shipments/update-status', 'ShippingController::updateShipmentStatus');
        $routes->get('shipments/tracking/(:any)', 'ShippingController::trackingDetails/$1');

        $routes->get('calculate', 'ShippingController::calculateShipping');
        $routes->post('calculate-cost', 'ShippingController::calculateCost');
        $routes->get('zones', 'ShippingController::zones');
        $routes->get('reports', 'ShippingController::reports');
    });

    // ========================================
    // ADMIN BILLING ROUTES
    // ========================================
    $routes->group('admin/billing', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('/', 'BillingConfigController::index');
        $routes->get('config', 'BillingConfigController::index');
        $routes->post('config/update', 'BillingConfigController::update');
        $routes->post('config/test-connection', 'BillingConfigController::testConnection');
        $routes->get('history', 'BillingConfigController::invoiceHistory');
        $routes->post('regenerate/(:num)', 'BillingConfigController::regenerateInvoice/$1');
        $routes->get('download/(:num)', 'BillingConfigController::downloadInvoice/$1');
    });

    // ========================================
    // ADMIN SOCIAL MANAGER ROUTES
    // ========================================
    $routes->group('admin/social', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('/', 'SocialManagerController::index');
        $routes->get('config', 'SocialManagerController::index');
        $routes->post('config/update', 'SocialManagerController::update');
        $routes->post('config/test-connection', 'SocialManagerController::testConnection');
        $routes->get('scheduled', 'SocialManagerController::scheduledPosts');
        $routes->get('create-post', 'SocialManagerController::createPost');
        $routes->post('store-post', 'SocialManagerController::storePost');
        $routes->get('edit-post/(:num)', 'SocialManagerController::editPost/$1');
        $routes->post('update-post/(:num)', 'SocialManagerController::updatePost/$1');
        $routes->delete('delete-post/(:num)', 'SocialManagerController::deletePost/$1');
        $routes->post('publish-now/(:num)', 'SocialManagerController::publishNow/$1');
    });

} // End of if (isset($routes)) block
