<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class CatalogApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener catálogo de productos público
     * GET /api/catalog/products
     */
    public function getProducts()
    {
        try {
            $categoryId = $this->request->getGet('category_id');
            $search = $this->request->getGet('search');
            $minPrice = $this->request->getGet('min_price');
            $maxPrice = $this->request->getGet('max_price');
            $sortBy = $this->request->getGet('sort_by') ?? 'newest';
            $limit = (int) ($this->request->getGet('limit') ?? 24);
            $offset = (int) ($this->request->getGet('offset') ?? 0);

            // Validar parámetros
            $validSorts = ['name', 'price_asc', 'price_desc', 'newest', 'popular'];
            if (!in_array($sortBy, $validSorts)) {
                $sortBy = 'newest';
            }

            if ($limit > 100) {
                $limit = 100; // Máximo 100 productos por página
            }

            // Llamar SP para obtener productos
            $query = $this->db->query("CALL sp_get_public_product_catalog(?, ?, ?, ?, ?, ?, ?)", [
                $categoryId, $search, $minPrice, $maxPrice, $sortBy, $limit, $offset
            ]);

            $products = $query->getResultArray();

            // Obtener ratings actualizados para todos los productos
            $productIds = array_column($products, 'id');
            $ratings = [];
            if (!empty($productIds)) {
                $ratingsQuery = $this->db->table('products')
                                        ->select('id, rating_average, rating_count')
                                        ->whereIn('id', $productIds)
                                        ->get();
                foreach ($ratingsQuery->getResultArray() as $rating) {
                    $ratings[$rating['id']] = $rating;
                }
            }

            // Procesar datos
            foreach ($products as &$product) {
                // Convertir precios a float
                $product['price_regular'] = (float) $product['price_regular'];
                $product['price_sale'] = $product['price_sale'] ? (float) $product['price_sale'] : null;
                $product['final_price'] = (float) $product['final_price'];
                $product['discount_percentage'] = (int) $product['discount_percentage'];

                // Convertir stock a int
                $product['stock_quantity'] = (int) $product['stock_quantity'];
                $product['images_count'] = (int) $product['images_count'];

                // Actualizar ratings desde la consulta separada
                if (isset($ratings[$product['id']])) {
                    $product['rating_average'] = (float) $ratings[$product['id']]['rating_average'];
                    $product['rating_count'] = (int) $ratings[$product['id']]['rating_count'];
                } else {
                    $product['rating_average'] = 0.0;
                    $product['rating_count'] = 0;
                }

                // Convertir booleanos
                $product['is_featured'] = (bool) $product['is_featured'];

                // Formatear fechas
                $product['created_at'] = date('c', strtotime($product['created_at']));
            }

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'products' => $products,
                    'pagination' => [
                        'limit' => $limit,
                        'offset' => $offset,
                        'total' => count($products),
                        'has_more' => count($products) === $limit
                    ],
                    'filters' => [
                        'category_id' => $categoryId,
                        'search' => $search,
                        'min_price' => $minPrice,
                        'max_price' => $maxPrice,
                        'sort_by' => $sortBy
                    ]
                ],
                'message' => 'Productos obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getProducts: ' . $e->getMessage());
            return $this->failServerError('Error al obtener productos');
        }
    }

    /**
     * Obtener detalles de un producto
     * GET /api/catalog/products/{id}
     * GET /api/catalog/products/slug/{slug}
     */
    public function getProduct($id = null, $slug = null)
    {
        try {
            if (!$id && !$slug) {
                return $this->failValidationError('ID o slug del producto requerido');
            }

            // Llamar SP para obtener detalles
            $query = $this->db->query("CALL sp_get_public_product_details(?, ?)", [
                $id, $slug
            ]);

            $product = $query->getRowArray();

            if (!$product) {
                return $this->failNotFound('Producto no encontrado');
            }

            // Procesar datos
            $product['price_regular'] = (float) $product['price_regular'];
            $product['price_sale'] = $product['price_sale'] ? (float) $product['price_sale'] : null;
            $product['final_price'] = (float) $product['final_price'];
            $product['discount_percentage'] = (int) $product['discount_percentage'];
            $product['stock_quantity'] = (int) $product['stock_quantity'];
            $product['rating_average'] = (float) $product['rating_average'];
            $product['rating_count'] = (int) $product['rating_count'];
            $product['is_featured'] = (bool) $product['is_featured'];
            $product['views_count'] = (int) $product['views_count'];

            // Formatear fechas
            $product['created_at'] = date('c', strtotime($product['created_at']));
            $product['updated_at'] = $product['updated_at'] ? date('c', strtotime($product['updated_at'])) : null;

            // Procesar atributos JSON
            if (!empty($product['attributes'])) {
                $product['attributes'] = json_decode($product['attributes'], true);
            }

            return $this->respond([
                'status' => 'success',
                'data' => $product,
                'message' => 'Producto obtenido correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getProduct: ' . $e->getMessage());
            return $this->failServerError('Error al obtener producto');
        }
    }

    /**
     * Obtener galería de un producto
     * GET /api/catalog/products/{id}/gallery
     */
    public function getProductGallery($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID del producto requerido');
            }

            // Reutilizar SP existente de media
            $query = $this->db->query("CALL sp_get_product_gallery(?)", [$id]);
            $gallery = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $gallery,
                'message' => 'Galería obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getProductGallery: ' . $e->getMessage());
            return $this->failServerError('Error al obtener galería');
        }
    }

    /**
     * Obtener categorías para filtros
     * GET /api/catalog/categories
     */
    public function getCategories()
    {
        try {
            // Consulta directa para categorías activas
            $sql = "
                SELECT
                    id, uuid, name, slug, description, parent_id, sort_order,
                    (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.deleted_at IS NULL AND p.is_active = 1) as products_count
                FROM categories c
                WHERE c.deleted_at IS NULL
                  AND c.is_active = 1
                ORDER BY c.sort_order ASC, c.name ASC
            ";

            $query = $this->db->query($sql);
            $categories = $query->getResultArray();

            // Procesar datos
            foreach ($categories as &$category) {
                $category['products_count'] = (int) $category['products_count'];
                $category['sort_order'] = (int) $category['sort_order'];
                $category['parent_id'] = $category['parent_id'] ? (int) $category['parent_id'] : null;
            }

            return $this->respond([
                'status' => 'success',
                'data' => $categories,
                'message' => 'Categorías obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getCategories: ' . $e->getMessage());
            return $this->failServerError('Error al obtener categorías');
        }
    }

    /**
     * Buscar productos con autocompletado
     * GET /api/catalog/search/suggestions
     */
    public function getSearchSuggestions()
    {
        try {
            $query = $this->request->getGet('q');
            $limit = (int) ($this->request->getGet('limit') ?? 10);

            if (!$query || strlen($query) < 2) {
                return $this->respond([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'Query muy corto'
                ]);
            }

            // Buscar productos que coincidan
            $sql = "
                SELECT 
                    id, name, slug, price_regular, price_sale,
                    CASE 
                        WHEN price_sale IS NOT NULL AND price_sale > 0 THEN price_sale
                        ELSE price_regular
                    END as final_price
                FROM products 
                WHERE deleted_at IS NULL 
                  AND is_active = 1
                  AND (name LIKE ? OR sku LIKE ?)
                ORDER BY 
                    CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
                    name ASC
                LIMIT ?
            ";

            $searchTerm = '%' . $query . '%';
            $exactTerm = $query . '%';

            $queryResult = $this->db->query($sql, [
                $searchTerm, $searchTerm, $exactTerm, $limit
            ]);

            $suggestions = $queryResult->getResultArray();

            // Procesar resultados
            foreach ($suggestions as &$suggestion) {
                $suggestion['final_price'] = (float) $suggestion['final_price'];
                $suggestion['price_regular'] = (float) $suggestion['price_regular'];
                $suggestion['price_sale'] = $suggestion['price_sale'] ? (float) $suggestion['price_sale'] : null;
            }

            return $this->respond([
                'status' => 'success',
                'data' => $suggestions,
                'message' => 'Sugerencias obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getSearchSuggestions: ' . $e->getMessage());
            return $this->failServerError('Error al obtener sugerencias');
        }
    }

    /**
     * Obtener productos relacionados
     * GET /api/catalog/products/{id}/related
     */
    public function getRelatedProducts($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID del producto requerido');
            }

            $limit = (int) ($this->request->getGet('limit') ?? 6);

            // Obtener productos de la misma categoría
            $sql = "
                SELECT 
                    p2.id, p2.name, p2.slug, p2.price_regular, p2.price_sale,
                    CASE 
                        WHEN p2.price_sale IS NOT NULL AND p2.price_sale > 0 THEN p2.price_sale
                        ELSE p2.price_regular
                    END as final_price,
                    p2.stock_quantity,
                    (SELECT mf.file_url 
                     FROM media_relations mr 
                     INNER JOIN media_files mf ON mf.id = mr.media_id 
                     WHERE mr.entity_type = 'product' 
                       AND mr.entity_id = p2.id 
                       AND mr.relation_type = 'featured' 
                       AND mf.deleted_at IS NULL 
                     LIMIT 1) as featured_image
                FROM products p1
                INNER JOIN products p2 ON p2.category_id = p1.category_id
                WHERE p1.id = ?
                  AND p2.id != ?
                  AND p2.deleted_at IS NULL
                  AND p2.is_active = 1
                ORDER BY p2.is_featured DESC, RAND()
                LIMIT ?
            ";

            $queryResult = $this->db->query($sql, [$id, $id, $limit]);
            $relatedProducts = $queryResult->getResultArray();

            // Procesar resultados
            foreach ($relatedProducts as &$product) {
                $product['final_price'] = (float) $product['final_price'];
                $product['price_regular'] = (float) $product['price_regular'];
                $product['price_sale'] = $product['price_sale'] ? (float) $product['price_sale'] : null;
                $product['stock_quantity'] = (int) $product['stock_quantity'];
            }

            return $this->respond([
                'status' => 'success',
                'data' => $relatedProducts,
                'message' => 'Productos relacionados obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CatalogApiSP::getRelatedProducts: ' . $e->getMessage());
            return $this->failServerError('Error al obtener productos relacionados');
        }
    }
}
