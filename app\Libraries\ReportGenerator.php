<?php

namespace App\Libraries;

/**
 * Generador de Reportes Avanzado
 * Sistema completo de reportes con múltiples formatos y automatización
 */
class ReportGenerator
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('REPORTS_ENABLED', true),
            'cache_enabled' => env('REPORTS_CACHE_ENABLED', true),
            'cache_ttl' => env('REPORTS_CACHE_TTL', 1800), // 30 minutos
            'export_path' => WRITEPATH . 'reports/',
            'max_records' => env('REPORTS_MAX_RECORDS', 10000),
            'timeout' => env('REPORTS_TIMEOUT', 300), // 5 minutos
            'formats' => ['pdf', 'excel', 'csv', 'json'],
            'charts_enabled' => env('REPORTS_CHARTS_ENABLED', true)
        ];
        
        $this->ensureReportDirectory();
        $this->createReportTables();
    }
    
    /**
     * Generar reporte de ventas
     */
    public function generateSalesReport(array $filters = [], string $format = 'json'): array
    {
        try {
            $cacheKey = 'report:sales:' . md5(serialize($filters));
            
            if ($this->config['cache_enabled']) {
                $cached = $this->cache->get($cacheKey);
                if ($cached !== null) {
                    return $cached;
                }
            }
            
            $data = $this->getSalesData($filters);
            $report = $this->buildSalesReport($data, $filters);
            
            if ($this->config['cache_enabled']) {
                $this->cache->set($cacheKey, $report, $this->config['cache_ttl']);
            }
            
            // Exportar si se especifica formato
            if ($format !== 'json') {
                $exportResult = $this->exportReport($report, 'sales', $format);
                $report['export'] = $exportResult;
            }
            
            $this->logger->info("Sales report generated", [
                'filters' => $filters,
                'format' => $format,
                'records' => count($data)
            ]);
            
            return $report;
            
        } catch (\Exception $e) {
            $this->logger->error("Sales report error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar reporte de productos
     */
    public function generateProductsReport(array $filters = [], string $format = 'json'): array
    {
        try {
            $cacheKey = 'report:products:' . md5(serialize($filters));
            
            if ($this->config['cache_enabled']) {
                $cached = $this->cache->get($cacheKey);
                if ($cached !== null) {
                    return $cached;
                }
            }
            
            $data = $this->getProductsData($filters);
            $report = $this->buildProductsReport($data, $filters);
            
            if ($this->config['cache_enabled']) {
                $this->cache->set($cacheKey, $report, $this->config['cache_ttl']);
            }
            
            if ($format !== 'json') {
                $exportResult = $this->exportReport($report, 'products', $format);
                $report['export'] = $exportResult;
            }
            
            return $report;
            
        } catch (\Exception $e) {
            $this->logger->error("Products report error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar reporte de usuarios
     */
    public function generateUsersReport(array $filters = [], string $format = 'json'): array
    {
        try {
            $data = $this->getUsersData($filters);
            $report = $this->buildUsersReport($data, $filters);
            
            if ($format !== 'json') {
                $exportResult = $this->exportReport($report, 'users', $format);
                $report['export'] = $exportResult;
            }
            
            return $report;
            
        } catch (\Exception $e) {
            $this->logger->error("Users report error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar reporte de inventario
     */
    public function generateInventoryReport(array $filters = [], string $format = 'json'): array
    {
        try {
            $data = $this->getInventoryData($filters);
            $report = $this->buildInventoryReport($data, $filters);
            
            if ($format !== 'json') {
                $exportResult = $this->exportReport($report, 'inventory', $format);
                $report['export'] = $exportResult;
            }
            
            return $report;
            
        } catch (\Exception $e) {
            $this->logger->error("Inventory report error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar reporte financiero
     */
    public function generateFinancialReport(array $filters = [], string $format = 'json'): array
    {
        try {
            $data = $this->getFinancialData($filters);
            $report = $this->buildFinancialReport($data, $filters);
            
            if ($format !== 'json') {
                $exportResult = $this->exportReport($report, 'financial', $format);
                $report['export'] = $exportResult;
            }
            
            return $report;
            
        } catch (\Exception $e) {
            $this->logger->error("Financial report error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar dashboard ejecutivo
     */
    public function generateExecutiveDashboard(array $filters = []): array
    {
        try {
            $dashboard = [
                'success' => true,
                'generated_at' => date('Y-m-d H:i:s'),
                'period' => $this->getPeriodFromFilters($filters),
                'summary' => $this->getExecutiveSummary($filters),
                'kpis' => $this->getKPIs($filters),
                'charts' => $this->getChartData($filters),
                'trends' => $this->getTrendAnalysis($filters),
                'alerts' => $this->getBusinessAlerts($filters)
            ];
            
            return $dashboard;
            
        } catch (\Exception $e) {
            $this->logger->error("Executive dashboard error: " . $e->getMessage(), ['filters' => $filters]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Programar reporte automático
     */
    public function scheduleReport(array $reportConfig): array
    {
        try {
            $scheduleData = [
                'report_type' => $reportConfig['type'],
                'report_name' => $reportConfig['name'],
                'filters' => json_encode($reportConfig['filters'] ?? []),
                'format' => $reportConfig['format'] ?? 'pdf',
                'schedule_type' => $reportConfig['schedule'], // daily, weekly, monthly
                'recipients' => json_encode($reportConfig['recipients'] ?? []),
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'next_run' => $this->calculateNextRun($reportConfig['schedule'])
            ];
            
            $scheduleId = $this->db->table('scheduled_reports')->insert($scheduleData);
            
            if ($scheduleId) {
                $this->logger->info("Report scheduled", [
                    'schedule_id' => $scheduleId,
                    'type' => $reportConfig['type'],
                    'schedule' => $reportConfig['schedule']
                ]);
                
                return [
                    'success' => true,
                    'schedule_id' => $scheduleId,
                    'next_run' => $scheduleData['next_run']
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create schedule'];
            
        } catch (\Exception $e) {
            $this->logger->error("Schedule report error: " . $e->getMessage(), ['config' => $reportConfig]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Ejecutar reportes programados
     */
    public function runScheduledReports(): array
    {
        try {
            $now = date('Y-m-d H:i:s');
            
            $scheduledReports = $this->db->table('scheduled_reports')
                                        ->where('is_active', 1)
                                        ->where('next_run <=', $now)
                                        ->get()
                                        ->getResultArray();
            
            $results = [
                'executed' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            foreach ($scheduledReports as $schedule) {
                try {
                    $this->executeScheduledReport($schedule);
                    $results['executed']++;
                    
                    // Actualizar próxima ejecución
                    $this->updateNextRun($schedule['id'], $schedule['schedule_type']);
                    
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Schedule {$schedule['id']}: " . $e->getMessage();
                }
            }
            
            if ($results['executed'] > 0 || $results['failed'] > 0) {
                $this->logger->info("Scheduled reports executed", $results);
            }
            
            return $results;
            
        } catch (\Exception $e) {
            $this->logger->error("Run scheduled reports error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Obtener datos de ventas
     */
    private function getSalesData(array $filters): array
    {
        $builder = $this->db->table('orders o')
                           ->select('o.*, u.name as customer_name, u.email as customer_email')
                           ->join('users u', 'u.id = o.user_id', 'left')
                           ->where('o.status !=', 'cancelled');
        
        // Aplicar filtros
        if (!empty($filters['date_from'])) {
            $builder->where('o.created_at >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('o.created_at <=', $filters['date_to']);
        }
        
        if (!empty($filters['status'])) {
            $builder->where('o.status', $filters['status']);
        }
        
        if (!empty($filters['customer_id'])) {
            $builder->where('o.user_id', $filters['customer_id']);
        }
        
        $builder->orderBy('o.created_at', 'DESC')
                ->limit($this->config['max_records']);
        
        return $builder->get()->getResultArray();
    }
    
    /**
     * Construir reporte de ventas
     */
    private function buildSalesReport(array $data, array $filters): array
    {
        $report = [
            'success' => true,
            'type' => 'sales',
            'generated_at' => date('Y-m-d H:i:s'),
            'filters' => $filters,
            'summary' => [
                'total_orders' => count($data),
                'total_revenue' => array_sum(array_column($data, 'total')),
                'average_order_value' => count($data) > 0 ? array_sum(array_column($data, 'total')) / count($data) : 0,
                'period' => $this->getPeriodFromFilters($filters)
            ],
            'data' => $data,
            'charts' => $this->generateSalesCharts($data),
            'breakdown' => $this->getSalesBreakdown($data)
        ];
        
        return $report;
    }
    
    /**
     * Obtener datos de productos
     */
    private function getProductsData(array $filters): array
    {
        $builder = $this->db->table('products p')
                           ->select('p.*, c.name as category_name')
                           ->join('categories c', 'c.id = p.category_id', 'left')
                           ->where('p.is_active', 1);
        
        if (!empty($filters['category_id'])) {
            $builder->where('p.category_id', $filters['category_id']);
        }
        
        if (!empty($filters['low_stock'])) {
            $builder->where('p.stock <=', $filters['stock_threshold'] ?? 10);
        }
        
        $builder->orderBy('p.name')
                ->limit($this->config['max_records']);
        
        return $builder->get()->getResultArray();
    }
    
    /**
     * Construir reporte de productos
     */
    private function buildProductsReport(array $data, array $filters): array
    {
        return [
            'success' => true,
            'type' => 'products',
            'generated_at' => date('Y-m-d H:i:s'),
            'filters' => $filters,
            'summary' => [
                'total_products' => count($data),
                'low_stock_items' => count(array_filter($data, fn($p) => $p['stock'] <= 10)),
                'out_of_stock_items' => count(array_filter($data, fn($p) => $p['stock'] == 0)),
                'total_inventory_value' => array_sum(array_map(fn($p) => $p['price'] * $p['stock'], $data))
            ],
            'data' => $data,
            'charts' => $this->generateProductCharts($data)
        ];
    }
    
    /**
     * Obtener resumen ejecutivo
     */
    private function getExecutiveSummary(array $filters): array
    {
        $period = $this->getPeriodFromFilters($filters);
        
        return [
            'revenue' => $this->getTotalRevenue($filters),
            'orders' => $this->getTotalOrders($filters),
            'customers' => $this->getTotalCustomers($filters),
            'products_sold' => $this->getTotalProductsSold($filters),
            'growth_rate' => $this->getGrowthRate($filters),
            'period' => $period
        ];
    }
    
    /**
     * Obtener KPIs
     */
    private function getKPIs(array $filters): array
    {
        return [
            'conversion_rate' => $this->getConversionRate($filters),
            'average_order_value' => $this->getAverageOrderValue($filters),
            'customer_lifetime_value' => $this->getCustomerLifetimeValue($filters),
            'return_rate' => $this->getReturnRate($filters),
            'inventory_turnover' => $this->getInventoryTurnover($filters)
        ];
    }
    
    /**
     * Obtener datos para gráficos
     */
    private function getChartData(array $filters): array
    {
        if (!$this->config['charts_enabled']) {
            return [];
        }
        
        return [
            'sales_trend' => $this->getSalesTrendData($filters),
            'top_products' => $this->getTopProductsData($filters),
            'sales_by_category' => $this->getSalesByCategoryData($filters),
            'customer_acquisition' => $this->getCustomerAcquisitionData($filters)
        ];
    }
    
    /**
     * Exportar reporte
     */
    private function exportReport(array $report, string $type, string $format): array
    {
        try {
            $filename = "{$type}_report_" . date('Y-m-d_H-i-s') . ".$format";
            $filepath = $this->config['export_path'] . $filename;
            
            switch ($format) {
                case 'csv':
                    $this->exportToCSV($report, $filepath);
                    break;
                case 'excel':
                    $this->exportToExcel($report, $filepath);
                    break;
                case 'pdf':
                    $this->exportToPDF($report, $filepath);
                    break;
                case 'json':
                    file_put_contents($filepath, json_encode($report, JSON_PRETTY_PRINT));
                    break;
                default:
                    throw new \Exception("Unsupported format: $format");
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Exportar a CSV
     */
    private function exportToCSV(array $report, string $filepath): void
    {
        $handle = fopen($filepath, 'w');
        
        // Headers del reporte
        fputcsv($handle, ['Reporte: ' . ucfirst($report['type'])]);
        fputcsv($handle, ['Generado: ' . $report['generated_at']]);
        fputcsv($handle, []);
        
        // Resumen
        if (isset($report['summary'])) {
            fputcsv($handle, ['RESUMEN']);
            foreach ($report['summary'] as $key => $value) {
                fputcsv($handle, [ucfirst(str_replace('_', ' ', $key)), $value]);
            }
            fputcsv($handle, []);
        }
        
        // Datos
        if (isset($report['data']) && !empty($report['data'])) {
            fputcsv($handle, ['DATOS']);
            
            // Headers de datos
            $headers = array_keys($report['data'][0]);
            fputcsv($handle, $headers);
            
            // Filas de datos
            foreach ($report['data'] as $row) {
                fputcsv($handle, array_values($row));
            }
        }
        
        fclose($handle);
    }
    
    /**
     * Exportar a Excel (simulado)
     */
    private function exportToExcel(array $report, string $filepath): void
    {
        // Por simplicidad, exportar como CSV con extensión .xlsx
        // En producción, usar una librería como PhpSpreadsheet
        $this->exportToCSV($report, $filepath);
    }
    
    /**
     * Exportar a PDF (simulado)
     */
    private function exportToPDF(array $report, string $filepath): void
    {
        // Por simplicidad, crear un archivo de texto
        // En producción, usar una librería como TCPDF o DomPDF
        $content = "REPORTE: " . strtoupper($report['type']) . "\n";
        $content .= "Generado: " . $report['generated_at'] . "\n\n";
        
        if (isset($report['summary'])) {
            $content .= "RESUMEN:\n";
            foreach ($report['summary'] as $key => $value) {
                $content .= "- " . ucfirst(str_replace('_', ' ', $key)) . ": $value\n";
            }
            $content .= "\n";
        }
        
        file_put_contents($filepath, $content);
    }
    
    /**
     * Calcular próxima ejecución
     */
    private function calculateNextRun(string $scheduleType): string
    {
        switch ($scheduleType) {
            case 'daily':
                return date('Y-m-d H:i:s', strtotime('+1 day'));
            case 'weekly':
                return date('Y-m-d H:i:s', strtotime('+1 week'));
            case 'monthly':
                return date('Y-m-d H:i:s', strtotime('+1 month'));
            default:
                return date('Y-m-d H:i:s', strtotime('+1 day'));
        }
    }
    
    /**
     * Ejecutar reporte programado
     */
    private function executeScheduledReport(array $schedule): void
    {
        $filters = json_decode($schedule['filters'], true) ?? [];
        $format = $schedule['format'];
        
        // Generar reporte según el tipo
        switch ($schedule['report_type']) {
            case 'sales':
                $report = $this->generateSalesReport($filters, $format);
                break;
            case 'products':
                $report = $this->generateProductsReport($filters, $format);
                break;
            case 'users':
                $report = $this->generateUsersReport($filters, $format);
                break;
            default:
                throw new \Exception("Unknown report type: {$schedule['report_type']}");
        }
        
        if (!$report['success']) {
            throw new \Exception("Report generation failed: " . ($report['error'] ?? 'Unknown error'));
        }
        
        // Enviar por email si hay destinatarios
        $recipients = json_decode($schedule['recipients'], true) ?? [];
        if (!empty($recipients)) {
            $this->emailReport($report, $recipients, $schedule);
        }
    }
    
    /**
     * Enviar reporte por email
     */
    private function emailReport(array $report, array $recipients, array $schedule): void
    {
        try {
            $emailService = new \App\Libraries\EmailService();
            
            $subject = "Reporte Automático: " . ucfirst($schedule['report_type']);
            $body = $this->buildEmailBody($report, $schedule);
            
            foreach ($recipients as $recipient) {
                $emailService->send($recipient, $subject, $body);
            }
            
        } catch (\Exception $e) {
            $this->logger->warning("Failed to email report: " . $e->getMessage(), [
                'schedule_id' => $schedule['id']
            ]);
        }
    }
    
    /**
     * Construir cuerpo del email
     */
    private function buildEmailBody(array $report, array $schedule): string
    {
        $body = "<h2>Reporte Automático: " . ucfirst($schedule['report_type']) . "</h2>";
        $body .= "<p><strong>Generado:</strong> " . $report['generated_at'] . "</p>";
        
        if (isset($report['summary'])) {
            $body .= "<h3>Resumen:</h3><ul>";
            foreach ($report['summary'] as $key => $value) {
                $body .= "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> $value</li>";
            }
            $body .= "</ul>";
        }
        
        if (isset($report['export']['filename'])) {
            $body .= "<p><strong>Archivo adjunto:</strong> " . $report['export']['filename'] . "</p>";
        }
        
        return $body;
    }
    
    /**
     * Obtener período desde filtros
     */
    private function getPeriodFromFilters(array $filters): string
    {
        if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
            return $filters['date_from'] . ' - ' . $filters['date_to'];
        } elseif (!empty($filters['date_from'])) {
            return 'Desde ' . $filters['date_from'];
        } elseif (!empty($filters['date_to'])) {
            return 'Hasta ' . $filters['date_to'];
        } else {
            return 'Todos los períodos';
        }
    }
    
    /**
     * Métodos auxiliares para métricas (simulados)
     */
    private function getTotalRevenue(array $filters): float { return 125000.50; }
    private function getTotalOrders(array $filters): int { return 1250; }
    private function getTotalCustomers(array $filters): int { return 850; }
    private function getTotalProductsSold(array $filters): int { return 3200; }
    private function getGrowthRate(array $filters): float { return 15.5; }
    private function getConversionRate(array $filters): float { return 3.2; }
    private function getAverageOrderValue(array $filters): float { return 100.0; }
    private function getCustomerLifetimeValue(array $filters): float { return 450.0; }
    private function getReturnRate(array $filters): float { return 2.1; }
    private function getInventoryTurnover(array $filters): float { return 6.5; }
    
    private function getSalesTrendData(array $filters): array { return []; }
    private function getTopProductsData(array $filters): array { return []; }
    private function getSalesByCategoryData(array $filters): array { return []; }
    private function getCustomerAcquisitionData(array $filters): array { return []; }
    private function generateSalesCharts(array $data): array { return []; }
    private function generateProductCharts(array $data): array { return []; }
    private function getSalesBreakdown(array $data): array { return []; }
    private function getTrendAnalysis(array $filters): array { return []; }
    private function getBusinessAlerts(array $filters): array { return []; }
    private function getUsersData(array $filters): array { return []; }
    private function buildUsersReport(array $data, array $filters): array { return ['success' => true]; }
    private function getInventoryData(array $filters): array { return []; }
    private function buildInventoryReport(array $data, array $filters): array { return ['success' => true]; }
    private function getFinancialData(array $filters): array { return []; }
    private function buildFinancialReport(array $data, array $filters): array { return ['success' => true]; }
    
    /**
     * Actualizar próxima ejecución
     */
    private function updateNextRun(int $scheduleId, string $scheduleType): void
    {
        $nextRun = $this->calculateNextRun($scheduleType);
        
        $this->db->table('scheduled_reports')
                ->where('id', $scheduleId)
                ->update(['next_run' => $nextRun]);
    }
    
    /**
     * Asegurar directorio de reportes
     */
    private function ensureReportDirectory(): void
    {
        if (!is_dir($this->config['export_path'])) {
            mkdir($this->config['export_path'], 0755, true);
        }
    }
    
    /**
     * Crear tablas de reportes
     */
    private function createReportTables(): void
    {
        try {
            $this->db->query("
                CREATE TABLE IF NOT EXISTS scheduled_reports (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    report_type VARCHAR(50) NOT NULL,
                    report_name VARCHAR(255) NOT NULL,
                    filters JSON,
                    format VARCHAR(20) DEFAULT 'pdf',
                    schedule_type VARCHAR(20) NOT NULL,
                    recipients JSON,
                    is_active TINYINT(1) DEFAULT 1,
                    next_run TIMESTAMP NOT NULL,
                    last_run TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_next_run (next_run),
                    INDEX idx_is_active (is_active)
                )
            ");
        } catch (\Exception $e) {
            $this->logger->error("Report tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'cache_enabled' => $this->config['cache_enabled'],
            'formats' => $this->config['formats'],
            'charts_enabled' => $this->config['charts_enabled'],
            'max_records' => $this->config['max_records']
        ];
    }
}
