<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class CreateWishlistTableSeeder extends Seeder
{
    public function run()
    {
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS wishlist (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    product_id INT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_user_product (user_id, product_id),
                    KEY idx_user_id (user_id),
                    KEY idx_product_id (product_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->db->query($sql);
            echo "✅ Tabla 'wishlist' creada exitosamente\n";
            
        } catch (\Exception $e) {
            echo "⚠️  Error creando tabla wishlist: " . $e->getMessage() . "\n";
        }
    }
}
