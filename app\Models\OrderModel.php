<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderModel extends BaseModel
{
    protected $table            = 'orders';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'order_number',
        'external_id',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'shipping_address',
        'customer_notes',
        'admin_notes',
        'priority',
        'source',
        'billing_address',
        'subtotal',
        'tax_amount',
        'shipping_cost',
        'discount_amount',
        'total',
        'status',
        'payment_status',
        'fulfillment_status',
        'payment_method',
        'tracking_number',
        'carrier',
        'estimated_delivery',
        'delivered_at',
        'cancelled_at',
        'cancellation_reason',
        'refund_amount',
        'refund_reason',
        'tags',
        'notes',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'order_number' => 'required|max_length[50]',
        'customer_name' => 'permit_empty|max_length[100]',
        'customer_email' => 'permit_empty|valid_email|max_length[100]',
        'customer_phone' => 'permit_empty|max_length[20]',
        'status' => 'required|in_list[pending,confirmed,processing,shipped,delivered,cancelled]',
        'total' => 'required|decimal',
        'payment_method' => 'permit_empty|max_length[50]',
        'payment_status' => 'permit_empty|in_list[pending,paid,failed,refunded]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'El ID del usuario es requerido',
            'integer' => 'El ID del usuario debe ser un número entero'
        ],
        'status' => [
            'required' => 'El estado del pedido es requerido',
            'in_list' => 'Estado de pedido inválido'
        ],
        'total' => [
            'required' => 'El monto total es requerido',
            'decimal' => 'El monto total debe ser un número decimal válido'
        ],
        'payment_status' => [
            'required' => 'El estado del pago es requerido',
            'in_list' => 'Estado de pago inválido'
        ]
    ];

    protected $skipValidation = false;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateOrderNumber'];
    protected $afterInsert    = ['triggerOrderCreated'];
    protected $afterUpdate    = ['triggerOrderUpdated'];

    /**
     * Generate unique order number
     */
    protected function generateOrderNumber(array $data)
    {
        if (empty($data['data']['order_number'])) {
            $data['data']['order_number'] = 'MRC-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        }
        return $data;
    }

    /**
     * Get orders with user information
     */
    public function getOrdersWithUser($limit = 20, $offset = 0)
    {
        return $this->select('orders.*, users.name as user_name, users.email as user_email')
                   ->join('users', 'users.id = orders.user_id', 'left')
                   ->orderBy('orders.created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Get orders by user
     */
    public function getOrdersByUser($userId, $limit = 20)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get order with items
     */
    public function getOrderWithItems($orderId)
    {
        $order = $this->find($orderId);
        if ($order) {
            $orderItemModel = new OrderItemModel();
            $order['items'] = $orderItemModel->getOrderItems($orderId);
        }
        return $order;
    }

    /**
     * Update order status
     */
    public function updateOrderStatus($orderId, $status)
    {
        $updateData = ['status' => $status];
        
        // Set timestamps for specific statuses
        switch ($status) {
            case 'shipped':
                $updateData['shipped_at'] = date('Y-m-d H:i:s');
                break;
            case 'delivered':
                $updateData['delivered_at'] = date('Y-m-d H:i:s');
                break;
        }

        return $this->update($orderId, $updateData);
    }

    /**
     * Get order statistics
     */
    public function getOrderStats($startDate = null, $endDate = null)
    {
        $builder = $this->builder();
        
        if ($startDate) {
            $builder->where('created_at >=', $startDate);
        }
        if ($endDate) {
            $builder->where('created_at <=', $endDate);
        }

        $stats = [
            'total_orders' => $builder->countAllResults(false),
            'total_revenue' => $builder->selectSum('total')->get()->getRow()->total ?? 0,
            'pending_orders' => $builder->where('status', 'pending')->countAllResults(false),
            'processing_orders' => $builder->where('status', 'processing')->countAllResults(false),
            'shipped_orders' => $builder->where('status', 'shipped')->countAllResults(false),
            'delivered_orders' => $builder->where('status', 'delivered')->countAllResults(false),
            'cancelled_orders' => $builder->where('status', 'cancelled')->countAllResults(false)
        ];

        return $stats;
    }

    /**
     * Search orders
     */
    public function searchOrders($term)
    {
        return $this->like('order_number',
        'external_id', $term)
                   ->orLike('shipping_address', $term)
                   ->orLike('billing_address', $term)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get recent orders
     */
    public function getRecentOrders($limit = 10)
    {
        return $this->select('orders.*, users.name as user_name')
                   ->join('users', 'users.id = orders.user_id', 'left')
                   ->orderBy('orders.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get orders by status
     */
    public function getOrdersByStatus($status, $limit = 20)
    {
        return $this->where('status', $status)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Calculate order totals
     */
    public function calculateOrderTotals($orderId)
    {
        $orderItemModel = new OrderItemModel();
        $items = $orderItemModel->getOrderItems($orderId);
        
        $subtotal = 0;
        foreach ($items as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }
        
        $taxAmount = $subtotal * 0.12; // 12% tax
        $shippingAmount = $subtotal > 500 ? 0 : 50; // Free shipping over Q500
        $totalAmount = $subtotal + $taxAmount + $shippingAmount;
        
        return [
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'total' => $totalAmount
        ];
    }

    /**
     * Callback después de insertar orden
     */
    protected function triggerOrderCreated(array $data)
    {
        if (isset($data['id']) && isset($data['data'])) {
            $orderData = array_merge($data['data'], ['id' => $data['id']]);
            \App\Libraries\WhatsAppEventHandler::triggerOrderCreated($orderData);
        }
        return $data;
    }

    /**
     * Callback después de actualizar orden
     */
    protected function triggerOrderUpdated(array $data)
    {
        if (isset($data['id']) && isset($data['data'])) {
            // Verificar si cambió el estado
            $oldOrder = $this->find($data['id']);
            $newData = $data['data'];

            if ($oldOrder && isset($newData['status']) && $oldOrder['status'] !== $newData['status']) {
                $orderData = array_merge($oldOrder, $newData);
                \App\Libraries\WhatsAppEventHandler::triggerOrderStatusChanged(
                    $orderData,
                    $oldOrder['status'],
                    $newData['status']
                );
            }
        }
        return $data;
    }
}
