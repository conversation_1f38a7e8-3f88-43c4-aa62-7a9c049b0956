/**
 * Price Fix Script
 * Fixes the QNaN price display issue by correctly parsing price_regular and price_sale fields
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Price Fix Script loaded');
    
    // Wait for products to load, then fix prices
    setTimeout(fixPrices, 2000);
    
    // Also fix prices when products are loaded via AJAX
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const addedNodes = Array.from(mutation.addedNodes);
                const hasProductCards = addedNodes.some(node => 
                    node.nodeType === 1 && 
                    (node.classList?.contains('product-card') || node.querySelector?.('.product-card'))
                );
                
                if (hasProductCards) {
                    console.log('New product cards detected, fixing prices...');
                    setTimeout(fixPrices, 500);
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

let retryCount = 0;
const maxRetries = 5;

function fixPrices() {
    console.log('Attempting to fix prices...');

    const priceElements = document.querySelectorAll('.product-price');
    console.log(`Found ${priceElements.length} price elements`);

    if (priceElements.length === 0) {
        retryCount++;
        if (retryCount < maxRetries) {
            console.log(`No price elements found, retrying in 1 second... (${retryCount}/${maxRetries})`);
            setTimeout(fixPrices, 1000);
        } else {
            console.log('Max retries reached, stopping price fix attempts');
        }
        return;
    }
    
    // Check if prices are already showing QNaN
    const hasQNaN = Array.from(priceElements).some(el => el.textContent.includes('QNaN'));
    if (!hasQNaN) {
        console.log('Prices appear to be correct, no fix needed');
        return;
    }
    
    console.log('QNaN detected in prices, fetching product data...');
    
    // Get the product data from the API
    fetch('/MrCellCI4/api/products')
        .then(response => {
            console.log('API response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('API data received:', data);
            
            if (data.success && data.data) {
                console.log(`Processing ${data.data.length} products`);
                
                data.data.forEach((product, index) => {
                    if (priceElements[index]) {
                        const regularPrice = parseFloat(product.price_regular) || parseFloat(product.price) || 0;
                        const salePrice = parseFloat(product.price_sale) || parseFloat(product.sale_price) || 0;
                        
                        console.log(`Product ${index + 1}: ${product.name}`);
                        console.log(`  Regular price: ${regularPrice}`);
                        console.log(`  Sale price: ${salePrice}`);
                        
                        let priceHTML;
                        if (salePrice > 0 && salePrice < regularPrice) {
                            priceHTML = `
                                <span class="text-muted text-decoration-line-through small">Q${regularPrice.toFixed(2)}</span><br>
                                <span class="h5 text-primary fw-bold">Q${salePrice.toFixed(2)}</span>
                            `;
                            console.log(`  Display: Sale price Q${salePrice.toFixed(2)} (was Q${regularPrice.toFixed(2)})`);
                        } else {
                            priceHTML = `<span class="h5 text-primary fw-bold">Q${regularPrice.toFixed(2)}</span>`;
                            console.log(`  Display: Regular price Q${regularPrice.toFixed(2)}`);
                        }
                        
                        priceElements[index].innerHTML = priceHTML;
                    }
                });
                
                console.log('Price fix completed successfully');
            } else {
                console.error('Invalid API response format:', data);
            }
        })
        .catch(error => {
            console.error('Error fixing prices:', error);
        });
}

// Export for manual use
window.fixPrices = fixPrices;
