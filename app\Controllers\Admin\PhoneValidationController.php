<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\AdminController;
use App\Services\PhoneValidationService;

class PhoneValidationController extends AdminController
{
    protected $phoneValidationService;

    public function __construct()
    {
        parent::__construct();
        $this->phoneValidationService = new PhoneValidationService();
    }

    /**
     * Página principal de validación de teléfonos
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $data = [
            'title' => 'Validación de Teléfonos - Admin',
            'page_title' => 'Validación de Números de Teléfono'
        ];

        return view('admin/phone_validation/index', $data);
    }

    /**
     * Validar un número individual
     * POST /admin/phone-validation/validate
     */
    public function validateSingle()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $phoneNumber = $this->request->getPost('phone_number');
            
            if (empty($phoneNumber)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de teléfono requerido'
                ]);
            }

            $result = $this->phoneValidationService->validatePhoneNumber($phoneNumber);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en validación individual: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Validar números en lote
     * POST /admin/phone-validation/validate-batch
     */
    public function validateBatch()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $phoneNumbers = $this->request->getPost('phone_numbers');
            
            if (empty($phoneNumbers)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Lista de números requerida'
                ]);
            }

            // Convertir string a array si es necesario
            if (is_string($phoneNumbers)) {
                $phoneNumbers = array_filter(array_map('trim', explode("\n", $phoneNumbers)));
            }

            if (count($phoneNumbers) > 100) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Máximo 100 números por lote'
                ]);
            }

            $results = $this->phoneValidationService->validateBatch($phoneNumbers);

            return $this->response->setJSON([
                'success' => true,
                'data' => $results,
                'summary' => [
                    'total' => count($results),
                    'valid' => count(array_filter($results, fn($r) => $r['valid'])),
                    'invalid' => count(array_filter($results, fn($r) => !$r['valid']))
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en validación por lotes: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Validar números de usuarios existentes
     * POST /admin/phone-validation/validate-users
     */
    public function validateExistingUsers()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Obtener usuarios con teléfono
            $users = $this->db->query("
                SELECT id, name, email, phone 
                FROM users 
                WHERE phone IS NOT NULL 
                AND phone != '' 
                AND deleted_at IS NULL
                ORDER BY created_at DESC
                LIMIT 500
            ")->getResultArray();

            $phoneNumbers = array_column($users, 'phone');
            $results = $this->phoneValidationService->validateBatch($phoneNumbers);

            // Combinar resultados con datos de usuario
            $combinedResults = [];
            foreach ($users as $index => $user) {
                $combinedResults[] = [
                    'user' => $user,
                    'validation' => $results[$index] ?? ['valid' => false, 'error' => 'No validado']
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $combinedResults,
                'summary' => [
                    'total_users' => count($users),
                    'valid_phones' => count(array_filter($results, fn($r) => $r['valid'])),
                    'invalid_phones' => count(array_filter($results, fn($r) => !$r['valid']))
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error validando usuarios existentes: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Obtener estadísticas de validación
     * GET /admin/phone-validation/stats
     */
    public function getStats()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // Estadísticas de usuarios
            $totalUsers = $this->db->query("SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL")->getRow()->count;
            $usersWithPhone = $this->db->query("SELECT COUNT(*) as count FROM users WHERE phone IS NOT NULL AND phone != '' AND deleted_at IS NULL")->getRow()->count;
            
            // Estadísticas de WhatsApp (últimos 30 días)
            $whatsappSent = $this->db->query("
                SELECT COUNT(*) as count 
                FROM whatsapp_message_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ")->getRow()->count ?? 0;
            
            $whatsappFailed = $this->db->query("
                SELECT COUNT(*) as count 
                FROM whatsapp_message_logs 
                WHERE status = 'failed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ")->getRow()->count ?? 0;

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'users' => [
                        'total' => $totalUsers,
                        'with_phone' => $usersWithPhone,
                        'without_phone' => $totalUsers - $usersWithPhone,
                        'phone_percentage' => $totalUsers > 0 ? round(($usersWithPhone / $totalUsers) * 100, 2) : 0
                    ],
                    'whatsapp_last_30_days' => [
                        'sent' => $whatsappSent,
                        'failed' => $whatsappFailed,
                        'success_rate' => $whatsappSent > 0 ? round((($whatsappSent - $whatsappFailed) / $whatsappSent) * 100, 2) : 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo estadísticas: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Configurar APIs de validación
     * GET/POST /admin/phone-validation/config
     */
    public function config()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->updateConfig();
        }

        $data = [
            'title' => 'Configuración de APIs - Admin',
            'page_title' => 'Configuración de APIs de Validación',
            'current_config' => [
                'numverify_enabled' => !empty(env('NUMVERIFY_API_KEY')),
                'abstract_enabled' => !empty(env('ABSTRACT_PHONE_API_KEY')),
                'twilio_enabled' => !empty(env('TWILIO_ACCOUNT_SID')) && !empty(env('TWILIO_AUTH_TOKEN')),
                'validation_enabled' => env('VALIDATE_PHONE_BEFORE_WHATSAPP', true)
            ]
        ];

        return view('admin/phone_validation/config', $data);
    }

    /**
     * Actualizar configuración
     */
    private function updateConfig()
    {
        try {
            // Aquí podrías actualizar las variables de entorno
            // Por seguridad, esto debería hacerse manualmente en el .env
            
            return redirect()->back()->with('success', 'Configuración actualizada. Recuerda actualizar las claves API en el archivo .env');
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al actualizar configuración: ' . $e->getMessage());
        }
    }
}
