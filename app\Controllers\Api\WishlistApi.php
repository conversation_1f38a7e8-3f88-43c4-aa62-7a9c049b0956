<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Models\WishlistModel;
use App\Libraries\SimpleCache;

/**
 * API de Wishlist/Lista de Deseos
 * Compatible con cPanel hosting
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class WishlistApi extends ResourceController
{
    protected $wishlistModel;
    protected $format = 'json';
    
    public function __construct()
    {
        $this->wishlistModel = new WishlistModel();
    }
    
    /**
     * Obtener wishlist del usuario
     * GET /api/wishlist
     * 
     * Parámetros:
     * - priority: filtrar por prioridad (low, medium, high)
     * - sort_by: ordenar por (created_at, name, price, priority, stock)
     * - sort_order: orden (ASC, DESC)
     * - page: página (default: 1)
     * - limit: items por página (default: 20, max: 50)
     */
    public function index()
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('Debes iniciar sesión para ver tu lista de deseos');
            }
            
            // Obtener parámetros
            $priority = $this->request->getGet('priority');
            $sortBy = $this->request->getGet('sort_by') ?? 'created_at';
            $sortOrder = strtoupper($this->request->getGet('sort_order') ?? 'DESC');
            $page = max(1, (int) ($this->request->getGet('page') ?? 1));
            $limit = min(50, max(1, (int) ($this->request->getGet('limit') ?? 20)));
            $offset = ($page - 1) * $limit;
            
            // Validar parámetros
            if (!in_array($sortOrder, ['ASC', 'DESC'])) {
                $sortOrder = 'DESC';
            }
            
            $allowedSortFields = ['created_at', 'name', 'price', 'priority', 'stock'];
            if (!in_array($sortBy, $allowedSortFields)) {
                $sortBy = 'created_at';
            }
            
            // Cache key
            $cacheKey = "wishlist_user_{$userId}_" . md5(serialize([
                'priority' => $priority,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
                'page' => $page,
                'limit' => $limit
            ]));
            
            // Intentar obtener del cache
            $cachedData = SimpleCache::get($cacheKey);
            if ($cachedData) {
                return $this->respond([
                    'status' => 'success',
                    'data' => $cachedData,
                    'meta' => [
                        'cached' => true,
                        'execution_time' => 0
                    ]
                ]);
            }
            
            // Obtener datos
            $options = [
                'priority' => $priority,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
                'limit' => $limit,
                'offset' => $offset
            ];
            
            $items = $this->wishlistModel->getUserWishlistWithProducts($userId, $options);
            $totalCount = $this->wishlistModel->getUserWishlistCount($userId);
            $totalPages = ceil($totalCount / $limit);
            
            // Formatear items
            $formattedItems = array_map([$this, 'formatWishlistItem'], $items);
            
            $responseData = [
                'items' => $formattedItems,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $totalCount,
                    'total_pages' => $totalPages,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages,
                    'previous_page' => $page > 1 ? $page - 1 : null,
                    'next_page' => $page < $totalPages ? $page + 1 : null
                ],
                'filters' => [
                    'priority' => $priority,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder
                ],
                'stats' => $this->wishlistModel->getWishlistStats($userId)
            ];
            
            // Guardar en cache por 10 minutos
            SimpleCache::set($cacheKey, $responseData, 600);
            
            return $this->respond([
                'status' => 'success',
                'data' => $responseData,
                'meta' => [
                    'cached' => false,
                    'execution_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 3)
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::index: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'WISHLIST_ERROR'
            ], 500);
        }
    }
    
    /**
     * Agregar producto a wishlist
     * POST /api/wishlist
     * 
     * Body:
     * - product_id: ID del producto (requerido)
     * - notes: notas opcionales
     * - priority: prioridad (low, medium, high)
     * - notification_enabled: habilitar notificaciones (0/1)
     * - price_alert_threshold: precio para alerta
     */
    public function create()
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('Debes iniciar sesión para agregar productos a tu lista de deseos');
            }
            
            // Obtener datos del request
            $json = $this->request->getJSON(true);
            $productId = $json['product_id'] ?? null;
            
            if (!$productId || !is_numeric($productId)) {
                return $this->failValidationError('ID de producto requerido y debe ser numérico');
            }
            
            // Opciones adicionales
            $options = [
                'notes' => $json['notes'] ?? null,
                'priority' => $json['priority'] ?? 'medium',
                'notification_enabled' => isset($json['notification_enabled']) ? (int)$json['notification_enabled'] : 1,
                'price_alert_threshold' => $json['price_alert_threshold'] ?? null
            ];
            
            // Validar prioridad
            if (!in_array($options['priority'], ['low', 'medium', 'high'])) {
                $options['priority'] = 'medium';
            }
            
            // Agregar a wishlist
            $result = $this->wishlistModel->addToWishlist($userId, (int)$productId, $options);
            
            if ($result['success']) {
                // Limpiar cache del usuario
                $this->clearUserWishlistCache($userId);
                
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                $statusCode = 400;
                if ($result['code'] === 'ALREADY_EXISTS') {
                    $statusCode = 409; // Conflict
                } elseif ($result['code'] === 'PRODUCT_NOT_FOUND') {
                    $statusCode = 404; // Not Found
                }
                
                return $this->fail([
                    'status' => 'error',
                    'message' => $result['message'],
                    'error_code' => $result['code']
                ], $statusCode);
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::create: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Eliminar producto de wishlist
     * DELETE /api/wishlist/{product_id}
     */
    public function delete($productId = null)
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('Debes iniciar sesión para modificar tu lista de deseos');
            }
            
            if (!$productId || !is_numeric($productId)) {
                return $this->failValidationError('ID de producto requerido y debe ser numérico');
            }
            
            // Eliminar de wishlist
            $result = $this->wishlistModel->removeFromWishlist($userId, (int)$productId);
            
            if ($result['success']) {
                // Limpiar cache del usuario
                $this->clearUserWishlistCache($userId);
                
                return $this->respond([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                $statusCode = $result['code'] === 'NOT_FOUND' ? 404 : 400;
                
                return $this->fail([
                    'status' => 'error',
                    'message' => $result['message'],
                    'error_code' => $result['code']
                ], $statusCode);
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::delete: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Actualizar item de wishlist
     * PUT /api/wishlist/{product_id}
     * 
     * Body:
     * - notes: notas
     * - priority: prioridad
     * - notification_enabled: habilitar notificaciones
     * - price_alert_threshold: precio para alerta
     */
    public function update($productId = null)
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('Debes iniciar sesión para modificar tu lista de deseos');
            }
            
            if (!$productId || !is_numeric($productId)) {
                return $this->failValidationError('ID de producto requerido y debe ser numérico');
            }
            
            // Obtener datos del request
            $json = $this->request->getJSON(true);
            
            if (empty($json)) {
                return $this->failValidationError('No se enviaron datos para actualizar');
            }
            
            // Actualizar item
            $result = $this->wishlistModel->updateWishlistItem($userId, (int)$productId, $json);
            
            if ($result['success']) {
                // Limpiar cache del usuario
                $this->clearUserWishlistCache($userId);
                
                return $this->respond([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                $statusCode = $result['code'] === 'NOT_FOUND' ? 404 : 400;
                
                return $this->fail([
                    'status' => 'error',
                    'message' => $result['message'],
                    'error_code' => $result['code']
                ], $statusCode);
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::update: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Verificar si producto está en wishlist
     * GET /api/wishlist/check/{product_id}
     */
    public function check($productId = null)
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->respond([
                    'status' => 'success',
                    'data' => [
                        'in_wishlist' => false,
                        'requires_login' => true
                    ]
                ]);
            }
            
            if (!$productId || !is_numeric($productId)) {
                return $this->failValidationError('ID de producto requerido y debe ser numérico');
            }
            
            $inWishlist = $this->wishlistModel->isInWishlist($userId, (int)$productId);
            
            return $this->respond([
                'status' => 'success',
                'data' => [
                    'in_wishlist' => $inWishlist,
                    'requires_login' => false
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::check: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Obtener contador de wishlist
     * GET /api/wishlist/count
     */
    public function count()
    {
        try {
            // Verificar autenticación
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->respond([
                    'status' => 'success',
                    'data' => [
                        'count' => 0,
                        'requires_login' => true
                    ]
                ]);
            }
            
            $count = $this->wishlistModel->getUserWishlistCount($userId);
            
            return $this->respond([
                'status' => 'success',
                'data' => [
                    'count' => $count,
                    'requires_login' => false
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistApi::count: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Formatear item de wishlist para respuesta
     */
    private function formatWishlistItem(array $item): array
    {
        return [
            'wishlist_id' => (int) $item['id'],
            'product' => [
                'id' => (int) $item['product_id'],
                'name' => $item['name'],
                'slug' => $item['slug'],
                'url' => $item['product_url'],
                'image' => $item['image_url'],
                'category' => $item['category_name'],
                'brand' => $item['brand_name']
            ],
            'price' => [
                'regular' => (float) $item['price_regular'],
                'sale' => $item['price_sale'] ? (float) $item['price_sale'] : null,
                'final' => (float) $item['final_price'],
                'formatted' => 'Q' . number_format($item['final_price'], 2),
                'has_discount' => $item['has_discount'],
                'discount_percentage' => $item['discount_percentage']
            ],
            'stock' => [
                'quantity' => (int) $item['stock_quantity'],
                'status' => $item['stock_status'],
                'in_stock' => $item['in_stock']
            ],
            'rating' => [
                'average' => (float) $item['rating_average'],
                'count' => (int) $item['rating_count']
            ],
            'wishlist_data' => [
                'notes' => $item['notes'],
                'priority' => $item['priority'],
                'notification_enabled' => (bool) $item['notification_enabled'],
                'price_alert_threshold' => $item['price_alert_threshold'] ? (float) $item['price_alert_threshold'] : null,
                'price_alert_triggered' => $item['price_alert_triggered'],
                'added_at' => $item['created_at']
            ]
        ];
    }
    
    /**
     * Obtener ID del usuario autenticado
     */
    private function getUserId(): ?int
    {
        $session = session();
        return $session->get('user_id') ? (int) $session->get('user_id') : null;
    }
    
    /**
     * Limpiar cache de wishlist del usuario
     */
    private function clearUserWishlistCache(int $userId): void
    {
        $cacheDir = WRITEPATH . 'cache/simple/';
        $files = glob($cacheDir . '*.cache');
        
        foreach ($files as $file) {
            $filename = basename($file, '.cache');
            if (strpos($filename, "wishlist_user_{$userId}_") === 0) {
                unlink($file);
            }
        }
    }
}
