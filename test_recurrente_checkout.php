<?php

/**
 * Script para probar el checkout con Recurrente
 */

echo "=== PRUEBA DE CHECKOUT CON RECURRENTE ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "========================================\n\n";

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Obtener productos sincronizados con Recurrente
    echo "🔍 Buscando productos sincronizados con Recurrente...\n";
    
    $stmt = $db->prepare("
        SELECT id, name, sku, recurrente_product_id, price_regular, price_sale, currency
        FROM products 
        WHERE recurrente_sync_status = 'synced' 
        AND recurrente_product_id IS NOT NULL
        AND is_active = 1
        AND deleted_at IS NULL
        ORDER BY id DESC
        LIMIT 5
    ");
    $stmt->execute();
    $products = $stmt->fetchAll();

    if (empty($products)) {
        echo "❌ No hay productos sincronizados con Recurrente para probar\n";
        exit(1);
    }

    echo "📦 Productos disponibles para checkout:\n";
    foreach ($products as $product) {
        $price = !empty($product['price_sale']) && $product['price_sale'] > 0
            ? $product['price_sale']
            : $product['price_regular'];
        
        echo "   ID: {$product['id']} | SKU: {$product['sku']} | Nombre: {$product['name']}\n";
        echo "   Recurrente ID: {$product['recurrente_product_id']} | Precio: {$product['currency']} " . number_format($price, 2) . "\n\n";
    }

    // Función para hacer petición al API
    function makeApiRequest($url, $data, $method = 'POST') {
        $ch = curl_init();
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false, // Solo para pruebas locales
            CURLOPT_SSL_VERIFYHOST => false  // Solo para pruebas locales
        ];
        
        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }
        
        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        return [
            'http_code' => $httpCode,
            'response' => $response,
            'error' => $error
        ];
    }

    // Casos de prueba para checkout
    $checkoutTests = [
        [
            'name' => 'Checkout con un producto',
            'data' => [
                'cart_items' => [
                    [
                        'product_id' => $products[0]['id'],
                        'quantity' => 1
                    ]
                ],
                'metadata' => [
                    'test_case' => 'single_product',
                    'user_id' => 'test_user_123'
                ]
            ]
        ],
        [
            'name' => 'Checkout con múltiples productos',
            'data' => [
                'cart_items' => [
                    [
                        'product_id' => $products[0]['id'],
                        'quantity' => 2
                    ],
                    [
                        'product_id' => $products[1]['id'] ?? $products[0]['id'],
                        'quantity' => 1
                    ]
                ],
                'metadata' => [
                    'test_case' => 'multiple_products',
                    'user_id' => 'test_user_456'
                ]
            ]
        ]
    ];

    // Probar cada caso
    foreach ($checkoutTests as $test) {
        echo "🧪 Probando: {$test['name']}\n";
        echo "   Datos del carrito:\n";
        
        foreach ($test['data']['cart_items'] as $item) {
            $product = array_filter($products, function($p) use ($item) {
                return $p['id'] == $item['product_id'];
            });
            $product = reset($product);
            
            if ($product) {
                echo "      - {$product['name']} (Cantidad: {$item['quantity']})\n";
            }
        }
        
        echo "   Enviando petición al API...\n";
        
        // URL del endpoint (ajustar según tu configuración)
        $checkoutUrl = 'https://mrcell.com.gt/api/v1/payments/recurrente/checkout';
        
        $result = makeApiRequest($checkoutUrl, $test['data']);
        
        echo "   HTTP Code: {$result['http_code']}\n";
        
        if ($result['error']) {
            echo "   ❌ Error de cURL: {$result['error']}\n";
        } else {
            $decodedResponse = json_decode($result['response'], true);
            
            if ($decodedResponse) {
                echo "   📄 Respuesta del API:\n";
                echo "      Status: " . ($decodedResponse['status'] ?? 'N/A') . "\n";
                
                if (isset($decodedResponse['data'])) {
                    $data = $decodedResponse['data'];
                    echo "      Checkout ID: " . ($data['checkout_id'] ?? 'N/A') . "\n";
                    echo "      Checkout URL: " . ($data['checkout_url'] ?? 'N/A') . "\n";
                    echo "      Total Items: " . ($data['total_items'] ?? 'N/A') . "\n";
                    
                    if (isset($data['expires_at'])) {
                        echo "      Expira: {$data['expires_at']}\n";
                    }
                    
                    // Si se creó exitosamente, probar obtener el estado
                    if (isset($data['checkout_id'])) {
                        echo "   🔍 Consultando estado del checkout...\n";
                        
                        $statusUrl = "https://mrcell.com.gt/api/v1/payments/recurrente/checkout/{$data['checkout_id']}";
                        $statusResult = makeApiRequest($statusUrl, [], 'GET');
                        
                        echo "   Estado HTTP: {$statusResult['http_code']}\n";
                        
                        if (!$statusResult['error']) {
                            $statusResponse = json_decode($statusResult['response'], true);
                            if ($statusResponse && isset($statusResponse['data'])) {
                                echo "   📊 Estado del checkout:\n";
                                $statusData = $statusResponse['data'];
                                echo "      ID: " . ($statusData['id'] ?? 'N/A') . "\n";
                                echo "      Estado: " . ($statusData['status'] ?? 'N/A') . "\n";
                                echo "      URL: " . ($statusData['checkout_url'] ?? 'N/A') . "\n";
                            }
                        }
                    }
                    
                } elseif (isset($decodedResponse['message'])) {
                    echo "      Mensaje: {$decodedResponse['message']}\n";
                    
                    if (isset($decodedResponse['invalid_products'])) {
                        echo "      Productos inválidos:\n";
                        foreach ($decodedResponse['invalid_products'] as $invalid) {
                            echo "         - {$invalid}\n";
                        }
                    }
                }
            } else {
                echo "   📄 Respuesta cruda: {$result['response']}\n";
            }
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }

    // Probar caso con producto no sincronizado
    echo "🧪 Probando: Checkout con producto no sincronizado\n";
    
    $stmt = $db->prepare("
        SELECT id, name 
        FROM products 
        WHERE (recurrente_product_id IS NULL OR recurrente_sync_status != 'synced')
        AND is_active = 1
        AND deleted_at IS NULL
        LIMIT 1
    ");
    $stmt->execute();
    $unsyncedProduct = $stmt->fetch();
    
    if ($unsyncedProduct) {
        echo "   Producto no sincronizado: {$unsyncedProduct['name']} (ID: {$unsyncedProduct['id']})\n";
        
        $invalidTest = [
            'cart_items' => [
                [
                    'product_id' => $unsyncedProduct['id'],
                    'quantity' => 1
                ]
            ],
            'metadata' => [
                'test_case' => 'invalid_product'
            ]
        ];
        
        $result = makeApiRequest($checkoutUrl, $invalidTest);
        
        echo "   HTTP Code: {$result['http_code']}\n";
        
        if (!$result['error']) {
            $decodedResponse = json_decode($result['response'], true);
            if ($decodedResponse) {
                echo "   📄 Respuesta esperada (error):\n";
                echo "      Status: " . ($decodedResponse['status'] ?? 'N/A') . "\n";
                echo "      Mensaje: " . ($decodedResponse['message'] ?? 'N/A') . "\n";
                
                if (isset($decodedResponse['invalid_products'])) {
                    echo "      Productos inválidos:\n";
                    foreach ($decodedResponse['invalid_products'] as $invalid) {
                        echo "         - {$invalid}\n";
                    }
                }
            }
        }
    } else {
        echo "   ℹ️  No hay productos no sincronizados para probar\n";
    }

    echo "\n🎉 Pruebas de checkout completadas.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
