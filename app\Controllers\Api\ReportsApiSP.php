<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class ReportsApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Reporte de ventas por período
     * GET /api/reports/sales
     */
    public function salesReport()
    {
        try {
            $periodType = $this->request->getGet('period_type') ?? 'daily';
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');
            $categoryId = $this->request->getGet('category_id');
            $productId = $this->request->getGet('product_id');

            // Validar período
            $validPeriods = ['daily', 'weekly', 'monthly', 'yearly'];
            if (!in_array($periodType, $validPeriods)) {
                return $this->failValidationError('Período no válido');
            }

            $query = $this->db->query("CALL sp_sales_report_by_period(?, ?, ?, ?, ?)", [
                $periodType, $dateFrom, $dateTo, $categoryId, $productId
            ]);

            $salesData = $query->getResultArray();

            // Calcular totales
            $totals = [
                'total_orders' => array_sum(array_column($salesData, 'total_orders')),
                'total_quantity' => array_sum(array_column($salesData, 'total_quantity')),
                'total_revenue' => array_sum(array_column($salesData, 'total_revenue')),
                'total_shipping' => array_sum(array_column($salesData, 'total_shipping')),
                'avg_order_value' => count($salesData) > 0 ? array_sum(array_column($salesData, 'avg_order_value')) / count($salesData) : 0
            ];

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'period_type' => $periodType,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'sales_data' => $salesData,
                    'totals' => $totals
                ],
                'message' => 'Reporte de ventas generado correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ReportsApiSP::salesReport: ' . $e->getMessage());
            return $this->failServerError('Error al generar reporte de ventas');
        }
    }

    /**
     * Reporte de productos más vendidos
     * GET /api/reports/top-products
     */
    public function topProductsReport()
    {
        try {
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');
            $categoryId = $this->request->getGet('category_id');
            $limit = (int) ($this->request->getGet('limit') ?? 20);

            $query = $this->db->query("CALL sp_top_selling_products_report(?, ?, ?, ?)", [
                $dateFrom, $dateTo, $categoryId, $limit
            ]);

            $topProducts = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'category_id' => $categoryId,
                    'limit' => $limit,
                    'products' => $topProducts
                ],
                'message' => 'Reporte de productos más vendidos generado correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ReportsApiSP::topProductsReport: ' . $e->getMessage());
            return $this->failServerError('Error al generar reporte de productos');
        }
    }

    /**
     * Reporte detallado de inventario
     * GET /api/reports/inventory
     */
    public function inventoryReport()
    {
        try {
            $categoryId = $this->request->getGet('category_id');
            $stockStatus = $this->request->getGet('stock_status') ?? 'all';
            $sortBy = $this->request->getGet('sort_by') ?? 'name';

            // Validar parámetros
            $validStatuses = ['all', 'low', 'out', 'normal'];
            $validSorts = ['name', 'stock', 'value', 'category'];

            if (!in_array($stockStatus, $validStatuses)) {
                return $this->failValidationError('Estado de stock no válido');
            }

            if (!in_array($sortBy, $validSorts)) {
                return $this->failValidationError('Criterio de ordenamiento no válido');
            }

            $query = $this->db->query("CALL sp_inventory_detailed_report(?, ?, ?)", [
                $categoryId, $stockStatus, $sortBy
            ]);

            $inventoryData = $query->getResultArray();

            // Calcular estadísticas
            $stats = [
                'total_products' => count($inventoryData),
                'total_inventory_value' => array_sum(array_column($inventoryData, 'inventory_value')),
                'out_of_stock' => count(array_filter($inventoryData, fn($item) => $item['stock_quantity'] == 0)),
                'low_stock' => count(array_filter($inventoryData, fn($item) => $item['stock_quantity'] > 0 && $item['stock_quantity'] <= 5)),
                'normal_stock' => count(array_filter($inventoryData, fn($item) => $item['stock_quantity'] > 5))
            ];

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'filters' => [
                        'category_id' => $categoryId,
                        'stock_status' => $stockStatus,
                        'sort_by' => $sortBy
                    ],
                    'statistics' => $stats,
                    'inventory' => $inventoryData
                ],
                'message' => 'Reporte de inventario generado correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ReportsApiSP::inventoryReport: ' . $e->getMessage());
            return $this->failServerError('Error al generar reporte de inventario');
        }
    }

    /**
     * Dashboard de métricas generales
     * GET /api/reports/dashboard
     */
    public function dashboardMetrics()
    {
        try {
            $period = $this->request->getGet('period') ?? '30'; // días

            // Métricas de ventas
            $salesQuery = $this->db->query("
                SELECT 
                    COUNT(DISTINCT o.id) as total_orders,
                    SUM(o.total) as total_revenue,
                    AVG(o.total) as avg_order_value,
                    COUNT(DISTINCT o.customer_email) as unique_customers
                FROM orders o
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ", [$period]);
            $salesMetrics = $salesQuery->getRowArray();

            // Métricas de productos
            $productsQuery = $this->db->query("
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(CASE WHEN stock_quantity = 0 THEN 1 END) as out_of_stock,
                    COUNT(CASE WHEN stock_quantity <= 5 AND stock_quantity > 0 THEN 1 END) as low_stock,
                    SUM(stock_quantity * price_regular) as inventory_value
                FROM products 
                WHERE deleted_at IS NULL AND is_active = 1
            ");
            $productsMetrics = $productsQuery->getRowArray();

            // Top 5 productos vendidos en el período
            $topProductsQuery = $this->db->query("
                SELECT 
                    p.name,
                    SUM(oi.quantity) as total_sold,
                    SUM(oi.quantity * oi.price) as revenue
                FROM products p
                INNER JOIN order_items oi ON oi.product_id = p.id
                INNER JOIN orders o ON o.id = oi.order_id
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY p.id, p.name
                ORDER BY total_sold DESC
                LIMIT 5
            ", [$period]);
            $topProducts = $topProductsQuery->getResultArray();

            // Ventas por día (últimos 7 días)
            $dailySalesQuery = $this->db->query("
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as orders,
                    SUM(total) as revenue
                FROM orders 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            ");
            $dailySales = $dailySalesQuery->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'period_days' => $period,
                    'sales_metrics' => $salesMetrics,
                    'products_metrics' => $productsMetrics,
                    'top_products' => $topProducts,
                    'daily_sales' => $dailySales
                ],
                'message' => 'Métricas del dashboard obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ReportsApiSP::dashboardMetrics: ' . $e->getMessage());
            return $this->failServerError('Error al obtener métricas del dashboard');
        }
    }

    /**
     * Exportar reporte a CSV
     * GET /api/reports/export/{type}
     */
    public function exportReport($type = null)
    {
        try {
            if (!$type || !in_array($type, ['sales', 'products', 'inventory'])) {
                return $this->failValidationError('Tipo de reporte no válido');
            }

            $data = [];
            $filename = '';
            $headers = [];

            switch ($type) {
                case 'sales':
                    $query = $this->db->query("CALL sp_sales_report_by_period('daily', DATE_SUB(NOW(), INTERVAL 30 DAY), NOW(), NULL, NULL)");
                    $data = $query->getResultArray();
                    $filename = 'reporte_ventas_' . date('Y-m-d') . '.csv';
                    $headers = ['Fecha', 'Pedidos', 'Productos', 'Cantidad', 'Ingresos', 'Valor Promedio', 'Envío'];
                    break;

                case 'products':
                    $query = $this->db->query("CALL sp_top_selling_products_report(DATE_SUB(NOW(), INTERVAL 30 DAY), NOW(), NULL, 50)");
                    $data = $query->getResultArray();
                    $filename = 'productos_mas_vendidos_' . date('Y-m-d') . '.csv';
                    $headers = ['ID', 'Producto', 'SKU', 'Precio', 'Categoría', 'Vendidos', 'Pedidos', 'Ingresos'];
                    break;

                case 'inventory':
                    $query = $this->db->query("CALL sp_inventory_detailed_report(NULL, 'all', 'name')");
                    $data = $query->getResultArray();
                    $filename = 'reporte_inventario_' . date('Y-m-d') . '.csv';
                    $headers = ['ID', 'Producto', 'SKU', 'Stock', 'Precio', 'Valor', 'Categoría', 'Estado'];
                    break;
            }

            // Generar CSV
            $csv = fopen('php://temp', 'r+');
            fputcsv($csv, $headers);

            foreach ($data as $row) {
                fputcsv($csv, array_values($row));
            }

            rewind($csv);
            $csvContent = stream_get_contents($csv);
            fclose($csv);

            // Configurar headers para descarga
            $this->response->setHeader('Content-Type', 'text/csv');
            $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');
            $this->response->setHeader('Content-Length', strlen($csvContent));

            return $this->response->setBody($csvContent);

        } catch (\Exception $e) {
            log_message('error', 'Error en ReportsApiSP::exportReport: ' . $e->getMessage());
            return $this->failServerError('Error al exportar reporte');
        }
    }
}
