<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-eye me-2"></i>Detalle del Pedido</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/admin/orders">Pedidos</a></li>
                    <li class="breadcrumb-item active"><?= esc($order['order_number']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/admin/orders" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Volver
            </a>
            <a href="/admin/orders/edit/<?= $order['id'] ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Editar
            </a>
        </div>
    </div>
</div>

<!-- Información del Pedido -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información del Pedido</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Número de Pedido:</strong></td>
                                <td><?= esc($order['order_number']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Estado:</strong></td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-secondary';
                                    $statusText = 'Desconocido';
                                    
                                    switch ($order['status']) {
                                        case 'pending':
                                            $statusClass = 'bg-warning';
                                            $statusText = 'Pendiente';
                                            break;
                                        case 'processing':
                                            $statusClass = 'bg-info';
                                            $statusText = 'Procesando';
                                            break;
                                        case 'shipped':
                                            $statusClass = 'bg-primary';
                                            $statusText = 'Enviado';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'bg-success';
                                            $statusText = 'Entregado';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-danger';
                                            $statusText = 'Cancelado';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Total:</strong></td>
                                <td><strong>Q<?= number_format($order['total'], 2) ?></strong></td>
                            </tr>
                            <tr>
                                <td><strong>Método de Pago:</strong></td>
                                <td><?= esc($order['payment_method'] ?? 'No especificado') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Estado de Pago:</strong></td>
                                <td>
                                    <?php
                                    $paymentClass = $order['payment_status'] === 'paid' ? 'bg-success' : 'bg-warning';
                                    $paymentText = $order['payment_status'] === 'paid' ? 'Pagado' : 'Pendiente';
                                    ?>
                                    <span class="badge <?= $paymentClass ?>"><?= $paymentText ?></span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Fecha de Pedido:</strong></td>
                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Última Actualización:</strong></td>
                                <td><?= $order['updated_at'] ? date('d/m/Y H:i', strtotime($order['updated_at'])) : 'N/A' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Información del Cliente</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Nombre:</strong></td>
                        <td><?= esc($order['customer_name']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?= esc($order['customer_email']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Teléfono:</strong></td>
                        <td><?= esc($order['customer_phone'] ?? 'No especificado') ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Productos del Pedido -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Productos del Pedido</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Producto</th>
                        <th>Cantidad</th>
                        <th>Precio Unitario</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (isset($order['items']) && !empty($order['items'])): ?>
                        <?php foreach ($order['items'] as $item): ?>
                            <tr>
                                <td><?= esc($item['product_name']) ?></td>
                                <td><?= esc($item['quantity']) ?></td>
                                <td>Q<?= number_format($item['price'], 2) ?></td>
                                <td>Q<?= number_format($item['quantity'] * $item['price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center text-muted">No hay productos en este pedido</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3" class="text-end">Total:</th>
                        <th>Q<?= number_format($order['total'], 2) ?></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
