/**
 * MrCell - JavaScript Functions
 * Funcionalidades principales para la tienda online
 */

class MrCell {
    constructor() {
        this.baseUrl = window.location.origin;
        this.apiUrl = this.baseUrl + '/api';
        this.cartCount = 0;
        this.init();
    }

    init() {
        this.loadCartCount();
        this.bindEvents();
        this.initTooltips();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-add-cart, .btn-add-cart *')) {
                e.preventDefault();
                const button = e.target.closest('.btn-add-cart');
                const productId = button.dataset.productId;
                const quantity = button.dataset.quantity || 1;
                this.addToCart(productId, quantity, button);
            }
        });

        // Cart quantity controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quantity-btn-plus')) {
                this.updateCartQuantity(e.target, 'increase');
            }
            if (e.target.matches('.quantity-btn-minus')) {
                this.updateCartQuantity(e.target, 'decrease');
            }
        });

        // Remove from cart
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-remove-cart, .btn-remove-cart *')) {
                e.preventDefault();
                const button = e.target.closest('.btn-remove-cart');
                const productId = button.dataset.productId;
                this.removeFromCart(productId, button);
            }
        });

        // Search functionality
        const searchForm = document.querySelector('#search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }
    }

    /**
     * Initialize tooltips
     */
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * Load cart count
     */
    async loadCartCount() {
        try {
            const response = await fetch(`${this.apiUrl}/cart/count`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.cartCount = data.count;
                this.updateCartCountDisplay();
            }
        } catch (error) {
            console.error('Error loading cart count:', error);
        }
    }

    /**
     * Update cart count display
     */
    updateCartCountDisplay() {
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = this.cartCount;
            element.style.display = this.cartCount > 0 ? 'inline' : 'none';
        });
    }

    /**
     * Add product to cart
     */
    async addToCart(productId, quantity = 1, button = null) {
        if (!productId) {
            this.showAlert('Error: ID de producto no válido', 'error');
            return;
        }

        // Show loading state
        if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Agregando...';
            button.disabled = true;
        }

        try {
            const response = await fetch(`${this.apiUrl}/cart/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: parseInt(quantity)
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.cartCount = data.cart_count;
                this.updateCartCountDisplay();

                // Mostrar modal elegante en lugar de alerta
                if (window.showCartModal) {
                    window.showCartModal(
                        data.product_name || 'Producto',
                        data.cart_count,
                        data.cart_total
                    );
                } else {
                    // Fallback a alerta si el modal no está disponible
                    this.showAlert('Producto agregado al carrito', 'success');
                }

                // Update button state
                if (button) {
                    button.innerHTML = '<i class="fas fa-check me-2"></i>¡Agregado!';
                    button.classList.remove('btn-primary');
                    button.classList.add('btn-success');

                    setTimeout(() => {
                        button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
                        button.classList.remove('btn-success');
                        button.classList.add('btn-primary');
                        button.disabled = false;
                    }, 2000);
                }
            } else {
                throw new Error(data.message || 'Error al agregar producto');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showAlert('Error al agregar producto al carrito', 'error');
            
            if (button) {
                button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
                button.disabled = false;
            }
        }
    }

    /**
     * Remove product from cart
     */
    async removeFromCart(productId, button = null) {
        if (!confirm('¿Estás seguro de que quieres remover este producto del carrito?')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}/cart/remove/${productId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.cartCount = data.cart_count;
                this.updateCartCountDisplay();
                this.showAlert('Producto removido del carrito', 'success');
                
                // Remove item from DOM if on cart page
                if (button) {
                    const cartItem = button.closest('.cart-item');
                    if (cartItem) {
                        cartItem.remove();
                    }
                }
                
                // Reload cart if on cart page
                if (window.location.pathname.includes('carrito')) {
                    this.loadCartItems();
                }
            } else {
                throw new Error(data.message || 'Error al remover producto');
            }
        } catch (error) {
            console.error('Error removing from cart:', error);
            this.showAlert('Error al remover producto del carrito', 'error');
        }
    }

    /**
     * Update cart quantity
     */
    async updateCartQuantity(button, action) {
        const cartItem = button.closest('.cart-item');
        const productId = cartItem.dataset.productId;
        const quantityInput = cartItem.querySelector('.quantity-input');
        let currentQuantity = parseInt(quantityInput.value);
        
        if (action === 'increase') {
            currentQuantity++;
        } else if (action === 'decrease' && currentQuantity > 1) {
            currentQuantity--;
        } else {
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}/cart/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: currentQuantity
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                quantityInput.value = currentQuantity;
                this.cartCount = data.cart_count;
                this.updateCartCountDisplay();
                this.updateCartTotals();
            } else {
                throw new Error(data.message || 'Error al actualizar cantidad');
            }
        } catch (error) {
            console.error('Error updating quantity:', error);
            this.showAlert('Error al actualizar cantidad', 'error');
        }
    }

    /**
     * Load cart items
     */
    async loadCartItems() {
        try {
            const response = await fetch(`${this.apiUrl}/cart/items`);
            const data = await response.json();

            if (data.status === 'success') {
                this.renderCartItems(data.data.items);
                this.renderCartTotals(data.data.totals);
            }
        } catch (error) {
            console.error('Error loading cart items:', error);
        }
    }

    /**
     * Render cart items
     */
    renderCartItems(items) {
        const container = document.querySelector('#cart-items');
        if (!container) return;

        if (items.length === 0) {
            container.innerHTML = `
                <div class="empty-cart text-center py-5">
                    <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                    <h3>Tu carrito está vacío</h3>
                    <p class="text-muted mb-4">¡Agrega algunos productos increíbles a tu carrito!</p>
                    <a href="/tienda" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Ir a la Tienda
                    </a>
                </div>
            `;
            return;
        }

        let html = '';
        items.forEach(item => {
            html += `
                <div class="cart-item" data-product-id="${item.product_id}">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <img src="${getProductImageUrl(item.image)}" alt="${item.name}" class="product-image">
                        </div>
                        <div class="col-md-4">
                            <h5 class="mb-1">${item.name}</h5>
                            <p class="text-muted mb-0">SKU: ${item.sku || 'N/A'}</p>
                        </div>
                        <div class="col-md-2">
                            <div class="quantity-controls">
                                <button class="quantity-btn quantity-btn-minus">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1">
                                <button class="quantity-btn quantity-btn-plus">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <strong class="text-primary">Q${item.total.toFixed(2)}</strong>
                        </div>
                        <div class="col-md-2 text-center">
                            <button class="btn btn-outline-danger btn-sm btn-remove-cart" data-product-id="${item.product_id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    /**
     * Render cart totals
     */
    renderCartTotals(totals) {
        const elements = {
            subtotal: document.querySelector('#subtotal'),
            shipping: document.querySelector('#shipping'),
            tax: document.querySelector('#tax'),
            total: document.querySelector('#total')
        };

        if (elements.subtotal) elements.subtotal.textContent = `Q${totals.subtotal.toFixed(2)}`;
        if (elements.shipping) elements.shipping.textContent = `Q${totals.shipping.toFixed(2)}`;
        if (elements.tax) elements.tax.textContent = `Q${totals.tax.toFixed(2)}`;
        if (elements.total) elements.total.textContent = `Q${totals.total.toFixed(2)}`;
    }

    /**
     * Update cart totals
     */
    updateCartTotals() {
        this.loadCartItems();
    }

    /**
     * Perform search
     */
    async performSearch() {
        const searchInput = document.querySelector('#search-input');
        const query = searchInput.value.trim();

        if (!query) {
            this.showAlert('Por favor ingresa un término de búsqueda', 'warning');
            return;
        }

        window.location.href = `/tienda?search=${encodeURIComponent(query)}`;
    }

    /**
     * Show alert message
     */
    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    /**
     * Format currency
     */
    formatCurrency(amount) {
        return `Q${parseFloat(amount).toFixed(2)}`;
    }
}

// Utility functions
function getProductImageUrl(imageName, defaultImage = 'no-image.jpg') {
    if (!imageName) {
        return `/assets/img/${defaultImage}`;
    }

    // Si ya es una URL completa, devolverla tal como está
    if (imageName.startsWith('http')) {
        return imageName;
    }

    // Si contiene una ruta relativa completa, usar tal como está
    if (imageName.startsWith('assets/')) {
        return `/${imageName}`;
    }

    // Si es solo el nombre del archivo, construir la ruta completa
    return `/assets/img/products/${imageName}`;
}

// Initialize MrCell when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.MrCell = new MrCell();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MrCell;
}
