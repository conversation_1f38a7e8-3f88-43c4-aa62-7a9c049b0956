<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Checkout - MrCell Guatemala<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    :root {
        --primary-color: #dc2626;
        --primary-dark: #b91c1c;
    }

    .checkout-steps {
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .step.active {
        background-color: #e7f3ff;
        border: 2px solid #007bff;
    }

    .step.completed {
        background-color: #d4edda;
        border: 2px solid #28a745;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }

    .step.active .step-number {
        background-color: #007bff;
        color: white;
    }

    .step.completed .step-number {
        background-color: #28a745;
        color: white;
    }

    .address-card {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .address-card:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .address-card.selected {
        border-color: #007bff;
        background-color: #e7f3ff;
    }

    .shipping-method {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .shipping-method:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .shipping-method.selected {
        border-color: #007bff;
        background-color: #e7f3ff;
    }

    .order-summary {
        position: sticky;
        top: 2rem;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <!-- Pasos del Checkout -->
    <div class="checkout-steps">
        <div class="row">
            <div class="col-md-4">
                <div class="step active" id="step-1">
                    <div class="step-number">1</div>
                    <div>
                        <h6 class="mb-0">Dirección de Envío</h6>
                        <small class="text-muted">Selecciona o agrega una dirección</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="step" id="step-2">
                    <div class="step-number">2</div>
                    <div>
                        <h6 class="mb-0">Método de Envío</h6>
                        <small class="text-muted">Selecciona cómo quieres recibir tu pedido</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="step" id="step-3">
                    <div class="step-number">3</div>
                    <div>
                        <h6 class="mb-0">Pago</h6>
                        <small class="text-muted">Confirma y paga tu pedido</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contenido Principal -->
        <div class="col-lg-8">
            <!-- Paso 1: Selección de Dirección -->
            <div class="card mb-4" id="address-section">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Dirección de Envío</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($addresses)): ?>
                        <h6>Selecciona una dirección:</h6>
                        <div id="addresses-list">
                            <?php foreach ($addresses as $address): ?>
                                <div class="address-card <?= $address['is_default'] ? 'selected' : '' ?>" 
                                     data-address-id="<?= $address['id'] ?>"
                                     onclick="selectAddress(<?= $address['id'] ?>)">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="address" 
                                               value="<?= $address['id'] ?>" 
                                               <?= $address['is_default'] ? 'checked' : '' ?>>
                                        <label class="form-check-label w-100">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <strong><?= esc($address['address_name'] ?? 'Dirección') ?></strong>
                                                    <?php if ($address['is_default']): ?>
                                                        <span class="badge bg-primary ms-2">Por defecto</span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <span><?= esc($address['nombre_completo']) ?></span><br>
                                                    <span><?= esc($address['direccion_linea_1']) ?></span>
                                                    <?php if (!empty($address['direccion_linea_2'])): ?>
                                                        <br><span><?= esc($address['direccion_linea_2']) ?></span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <span><?= esc($address['ciudad']) ?>, <?= esc($address['estado_departamento']) ?></span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-phone me-1"></i><?= esc($address['telefono']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <hr>
                    <?php endif; ?>

                    <button type="button" class="btn btn-outline-primary" onclick="showAddressForm()">
                        <i class="fas fa-plus me-2"></i>Agregar Nueva Dirección
                    </button>

                    <!-- Formulario para nueva dirección (oculto inicialmente) -->
                    <div id="address-form" class="mt-3" style="display: none;">
                        <h6>Nueva Dirección:</h6>
                        <form id="new-address-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Nombre de la dirección *</label>
                                        <input type="text" class="form-control" name="name" 
                                               placeholder="Ej: Casa, Oficina, etc." required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Nombre del destinatario *</label>
                                        <input type="text" class="form-control" name="nombre_completo" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Teléfono *</label>
                                        <input type="tel" class="form-control" name="telefono" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Dirección *</label>
                                <input type="text" class="form-control" name="direccion_linea_1"
                                       placeholder="Calle, número, colonia" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Dirección línea 2</label>
                                <input type="text" class="form-control" name="direccion_linea_2"
                                       placeholder="Apartamento, edificio, referencias">
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Departamento *</label>
                                        <select class="form-select" name="estado_departamento" id="department-select" required onchange="loadMunicipalities()">
                                            <option value="">Seleccionar departamento</option>
                                            <?php foreach ($locations as $department => $municipalities): ?>
                                                <option value="<?= esc($department) ?>"><?= esc($department) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Municipio *</label>
                                        <select class="form-select" name="ciudad" id="municipality-select" required>
                                            <option value="">Seleccionar municipio</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Código postal</label>
                                        <input type="text" class="form-control" name="postal_code">
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Guardar Dirección
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideAddressForm()">
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Paso 2: Métodos de Envío (inicialmente oculto) -->
            <div class="card mb-4" id="shipping-section" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>Método de Envío</h5>
                </div>
                <div class="card-body">
                    <div id="shipping-methods-container">
                        <div class="text-center text-muted">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            Calculando métodos de envío...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paso 3: Método de Pago (inicialmente oculto) -->
            <div class="card mb-4" id="payment-section" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Método de Pago</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php if (isset($payment_methods) && !empty($payment_methods)): ?>
                            <?php foreach ($payment_methods as $index => $method): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="payment-method-option" onclick="selectPaymentMethod('<?= esc($method['slug']) ?>')"
                                         style="cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 8px; transition: all 0.3s;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method"
                                                   id="payment-<?= esc($method['slug']) ?>" value="<?= esc($method['slug']) ?>"
                                                   <?= $index === 0 ? 'checked' : '' ?>>
                                            <label class="form-check-label w-100" for="payment-<?= esc($method['slug']) ?>">
                                                <?php if (!empty($method['icon'])): ?>
                                                    <i class="<?= esc($method['icon']) ?> me-2"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-credit-card me-2"></i>
                                                <?php endif; ?>
                                                <strong><?= esc($method['name']) ?></strong>
                                                <?php if (!empty($method['description'])): ?>
                                                    <br><small class="text-muted"><?= esc($method['description']) ?></small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No hay métodos de pago disponibles. Por favor contacta al administrador.
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-primary btn-lg" onclick="processOrder()">
                            <i class="fas fa-check me-2"></i>Confirmar Pedido
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resumen del Pedido -->
        <div class="col-lg-4">
            <div class="order-summary">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Resumen del Pedido</h5>
                    </div>
                    <div class="card-body">
                        <!-- Productos -->
                        <h6>Productos (<?= count($cart_items) ?>)</h6>
                        <?php foreach ($cart_items as $item): ?>
                            <div class="d-flex justify-content-between mb-2">
                                <div>
                                    <small><?= esc($item['name']) ?></small>
                                    <br><small class="text-muted">Cantidad: <?= $item['quantity'] ?></small>
                                </div>
                                <small>Q<?= number_format($item['subtotal'], 2) ?></small>
                            </div>
                        <?php endforeach; ?>
                        
                        <hr>
                        
                        <!-- Totales -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="subtotal">Q<?= number_format($cart_totals['subtotal'], 2) ?></span>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-2" id="shipping-cost-row" style="display: none;">
                            <span>Envío:</span>
                            <span id="shipping-cost">Q0.00</span>
                        </div>
                        
                        <?php if ($cart_totals['tax'] > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Impuestos:</span>
                            <span id="tax">Q<?= number_format($cart_totals['tax'], 2) ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total:</span>
                            <span class="text-primary" id="final-total">Q<?= number_format($cart_totals['total'], 2) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Variables globales
    let selectedAddressId = null;
    let selectedShippingMethod = null;
    let shippingCost = 0;

    // Datos de ubicaciones para el formulario
    const locations = <?= json_encode($locations) ?>;

    // Inicialización
    document.addEventListener('DOMContentLoaded', function() {
        // Si hay una dirección por defecto, seleccionarla automáticamente
        const defaultAddress = document.querySelector('input[name="address"]:checked');
        if (defaultAddress) {
            selectedAddressId = defaultAddress.value;
            calculateShippingForAddress(selectedAddressId);
        }

        // Inicializar el primer método de pago como seleccionado
        const firstPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
        if (firstPaymentMethod) {
            selectPaymentMethod(firstPaymentMethod.value);
        }

        // Si no hay dirección seleccionada, seleccionar la primera disponible
        if (!selectedAddressId) {
            const firstAddress = document.querySelector('input[name="address"]');
            if (firstAddress) {
                selectedAddressId = firstAddress.value;
                firstAddress.checked = true;
                selectAddress(selectedAddressId);
            }
        }
    });

    // Función para seleccionar dirección
    function selectAddress(addressId) {
        selectedAddressId = addressId;

        // Actualizar UI
        document.querySelectorAll('.address-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.querySelector(`[data-address-id="${addressId}"]`).classList.add('selected');

        // Marcar radio button
        document.querySelector(`input[value="${addressId}"]`).checked = true;

        // Calcular métodos de envío
        calculateShippingForAddress(addressId);
    }

    // Función para calcular métodos de envío
    function calculateShippingForAddress(addressId) {
        const container = document.getElementById('shipping-methods-container');
        container.innerHTML = `
            <div class="text-center text-muted">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                Calculando métodos de envío...
            </div>
        `;

        fetch('/checkout/calculate-shipping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `address_id=${addressId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayShippingMethods(data.shipping_methods);
                showStep(2);
            } else {
                showAlert('error', 'Error al calcular envío: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error de conexión al calcular envío');
        });
    }

    // Función para mostrar métodos de envío
    function displayShippingMethods(methods) {
        const container = document.getElementById('shipping-methods-container');
        container.innerHTML = '';

        methods.forEach((method, index) => {
            const methodDiv = document.createElement('div');
            methodDiv.className = 'shipping-method';
            methodDiv.dataset.methodId = method.id;
            methodDiv.dataset.cost = method.cost;

            methodDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="shipping_method"
                           value="${method.id}" data-cost="${method.cost}"
                           ${index === 0 ? 'checked' : ''}>
                    <label class="form-check-label w-100">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${method.name}</strong>
                                <br><small class="text-muted">${method.description}</small>
                                <br><small class="text-info">${method.estimated_days}</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">Q${method.cost.toFixed(2)}</strong>
                            </div>
                        </div>
                    </label>
                </div>
            `;

            // Hacer clickeable
            methodDiv.addEventListener('click', function(e) {
                if (e.target.type !== 'radio') {
                    const radio = methodDiv.querySelector('input[type="radio"]');
                    radio.checked = true;
                    selectShippingMethod(method.id, method.cost);
                }
            });

            // Listener para el radio button
            methodDiv.querySelector('input[type="radio"]').addEventListener('change', function() {
                selectShippingMethod(method.id, method.cost);
            });

            container.appendChild(methodDiv);
        });

        // Seleccionar el primer método automáticamente
        if (methods.length > 0) {
            selectShippingMethod(methods[0].id, methods[0].cost);
        } else {
            // Si no hay métodos de envío, usar valores por defecto
            selectedShippingMethod = 'default';
            shippingCost = 0;
        }
    }

    // Función para seleccionar método de envío
    function selectShippingMethod(methodId, cost) {
        selectedShippingMethod = methodId;
        shippingCost = parseFloat(cost);

        // Actualizar UI
        document.querySelectorAll('.shipping-method').forEach(method => {
            method.classList.remove('selected');
        });
        document.querySelector(`[data-method-id="${methodId}"]`).classList.add('selected');

        // Actualizar totales
        updateOrderSummary();

        // Mostrar paso de pago
        showStep(3);
    }

    // Función para actualizar resumen del pedido
    function updateOrderSummary() {
        const shippingRow = document.getElementById('shipping-cost-row');
        const shippingCostElement = document.getElementById('shipping-cost');
        const finalTotalElement = document.getElementById('final-total');

        if (shippingCost > 0) {
            shippingRow.style.display = 'flex';
            shippingCostElement.textContent = 'Q' + shippingCost.toFixed(2);
        } else {
            shippingRow.style.display = 'none';
        }

        // Calcular total final
        const subtotal = parseFloat(document.getElementById('subtotal').textContent.replace('Q', '').replace(',', ''));
        const tax = parseFloat((document.getElementById('tax')?.textContent || 'Q0').replace('Q', '').replace(',', ''));
        const finalTotal = subtotal + tax + shippingCost;

        finalTotalElement.textContent = 'Q' + finalTotal.toFixed(2);
    }

    // Función para mostrar paso
    function showStep(stepNumber) {
        // Actualizar indicadores de pasos
        for (let i = 1; i <= 3; i++) {
            const step = document.getElementById(`step-${i}`);
            step.classList.remove('active', 'completed');

            if (i < stepNumber) {
                step.classList.add('completed');
            } else if (i === stepNumber) {
                step.classList.add('active');
            }
        }

        // Mostrar/ocultar secciones
        document.getElementById('shipping-section').style.display = stepNumber >= 2 ? 'block' : 'none';
        document.getElementById('payment-section').style.display = stepNumber >= 3 ? 'block' : 'none';
    }

    // Funciones para el formulario de dirección
    function showAddressForm() {
        document.getElementById('address-form').style.display = 'block';
    }

    function hideAddressForm() {
        document.getElementById('address-form').style.display = 'none';
        document.getElementById('new-address-form').reset();
    }

    function loadMunicipalities() {
        const departmentSelect = document.getElementById('department-select');
        const municipalitySelect = document.getElementById('municipality-select');
        const selectedDepartment = departmentSelect.value;

        municipalitySelect.innerHTML = '<option value="">Seleccionar municipio</option>';

        if (selectedDepartment && locations[selectedDepartment]) {
            locations[selectedDepartment].forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    // Manejar envío del formulario de nueva dirección
    document.getElementById('new-address-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('/checkout/save-address', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Dirección guardada correctamente');
                hideAddressForm();
                // Recargar la página para mostrar la nueva dirección
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('error', 'Error al guardar dirección: ' + (data.error || 'Error desconocido'));
                if (data.validation_errors) {
                    console.log('Errores de validación:', data.validation_errors);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error de conexión al guardar dirección');
        });
    });

    // Función para seleccionar método de pago
    function selectPaymentMethod(methodSlug) {
        // Remover selección anterior
        document.querySelectorAll('.payment-method-option').forEach(option => {
            option.style.borderColor = '#e9ecef';
            option.style.backgroundColor = 'transparent';
        });

        // Seleccionar el nuevo método
        const selectedOption = document.querySelector(`#payment-${methodSlug}`).closest('.payment-method-option');
        selectedOption.style.borderColor = '#007bff';
        selectedOption.style.backgroundColor = '#f8f9fa';

        // Marcar el radio button
        document.getElementById(`payment-${methodSlug}`).checked = true;

        console.log('Método de pago seleccionado:', methodSlug);
    }

    // Función para procesar el pedido
    function processOrder() {
        console.log('=== DEBUG PROCESS ORDER ===');
        console.log('selectedAddressId:', selectedAddressId);
        console.log('selectedShippingMethod:', selectedShippingMethod);
        console.log('shippingCost:', shippingCost);

        if (!selectedAddressId) {
            showAlert('error', 'Por favor selecciona una dirección de envío');
            return;
        }

        if (!selectedShippingMethod) {
            showAlert('error', 'Por favor selecciona un método de envío');
            return;
        }

        const paymentMethod = document.querySelector('input[name="payment_method"]:checked')?.value;
        if (!paymentMethod) {
            showAlert('error', 'Por favor selecciona un método de pago');
            return;
        }

        // Mostrar loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Procesando...';
        button.disabled = true;

        const formData = new FormData();
        formData.append('address_id', selectedAddressId);
        formData.append('shipping_method', selectedShippingMethod);
        formData.append('shipping_cost', shippingCost);
        formData.append('payment_method', paymentMethod);

        fetch('/checkout/process-order', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Pedido creado correctamente');
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1500);
            } else {
                showAlert('error', 'Error al procesar pedido: ' + data.error);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error de conexión al procesar pedido');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    // Función para mostrar alertas
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Inicializar cuando se carga la página
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar el primer método de pago como seleccionado
        const firstPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
        if (firstPaymentMethod) {
            selectPaymentMethod(firstPaymentMethod.value);
        }
    });
</script>
<?= $this->endSection() ?>
