# Guía de Instalación - Sistema de Integraciones MrCell

## Requisitos del Sistema

### Requisitos Mínimos
- **PHP**: 8.0 o superior
- **MySQL**: 8.0 o superior
- **Apache/Nginx**: Cualquier versión reciente
- **Memoria RAM**: 512MB mínimo (2GB recomendado)
- **Espacio en disco**: 1GB mínimo

### Extensiones PHP Requeridas
```bash
# Verificar extensiones instaladas
php -m | grep -E "(curl|json|mbstring|openssl|pdo|pdo_mysql|intl|xml|zip)"
```

Extensiones necesarias:
- `curl` - Para comunicación con APIs externas
- `json` - Para manejo de datos JSON
- `mbstring` - Para manejo de strings multibyte
- `openssl` - Para conexiones HTTPS seguras
- `pdo` y `pdo_mysql` - Para conexión a base de datos
- `intl` - Para internacionalización
- `xml` - Para procesamiento de XML (facturas)
- `zip` - Para compresión de archivos

## Instalación Paso a Paso

### 1. Preparación del Entorno

#### Clonar o Descargar el Proyecto
```bash
# Si usas Git
git clone [URL_DEL_REPOSITORIO] mrcell-integraciones
cd mrcell-integraciones

# O descargar y extraer el ZIP
unzip mrcell-integraciones.zip
cd mrcell-integraciones
```

#### Configurar Permisos
```bash
# Dar permisos de escritura a directorios necesarios
chmod -R 755 writable/
chmod -R 755 public/
chown -R www-data:www-data writable/
chown -R www-data:www-data public/
```

### 2. Configuración de Base de Datos

#### Crear Base de Datos
```sql
-- Conectar a MySQL como root
mysql -u root -p

-- Crear base de datos
CREATE DATABASE mrcell_integraciones CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario (opcional pero recomendado)
CREATE USER 'mrcell_user'@'localhost' IDENTIFIED BY 'password_seguro';
GRANT ALL PRIVILEGES ON mrcell_integraciones.* TO 'mrcell_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### Configurar Conexión
```bash
# Copiar archivo de configuración
cp env .env

# Editar configuración de base de datos
nano .env
```

Configurar en `.env`:
```env
#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------

database.default.hostname = localhost
database.default.database = mrcell_integraciones
database.default.username = mrcell_user
database.default.password = password_seguro
database.default.DBDriver = MySQLi
database.default.DBPrefix = 
database.default.port = 3306
```

### 3. Instalación de Dependencias

#### Composer (si aplica)
```bash
# Si el proyecto usa Composer
composer install --no-dev --optimize-autoloader
```

#### Verificar CodeIgniter
```bash
# Verificar que CodeIgniter funciona
php spark --version
```

### 4. Ejecutar Migraciones

#### Ejecutar Todas las Migraciones
```bash
# Ejecutar migraciones en orden
php spark migrate

# Verificar estado de migraciones
php spark migrate:status
```

#### Migraciones Incluidas
1. `CreateCouponsTable` - Tablas de cupones y descuentos
2. `CreateInventoryTables` - Tablas de inventario avanzado
3. `CreateShippingTables` - Tablas de envíos y tracking
4. `CreateKPITables` - Tablas de KPIs y analytics
5. `CreateBillingTables` - Tablas de facturación y social media

### 5. Poblar Datos Iniciales

#### Ejecutar Seeders
```bash
# Ejecutar seeder principal
php spark db:seed InitialDataSeeder
```

#### Datos Incluidos
- Empresas de envío guatemaltecas (Cargo Expreso, GuatEx, Forza)
- Tarifas de envío por zona
- Ubicaciones de almacén predeterminadas
- Cupones de ejemplo
- Descuentos por volumen
- Campañas promocionales estacionales

### 6. Configuración del Servidor Web

#### Apache (.htaccess)
```apache
# Archivo public/.htaccess (ya incluido)
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]

# Configuraciones de seguridad
<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name tu-dominio.com;
    root /path/to/mrcell-integraciones/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Seguridad
    location ~ /\. {
        deny all;
    }
}
```

### 7. Configuración de Integraciones

#### Acceder al Panel de Administración
1. Navegar a `http://tu-dominio.com/admin`
2. Iniciar sesión con credenciales de administrador
3. Ir a cada sección de configuración

#### Configurar Facturación Electrónica
1. Ir a `/admin/billing/config`
2. Ingresar datos:
   - **API URL**: `https://facturalo.mayansource.com/api`
   - **API Key**: Obtener de facturalo.mayansource.com
   - **NIT de la empresa**: NIT registrado en SAT
   - **Razón social**: Nombre legal de la empresa
   - **Dirección**: Dirección fiscal completa
3. Probar conexión
4. Activar generación automática

#### Configurar Social Manager
1. Ir a `/admin/social/config`
2. Ingresar datos:
   - **API URL**: `https://socialmanager.mayansource.com/api`
   - **API Key**: Obtener de socialmanager.mayansource.com
   - **Plataformas**: Seleccionar redes sociales a usar
   - **Hashtags**: Configurar hashtags predeterminados
3. Probar conexión
4. Activar publicación automática

#### Configurar Empresas de Envío
1. Ir a `/admin/shipping/companies`
2. Verificar empresas precargadas
3. Ajustar información de contacto si es necesario
4. Configurar tarifas en `/admin/shipping/rates`

### 8. Configuración de Tareas Programadas (Cron Jobs)

#### Crear Archivo de Cron
```bash
# Editar crontab
crontab -e

# Agregar tareas programadas
# Actualizar seguimiento de envíos cada hora
0 * * * * /usr/bin/php /path/to/mrcell-integraciones/spark tracking:update

# Generar reportes KPI diarios a las 6 AM
0 6 * * * /usr/bin/php /path/to/mrcell-integraciones/spark kpi:generate-daily

# Limpiar logs antiguos semanalmente
0 2 * * 0 /usr/bin/php /path/to/mrcell-integraciones/spark logs:cleanup

# Procesar publicaciones programadas cada 15 minutos
*/15 * * * * /usr/bin/php /path/to/mrcell-integraciones/spark social:process-scheduled
```

### 9. Configuración de Seguridad

#### Variables de Entorno Sensibles
```env
# Configurar en .env
app.encryptionKey = [GENERAR_CLAVE_SEGURA_32_CARACTERES]
app.CSRFProtection = true
app.CSRFTokenName = 'csrf_token_name'
app.CSRFHeaderName = 'X-CSRF-TOKEN'

# Configuración de sesiones
app.sessionDriver = 'CodeIgniter\Session\Handlers\FileHandler'
app.sessionCookieName = 'mrcell_session'
app.sessionExpiration = 7200
app.sessionSavePath = null
app.sessionMatchIP = false
app.sessionTimeToUpdate = 300
app.sessionRegenerateDestroy = false
```

#### Configurar HTTPS (Recomendado)
```bash
# Instalar Certbot para Let's Encrypt
sudo apt install certbot python3-certbot-apache

# Obtener certificado SSL
sudo certbot --apache -d tu-dominio.com

# Renovación automática
sudo crontab -e
# Agregar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 10. Verificación de Instalación

#### Checklist de Verificación
- [ ] Página principal carga correctamente
- [ ] Panel de administración accesible
- [ ] Base de datos poblada con datos iniciales
- [ ] Migraciones ejecutadas sin errores
- [ ] Permisos de archivos configurados
- [ ] Configuración de integraciones completada
- [ ] Tareas programadas configuradas
- [ ] SSL configurado (producción)

#### Comandos de Verificación
```bash
# Verificar estado de migraciones
php spark migrate:status

# Verificar configuración
php spark env

# Verificar rutas
php spark routes

# Probar conexión a base de datos
php spark db:table users
```

### 11. Configuración de Producción

#### Optimizaciones
```env
# En .env para producción
CI_ENVIRONMENT = production
app.baseURL = 'https://tu-dominio.com'
app.forceGlobalSecureRequests = true

# Desactivar debugging
app.debug = false
```

#### Configurar Logs
```bash
# Crear directorio de logs personalizado
mkdir -p writable/logs/integrations

# Configurar rotación de logs
sudo nano /etc/logrotate.d/mrcell-integraciones
```

Contenido de logrotate:
```
/path/to/mrcell-integraciones/writable/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 12. Backup y Recuperación

#### Script de Backup
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mrcell-integraciones"
DB_NAME="mrcell_integraciones"
DB_USER="mrcell_user"
DB_PASS="password_seguro"

# Crear directorio de backup
mkdir -p $BACKUP_DIR

# Backup de base de datos
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# Backup de archivos
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /path/to/mrcell-integraciones/writable/uploads

# Limpiar backups antiguos (mantener 30 días)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

#### Programar Backup
```bash
# Agregar a crontab
0 2 * * * /path/to/backup.sh
```

## Solución de Problemas Comunes

### Error: "Class not found"
```bash
# Limpiar cache de autoloader
composer dump-autoload
php spark cache:clear
```

### Error de Permisos
```bash
# Verificar y corregir permisos
sudo chown -R www-data:www-data writable/
sudo chmod -R 755 writable/
```

### Error de Conexión a Base de Datos
1. Verificar credenciales en `.env`
2. Verificar que MySQL esté ejecutándose
3. Verificar que el usuario tenga permisos

### APIs Externas No Responden
1. Verificar conectividad a internet
2. Verificar API Keys
3. Revisar logs en `writable/logs/`

## Contacto de Soporte

Para problemas de instalación:
- **Email**: <EMAIL>
- **Documentación**: Ver README_INTEGRACIONES.md
- **APIs Externas**: <EMAIL>
