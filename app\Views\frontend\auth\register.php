<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? '<PERSON><PERSON><PERSON> - Mr<PERSON>ell <PERSON>' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* <PERSON><PERSON>a <PERSON> - <PERSON>, Negro, Blanco, Gris */
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --success-color: #059669;        /* Verde para éxito */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris medio claro */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Negro gris */
            --gray-900: #111827;             /* Negro */
        }
        
        .auth-container {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--gray-800) 50%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .auth-card {
            background: var(--white-color);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            position: relative;
            z-index: 1;
            border: 1px solid var(--gray-200);
        }

        .auth-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white-color);
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .auth-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        }
        
        .auth-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            position: relative;
            z-index: 1;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .auth-body {
            padding: 40px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating > .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 10px;
            padding: 12px 15px;
            height: auto;
            transition: all 0.3s ease;
            background-color: var(--white-color);
        }

        .form-floating > .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
            background-color: var(--white-color);
        }

        .form-floating > label {
            padding: 12px 15px;
            color: var(--gray-500);
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color);
        }
        
        .btn-register {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            color: var(--white-color);
        }

        .btn-register:hover::before {
            left: 100%;
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 20px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .social-login {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn-social {
            flex: 1;
            padding: 12px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            background: white;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-social:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .auth-footer {
            text-align: center;
            padding: 20px 40px;
            background: var(--light-color);
            border-top: 1px solid var(--gray-200);
            color: var(--gray-600);
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--primary-dark);
        }

        /* Estilos para alertas */
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
            padding: 15px 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--primary-dark));
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color), #0ea5e9);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
        }
        
        .auth-footer a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #20c997);
            color: white;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), #e74c3c);
            color: white;
        }
        
        .password-strength {
            margin-top: 10px;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #ffc107; width: 50%; }
        .strength-good { background: #fd7e14; width: 75%; }
        .strength-strong { background: #28a745; width: 100%; }
        
        .form-check {
            margin: 20px 0;
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .form-check-label {
            font-size: 14px;
            color: #6c757d;
        }
        
        .form-check-label a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .form-check-label a:hover {
            text-decoration: underline;
        }

        /* Estilos para el selector de país */
        #country_code {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #country_code option {
            padding: 8px;
        }

        .row .col-md-4 {
            padding-right: 8px;
        }

        .row .col-md-8 {
            padding-left: 8px;
        }
        
        @media (max-width: 576px) {
            .auth-container {
                padding: 10px;
            }
            
            .auth-body {
                padding: 30px 20px;
            }
            
            .auth-footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="/logo.jpg" alt="MrCell Logo" style="width: 40px; height: 40px; object-fit: contain;">
                </div>
                <h2 class="mb-0">Crear Cuenta</h2>
                <p class="mb-0 opacity-75">Únete a MrCell Guatemala</p>
            </div>
            
            <div class="auth-body">
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Errores de validación:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="/register" id="registerForm">
                    <?= csrf_field() ?>
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="name" name="name" 
                               placeholder="Nombre completo" required 
                               value="<?= old('name') ?>">
                        <label for="name">
                            <i class="fas fa-user me-2"></i>Nombre Completo
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>" required 
                               value="<?= old('email') ?>">
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Correo Electrónico
                        </label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="country_code" name="country_code" required>
                                    <?php foreach ($countries as $code => $country): ?>
                                        <option value="<?= $country['code'] ?>"
                                                data-country="<?= $code ?>"
                                                data-format="<?= $country['format'] ?>"
                                                <?= $code === $defaultCountry ? 'selected' : '' ?>>
                                            <?= $country['flag'] ?> +<?= $country['code'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label for="country_code">
                                    <i class="fas fa-globe me-2"></i>País
                                </label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-floating">
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="12345678" required
                                       value="<?= old('phone') ?>">
                                <label for="phone">
                                    <i class="fas fa-phone me-2"></i>Teléfono *
                                </label>
                                <div class="form-text" id="phone-format-hint">
                                    Ingresa tu número de teléfono
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Contraseña" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Contraseña
                        </label>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <small class="text-muted" id="strengthText">Ingresa una contraseña</small>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                               placeholder="Confirmar contraseña" required>
                        <label for="password_confirm">
                            <i class="fas fa-lock me-2"></i>Confirmar Contraseña
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">
                            Acepto los <a href="/terms" target="_blank">Términos y Condiciones</a> 
                            y la <a href="/privacy" target="_blank">Política de Privacidad</a>
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="whatsapp_notifications" name="whatsapp_notifications" checked>
                        <label class="form-check-label" for="whatsapp_notifications">
                            <i class="fab fa-whatsapp me-1 text-success"></i>
                            Recibir notificaciones por WhatsApp (ofertas y productos nuevos)
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="fas fa-user-plus me-2"></i>Crear Cuenta
                    </button>
                </form>
            </div>
            
            <div class="auth-footer">
                <p class="mb-0">
                    ¿Ya tienes cuenta? 
                    <a href="/login">Iniciar Sesión</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Validación de fortaleza de contraseña
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            let strength = 0;
            let text = '';
            let className = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    text = 'Muy débil';
                    className = 'strength-weak';
                    break;
                case 2:
                    text = 'Débil';
                    className = 'strength-weak';
                    break;
                case 3:
                    text = 'Regular';
                    className = 'strength-fair';
                    break;
                case 4:
                    text = 'Buena';
                    className = 'strength-good';
                    break;
                case 5:
                    text = 'Muy fuerte';
                    className = 'strength-strong';
                    break;
            }
            
            strengthFill.className = 'strength-fill ' + className;
            strengthText.textContent = text;
        });
        
        // Validación de confirmación de contraseña
        document.getElementById('password_confirm').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirm = this.value;
            
            if (confirm && password !== confirm) {
                this.setCustomValidity('Las contraseñas no coinciden');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Datos de países para formateo
        const countries = <?= json_encode($countries) ?>;

        // Función para actualizar el formato del teléfono
        function updatePhoneFormat() {
            const countrySelect = document.getElementById('country_code');
            const phoneInput = document.getElementById('phone');
            const formatHint = document.getElementById('phone-format-hint');

            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const countryCode = selectedOption.dataset.country;
            const format = selectedOption.dataset.format;
            const countryInfo = countries[countryCode];

            // Actualizar placeholder y hint
            phoneInput.placeholder = format.replace(/#/g, '0');
            formatHint.textContent = `Formato: ${format} (${countryInfo.name})`;

            // Limpiar validación personalizada
            phoneInput.setCustomValidity('');
        }

        // Validación de teléfono dinámico según país
        document.getElementById('phone').addEventListener('input', function() {
            const countrySelect = document.getElementById('country_code');
            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const format = selectedOption.dataset.format;

            let value = this.value.replace(/\D/g, ''); // Solo números

            // Contar cuántos dígitos espera el formato
            const expectedDigits = (format.match(/#/g) || []).length;

            // Limitar según el formato del país
            if (value.length > expectedDigits) {
                value = value.substring(0, expectedDigits);
            }

            this.value = value;

            // Validar según el país seleccionado
            if (value.length > 0 && value.length !== expectedDigits) {
                this.setCustomValidity(`El teléfono debe tener exactamente ${expectedDigits} dígitos`);
            } else {
                this.setCustomValidity('');
            }
        });

        // Actualizar formato cuando cambie el país
        document.getElementById('country_code').addEventListener('change', updatePhoneFormat);

        // Inicializar formato al cargar
        updatePhoneFormat();

        // Detectar país automáticamente al cargar la página
        detectCountryAutomatically();

        // Función para detectar país automáticamente
        function detectCountryAutomatically() {
            // Mostrar indicador de carga
            const countrySelect = document.getElementById('country_code');
            const originalHTML = countrySelect.innerHTML;

            // Agregar opción de "Detectando..."
            const detectingOption = document.createElement('option');
            detectingOption.value = '';
            detectingOption.textContent = '🌍 Detectando tu país...';
            detectingOption.selected = true;
            countrySelect.insertBefore(detectingOption, countrySelect.firstChild);

            // Hacer petición AJAX para detectar país
            fetch('/api/detect-country')
                .then(response => response.json())
                .then(data => {
                    // Remover opción de "Detectando..."
                    countrySelect.removeChild(detectingOption);

                    // Seleccionar el país detectado
                    const countryCode = data.country_code;
                    const countryInfo = data.country_info;

                    // Buscar la opción correspondiente por código de país ISO
                    for (let option of countrySelect.options) {
                        if (option.dataset.country === countryCode) {
                            option.selected = true;
                            break;
                        }
                    }

                    // Actualizar el formato del teléfono
                    updatePhoneFormat();

                    // Mostrar mensaje de detección exitosa (opcional)
                    console.log(`🌍 País detectado automáticamente: ${countryInfo.flag} ${countryInfo.name}`);
                })
                .catch(error => {
                    console.error('Error detectando país:', error);
                    // Remover opción de "Detectando..." en caso de error
                    if (countrySelect.contains(detectingOption)) {
                        countrySelect.removeChild(detectingOption);
                    }
                    // Mantener Guatemala por defecto
                });
        }
        
        // Validación del formulario
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirm = document.getElementById('password_confirm').value;
            const terms = document.getElementById('terms').checked;
            const phone = document.getElementById('phone').value;
            const countrySelect = document.getElementById('country_code');
            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const format = selectedOption.dataset.format;
            const expectedDigits = (format.match(/#/g) || []).length;

            if (password !== confirm) {
                e.preventDefault();
                alert('Las contraseñas no coinciden');
                return;
            }

            if (!terms) {
                e.preventDefault();
                alert('Debes aceptar los términos y condiciones');
                return;
            }

            if (password.length < 6) {
                e.preventDefault();
                alert('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            if (phone.replace(/\D/g, '').length !== expectedDigits) {
                e.preventDefault();
                alert(`El teléfono debe tener exactamente ${expectedDigits} dígitos`);
                return;
            }

            // Esta validación ya se hace arriba con el formato dinámico
            // if (!phone || phone.length !== 8) {
            //     e.preventDefault();
            //     alert('El teléfono debe tener exactamente 8 dígitos');
            //     return;
            // }
        });
    </script>
</body>
</html>
