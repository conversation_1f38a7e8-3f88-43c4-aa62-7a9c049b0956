/**
 * 🚀 MrCell E-commerce Modern - Next Generation System
 * 
 * Ultra-modern, reactive e-commerce system built with:
 * - ES6+ JavaScript (no jQuery dependency)
 * - Modern Web APIs (Fetch, Intersection Observer, etc.)
 * - Reactive state management
 * - Performance optimizations
 * - Accessibility features
 * - PWA capabilities
 * 
 * @version 3.0.0
 * <AUTHOR> Development Team
 */

class MrCellEcommerce {
    constructor() {
        // 🎯 Core Configuration
        this.config = {
            ...window.MrCellConfig,
            timeout: 10000,
            retryAttempts: 3,
            retryDelay: 1000,
            cacheTimeout: 300000 // 5 minutes
        };

        // 🔄 Reactive State Management
        this.state = this.createReactiveState({
            cart: {
                items: [],
                count: this.config.cart?.count || 0,
                total: this.config.cart?.total || 0,
                subtotal: 0,
                shipping: 0,
                tax: 0,
                loading: false,
                error: null
            },
            user: {
                loggedIn: !!this.config.user,
                data: this.config.user || null
            },
            ui: {
                cartOffcanvasOpen: false,
                mobileMenuOpen: false,
                notifications: [],
                loading: false,
                darkMode: this.getDarkModePreference()
            },
            search: {
                query: '',
                suggestions: [],
                loading: false
            },
            wishlist: {
                items: [],
                count: 0
            }
        });

        // 🎨 Animation Configuration
        this.animations = {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            stagger: 50
        };

        // 📱 Device Detection
        this.device = {
            isMobile: window.innerWidth <= 768,
            isTablet: window.innerWidth > 768 && window.innerWidth <= 1024,
            isDesktop: window.innerWidth > 1024,
            hasTouch: 'ontouchstart' in window,
            supportsWebP: this.checkWebPSupport()
        };

        // 🚀 Initialize System
        this.init();
    }

    // 🔄 Create Reactive State System
    createReactiveState(initialState) {
        const listeners = new Map();
        
        const createProxy = (obj, path = '') => {
            return new Proxy(obj, {
                set(target, property, value) {
                    const oldValue = target[property];
                    const fullPath = path ? `${path}.${property}` : property;
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        target[property] = createProxy(value, fullPath);
                    } else {
                        target[property] = value;
                    }

                    // Notify listeners
                    this.notifyListeners(fullPath, value, oldValue);
                    this.notifyListeners(property, value, oldValue);
                    
                    return true;
                },
                
                get(target, property) {
                    if (property === 'subscribe') {
                        return (prop, callback) => {
                            if (!listeners.has(prop)) {
                                listeners.set(prop, new Set());
                            }
                            listeners.get(prop).add(callback);
                            
                            // Return unsubscribe function
                            return () => {
                                if (listeners.has(prop)) {
                                    listeners.get(prop).delete(callback);
                                }
                            };
                        };
                    }
                    
                    const value = target[property];
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return createProxy(value, path ? `${path}.${property}` : property);
                    }
                    
                    return value;
                },

                notifyListeners(prop, newValue, oldValue) {
                    if (listeners.has(prop)) {
                        listeners.get(prop).forEach(callback => {
                            try {
                                callback(newValue, oldValue, prop);
                            } catch (error) {
                                console.error('State listener error:', error);
                            }
                        });
                    }
                }
            });
        };

        return createProxy(initialState);
    }

    // 🚀 Initialize Application
    async init() {
        try {
            console.log('🚀 Initializing MrCell E-commerce Modern...');
            
            // Initialize core systems
            await this.initializeCore();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize UI components
            this.initializeUI();
            
            // Load initial data
            await this.loadInitialData();
            
            // Setup state subscriptions
            this.setupStateSubscriptions();
            
            // Initialize performance monitoring
            this.initializePerformanceMonitoring();
            
            console.log('✅ MrCell E-commerce Modern initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize MrCell E-commerce:', error);
            this.showNotification('Error al inicializar la aplicación', 'error');
        }
    }

    // 🔧 Initialize Core Systems
    async initializeCore() {
        // Initialize cache
        this.cache = new Map();
        
        // Initialize notification system
        this.notifications = new Notyf({
            duration: 4000,
            position: { x: 'right', y: 'top' },
            types: [
                {
                    type: 'success',
                    background: '#28a745',
                    icon: { className: 'fas fa-check', tagName: 'i' }
                },
                {
                    type: 'error',
                    background: '#dc3545',
                    icon: { className: 'fas fa-times', tagName: 'i' }
                },
                {
                    type: 'warning',
                    background: '#ffc107',
                    icon: { className: 'fas fa-exclamation-triangle', tagName: 'i' }
                },
                {
                    type: 'info',
                    background: '#17a2b8',
                    icon: { className: 'fas fa-info-circle', tagName: 'i' }
                }
            ]
        });

        // Initialize lazy loading
        if (this.config.features.lazyLoading) {
            this.lazyLoad = new LazyLoad({
                elements_selector: '.lazy',
                threshold: 0,
                callback_loaded: (el) => {
                    el.classList.add('loaded');
                }
            });
        }

        // Apply dark mode if enabled
        if (this.state.ui.darkMode) {
            this.applyDarkMode(true);
        }
    }

    // 🎧 Setup Event Listeners
    setupEventListeners() {
        // Window events
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));

        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
            searchInput.addEventListener('focus', this.showSearchSuggestions.bind(this));
            searchInput.addEventListener('blur', this.hideSearchSuggestions.bind(this));
        }

        // Cart events
        document.addEventListener('click', this.handleCartActions.bind(this));
        
        // Dark mode toggle
        const darkModeToggle = document.getElementById('mobile-dark-mode-toggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('change', this.toggleDarkMode.bind(this));
        }

        // Back to top button
        const backToTopBtn = document.getElementById('back-to-top');
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', this.scrollToTop.bind(this));
        }

        // Form submissions
        document.addEventListener('submit', this.handleFormSubmissions.bind(this));
    }

    // 🎨 Initialize UI Components
    initializeUI() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

        // Initialize popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));

        // Update cart count display
        this.updateCartDisplay();
        
        // Update wishlist count display
        this.updateWishlistDisplay();
    }

    // 📊 Load Initial Data
    async loadInitialData() {
        try {
            // Load cart data if user is logged in
            if (this.state.user.loggedIn) {
                await this.loadCartData();
                await this.loadWishlistData();
            }
            
            // Load any cached data
            this.loadCachedData();
            
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    // 🔄 Setup State Subscriptions
    setupStateSubscriptions() {
        // Cart state changes
        this.state.subscribe('cart.count', (newCount) => {
            this.updateCartCountDisplay(newCount);
        });

        this.state.subscribe('cart.total', (newTotal) => {
            this.updateCartTotalDisplay(newTotal);
        });

        // Wishlist state changes
        this.state.subscribe('wishlist.count', (newCount) => {
            this.updateWishlistCountDisplay(newCount);
        });

        // UI state changes
        this.state.subscribe('ui.darkMode', (isDark) => {
            this.applyDarkMode(isDark);
        });
    }

    // 📈 Initialize Performance Monitoring
    initializePerformanceMonitoring() {
        if ('performance' in window) {
            // Monitor page load time
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`⚡ Page loaded in ${loadTime.toFixed(2)}ms`);
            });

            // Monitor API response times
            this.apiResponseTimes = [];
        }
    }

    // 🛒 Cart Management Methods
    async addToCart(productId, quantity = 1, options = {}) {
        try {
            this.state.cart.loading = true;
            
            const response = await this.apiRequest('POST', `${this.config.carritoUrl}/add`, {
                product_id: productId,
                quantity: quantity,
                options: options
            });

            if (response.success) {
                this.state.cart.items = response.data.items;
                this.state.cart.count = response.data.count;
                this.state.cart.total = response.data.total;
                
                this.showNotification('Producto agregado al carrito', 'success');
                this.animateCartIcon();
                
                return response.data;
            } else {
                throw new Error(response.message || 'Error al agregar producto');
            }
            
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showNotification(error.message || 'Error al agregar al carrito', 'error');
            throw error;
        } finally {
            this.state.cart.loading = false;
        }
    }

    // Continue with more methods...
    // [The file would continue with more methods for cart management, API requests, UI updates, etc.]
}
