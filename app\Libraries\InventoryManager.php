<?php

namespace App\Libraries;

/**
 * Gestor de Inventario Inteligente
 * Sistema avanzado de gestión de inventario con alertas automáticas y predicción de demanda
 */
class InventoryManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('INVENTORY_ENABLED', true),
            'auto_reorder' => env('INVENTORY_AUTO_REORDER', true),
            'low_stock_threshold' => env('INVENTORY_LOW_STOCK_THRESHOLD', 10),
            'critical_stock_threshold' => env('INVENTORY_CRITICAL_STOCK_THRESHOLD', 5),
            'reorder_point_multiplier' => env('INVENTORY_REORDER_MULTIPLIER', 1.5),
            'lead_time_days' => env('INVENTORY_LEAD_TIME_DAYS', 7),
            'safety_stock_days' => env('INVENTORY_SAFETY_STOCK_DAYS', 3),
            'demand_prediction_enabled' => env('DEMAND_PREDICTION_ENABLED', true),
            'seasonal_adjustment' => env('SEASONAL_ADJUSTMENT_ENABLED', true),
            'multi_location_enabled' => env('MULTI_LOCATION_ENABLED', true),
            'barcode_tracking' => env('BARCODE_TRACKING_ENABLED', true),
            'batch_tracking' => env('BATCH_TRACKING_ENABLED', true),
            'expiry_tracking' => env('EXPIRY_TRACKING_ENABLED', true),
            'cost_method' => env('INVENTORY_COST_METHOD', 'FIFO'), // FIFO, LIFO, AVERAGE
            'alert_email' => env('INVENTORY_ALERT_EMAIL', '<EMAIL>'),
            'cache_ttl' => env('INVENTORY_CACHE_TTL', 300)
        ];
        
        $this->createInventoryTables();
    }
    
    /**
     * Actualizar stock de producto
     */
    public function updateStock(int $productId, int $quantity, string $type = 'adjustment', array $metadata = []): array
    {
        try {
            if (!$this->config['enabled']) {
                return ['success' => false, 'error' => 'Inventory management disabled'];
            }
            
            // Obtener producto actual
            $product = $this->getProduct($productId);
            if (!$product) {
                throw new \Exception("Product not found: $productId");
            }
            
            $oldStock = $product['stock'];
            $newStock = $oldStock;
            
            // Calcular nuevo stock según el tipo de movimiento
            switch ($type) {
                case 'sale':
                    $newStock = $oldStock - abs($quantity);
                    break;
                case 'purchase':
                case 'restock':
                    $newStock = $oldStock + abs($quantity);
                    break;
                case 'adjustment':
                    $newStock = $quantity;
                    break;
                case 'return':
                    $newStock = $oldStock + abs($quantity);
                    break;
                case 'damage':
                case 'loss':
                    $newStock = $oldStock - abs($quantity);
                    break;
                default:
                    throw new \Exception("Invalid stock movement type: $type");
            }
            
            // Validar que no sea negativo
            if ($newStock < 0) {
                throw new \Exception("Insufficient stock. Available: $oldStock, Requested: " . abs($quantity));
            }
            
            // Actualizar stock en base de datos
            $this->db->transStart();
            
            $this->db->table('products')
                    ->where('id', $productId)
                    ->update([
                        'stock' => $newStock,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            
            // Registrar movimiento de inventario
            $movementId = $this->recordStockMovement([
                'product_id' => $productId,
                'type' => $type,
                'quantity' => $type === 'adjustment' ? ($newStock - $oldStock) : ($type === 'sale' || $type === 'damage' || $type === 'loss' ? -abs($quantity) : abs($quantity)),
                'old_stock' => $oldStock,
                'new_stock' => $newStock,
                'reference' => $metadata['reference'] ?? null,
                'notes' => $metadata['notes'] ?? '',
                'location_id' => $metadata['location_id'] ?? 1,
                'batch_number' => $metadata['batch_number'] ?? null,
                'expiry_date' => $metadata['expiry_date'] ?? null,
                'cost_per_unit' => $metadata['cost_per_unit'] ?? null,
                'user_id' => $metadata['user_id'] ?? 0
            ]);
            
            $this->db->transComplete();
            
            if ($this->db->transStatus() === false) {
                throw new \Exception("Database transaction failed");
            }
            
            // Limpiar cache
            $this->cache->delete("product_stock:$productId");
            $this->cache->delete('low_stock_products');
            
            // Verificar alertas de stock
            $this->checkStockAlerts($productId, $newStock);
            
            // Verificar punto de reorden
            if ($this->config['auto_reorder']) {
                $this->checkReorderPoint($productId, $newStock);
            }
            
            $this->logger->info("Stock updated", [
                'product_id' => $productId,
                'type' => $type,
                'old_stock' => $oldStock,
                'new_stock' => $newStock,
                'movement_id' => $movementId
            ]);
            
            return [
                'success' => true,
                'movement_id' => $movementId,
                'old_stock' => $oldStock,
                'new_stock' => $newStock,
                'alerts' => $this->getProductAlerts($productId)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Stock update error: " . $e->getMessage(), [
                'product_id' => $productId,
                'quantity' => $quantity,
                'type' => $type
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener productos con stock bajo
     */
    public function getLowStockProducts(): array
    {
        try {
            $cacheKey = 'low_stock_products';
            $products = $this->cache->get($cacheKey);
            
            if ($products === null) {
                $products = $this->db->table('products p')
                                   ->select('p.*, c.name as category_name')
                                   ->join('categories c', 'c.id = p.category_id', 'left')
                                   ->where('p.is_active', 1)
                                   ->where('p.stock <=', $this->config['low_stock_threshold'])
                                   ->orderBy('p.stock', 'ASC')
                                   ->get()
                                   ->getResultArray();
                
                // Agregar información adicional
                foreach ($products as &$product) {
                    $product['alert_level'] = $this->getStockAlertLevel($product['stock']);
                    $product['reorder_point'] = $this->calculateReorderPoint($product['id']);
                    $product['suggested_order_quantity'] = $this->calculateSuggestedOrderQuantity($product['id']);
                    $product['days_of_stock'] = $this->calculateDaysOfStock($product['id'], $product['stock']);
                }
                
                $this->cache->set($cacheKey, $products, $this->config['cache_ttl']);
            }
            
            return $products;
            
        } catch (\Exception $e) {
            $this->logger->error("Low stock products error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Predecir demanda futura
     */
    public function predictDemand(int $productId, int $days = 30): array
    {
        if (!$this->config['demand_prediction_enabled']) {
            return ['success' => false, 'error' => 'Demand prediction disabled'];
        }
        
        try {
            // Obtener datos históricos de ventas
            $historicalData = $this->getHistoricalSalesData($productId, 90); // 90 días de historia
            
            if (empty($historicalData)) {
                return [
                    'success' => true,
                    'predicted_demand' => 0,
                    'confidence' => 0,
                    'method' => 'no_data'
                ];
            }
            
            // Calcular demanda promedio diaria
            $totalSales = array_sum(array_column($historicalData, 'quantity'));
            $totalDays = count($historicalData);
            $averageDailyDemand = $totalSales / $totalDays;
            
            // Aplicar ajustes estacionales si está habilitado
            if ($this->config['seasonal_adjustment']) {
                $seasonalFactor = $this->calculateSeasonalFactor($productId);
                $averageDailyDemand *= $seasonalFactor;
            }
            
            // Calcular tendencia
            $trend = $this->calculateTrend($historicalData);
            
            // Predicción para los próximos días
            $predictedDemand = ($averageDailyDemand + $trend) * $days;
            
            // Calcular confianza basada en la variabilidad de los datos
            $confidence = $this->calculatePredictionConfidence($historicalData);
            
            return [
                'success' => true,
                'predicted_demand' => round($predictedDemand),
                'daily_average' => round($averageDailyDemand, 2),
                'trend' => round($trend, 2),
                'confidence' => round($confidence, 2),
                'seasonal_factor' => $seasonalFactor ?? 1,
                'method' => 'linear_trend',
                'period_days' => $days
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Demand prediction error: " . $e->getMessage(), [
                'product_id' => $productId,
                'days' => $days
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generar orden de reabastecimiento automática
     */
    public function generateReorderSuggestions(): array
    {
        try {
            $suggestions = [];
            $lowStockProducts = $this->getLowStockProducts();
            
            foreach ($lowStockProducts as $product) {
                if ($product['stock'] <= $product['reorder_point']) {
                    $demandPrediction = $this->predictDemand($product['id'], $this->config['lead_time_days'] + $this->config['safety_stock_days']);
                    
                    $suggestion = [
                        'product_id' => $product['id'],
                        'product_name' => $product['name'],
                        'current_stock' => $product['stock'],
                        'reorder_point' => $product['reorder_point'],
                        'suggested_quantity' => $product['suggested_order_quantity'],
                        'predicted_demand' => $demandPrediction['predicted_demand'] ?? 0,
                        'urgency' => $this->calculateUrgency($product),
                        'estimated_cost' => $this->estimateReorderCost($product['id'], $product['suggested_order_quantity']),
                        'supplier_info' => $this->getPreferredSupplier($product['id'])
                    ];
                    
                    $suggestions[] = $suggestion;
                }
            }
            
            // Ordenar por urgencia
            usort($suggestions, function($a, $b) {
                return $b['urgency'] <=> $a['urgency'];
            });
            
            return [
                'success' => true,
                'suggestions' => $suggestions,
                'total_suggestions' => count($suggestions),
                'total_estimated_cost' => array_sum(array_column($suggestions, 'estimated_cost'))
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener movimientos de inventario
     */
    public function getInventoryMovements(array $filters = []): array
    {
        try {
            $builder = $this->db->table('inventory_movements im')
                               ->select('im.*, p.name as product_name, p.sku, u.name as user_name, l.name as location_name')
                               ->join('products p', 'p.id = im.product_id', 'left')
                               ->join('users u', 'u.id = im.user_id', 'left')
                               ->join('inventory_locations l', 'l.id = im.location_id', 'left');
            
            // Aplicar filtros
            if (!empty($filters['product_id'])) {
                $builder->where('im.product_id', $filters['product_id']);
            }
            
            if (!empty($filters['type'])) {
                $builder->where('im.type', $filters['type']);
            }
            
            if (!empty($filters['location_id'])) {
                $builder->where('im.location_id', $filters['location_id']);
            }
            
            if (!empty($filters['date_from'])) {
                $builder->where('im.created_at >=', $filters['date_from']);
            }
            
            if (!empty($filters['date_to'])) {
                $builder->where('im.created_at <=', $filters['date_to']);
            }
            
            $movements = $builder->orderBy('im.created_at', 'DESC')
                               ->limit($filters['limit'] ?? 100)
                               ->get()
                               ->getResultArray();
            
            return [
                'success' => true,
                'movements' => $movements,
                'total' => count($movements)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de inventario
     */
    public function getInventoryStats(int $days = 30): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Valor total del inventario
            $totalValue = $this->db->query("
                SELECT SUM(p.stock * p.cost_price) as total_value
                FROM products p
                WHERE p.is_active = 1 AND p.stock > 0
            ")->getRowArray()['total_value'] ?? 0;
            
            // Productos con stock bajo
            $lowStockCount = $this->db->table('products')
                                     ->where('is_active', 1)
                                     ->where('stock <=', $this->config['low_stock_threshold'])
                                     ->countAllResults();
            
            // Productos sin stock
            $outOfStockCount = $this->db->table('products')
                                       ->where('is_active', 1)
                                       ->where('stock', 0)
                                       ->countAllResults();
            
            // Movimientos por tipo
            $movementsByType = $this->db->query("
                SELECT type, COUNT(*) as count, SUM(ABS(quantity)) as total_quantity
                FROM inventory_movements
                WHERE created_at >= ?
                GROUP BY type
                ORDER BY count DESC
            ", [$dateFrom])->getResultArray();
            
            // Top productos por movimiento
            $topMovedProducts = $this->db->query("
                SELECT p.name, p.sku, SUM(ABS(im.quantity)) as total_moved
                FROM inventory_movements im
                JOIN products p ON p.id = im.product_id
                WHERE im.created_at >= ?
                GROUP BY im.product_id, p.name, p.sku
                ORDER BY total_moved DESC
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Rotación de inventario
            $inventoryTurnover = $this->calculateInventoryTurnover($days);
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => [
                    'total_inventory_value' => $totalValue,
                    'low_stock_products' => $lowStockCount,
                    'out_of_stock_products' => $outOfStockCount,
                    'movements_by_type' => $movementsByType,
                    'top_moved_products' => $topMovedProducts,
                    'inventory_turnover' => $inventoryTurnover,
                    'total_products' => $this->getTotalActiveProducts(),
                    'average_stock_level' => $this->getAverageStockLevel()
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getProduct(int $productId): ?array
    {
        return $this->db->table('products')
                       ->where('id', $productId)
                       ->get()
                       ->getRowArray();
    }
    
    private function recordStockMovement(array $data): int
    {
        return $this->db->table('inventory_movements')->insert(array_merge($data, [
            'created_at' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function checkStockAlerts(int $productId, int $currentStock): void
    {
        $alertLevel = $this->getStockAlertLevel($currentStock);
        
        if ($alertLevel !== 'normal') {
            $this->createStockAlert($productId, $alertLevel, $currentStock);
        }
    }
    
    private function getStockAlertLevel(int $stock): string
    {
        if ($stock <= $this->config['critical_stock_threshold']) {
            return 'critical';
        } elseif ($stock <= $this->config['low_stock_threshold']) {
            return 'low';
        }
        
        return 'normal';
    }
    
    private function createStockAlert(int $productId, string $level, int $currentStock): void
    {
        // Verificar si ya existe una alerta activa
        $existingAlert = $this->db->table('inventory_alerts')
                                 ->where('product_id', $productId)
                                 ->where('alert_type', 'stock_' . $level)
                                 ->where('is_resolved', 0)
                                 ->countAllResults();
        
        if ($existingAlert === 0) {
            $this->db->table('inventory_alerts')->insert([
                'product_id' => $productId,
                'alert_type' => 'stock_' . $level,
                'message' => "Stock $level: $currentStock units remaining",
                'current_stock' => $currentStock,
                'threshold' => $level === 'critical' ? $this->config['critical_stock_threshold'] : $this->config['low_stock_threshold'],
                'is_resolved' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Enviar notificación por email si está configurado
            if ($this->config['alert_email']) {
                $this->sendStockAlert($productId, $level, $currentStock);
            }
        }
    }
    
    private function sendStockAlert(int $productId, string $level, int $currentStock): void
    {
        // Implementar envío de email de alerta
        $this->logger->info("Stock alert sent", [
            'product_id' => $productId,
            'level' => $level,
            'current_stock' => $currentStock
        ]);
    }
    
    private function checkReorderPoint(int $productId, int $currentStock): void
    {
        $reorderPoint = $this->calculateReorderPoint($productId);
        
        if ($currentStock <= $reorderPoint) {
            $this->createReorderSuggestion($productId, $currentStock);
        }
    }
    
    private function calculateReorderPoint(int $productId): int
    {
        $demandPrediction = $this->predictDemand($productId, $this->config['lead_time_days']);
        $safetyStock = $this->calculateSafetyStock($productId);
        
        return ($demandPrediction['predicted_demand'] ?? 0) + $safetyStock;
    }
    
    private function calculateSafetyStock(int $productId): int
    {
        $demandPrediction = $this->predictDemand($productId, $this->config['safety_stock_days']);
        return $demandPrediction['predicted_demand'] ?? 0;
    }
    
    private function calculateSuggestedOrderQuantity(int $productId): int
    {
        $demandPrediction = $this->predictDemand($productId, 30); // 30 días
        return round(($demandPrediction['predicted_demand'] ?? 0) * $this->config['reorder_point_multiplier']);
    }
    
    private function calculateDaysOfStock(int $productId, int $currentStock): int
    {
        $demandPrediction = $this->predictDemand($productId, 1); // Demanda diaria
        $dailyDemand = $demandPrediction['daily_average'] ?? 1;
        
        return $dailyDemand > 0 ? floor($currentStock / $dailyDemand) : 999;
    }
    
    private function getHistoricalSalesData(int $productId, int $days): array
    {
        $dateFrom = date('Y-m-d', strtotime("-$days days"));
        
        return $this->db->query("
            SELECT DATE(im.created_at) as date, SUM(ABS(im.quantity)) as quantity
            FROM inventory_movements im
            WHERE im.product_id = ? 
            AND im.type = 'sale'
            AND DATE(im.created_at) >= ?
            GROUP BY DATE(im.created_at)
            ORDER BY date ASC
        ", [$productId, $dateFrom])->getResultArray();
    }
    
    private function calculateSeasonalFactor(int $productId): float
    {
        // Implementación simplificada del factor estacional
        $currentMonth = (int)date('n');
        
        // Factores estacionales por mes (ejemplo para productos electrónicos)
        $seasonalFactors = [
            1 => 0.8,  // Enero
            2 => 0.9,  // Febrero
            3 => 1.0,  // Marzo
            4 => 1.1,  // Abril
            5 => 1.2,  // Mayo (Día de la Madre)
            6 => 1.0,  // Junio
            7 => 1.0,  // Julio
            8 => 1.1,  // Agosto
            9 => 1.2,  // Septiembre (Regreso a clases)
            10 => 1.1, // Octubre
            11 => 1.4, // Noviembre (Black Friday)
            12 => 1.5  // Diciembre (Navidad)
        ];
        
        return $seasonalFactors[$currentMonth] ?? 1.0;
    }
    
    private function calculateTrend(array $historicalData): float
    {
        if (count($historicalData) < 2) {
            return 0;
        }
        
        $n = count($historicalData);
        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;
        
        foreach ($historicalData as $i => $data) {
            $x = $i + 1;
            $y = $data['quantity'];
            
            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        
        return $slope;
    }
    
    private function calculatePredictionConfidence(array $historicalData): float
    {
        if (empty($historicalData)) {
            return 0;
        }
        
        $quantities = array_column($historicalData, 'quantity');
        $mean = array_sum($quantities) / count($quantities);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $quantities)) / count($quantities);
        
        $coefficientOfVariation = $mean > 0 ? sqrt($variance) / $mean : 1;
        
        // Convertir a porcentaje de confianza (inverso del coeficiente de variación)
        return max(0, min(100, (1 - $coefficientOfVariation) * 100));
    }
    
    private function calculateUrgency(array $product): float
    {
        $daysOfStock = $this->calculateDaysOfStock($product['id'], $product['stock']);
        $leadTime = $this->config['lead_time_days'];
        
        if ($daysOfStock <= $leadTime) {
            return 1.0; // Urgencia máxima
        } elseif ($daysOfStock <= $leadTime * 2) {
            return 0.7; // Urgencia alta
        } elseif ($daysOfStock <= $leadTime * 3) {
            return 0.4; // Urgencia media
        } else {
            return 0.1; // Urgencia baja
        }
    }
    
    private function estimateReorderCost(int $productId, int $quantity): float
    {
        $product = $this->getProduct($productId);
        $costPrice = $product['cost_price'] ?? $product['price'] * 0.7; // Estimar 70% del precio de venta
        
        return $quantity * $costPrice;
    }
    
    private function getPreferredSupplier(int $productId): ?array
    {
        // Implementación simplificada
        return [
            'name' => 'Proveedor Principal',
            'contact' => '<EMAIL>',
            'lead_time' => $this->config['lead_time_days']
        ];
    }
    
    private function createReorderSuggestion(int $productId, int $currentStock): void
    {
        $suggestedQuantity = $this->calculateSuggestedOrderQuantity($productId);
        
        $this->db->table('reorder_suggestions')->insert([
            'product_id' => $productId,
            'current_stock' => $currentStock,
            'suggested_quantity' => $suggestedQuantity,
            'estimated_cost' => $this->estimateReorderCost($productId, $suggestedQuantity),
            'urgency' => $this->calculateUrgency(['id' => $productId, 'stock' => $currentStock]),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    private function getProductAlerts(int $productId): array
    {
        return $this->db->table('inventory_alerts')
                       ->where('product_id', $productId)
                       ->where('is_resolved', 0)
                       ->get()
                       ->getResultArray();
    }
    
    private function calculateInventoryTurnover(int $days): float
    {
        $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        // COGS (Cost of Goods Sold)
        $cogs = $this->db->query("
            SELECT SUM(ABS(im.quantity) * COALESCE(im.cost_per_unit, p.cost_price, p.price * 0.7)) as cogs
            FROM inventory_movements im
            JOIN products p ON p.id = im.product_id
            WHERE im.type = 'sale' AND im.created_at >= ?
        ", [$dateFrom])->getRowArray()['cogs'] ?? 0;
        
        // Inventario promedio
        $averageInventory = $this->db->query("
            SELECT AVG(p.stock * COALESCE(p.cost_price, p.price * 0.7)) as avg_inventory
            FROM products p
            WHERE p.is_active = 1
        ")->getRowArray()['avg_inventory'] ?? 1;
        
        return $averageInventory > 0 ? ($cogs / $averageInventory) * (365 / $days) : 0;
    }
    
    private function getTotalActiveProducts(): int
    {
        return $this->db->table('products')
                       ->where('is_active', 1)
                       ->countAllResults();
    }
    
    private function getAverageStockLevel(): float
    {
        $result = $this->db->table('products')
                          ->where('is_active', 1)
                          ->selectAvg('stock')
                          ->get()
                          ->getRowArray();
        
        return $result['stock'] ?? 0;
    }
    
    /**
     * Crear tablas de inventario
     */
    private function createInventoryTables(): void
    {
        try {
            // Tabla de movimientos de inventario
            $this->db->query("
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    type ENUM('sale', 'purchase', 'restock', 'adjustment', 'return', 'damage', 'loss') NOT NULL,
                    quantity INT NOT NULL,
                    old_stock INT NOT NULL,
                    new_stock INT NOT NULL,
                    reference VARCHAR(255),
                    notes TEXT,
                    location_id INT DEFAULT 1,
                    batch_number VARCHAR(100),
                    expiry_date DATE,
                    cost_per_unit DECIMAL(10,2),
                    user_id INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_product_id (product_id),
                    INDEX idx_type (type),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ");
            
            // Tabla de ubicaciones de inventario
            $this->db->query("
                CREATE TABLE IF NOT EXISTS inventory_locations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    address TEXT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            
            // Insertar ubicación por defecto
            $this->db->query("
                INSERT IGNORE INTO inventory_locations (id, name, description) 
                VALUES (1, 'Almacén Principal', 'Ubicación principal del inventario')
            ");
            
            // Tabla de alertas de inventario
            $this->db->query("
                CREATE TABLE IF NOT EXISTS inventory_alerts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    alert_type VARCHAR(50) NOT NULL,
                    message TEXT NOT NULL,
                    current_stock INT,
                    threshold INT,
                    is_resolved TINYINT(1) DEFAULT 0,
                    resolved_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_product_id (product_id),
                    INDEX idx_alert_type (alert_type),
                    INDEX idx_is_resolved (is_resolved),
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ");
            
            // Tabla de sugerencias de reorden
            $this->db->query("
                CREATE TABLE IF NOT EXISTS reorder_suggestions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    current_stock INT NOT NULL,
                    suggested_quantity INT NOT NULL,
                    estimated_cost DECIMAL(10,2),
                    urgency DECIMAL(3,2),
                    status ENUM('pending', 'approved', 'ordered', 'rejected') DEFAULT 'pending',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_product_id (product_id),
                    INDEX idx_status (status),
                    INDEX idx_urgency (urgency),
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Inventory tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'auto_reorder' => $this->config['auto_reorder'],
            'low_stock_threshold' => $this->config['low_stock_threshold'],
            'critical_stock_threshold' => $this->config['critical_stock_threshold'],
            'demand_prediction_enabled' => $this->config['demand_prediction_enabled'],
            'multi_location_enabled' => $this->config['multi_location_enabled'],
            'barcode_tracking' => $this->config['barcode_tracking'],
            'batch_tracking' => $this->config['batch_tracking'],
            'expiry_tracking' => $this->config['expiry_tracking']
        ];
    }
}
