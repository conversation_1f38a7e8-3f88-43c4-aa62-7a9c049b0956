/**
 * Service Worker Avanzado para MrCell Guatemala
 * PWA, Cache, Notificaciones Push y Funcionalidades Offline
 *
 * <AUTHOR> Development Team
 * @version 2.0
 */

const CACHE_NAME = 'mrcell-v2.0.0';
const OFFLINE_URL = '/offline.html';
const API_CACHE_NAME = 'mrcell-api-v2.0.0';

// Recursos críticos para cachear (solo archivos que existen)
const CRITICAL_RESOURCES = [
    '/offline.html',
    '/manifest.json',
    '/icon-96x96.png',
    '/icon-144x144.png',
    '/icon-192x192.png',
    '/icon-512x512.png'
];

/**
 * Evento de instalación del Service Worker
 */
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Installing...');

    event.waitUntil(
        Promise.all([
            // Cachear recursos críticos
            caches.open(CACHE_NAME).then(cache => {
                console.log('📦 Service Worker: Caching critical resources');
                return cache.addAll(CRITICAL_RESOURCES);
            }),

            // Forzar activación inmediata
            self.skipWaiting()
        ])
    );
});

/**
 * Evento de activación del Service Worker
 */
self.addEventListener('activate', event => {
    console.log('✅ Service Worker: Activating...');

    event.waitUntil(
        Promise.all([
            // Limpiar caches antiguos
            cleanupOldCaches(),

            // Tomar control de todas las pestañas
            self.clients.claim()
        ])
    );
});

/**
 * Interceptar requests de red
 */
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);

    // Solo manejar requests HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }

    // Para peticiones POST/PUT/DELETE (como pagos), pasar directamente al servidor
    if (request.method !== 'GET') {
        event.respondWith(fetch(request));
        return;
    }

    // Determinar estrategia de cache solo para peticiones GET
    const strategy = getCacheStrategy(request);

    event.respondWith(
        handleRequest(request, strategy)
    );
});

/**
 * Manejar notificaciones push
 */
self.addEventListener('push', event => {
    console.log('📱 Service Worker: Push notification received');

    if (!event.data) {
        return;
    }

    try {
        const data = event.data.json();

        const options = {
            body: data.body,
            icon: data.icon || '/icon-192x192.png',
            badge: data.badge || '/icon-96x96.png',
            image: data.image,
            data: data.data || {},
            actions: data.actions || [],
            requireInteraction: data.requireInteraction || false,
            tag: data.tag || 'default',
            renotify: true,
            vibrate: [200, 100, 200],
            timestamp: Date.now()
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );

    } catch (error) {
        console.error('❌ Service Worker: Error handling push notification:', error);

        // Mostrar notificación genérica en caso de error
        event.waitUntil(
            self.registration.showNotification('MrCell Guatemala', {
                body: 'Tienes una nueva notificación',
                icon: '/icon-192x192.png',
                tag: 'fallback'
            })
        );
    }
});

/**
 * Manejar clics en notificaciones
 */
self.addEventListener('notificationclick', event => {
    console.log('🔔 Service Worker: Notification clicked');

    event.notification.close();

    const data = event.notification.data || {};
    const action = event.action;

    let url = data.url || '/';

    // Manejar acciones específicas
    switch (action) {
        case 'view':
            url = data.url || '/';
            break;
        case 'buy':
            url = data.url ? data.url + '?action=buy' : '/';
            break;
        case 'track':
            url = data.tracking_url || data.url || '/';
            break;
        case 'explore':
            url = '/productos';
            break;
        default:
            url = data.url || '/';
    }

    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true }).then(clientList => {
            // Buscar ventana existente
            for (const client of clientList) {
                if (client.url.includes(new URL(url).pathname) && 'focus' in client) {
                    return client.focus();
                }
            }

            // Abrir nueva ventana
            if (clients.openWindow) {
                return clients.openWindow(url);
            }
        })
    );
});

/**
 * Funciones auxiliares
 */
function cleanupOldCaches() {
    return caches.keys().then(cacheNames => {
        const validCaches = [CACHE_NAME, API_CACHE_NAME];

        return Promise.all(
            cacheNames
                .filter(cacheName => !validCaches.includes(cacheName))
                .map(cacheName => {
                    console.log('🗑️ Service Worker: Deleting old cache:', cacheName);
                    return caches.delete(cacheName);
                })
        );
    });
}

function getCacheStrategy(request) {
    const url = new URL(request.url);

    // Imágenes
    if (request.destination === 'image') {
        return 'cache-first';
    }

    // API calls
    if (url.pathname.startsWith('/api/')) {
        return 'network-first';
    }

    // Assets estáticos
    if (url.pathname.match(/\.(css|js|woff2?|ttf|eot)$/)) {
        return 'cache-first';
    }

    // Páginas HTML
    if (request.destination === 'document') {
        return 'network-first';
    }

    return 'network-first';
}

async function handleRequest(request, strategy) {
    switch (strategy) {
        case 'cache-first':
            return cacheFirst(request);
        case 'network-first':
            return networkFirst(request);
        default:
            return networkFirst(request);
    }
}

async function cacheFirst(request) {
    // Solo buscar en cache para peticiones GET
    if (request.method === 'GET') {
        const cachedResponse = await caches.match(request);

        if (cachedResponse) {
            return cachedResponse;
        }
    }

    try {
        const networkResponse = await fetch(request);

        // Solo cachear peticiones GET exitosas
        if (networkResponse.ok && request.method === 'GET') {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        // Para peticiones POST/PUT/DELETE que fallan, devolver error apropiado
        return new Response(
            JSON.stringify({
                error: 'Network error',
                message: 'No hay conexión a internet',
                offline: true
            }),
            {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);

        // Solo cachear peticiones GET exitosas
        if (networkResponse.ok && request.method === 'GET') {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        // Solo buscar en cache para peticiones GET
        if (request.method === 'GET') {
            const cachedResponse = await caches.match(request);

            if (cachedResponse) {
                return cachedResponse;
            }
        }

        // Si es una página HTML, mostrar página offline
        if (request.destination === 'document') {
            const offlineResponse = await caches.match('/offline.html');
            if (offlineResponse) {
                return offlineResponse;
            }
        }

        // Para peticiones POST/PUT/DELETE que fallan, devolver error apropiado
        return new Response(
            JSON.stringify({
                error: 'Network error',
                message: 'No hay conexión a internet',
                offline: true
            }),
            {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

console.log('🚀 Service Worker: Loaded successfully with advanced features');
