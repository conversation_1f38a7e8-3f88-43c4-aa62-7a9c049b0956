<?php

/**
 * Script para debuggear productos que fallan en la sincronización con Recurrente
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "=== DEBUG DE PRODUCTOS FALLIDOS ===\n";
    echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
    echo "===================================\n\n";

    // Obtener productos con error
    $stmt = $db->prepare("
        SELECT 
            id, name, sku, description, short_description, 
            price_regular, price_sale, currency, stock_quantity,
            featured_image, recurrente_sync_status, recurrente_synced_at
        FROM products 
        WHERE recurrente_sync_status = 'error'
        AND is_active = 1 
        AND deleted_at IS NULL
        ORDER BY id ASC
    ");
    
    $stmt->execute();
    $failedProducts = $stmt->fetchAll();
    
    if (empty($failedProducts)) {
        echo "✅ No hay productos con errores de sincronización.\n";
        exit(0);
    }
    
    echo "📋 Productos con errores de sincronización:\n";
    echo "==========================================\n\n";
    
    foreach ($failedProducts as $product) {
        echo "🚗 PRODUCTO ID: {$product['id']}\n";
        echo "   Nombre: {$product['name']}\n";
        echo "   SKU: {$product['sku']}\n";
        echo "   Precio Regular: " . ($product['price_regular'] ?? 'NULL') . "\n";
        echo "   Precio Oferta: " . ($product['price_sale'] ?? 'NULL') . "\n";
        echo "   Moneda: " . ($product['currency'] ?? 'NULL') . "\n";
        echo "   Stock: " . ($product['stock_quantity'] ?? 'NULL') . "\n";
        echo "   Imagen: " . ($product['featured_image'] ?? 'NULL') . "\n";
        echo "   Descripción: " . (strlen($product['description'] ?? '') > 0 ? 'SÍ (' . strlen($product['description']) . ' chars)' : 'NO') . "\n";
        echo "   Descripción Corta: " . (strlen($product['short_description'] ?? '') > 0 ? 'SÍ (' . strlen($product['short_description']) . ' chars)' : 'NO') . "\n";
        echo "   Estado Sync: {$product['recurrente_sync_status']}\n";
        echo "   Última Sync: " . ($product['recurrente_synced_at'] ?? 'NULL') . "\n";
        
        // Verificar problemas específicos
        $issues = [];
        
        if (empty($product['price_regular']) || $product['price_regular'] <= 0) {
            $issues[] = "❌ Precio regular inválido o cero";
        }
        
        if (empty($product['name']) || strlen(trim($product['name'])) < 2) {
            $issues[] = "❌ Nombre muy corto o vacío";
        }
        
        if (empty($product['description']) && empty($product['short_description'])) {
            $issues[] = "⚠️  Sin descripción";
        }
        
        if (empty($product['currency'])) {
            $issues[] = "⚠️  Sin moneda especificada";
        }
        
        if (!empty($issues)) {
            echo "   PROBLEMAS DETECTADOS:\n";
            foreach ($issues as $issue) {
                echo "   {$issue}\n";
            }
        } else {
            echo "   ✅ Datos parecen correctos\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }
    
    echo "📊 RESUMEN:\n";
    echo "===========\n";
    echo "Total de productos con error: " . count($failedProducts) . "\n\n";
    
    // Intentar sincronizar uno de los productos fallidos para ver el error específico
    if (!empty($failedProducts)) {
        echo "🔍 INTENTANDO SINCRONIZAR EL PRIMER PRODUCTO PARA VER ERROR ESPECÍFICO:\n";
        echo "======================================================================\n\n";
        
        $testProduct = $failedProducts[0];
        
        // Obtener configuración de Recurrente
        $stmt = $db->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE 'recurrente_%'");
        $stmt->execute();
        $recurrenteSettings = [];
        while ($row = $stmt->fetch()) {
            $recurrenteSettings[$row['setting_key']] = $row['setting_value'];
        }
        
        if (empty($recurrenteSettings['recurrente_public_key']) || empty($recurrenteSettings['recurrente_secret_key'])) {
            echo "❌ Error: Recurrente no está configurado correctamente.\n";
            exit(1);
        }
        
        // Preparar datos del producto
        $price = !empty($testProduct['price_sale']) && $testProduct['price_sale'] > 0
            ? $testProduct['price_sale']
            : $testProduct['price_regular'];
        
        $imageUrl = null;
        if (!empty($testProduct['featured_image'])) {
            $productionUrl = 'https://mrcell.com.gt';
            $featuredImage = $testProduct['featured_image'];
            
            if (strpos($featuredImage, 'http') === 0) {
                $imageUrl = $featuredImage;
            } elseif (strpos($featuredImage, 'assets/') === 0) {
                $imageUrl = $productionUrl . '/' . $featuredImage;
            } else {
                $imageUrl = $productionUrl . '/assets/img/products/' . $featuredImage;
            }
        }
        
        $productData = [
            'product' => [
                'name' => $testProduct['name'],
                'description' => $testProduct['description'] ?: $testProduct['short_description'] ?: '',
                'image_url' => $imageUrl,
                'prices_attributes' => [
                    [
                        'currency' => $testProduct['currency'] ?: 'GTQ',
                        'charge_type' => 'one_time',
                        'amount_in_cents' => intval($price * 100)
                    ]
                ],
                'cancel_url' => 'https://mrcell.com.gt/checkout/cancel',
                'success_url' => 'https://mrcell.com.gt/checkout/success',
                'custom_terms_and_conditions' => 'Términos y condiciones de MrCell Guatemala.',
                'phone_requirement' => 'none',
                'address_requirement' => 'none',
                'billing_info_requirement' => 'none'
            ],
            'adjustable_quantity' => true,
            'inventory_quantity' => $testProduct['stock_quantity'],
            'metadata' => [
                'local_product_id' => (string)$testProduct['id'],
                'sku' => $testProduct['sku'],
                'platform' => 'MrCell_CI4'
            ]
        ];
        
        echo "📦 Datos a enviar a Recurrente:\n";
        echo json_encode($productData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        
        // Realizar petición a Recurrente
        $url = 'https://app.recurrente.com/api/products/';
        
        $headers = [
            'X-PUBLIC-KEY: ' . $recurrenteSettings['recurrente_public_key'],
            'X-SECRET-KEY: ' . $recurrenteSettings['recurrente_secret_key'],
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($productData)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "🌐 RESPUESTA DE RECURRENTE:\n";
        echo "HTTP Code: {$httpCode}\n";
        
        if ($error) {
            echo "❌ Error de cURL: {$error}\n";
        } else {
            echo "Respuesta completa:\n";
            echo $response . "\n";
            
            $decodedResponse = json_decode($response, true);
            if ($decodedResponse) {
                echo "\nRespuesta decodificada:\n";
                echo json_encode($decodedResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
