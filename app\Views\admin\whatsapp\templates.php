<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-file-alt me-2"></i>Plantillas de Mensajes</h1>
        <a href="/admin/whatsapp" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Volver
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Plantillas</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                    <i class="fas fa-plus me-2"></i>Nueva Plantilla
                </button>
            </div>
            <div class="card-body">
                <?php if (!empty($templates)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nombre</th>
                                    <th>Descripción</th>
                                    <th>Variables</th>
                                    <th>Tipo</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($templates as $template): ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($template['template_name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($template['template_key']) ?></small>
                                        </td>
                                        <td>
                                            <span class="text-muted">
                                                <?= esc(substr($template['description'], 0, 100)) ?>
                                                <?php if (strlen($template['description']) > 100): ?>...<?php endif; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($template['variables'])): ?>
                                                <?php $variables = is_string($template['variables']) ? json_decode($template['variables'], true) : $template['variables']; ?>
                                                <?php if (is_array($variables)): ?>
                                                    <?php foreach ($variables as $var): ?>
                                                        <span class="badge bg-info me-1">{<?= esc($var) ?>}</span>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Sin variables</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($template['is_mandatory']): ?>
                                                <span class="badge bg-warning">Obligatoria</span>
                                            <?php else: ?>
                                                <span class="badge bg-info">Opcional</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($template['is_active']): ?>
                                                <span class="badge bg-success">Activa</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactiva</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" 
                                                        onclick="viewTemplate(<?= $template['id'] ?>)"
                                                        title="Ver plantilla">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="/admin/whatsapp/templates/edit/<?= $template['id'] ?>" 
                                                   class="btn btn-outline-warning" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="testTemplate(<?= $template['id'] ?>)"
                                                        title="Probar plantilla">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay plantillas configuradas</h5>
                        <p class="text-muted">Las plantillas se crean automáticamente al ejecutar las migraciones</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal para ver plantilla -->
<div class="modal fade" id="viewTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ver Plantilla</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="templateContent">
                    <!-- Contenido cargado dinámicamente -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para probar plantilla -->
<div class="modal fade" id="testTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Probar Plantilla</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testTemplateForm">
                    <div class="mb-3">
                        <label for="testPhoneNumber" class="form-label">Número de Teléfono</label>
                        <input type="text" class="form-control" id="testPhoneNumber" 
                               placeholder="Ej: 50212345678" required>
                        <div class="form-text">Número sin espacios ni símbolos</div>
                    </div>
                    <div id="templateVariables">
                        <!-- Variables cargadas dinámicamente -->
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Vista Previa del Mensaje</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="messagePreview" class="text-muted">
                                Selecciona una plantilla para ver la vista previa
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="sendTestMessage()">
                    <i class="fas fa-paper-plane me-1"></i>Enviar Prueba
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let currentTemplate = null;

function viewTemplate(templateId) {
    // Buscar la plantilla en los datos
    const templates = <?= json_encode($templates) ?>;
    const template = templates.find(t => t.id == templateId);
    
    if (template) {
        const content = `
            <div class="mb-3">
                <h6>Nombre:</h6>
                <p>${template.template_name}</p>
            </div>
            <div class="mb-3">
                <h6>Descripción:</h6>
                <p class="text-muted">${template.description || 'Sin descripción'}</p>
            </div>
            <div class="mb-3">
                <h6>Mensaje:</h6>
                <div class="border rounded p-3 bg-light">
                    <pre class="mb-0">${template.message_template}</pre>
                </div>
            </div>
            <div class="mb-3">
                <h6>Variables:</h6>
                ${template.variables && template.variables.length > 0 
                    ? template.variables.map(v => `<span class="badge bg-info me-1">{${v}}</span>`).join('')
                    : '<span class="text-muted">Sin variables</span>'
                }
            </div>
            <div class="row">
                <div class="col-6">
                    <h6>Tipo:</h6>
                    <span class="badge ${template.is_mandatory ? 'bg-warning' : 'bg-info'}">
                        ${template.is_mandatory ? 'Obligatoria' : 'Opcional'}
                    </span>
                </div>
                <div class="col-6">
                    <h6>Estado:</h6>
                    <span class="badge ${template.is_active ? 'bg-success' : 'bg-secondary'}">
                        ${template.is_active ? 'Activa' : 'Inactiva'}
                    </span>
                </div>
            </div>
        `;
        
        document.getElementById('templateContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('viewTemplateModal')).show();
    }
}

function testTemplate(templateId) {
    const templates = <?= json_encode($templates) ?>;
    const template = templates.find(t => t.id == templateId);
    
    if (template) {
        currentTemplate = template;
        
        // Generar campos para variables
        let variablesHtml = '';
        if (template.variables && template.variables.length > 0) {
            variablesHtml = '<h6>Variables de la Plantilla:</h6>';
            template.variables.forEach(variable => {
                variablesHtml += `
                    <div class="mb-3">
                        <label for="var_${variable}" class="form-label">{${variable}}</label>
                        <input type="text" class="form-control template-variable" 
                               id="var_${variable}" data-variable="${variable}"
                               placeholder="Valor para ${variable}">
                    </div>
                `;
            });
        }
        
        document.getElementById('templateVariables').innerHTML = variablesHtml;
        updateMessagePreview();
        
        // Agregar event listeners para actualizar vista previa
        document.querySelectorAll('.template-variable').forEach(input => {
            input.addEventListener('input', updateMessagePreview);
        });
        
        new bootstrap.Modal(document.getElementById('testTemplateModal')).show();
    }
}

function updateMessagePreview() {
    if (!currentTemplate) return;
    
    let message = currentTemplate.message_template;
    
    // Reemplazar variables con valores del formulario
    document.querySelectorAll('.template-variable').forEach(input => {
        const variable = input.dataset.variable;
        const value = input.value || `{${variable}}`;
        message = message.replace(new RegExp(`\\{${variable}\\}`, 'g'), value);
    });
    
    document.getElementById('messagePreview').textContent = message;
}

function sendTestMessage() {
    if (!currentTemplate) return;
    
    const phoneNumber = document.getElementById('testPhoneNumber').value;
    if (!phoneNumber) {
        alert('Por favor ingresa un número de teléfono');
        return;
    }
    
    // Recopilar variables
    const variables = {};
    document.querySelectorAll('.template-variable').forEach(input => {
        variables[input.dataset.variable] = input.value;
    });
    
    // Enviar mensaje de prueba
    fetch('/admin/whatsapp/send-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            template_key: currentTemplate.template_key,
            phone_number: phoneNumber,
            variables: variables
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Mensaje enviado correctamente');
            bootstrap.Modal.getInstance(document.getElementById('testTemplateModal')).hide();
        } else {
            alert('Error al enviar mensaje: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error de conexión');
    });
}
</script>

<!-- Modal para crear nueva plantilla -->
<div class="modal fade" id="createTemplateModal" tabindex="-1" aria-labelledby="createTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTemplateModalLabel">
                    <i class="fas fa-plus me-2"></i>Nueva Plantilla de WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createTemplateForm" method="POST" action="<?= base_url('admin/whatsapp/templates/create') ?>">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_name" class="form-label">Nombre de la Plantilla <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="template_name" name="template_name" required>
                                <div class="form-text">Nombre único para identificar la plantilla</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_type" class="form-label">Tipo de Plantilla</label>
                                <select class="form-select" id="template_type" name="template_type">
                                    <option value="notification">Notificación</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="order">Orden</option>
                                    <option value="customer">Cliente</option>
                                    <option value="system">Sistema</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <input type="text" class="form-control" id="description" name="description">
                        <div class="form-text">Descripción breve de para qué se usa esta plantilla</div>
                    </div>

                    <div class="mb-3">
                        <label for="message_template" class="form-label">Mensaje de la Plantilla <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="message_template" name="message_template" rows="6" required></textarea>
                        <div class="form-text">
                            Usa variables como: {customer_name}, {order_number}, {total}, etc.
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="variables" class="form-label">Variables Disponibles</label>
                                <input type="text" class="form-control" id="variables" name="variables"
                                       placeholder="customer_name,order_number,total">
                                <div class="form-text">Variables separadas por comas</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Estado</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        Plantilla activa
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Variables Comunes:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0 small">
                                    <li><code>{customer_name}</code> - Nombre del cliente</li>
                                    <li><code>{order_number}</code> - Número de orden</li>
                                    <li><code>{total}</code> - Total de la orden</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0 small">
                                    <li><code>{site_name}</code> - Nombre del sitio</li>
                                    <li><code>{contact_phone}</code> - Teléfono de contacto</li>
                                    <li><code>{date}</code> - Fecha actual</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Crear Plantilla
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Manejar envío del formulario de crear plantilla
document.getElementById('createTemplateForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creando...';
    submitBtn.disabled = true;

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Plantilla creada exitosamente');
            location.reload();
        } else {
            alert('❌ Error: ' + (data.error || 'Error desconocido'));
        }
    })
    .catch(error => {
        alert('❌ Error de conexión');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
<?= $this->endSection() ?>
