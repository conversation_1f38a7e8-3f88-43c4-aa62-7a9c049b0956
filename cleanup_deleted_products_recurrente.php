<?php

/**
 * Script para limpiar productos eliminados de Recurrente
 * 
 * Este script encuentra productos que fueron eliminados localmente (soft delete)
 * pero que aún existen en Recurrente y los elimina de allí también.
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// Parsear argumentos de línea de comandos
$options = getopt('', ['dry-run', 'limit:', 'help']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

$dryRun = isset($options['dry-run']);
$limit = isset($options['limit']) ? (int)$options['limit'] : null;

echo "=== LIMPIEZA DE PRODUCTOS ELIMINADOS EN RECURRENTE ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "Modo: " . ($dryRun ? 'DRY RUN (solo consulta)' : 'LIMPIEZA REAL') . "\n";
if ($limit) {
    echo "Límite: {$limit} productos\n";
}
echo "=====================================================\n\n";

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Obtener configuración de Recurrente
    $stmt = $db->prepare("
        SELECT setting_key, setting_value 
        FROM system_settings 
        WHERE setting_key IN ('recurrente_public_key', 'recurrente_secret_key', 'recurrente_enabled')
        AND is_active = 1
    ");
    $stmt->execute();
    
    $recurrenteConfig = [];
    while ($row = $stmt->fetch()) {
        $recurrenteConfig[$row['setting_key']] = $row['setting_value'];
    }

    if (empty($recurrenteConfig['recurrente_public_key']) || empty($recurrenteConfig['recurrente_secret_key'])) {
        echo "❌ Error: Recurrente no está configurado correctamente.\n";
        exit(1);
    }

    if ($recurrenteConfig['recurrente_enabled'] !== '1') {
        echo "⚠️  Advertencia: Recurrente está deshabilitado en la configuración.\n";
        echo "¿Desea continuar de todos modos? (s/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 's') {
            echo "❌ Operación cancelada.\n";
            exit(1);
        }
    }

    // Buscar productos eliminados localmente que aún tienen ID de Recurrente
    $whereClause = "WHERE deleted_at IS NOT NULL AND recurrente_product_id IS NOT NULL";
    $limitClause = $limit ? "LIMIT {$limit}" : "";
    
    $query = "
        SELECT 
            id, name, sku, recurrente_product_id, recurrente_sync_status,
            deleted_at, recurrente_synced_at
        FROM products 
        {$whereClause}
        ORDER BY deleted_at DESC
        {$limitClause}
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $deletedProducts = $stmt->fetchAll();
    
    if (empty($deletedProducts)) {
        echo "✅ No hay productos eliminados que necesiten limpieza en Recurrente.\n";
        exit(0);
    }
    
    echo "📋 Productos eliminados localmente que aún están en Recurrente:\n";
    echo "ID\tSKU\t\tNombre\t\t\tRecurrente ID\t\tEliminado\n";
    echo "---\t---\t\t------\t\t\t-------------\t\t---------\n";
    
    foreach ($deletedProducts as $product) {
        printf(
            "%d\t%s\t\t%s\t\t%s\t\t%s\n",
            $product['id'],
            substr($product['sku'], 0, 15),
            substr($product['name'], 0, 30),
            $product['recurrente_product_id'],
            substr($product['deleted_at'], 0, 10)
        );
    }
    
    echo "\n📊 Total de productos a limpiar: " . count($deletedProducts) . "\n\n";
    
    if ($dryRun) {
        echo "🔍 Modo DRY RUN - No se realizarán cambios.\n";
        echo "Para ejecutar la limpieza real, use: php cleanup_deleted_products_recurrente.php\n";
        exit(0);
    }
    
    echo "🧹 Iniciando limpieza...\n\n";
    
    $cleaned = 0;
    $errors = 0;
    $notFound = 0;
    
    foreach ($deletedProducts as $product) {
        echo "🗑️  Procesando: {$product['name']} (ID: {$product['id']})...\n";
        
        try {
            // Eliminar de Recurrente
            $url = "https://app.recurrente.com/api/products/{$product['recurrente_product_id']}";
            
            $headers = [
                'X-PUBLIC-KEY: ' . $recurrenteConfig['recurrente_public_key'],
                'X-SECRET-KEY: ' . $recurrenteConfig['recurrente_secret_key'],
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_CUSTOMREQUEST => 'DELETE',
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception('Error de conexión: ' . $error);
            }
            
            if ($httpCode === 404) {
                echo "   ℹ️  Producto ya no existe en Recurrente\n";
                $notFound++;
            } elseif ($httpCode >= 400) {
                throw new Exception("Error HTTP {$httpCode}: {$response}");
            } else {
                echo "   ✅ Eliminado de Recurrente exitosamente\n";
            }
            
            // Limpiar campos de Recurrente en la base de datos local
            $updateStmt = $db->prepare("
                UPDATE products
                SET recurrente_product_id = NULL,
                    recurrente_sync_status = 'disabled',
                    recurrente_synced_at = NOW(),
                    recurrente_storefront_link = NULL
                WHERE id = ?
            ");
            $updateStmt->execute([$product['id']]);
            
            echo "   📝 Campos de Recurrente limpiados en BD local\n";
            $cleaned++;
            
        } catch (Exception $e) {
            echo "   ❌ Error: " . $e->getMessage() . "\n";
            
            // Marcar como error en la base de datos
            $errorStmt = $db->prepare("
                UPDATE products
                SET recurrente_sync_status = 'error',
                    recurrente_synced_at = NOW()
                WHERE id = ?
            ");
            $errorStmt->execute([$product['id']]);
            
            $errors++;
        }
        
        // Pausa para no sobrecargar la API
        usleep(500000); // 0.5 segundos
    }
    
    echo "\n📊 RESUMEN DE LIMPIEZA:\n";
    echo "======================\n";
    echo "Total procesados: " . count($deletedProducts) . "\n";
    echo "Limpiados exitosamente: {$cleaned}\n";
    echo "Ya no existían: {$notFound}\n";
    echo "Errores: {$errors}\n";
    
    if ($cleaned > 0 || $notFound > 0) {
        echo "\n🎉 Limpieza completada exitosamente!\n";
    }
    
    if ($errors > 0) {
        echo "\n⚠️  Algunos productos tuvieron errores. Revise los logs para más detalles.\n";
    }

} catch (Exception $e) {
    echo "❌ Error inesperado: " . $e->getMessage() . "\n";
    exit(1);
}

function showHelp() {
    echo "Limpieza de Productos Eliminados en Recurrente - MrCell Guatemala\n";
    echo "================================================================\n\n";
    echo "Este script elimina de Recurrente los productos que fueron eliminados\n";
    echo "localmente (soft delete) pero que aún existen en la plataforma Recurrente.\n\n";
    echo "Uso:\n";
    echo "  php cleanup_deleted_products_recurrente.php [opciones]\n\n";
    echo "Opciones:\n";
    echo "  --dry-run    Solo muestra qué productos se limpiarían (no ejecuta)\n";
    echo "  --limit=N    Limita la limpieza a N productos\n";
    echo "  --help       Muestra esta ayuda\n\n";
    echo "Ejemplos:\n";
    echo "  php cleanup_deleted_products_recurrente.php --dry-run     # Ver qué se limpiaría\n";
    echo "  php cleanup_deleted_products_recurrente.php --limit=10    # Limpiar solo 10\n";
    echo "  php cleanup_deleted_products_recurrente.php               # Limpiar todos\n\n";
    echo "NOTA: Esta operación es irreversible en Recurrente\n";
}
