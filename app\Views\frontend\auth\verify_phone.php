<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Verificar Teléfono - MrCell Guatemala' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Pa<PERSON>a Mr<PERSON>ell - R<PERSON>jo, Negro, Blanco, Gris */
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --success-color: #059669;        /* Verde para éxito */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris medio claro */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Negro gris */
            --gray-900: #111827;             /* Negro */
            --whatsapp-color: #25d366;       /* Verde WhatsApp */
        }
        
        .auth-container {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--gray-800) 50%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }
        
        .auth-header {
            background: linear-gradient(135deg, var(--whatsapp-color), #128c7e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .auth-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
        }
        
        .auth-body {
            padding: 40px;
        }
        
        .verification-code-input {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .code-digit {
            width: 50px;
            height: 50px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .code-digit:focus {
            border-color: var(--whatsapp-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
            outline: none;
        }
        
        .btn-verify {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-verify::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-verify:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            color: var(--white-color);
        }

        .btn-verify:hover::before {
            left: 100%;
        }

        .btn-verify:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
            cursor: not-allowed;
        }
        
        .resend-code {
            text-align: center;
            margin-top: 20px;
        }
        
        .resend-code button {
            background: none;
            border: none;
            color: var(--primary-color);
            text-decoration: underline;
            cursor: pointer;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .resend-code button:hover:not(:disabled) {
            color: var(--primary-dark);
        }

        .resend-code button:disabled {
            color: var(--gray-500);
            cursor: not-allowed;
            text-decoration: none;
        }

        .countdown {
            color: var(--gray-500);
            font-size: 14px;
            font-weight: 500;
        }

        .whatsapp-info {
            background: var(--light-color);
            border-left: 4px solid var(--whatsapp-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid var(--gray-200);
        }

        .whatsapp-info i {
            color: var(--whatsapp-color);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
            padding: 15px 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--primary-dark));
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color), #0ea5e9);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="/logo.jpg" alt="MrCell Logo" style="width: 40px; height: 40px; object-fit: contain;">
                </div>
                <h3>Verificar Teléfono</h3>
                <p class="mb-0">Ingresa el código que enviamos por WhatsApp</p>
            </div>
            
            <div class="auth-body">
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Errores de validación:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="whatsapp-info">
                    <i class="fab fa-whatsapp me-2"></i>
                    <strong>Código enviado por WhatsApp</strong><br>
                    <small>Revisa tu WhatsApp y ingresa el código de 6 dígitos que recibiste.</small>
                </div>
                
                <form action="<?= base_url('verify-phone') ?>" method="POST" id="verifyForm">
                    <?= csrf_field() ?>
                    <input type="hidden" name="user_id" value="<?= esc($user_id) ?>">
                    
                    <div class="verification-code-input">
                        <input type="text" class="code-digit" maxlength="1" data-index="0">
                        <input type="text" class="code-digit" maxlength="1" data-index="1">
                        <input type="text" class="code-digit" maxlength="1" data-index="2">
                        <input type="text" class="code-digit" maxlength="1" data-index="3">
                        <input type="text" class="code-digit" maxlength="1" data-index="4">
                        <input type="text" class="code-digit" maxlength="1" data-index="5">
                    </div>
                    
                    <input type="hidden" name="verification_code" id="verification_code">
                    
                    <button type="submit" class="btn btn-verify" id="verifyBtn" disabled>
                        <i class="fas fa-check me-2"></i>Verificar Código
                    </button>
                </form>
                
                <div class="resend-code">
                    <p class="mb-2">¿No recibiste el código?</p>
                    <button type="button" id="resendBtn" onclick="resendCode()" disabled>
                        Reenviar código
                    </button>
                    <div class="countdown" id="countdown">
                        Podrás reenviar en <span id="timer">60</span> segundos
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="<?= base_url('register') ?>" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>Volver al registro
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Manejo de inputs de código
        const codeInputs = document.querySelectorAll('.code-digit');
        const verificationCodeInput = document.getElementById('verification_code');
        const verifyBtn = document.getElementById('verifyBtn');
        
        codeInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                const value = e.target.value;
                
                // Solo permitir números
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }
                
                // Mover al siguiente input
                if (value && index < codeInputs.length - 1) {
                    codeInputs[index + 1].focus();
                }
                
                updateVerificationCode();
            });
            
            input.addEventListener('keydown', function(e) {
                // Mover al input anterior con backspace
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                }
            });
            
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                const digits = paste.replace(/\D/g, '').slice(0, 6);
                
                digits.split('').forEach((digit, i) => {
                    if (codeInputs[i]) {
                        codeInputs[i].value = digit;
                    }
                });
                
                updateVerificationCode();
            });
        });
        
        function updateVerificationCode() {
            const code = Array.from(codeInputs).map(input => input.value).join('');
            verificationCodeInput.value = code;
            verifyBtn.disabled = code.length !== 6;
        }
        
        // Countdown para reenvío
        let countdown = 60;
        const timerElement = document.getElementById('timer');
        const resendBtn = document.getElementById('resendBtn');
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            timerElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                resendBtn.disabled = false;
                countdownElement.style.display = 'none';
                resendBtn.textContent = 'Reenviar código';
            }
        }, 1000);
        
        function resendCode() {
            // Aquí implementarías la lógica para reenviar el código
            alert('Funcionalidad de reenvío no implementada aún');
        }
        
        // Auto-focus en el primer input
        codeInputs[0].focus();
    </script>
</body>
</html>
