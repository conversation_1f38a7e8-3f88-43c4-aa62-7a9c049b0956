<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fab fa-whatsapp me-2 text-success"></i>WhatsApp</h1>
        <div class="btn-group">
            <a href="/admin/whatsapp/settings" class="btn btn-outline-primary">
                <i class="fas fa-cog me-1"></i>Configuración
            </a>
            <a href="/admin/whatsapp/test-connection" class="btn btn-outline-success">
                <i class="fas fa-plug me-1"></i>Probar Conexión
            </a>
        </div>
    </div>
</div>

<!-- Estado del Servicio -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <?php if (isset($settings['enabled']) && $settings['enabled'] === '1'): ?>
                            <span class="badge bg-success fs-6">
                                <i class="fas fa-check-circle me-1"></i>Activo
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger fs-6">
                                <i class="fas fa-times-circle me-1"></i>Inactivo
                            </span>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h5 class="mb-1">Estado del Servicio WhatsApp</h5>
                        <p class="text-muted mb-0">
                            <?php if (isset($settings['enabled']) && $settings['enabled'] === '1'): ?>
                                El servicio de WhatsApp está funcionando correctamente
                            <?php else: ?>
                                El servicio de WhatsApp está deshabilitado
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-paper-plane fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['total_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Total Mensajes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['sent_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Enviados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['failed_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Fallidos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-percentage fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['success_rate'] ?? 0, 1) ?>%</h3>
                <p class="text-muted mb-0">Tasa de Éxito</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Plantillas de Mensajes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Plantillas de Mensajes</h5>
                <a href="/admin/whatsapp/templates" class="btn btn-sm btn-outline-primary">
                    Ver Todas
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($templates)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($templates, 0, 5) as $template): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <h6 class="mb-1"><?= esc($template['template_name']) ?></h6>
                                    <small class="text-muted"><?= esc($template['description']) ?></small>
                                    <?php if ($template['is_mandatory']): ?>
                                        <span class="badge bg-warning ms-2">Obligatoria</span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if ($template['is_active']): ?>
                                        <span class="badge bg-success">Activa</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactiva</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center py-3">No hay plantillas configuradas</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Mensajes Recientes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Mensajes Recientes</h5>
                <a href="/admin/whatsapp/message-log" class="btn btn-sm btn-outline-primary">
                    Ver Todos
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_messages)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_messages as $message): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong class="me-2"><?= esc($message['phone_number']) ?></strong>
                                            <?php
                                            $statusClass = match($message['status']) {
                                                'sent' => 'bg-success',
                                                'failed' => 'bg-danger',
                                                'pending' => 'bg-warning',
                                                default => 'bg-secondary'
                                            };
                                            ?>
                                            <span class="badge <?= $statusClass ?>"><?= ucfirst($message['status']) ?></span>
                                        </div>
                                        <p class="mb-1 text-truncate" style="max-width: 300px;">
                                            <?= esc(substr($message['message_content'], 0, 100)) ?>
                                            <?php if (strlen($message['message_content']) > 100): ?>...<?php endif; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?= date('d/m/Y H:i', strtotime($message['created_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center py-3">No hay mensajes recientes</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="/admin/whatsapp/send-message" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-paper-plane me-2"></i>
                            Enviar Mensaje Manual
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/whatsapp/templates" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-edit me-2"></i>
                            Editar Plantillas
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/whatsapp/statistics" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-chart-bar me-2"></i>
                            Ver Estadísticas
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/whatsapp/message-log" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-list me-2"></i>
                            Log de Mensajes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Auto-actualizar estadísticas cada 30 segundos
setInterval(function() {
    fetch('/admin/whatsapp/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.total_messages !== undefined) {
                // Actualizar estadísticas en tiempo real
                document.querySelector('.card:nth-child(1) h3').textContent = new Intl.NumberFormat().format(data.total_messages);
                document.querySelector('.card:nth-child(2) h3').textContent = new Intl.NumberFormat().format(data.sent_messages);
                document.querySelector('.card:nth-child(3) h3').textContent = new Intl.NumberFormat().format(data.failed_messages);
                document.querySelector('.card:nth-child(4) h3').textContent = data.success_rate.toFixed(1) + '%';
            }
        })
        .catch(error => console.log('Error actualizando estadísticas:', error));
}, 30000);
</script>
<?= $this->endSection() ?>
