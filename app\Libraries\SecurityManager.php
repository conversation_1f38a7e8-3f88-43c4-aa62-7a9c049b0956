<?php

namespace App\Libraries;

/**
 * Gestor de Seguridad Avanzado
 * Sistema completo de seguridad, autenticación y protección
 */
class SecurityManager
{
    private $db;
    private $session;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->session = \Config\Services::session();
        
        $this->config = [
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 minutos
            'session_timeout' => 3600, // 1 hora
            'password_min_length' => 8,
            'require_2fa' => false,
            'jwt_secret' => env('JWT_SECRET', 'mrcell-guatemala-secret-key'),
            'jwt_expiry' => 86400, // 24 horas
            'csrf_protection' => true,
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 100,
            'rate_limit_window' => 3600, // 1 hora
            'ip_whitelist' => [],
            'ip_blacklist' => []
        ];
    }
    
    /**
     * Autenticar usuario
     */
    public function authenticate(string $email, string $password, bool $remember = false): array
    {
        try {
            // Verificar rate limiting
            if (!$this->checkRateLimit($email)) {
                return [
                    'success' => false,
                    'error' => 'Demasiados intentos de login. Intenta más tarde.',
                    'code' => 'RATE_LIMITED'
                ];
            }
            
            // Verificar si la cuenta está bloqueada
            if ($this->isAccountLocked($email)) {
                return [
                    'success' => false,
                    'error' => 'Cuenta temporalmente bloqueada por seguridad.',
                    'code' => 'ACCOUNT_LOCKED'
                ];
            }
            
            // Buscar usuario
            $user = $this->db->table('users')
                            ->where('email', $email)
                            ->where('is_active', 1)
                            ->get()
                            ->getRowArray();
            
            if (!$user) {
                $this->logFailedAttempt($email, 'USER_NOT_FOUND');
                return [
                    'success' => false,
                    'error' => 'Credenciales inválidas.',
                    'code' => 'INVALID_CREDENTIALS'
                ];
            }
            
            // Verificar contraseña
            if (!password_verify($password, $user['password'])) {
                $this->logFailedAttempt($email, 'INVALID_PASSWORD');
                return [
                    'success' => false,
                    'error' => 'Credenciales inválidas.',
                    'code' => 'INVALID_CREDENTIALS'
                ];
            }
            
            // Verificar 2FA si está habilitado
            if ($this->config['require_2fa'] && $user['two_factor_enabled']) {
                return [
                    'success' => false,
                    'requires_2fa' => true,
                    'user_id' => $user['id'],
                    'code' => 'REQUIRES_2FA'
                ];
            }
            
            // Autenticación exitosa
            $this->clearFailedAttempts($email);
            $sessionData = $this->createUserSession($user, $remember);
            
            // Log de login exitoso
            $this->logSecurityEvent('LOGIN_SUCCESS', $user['id'], [
                'ip' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'remember' => $remember
            ]);
            
            return [
                'success' => true,
                'user' => $this->sanitizeUserData($user),
                'session' => $sessionData,
                'token' => $this->generateJWT($user)
            ];
            
        } catch (\Exception $e) {
            $this->logSecurityEvent('LOGIN_ERROR', null, [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => 'Error interno del servidor.',
                'code' => 'INTERNAL_ERROR'
            ];
        }
    }
    
    /**
     * Verificar 2FA
     */
    public function verify2FA(int $userId, string $code): array
    {
        try {
            $user = $this->db->table('users')
                            ->where('id', $userId)
                            ->get()
                            ->getRowArray();
            
            if (!$user) {
                return [
                    'success' => false,
                    'error' => 'Usuario no encontrado.',
                    'code' => 'USER_NOT_FOUND'
                ];
            }
            
            // Verificar código 2FA (implementar según el método usado)
            $isValidCode = $this->validate2FACode($user, $code);
            
            if (!$isValidCode) {
                $this->logSecurityEvent('2FA_FAILED', $userId, [
                    'ip' => $this->getClientIP(),
                    'code_provided' => substr($code, 0, 2) . '****'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Código de verificación inválido.',
                    'code' => 'INVALID_2FA_CODE'
                ];
            }
            
            // 2FA exitoso
            $sessionData = $this->createUserSession($user, false);
            
            $this->logSecurityEvent('2FA_SUCCESS', $userId, [
                'ip' => $this->getClientIP()
            ]);
            
            return [
                'success' => true,
                'user' => $this->sanitizeUserData($user),
                'session' => $sessionData,
                'token' => $this->generateJWT($user)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error verificando código 2FA.',
                'code' => 'INTERNAL_ERROR'
            ];
        }
    }
    
    /**
     * Cerrar sesión
     */
    public function logout(?int $userId = null): array
    {
        try {
            $currentUserId = $userId ?? $this->session->get('user_id');
            
            if ($currentUserId) {
                // Invalidar todas las sesiones del usuario
                $this->db->table('user_sessions')
                        ->where('user_id', $currentUserId)
                        ->update(['is_active' => 0, 'ended_at' => date('Y-m-d H:i:s')]);
                
                $this->logSecurityEvent('LOGOUT', $currentUserId, [
                    'ip' => $this->getClientIP()
                ]);
            }
            
            // Limpiar sesión
            $this->session->destroy();
            
            return [
                'success' => true,
                'message' => 'Sesión cerrada exitosamente'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error cerrando sesión'
            ];
        }
    }
    
    /**
     * Verificar si el usuario está autenticado
     */
    public function isAuthenticated(): bool
    {
        $userId = $this->session->get('user_id');
        $sessionId = $this->session->get('session_id');
        
        if (!$userId || !$sessionId) {
            return false;
        }
        
        // Verificar sesión en base de datos
        $session = $this->db->table('user_sessions')
                           ->where('id', $sessionId)
                           ->where('user_id', $userId)
                           ->where('is_active', 1)
                           ->where('expires_at >', date('Y-m-d H:i:s'))
                           ->get()
                           ->getRowArray();
        
        if (!$session) {
            $this->session->destroy();
            return false;
        }
        
        // Actualizar última actividad
        $this->updateSessionActivity($sessionId);
        
        return true;
    }
    
    /**
     * Verificar permisos de usuario
     */
    public function hasPermission(string $permission, ?int $userId = null): bool
    {
        $userId = $userId ?? $this->session->get('user_id');
        
        if (!$userId) {
            return false;
        }
        
        try {
            // Verificar permisos directos del usuario
            $userPermission = $this->db->table('user_permissions up')
                                      ->join('permissions p', 'p.id = up.permission_id')
                                      ->where('up.user_id', $userId)
                                      ->where('p.name', $permission)
                                      ->where('up.is_active', 1)
                                      ->get()
                                      ->getRowArray();
            
            if ($userPermission) {
                return true;
            }
            
            // Verificar permisos por rol
            $rolePermission = $this->db->table('user_roles ur')
                                      ->join('role_permissions rp', 'rp.role_id = ur.role_id')
                                      ->join('permissions p', 'p.id = rp.permission_id')
                                      ->where('ur.user_id', $userId)
                                      ->where('p.name', $permission)
                                      ->where('ur.is_active', 1)
                                      ->where('rp.is_active', 1)
                                      ->get()
                                      ->getRowArray();
            
            return $rolePermission !== null;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Verificar si el usuario tiene un rol específico
     */
    public function hasRole(string $roleName, ?int $userId = null): bool
    {
        $userId = $userId ?? $this->session->get('user_id');
        
        if (!$userId) {
            return false;
        }
        
        try {
            $role = $this->db->table('user_roles ur')
                            ->join('roles r', 'r.id = ur.role_id')
                            ->where('ur.user_id', $userId)
                            ->where('r.name', $roleName)
                            ->where('ur.is_active', 1)
                            ->get()
                            ->getRowArray();
            
            return $role !== null;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Generar token CSRF
     */
    public function generateCSRFToken(): string
    {
        if (!$this->config['csrf_protection']) {
            return '';
        }
        
        $token = bin2hex(random_bytes(32));
        $this->session->set('csrf_token', $token);
        $this->session->set('csrf_token_time', time());
        
        return $token;
    }
    
    /**
     * Verificar token CSRF
     */
    public function verifyCSRFToken(string $token): bool
    {
        if (!$this->config['csrf_protection']) {
            return true;
        }
        
        $sessionToken = $this->session->get('csrf_token');
        $tokenTime = $this->session->get('csrf_token_time');
        
        // Token expirado (30 minutos)
        if (!$tokenTime || (time() - $tokenTime) > 1800) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }
    
    /**
     * Verificar rate limiting
     */
    public function checkRateLimit(string $identifier, string $action = 'login'): bool
    {
        if (!$this->config['rate_limit_enabled']) {
            return true;
        }
        
        try {
            $ip = $this->getClientIP();
            $key = $action . '_' . $ip . '_' . $identifier;
            
            $attempts = $this->db->table('rate_limits')
                                ->where('identifier', $key)
                                ->where('created_at >', date('Y-m-d H:i:s', time() - $this->config['rate_limit_window']))
                                ->countAllResults();
            
            if ($attempts >= $this->config['rate_limit_requests']) {
                return false;
            }
            
            // Registrar intento
            $this->db->table('rate_limits')->insert([
                'identifier' => $key,
                'ip_address' => $ip,
                'action' => $action,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return true; // En caso de error, permitir acceso
        }
    }
    
    /**
     * Verificar si una IP está en lista blanca/negra
     */
    public function checkIPAccess(string $ip): bool
    {
        // Lista negra tiene prioridad
        if (!empty($this->config['ip_blacklist']) && in_array($ip, $this->config['ip_blacklist'])) {
            return false;
        }
        
        // Si hay lista blanca, solo permitir IPs en la lista
        if (!empty($this->config['ip_whitelist'])) {
            return in_array($ip, $this->config['ip_whitelist']);
        }
        
        return true;
    }
    
    /**
     * Sanitizar datos de entrada
     */
    public function sanitizeInput(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInput($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Validar fortaleza de contraseña
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < $this->config['password_min_length']) {
            $errors[] = "La contraseña debe tener al menos {$this->config['password_min_length']} caracteres";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos una letra mayúscula';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos una letra minúscula';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos un número';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos un carácter especial';
        }
        
        // Verificar contraseñas comunes
        $commonPasswords = ['123456', 'password', 'admin', 'qwerty', '123456789'];
        if (in_array(strtolower($password), $commonPasswords)) {
            $errors[] = 'La contraseña es demasiado común';
        }
        
        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'strength_score' => $this->calculatePasswordStrength($password)
        ];
    }
    
    /**
     * Generar JWT token
     */
    public function generateJWT(array $user): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        
        $payload = json_encode([
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'] ?? 'user',
            'iat' => time(),
            'exp' => time() + $this->config['jwt_expiry']
        ]);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->config['jwt_secret'], true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Verificar JWT token
     */
    public function verifyJWT(string $token): array
    {
        try {
            $parts = explode('.', $token);
            
            if (count($parts) !== 3) {
                return ['valid' => false, 'error' => 'Token format invalid'];
            }
            
            [$header, $payload, $signature] = $parts;
            
            $validSignature = hash_hmac('sha256', $header . "." . $payload, $this->config['jwt_secret'], true);
            $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
            
            if (!hash_equals($signature, $validSignature)) {
                return ['valid' => false, 'error' => 'Invalid signature'];
            }
            
            $payloadData = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
            
            if ($payloadData['exp'] < time()) {
                return ['valid' => false, 'error' => 'Token expired'];
            }
            
            return ['valid' => true, 'data' => $payloadData];
            
        } catch (\Exception $e) {
            return ['valid' => false, 'error' => 'Token verification failed'];
        }
    }
    
    // Métodos privados auxiliares...
    
    private function isAccountLocked(string $email): bool
    {
        try {
            $attempts = $this->db->table('failed_login_attempts')
                                ->where('email', $email)
                                ->where('created_at >', date('Y-m-d H:i:s', time() - $this->config['lockout_duration']))
                                ->countAllResults();
            
            return $attempts >= $this->config['max_login_attempts'];
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    private function logFailedAttempt(string $email, string $reason): void
    {
        try {
            $this->db->table('failed_login_attempts')->insert([
                'email' => $email,
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'reason' => $reason,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail authentication
        }
    }
    
    private function clearFailedAttempts(string $email): void
    {
        try {
            $this->db->table('failed_login_attempts')
                    ->where('email', $email)
                    ->delete();
        } catch (\Exception $e) {
            // Log error but don't fail authentication
        }
    }
    
    private function createUserSession(array $user, bool $remember): array
    {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + ($remember ? 2592000 : $this->config['session_timeout'])); // 30 días si remember
        
        // Crear sesión en base de datos
        $this->db->table('user_sessions')->insert([
            'id' => $sessionId,
            'user_id' => $user['id'],
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'is_active' => 1,
            'remember_me' => $remember ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s'),
            'expires_at' => $expiresAt,
            'last_activity' => date('Y-m-d H:i:s')
        ]);
        
        // Establecer datos de sesión
        $this->session->set([
            'user_id' => $user['id'],
            'session_id' => $sessionId,
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'] ?? 'user',
            'is_authenticated' => true,
            'login_time' => time()
        ]);
        
        return [
            'session_id' => $sessionId,
            'expires_at' => $expiresAt,
            'remember_me' => $remember
        ];
    }
    
    private function updateSessionActivity(string $sessionId): void
    {
        try {
            $this->db->table('user_sessions')
                    ->where('id', $sessionId)
                    ->update(['last_activity' => date('Y-m-d H:i:s')]);
        } catch (\Exception $e) {
            // Log error but don't fail
        }
    }
    
    private function sanitizeUserData(array $user): array
    {
        unset($user['password']);
        unset($user['two_factor_secret']);
        return $user;
    }
    
    private function validate2FACode(array $user, string $code): bool
    {
        // Implementar validación según el método 2FA usado (TOTP, SMS, etc.)
        // Por ahora, simulamos validación exitosa
        return strlen($code) === 6 && is_numeric($code);
    }
    
    private function calculatePasswordStrength(string $password): int
    {
        $score = 0;
        
        // Longitud
        $score += min(strlen($password) * 2, 20);
        
        // Variedad de caracteres
        if (preg_match('/[a-z]/', $password)) $score += 5;
        if (preg_match('/[A-Z]/', $password)) $score += 5;
        if (preg_match('/[0-9]/', $password)) $score += 5;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
        
        // Patrones complejos
        if (preg_match('/[a-z].*[A-Z]|[A-Z].*[a-z]/', $password)) $score += 5;
        if (preg_match('/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/', $password)) $score += 5;
        
        return min($score, 100);
    }
    
    private function getClientIP(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    private function logSecurityEvent(string $event, ?int $userId, array $data = []): void
    {
        try {
            $this->db->table('security_log')->insert([
                'event_type' => $event,
                'user_id' => $userId,
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'event_data' => json_encode($data),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Log to file if database fails
            error_log("Security event logging failed: " . $e->getMessage());
        }
    }
}
