<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\UserAddressModel;

/**
 * Controlador de cuenta de usuario simplificado
 * Sin dependencia de stored procedures
 */
class UserAccountController extends Controller
{
    protected $addressModel;
    protected $session;

    public function __construct()
    {
        $this->addressModel = new UserAddressModel();
        $this->session = session();
    }

    /**
     * Verificar autenticación
     */
    private function checkAuth()
    {
        $userId = $this->session->get('user_id');
        log_message('error', 'UserAccountController::checkAuth: user_id en sesión = ' . ($userId ?: 'NULL'));

        if (!$userId) {
            log_message('error', 'UserAccountController::checkAuth: No hay user_id, redirigiendo a login');
            return redirect()->to('/login')->with('error', 'Debes iniciar sesión para acceder a tu cuenta');
        }

        log_message('error', 'UserAccountController::checkAuth: Autenticación exitosa para user_id = ' . $userId);
        return true;
    }

    /**
     * Dashboard principal
     */
    public function dashboard()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            log_message('debug', 'UserAccountController: Iniciando dashboard para user_id: ' . $userId);

            // Obtener información del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            log_message('debug', 'UserAccountController: Usuario obtenido: ' . ($user ? 'SÍ' : 'NO'));

            if (!$user) {
                log_message('error', 'UserAccountController: Usuario no encontrado para ID: ' . $userId);
                $this->session->destroy();
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Obtener estadísticas reales del usuario
            $totalOrders = 0;
            $totalSpent = 0;
            $wishlistItems = 0;
            $loyaltyPoints = 0;
            $totalSavings = 0;
            $recentOrders = [];
            $addresses = [];

            try {
                // Obtener estadísticas de pedidos
                $orderStats = $db->query("
                    SELECT
                        COUNT(*) as total_orders,
                        COALESCE(SUM(total), 0) as total_spent
                    FROM orders
                    WHERE customer_id = ? AND status != 'cancelled'
                ", [$userId])->getRowArray();

                if ($orderStats) {
                    $totalOrders = (int)$orderStats['total_orders'];
                    $totalSpent = (float)$orderStats['total_spent'];
                }

                // Obtener items de lista de deseos
                $wishlistCount = $db->query("
                    SELECT COUNT(*) as count
                    FROM wishlist
                    WHERE user_id = ?
                ", [$userId])->getRowArray();

                if ($wishlistCount) {
                    $wishlistItems = (int)$wishlistCount['count'];
                }

                // Obtener puntos de lealtad (si existe la tabla)
                if ($db->tableExists('user_loyalty_points')) {
                    $pointsData = $db->query("
                        SELECT COALESCE(SUM(points), 0) as total_points
                        FROM user_loyalty_points
                        WHERE user_id = ? AND status = 'active'
                    ", [$userId])->getRowArray();

                    if ($pointsData) {
                        $loyaltyPoints = (int)$pointsData['total_points'];
                    }
                }

                // Calcular ahorros totales (simulado - 10% del total gastado)
                $totalSavings = $totalSpent * 0.1;



                // Obtener pedidos recientes
                $recentOrders = $db->query("
                    SELECT o.*,
                           o.order_number,
                           o.total,
                           o.status,
                           o.created_at,
                           COUNT(oi.id) as total_items
                    FROM orders o
                    LEFT JOIN order_items oi ON o.id = oi.order_id
                    WHERE o.customer_id = ?
                    GROUP BY o.id
                    ORDER BY o.created_at DESC
                    LIMIT 5
                ", [$userId])->getResultArray();

                // Obtener direcciones
                if ($db->tableExists('user_addresses')) {
                    $addresses = $db->query("
                        SELECT * FROM user_addresses
                        WHERE user_id = ? AND is_active = 1
                        ORDER BY is_default DESC, created_at DESC
                    ", [$userId])->getResultArray();
                }

            } catch (\Exception $e) {
                log_message('error', 'Error obteniendo estadísticas de usuario: ' . $e->getMessage());
                // Mantener valores por defecto en caso de error
            }

            // Crear nombre completo
            $user['name'] = trim($user['first_name'] . ' ' . $user['last_name']);
            if (empty($user['name'])) {
                $user['name'] = $user['username'];
            }

            log_message('debug', 'UserAccountController: Preparando datos para la vista');

            $data = [
                'title' => 'Mi Cuenta - MrCell Guatemala',
                'page_title' => 'Mi Cuenta',
                'user' => $user,
                'stats' => [
                    'total_orders' => $totalOrders,
                    'total_spent' => $totalSpent,
                    'wishlist_items' => $wishlistItems,
                    'loyalty_points' => $loyaltyPoints,
                    'total_savings' => $totalSavings,
                    'addresses_count' => count($addresses)
                ],
                'recent_orders' => $recentOrders,
                'addresses' => $addresses
            ];

            log_message('debug', 'UserAccountController: Cargando vista frontend/user_dashboard');
            return view('frontend/user_dashboard', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en dashboard de usuario: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Error al cargar el dashboard. Por favor, inicia sesión nuevamente.');
        }
    }

    /**
     * Pedidos del usuario
     */
    public function orders()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            // Obtener datos del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!$user) {
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Obtener todos los pedidos del usuario con información adicional
            $orders = $db->query("
                SELECT o.*,
                       COUNT(oi.id) as total_items,
                       GROUP_CONCAT(oi.product_name SEPARATOR ', ') as product_names
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE o.customer_id = ?
                GROUP BY o.id
                ORDER BY o.created_at DESC
            ", [$userId])->getResultArray();

            // Filtrar pedidos por estado
            $ordersByStatus = [
                'all' => $orders,
                'pending' => array_filter($orders, fn($o) => in_array(strtolower($o['status']), ['pending', 'processing'])),
                'shipped' => array_filter($orders, fn($o) => strtolower($o['status']) === 'shipped'),
                'delivered' => array_filter($orders, fn($o) => strtolower($o['status']) === 'delivered'),
                'cancelled' => array_filter($orders, fn($o) => strtolower($o['status']) === 'cancelled')
            ];

            // Estadísticas de pedidos
            $orderStats = [
                'total' => count($orders),
                'pending' => count($ordersByStatus['pending']),
                'shipped' => count($ordersByStatus['shipped']),
                'delivered' => count($ordersByStatus['delivered']),
                'cancelled' => count($ordersByStatus['cancelled'])
            ];

            $data = [
                'title' => 'Mis Pedidos - MrCell Guatemala',
                'page_title' => 'Mis Pedidos',
                'user' => $user,
                'orders' => $orders,
                'orders_by_status' => $ordersByStatus,
                'order_stats' => $orderStats
            ];

            return view('frontend/user/orders', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar pedidos: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar los pedidos');
        }
    }

    /**
     * Mostrar detalles de un pedido específico
     */
    public function orderDetail($orderId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            // Obtener detalles del pedido
            $order = $db->query("
                SELECT o.*,
                       u.first_name, u.last_name, u.email
                FROM orders o
                LEFT JOIN users u ON o.customer_id = u.id
                WHERE o.id = ? AND o.customer_id = ?
            ", [$orderId, $userId])->getRowArray();

            if (!$order) {
                return redirect()->to('/cuenta/pedidos')->with('error', 'Pedido no encontrado');
            }

            // Obtener items del pedido
            $orderItems = $db->query("
                SELECT oi.*, p.name, p.slug, p.featured_image, p.sku
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = ?
                ORDER BY oi.id
            ", [$orderId])->getResultArray();

            // Obtener historial de estado del pedido (si existe la tabla)
            $statusHistory = [];
            try {
                $statusHistory = $db->query("
                    SELECT * FROM order_status_history
                    WHERE order_id = ?
                    ORDER BY created_at DESC
                ", [$orderId])->getResultArray();
            } catch (\Exception $e) {
                // Tabla no existe, usar historial básico
                $statusHistory = [
                    [
                        'status' => $order['status'],
                        'created_at' => $order['updated_at'] ?? $order['created_at'],
                        'notes' => 'Estado actual del pedido'
                    ]
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo detalles del pedido: ' . $e->getMessage());
            return redirect()->to('/cuenta/pedidos')->with('error', 'Pedido no encontrado');
        }

        $data = [
            'title' => 'Pedido #' . ($order['order_number'] ?? $order['id']) . ' - MrCell Guatemala',
            'page_title' => 'Detalles del Pedido',
            'order' => $order,
            'order_items' => $orderItems,
            'status_history' => $statusHistory
        ];

        return view('frontend/user/order_detail', $data);
    }

    /**
     * Lista de deseos
     */
    public function wishlist()
    {
        log_message('debug', 'UserAccountController::wishlist - INICIANDO MÉTODO');
        log_message('debug', 'UserAccountController::wishlist - URI actual: ' . current_url());

        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            log_message('debug', 'UserAccountController::wishlist - checkAuth falló, redireccionando');
            return $authCheck;
        }

        $userId = $this->session->get('user_id');
        log_message('debug', 'UserAccountController::wishlist - userId obtenido: ' . $userId);

        $db = \Config\Database::connect();

        try {
            // Obtener datos del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!$user) {
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Obtener productos de la wishlist - TEMPORALMENTE SIMPLIFICADO PARA DEBUG
            $wishlistItems = [];

            // Intentar obtener de la base de datos solo si las tablas existen
            try {
                if ($db->tableExists('wishlist') && $db->tableExists('products')) {
                    $wishlistItems = $db->table('wishlist w')
                                       ->select('w.*, p.name, p.price, p.image, p.slug')
                                       ->join('products p', 'p.id = w.product_id')
                                       ->where('w.user_id', $userId)
                                       ->orderBy('w.created_at', 'DESC')
                                       ->get()
                                       ->getResultArray();
                } else {
                    log_message('debug', 'UserAccountController::wishlist - Tablas wishlist o products no existen, usando datos vacíos');
                }
            } catch (\Exception $dbError) {
                log_message('error', 'UserAccountController::wishlist - Error de BD: ' . $dbError->getMessage());
                $wishlistItems = [];
            }

            log_message('debug', 'UserAccountController::wishlist - Items encontrados: ' . count($wishlistItems));

            $data = [
                'title' => 'Mi Lista de Deseos - MrCell Guatemala',
                'page_title' => 'Mi Lista de Deseos',
                'user' => $user,
                'wishlist_items' => $wishlistItems
            ];

            log_message('debug', 'UserAccountController::wishlist - Cargando vista frontend/user/wishlist');
            return view('frontend/user/wishlist', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar wishlist: ' . $e->getMessage());
            log_message('error', 'UserAccountController::wishlist - Stack trace: ' . $e->getTraceAsString());
            log_message('debug', 'UserAccountController::wishlist - EXCEPCIÓN, redireccionando a /cuenta');
            return redirect()->to('/cuenta')->with('error', 'Error al cargar la lista de deseos');
        }
    }

    /**
     * Direcciones del usuario
     */
    public function addresses()
    {
        log_message('debug', 'UserAccountController::addresses - INICIANDO MÉTODO');
        log_message('debug', 'UserAccountController::addresses - URI actual: ' . current_url());

        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            log_message('debug', 'UserAccountController::addresses - checkAuth falló, redireccionando');
            return $authCheck;
        }

        $userId = $this->session->get('user_id');
        log_message('debug', 'UserAccountController::addresses - userId obtenido: ' . $userId);

        try {
            $db = \Config\Database::connect();

            // Obtener datos del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!$user) {
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Intentar obtener direcciones - TEMPORALMENTE SIMPLIFICADO PARA DEBUG
            $addresses = [];
            $locations = [];

            try {
                $addresses = $this->addressModel->getUserAddresses($userId);
                $locations = $this->addressModel->getGuatemalaLocations();
                log_message('debug', 'UserAccountController::addresses - Direcciones encontradas: ' . count($addresses));
            } catch (\Exception $modelError) {
                log_message('error', 'UserAccountController::addresses - Error en modelo: ' . $modelError->getMessage());
                // Usar datos por defecto si hay error en el modelo
                $addresses = [];
                $locations = [
                    'Guatemala' => ['Guatemala', 'Mixco', 'Villa Nueva'],
                    'Sacatepéquez' => ['Antigua Guatemala', 'Ciudad Vieja'],
                    'Chimaltenango' => ['Chimaltenango', 'San José Poaquil']
                ];
            }

            $data = [
                'title' => 'Mis Direcciones - MrCell Guatemala',
                'page_title' => 'Mis Direcciones',
                'user' => $user,
                'addresses' => $addresses,
                'locations' => $locations
            ];

            log_message('debug', 'UserAccountController::addresses - Cargando vista frontend/user/addresses');
            return view('frontend/user/addresses', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar direcciones: ' . $e->getMessage());
            log_message('error', 'UserAccountController::addresses - Stack trace: ' . $e->getTraceAsString());
            log_message('debug', 'UserAccountController::addresses - EXCEPCIÓN, redireccionando a /cuenta');
            return redirect()->to('/cuenta')->with('error', 'Error al cargar las direcciones');
        }
    }

    /**
     * Guardar nueva dirección
     */
    public function saveAddress()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');

        try {
            $data = [
                'type' => 'both', // Por defecto para facturación y envío
                'address_name' => $this->request->getPost('address_name') ?: 'Dirección',
                'nombre_completo' => $this->request->getPost('recipient_name'),
                'telefono' => $this->request->getPost('phone'),
                'direccion_linea_1' => $this->request->getPost('full_address'),
                'direccion_linea_2' => '',
                'ciudad' => $this->request->getPost('municipality'),
                'estado_departamento' => $this->request->getPost('department'),
                'codigo_postal' => $this->request->getPost('postal_code'),
                'pais' => 'Guatemala',
                'is_default' => $this->request->getPost('is_default') ? 1 : 0
            ];

            $addressId = $this->addressModel->createAddress($data, $userId);

            if ($addressId) {
                return redirect()->to('/cuenta/direcciones')->with('success', 'Dirección guardada correctamente');
            } else {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Error al guardar la dirección');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al guardar dirección: ' . $e->getMessage());
            return redirect()->to('/cuenta/direcciones')->with('error', 'Error al guardar la dirección');
        }
    }

    /**
     * Establecer dirección como predeterminada
     */
    public function setDefaultAddress()
    {
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->to('/cuenta/direcciones');
        }

        $addressId = $this->request->getPost('address_id');
        $userId = session()->get('user_id');

        if (!$userId || !$addressId) {
            return redirect()->to('/cuenta/direcciones')->with('error', 'Datos inválidos');
        }

        try {
            $addressModel = new UserAddressModel();

            // Verificar que la dirección pertenece al usuario
            $address = $addressModel->where('id', $addressId)
                                  ->where('user_id', $userId)
                                  ->first();

            if (!$address) {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Dirección no encontrada');
            }

            // Establecer como predeterminada
            if ($addressModel->setAsDefault($addressId, $userId)) {
                return redirect()->to('/cuenta/direcciones')->with('success', 'Dirección establecida como predeterminada');
            } else {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Error al establecer dirección predeterminada');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al establecer dirección predeterminada: ' . $e->getMessage());
            return redirect()->to('/cuenta/direcciones')->with('error', 'Error al establecer dirección predeterminada');
        }
    }

    /**
     * Eliminar dirección
     */
    public function deleteAddress()
    {
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->to('/cuenta/direcciones');
        }

        $addressId = $this->request->getPost('address_id');
        $userId = session()->get('user_id');

        if (!$userId || !$addressId) {
            return redirect()->to('/cuenta/direcciones')->with('error', 'Datos inválidos');
        }

        try {
            $addressModel = new UserAddressModel();

            // Verificar que la dirección pertenece al usuario
            $address = $addressModel->where('id', $addressId)
                                  ->where('user_id', $userId)
                                  ->first();

            if (!$address) {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Dirección no encontrada');
            }

            // No permitir eliminar la dirección predeterminada si es la única
            $userAddresses = $addressModel->getUserAddresses($userId);
            if (count($userAddresses) == 1) {
                return redirect()->to('/cuenta/direcciones')->with('error', 'No puedes eliminar tu única dirección');
            }

            // Eliminar dirección
            if ($addressModel->delete($addressId)) {
                return redirect()->to('/cuenta/direcciones')->with('success', 'Dirección eliminada correctamente');
            } else {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Error al eliminar la dirección');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al eliminar dirección: ' . $e->getMessage());
            return redirect()->to('/cuenta/direcciones')->with('error', 'Error al eliminar la dirección');
        }
    }

    /**
     * Mostrar formulario de edición de dirección
     */
    public function editAddress($addressId = null)
    {
        $userId = session()->get('user_id');

        if (!$userId || !$addressId) {
            return redirect()->to('/cuenta/direcciones')->with('error', 'Datos inválidos - User: ' . $userId . ', Address: ' . $addressId);
        }

        try {
            $addressModel = new UserAddressModel();
            $db = \Config\Database::connect();

            // Obtener datos del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!$user) {
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            // Debug: Verificar todas las direcciones del usuario
            $allAddresses = $addressModel->where('user_id', $userId)->findAll();
            log_message('info', 'Direcciones del usuario ' . $userId . ': ' . json_encode($allAddresses));

            // Verificar que la dirección pertenece al usuario
            $address = $addressModel->where('id', $addressId)
                                  ->where('user_id', $userId)
                                  ->first();

            if (!$address) {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Dirección no encontrada - ID: ' . $addressId . ', User: ' . $userId);
            }

            // Obtener ubicaciones de Guatemala
            $locationsData = $addressModel->getGuatemalaLocations();

            // Extraer solo los departamentos para el select
            $departments = array_keys($locationsData);

            $data = [
                'title' => 'Editar Dirección',
                'user' => $user,
                'address' => $address,
                'departments' => $departments,
                'locations' => $locationsData
            ];

            return view('frontend/user/edit_address', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar dirección para editar: ' . $e->getMessage());
            return redirect()->to('/cuenta/direcciones')->with('error', 'Error al cargar la dirección: ' . $e->getMessage());
        }
    }

    /**
     * Actualizar dirección editada
     */
    public function updateAddress($addressId = null)
    {
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->to('/cuenta/direcciones');
        }

        $userId = session()->get('user_id');

        if (!$userId || !$addressId) {
            return redirect()->to('/cuenta/direcciones')->with('error', 'Datos inválidos');
        }

        // Validación
        $validation = \Config\Services::validation();
        $validation->setRules([
            'address_name' => 'required|min_length[2]|max_length[100]',
            'full_name' => 'required|min_length[2]|max_length[100]',
            'phone' => 'required|min_length[8]|max_length[20]',
            'department' => 'required|max_length[50]',
            'municipality' => 'required|max_length[50]',
            'postal_code' => 'required|max_length[10]',
            'address_line' => 'required|min_length[10]|max_length[255]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        try {
            $addressModel = new UserAddressModel();

            // Verificar que la dirección pertenece al usuario
            $existingAddress = $addressModel->where('id', $addressId)
                                           ->where('user_id', $userId)
                                           ->first();

            if (!$existingAddress) {
                return redirect()->to('/cuenta/direcciones')->with('error', 'Dirección no encontrada');
            }

            // Preparar datos para actualizar
            $addressData = [
                'address_name' => $this->request->getPost('address_name'),
                'nombre_completo' => $this->request->getPost('full_name'),
                'telefono' => $this->request->getPost('phone'),
                'estado_departamento' => $this->request->getPost('department'),
                'ciudad' => $this->request->getPost('municipality'),
                'codigo_postal' => $this->request->getPost('postal_code'),
                'direccion_linea_1' => $this->request->getPost('address_line'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validar que address_name no esté vacío
            if (empty($addressData['address_name'])) {
                return redirect()->back()->withInput()->with('error', 'El nombre de la dirección es requerido');
            }

            // Manejar dirección predeterminada
            $setAsDefault = $this->request->getPost('is_default');
            if ($setAsDefault) {
                $addressModel->setAsDefault($addressId, $userId);
            }

            // Actualizar dirección
            if ($addressModel->update($addressId, $addressData)) {
                return redirect()->to('/cuenta/direcciones')->with('success', 'Dirección actualizada correctamente');
            } else {
                return redirect()->back()->withInput()->with('error', 'Error al actualizar la dirección');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al actualizar dirección: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al actualizar la dirección');
        }
    }

    /**
     * Perfil del usuario
     */
    public function profile()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();

            if (!$user) {
                $this->session->destroy();
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            $data = [
                'title' => 'Mi Perfil - MrCell Guatemala',
                'page_title' => 'Mi Perfil',
                'user' => $user
            ];

            return view('frontend/user/profile', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar perfil: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar el perfil');
        }
    }

    /**
     * Actualizar perfil
     */
    public function updateProfile()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            $data = [
                'first_name' => $this->request->getPost('first_name'),
                'last_name' => $this->request->getPost('last_name'),
                'email' => $this->request->getPost('email'),
                'phone' => $this->request->getPost('phone'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->table('users')->where('id', $userId)->update($data);

            return redirect()->to('/cuenta/perfil')->with('success', 'Perfil actualizado correctamente');

        } catch (\Exception $e) {
            log_message('error', 'Error al actualizar perfil: ' . $e->getMessage());
            return redirect()->to('/cuenta/perfil')->with('error', 'Error al actualizar el perfil');
        }
    }

    /**
     * Configuración de seguridad
     */
    public function security()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            // Obtener datos del usuario
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!$user) {
                return redirect()->to('/login')->with('error', 'Usuario no encontrado');
            }

            $data = [
                'title' => 'Seguridad - MrCell Guatemala',
                'page_title' => 'Configuración de Seguridad',
                'user' => $user
            ];

            return view('frontend/user/security', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar configuración de seguridad: ' . $e->getMessage());
            return redirect()->to('/cuenta')->with('error', 'Error al cargar la configuración de seguridad');
        }
    }

    /**
     * Cambiar contraseña
     */
    public function changePassword()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();

        try {
            $currentPassword = $this->request->getPost('current_password');
            $newPassword = $this->request->getPost('new_password');
            $confirmPassword = $this->request->getPost('confirm_password');

            // Validaciones
            if ($newPassword !== $confirmPassword) {
                return redirect()->to('/cuenta/seguridad')->with('error', 'Las contraseñas no coinciden');
            }

            // Verificar contraseña actual
            $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
            if (!password_verify($currentPassword, $user['password'])) {
                return redirect()->to('/cuenta/seguridad')->with('error', 'La contraseña actual es incorrecta');
            }

            // Actualizar contraseña
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $db->table('users')->where('id', $userId)->update([
                'password' => $hashedPassword,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return redirect()->to('/cuenta/seguridad')->with('success', 'Contraseña actualizada correctamente');

        } catch (\Exception $e) {
            log_message('error', 'Error al cambiar contraseña: ' . $e->getMessage());
            return redirect()->to('/cuenta/seguridad')->with('error', 'Error al cambiar la contraseña');
        }
    }
}
