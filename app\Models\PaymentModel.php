<?php

namespace App\Models;

use CodeIgniter\Model;

class PaymentModel extends Model
{
    protected $table            = 'payments';
    protected $primaryKey        = 'id';
    protected $useAutoIncrement  = true;
    protected $returnType        = 'array';
    protected $useSoftDeletes    = false;
    protected $protectFields     = true;
    protected $allowedFields     = [
        'order_id',
        'payment_method',
        'amount',
        'currency',
        'status',
        'reference_number',
        'transaction_id',
        'gateway_response',
        'bank_account',
        'card_last_four',
        'card_type',
        'payment_date',
        'verified_at',
        'verified_by',
        'notes',
        'metadata'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'order_id' => 'required|integer',
        'payment_method' => 'required|in_list[transfer,paypal,card,cash,recurrente]',
        'amount' => 'required|decimal|greater_than[0]',
        'currency' => 'required|in_list[GTQ,USD]',
        'status' => 'required|in_list[pending,pending_verification,completed,failed,refunded,cancelled]'
    ];

    protected $validationMessages = [
        'order_id' => [
            'required' => 'El ID del pedido es requerido',
            'integer' => 'El ID del pedido debe ser un número entero'
        ],
        'payment_method' => [
            'required' => 'El método de pago es requerido',
            'in_list' => 'Método de pago no válido'
        ],
        'amount' => [
            'required' => 'El monto es requerido',
            'decimal' => 'El monto debe ser un número decimal',
            'greater_than' => 'El monto debe ser mayor a 0'
        ],
        'currency' => [
            'required' => 'La moneda es requerida',
            'in_list' => 'Moneda no válida'
        ],
        'status' => [
            'required' => 'El estado del pago es requerido',
            'in_list' => 'Estado de pago no válido'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Obtener pagos por pedido
     */
    public function getPaymentsByOrder($orderId)
    {
        return $this->where('order_id', $orderId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Obtener último pago de un pedido
     */
    public function getLastPaymentByOrder($orderId)
    {
        return $this->where('order_id', $orderId)
                   ->orderBy('created_at', 'DESC')
                   ->first();
    }

    /**
     * Obtener pagos pendientes de verificación
     */
    public function getPendingVerificationPayments()
    {
        return $this->select('payments.*, orders.order_number, orders.customer_name, orders.customer_email')
                   ->join('orders', 'orders.id = payments.order_id')
                   ->where('payments.status', 'pending_verification')
                   ->orderBy('payments.created_at', 'ASC')
                   ->findAll();
    }

    /**
     * Verificar pago
     */
    public function verifyPayment($paymentId, $verifiedBy, $notes = null)
    {
        $updateData = [
            'status' => 'completed',
            'verified_at' => date('Y-m-d H:i:s'),
            'verified_by' => $verifiedBy
        ];

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        return $this->update($paymentId, $updateData);
    }

    /**
     * Rechazar pago
     */
    public function rejectPayment($paymentId, $verifiedBy, $reason)
    {
        return $this->update($paymentId, [
            'status' => 'failed',
            'verified_at' => date('Y-m-d H:i:s'),
            'verified_by' => $verifiedBy,
            'notes' => 'Pago rechazado: ' . $reason
        ]);
    }

    /**
     * Obtener estadísticas de pagos
     */
    public function getPaymentStats($startDate = null, $endDate = null)
    {
        $builder = $this->builder();

        if ($startDate) {
            $builder->where('created_at >=', $startDate);
        }

        if ($endDate) {
            $builder->where('created_at <=', $endDate);
        }

        $stats = [
            'total_payments' => $builder->countAllResults(false),
            'total_amount' => $builder->selectSum('amount')->get()->getRow()->amount ?? 0,
            'completed_payments' => $builder->where('status', 'completed')->countAllResults(false),
            'pending_payments' => $builder->where('status', 'pending')->countAllResults(false),
            'pending_verification' => $builder->where('status', 'pending_verification')->countAllResults(false),
            'failed_payments' => $builder->where('status', 'failed')->countAllResults(false)
        ];

        // Estadísticas por método de pago
        $builder = $this->builder();
        if ($startDate) $builder->where('created_at >=', $startDate);
        if ($endDate) $builder->where('created_at <=', $endDate);

        $paymentMethods = $builder->select('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
                                ->where('status', 'completed')
                                ->groupBy('payment_method')
                                ->get()
                                ->getResultArray();

        $stats['by_method'] = [];
        foreach ($paymentMethods as $method) {
            $stats['by_method'][$method['payment_method']] = [
                'count' => $method['count'],
                'total_amount' => $method['total_amount']
            ];
        }

        return $stats;
    }

    /**
     * Obtener pagos por estado
     */
    public function getPaymentsByStatus($status, $limit = null)
    {
        $builder = $this->select('payments.*, orders.order_number, orders.customer_name, orders.customer_email')
                       ->join('orders', 'orders.id = payments.order_id')
                       ->where('payments.status', $status)
                       ->orderBy('payments.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Buscar pagos
     */
    public function searchPayments($search, $status = null, $method = null, $startDate = null, $endDate = null)
    {
        $builder = $this->select('payments.*, orders.order_number, orders.customer_name, orders.customer_email')
                       ->join('orders', 'orders.id = payments.order_id');

        if ($search) {
            $builder->groupStart()
                   ->like('orders.order_number', $search)
                   ->orLike('orders.customer_name', $search)
                   ->orLike('orders.customer_email', $search)
                   ->orLike('payments.reference_number', $search)
                   ->orLike('payments.transaction_id', $search)
                   ->groupEnd();
        }

        if ($status) {
            $builder->where('payments.status', $status);
        }

        if ($method) {
            $builder->where('payments.payment_method', $method);
        }

        if ($startDate) {
            $builder->where('payments.created_at >=', $startDate);
        }

        if ($endDate) {
            $builder->where('payments.created_at <=', $endDate);
        }

        return $builder->orderBy('payments.created_at', 'DESC')->findAll();
    }

    /**
     * Obtener total de ingresos por período
     */
    public function getRevenueByPeriod($period = 'month', $year = null)
    {
        $year = $year ?? date('Y');
        
        $builder = $this->builder();
        
        switch ($period) {
            case 'month':
                $select = "MONTH(created_at) as period, SUM(amount) as total";
                $groupBy = "MONTH(created_at)";
                break;
            case 'week':
                $select = "WEEK(created_at) as period, SUM(amount) as total";
                $groupBy = "WEEK(created_at)";
                break;
            case 'day':
                $select = "DAY(created_at) as period, SUM(amount) as total";
                $groupBy = "DAY(created_at)";
                break;
            default:
                $select = "MONTH(created_at) as period, SUM(amount) as total";
                $groupBy = "MONTH(created_at)";
        }

        return $builder->select($select)
                      ->where('status', 'completed')
                      ->where('YEAR(created_at)', $year)
                      ->groupBy($groupBy)
                      ->orderBy('period', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Obtener métodos de pago más utilizados
     */
    public function getTopPaymentMethods($limit = 5)
    {
        return $this->select('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
                   ->where('status', 'completed')
                   ->groupBy('payment_method')
                   ->orderBy('count', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
}
