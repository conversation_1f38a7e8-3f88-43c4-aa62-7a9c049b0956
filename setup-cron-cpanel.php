<?php
/**
 * Script de configuración automática para el cron de alertas en cPanel
 * 
 * Este script:
 * 1. Configura la base de datos
 * 2. Crea los directorios necesarios
 * 3. Configura el número de grupo de WhatsApp
 * 4. Proporciona instrucciones para cPanel
 */

echo "🚀 CONFIGURACIÓN AUTOMÁTICA DEL CRON DE ALERTAS\n";
echo "===============================================\n\n";

try {
    // Detectar si estamos en /public o en la raíz
    $isInPublic = basename(__DIR__) === 'public';
    $projectRoot = $isInPublic ? dirname(__DIR__) : __DIR__;

    echo "📁 Directorio actual: " . __DIR__ . "\n";
    echo "📁 Raíz del proyecto: $projectRoot\n";
    echo "📁 Estructura detectada: " . ($isInPublic ? "/public" : "raíz") . "\n\n";

    // Cargar CodeIgniter desde la ruta correcta
    $autoloadPath = $projectRoot . '/vendor/autoload.php';

    if (!file_exists($autoloadPath)) {
        throw new Exception("Autoloader no encontrado en: $autoloadPath");
    }

    require_once $autoloadPath;

    // Configurar rutas de CodeIgniter
    $paths = new Config\Paths();
    $paths->systemDirectory = $projectRoot . '/system';
    $paths->appDirectory = $projectRoot . '/app';
    $paths->writableDirectory = $projectRoot . '/writable';
    $paths->testsDirectory = $projectRoot . '/tests';
    $paths->viewDirectory = $projectRoot . '/app/Views';

    $bootstrap = \CodeIgniter\Boot::bootWeb($paths);
    $app = $bootstrap->getApp();

    $db = \Config\Database::connect();

    echo "✅ CodeIgniter y base de datos inicializados\n\n";
    
    // 1. Crear directorios necesarios
    echo "📁 CREANDO DIRECTORIOS NECESARIOS:\n";
    
    $directories = [
        'writable/logs',
        'writable/cache',
        'writable/session',
        'writable/uploads'
    ];

    foreach ($directories as $dir) {
        $fullPath = $projectRoot . '/' . $dir;
        if (!is_dir($fullPath)) {
            if (mkdir($fullPath, 0755, true)) {
                echo "  ✅ Creado: $dir\n";
            } else {
                echo "  ❌ Error creando: $dir\n";
            }
        } else {
            echo "  ✅ Ya existe: $dir\n";
        }
    }
    
    echo "\n";
    
    // 2. Crear tabla de ejecuciones de cron
    echo "🗄️ CONFIGURANDO TABLA DE CRON:\n";
    
    $db->query("
        CREATE TABLE IF NOT EXISTS cron_executions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_name VARCHAR(100) NOT NULL,
            status ENUM('success', 'error') DEFAULT 'success',
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_task_name (task_name),
            INDEX idx_created_at (created_at)
        )
    ");
    
    echo "  ✅ Tabla cron_executions configurada\n\n";
    
    // 3. Configurar número de grupo de WhatsApp
    echo "📱 CONFIGURANDO WHATSAPP:\n";
    
    // Verificar si existe la configuración
    $existingConfig = $db->query("
        SELECT setting_value FROM system_settings 
        WHERE setting_key = 'whatsapp_alerts_group' 
        LIMIT 1
    ")->getRowArray();
    
    if (!$existingConfig) {
        // Insertar configuración
        $db->query("
            INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
            VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
        ");
        echo "  ✅ Número de grupo configurado: 120363416393766854\n";
    } else {
        echo "  ✅ Número de grupo ya configurado: {$existingConfig['setting_value']}\n";
    }
    
    echo "\n";
    
    // 4. Verificar servicios necesarios
    echo "🔧 VERIFICANDO SERVICIOS:\n";
    
    if (class_exists('\\App\\Services\\WhatsAppService')) {
        echo "  ✅ Servicio WhatsApp disponible\n";
    } else {
        echo "  ⚠️ Servicio WhatsApp no encontrado\n";
    }
    
    if (class_exists('\\App\\Libraries\\CronService')) {
        echo "  ✅ CronService disponible\n";
    } else {
        echo "  ⚠️ CronService no encontrado\n";
    }
    
    echo "\n";
    
    // 5. Crear archivo de configuración de cron
    echo "📝 CREANDO ARCHIVO DE CONFIGURACIÓN:\n";

    $cronConfig = [
        'project_path' => $projectRoot,
        'public_path' => $isInPublic ? __DIR__ : $projectRoot . '/public',
        'php_path' => '/opt/cpanel/ea-php82/root/usr/bin/php',
        'cron_script' => ($isInPublic ? __DIR__ : $projectRoot . '/public') . '/cron-alerts.php',
        'log_file' => $projectRoot . '/writable/logs/cron-alerts.log',
        'whatsapp_group' => $existingConfig['setting_value'] ?? '120363416393766854',
        'configured_at' => date('Y-m-d H:i:s'),
        'structure' => $isInPublic ? 'public' : 'root'
    ];
    
    $configFile = $projectRoot . '/cron-config.json';
    file_put_contents($configFile, json_encode($cronConfig, JSON_PRETTY_PRINT));
    echo "  ✅ Configuración guardada en: cron-config.json\n\n";

    // 6. Probar el script de cron
    echo "🧪 PROBANDO SCRIPT DE CRON:\n";

    if (file_exists($cronConfig['cron_script'])) {
        echo "  ✅ Script cron-alerts.php encontrado en /public\n";

        // Probar que el script se puede cargar sin errores
        $output = [];
        $returnCode = 0;

        $command = "/opt/cpanel/ea-php82/root/usr/bin/php -l " . $cronConfig['cron_script'];
        exec($command, $output, $returnCode);

        if ($returnCode === 0) {
            echo "  ✅ Script tiene sintaxis válida\n";
        } else {
            echo "  ⚠️ Posibles errores de sintaxis en el script\n";
        }
    } else {
        echo "  ❌ Script cron-alerts.php no encontrado en /public\n";
    }

    echo "\n";

    // 7. Mostrar instrucciones finales
    echo "🎯 CONFIGURACIÓN COMPLETADA\n";
    echo "============================\n\n";

    echo "📋 INSTRUCCIONES PARA CPANEL:\n\n";

    echo "1. Accede a tu cPanel\n";
    echo "2. Ve a la sección 'Avanzado' > 'Cron Jobs'\n";
    echo "3. Configura un nuevo cron job con:\n\n";

    echo "   📅 PROGRAMACIÓN (cada 12 horas):\n";
    echo "   - Minuto: 0\n";
    echo "   - Hora: 8,20\n";
    echo "   - Día: *\n";
    echo "   - Mes: *\n";
    echo "   - Día de la semana: *\n\n";

    echo "   💻 COMANDO:\n";
    echo "   {$cronConfig['php_path']} {$cronConfig['cron_script']}\n\n";

    echo "4. Guarda la configuración\n\n";

    echo "📊 INFORMACIÓN DE LA CONFIGURACIÓN:\n";
    echo "- Estructura detectada: {$cronConfig['structure']}\n";
    echo "- Ruta del proyecto: {$cronConfig['project_path']}\n";
    echo "- Script de cron: {$cronConfig['cron_script']}\n";
    echo "- Archivo de logs: {$cronConfig['log_file']}\n";
    echo "- Grupo de WhatsApp: {$cronConfig['whatsapp_group']}\n";
    echo "- Configurado el: {$cronConfig['configured_at']}\n\n";

    echo "🔧 COMANDOS DE PRUEBA:\n\n";
    echo "Para probar el entorno:\n";
    echo "{$cronConfig['php_path']} " . ($isInPublic ? __DIR__ : $projectRoot . '/public') . "/test-cron-environment.php\n\n";

    echo "Para ejecutar el cron manualmente:\n";
    echo "{$cronConfig['php_path']} {$cronConfig['cron_script']}\n\n";

    echo "📱 CONFIGURACIÓN DE WHATSAPP:\n";
    echo "- El número de grupo se puede cambiar desde: /admin/settings?tab=notifications\n";
    echo "- Número actual: {$cronConfig['whatsapp_group']}\n\n";

    echo "📝 LOGS Y MONITOREO:\n";
    echo "- Los logs se guardan en: {$cronConfig['log_file']}\n";
    echo "- Las ejecuciones se registran en la tabla: cron_executions\n\n";

    echo "🎉 ¡CONFIGURACIÓN LISTA!\n";
    echo "Ahora solo necesitas configurar el cron job en cPanel.\n\n";
    
    // 6. Probar el script de cron
    echo "🧪 PROBANDO SCRIPT DE CRON:\n";
    
    if (file_exists(__DIR__ . '/cron-alerts.php')) {
        echo "  ✅ Script cron-alerts.php encontrado\n";
        
        // Probar que el script se puede cargar sin errores
        $output = [];
        $returnCode = 0;
        
        $command = "/opt/cpanel/ea-php82/root/usr/bin/php -l " . __DIR__ . "/cron-alerts.php";
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ Script tiene sintaxis válida\n";
        } else {
            echo "  ⚠️ Posibles errores de sintaxis en el script\n";
        }
    } else {
        echo "  ❌ Script cron-alerts.php no encontrado\n";
    }
    
    echo "\n";
    
    // 7. Mostrar instrucciones finales
    echo "🎯 CONFIGURACIÓN COMPLETADA\n";
    echo "============================\n\n";
    
    echo "📋 INSTRUCCIONES PARA CPANEL:\n\n";
    
    echo "1. Accede a tu cPanel\n";
    echo "2. Ve a la sección 'Avanzado' > 'Cron Jobs'\n";
    echo "3. Configura un nuevo cron job con:\n\n";
    
    echo "   📅 PROGRAMACIÓN (cada 12 horas):\n";
    echo "   - Minuto: 0\n";
    echo "   - Hora: 8,20\n";
    echo "   - Día: *\n";
    echo "   - Mes: *\n";
    echo "   - Día de la semana: *\n\n";
    
    echo "   💻 COMANDO:\n";
    echo "   /opt/cpanel/ea-php82/root/usr/bin/php {$cronConfig['cron_script']}\n\n";
    
    echo "4. Guarda la configuración\n\n";
    
    echo "📊 INFORMACIÓN DE LA CONFIGURACIÓN:\n";
    echo "- Ruta del proyecto: {$cronConfig['project_path']}\n";
    echo "- Script de cron: {$cronConfig['cron_script']}\n";
    echo "- Archivo de logs: {$cronConfig['log_file']}\n";
    echo "- Grupo de WhatsApp: {$cronConfig['whatsapp_group']}\n";
    echo "- Configurado el: {$cronConfig['configured_at']}\n\n";
    
    echo "🔧 COMANDOS DE PRUEBA:\n\n";
    echo "Para probar el entorno:\n";
    echo "/opt/cpanel/ea-php82/root/usr/bin/php " . __DIR__ . "/test-cron-environment.php\n\n";
    
    echo "Para ejecutar el cron manualmente:\n";
    echo "/opt/cpanel/ea-php82/root/usr/bin/php " . __DIR__ . "/cron-alerts.php\n\n";
    
    echo "📱 CONFIGURACIÓN DE WHATSAPP:\n";
    echo "- El número de grupo se puede cambiar desde: /admin/settings?tab=notifications\n";
    echo "- Número actual: {$cronConfig['whatsapp_group']}\n\n";
    
    echo "📝 LOGS Y MONITOREO:\n";
    echo "- Los logs se guardan en: {$cronConfig['log_file']}\n";
    echo "- Las ejecuciones se registran en la tabla: cron_executions\n\n";
    
    echo "🎉 ¡CONFIGURACIÓN LISTA!\n";
    echo "Ahora solo necesitas configurar el cron job en cPanel.\n\n";
    
} catch (Exception $e) {
    echo "❌ ERROR DURANTE LA CONFIGURACIÓN:\n";
    echo "   " . $e->getMessage() . "\n";
    echo "   Archivo: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
    
    echo "💡 POSIBLES SOLUCIONES:\n";
    echo "1. Verifica que CodeIgniter esté correctamente instalado\n";
    echo "2. Asegúrate de que la base de datos esté configurada\n";
    echo "3. Verifica los permisos de escritura en la carpeta writable/\n";
    echo "4. Contacta al soporte de tu hosting si persisten los problemas\n";
}

echo "\n📅 Configuración completada: " . date('Y-m-d H:i:s') . "\n";
?>
