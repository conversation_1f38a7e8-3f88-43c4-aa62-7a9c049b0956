<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 text-primary mb-3">
            <i class="fas fa-ticket-alt me-3"></i>Cupones Disponibles
        </h1>
        <p class="lead text-muted">Aprovecha nuestras ofertas especiales y ahorra en tu próxima compra</p>
    </div>

    <!-- Cupones Públicos -->
    <?php if (!empty($public_coupons)): ?>
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="h3 mb-4">
                    <i class="fas fa-star text-warning me-2"></i>Ofertas Especiales
                </h2>
            </div>
            
            <?php foreach ($public_coupons as $coupon): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card coupon-card h-100 shadow-sm border-0">
                        <div class="card-header bg-gradient-primary text-white text-center">
                            <h5 class="card-title mb-0">
                                <?= esc($coupon['name']) ?>
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="coupon-code mb-3">
                                <span class="badge bg-dark fs-5 px-3 py-2 font-monospace">
                                    <?= esc($coupon['code']) ?>
                                </span>
                            </div>
                            
                            <div class="coupon-value mb-3">
                                <?php if ($coupon['type'] === 'percentage'): ?>
                                    <div class="display-6 text-success fw-bold">
                                        <?= $coupon['value'] ?>% OFF
                                    </div>
                                <?php elseif ($coupon['type'] === 'fixed'): ?>
                                    <div class="display-6 text-success fw-bold">
                                        Q<?= number_format($coupon['value'], 2) ?> OFF
                                    </div>
                                <?php elseif ($coupon['type'] === 'free_shipping'): ?>
                                    <div class="display-6 text-info fw-bold">
                                        <i class="fas fa-truck"></i> ENVÍO GRATIS
                                    </div>
                                <?php else: ?>
                                    <div class="display-6 text-warning fw-bold">
                                        <i class="fas fa-gift"></i> OFERTA ESPECIAL
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if (!empty($coupon['description'])): ?>
                                <p class="text-muted small mb-3">
                                    <?= esc($coupon['description']) ?>
                                </p>
                            <?php endif; ?>

                            <?php if ($coupon['min_order_amount'] > 0): ?>
                                <div class="alert alert-info small mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Compra mínima: Q<?= number_format($coupon['min_order_amount'], 2) ?>
                                </div>
                            <?php endif; ?>

                            <button class="btn btn-primary btn-sm" onclick="copyCouponCode('<?= esc($coupon['code']) ?>')">
                                <i class="fas fa-copy me-2"></i>Copiar Código
                            </button>
                        </div>
                        <div class="card-footer bg-light text-center">
                            <?php if ($coupon['valid_until']): ?>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Válido hasta: <?= date('d/m/Y', strtotime($coupon['valid_until'])) ?>
                                </small>
                            <?php else: ?>
                                <small class="text-success">
                                    <i class="fas fa-infinity me-1"></i>
                                    Sin fecha de vencimiento
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Cupones de Usuario -->
    <?php if (!empty($user_coupons) && session('user_id')): ?>
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="h3 mb-4">
                    <i class="fas fa-user text-primary me-2"></i>Tus Cupones Personalizados
                </h2>
            </div>
            
            <?php foreach ($user_coupons as $coupon): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card coupon-card h-100 shadow-sm border-warning">
                        <div class="card-header bg-gradient-warning text-dark text-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-crown me-2"></i><?= esc($coupon['name']) ?>
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="coupon-code mb-3">
                                <span class="badge bg-warning text-dark fs-5 px-3 py-2 font-monospace">
                                    <?= esc($coupon['code']) ?>
                                </span>
                            </div>
                            
                            <div class="coupon-value mb-3">
                                <?php if ($coupon['type'] === 'percentage'): ?>
                                    <div class="display-6 text-warning fw-bold">
                                        <?= $coupon['value'] ?>% OFF
                                    </div>
                                <?php elseif ($coupon['type'] === 'fixed'): ?>
                                    <div class="display-6 text-warning fw-bold">
                                        Q<?= number_format($coupon['value'], 2) ?> OFF
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    Usos restantes: 
                                    <strong class="text-primary">
                                        <?= ($coupon['usage_limit_per_user'] - ($coupon['user_uses'] ?? 0)) ?>
                                    </strong>
                                </small>
                            </div>

                            <button class="btn btn-warning btn-sm" onclick="copyCouponCode('<?= esc($coupon['code']) ?>')">
                                <i class="fas fa-copy me-2"></i>Copiar Código
                            </button>
                        </div>
                        <div class="card-footer bg-light text-center">
                            <?php if ($coupon['valid_until']): ?>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Válido hasta: <?= date('d/m/Y', strtotime($coupon['valid_until'])) ?>
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Promociones Estacionales -->
    <?php if (!empty($seasonal_promotions)): ?>
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="h3 mb-4">
                    <i class="fas fa-calendar-alt text-success me-2"></i>Promociones de Temporada
                </h2>
            </div>
            
            <?php foreach ($seasonal_promotions as $promotion): ?>
                <div class="col-lg-6 mb-4">
                    <div class="card promotion-card h-100 shadow border-success">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="card-title text-success">
                                        <i class="fas fa-leaf me-2"></i><?= esc($promotion['name']) ?>
                                    </h5>
                                    <p class="card-text"><?= esc($promotion['description']) ?></p>
                                    
                                    <div class="coupon-code mb-2">
                                        <span class="badge bg-success fs-6 px-3 py-2 font-monospace">
                                            <?= esc($promotion['code']) ?>
                                        </span>
                                    </div>
                                    
                                    <button class="btn btn-success btn-sm" onclick="copyCouponCode('<?= esc($promotion['code']) ?>')">
                                        <i class="fas fa-copy me-2"></i>Usar Código
                                    </button>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="promotion-value">
                                        <?php if ($promotion['type'] === 'percentage'): ?>
                                            <div class="display-5 text-success fw-bold">
                                                <?= $promotion['value'] ?>%
                                            </div>
                                            <small class="text-muted">DESCUENTO</small>
                                        <?php elseif ($promotion['type'] === 'fixed'): ?>
                                            <div class="display-5 text-success fw-bold">
                                                Q<?= number_format($promotion['value'], 2) ?>
                                            </div>
                                            <small class="text-muted">DESCUENTO</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        Desde: <?= date('d/m/Y', strtotime($promotion['valid_from'])) ?>
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-times me-1"></i>
                                        Hasta: <?= date('d/m/Y', strtotime($promotion['valid_until'])) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Sin cupones disponibles -->
    <?php if (empty($public_coupons) && empty($user_coupons) && empty($seasonal_promotions)): ?>
        <div class="text-center py-5">
            <i class="fas fa-ticket-alt fa-4x text-muted mb-4"></i>
            <h3 class="text-muted">No hay cupones disponibles en este momento</h3>
            <p class="text-muted mb-4">¡Mantente atento! Pronto tendremos nuevas ofertas para ti.</p>
            <a href="<?= base_url('shop') ?>" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Continuar Comprando
            </a>
        </div>
    <?php endif; ?>

    <!-- Suscripción a Newsletter -->
    <div class="row mt-5">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body text-center py-5">
                    <h3 class="card-title">
                        <i class="fas fa-envelope me-2"></i>¡No te pierdas nuestras ofertas!
                    </h3>
                    <p class="card-text mb-4">
                        Suscríbete a nuestro newsletter y recibe cupones exclusivos directamente en tu email.
                    </p>
                    
                    <form id="newsletterForm" class="row g-3 justify-content-center">
                        <div class="col-md-6">
                            <input type="email" class="form-control" id="newsletter_email" 
                                   placeholder="Tu email" required>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-light w-100">
                                <i class="fas fa-paper-plane me-2"></i>Suscribirse
                            </button>
                        </div>
                    </form>
                    
                    <small class="d-block mt-3 opacity-75">
                        <i class="fas fa-gift me-1"></i>
                        Recibe un cupón de bienvenida del 10% al suscribirte
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.coupon-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.coupon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.promotion-card {
    transition: transform 0.3s ease;
}

.promotion-card:hover {
    transform: translateY(-3px);
}

.coupon-code .badge {
    letter-spacing: 2px;
    font-size: 1rem !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.promotion-value {
    padding: 1rem;
    border-radius: 50%;
    background: rgba(40, 167, 69, 0.1);
    display: inline-block;
    min-width: 120px;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Copiar código de cupón
function copyCouponCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // Mostrar notificación de éxito
        showNotification('¡Código copiado!', `El código "${code}" ha sido copiado al portapapeles`, 'success');
    }).catch(function(err) {
        // Fallback para navegadores que no soportan clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        showNotification('¡Código copiado!', `El código "${code}" ha sido copiado`, 'success');
    });
}

// Mostrar notificación
function showNotification(title, message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        <strong>${title}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remover después de 3 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Suscripción a newsletter
document.getElementById('newsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('newsletter_email').value;
    
    if (!email) {
        showNotification('Error', 'Por favor ingresa tu email', 'danger');
        return;
    }
    
    // Simular suscripción (aquí iría la llamada AJAX real)
    fetch('<?= base_url('coupons/subscribe-newsletter') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('¡Suscripción exitosa!', 'Revisa tu email para el cupón de bienvenida', 'success');
            document.getElementById('newsletter_email').value = '';
        } else {
            showNotification('Error', data.error || 'Error al suscribirse', 'danger');
        }
    })
    .catch(error => {
        showNotification('Error', 'Error de conexión', 'danger');
    });
});

// Aplicar cupón automáticamente si viene de un enlace
const urlParams = new URLSearchParams(window.location.search);
const autoCoupon = urlParams.get('coupon');
if (autoCoupon) {
    copyCouponCode(autoCoupon);
    showNotification('Cupón listo', `El código "${autoCoupon}" está listo para usar`, 'info');
}
</script>
<?= $this->endSection() ?>
