<?php

/**
 * Verificador de Requisitos del Sistema - MrCell Guatemala
 * Script para verificar que el servidor cumple con todos los requisitos
 */

// Configuración
$requirements = [
    'php_version' => '8.1.0',
    'mysql_version' => '8.0.0',
    'extensions' => ['mysqli', 'json', 'mbstring', 'openssl', 'zip', 'curl', 'gd', 'intl'],
    'functions' => ['exec', 'shell_exec', 'file_get_contents', 'curl_init'],
    'directories' => ['writable', 'writable/backups', 'writable/logs', 'writable/cache'],
    'files' => ['.env', 'app/Config/Routes.php', 'public/index.php'],
    'memory_limit' => '512M',
    'max_execution_time' => 300,
    'upload_max_filesize' => '50M',
    'post_max_size' => '50M'
];

// Colores para CLI
$colors = [
    'green' => "\033[32m",
    'red' => "\033[31m",
    'yellow' => "\033[33m",
    'blue' => "\033[34m",
    'reset' => "\033[0m"
];

// Función para mostrar mensajes
function printStatus($message, $status = 'info') {
    global $colors;
    
    $icon = [
        'success' => '✓',
        'error' => '✗',
        'warning' => '⚠',
        'info' => 'ℹ'
    ];
    
    $color = [
        'success' => $colors['green'],
        'error' => $colors['red'],
        'warning' => $colors['yellow'],
        'info' => $colors['blue']
    ];
    
    if (php_sapi_name() === 'cli') {
        echo $color[$status] . "[" . $icon[$status] . "] " . $message . $colors['reset'] . "\n";
    } else {
        $class = $status === 'success' ? 'text-success' : ($status === 'error' ? 'text-danger' : 'text-warning');
        echo "<div class='$class'>[" . $icon[$status] . "] " . htmlspecialchars($message) . "</div>";
    }
}

// Función para convertir tamaños
function convertSize($size) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = (int) $size;
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

// Función para convertir valores de PHP ini
function convertPhpSize($size) {
    $size = trim($size);
    $last = strtolower($size[strlen($size)-1]);
    $size = (int) $size;
    
    switch($last) {
        case 'g': $size *= 1024;
        case 'm': $size *= 1024;
        case 'k': $size *= 1024;
    }
    
    return $size;
}

// Verificar versión de PHP
function checkPhpVersion($required) {
    $current = PHP_VERSION;
    $status = version_compare($current, $required, '>=') ? 'success' : 'error';
    
    printStatus("PHP Version: $current (Required: $required+)", $status);
    
    return $status === 'success';
}

// Verificar extensiones PHP
function checkPhpExtensions($extensions) {
    $allGood = true;
    
    printStatus("Verificando extensiones PHP:", 'info');
    
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            printStatus("  Extension $ext: OK", 'success');
        } else {
            printStatus("  Extension $ext: MISSING", 'error');
            $allGood = false;
        }
    }
    
    return $allGood;
}

// Verificar funciones PHP
function checkPhpFunctions($functions) {
    $allGood = true;
    
    printStatus("Verificando funciones PHP:", 'info');
    
    foreach ($functions as $func) {
        if (function_exists($func)) {
            printStatus("  Function $func: OK", 'success');
        } else {
            printStatus("  Function $func: DISABLED", 'warning');
        }
    }
    
    return $allGood;
}

// Verificar configuración PHP
function checkPhpConfig($requirements) {
    printStatus("Verificando configuración PHP:", 'info');
    
    $configs = [
        'memory_limit' => $requirements['memory_limit'],
        'max_execution_time' => $requirements['max_execution_time'],
        'upload_max_filesize' => $requirements['upload_max_filesize'],
        'post_max_size' => $requirements['post_max_size']
    ];
    
    $allGood = true;
    
    foreach ($configs as $config => $required) {
        $current = ini_get($config);
        
        if (in_array($config, ['memory_limit', 'upload_max_filesize', 'post_max_size'])) {
            $currentBytes = convertPhpSize($current);
            $requiredBytes = convertPhpSize($required);
            $status = $currentBytes >= $requiredBytes ? 'success' : 'warning';
        } else {
            $status = (int)$current >= (int)$required ? 'success' : 'warning';
        }
        
        printStatus("  $config: $current (Required: $required)", $status);
        
        if ($status === 'warning') {
            $allGood = false;
        }
    }
    
    return $allGood;
}

// Verificar directorios
function checkDirectories($directories) {
    $allGood = true;
    
    printStatus("Verificando directorios:", 'info');
    
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            if (is_writable($dir)) {
                printStatus("  Directory $dir: OK (writable)", 'success');
            } else {
                printStatus("  Directory $dir: NOT WRITABLE", 'error');
                $allGood = false;
            }
        } else {
            printStatus("  Directory $dir: MISSING", 'error');
            $allGood = false;
        }
    }
    
    return $allGood;
}

// Verificar archivos
function checkFiles($files) {
    $allGood = true;
    
    printStatus("Verificando archivos críticos:", 'info');
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            if (is_readable($file)) {
                printStatus("  File $file: OK", 'success');
            } else {
                printStatus("  File $file: NOT READABLE", 'error');
                $allGood = false;
            }
        } else {
            printStatus("  File $file: MISSING", 'error');
            $allGood = false;
        }
    }
    
    return $allGood;
}

// Verificar base de datos
function checkDatabase() {
    if (!file_exists('.env')) {
        printStatus("Database: .env file not found", 'error');
        return false;
    }
    
    $env = file_get_contents('.env');
    preg_match('/database\.default\.hostname\s*=\s*(.+)/', $env, $host);
    preg_match('/database\.default\.database\s*=\s*(.+)/', $env, $database);
    preg_match('/database\.default\.username\s*=\s*(.+)/', $env, $username);
    preg_match('/database\.default\.password\s*=\s*(.+)/', $env, $password);
    
    if (empty($host[1]) || empty($database[1]) || empty($username[1])) {
        printStatus("Database: Configuration incomplete in .env", 'warning');
        return false;
    }
    
    try {
        $dsn = "mysql:host=" . trim($host[1]) . ";dbname=" . trim($database[1]);
        $pdo = new PDO($dsn, trim($username[1]), trim($password[1] ?? ''));
        
        // Contar tablas
        $stmt = $pdo->query("SHOW TABLES");
        $tableCount = $stmt->rowCount();
        
        printStatus("Database: Connected successfully ($tableCount tables)", 'success');
        
        if ($tableCount < 90) {
            printStatus("Database: Warning - Expected 90+ tables, found $tableCount", 'warning');
        }
        
        return true;
        
    } catch (PDOException $e) {
        printStatus("Database: Connection failed - " . $e->getMessage(), 'error');
        return false;
    }
}

// Verificar servidor web
function checkWebServer() {
    $server = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    printStatus("Web Server: $server", 'info');
    
    // Verificar mod_rewrite para Apache
    if (strpos($server, 'Apache') !== false) {
        if (function_exists('apache_get_modules')) {
            $modules = apache_get_modules();
            if (in_array('mod_rewrite', $modules)) {
                printStatus("  mod_rewrite: OK", 'success');
            } else {
                printStatus("  mod_rewrite: NOT LOADED", 'error');
                return false;
            }
        } else {
            printStatus("  mod_rewrite: Cannot verify", 'warning');
        }
    }
    
    return true;
}

// Verificar recursos del sistema
function checkSystemResources() {
    printStatus("Verificando recursos del sistema:", 'info');
    
    // Memoria disponible
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = convertPhpSize(ini_get('memory_limit'));
    $memoryPercent = ($memoryUsage / $memoryLimit) * 100;
    
    printStatus("  Memory Usage: " . convertSize($memoryUsage) . " / " . convertSize($memoryLimit) . " (" . round($memoryPercent, 1) . "%)", 
                $memoryPercent < 80 ? 'success' : 'warning');
    
    // Espacio en disco
    $diskFree = disk_free_space('.');
    $diskTotal = disk_total_space('.');
    $diskUsed = $diskTotal - $diskFree;
    $diskPercent = ($diskUsed / $diskTotal) * 100;
    
    printStatus("  Disk Usage: " . convertSize($diskUsed) . " / " . convertSize($diskTotal) . " (" . round($diskPercent, 1) . "%)", 
                $diskPercent < 85 ? 'success' : 'warning');
    
    return true;
}

// Verificar URLs críticas
function checkCriticalUrls() {
    if (php_sapi_name() === 'cli') {
        printStatus("URL Check: Skipped (CLI mode)", 'info');
        return true;
    }
    
    $baseUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
    $urls = [
        '/cron/status',
        '/test',
        '/admin/automation'
    ];
    
    printStatus("Verificando URLs críticas:", 'info');
    
    foreach ($urls as $url) {
        $fullUrl = $baseUrl . $url;
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($fullUrl, false, $context);
        
        if ($response !== false) {
            printStatus("  URL $url: OK", 'success');
        } else {
            printStatus("  URL $url: NOT ACCESSIBLE", 'warning');
        }
    }
    
    return true;
}

// Función principal
function runSystemCheck() {
    global $requirements;
    
    if (php_sapi_name() !== 'cli') {
        echo "<!DOCTYPE html><html><head><title>System Check - MrCell Guatemala</title>";
        echo "<style>body{font-family:monospace;margin:20px;} .text-success{color:green;} .text-danger{color:red;} .text-warning{color:orange;}</style>";
        echo "</head><body><h1>🔍 System Check - MrCell Guatemala</h1><pre>";
    }
    
    printStatus("🚀 VERIFICACIÓN DEL SISTEMA - MRCELL GUATEMALA", 'info');
    printStatus("=" . str_repeat("=", 50), 'info');
    
    $results = [];
    
    // Ejecutar verificaciones
    $results['php_version'] = checkPhpVersion($requirements['php_version']);
    $results['php_extensions'] = checkPhpExtensions($requirements['extensions']);
    $results['php_functions'] = checkPhpFunctions($requirements['functions']);
    $results['php_config'] = checkPhpConfig($requirements);
    $results['directories'] = checkDirectories($requirements['directories']);
    $results['files'] = checkFiles($requirements['files']);
    $results['database'] = checkDatabase();
    $results['webserver'] = checkWebServer();
    $results['resources'] = checkSystemResources();
    $results['urls'] = checkCriticalUrls();
    
    // Resumen final
    printStatus("", 'info');
    printStatus("📊 RESUMEN DE VERIFICACIÓN", 'info');
    printStatus("=" . str_repeat("=", 30), 'info');
    
    $passed = 0;
    $total = count($results);
    
    foreach ($results as $check => $result) {
        $status = $result ? 'success' : 'error';
        $statusText = $result ? 'PASSED' : 'FAILED';
        printStatus(ucfirst(str_replace('_', ' ', $check)) . ": $statusText", $status);
        
        if ($result) $passed++;
    }
    
    printStatus("", 'info');
    printStatus("Verificaciones pasadas: $passed/$total", $passed === $total ? 'success' : 'warning');
    
    if ($passed === $total) {
        printStatus("🎉 ¡Sistema listo para MrCell Guatemala!", 'success');
    } else {
        printStatus("⚠️  Algunos requisitos no se cumplen. Revisa los errores arriba.", 'warning');
    }
    
    if (php_sapi_name() !== 'cli') {
        echo "</pre></body></html>";
    }
    
    return $passed === $total;
}

// Ejecutar verificación
if (php_sapi_name() === 'cli') {
    // Modo CLI
    $result = runSystemCheck();
    exit($result ? 0 : 1);
} else {
    // Modo web
    runSystemCheck();
}
