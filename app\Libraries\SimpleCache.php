<?php

namespace App\Libraries;

/**
 * Sistema de Cache Simple para cPanel
 * Compatible con hosting compartido sin Redis
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SimpleCache
{
    private static $cacheDir;
    private static $defaultTTL = 3600; // 1 hora por defecto
    
    public function __construct()
    {
        self::$cacheDir = WRITEPATH . 'cache/simple/';
        
        // Crear directorio si no existe
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
    }
    
    /**
     * Obtener valor del cache
     * 
     * @param string $key Clave del cache
     * @return mixed|false Valor del cache o false si no existe/expiró
     */
    public static function get(string $key)
    {
        $filename = self::getFilename($key);
        
        if (!file_exists($filename)) {
            return false;
        }
        
        $data = file_get_contents($filename);
        $cache = unserialize($data);
        
        // Verificar si expiró
        if ($cache['expires'] < time()) {
            self::delete($key);
            return false;
        }
        
        return $cache['data'];
    }
    
    /**
     * Guardar valor en cache
     * 
     * @param string $key Clave del cache
     * @param mixed $data Datos a guardar
     * @param int $ttl Tiempo de vida en segundos
     * @return bool
     */
    public static function set(string $key, $data, int $ttl = null): bool
    {
        if ($ttl === null) {
            $ttl = self::$defaultTTL;
        }
        
        $cache = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $filename = self::getFilename($key);
        
        return file_put_contents($filename, serialize($cache)) !== false;
    }
    
    /**
     * Eliminar valor del cache
     * 
     * @param string $key Clave del cache
     * @return bool
     */
    public static function delete(string $key): bool
    {
        $filename = self::getFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Limpiar todo el cache
     * 
     * @return bool
     */
    public static function clear(): bool
    {
        $files = glob(self::$cacheDir . '*.cache');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        
        return true;
    }
    
    /**
     * Limpiar cache expirado
     * 
     * @return int Número de archivos eliminados
     */
    public static function clearExpired(): int
    {
        $files = glob(self::$cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $data = file_get_contents($file);
                $cache = unserialize($data);
                
                if ($cache['expires'] < time()) {
                    unlink($file);
                    $deleted++;
                }
            }
        }
        
        return $deleted;
    }
    
    /**
     * Obtener o crear cache (patrón remember)
     * 
     * @param string $key Clave del cache
     * @param callable $callback Función para generar datos si no existen
     * @param int $ttl Tiempo de vida en segundos
     * @return mixed
     */
    public static function remember(string $key, callable $callback, int $ttl = null)
    {
        $data = self::get($key);
        
        if ($data !== false) {
            return $data;
        }
        
        $data = $callback();
        self::set($key, $data, $ttl);
        
        return $data;
    }
    
    /**
     * Verificar si existe una clave en cache
     * 
     * @param string $key Clave del cache
     * @return bool
     */
    public static function has(string $key): bool
    {
        return self::get($key) !== false;
    }
    
    /**
     * Obtener información del cache
     * 
     * @return array
     */
    public static function info(): array
    {
        $files = glob(self::$cacheDir . '*.cache');
        $totalSize = 0;
        $expired = 0;
        $active = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
                
                $data = file_get_contents($file);
                $cache = unserialize($data);
                
                if ($cache['expires'] < time()) {
                    $expired++;
                } else {
                    $active++;
                }
            }
        }
        
        return [
            'total_files' => count($files),
            'active_files' => $active,
            'expired_files' => $expired,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'cache_dir' => self::$cacheDir
        ];
    }
    
    /**
     * Generar nombre de archivo para la clave
     * 
     * @param string $key Clave del cache
     * @return string
     */
    private static function getFilename(string $key): string
    {
        if (self::$cacheDir === null) {
            self::$cacheDir = WRITEPATH . 'cache/simple/';
        }
        
        return self::$cacheDir . md5($key) . '.cache';
    }
    
    /**
     * Cache específico para productos
     * 
     * @param int $productId ID del producto
     * @param callable $callback Función para obtener datos
     * @param int $ttl Tiempo de vida (por defecto 30 minutos)
     * @return mixed
     */
    public static function rememberProduct(int $productId, callable $callback, int $ttl = 1800)
    {
        return self::remember("product_{$productId}", $callback, $ttl);
    }
    
    /**
     * Cache específico para categorías
     * 
     * @param string $key Clave específica
     * @param callable $callback Función para obtener datos
     * @param int $ttl Tiempo de vida (por defecto 1 hora)
     * @return mixed
     */
    public static function rememberCategories(string $key, callable $callback, int $ttl = 3600)
    {
        return self::remember("categories_{$key}", $callback, $ttl);
    }
    
    /**
     * Cache específico para búsquedas
     * 
     * @param string $searchTerm Término de búsqueda
     * @param array $filters Filtros aplicados
     * @param callable $callback Función para obtener resultados
     * @param int $ttl Tiempo de vida (por defecto 15 minutos)
     * @return mixed
     */
    public static function rememberSearch(string $searchTerm, array $filters, callable $callback, int $ttl = 900)
    {
        $key = 'search_' . md5($searchTerm . serialize($filters));
        return self::remember($key, $callback, $ttl);
    }
    
    /**
     * Invalidar cache relacionado con un producto
     * 
     * @param int $productId ID del producto
     * @return void
     */
    public static function invalidateProduct(int $productId): void
    {
        // Eliminar cache del producto específico
        self::delete("product_{$productId}");
        
        // Eliminar caches relacionados (búsquedas, categorías, etc.)
        $files = glob(self::$cacheDir . '*.cache');
        
        foreach ($files as $file) {
            $filename = basename($file, '.cache');
            
            // Si el archivo contiene referencias al producto, eliminarlo
            if (strpos($filename, 'search_') === 0 || 
                strpos($filename, 'categories_') === 0 ||
                strpos($filename, 'featured_') === 0) {
                unlink($file);
            }
        }
    }
}
