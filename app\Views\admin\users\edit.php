<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-user-edit me-2"></i>Editar Usuario</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/admin/users">Usuarios</a></li>
                    <li class="breadcrumb-item active">Editar <?= esc($user['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/admin/users/view/<?= $user['id'] ?>" class="btn btn-outline-info me-2">
                <i class="fas fa-eye me-2"></i>Ver Detalle
            </a>
            <a href="/admin/users" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver
            </a>
        </div>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Información del Usuario</h5>
            </div>
            <div class="card-body">
                <form action="/admin/users/edit/<?= $user['id'] ?>" method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= esc($user['name']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nombre de Usuario <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" value="<?= esc($user['username']) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= esc($user['email']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Teléfono <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= esc($user['phone'] ?? '') ?>" placeholder="+50212345678" required>
                                <div class="form-text">Formato: +502XXXXXXXX (incluir código de país)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Estado</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?= $user['is_active'] === 'active' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        Usuario Activo
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Espacio para futuras opciones -->
                        </div>
                    </div>

                    
                    <div class="mb-3">
                        <label class="form-label">Roles</label>
                        <div class="row">
                            <?php if (!empty($all_roles)): ?>
                                <?php foreach ($all_roles as $role): ?>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="roles[]" value="<?= $role['id'] ?>" id="role_<?= $role['id'] ?>" <?= in_array($role['id'], $user_role_ids) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="role_<?= $role['id'] ?>">
                                                <?= esc($role['name']) ?>
                                                <?php if ($role['description']): ?>
                                                    <small class="text-muted d-block"><?= esc($role['description']) ?></small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <p class="text-muted">No hay roles disponibles</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Usuario
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Información Actual -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información Actual</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="user-avatar mx-auto" style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.5rem;">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </div>
                    <h6 class="mt-2 mb-0"><?= esc($user['name']) ?></h6>
                    <small class="text-muted"><?= esc($user['email']) ?></small>
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td><?= $user['id'] ?></td>
                    </tr>
                    <tr>
                        <td><strong>Estado:</strong></td>
                        <td>
                            <?php if ($user['is_active']): ?>
                                <span class="badge bg-success">Activo</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Inactivo</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Registrado:</strong></td>
                        <td><?= date('d/m/Y', strtotime($user['created_at'])) ?></td>
                    </tr>
                </table>
                
                <hr>
                
                <h6>Roles Actuales</h6>
                <?php if (!empty($user_roles)): ?>
                    <?php foreach ($user_roles as $role): ?>
                        <span class="badge bg-primary me-1 mb-1"><?= esc($role['name']) ?></span>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted small">Sin roles asignados</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Acciones Adicionales -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Acciones Adicionales</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/users/view/<?= $user['id'] ?>" class="btn btn-outline-info">
                        <i class="fas fa-eye me-2"></i>Ver Detalle Completo
                    </a>
                    <button class="btn btn-outline-warning" onclick="resetPassword(<?= $user['id'] ?>)">
                        <i class="fas fa-key me-2"></i>Resetear Contraseña
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showUserActivity(<?= $user['id'] ?>)">
                        <i class="fas fa-history me-2"></i>Ver Actividad
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Elegir Tipo de Reseteo -->
<div class="modal fade" id="resetPasswordTypeModal" tabindex="-1" aria-labelledby="resetPasswordTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="resetPasswordTypeModalLabel">
                    <i class="fas fa-key me-2"></i>Resetear Contraseña
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-user-lock fa-3x text-warning mb-3"></i>
                    <h6>¿Cómo deseas resetear la contraseña?</h6>
                    <p class="text-muted small">Elige el método que prefieras para establecer la nueva contraseña.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-info" id="autoPasswordBtn">
                    <i class="fas fa-random me-2"></i>Contraseña Automática
                </button>
                <button type="button" class="btn btn-warning" id="manualPasswordBtn">
                    <i class="fas fa-edit me-2"></i>Contraseña Manual
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Contraseña Manual -->
<div class="modal fade" id="manualPasswordModal" tabindex="-1" aria-labelledby="manualPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="manualPasswordModalLabel">
                    <i class="fas fa-edit me-2"></i>Establecer Contraseña Manual
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newPassword" class="form-label">Nueva Contraseña</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="newPassword" placeholder="Ingrese la nueva contraseña" minlength="6">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">La contraseña debe tener al menos 6 caracteres.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmManualPasswordBtn">
                    <i class="fas fa-check me-2"></i>Establecer Contraseña
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="resultModalHeader">
                <h5 class="modal-title" id="resultModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Resultado
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="resultModalBody">
                    <!-- Contenido dinámico -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>Entendido
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let currentUserId = null;

function resetPassword(userId) {
    currentUserId = userId;

    // Mostrar modal para elegir tipo de reseteo
    const typeModal = new bootstrap.Modal(document.getElementById('resetPasswordTypeModal'));
    typeModal.show();
}

// Event listeners para los botones del modal de tipo
document.getElementById('autoPasswordBtn').addEventListener('click', function() {
    bootstrap.Modal.getInstance(document.getElementById('resetPasswordTypeModal')).hide();
    executePasswordReset(null);
});

document.getElementById('manualPasswordBtn').addEventListener('click', function() {
    bootstrap.Modal.getInstance(document.getElementById('resetPasswordTypeModal')).hide();

    // Mostrar modal para contraseña manual
    const manualModal = new bootstrap.Modal(document.getElementById('manualPasswordModal'));
    manualModal.show();

    // Limpiar campo
    document.getElementById('newPassword').value = '';
});

// Toggle para mostrar/ocultar contraseña
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('newPassword');
    const icon = this.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Confirmar contraseña manual
document.getElementById('confirmManualPasswordBtn').addEventListener('click', function() {
    const newPassword = document.getElementById('newPassword').value.trim();

    if (!newPassword || newPassword.length < 6) {
        showResultModal('error', 'Error de Validación', 'La contraseña debe tener al menos 6 caracteres.');
        return;
    }

    bootstrap.Modal.getInstance(document.getElementById('manualPasswordModal')).hide();
    executePasswordReset(newPassword);
});

function executePasswordReset(password) {
    const requestBody = password ? { password: password } : {};

    // Mostrar loading en el botón original
    const originalButton = document.querySelector(`button[onclick="resetPassword(${currentUserId})"]`);
    if (originalButton) {
        originalButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Reseteando...';
        originalButton.disabled = true;
    }

    fetch(`/admin/users/reset-password/${currentUserId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(requestBody)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const title = data.is_manual ? 'Contraseña Establecida' : 'Contraseña Restablecida';
            const message = data.is_manual
                ? `<strong>Nueva contraseña:</strong> ${data.new_password}<br><strong>Usuario:</strong> ${data.user_email}`
                : `<strong>Nueva contraseña temporal:</strong> ${data.new_password}<br><strong>Usuario:</strong> ${data.user_email}<br><small class="text-muted">Se ha enviado un email al usuario.</small>`;

            showResultModal('success', title, message);
        } else {
            showResultModal('error', 'Error', data.error || 'No se pudo resetear la contraseña');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showResultModal('error', 'Error de Conexión', 'No se pudo conectar con el servidor');
    })
    .finally(() => {
        // Restaurar botón original
        if (originalButton) {
            originalButton.innerHTML = '<i class="fas fa-key me-2"></i>Resetear Contraseña';
            originalButton.disabled = false;
        }
    });
}

function showResultModal(type, title, message) {
    const modal = document.getElementById('resultModal');
    const header = document.getElementById('resultModalHeader');
    const titleElement = document.getElementById('resultModalLabel');
    const body = document.getElementById('resultModalBody');

    // Configurar colores según el tipo
    if (type === 'success') {
        header.className = 'modal-header bg-success text-white';
        titleElement.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + title;
        body.innerHTML = `<i class="fas fa-check-circle fa-3x text-success mb-3"></i><div>${message}</div>`;
    } else {
        header.className = 'modal-header bg-danger text-white';
        titleElement.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>' + title;
        body.innerHTML = `<i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i><div>${message}</div>`;
    }

    const resultModal = new bootstrap.Modal(modal);
    resultModal.show();
}

function showUserActivity(userId) {
    // Redirigir a la vista detallada del usuario donde está implementada la funcionalidad completa
    window.location.href = `/admin/users/view/${userId}`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Validación del formulario
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        
        if (!name || !email) {
            e.preventDefault();
            alert('Nombre y email son requeridos');
        }
    });
});
</script>
<?= $this->endSection() ?>
