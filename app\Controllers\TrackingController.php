<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\ShippingManager;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Seguimiento de Envíos
 * Seguimiento público de paquetes para clientes
 */
class TrackingController extends BaseController
{
    protected $shippingManager;
    protected $logger;
    
    public function __construct()
    {
        $this->shippingManager = new ShippingManager();
        $this->logger = new AdvancedLogger();
    }
    
    /**
     * Página principal de seguimiento
     */
    public function index()
    {
        $data = [
            'title' => 'Seguimiento de Envíos - MrCell Guatemala',
            'tracking_number' => $this->request->getGet('tracking')
        ];
        
        return view('tracking/index', $data);
    }
    
    /**
     * Buscar envío por número de seguimiento
     */
    public function track()
    {
        try {
            $trackingNumber = $this->request->getPost('tracking_number') ?? $this->request->getGet('tracking_number');
            
            if (empty($trackingNumber)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de seguimiento requerido'
                ]);
            }
            
            $result = $this->shippingManager->trackShipment($trackingNumber);
            
            if ($result['success']) {
                $this->logger->info("Package tracked by customer", [
                    'tracking_number' => $trackingNumber,
                    'ip' => $this->request->getIPAddress()
                ]);
            }
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            $this->logger->error("Tracking error: " . $e->getMessage(), [
                'tracking_number' => $trackingNumber ?? 'unknown',
                'ip' => $this->request->getIPAddress()
            ]);
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al buscar el envío'
            ]);
        }
    }
    
    /**
     * Página de resultados de seguimiento
     */
    public function result($trackingNumber = null)
    {
        if (!$trackingNumber) {
            return redirect()->to('/tracking');
        }
        
        $trackingInfo = $this->shippingManager->trackShipment($trackingNumber);
        
        if (!$trackingInfo['success']) {
            session()->setFlashdata('error', 'Número de seguimiento no encontrado');
            return redirect()->to('/tracking');
        }
        
        $data = [
            'title' => 'Seguimiento: ' . $trackingNumber . ' - MrCell Guatemala',
            'tracking_info' => $trackingInfo,
            'tracking_number' => $trackingNumber
        ];
        
        return view('tracking/result', $data);
    }
    
    /**
     * Calcular costo de envío para checkout
     */
    public function calculateShipping()
    {
        try {
            $orderData = [
                'destination' => [
                    'department' => $this->request->getPost('department'),
                    'municipality' => $this->request->getPost('municipality')
                ],
                'weight' => (float)$this->request->getPost('weight'),
                'value' => (float)$this->request->getPost('value'),
                'items' => $this->request->getPost('items') ?? []
            ];
            
            $result = $this->shippingManager->calculateShippingCost($orderData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al calcular costo de envío'
            ]);
        }
    }
    
    /**
     * Obtener departamentos de Guatemala
     */
    public function getDepartments()
    {
        try {
            $departments = $this->db->table('guatemala_departments')
                                   ->where('is_active', 1)
                                   ->orderBy('name')
                                   ->get()
                                   ->getResultArray();
            
            return $this->response->setJSON([
                'success' => true,
                'departments' => $departments
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener departamentos'
            ]);
        }
    }
    
    /**
     * Obtener empresas de envío disponibles
     */
    public function getShippingCompanies()
    {
        try {
            $companies = $this->db->table('shipping_companies')
                                 ->where('is_active', 1)
                                 ->orderBy('name')
                                 ->get()
                                 ->getResultArray();
            
            return $this->response->setJSON([
                'success' => true,
                'companies' => $companies
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener empresas de envío'
            ]);
        }
    }
    
    /**
     * Suscribirse a notificaciones de seguimiento
     */
    public function subscribeNotifications()
    {
        try {
            $trackingNumber = $this->request->getPost('tracking_number');
            $email = $this->request->getPost('email');
            $phone = $this->request->getPost('phone');
            
            if (empty($trackingNumber) || (empty($email) && empty($phone))) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de seguimiento y al menos un método de contacto son requeridos'
                ]);
            }
            
            // Verificar que el envío existe
            $trackingInfo = $this->shippingManager->trackShipment($trackingNumber);
            
            if (!$trackingInfo['success']) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de seguimiento no válido'
                ]);
            }
            
            // Guardar suscripción
            $subscriptionData = [
                'tracking_number' => $trackingNumber,
                'email' => $email,
                'phone' => $phone,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->table('tracking_subscriptions')->insert($subscriptionData);
            
            $this->logger->info("Tracking notification subscription", [
                'tracking_number' => $trackingNumber,
                'email' => $email,
                'phone' => $phone
            ]);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Suscripción exitosa. Recibirás notificaciones sobre el estado de tu envío.'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al suscribirse a notificaciones'
            ]);
        }
    }
    
    /**
     * Reportar problema con envío
     */
    public function reportIssue()
    {
        try {
            $trackingNumber = $this->request->getPost('tracking_number');
            $issueType = $this->request->getPost('issue_type');
            $description = $this->request->getPost('description');
            $contactEmail = $this->request->getPost('contact_email');
            $contactPhone = $this->request->getPost('contact_phone');
            
            if (empty($trackingNumber) || empty($issueType) || empty($description)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Todos los campos son requeridos'
                ]);
            }
            
            // Verificar que el envío existe
            $trackingInfo = $this->shippingManager->trackShipment($trackingNumber);
            
            if (!$trackingInfo['success']) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de seguimiento no válido'
                ]);
            }
            
            // Guardar reporte
            $reportData = [
                'tracking_number' => $trackingNumber,
                'issue_type' => $issueType,
                'description' => $description,
                'contact_email' => $contactEmail,
                'contact_phone' => $contactPhone,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $reportId = $this->db->table('shipping_issue_reports')->insert($reportData);
            
            $this->logger->info("Shipping issue reported", [
                'report_id' => $reportId,
                'tracking_number' => $trackingNumber,
                'issue_type' => $issueType
            ]);
            
            return $this->response->setJSON([
                'success' => true,
                'report_id' => $reportId,
                'message' => 'Reporte enviado exitosamente. Te contactaremos pronto para resolver el problema.'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al enviar reporte'
            ]);
        }
    }
    
    /**
     * Obtener información de envío para modal
     */
    public function getShipmentInfo($trackingNumber)
    {
        try {
            $result = $this->shippingManager->trackShipment($trackingNumber);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener información del envío'
            ]);
        }
    }
    
    /**
     * Página de ayuda sobre envíos
     */
    public function help()
    {
        $data = [
            'title' => 'Ayuda - Envíos y Seguimiento - MrCell Guatemala',
            'companies' => $this->getShippingCompaniesInfo(),
            'departments' => $this->getGuatemalaDepartments(),
            'faq' => $this->getShippingFAQ()
        ];
        
        return view('tracking/help', $data);
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getShippingCompaniesInfo(): array
    {
        return $this->db->table('shipping_companies')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getGuatemalaDepartments(): array
    {
        return $this->db->table('guatemala_departments')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getShippingFAQ(): array
    {
        return [
            [
                'question' => '¿Cómo puedo rastrear mi paquete?',
                'answer' => 'Ingresa tu número de seguimiento en la página de seguimiento. Recibirás información detallada sobre el estado y ubicación de tu paquete.'
            ],
            [
                'question' => '¿Cuánto tiempo tarda la entrega?',
                'answer' => 'Los tiempos de entrega varían según el destino: Ciudad de Guatemala 1-2 días, departamentos cercanos 2-3 días, departamentos lejanos 3-5 días.'
            ],
            [
                'question' => '¿Qué empresas de envío utilizan?',
                'answer' => 'Trabajamos con las principales empresas de Guatemala: Cargo Expreso, GuatEx y Forza, para garantizar la mejor cobertura nacional.'
            ],
            [
                'question' => '¿Puedo cambiar la dirección de entrega?',
                'answer' => 'Sí, puedes cambiar la dirección antes de que el paquete salga de nuestras instalaciones. Contacta a servicio al cliente lo antes posible.'
            ],
            [
                'question' => '¿Qué hago si mi paquete no llega?',
                'answer' => 'Si tu paquete no llega en el tiempo estimado, usa la función "Reportar Problema" en la página de seguimiento o contacta directamente a servicio al cliente.'
            ]
        ];
    }
}
