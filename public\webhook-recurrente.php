<?php
/**
 * Webhook mejorado para Recurrente
 * Archivo: public/webhook-recurrente.php
 */

// Configurar headers para CORS y JSON
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Recurrente-Signature");
header("Content-Type: application/json");

// Manejar preflight OPTIONS
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

// Solo permitir POST
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    http_response_code(405);
    echo json_encode(["error" => "Method not allowed"]);
    exit();
}

try {
    // Conectar a la base de datos
    $pdo = new PDO("mysql:host=**************;port=3306;dbname=mayansourcecom_mrcell;charset=utf8mb4", "mayansourcecom_mrcell", "Clairo!23");
    
    // Obtener payload
    $payload = file_get_contents("php://input");
    $signature = $_SERVER["HTTP_RECURRENTE_SIGNATURE"] ?? "";
    
    // Log del webhook recibido
    error_log("=== WEBHOOK RECURRENTE RECIBIDO ===");
    error_log("Timestamp: " . date("Y-m-d H:i:s"));
    error_log("Signature: " . $signature);
    error_log("Payload: " . $payload);
    
    // Registrar en webhook_logs
    $stmt = $pdo->prepare("
        INSERT INTO webhook_logs (source, event_type, payload, headers, status, created_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $headers = json_encode(getallheaders());
    $stmt->execute(["recurrente", "webhook_received", $payload, $headers, "pending"]);
    $webhookLogId = $pdo->lastInsertId();
    
    // Decodificar payload
    $data = json_decode($payload, true);
    if (!$data) {
        throw new Exception("Invalid JSON payload");
    }
    
    // Extraer información del evento
    $eventType = $data["type"] ?? $data["event"] ?? "unknown";
    $eventData = $data["data"] ?? $data;
    
    error_log("Event Type: " . $eventType);
    error_log("Event Data: " . json_encode($eventData));
    
    // Actualizar tipo de evento en log
    $stmt = $pdo->prepare("UPDATE webhook_logs SET event_type = ? WHERE id = ?");
    $stmt->execute([$eventType, $webhookLogId]);
    
    $processed = false;
    $response = "";
    
    // Procesar según el tipo de evento
    switch ($eventType) {
        case "checkout.completed":
        case "charge.succeeded":
        case "payment.completed":
            $processed = processSuccessfulPayment($pdo, $eventData);
            $response = "Payment processed successfully";
            break;
            
        case "checkout.failed":
        case "charge.failed":
        case "payment.failed":
            $processed = processFailedPayment($pdo, $eventData);
            $response = "Failed payment processed";
            break;
            
        case "checkout.cancelled":
        case "payment.cancelled":
            $processed = processCancelledPayment($pdo, $eventData);
            $response = "Cancelled payment processed";
            break;
            
        default:
            error_log("Unhandled event type: " . $eventType);
            $response = "Event type not handled: " . $eventType;
            $processed = true; // No es un error, solo no lo manejamos
    }
    
    // Actualizar log con resultado
    $status = $processed ? "success" : "error";
    $stmt = $pdo->prepare("
        UPDATE webhook_logs 
        SET status = ?, response = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$status, $response, $webhookLogId]);
    
    // Responder
    http_response_code(200);
    echo json_encode([
        "status" => "ok",
        "message" => $response,
        "processed" => $processed
    ]);
    
} catch (Exception $e) {
    error_log("Webhook error: " . $e->getMessage());
    
    // Actualizar log con error si existe
    if (isset($webhookLogId)) {
        $stmt = $pdo->prepare("
            UPDATE webhook_logs 
            SET status = ?, error_message = ?, processed_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute(["error", $e->getMessage(), $webhookLogId]);
    }
    
    http_response_code(500);
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage()
    ]);
}

/**
 * Procesar pago exitoso
 */
function processSuccessfulPayment($pdo, $data) {
    try {
        // Extraer información del pago
        $transactionId = $data["id"] ?? $data["checkout_id"] ?? $data["transaction_id"] ?? null;
        $orderId = $data["metadata"]["order_id"] ?? $data["order_id"] ?? $data["external_id"] ?? null;
        $amount = isset($data["amount"]) ? $data["amount"] / 100 : null;
        $currency = $data["currency"] ?? "GTQ";
        
        error_log("Processing successful payment - Transaction: $transactionId, Order: $orderId, Amount: $amount");
        
        if (!$transactionId || !$orderId) {
            throw new Exception("Missing transaction_id or order_id");
        }
        
        // Buscar el pago
        $stmt = $pdo->prepare("
            SELECT * FROM payments 
            WHERE (transaction_id = ? OR order_id = ?) 
            AND payment_method = \"recurrente\"
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$transactionId, $orderId]);
        $payment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$payment) {
            error_log("Payment not found for transaction: $transactionId, order: $orderId");
            return false;
        }
        
        // Actualizar pago si no está completado
        if ($payment["status"] !== "completed") {
            $updateData = [
                "status" => "completed",
                "payment_date" => date("Y-m-d H:i:s"),
                "gateway_response" => json_encode($data),
                "notes" => "Pago completado via webhook"
            ];
            
            if ($amount) {
                $updateData["amount"] = $amount;
            }
            
            if ($transactionId) {
                $updateData["transaction_id"] = $transactionId;
            }
            
            $setClause = implode(", ", array_map(fn($k) => "$k = ?", array_keys($updateData)));
            $stmt = $pdo->prepare("UPDATE payments SET $setClause WHERE id = ?");
            $stmt->execute([...array_values($updateData), $payment["id"]]);
            
            // Actualizar estado del pedido
            $stmt = $pdo->prepare("UPDATE orders SET status = \"paid\", updated_at = NOW() WHERE id = ?");
            $stmt->execute([$payment["order_id"]]);
            
            error_log("Payment updated successfully - Payment ID: " . $payment["id"]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error processing successful payment: " . $e->getMessage());
        return false;
    }
}

/**
 * Procesar pago fallido
 */
function processFailedPayment($pdo, $data) {
    try {
        $transactionId = $data["id"] ?? $data["checkout_id"] ?? null;
        $orderId = $data["metadata"]["order_id"] ?? $data["order_id"] ?? null;
        
        if (!$transactionId && !$orderId) {
            return false;
        }
        
        // Buscar y actualizar pago
        $stmt = $pdo->prepare("
            UPDATE payments 
            SET status = \"failed\", 
                gateway_response = ?,
                notes = \"Pago fallido via webhook\"
            WHERE (transaction_id = ? OR order_id = ?) 
            AND payment_method = \"recurrente\"
        ");
        $stmt->execute([json_encode($data), $transactionId, $orderId]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error processing failed payment: " . $e->getMessage());
        return false;
    }
}

/**
 * Procesar pago cancelado
 */
function processCancelledPayment($pdo, $data) {
    try {
        $transactionId = $data["id"] ?? $data["checkout_id"] ?? null;
        $orderId = $data["metadata"]["order_id"] ?? $data["order_id"] ?? null;
        
        if (!$transactionId && !$orderId) {
            return false;
        }
        
        // Buscar y actualizar pago
        $stmt = $pdo->prepare("
            UPDATE payments 
            SET status = \"cancelled\", 
                gateway_response = ?,
                notes = \"Pago cancelado via webhook\"
            WHERE (transaction_id = ? OR order_id = ?) 
            AND payment_method = \"recurrente\"
        ");
        $stmt->execute([json_encode($data), $transactionId, $orderId]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error processing cancelled payment: " . $e->getMessage());
        return false;
    }
}
?>