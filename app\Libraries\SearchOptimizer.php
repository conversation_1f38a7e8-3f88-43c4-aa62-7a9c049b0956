<?php

namespace App\Libraries;

use App\Libraries\SimpleCache;

/**
 * Optimizador de Búsqueda para cPanel
 * Implementa búsqueda FULLTEXT MySQL sin Elasticsearch
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SearchOptimizer
{
    private $db;
    private $cache;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new SimpleCache();
    }
    
    /**
     * Preparar índices FULLTEXT para búsqueda optimizada
     * Ejecutar una sola vez para configurar la base de datos
     * 
     * @return array Resultado de la configuración
     */
    public function setupFullTextIndexes(): array
    {
        $results = [];
        
        try {
            // Verificar si ya existe el índice FULLTEXT
            $query = $this->db->query("SHOW INDEX FROM products WHERE Key_name = 'fulltext_search'");
            
            if ($query->getNumRows() == 0) {
                // Crear índice FULLTEXT para productos
                $this->db->query("ALTER TABLE products ADD FULLTEXT fulltext_search (name, description, sku, short_description)");
                $results['products_fulltext'] = 'Creado exitosamente';
            } else {
                $results['products_fulltext'] = 'Ya existe';
            }
            
            // Crear índices adicionales para optimizar filtros
            $indexes = [
                'idx_products_price' => "ALTER TABLE products ADD INDEX idx_products_price (price_regular, price_sale)",
                'idx_products_stock' => "ALTER TABLE products ADD INDEX idx_products_stock (stock_quantity, stock_status)",
                'idx_products_rating' => "ALTER TABLE products ADD INDEX idx_products_rating (rating_average, rating_count)",
                'idx_products_featured' => "ALTER TABLE products ADD INDEX idx_products_featured (is_featured, is_active)"
            ];
            
            foreach ($indexes as $indexName => $sql) {
                $checkQuery = $this->db->query("SHOW INDEX FROM products WHERE Key_name = '{$indexName}'");
                
                if ($checkQuery->getNumRows() == 0) {
                    $this->db->query($sql);
                    $results[$indexName] = 'Creado exitosamente';
                } else {
                    $results[$indexName] = 'Ya existe';
                }
            }
            
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Búsqueda optimizada con FULLTEXT
     * 
     * @param string $searchTerm Término de búsqueda
     * @param array $filters Filtros adicionales
     * @param int $limit Límite de resultados
     * @param int $offset Offset para paginación
     * @return array
     */
    public function search(string $searchTerm, array $filters = [], int $limit = 20, int $offset = 0): array
    {
        // Usar cache para búsquedas
        return SimpleCache::rememberSearch($searchTerm, $filters, function() use ($searchTerm, $filters, $limit, $offset) {
            
            $sql = "SELECT p.*, 
                           c.name as category_name,
                           b.name as brand_name,
                           MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE) as relevance_score
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN brands b ON p.brand_id = b.id
                    WHERE p.is_active = 1 
                    AND p.deleted_at IS NULL";
            
            $params = [];
            
            // Búsqueda FULLTEXT si hay término
            if (!empty($searchTerm)) {
                $searchTerm = $this->prepareSearchTerm($searchTerm);
                $sql .= " AND MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE)";
                $params[] = $searchTerm;
                $params[] = $searchTerm; // Para el SELECT también
            } else {
                // Si no hay término de búsqueda, usar relevancia por popularidad
                $sql = str_replace('MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE) as relevance_score', 
                                 '(p.rating_average * p.rating_count) as relevance_score', $sql);
            }
            
            // Aplicar filtros
            $sql .= $this->buildFilters($filters, $params);
            
            // Ordenamiento
            if (!empty($searchTerm)) {
                $sql .= " ORDER BY relevance_score DESC, p.is_featured DESC, p.rating_average DESC";
            } else {
                $sql .= $this->buildSorting($filters);
            }
            
            // Límite y offset
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $query = $this->db->query($sql, $params);
            $results = $query->getResultArray();
            
            // Obtener total de resultados para paginación
            $totalQuery = $this->getTotalResults($searchTerm, $filters);
            
            return [
                'products' => $results,
                'total' => $totalQuery,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalQuery
            ];
            
        }, 900); // Cache por 15 minutos
    }
    
    /**
     * Preparar término de búsqueda para FULLTEXT
     * 
     * @param string $term Término original
     * @return string Término preparado
     */
    private function prepareSearchTerm(string $term): string
    {
        // Limpiar el término
        $term = trim($term);
        $term = preg_replace('/[^\w\s\-]/', '', $term);
        
        // Dividir en palabras
        $words = explode(' ', $term);
        $processedWords = [];
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) >= 2) {
                // Agregar wildcards para búsqueda parcial
                $processedWords[] = "+{$word}*";
            }
        }
        
        return implode(' ', $processedWords);
    }
    
    /**
     * Construir filtros SQL
     * 
     * @param array $filters Filtros a aplicar
     * @param array &$params Parámetros por referencia
     * @return string SQL adicional
     */
    private function buildFilters(array $filters, array &$params): string
    {
        $sql = '';
        
        // Filtro por categoría
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        // Filtro por marca
        if (!empty($filters['brand_id'])) {
            $sql .= " AND p.brand_id = ?";
            $params[] = $filters['brand_id'];
        }
        
        // Filtro por rango de precios
        if (!empty($filters['min_price'])) {
            $sql .= " AND (COALESCE(p.price_sale, p.price_regular) >= ?)";
            $params[] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND (COALESCE(p.price_sale, p.price_regular) <= ?)";
            $params[] = $filters['max_price'];
        }
        
        // Filtro por disponibilidad
        if (!empty($filters['in_stock'])) {
            $sql .= " AND p.stock_quantity > 0 AND p.stock_status = 'in_stock'";
        }
        
        // Filtro por productos destacados
        if (!empty($filters['featured'])) {
            $sql .= " AND p.is_featured = 1";
        }
        
        // Filtro por rating mínimo
        if (!empty($filters['min_rating'])) {
            $sql .= " AND p.rating_average >= ?";
            $params[] = $filters['min_rating'];
        }
        
        return $sql;
    }
    
    /**
     * Construir ordenamiento SQL
     * 
     * @param array $filters Filtros que pueden incluir sort_by
     * @return string SQL de ordenamiento
     */
    private function buildSorting(array $filters): string
    {
        $sortBy = $filters['sort_by'] ?? 'relevance';
        
        switch ($sortBy) {
            case 'price_asc':
                return " ORDER BY COALESCE(p.price_sale, p.price_regular) ASC";
            case 'price_desc':
                return " ORDER BY COALESCE(p.price_sale, p.price_regular) DESC";
            case 'name':
                return " ORDER BY p.name ASC";
            case 'rating':
                return " ORDER BY p.rating_average DESC, p.rating_count DESC";
            case 'newest':
                return " ORDER BY p.created_at DESC";
            case 'popular':
                return " ORDER BY p.rating_count DESC, p.rating_average DESC";
            default:
                return " ORDER BY p.is_featured DESC, p.rating_average DESC, p.created_at DESC";
        }
    }
    
    /**
     * Obtener total de resultados para paginación
     * 
     * @param string $searchTerm Término de búsqueda
     * @param array $filters Filtros aplicados
     * @return int Total de resultados
     */
    private function getTotalResults(string $searchTerm, array $filters): int
    {
        $sql = "SELECT COUNT(*) as total
                FROM products p
                WHERE p.is_active = 1 
                AND p.deleted_at IS NULL";
        
        $params = [];
        
        if (!empty($searchTerm)) {
            $searchTerm = $this->prepareSearchTerm($searchTerm);
            $sql .= " AND MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE)";
            $params[] = $searchTerm;
        }
        
        $sql .= $this->buildFilters($filters, $params);
        
        $query = $this->db->query($sql, $params);
        $result = $query->getRowArray();
        
        return (int) $result['total'];
    }
    
    /**
     * Obtener sugerencias de búsqueda (autocompletado)
     * 
     * @param string $term Término parcial
     * @param int $limit Límite de sugerencias
     * @return array
     */
    public function getSuggestions(string $term, int $limit = 10): array
    {
        if (strlen($term) < 2) {
            return [];
        }
        
        return SimpleCache::remember("suggestions_" . md5($term), function() use ($term, $limit) {
            
            $sql = "SELECT DISTINCT p.name, p.slug, p.featured_image,
                           COALESCE(p.price_sale, p.price_regular) as price
                    FROM products p
                    WHERE p.is_active = 1 
                    AND p.deleted_at IS NULL
                    AND (p.name LIKE ? OR p.sku LIKE ?)
                    ORDER BY 
                        CASE WHEN p.name LIKE ? THEN 1 ELSE 2 END,
                        p.is_featured DESC,
                        p.name ASC
                    LIMIT ?";
            
            $searchPattern = "%{$term}%";
            $exactPattern = "{$term}%";
            
            $query = $this->db->query($sql, [$searchPattern, $searchPattern, $exactPattern, $limit]);
            
            return $query->getResultArray();
            
        }, 1800); // Cache por 30 minutos
    }
    
    /**
     * Limpiar cache de búsquedas
     * 
     * @return int Número de archivos eliminados
     */
    public function clearSearchCache(): int
    {
        $cacheDir = WRITEPATH . 'cache/simple/';
        $files = glob($cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            $filename = basename($file, '.cache');
            
            if (strpos($filename, 'search_') === 0 || 
                strpos($filename, 'suggestions_') === 0) {
                unlink($file);
                $deleted++;
            }
        }
        
        return $deleted;
    }
}
