<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class AdminAuthFilter implements FilterInterface
{
    /**
     * Verificar autenticación de administrador antes de ejecutar el controlador
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Rutas que no requieren autenticación
        $publicRoutes = [
            'admin/login',
            'admin/auth/login',
            'admin/auth/logout',
            'admin/forgot-password',
            'admin/reset-password'
        ];

        $currentPath = trim($request->getUri()->getPath(), '/');

        // Si es una ruta pública, permitir acceso
        if (in_array($currentPath, $publicRoutes)) {
            return null;
        }

        $session = session();
        $db = \Config\Database::connect();

        // Verificar si el admin está logueado
        $adminId = $session->get('admin_id');
        $isLoggedIn = $session->get('is_admin_logged_in');

        if (!$adminId || !$isLoggedIn) {
            // Si es una petición AJAX, devolver JSON
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'status' => 'error',
                    'message' => 'Sesión expirada. Por favor, inicia sesión nuevamente.',
                    'redirect' => base_url('admin/login')
                ])->setStatusCode(401);
            }

            // Limpiar sesión por seguridad
            $session->destroy();

            // Redirigir al login con mensaje
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado. Por favor, inicia sesión.');
        }

        try {
            // Verificar que el admin esté activo en la base de datos
            $admin = $db->query("
                SELECT id, is_active, bloqueado_hasta, session_timeout
                FROM administradores
                WHERE id = ? AND deleted_at IS NULL
            ", [$adminId])->getRowArray();

            if (!$admin || !$admin['is_active']) {
                $session->destroy();
                
                if ($request->isAJAX()) {
                    return service('response')->setJSON([
                        'status' => 'error',
                        'message' => 'Cuenta de administrador inactiva o eliminada.',
                        'redirect' => base_url('admin/login')
                    ])->setStatusCode(401);
                }
                
                return redirect()->to('/admin/login')->with('error', 'Cuenta de administrador inactiva o eliminada.');
            }

            // Verificar si la cuenta está bloqueada
            if ($admin['bloqueado_hasta'] && strtotime($admin['bloqueado_hasta']) > time()) {
                $session->destroy();
                
                if ($request->isAJAX()) {
                    return service('response')->setJSON([
                        'status' => 'error',
                        'message' => 'Cuenta temporalmente bloqueada.',
                        'redirect' => base_url('admin/login')
                    ])->setStatusCode(401);
                }
                
                return redirect()->to('/admin/login')->with('error', 'Cuenta temporalmente bloqueada.');
            }

            // Verificar timeout de sesión con configuración más flexible
            $loginTime = $session->get('admin_login_time');
            $lastActivity = $session->get('admin_last_activity');
            $sessionTimeout = $admin['session_timeout'] ?? 28800; // Default 8 horas

            $currentTime = time();
            
            // Usar el tiempo de última actividad si existe, sino el tiempo de login
            $timeToCheck = $lastActivity ?: $loginTime;
            
            if ($timeToCheck && ($currentTime - $timeToCheck) > $sessionTimeout) {
                $session->destroy();
                
                if ($request->isAJAX()) {
                    return service('response')->setJSON([
                        'status' => 'error',
                        'message' => 'Sesión expirada por inactividad.',
                        'redirect' => base_url('admin/login')
                    ])->setStatusCode(401);
                }
                
                return redirect()->to('/admin/login')->with('error', 'Sesión expirada por inactividad.');
            }

            // Actualizar tiempo de última actividad cada 2 minutos para mantener sesión activa
            if (!$lastActivity || ($currentTime - $lastActivity) > 120) {
                $session->set('admin_last_activity', $currentTime);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en AdminAuthFilter: ' . $e->getMessage());
            
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'status' => 'error',
                    'message' => 'Error interno del servidor',
                    'redirect' => base_url('admin/login')
                ])->setStatusCode(500);
            }
            
            return redirect()->to('/admin/login')->with('error', 'Error interno del servidor');
        }

        return null; // Continuar con la ejecución normal
    }

    /**
     * Después de ejecutar el controlador
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No hacer nada después
        return $response;
    }
}
