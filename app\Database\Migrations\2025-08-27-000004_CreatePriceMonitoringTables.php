<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tablas del sistema de monitoreo de precios
 * Incluye historial de precios y logs de notificaciones
 */
class CreatePriceMonitoringTables extends Migration
{
    public function up()
    {
        // Tabla de historial de precios
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'old_price' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
            ],
            'new_price' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
            ],
            'price_change' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Diferencia de precio (old_price - new_price)',
            ],
            'percentage_change' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'null' => false,
                'comment' => 'Porcentaje de cambio',
            ],
            'change_type' => [
                'type' => 'ENUM',
                'constraint' => ['decrease', 'increase'],
                'default' => 'decrease',
            ],
            'source' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => 'system',
                'comment' => 'Origen del cambio: system, manual, import',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['product_id', 'created_at']);
        $this->forge->addKey('created_at');
        $this->forge->addKey('change_type');
        
        $this->forge->createTable('price_history');
        
        // Foreign key para products
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de logs de notificaciones
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'notification_type' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'wishlist_reminder', 'similar_product', 'newsletter'],
                'null' => false,
            ],
            'channel' => [
                'type' => 'ENUM',
                'constraint' => ['email', 'whatsapp', 'sms', 'push'],
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['sent', 'failed', 'pending'],
                'default' => 'sent',
            ],
            'message_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'ID del mensaje del proveedor',
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Datos adicionales de la notificación',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'created_at']);
        $this->forge->addKey(['product_id', 'created_at']);
        $this->forge->addKey('notification_type');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('notification_log');
        
        // Foreign keys
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de configuración de notificaciones por usuario
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'email_notifications' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'whatsapp_notifications' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'sms_notifications' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'push_notifications' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'price_alerts' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'stock_alerts' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'wishlist_reminders' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'newsletter' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'frequency' => [
                'type' => 'ENUM',
                'constraint' => ['immediate', 'daily', 'weekly'],
                'default' => 'immediate',
            ],
            'quiet_hours_start' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Hora de inicio del período silencioso',
            ],
            'quiet_hours_end' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Hora de fin del período silencioso',
            ],
            'timezone' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => 'America/Guatemala',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id', false, true); // Unique key
        
        $this->forge->createTable('user_notification_preferences');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de plantillas de notificaciones personalizables
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'wishlist_reminder', 'newsletter'],
                'null' => false,
            ],
            'channel' => [
                'type' => 'ENUM',
                'constraint' => ['email', 'whatsapp', 'sms'],
                'null' => false,
            ],
            'subject' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Asunto para email',
            ],
            'content' => [
                'type' => 'TEXT',
                'null' => false,
                'comment' => 'Contenido de la plantilla con variables',
            ],
            'variables' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Variables disponibles en la plantilla',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'is_default' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['type', 'channel']);
        $this->forge->addKey('is_active');
        $this->forge->addKey('is_default');
        
        $this->forge->createTable('notification_templates');

        // Tabla de estadísticas de notificaciones
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'date' => [
                'type' => 'DATE',
                'null' => false,
            ],
            'notification_type' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'wishlist_reminder', 'newsletter'],
                'null' => false,
            ],
            'channel' => [
                'type' => 'ENUM',
                'constraint' => ['email', 'whatsapp', 'sms', 'push'],
                'null' => false,
            ],
            'sent_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'failed_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'success_rate' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'default' => 0.00,
                'comment' => 'Porcentaje de éxito',
            ],
            'unique_users' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'unique_products' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['date', 'notification_type', 'channel'], false, true); // Unique key
        $this->forge->addKey('date');
        
        $this->forge->createTable('notification_stats');
    }

    public function down()
    {
        $this->forge->dropTable('notification_stats', true);
        $this->forge->dropTable('notification_templates', true);
        $this->forge->dropTable('user_notification_preferences', true);
        $this->forge->dropTable('notification_log', true);
        $this->forge->dropTable('price_history', true);
    }
}
