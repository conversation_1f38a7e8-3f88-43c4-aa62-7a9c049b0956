<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ProductModel;
use App\Models\UserAddressModel;
use App\Models\ShippingPackageTypeModel;
use App\Models\ShippingZoneModel;

/**
 * Checkout Controller
 * Maneja el proceso de checkout con direcciones y cálculo de envío
 */
class CheckoutController extends Controller
{
    protected $productModel;
    protected $addressModel;
    protected $packageTypeModel;
    protected $zoneModel;

    public function __construct()
    {
        $this->productModel = new ProductModel();
        $this->addressModel = new UserAddressModel();
        $this->packageTypeModel = new ShippingPackageTypeModel();
        $this->zoneModel = new ShippingZoneModel();
    }

    /**
     * Página principal de checkout
     */
    public function index()
    {
        // Verificar que hay productos en el carrito
        $cartItems = $this->getCartItems();
        if (empty($cartItems) && ENVIRONMENT === 'production') {
            return redirect()->to('/carrito')->with('error', 'Tu carrito está vacío');
        }

        // Si no hay productos en desarrollo, crear productos de prueba
        if (empty($cartItems) && ENVIRONMENT === 'development') {
            $cartItems = [
                [
                    'id' => 1,
                    'name' => 'Producto de Prueba',
                    'price_regular' => 100.00,
                    'quantity' => 1,
                    'subtotal' => 100.00
                ]
            ];
        }

        // Obtener direcciones del usuario
        $userId = session()->get('user_id');
        $sessionId = session_id();
        
        $addresses = $this->addressModel->getUserAddresses($userId, $sessionId);
        $defaultAddress = $this->addressModel->getDefaultAddress($userId, $sessionId);
        
        // Obtener ubicaciones para el formulario
        $locations = $this->addressModel->getGuatemalaLocations();
        
        // Calcular totales del carrito
        $cartTotals = $this->calculateCartTotals($cartItems);

        // Obtener métodos de pago activos (pasando el total para validar Recurrente)
        $paymentMethods = $this->getActivePaymentMethods($cartTotals['total_before_shipping'], 'GTQ');

        // DEBUG TEMPORAL: Mostrar en pantalla qué está pasando
        //if (ENVIRONMENT === 'development') {
        //    echo "<div style='background: red; color: white; padding: 10px; position: fixed; top: 0; left: 0; z-index: 9999;'>";
        //    echo "DEBUG PAYMENT METHODS:<br>";
        //    echo "Count: " . count($paymentMethods) . "<br>";
        //    echo "Data: " . json_encode($paymentMethods) . "<br>";
        //    echo "</div>";
        //}

        $data = [
            'title' => 'Checkout - MrCell Guatemala',
            'cart_items' => $cartItems,
            'cart_totals' => $cartTotals,
            'addresses' => $addresses,
            'default_address' => $defaultAddress,
            'locations' => $locations,
            'payment_methods' => $paymentMethods,
            'step' => 1 // Paso actual del checkout
        ];

        return view('frontend/checkout', $data);
    }

    /**
     * Calcular métodos de envío para una dirección
     */
    public function calculateShippingForAddress()
    {
        try {
            $addressId = $this->request->getPost('address_id');
            
            if (!$addressId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de dirección requerido'
                ]);
            }

            // Obtener información de la zona de envío
            $shippingZone = $this->addressModel->calculateShippingZone($addressId);
            
            if (!$shippingZone) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'No se pudo determinar la zona de envío'
                ]);
            }

            // Obtener productos del carrito para calcular dimensiones
            $cartItems = $this->getCartItems();
            $packageInfo = $this->calculatePackageInfo($cartItems);
            
            // Determinar tipo de paquete
            $packageType = $this->packageTypeModel->determinePackageType(
                $packageInfo['max_length'],
                $packageInfo['max_width'], 
                $packageInfo['max_height'],
                $packageInfo['total_weight']
            );

            if (!$packageType) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'No se pudo determinar el tipo de paquete'
                ]);
            }

            // Calcular costo de envío
            $shippingCost = $this->packageTypeModel->calculateShippingCost(
                $packageType['id'],
                $shippingZone['distance_km'],
                $shippingZone['additional_cost']
            );

            // Verificar envío gratis
            $cartTotals = $this->calculateCartTotals($cartItems);
            $freeShippingEnabled = $this->getFreeShippingSettings();
            
            $shippingMethods = [];
            
            // Siempre incluir retiro en tienda
            $shippingMethods[] = [
                'id' => 'pickup',
                'name' => 'Retiro en Tienda',
                'description' => 'Sin costo adicional',
                'cost' => 0.00,
                'estimated_days' => 'Inmediato'
            ];

            // Verificar envío gratis
            if ($freeShippingEnabled['enabled'] && 
                $cartTotals['subtotal'] >= $freeShippingEnabled['threshold']) {
                $shippingMethods[] = [
                    'id' => 'free',
                    'name' => 'Envío Gratis',
                    'description' => 'Compra mayor a Q' . number_format($freeShippingEnabled['threshold'], 0),
                    'cost' => 0.00,
                    'estimated_days' => '3-5 días'
                ];
            } else {
                // Agregar método de envío calculado
                $shippingMethods[] = [
                    'id' => 'calculated',
                    'name' => $packageType['name'] . ' - ' . $shippingZone['zone_name'],
                    'description' => $this->getShippingDescription($packageType, $shippingZone),
                    'cost' => $shippingCost,
                    'estimated_days' => '2-5 días'
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'shipping_methods' => $shippingMethods,
                'package_info' => [
                    'type' => $packageType['name'],
                    'dimensions' => $packageInfo['max_length'] . 'x' . $packageInfo['max_width'] . 'x' . $packageInfo['max_height'] . ' cm',
                    'weight' => $packageInfo['total_weight'] . ' lbs'
                ],
                'zone_info' => $shippingZone
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al calcular envío: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Guardar nueva dirección
     */
    public function saveAddress()
    {
        try {
            $data = [
                'name' => $this->request->getPost('name'),
                'recipient_name' => $this->request->getPost('recipient_name'),
                'phone' => $this->request->getPost('phone'),
                'email' => $this->request->getPost('email'),
                'address_line_1' => $this->request->getPost('address_line_1'),
                'address_line_2' => $this->request->getPost('address_line_2'),
                'city' => $this->request->getPost('city'),
                'state' => $this->request->getPost('state'),
                'postal_code' => $this->request->getPost('postal_code'),
                'country' => 'Guatemala'
            ];

            $userId = session()->get('user_id');
            $sessionId = session()->getId();

            $addressId = $this->addressModel->createAddress($data, $userId, $sessionId);

            if ($addressId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Dirección guardada correctamente',
                    'address_id' => $addressId
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al guardar la dirección',
                    'validation_errors' => $this->addressModel->errors()
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al guardar dirección: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Procesar el pedido
     */
    public function processOrder()
    {
        try {
            $addressId = $this->request->getPost('address_id');
            $shippingMethodId = $this->request->getPost('shipping_method');
            $shippingCost = floatval($this->request->getPost('shipping_cost'));
            $paymentMethod = $this->request->getPost('payment_method');

            // Validaciones básicas
            if (!$addressId || !$shippingMethodId || !$paymentMethod) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Faltan datos requeridos para procesar el pedido'
                ]);
            }

            // Si es pago con Recurrente, redirigir al procesamiento de pago
            if ($paymentMethod === 'recurrente') {
                return $this->processRecurrentePayment($addressId, $shippingMethodId, $shippingCost);
            }

            // Obtener datos del carrito
            $cartItems = $this->getCartItems();
            if (empty($cartItems)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'El carrito está vacío'
                ]);
            }

            // Calcular totales
            $cartTotals = $this->calculateCartTotals($cartItems);
            $finalTotal = $cartTotals['total'] + $shippingCost;

            // Crear el pedido (aquí integrarías con tu sistema de órdenes)
            $orderData = [
                'user_id' => session()->get('user_id'),
                'session_id' => session_id(),
                'address_id' => $addressId,
                'shipping_method' => $shippingMethodId,
                'shipping_cost' => $shippingCost,
                'payment_method' => $paymentMethod,
                'subtotal' => $cartTotals['subtotal'],
                'tax_amount' => $cartTotals['tax'],
                'total_amount' => $finalTotal,
                'items' => $cartItems,
                'status' => 'pending'
            ];

            // Aquí llamarías a tu modelo de órdenes para crear el pedido
            // $orderId = $this->orderModel->createOrder($orderData);

            // Por ahora, simular creación exitosa
            $orderId = 'MRC-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));

            // Limpiar carrito
            session()->remove('cart');

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Pedido creado correctamente',
                'order_id' => $orderId,
                'redirect_url' => base_url('checkout/confirmation/' . $orderId)
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al procesar pedido: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Procesar pago con Recurrente
     */
    private function processRecurrentePayment($addressId, $shippingMethodId, $shippingCost)
    {
        try {
            // Cargar el servicio de Recurrente
            $recurrenteService = new \App\Services\RecurrenteService();

            if (!$recurrenteService->isEnabled()) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Recurrente no está configurado o habilitado'
                ]);
            }

            // Obtener datos del carrito
            $cartItems = $this->getCartItems();
            if (empty($cartItems)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'El carrito está vacío'
                ]);
            }

            // Calcular totales
            $cartTotals = $this->calculateCartTotals($cartItems);
            $finalTotal = $cartTotals['total'] + $shippingCost;

            // Crear datos del pedido para Recurrente
            $orderData = [
                'total' => $finalTotal,
                'order_number' => 'MRC-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6)),
                'customer_name' => session()->get('user_name') ?? 'Cliente',
                'customer_email' => session()->get('user_email') ?? '<EMAIL>',
                'customer_phone' => session()->get('user_phone') ?? '',
                'id' => uniqid('order_', true)
            ];

            // Crear el pedido en la base de datos ANTES de crear el checkout
            $orderModel = new \App\Models\OrderModel();

            $orderRecord = [
                'external_id' => $orderData['id'], // Guardar el ID temporal
                'order_number' => $orderData['order_number'],
                'customer_id' => session()->get('user_id') ?? 0,
                'customer_name' => $orderData['customer_name'],
                'customer_email' => $orderData['customer_email'],
                'customer_phone' => $orderData['customer_phone'],
                'subtotal' => $cartTotals['subtotal'],
                'tax_amount' => $cartTotals['tax'],
                'shipping_cost' => $shippingCost,
                'total' => $finalTotal,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => 'recurrente',
                'source' => 'web',
                'created_by' => session()->get('user_id') ?? 0
            ];

            $orderId = $orderModel->insert($orderRecord);

            if (!$orderId) {
                throw new \Exception('Error creando el pedido en la base de datos');
            }

            log_message('info', "Pedido creado con ID {$orderId} y external_id {$orderData['id']}");

            // Crear checkout en Recurrente
            $checkout = $recurrenteService->createCheckout($orderData, $cartItems);

            // Debug: Log la respuesta de Recurrente
            log_message('info', 'Respuesta completa de Recurrente: ' . json_encode($checkout));

            // La API de Recurrente devuelve diferentes campos según la documentación
            if (isset($checkout['checkout_url']) || isset($checkout['url']) || isset($checkout['hosted_checkout_url'])) {
                $checkoutUrl = $checkout['checkout_url'] ?? $checkout['url'] ?? $checkout['hosted_checkout_url'];
                log_message('info', 'URL de checkout encontrada: ' . $checkoutUrl);

                // Crear registro de pago pendiente
                $paymentModel = new \App\Models\PaymentModel();
                $paymentData = [
                    'order_id' => $orderId,
                    'payment_method' => 'recurrente',
                    'amount' => $finalTotal,
                    'currency' => 'GTQ',
                    'status' => 'pending',
                    'reference_number' => $checkout['id'] ?? 'REC_' . time(),
                    'transaction_id' => $checkout['id'] ?? null,
                    'gateway_response' => json_encode($checkout),
                    'notes' => 'Checkout creado - Pendiente de pago',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $paymentModel->insert($paymentData);

                // Guardar datos adicionales en sesión para referencia
                session()->set('pending_order', [
                    'order_id' => $orderId,
                    'external_id' => $orderData['id'],
                    'checkout_id' => $checkout['id'] ?? null
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'redirect_url' => $checkoutUrl,
                    'message' => 'Redirigiendo a Recurrente para procesar el pago...'
                ]);
            } else {
                log_message('error', 'No se encontró URL de checkout en la respuesta de Recurrente: ' . json_encode($checkout));
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al crear el checkout en Recurrente - No se recibió URL de pago',
                    'debug_info' => ENVIRONMENT === 'development' ? $checkout : null
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error procesando pago Recurrente: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al procesar el pago: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Página de confirmación
     */
    public function confirmation($orderId = null)
    {
        if (!$orderId) {
            return redirect()->to('/');
        }

        $data = [
            'title' => 'Pedido Confirmado - MrCell Guatemala',
            'order_id' => $orderId
        ];

        return view('frontend/checkout_confirmation', $data);
    }

    /**
     * Obtener productos del carrito con detalles
     */
    private function getCartItems()
    {
        $cart = session()->get('cart') ?? [];
        $cartItems = [];

        foreach ($cart as $productId => $item) {
            $product = $this->productModel->find($productId);
            if ($product) {
                // Calcular precio final (usar precio de oferta si existe, sino precio regular)
                $finalPrice = (!empty($product['price_sale']) && $product['price_sale'] > 0)
                    ? $product['price_sale']
                    : $product['price_regular'];

                $cartItems[] = [
                    'product_id' => $productId,
                    'name' => $product['name'],
                    'price' => $finalPrice,
                    'price_regular' => $product['price_regular'],
                    'price_sale' => $product['price_sale'] ?? null,
                    'quantity' => $item['quantity'],
                    'subtotal' => $finalPrice * $item['quantity'],
                    'image' => $product['featured_image'] ?? 'default.jpg',
                    'weight' => $product['weight'] ?? 1,
                    'length' => $product['dimension_length'] ?? 10,
                    'width' => $product['dimension_width'] ?? 10,
                    'height' => $product['dimension_height'] ?? 5
                ];
            }
        }

        return $cartItems;
    }

    /**
     * Calcular totales del carrito
     */
    private function calculateCartTotals($cartItems)
    {
        $subtotal = 0;
        foreach ($cartItems as $item) {
            $subtotal += $item['subtotal'];
        }

        // Obtener configuración de impuestos
        $taxSettings = $this->getTaxSettings();
        $tax = 0;
        if ($taxSettings['tax_enabled']) {
            $tax = $subtotal * ($taxSettings['tax_rate'] / 100);
        }

        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'total' => $subtotal + $tax,
            'total_before_shipping' => $subtotal + $tax
        ];
    }

    /**
     * Calcular información del paquete basado en productos del carrito
     */
    private function calculatePackageInfo($cartItems)
    {
        $totalWeight = 0;
        $maxLength = 0;
        $maxWidth = 0;
        $maxHeight = 0;

        foreach ($cartItems as $item) {
            $totalWeight += ($item['weight'] ?? 1) * $item['quantity'];
            $maxLength = max($maxLength, $item['length'] ?? 10);
            $maxWidth = max($maxWidth, $item['width'] ?? 10);
            $maxHeight = max($maxHeight, $item['height'] ?? 5);
        }

        return [
            'total_weight' => $totalWeight,
            'max_length' => $maxLength,
            'max_width' => $maxWidth,
            'max_height' => $maxHeight
        ];
    }

    /**
     * Obtener configuración de envío gratis
     */
    private function getFreeShippingSettings()
    {
        $db = \Config\Database::connect();

        try {
            $settings = $db->query("
                SELECT setting_key, setting_value
                FROM system_settings
                WHERE setting_group = 'shipping' AND is_active = 1
            ")->getResultArray();

            $shippingSettings = [];
            foreach ($settings as $setting) {
                $shippingSettings[$setting['setting_key']] = $setting['setting_value'];
            }

            return [
                'enabled' => ($shippingSettings['free_shipping_enabled'] ?? '1') == '1',
                'threshold' => floatval($shippingSettings['free_shipping_threshold'] ?? 500)
            ];

        } catch (\Exception $e) {
            return [
                'enabled' => true,
                'threshold' => 500
            ];
        }
    }

    /**
     * Obtener configuración de impuestos
     */
    private function getTaxSettings()
    {
        $db = \Config\Database::connect();

        try {
            $settings = $db->query("
                SELECT setting_key, setting_value, setting_type
                FROM system_settings
                WHERE setting_group = 'taxes' AND is_active = 1
            ")->getResultArray();

            $taxSettings = [];
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];
                if ($setting['setting_type'] === 'checkbox') {
                    $value = (bool) $value;
                } elseif ($setting['setting_type'] === 'number') {
                    $value = (float) $value;
                }
                $taxSettings[$setting['setting_key']] = $value;
            }

            return $taxSettings;

        } catch (\Exception $e) {
            return [
                'tax_enabled' => false,
                'tax_rate' => 0,
                'tax_name' => 'IVA'
            ];
        }
    }

    /**
     * Generar descripción de envío
     */
    private function getShippingDescription($packageType, $shippingZone)
    {
        $timeMap = [
            'Ciudad de Guatemala - Centro' => '2-3 días',
            'Ciudad de Guatemala - Zonas' => '3-4 días',
            'Municipios Cercanos' => '4-5 días'
        ];

        $timeDescription = $timeMap[$shippingZone['zone_name']] ?? '3-5 días';
        $sizeDescription = 'hasta ' . $packageType['max_length_cm'] . 'cm';

        return $timeDescription . ' • ' . $sizeDescription;
    }

    /**
     * Obtener métodos de pago activos
     */
    private function getActivePaymentMethods($cartTotal = null, $cartCurrency = 'GTQ')
    {
        try {
            $db = \Config\Database::connect();

            $paymentMethods = $db->query("
                SELECT id, name, slug, description, type, icon, instructions
                FROM payment_methods
                WHERE is_active = 1
                ORDER BY sort_order, name
            ")->getResultArray();

            // Filtrar Recurrente si excede el límite de precio
            foreach ($paymentMethods as $key => $method) {
                if ($method['slug'] === 'recurrente' && $cartTotal !== null) {
                    if (!$this->isRecurrenteEligible($cartTotal, $cartCurrency)) {
                        // Marcar como no disponible pero mantener en la lista con mensaje
                        $paymentMethods[$key]['available'] = false;
                        $paymentMethods[$key]['unavailable_reason'] = $this->getRecurrentePriceLimitMessage($cartTotal, $cartCurrency);
                    } else {
                        $paymentMethods[$key]['available'] = true;
                    }
                } else {
                    $paymentMethods[$key]['available'] = true;
                }
            }

            return $paymentMethods;
        } catch (\Exception $e) {
            log_message('error', 'Error getting payment methods: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Verificar si Recurrente es elegible para el carrito
     */
    private function isRecurrenteEligible($cartTotal, $cartCurrency = 'GTQ')
    {
        try {
            // Verificar límite de precio de Recurrente ($15,000 USD)
            $maxPriceUSD = 15000;
            $amountInUSD = $cartTotal;

            // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
            if ($cartCurrency === 'GTQ') {
                $amountInUSD = $cartTotal / 7.8;
            }

            return $amountInUSD <= $maxPriceUSD;

        } catch (\Exception $e) {
            log_message('error', 'Error verificando elegibilidad de Recurrente: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtener mensaje sobre límite de precio de Recurrente
     */
    private function getRecurrentePriceLimitMessage($cartTotal, $cartCurrency = 'GTQ')
    {
        $maxPriceUSD = 15000;
        $maxPriceGTQ = $maxPriceUSD * 7.8;

        if ($cartCurrency === 'GTQ') {
            return "El total del carrito (Q" . number_format($cartTotal, 2) . ") excede el límite de Q" . number_format($maxPriceGTQ, 0) . " para pagos con Recurrente.";
        } else {
            return "El total del carrito ($" . number_format($cartTotal, 2) . ") excede el límite de $" . number_format($maxPriceUSD, 0) . " USD para pagos con Recurrente.";
        }
    }

}
