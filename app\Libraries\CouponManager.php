<?php

namespace App\Libraries;

/**
 * Gestor de Cupones y Descuentos Avanzado
 * Sistema completo de cupones, promociones y campañas de marketing
 */
class CouponManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('COUPONS_ENABLED', true),
            'auto_apply' => env('COUPONS_AUTO_APPLY', true),
            'max_uses_per_user' => env('COUPONS_MAX_USES_PER_USER', 5),
            'max_discount_percentage' => env('COUPONS_MAX_DISCOUNT_PERCENTAGE', 90),
            'min_order_amount' => env('COUPONS_MIN_ORDER_AMOUNT', 10),
            'cache_ttl' => env('COUPONS_CACHE_TTL', 3600),
            'bulk_discount_enabled' => env('BULK_DISCOUNT_ENABLED', true),
            'loyalty_discount_enabled' => env('LOYALTY_DISCOUNT_ENABLED', true),
            'seasonal_promotions' => env('SEASONAL_PROMOTIONS_ENABLED', true)
        ];
        
        $this->createCouponTables();
    }
    
    /**
     * Crear cupón
     */
    public function createCoupon(array $couponData): array
    {
        try {
            // Validar datos del cupón
            $validation = $this->validateCouponData($couponData);
            if (!$validation['valid']) {
                return ['success' => false, 'errors' => $validation['errors']];
            }
            
            // Generar código si no se proporciona
            if (empty($couponData['code'])) {
                $couponData['code'] = $this->generateCouponCode();
            }
            
            // Verificar que el código no exista
            if ($this->couponCodeExists($couponData['code'])) {
                return ['success' => false, 'error' => 'Coupon code already exists'];
            }
            
            $data = [
                'code' => strtoupper($couponData['code']),
                'name' => $couponData['name'],
                'description' => $couponData['description'] ?? '',
                'type' => $couponData['type'], // percentage, fixed, free_shipping, buy_x_get_y
                'value' => $couponData['value'],
                'min_order_amount' => $couponData['min_order_amount'] ?? 0,
                'max_discount_amount' => $couponData['max_discount_amount'] ?? null,
                'usage_limit' => $couponData['usage_limit'] ?? null,
                'usage_limit_per_user' => $couponData['usage_limit_per_user'] ?? $this->config['max_uses_per_user'],
                'valid_from' => $couponData['valid_from'] ?? date('Y-m-d H:i:s'),
                'valid_until' => $couponData['valid_until'] ?? null,
                'applicable_products' => json_encode($couponData['applicable_products'] ?? []),
                'applicable_categories' => json_encode($couponData['applicable_categories'] ?? []),
                'excluded_products' => json_encode($couponData['excluded_products'] ?? []),
                'excluded_categories' => json_encode($couponData['excluded_categories'] ?? []),
                'first_order_only' => $couponData['first_order_only'] ?? 0,
                'stackable' => $couponData['stackable'] ?? 0,
                'is_active' => $couponData['is_active'] ?? 1,
                'created_by' => $couponData['created_by'] ?? 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $couponId = $this->db->table('coupons')->insert($data);
            
            if ($couponId) {
                // Limpiar cache
                $this->cache->delete('active_coupons');
                
                $this->logger->info("Coupon created", [
                    'coupon_id' => $couponId,
                    'code' => $data['code'],
                    'type' => $data['type'],
                    'value' => $data['value']
                ]);
                
                return [
                    'success' => true,
                    'coupon_id' => $couponId,
                    'code' => $data['code']
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create coupon'];
            
        } catch (\Exception $e) {
            $this->logger->error("Coupon creation error: " . $e->getMessage(), $couponData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Aplicar cupón a orden
     */
    public function applyCoupon(string $couponCode, array $orderData, int $userId = null): array
    {
        try {
            if (!$this->config['enabled']) {
                return ['success' => false, 'error' => 'Coupons are disabled'];
            }
            
            // Obtener cupón
            $coupon = $this->getCouponByCode($couponCode);
            
            if (!$coupon) {
                return ['success' => false, 'error' => 'Coupon not found'];
            }
            
            // Validar cupón
            $validation = $this->validateCouponUsage($coupon, $orderData, $userId);
            if (!$validation['valid']) {
                return ['success' => false, 'error' => $validation['error']];
            }
            
            // Calcular descuento
            $discount = $this->calculateDiscount($coupon, $orderData);
            
            if ($discount['amount'] <= 0) {
                return ['success' => false, 'error' => 'No discount applicable'];
            }
            
            // Registrar uso del cupón
            $this->recordCouponUsage($coupon['id'], $userId, $discount['amount']);
            
            $this->logger->info("Coupon applied", [
                'coupon_code' => $couponCode,
                'user_id' => $userId,
                'discount_amount' => $discount['amount'],
                'order_total' => $orderData['total']
            ]);
            
            return [
                'success' => true,
                'coupon' => $coupon,
                'discount' => $discount,
                'new_total' => $orderData['total'] - $discount['amount']
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Coupon application error: " . $e->getMessage(), [
                'coupon_code' => $couponCode,
                'user_id' => $userId
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener cupones automáticos aplicables
     */
    public function getAutomaticCoupons(array $orderData, int $userId = null): array
    {
        if (!$this->config['auto_apply']) {
            return [];
        }
        
        try {
            $applicableCoupons = [];
            $activeCoupons = $this->getActiveCoupons();
            
            foreach ($activeCoupons as $coupon) {
                // Solo cupones automáticos
                if (!$coupon['auto_apply']) {
                    continue;
                }
                
                $validation = $this->validateCouponUsage($coupon, $orderData, $userId);
                if ($validation['valid']) {
                    $discount = $this->calculateDiscount($coupon, $orderData);
                    
                    if ($discount['amount'] > 0) {
                        $applicableCoupons[] = [
                            'coupon' => $coupon,
                            'discount' => $discount
                        ];
                    }
                }
            }
            
            // Ordenar por mayor descuento
            usort($applicableCoupons, function($a, $b) {
                return $b['discount']['amount'] <=> $a['discount']['amount'];
            });
            
            return $applicableCoupons;
            
        } catch (\Exception $e) {
            $this->logger->error("Automatic coupons error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Crear promoción por volumen
     */
    public function createBulkDiscount(array $discountData): array
    {
        try {
            $data = [
                'name' => $discountData['name'],
                'description' => $discountData['description'] ?? '',
                'min_quantity' => $discountData['min_quantity'],
                'discount_type' => $discountData['discount_type'], // percentage, fixed_per_item
                'discount_value' => $discountData['discount_value'],
                'applicable_products' => json_encode($discountData['applicable_products'] ?? []),
                'applicable_categories' => json_encode($discountData['applicable_categories'] ?? []),
                'valid_from' => $discountData['valid_from'] ?? date('Y-m-d H:i:s'),
                'valid_until' => $discountData['valid_until'] ?? null,
                'is_active' => $discountData['is_active'] ?? 1,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $discountId = $this->db->table('bulk_discounts')->insert($data);
            
            if ($discountId) {
                $this->cache->delete('bulk_discounts');
                
                $this->logger->info("Bulk discount created", [
                    'discount_id' => $discountId,
                    'min_quantity' => $data['min_quantity'],
                    'discount_value' => $data['discount_value']
                ]);
                
                return [
                    'success' => true,
                    'discount_id' => $discountId
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create bulk discount'];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Aplicar descuentos por volumen
     */
    public function applyBulkDiscounts(array $orderItems): array
    {
        if (!$this->config['bulk_discount_enabled']) {
            return ['discounts' => [], 'total_discount' => 0];
        }
        
        try {
            $bulkDiscounts = $this->getActiveBulkDiscounts();
            $appliedDiscounts = [];
            $totalDiscount = 0;
            
            foreach ($bulkDiscounts as $discount) {
                $applicableItems = $this->getApplicableItems($orderItems, $discount);
                $totalQuantity = array_sum(array_column($applicableItems, 'quantity'));
                
                if ($totalQuantity >= $discount['min_quantity']) {
                    $discountAmount = $this->calculateBulkDiscount($applicableItems, $discount);
                    
                    if ($discountAmount > 0) {
                        $appliedDiscounts[] = [
                            'discount' => $discount,
                            'amount' => $discountAmount,
                            'items_affected' => count($applicableItems)
                        ];
                        
                        $totalDiscount += $discountAmount;
                    }
                }
            }
            
            return [
                'discounts' => $appliedDiscounts,
                'total_discount' => $totalDiscount
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Bulk discount application error: " . $e->getMessage());
            
            return [
                'discounts' => [],
                'total_discount' => 0
            ];
        }
    }
    
    /**
     * Crear campaña promocional
     */
    public function createPromotionalCampaign(array $campaignData): array
    {
        try {
            $data = [
                'name' => $campaignData['name'],
                'description' => $campaignData['description'] ?? '',
                'type' => $campaignData['type'], // seasonal, flash_sale, clearance, new_customer
                'discount_type' => $campaignData['discount_type'],
                'discount_value' => $campaignData['discount_value'],
                'target_audience' => $campaignData['target_audience'] ?? 'all', // all, new_customers, returning_customers, vip
                'min_order_amount' => $campaignData['min_order_amount'] ?? 0,
                'max_uses' => $campaignData['max_uses'] ?? null,
                'start_date' => $campaignData['start_date'],
                'end_date' => $campaignData['end_date'],
                'banner_text' => $campaignData['banner_text'] ?? '',
                'banner_color' => $campaignData['banner_color'] ?? '#ff6b6b',
                'email_template' => $campaignData['email_template'] ?? '',
                'auto_generate_coupons' => $campaignData['auto_generate_coupons'] ?? 0,
                'is_active' => $campaignData['is_active'] ?? 1,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $campaignId = $this->db->table('promotional_campaigns')->insert($data);
            
            if ($campaignId) {
                // Auto-generar cupones si está habilitado
                if ($data['auto_generate_coupons']) {
                    $this->generateCampaignCoupons($campaignId, $data);
                }
                
                $this->logger->info("Promotional campaign created", [
                    'campaign_id' => $campaignId,
                    'name' => $data['name'],
                    'type' => $data['type']
                ]);
                
                return [
                    'success' => true,
                    'campaign_id' => $campaignId
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create campaign'];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de cupones
     */
    public function getCouponStats(int $days = 30): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Cupones más usados
            $topCoupons = $this->db->query("
                SELECT c.code, c.name, COUNT(cu.id) as uses, SUM(cu.discount_amount) as total_discount
                FROM coupons c
                LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
                WHERE cu.created_at >= ?
                GROUP BY c.id, c.code, c.name
                ORDER BY uses DESC
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Descuento total otorgado
            $totalDiscount = $this->db->table('coupon_usage')
                                     ->where('created_at >=', $dateFrom)
                                     ->selectSum('discount_amount')
                                     ->get()
                                     ->getRowArray()['discount_amount'] ?? 0;
            
            // Cupones activos
            $activeCoupons = $this->db->table('coupons')
                                     ->where('is_active', 1)
                                     ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                                     ->countAllResults();
            
            // Uso por tipo de cupón
            $usageByType = $this->db->query("
                SELECT c.type, COUNT(cu.id) as uses, SUM(cu.discount_amount) as total_discount
                FROM coupons c
                LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
                WHERE cu.created_at >= ?
                GROUP BY c.type
                ORDER BY uses DESC
            ", [$dateFrom])->getResultArray();
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => [
                    'top_coupons' => $topCoupons,
                    'total_discount_given' => $totalDiscount,
                    'active_coupons' => $activeCoupons,
                    'usage_by_type' => $usageByType,
                    'conversion_rate' => $this->calculateCouponConversionRate($dateFrom)
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function validateCouponData(array $data): array
    {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors[] = 'Coupon name is required';
        }
        
        if (empty($data['type']) || !in_array($data['type'], ['percentage', 'fixed', 'free_shipping', 'buy_x_get_y'])) {
            $errors[] = 'Valid coupon type is required';
        }
        
        if (!isset($data['value']) || $data['value'] <= 0) {
            $errors[] = 'Coupon value must be greater than 0';
        }
        
        if ($data['type'] === 'percentage' && $data['value'] > $this->config['max_discount_percentage']) {
            $errors[] = "Percentage discount cannot exceed {$this->config['max_discount_percentage']}%";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    private function generateCouponCode(): string
    {
        do {
            $code = 'SAVE' . strtoupper(bin2hex(random_bytes(4)));
        } while ($this->couponCodeExists($code));
        
        return $code;
    }
    
    private function couponCodeExists(string $code): bool
    {
        return $this->db->table('coupons')
                       ->where('code', strtoupper($code))
                       ->countAllResults() > 0;
    }
    
    private function getCouponByCode(string $code): ?array
    {
        return $this->db->table('coupons')
                       ->where('code', strtoupper($code))
                       ->where('is_active', 1)
                       ->get()
                       ->getRowArray();
    }
    
    private function validateCouponUsage(array $coupon, array $orderData, ?int $userId): array
    {
        // Verificar si está activo
        if (!$coupon['is_active']) {
            return ['valid' => false, 'error' => 'Coupon is not active'];
        }
        
        // Verificar fechas de validez
        $now = date('Y-m-d H:i:s');
        if ($coupon['valid_from'] && $coupon['valid_from'] > $now) {
            return ['valid' => false, 'error' => 'Coupon is not yet valid'];
        }
        
        if ($coupon['valid_until'] && $coupon['valid_until'] < $now) {
            return ['valid' => false, 'error' => 'Coupon has expired'];
        }
        
        // Verificar monto mínimo
        if ($coupon['min_order_amount'] > 0 && $orderData['total'] < $coupon['min_order_amount']) {
            return ['valid' => false, 'error' => "Minimum order amount is {$coupon['min_order_amount']}"];
        }
        
        // Verificar límite de uso total
        if ($coupon['usage_limit']) {
            $totalUses = $this->getCouponUsageCount($coupon['id']);
            if ($totalUses >= $coupon['usage_limit']) {
                return ['valid' => false, 'error' => 'Coupon usage limit reached'];
            }
        }
        
        // Verificar límite de uso por usuario
        if ($userId && $coupon['usage_limit_per_user']) {
            $userUses = $this->getCouponUsageCount($coupon['id'], $userId);
            if ($userUses >= $coupon['usage_limit_per_user']) {
                return ['valid' => false, 'error' => 'User usage limit reached'];
            }
        }
        
        // Verificar si es solo para primera orden
        if ($coupon['first_order_only'] && $userId) {
            $orderCount = $this->getUserOrderCount($userId);
            if ($orderCount > 0) {
                return ['valid' => false, 'error' => 'Coupon is for first order only'];
            }
        }
        
        return ['valid' => true];
    }
    
    private function calculateDiscount(array $coupon, array $orderData): array
    {
        $discount = ['amount' => 0, 'type' => $coupon['type']];
        
        switch ($coupon['type']) {
            case 'percentage':
                $discount['amount'] = ($orderData['total'] * $coupon['value']) / 100;
                break;
                
            case 'fixed':
                $discount['amount'] = min($coupon['value'], $orderData['total']);
                break;
                
            case 'free_shipping':
                $discount['amount'] = $orderData['shipping_cost'] ?? 0;
                break;
                
            case 'buy_x_get_y':
                // Lógica más compleja para buy X get Y
                $discount['amount'] = $this->calculateBuyXGetYDiscount($coupon, $orderData);
                break;
        }
        
        // Aplicar límite máximo de descuento
        if ($coupon['max_discount_amount'] && $discount['amount'] > $coupon['max_discount_amount']) {
            $discount['amount'] = $coupon['max_discount_amount'];
        }
        
        return $discount;
    }
    
    private function calculateBuyXGetYDiscount(array $coupon, array $orderData): float
    {
        // Implementación simplificada
        return 0;
    }
    
    private function recordCouponUsage(int $couponId, ?int $userId, float $discountAmount): void
    {
        $this->db->table('coupon_usage')->insert([
            'coupon_id' => $couponId,
            'user_id' => $userId,
            'discount_amount' => $discountAmount,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    private function getActiveCoupons(): array
    {
        $cacheKey = 'active_coupons';
        $coupons = $this->cache->get($cacheKey);
        
        if ($coupons === null) {
            $coupons = $this->db->table('coupons')
                               ->where('is_active', 1)
                               ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                               ->get()
                               ->getResultArray();
            
            $this->cache->set($cacheKey, $coupons, $this->config['cache_ttl']);
        }
        
        return $coupons;
    }
    
    private function getActiveBulkDiscounts(): array
    {
        $cacheKey = 'bulk_discounts';
        $discounts = $this->cache->get($cacheKey);
        
        if ($discounts === null) {
            $discounts = $this->db->table('bulk_discounts')
                                 ->where('is_active', 1)
                                 ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                                 ->get()
                                 ->getResultArray();
            
            $this->cache->set($cacheKey, $discounts, $this->config['cache_ttl']);
        }
        
        return $discounts;
    }
    
    private function getApplicableItems(array $orderItems, array $discount): array
    {
        // Filtrar items aplicables según productos/categorías
        return $orderItems; // Implementación simplificada
    }
    
    private function calculateBulkDiscount(array $items, array $discount): float
    {
        $totalDiscount = 0;
        
        foreach ($items as $item) {
            if ($discount['discount_type'] === 'percentage') {
                $totalDiscount += ($item['price'] * $item['quantity'] * $discount['discount_value']) / 100;
            } else {
                $totalDiscount += $discount['discount_value'] * $item['quantity'];
            }
        }
        
        return $totalDiscount;
    }
    
    private function generateCampaignCoupons(int $campaignId, array $campaignData): void
    {
        // Generar cupones automáticamente para la campaña
        $couponData = [
            'name' => $campaignData['name'] . ' - Auto Generated',
            'type' => $campaignData['discount_type'],
            'value' => $campaignData['discount_value'],
            'min_order_amount' => $campaignData['min_order_amount'],
            'valid_from' => $campaignData['start_date'],
            'valid_until' => $campaignData['end_date'],
            'usage_limit' => $campaignData['max_uses'],
            'auto_apply' => 1
        ];
        
        $this->createCoupon($couponData);
    }
    
    private function getCouponUsageCount(int $couponId, ?int $userId = null): int
    {
        $builder = $this->db->table('coupon_usage')
                           ->where('coupon_id', $couponId);
        
        if ($userId) {
            $builder->where('user_id', $userId);
        }
        
        return $builder->countAllResults();
    }
    
    private function getUserOrderCount(int $userId): int
    {
        return $this->db->table('orders')
                       ->where('user_id', $userId)
                       ->where('status !=', 'cancelled')
                       ->countAllResults();
    }
    
    private function calculateCouponConversionRate(string $dateFrom): float
    {
        $totalCouponUses = $this->db->table('coupon_usage')
                                   ->where('created_at >=', $dateFrom)
                                   ->countAllResults();
        
        $totalOrders = $this->db->table('orders')
                               ->where('created_at >=', $dateFrom)
                               ->where('status !=', 'cancelled')
                               ->countAllResults();
        
        return $totalOrders > 0 ? ($totalCouponUses / $totalOrders) * 100 : 0;
    }
    
    /**
     * Crear tablas de cupones
     */
    private function createCouponTables(): void
    {
        try {
            // Tabla de cupones
            $this->db->query("
                CREATE TABLE IF NOT EXISTS coupons (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    type ENUM('percentage', 'fixed', 'free_shipping', 'buy_x_get_y') NOT NULL,
                    value DECIMAL(10,2) NOT NULL,
                    min_order_amount DECIMAL(10,2) DEFAULT 0,
                    max_discount_amount DECIMAL(10,2) NULL,
                    usage_limit INT NULL,
                    usage_limit_per_user INT DEFAULT 1,
                    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    valid_until TIMESTAMP NULL,
                    applicable_products JSON,
                    applicable_categories JSON,
                    excluded_products JSON,
                    excluded_categories JSON,
                    first_order_only TINYINT(1) DEFAULT 0,
                    stackable TINYINT(1) DEFAULT 0,
                    auto_apply TINYINT(1) DEFAULT 0,
                    is_active TINYINT(1) DEFAULT 1,
                    created_by INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_code (code),
                    INDEX idx_is_active (is_active),
                    INDEX idx_valid_until (valid_until)
                )
            ");
            
            // Tabla de uso de cupones
            $this->db->query("
                CREATE TABLE IF NOT EXISTS coupon_usage (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    coupon_id INT NOT NULL,
                    user_id INT NULL,
                    order_id INT NULL,
                    discount_amount DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_coupon_id (coupon_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE
                )
            ");
            
            // Tabla de descuentos por volumen
            $this->db->query("
                CREATE TABLE IF NOT EXISTS bulk_discounts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    min_quantity INT NOT NULL,
                    discount_type ENUM('percentage', 'fixed_per_item') NOT NULL,
                    discount_value DECIMAL(10,2) NOT NULL,
                    applicable_products JSON,
                    applicable_categories JSON,
                    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    valid_until TIMESTAMP NULL,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_is_active (is_active),
                    INDEX idx_min_quantity (min_quantity)
                )
            ");
            
            // Tabla de campañas promocionales
            $this->db->query("
                CREATE TABLE IF NOT EXISTS promotional_campaigns (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    type ENUM('seasonal', 'flash_sale', 'clearance', 'new_customer') NOT NULL,
                    discount_type ENUM('percentage', 'fixed') NOT NULL,
                    discount_value DECIMAL(10,2) NOT NULL,
                    target_audience ENUM('all', 'new_customers', 'returning_customers', 'vip') DEFAULT 'all',
                    min_order_amount DECIMAL(10,2) DEFAULT 0,
                    max_uses INT NULL,
                    start_date TIMESTAMP NOT NULL,
                    end_date TIMESTAMP NOT NULL,
                    banner_text VARCHAR(500),
                    banner_color VARCHAR(7) DEFAULT '#ff6b6b',
                    email_template TEXT,
                    auto_generate_coupons TINYINT(1) DEFAULT 0,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_is_active (is_active),
                    INDEX idx_start_date (start_date),
                    INDEX idx_end_date (end_date)
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Coupon tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'auto_apply' => $this->config['auto_apply'],
            'max_uses_per_user' => $this->config['max_uses_per_user'],
            'max_discount_percentage' => $this->config['max_discount_percentage'],
            'bulk_discount_enabled' => $this->config['bulk_discount_enabled'],
            'loyalty_discount_enabled' => $this->config['loyalty_discount_enabled']
        ];
    }
}
