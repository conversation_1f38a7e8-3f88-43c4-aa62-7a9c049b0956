<?php

namespace App\Models;

use CodeIgniter\Model;

class SystemSettingsModel extends Model
{
    protected $table = 'system_settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'setting_key',
        'setting_value',
        'setting_group',
        'display_name',
        'description',
        'setting_type',
        'options',
        'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'setting_key' => 'required|max_length[100]|is_unique[system_settings.setting_key,id,{id}]',
        'setting_value' => 'permit_empty',
        'setting_type' => 'required|in_list[text,textarea,select,checkbox,number,email,url]',
        'setting_group' => 'required|max_length[50]'
    ];

    protected $validationMessages = [
        'setting_key' => [
            'required' => 'La clave de configuración es requerida',
            'is_unique' => 'Esta clave de configuración ya existe'
        ]
    ];

    /**
     * Get a setting value by key
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return $this->castValue($setting['setting_value'], $setting['setting_type']);
    }

    /**
     * Set a setting value
     */
    public function setSetting($key, $value, $type = 'string')
    {
        $existing = $this->where('setting_key', $key)->first();
        
        $data = [
            'setting_key' => $key,
            'setting_value' => (string) $value,
            'setting_type' => $type
        ];

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Get all settings by group
     */
    public function getSettingsByGroup($group)
    {
        $settings = $this->where('setting_group', $group)->where('is_active', 1)->findAll();
        $result = [];

        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $this->castValue(
                $setting['setting_value'],
                $setting['setting_type']
            );
        }

        return $result;
    }

    /**
     * Get all tax-related settings
     */
    public function getTaxSettings()
    {
        return $this->getSettingsByGroup('taxes');
    }

    /**
     * Check if tax is enabled
     */
    public function isTaxEnabled()
    {
        return (bool) $this->getSetting('tax_enabled', false);
    }

    /**
     * Get tax rate as decimal (12% = 0.12)
     */
    public function getTaxRate()
    {
        $rate = $this->getSetting('tax_rate', 0);
        return $rate / 100; // Convert percentage to decimal
    }

    /**
     * Get tax name (IVA, IGV, etc.)
     */
    public function getTaxName()
    {
        return $this->getSetting('tax_name', 'IVA');
    }

    /**
     * Cast value to appropriate type
     */
    private function castValue($value, $type)
    {
        switch ($type) {
            case 'checkbox':
                return (bool) $value;
            case 'number':
                return is_numeric($value) ? (float) $value : 0;
            default:
                return $value;
        }
    }

    /**
     * Get active settings for admin panel
     */
    public function getActiveSettings($group = null)
    {
        $builder = $this->where('is_active', 1);

        if ($group) {
            $builder->where('setting_group', $group);
        }

        return $builder->orderBy('setting_group', 'ASC')
                      ->orderBy('setting_key', 'ASC')
                      ->findAll();
    }
}
