<!-- <PERSON>dal de Producto Agregado al Carrito -->
<div class="modal fade" id="cartModal" tabindex="-1" aria-labelledby="cartModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success text-white border-0">
                <h5 class="modal-title" id="cartModalLabel">
                    <i class="fas fa-check-circle me-2"></i>¡Producto Agregado!
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-shopping-cart text-success" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3" id="cartModalProductName">Producto agregado al carrito</h6>
                <p class="text-muted mb-4" id="cartModalMessage">El producto se ha agregado exitosamente a tu carrito de compras.</p>
                
                <!-- Información del carrito -->
                <div class="row text-center mb-4">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1" id="cartModalCount">1</h5>
                            <small class="text-muted">Productos en carrito</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1" id="cartModalTotal">Q0.00</h5>
                        <small class="text-muted">Total del carrito</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">
                    <i class="fas fa-arrow-left me-2"></i>Seguir Comprando
                </button>
                <a href="<?= base_url('carrito') ?>" class="btn btn-primary">
                    <i class="fas fa-shopping-cart me-2"></i>Ver Carrito
                </a>
            </div>
        </div>
    </div>
</div>

<style>
/* Animaciones para el modal */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Animación del icono */
@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.modal.show .fa-shopping-cart {
    animation: bounceIn 0.6s ease-out;
}

/* Efecto hover en botones */
.modal-footer .btn {
    transition: all 0.3s ease;
}

.modal-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>

<script>
// Función global para mostrar el modal del carrito
window.showCartModal = function(productName = null, cartCount = null, cartTotal = null) {
    // Actualizar contenido del modal
    if (productName) {
        document.getElementById('cartModalProductName').textContent = productName;
        document.getElementById('cartModalMessage').textContent = `"${productName}" se ha agregado exitosamente a tu carrito.`;
    }
    
    if (cartCount !== null) {
        document.getElementById('cartModalCount').textContent = cartCount;
    }
    
    if (cartTotal !== null) {
        document.getElementById('cartModalTotal').textContent = `Q${parseFloat(cartTotal).toFixed(2)}`;
    }
    
    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('cartModal'));
    modal.show();
    
    // Auto-cerrar después de 5 segundos si no hay interacción
    setTimeout(() => {
        if (document.getElementById('cartModal').classList.contains('show')) {
            modal.hide();
        }
    }, 5000);
};

// Función para actualizar solo los contadores del modal
window.updateCartModalCounters = function(cartCount, cartTotal) {
    if (cartCount !== null) {
        document.getElementById('cartModalCount').textContent = cartCount;
    }
    
    if (cartTotal !== null) {
        document.getElementById('cartModalTotal').textContent = `Q${parseFloat(cartTotal).toFixed(2)}`;
    }
};
</script>
