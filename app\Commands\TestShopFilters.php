<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestShopFilters extends BaseCommand
{
    protected $group       = 'Testing';
    protected $name        = 'shop:test-filters';
    protected $description = 'Probar filtros de la tienda';

    public function run(array $params)
    {
        CLI::write('=== PROBANDO FILTROS DE LA TIENDA ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();

        try {
            // 1. Probar categorías
            CLI::write('📂 Probando categorías...', 'white');
            $categories = $db->query("SELECT id, name FROM categories WHERE is_active = 1 LIMIT 5")->getResultArray();
            
            foreach ($categories as $category) {
                $productCount = $db->query("
                    SELECT COUNT(*) as count 
                    FROM products 
                    WHERE category_id = ? AND is_active = 1 AND deleted_at IS NULL
                ", [$category['id']])->getRowArray();
                
                CLI::write("   - {$category['name']} (ID: {$category['id']}) - {$productCount['count']} productos", 'green');
            }

            // 2. Probar marcas
            CLI::newLine();
            CLI::write('🏷️ Probando marcas...', 'white');
            $brands = $db->query("SELECT id, name FROM brands WHERE is_active = 1 LIMIT 5")->getResultArray();
            
            foreach ($brands as $brand) {
                $productCount = $db->query("
                    SELECT COUNT(*) as count 
                    FROM products 
                    WHERE brand_id = ? AND is_active = 1 AND deleted_at IS NULL
                ", [$brand['id']])->getRowArray();
                
                CLI::write("   - {$brand['name']} (ID: {$brand['id']}) - {$productCount['count']} productos", 'green');
            }

            // 3. Probar rangos de precios
            CLI::newLine();
            CLI::write('💰 Probando rangos de precios...', 'white');
            $priceStats = $db->query("
                SELECT
                    MIN(price_regular) as min_price,
                    MAX(price_regular) as max_price,
                    AVG(price_regular) as avg_price,
                    COUNT(*) as total_products
                FROM products
                WHERE is_active = 1 AND deleted_at IS NULL AND price_regular > 0
            ")->getRowArray();
            
            CLI::write("   - Precio mínimo: Q" . number_format($priceStats['min_price'], 2), 'green');
            CLI::write("   - Precio máximo: Q" . number_format($priceStats['max_price'], 2), 'green');
            CLI::write("   - Precio promedio: Q" . number_format($priceStats['avg_price'], 2), 'green');
            CLI::write("   - Total productos: {$priceStats['total_products']}", 'green');

            // 4. Probar filtros combinados
            CLI::newLine();
            CLI::write('🔍 Probando filtros combinados...', 'white');
            
            if (!empty($categories)) {
                $testCategory = $categories[0];
                $filteredProducts = $db->query("
                    SELECT COUNT(*) as count
                    FROM products p
                    LEFT JOIN categories c ON c.id = p.category_id
                    LEFT JOIN brands b ON b.id = p.brand_id
                    WHERE p.category_id = ?
                    AND p.price_regular BETWEEN 100 AND 500
                    AND p.is_active = 1
                    AND p.deleted_at IS NULL
                ", [$testCategory['id']])->getRowArray();
                
                CLI::write("   - Categoría '{$testCategory['name']}' + Precio Q100-500: {$filteredProducts['count']} productos", 'green');
            }

            // 5. Probar URLs de filtros
            CLI::newLine();
            CLI::write('🌐 URLs de prueba para filtros:', 'cyan');
            $baseUrl = base_url('tienda');
            
            if (!empty($categories)) {
                CLI::write("   - Filtro por categoría: {$baseUrl}?category={$categories[0]['id']}", 'white');
            }
            
            CLI::write("   - Filtro por precio: {$baseUrl}?price_min=1000&price_max=5000", 'white');
            CLI::write("   - Filtro por búsqueda: {$baseUrl}?search=samsung", 'white');
            
            if (!empty($categories)) {
                CLI::write("   - Filtros combinados: {$baseUrl}?category={$categories[0]['id']}&price_min=1000&price_max=3000", 'white');
            }

            // 6. Verificar APIs
            CLI::newLine();
            CLI::write('🔌 Verificando APIs...', 'white');
            
            $apiUrls = [
                'Categorías' => base_url('api/categories'),
                'Marcas' => base_url('api/brands'),
                'Productos' => base_url('api/products')
            ];
            
            foreach ($apiUrls as $name => $url) {
                CLI::write("   - API {$name}: {$url}", 'green');
            }

            CLI::newLine();
            CLI::write('✅ Prueba de filtros completada exitosamente', 'green');
            CLI::newLine();
            CLI::write('💡 Recomendaciones:', 'yellow');
            CLI::write('   1. Prueba los URLs generados en tu navegador', 'white');
            CLI::write('   2. Verifica que los checkboxes se marquen correctamente', 'white');
            CLI::write('   3. Prueba el slider de precios sin moverlo demasiado rápido', 'white');
            CLI::write('   4. Verifica que los filtros de marca funcionen después de seleccionar categoría', 'white');

        } catch (\Exception $e) {
            CLI::error('❌ Error probando filtros: ' . $e->getMessage());
            return;
        }

        CLI::newLine();
        CLI::write('=== PRUEBA COMPLETADA ===', 'yellow');
    }
}
