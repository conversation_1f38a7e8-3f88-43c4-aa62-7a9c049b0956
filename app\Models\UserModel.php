<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends BaseModel
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'uuid',
        'name',
        'username',
        'email',
        'password',
        'first_name',
        'last_name',
        'phone',
        'role',
        'status',
        'email_verified_at',
        'last_login_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'permit_empty|max_length[50]',
        'email' => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'phone' => 'required|min_length[11]|max_length[11]|is_unique[users.phone,id,{id}]',
        'status' => 'permit_empty|in_list[active,inactive,suspended,pending_verification]'
    ];

    protected $validationMessages = [
        'first_name' => [
            'required' => 'El nombre es obligatorio',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 50 caracteres'
        ],
        'last_name' => [
            'max_length' => 'El apellido no puede exceder 50 caracteres'
        ],
        'email' => [
            'required' => 'El email es obligatorio',
            'valid_email' => 'Debe proporcionar un email válido',
            'is_unique' => 'Este email ya está registrado'
        ],
        'password' => [
            'required' => 'La contraseña es obligatoria',
            'min_length' => 'La contraseña debe tener al menos 6 caracteres'
        ],
        'phone' => [
            'required' => 'El teléfono es obligatorio',
            'min_length' => 'El teléfono debe tener exactamente 11 dígitos (con código de país)',
            'max_length' => 'El teléfono debe tener exactamente 11 dígitos (con código de país)',
            'is_unique' => 'Este número de teléfono ya está registrado'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Crear nuevo usuario
     */
    public function createUser($data)
    {
        // Generate username from email or name
        $username = isset($data['email']) ? explode('@', $data['email'])[0] : 'user';
        $baseUsername = $username;
        $counter = 1;

        // Ensure username is unique
        while ($this->where('username', $username)->first()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        // Dividir el nombre completo en first_name y last_name
        $fullName = $data['name'] ?? '';
        $nameParts = explode(' ', trim($fullName), 2);
        $firstName = $nameParts[0] ?? '';
        $lastName = $nameParts[1] ?? '';

        $userData = [
            'uuid' => $this->generateUUID(),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'username' => $username,
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'phone' => $data['phone'] ?? '',
            'status' => 'active'
        ];

        $userId = $this->insert($userData);

        // Disparar evento de usuario registrado para WhatsApp
        if ($userId) {
            $userDataForEvent = [
                'id' => $userId,
                'name' => $fullName,
                'email' => $data['email'],
                'phone' => $data['phone'] ?? ''
            ];
            \App\Libraries\WhatsAppEventHandler::triggerUserRegistered($userDataForEvent);
        }

        return $userId;
    }

    /**
     * Verificar credenciales de usuario
     */
    public function verifyCredentials($email, $password)
    {
        $user = $this->where('email', $email)->first();
        
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        
        return false;
    }

    /**
     * Actualizar último login
     */
    public function updateLastLogin($userId)
    {
        return $this->update($userId, [
            'last_login_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Obtener usuario por email
     */
    public function getUserByEmail($email)
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Obtener usuario por UUID
     */
    public function getUserByUUID($uuid)
    {
        return $this->where('uuid', $uuid)->first();
    }

    /**
     * Generar UUID único
     */
    private function generateUUID()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Activar usuario
     */
    public function activateUser($userId)
    {
        return $this->update($userId, ['status' => 'active']);
    }

    /**
     * Desactivar usuario
     */
    public function deactivateUser($userId)
    {
        return $this->update($userId, ['status' => 'inactive']);
    }

    /**
     * Obtener usuarios activos
     */
    public function getActiveUsers()
    {
        return $this->where('status', 'active')->findAll();
    }

    /**
     * Buscar usuarios
     */
    public function searchUsers($term)
    {
        return $this->like('name', $term)
                   ->orLike('email', $term)
                   ->orLike('username', $term)
                   ->findAll();
    }
}
