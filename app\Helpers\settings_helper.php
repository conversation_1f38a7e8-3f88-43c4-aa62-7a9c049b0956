<?php

if (!function_exists('get_setting')) {
    /**
     * Obtener configuración del sistema desde la base de datos
     */
    function get_setting($key, $default = null)
    {
        static $settings = null;
        
        if ($settings === null) {
            $settings = [];
            try {
                $db = \Config\Database::connect();
                $results = $db->query("
                    SELECT setting_key, setting_value 
                    FROM system_settings 
                    WHERE is_active = 1
                ")->getResultArray();
                
                foreach ($results as $row) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            } catch (\Exception $e) {
                log_message('error', 'Error loading settings: ' . $e->getMessage());
            }
        }
        
        return $settings[$key] ?? $default;
    }
}

if (!function_exists('get_site_config')) {
    /**
     * Obtener configuración completa del sitio
     */
    function get_site_config()
    {
        static $config = null;
        
        if ($config === null) {
            // Configuraciones por defecto
            $defaults = [
                'site_name' => 'MrCell Guatemala',
                'site_description' => 'Tu tienda de confianza para celulares y tecnología en Guatemala',
                'site_keywords' => 'celulares Guatemala, smartphones, accesorios móviles, tecnología, MrCell',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+502 2234-5678',
                'contact_address' => 'Ciudad de Guatemala, Guatemala',
                'site_logo' => 'assets/images/logo.png',
                'site_favicon' => 'assets/images/favicon.ico',
                'currency_symbol' => 'Q',
                'currency_code' => 'GTQ',
                'timezone' => 'America/Guatemala',
                'language' => 'es'
            ];
            
            // Cargar desde base de datos y sobrescribir defaults
            foreach ($defaults as $key => $defaultValue) {
                $defaults[$key] = get_setting($key, $defaultValue);
            }
            
            $config = $defaults;
        }
        
        return $config;
    }
}

if (!function_exists('site_name')) {
    /**
     * Obtener nombre del sitio
     */
    function site_name()
    {
        return get_setting('site_name', 'MrCell Guatemala');
    }
}

if (!function_exists('site_description')) {
    /**
     * Obtener descripción del sitio
     */
    function site_description()
    {
        return get_setting('site_description', 'Tu tienda de confianza para celulares y tecnología en Guatemala');
    }
}

if (!function_exists('site_logo')) {
    /**
     * Obtener logo del sitio
     */
    function site_logo()
    {
        return get_setting('site_logo', 'assets/images/logo.png');
    }
}

if (!function_exists('contact_email')) {
    /**
     * Obtener email de contacto
     */
    function contact_email()
    {
        return get_setting('contact_email', '<EMAIL>');
    }
}

if (!function_exists('contact_phone')) {
    /**
     * Obtener teléfono de contacto
     */
    function contact_phone()
    {
        return get_setting('contact_phone', '+502 2234-5678');
    }
}

if (!function_exists('format_currency')) {
    /**
     * Formatear moneda
     */
    function format_currency($amount)
    {
        $symbol = get_setting('currency_symbol', 'Q');
        return $symbol . number_format($amount, 2);
    }
}
