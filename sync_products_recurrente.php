<?php

/**
 * Script para sincronizar productos con Recurrente
 * 
 * Este script sincroniza todos los productos activos con la plataforma Recurrente
 * 
 * Uso: php sync_products_recurrente.php [--dry-run] [--limit=N] [--force]
 */

// Definir constantes necesarias
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
define('APPPATH', __DIR__ . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR);
define('WRITEPATH', __DIR__ . DIRECTORY_SEPARATOR . 'writable' . DIRECTORY_SEPARATOR);
define('ROOTPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Cargar autoloader de Composer
require_once 'vendor/autoload.php';

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// Parsear argumentos de línea de comandos
$options = getopt('', ['dry-run', 'limit:', 'force', 'help']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

$dryRun = isset($options['dry-run']);
$limit = isset($options['limit']) ? (int)$options['limit'] : null;
$force = isset($options['force']);

echo "=== SINCRONIZACIÓN CON RECURRENTE ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "Base de datos: " . $dbConfig['database'] . "\n";
echo "Modo: " . ($dryRun ? 'DRY RUN (solo consulta)' : 'SINCRONIZACIÓN REAL') . "\n";
if ($limit) {
    echo "Límite: {$limit} productos\n";
}
echo "====================================\n\n";

try {
    // Conectar a la base de datos
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Obtener productos para sincronizar
    $whereClause = "WHERE is_active = 1 AND deleted_at IS NULL";
    if (!$force) {
        $whereClause .= " AND (recurrente_product_id IS NULL OR recurrente_sync_status IN ('pending', 'error'))";
    }

    // Para dry-run, también mostrar productos disabled
    if ($dryRun) {
        $whereClause = "WHERE is_active = 1 AND deleted_at IS NULL AND (recurrente_product_id IS NULL OR recurrente_sync_status IN ('pending', 'error', 'disabled'))";
    }
    
    $limitClause = $limit ? "LIMIT {$limit}" : "";
    
    $query = "
        SELECT 
            id, name, sku, description, short_description, 
            price_regular, price_sale, currency, stock_quantity,
            featured_image, recurrente_product_id, recurrente_sync_status
        FROM products 
        {$whereClause}
        ORDER BY id ASC
        {$limitClause}
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "✅ No hay productos que necesiten sincronización.\n";
        exit(0);
    }
    
    echo "📋 Productos encontrados para sincronización:\n";
    echo "ID\tSKU\t\tNombre\t\t\tEstado\n";
    echo "---\t---\t\t------\t\t\t------\n";
    
    foreach ($products as $product) {
        $status = 'Crear';
        if ($product['recurrente_product_id']) {
            $status = 'Actualizar';
        } elseif (isset($product['recurrente_sync_status']) && $product['recurrente_sync_status'] === 'disabled') {
            $status = 'Deshabilitado (precio alto)';
        }

        printf(
            "%d\t%s\t\t%s\t\t%s\n",
            $product['id'],
            substr($product['sku'], 0, 15),
            substr($product['name'], 0, 30),
            $status
        );
    }
    
    echo "\n📊 Total de productos a sincronizar: " . count($products) . "\n\n";
    
    if ($dryRun) {
        echo "🔍 Modo DRY RUN - No se realizarán cambios.\n";
        echo "Para ejecutar la sincronización real, use: php sync_products_recurrente.php\n";
        exit(0);
    }
    
    // Verificar configuración de Recurrente
    $stmt = $db->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE 'recurrente_%'");
    $stmt->execute();
    $recurrenteSettings = [];
    while ($row = $stmt->fetch()) {
        $recurrenteSettings[$row['setting_key']] = $row['setting_value'];
    }
    
    if (empty($recurrenteSettings['recurrente_public_key']) || empty($recurrenteSettings['recurrente_secret_key'])) {
        echo "❌ Error: Recurrente no está configurado correctamente.\n";
        echo "Verifique las claves públicas y secretas en la configuración.\n";
        exit(1);
    }
    
    if ($recurrenteSettings['recurrente_enabled'] !== '1') {
        echo "⚠️  Advertencia: Recurrente está deshabilitado en la configuración.\n";
        echo "¿Desea continuar de todos modos? (s/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 's') {
            echo "❌ Operación cancelada.\n";
            exit(1);
        }
    }
    
    echo "🔄 Iniciando sincronización...\n\n";
    
    $synced = 0;
    $errors = 0;
    $skipped = 0;
    
    foreach ($products as $product) {
        echo "📦 Procesando: {$product['name']} (ID: {$product['id']})...\n";
        
        try {
            // Preparar datos del producto para Recurrente
            $price = !empty($product['price_sale']) && $product['price_sale'] > 0
                ? $product['price_sale']
                : $product['price_regular'];

            // Verificar límite de precio de Recurrente ($15,000 USD)
            $maxPriceUSD = 15000;
            $priceInUSD = $price;

            // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
            if (($product['currency'] ?? 'GTQ') === 'GTQ') {
                $priceInUSD = $price / 7.8;
            }

            if ($priceInUSD > $maxPriceUSD) {
                echo "  ⚠️  Precio demasiado alto para Recurrente: $" . number_format($priceInUSD, 2) . " USD (límite: $15,000)\n";

                // Marcar como disabled en lugar de error
                $disableStmt = $db->prepare("
                    UPDATE products
                    SET recurrente_sync_status = 'disabled',
                        recurrente_synced_at = ?
                    WHERE id = ?
                ");
                $disableStmt->execute([date('Y-m-d H:i:s'), $product['id']]);

                echo "  📝 Producto marcado como 'disabled' debido al límite de precio\n";
                $skipped++;
                continue;
            }

            $imageUrl = null;
            if (!empty($product['featured_image'])) {
                $productionUrl = 'https://mrcell.com.gt';
                $featuredImage = $product['featured_image'];
                
                if (strpos($featuredImage, 'http') === 0) {
                    $imageUrl = $featuredImage;
                } elseif (strpos($featuredImage, 'assets/') === 0) {
                    $imageUrl = $productionUrl . '/' . $featuredImage;
                } else {
                    $imageUrl = $productionUrl . '/assets/img/products/' . $featuredImage;
                }
            }
            
            $productData = [
                'product' => [
                    'name' => $product['name'],
                    'description' => $product['description'] ?: $product['short_description'] ?: '',
                    'image_url' => $imageUrl,
                    'prices_attributes' => [
                        [
                            'currency' => $product['currency'] ?: 'GTQ',
                            'charge_type' => 'one_time',
                            'amount_in_cents' => intval($price * 100)
                        ]
                    ],
                    'cancel_url' => 'https://mrcell.com.gt/checkout/cancel',
                    'success_url' => 'https://mrcell.com.gt/checkout/success',
                    'custom_terms_and_conditions' => 'Términos y condiciones de MrCell Guatemala.',
                    'phone_requirement' => 'none',
                    'address_requirement' => 'none',
                    'billing_info_requirement' => 'none'
                ],
                'adjustable_quantity' => true,
                'inventory_quantity' => $product['stock_quantity'],
                'metadata' => [
                    'local_product_id' => (string)$product['id'],
                    'sku' => $product['sku'],
                    'platform' => 'MrCell_CI4'
                ]
            ];
            
            // Realizar petición a Recurrente
            $url = 'https://app.recurrente.com/api/products/';
            $method = empty($product['recurrente_product_id']) ? 'POST' : 'PUT';
            
            if ($method === 'PUT') {
                $url .= $product['recurrente_product_id'];
            }
            
            $headers = [
                'X-PUBLIC-KEY: ' . $recurrenteSettings['recurrente_public_key'],
                'X-SECRET-KEY: ' . $recurrenteSettings['recurrente_secret_key'],
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);
            
            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
            } else {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            }
            
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($productData));
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception('Error de conexión: ' . $error);
            }
            
            $decodedResponse = json_decode($response, true);
            
            if ($httpCode >= 400) {
                $errorMessage = $decodedResponse['message'] ?? $decodedResponse['error'] ?? 'Error desconocido';
                throw new Exception("Error HTTP {$httpCode}: {$errorMessage}");
            }
            
            if (isset($decodedResponse['id'])) {
                // Actualizar producto local
                $updateData = [
                    'recurrente_product_id' => $decodedResponse['id'],
                    'recurrente_sync_status' => 'synced',
                    'recurrente_synced_at' => date('Y-m-d H:i:s')
                ];
                
                if (isset($decodedResponse['storefront_link'])) {
                    $updateData['recurrente_storefront_link'] = $decodedResponse['storefront_link'];
                }
                
                $updateStmt = $db->prepare("
                    UPDATE products 
                    SET recurrente_product_id = ?, 
                        recurrente_sync_status = ?, 
                        recurrente_synced_at = ?,
                        recurrente_storefront_link = ?
                    WHERE id = ?
                ");
                
                $updateStmt->execute([
                    $updateData['recurrente_product_id'],
                    $updateData['recurrente_sync_status'],
                    $updateData['recurrente_synced_at'],
                    $updateData['recurrente_storefront_link'] ?? null,
                    $product['id']
                ]);
                
                echo "  ✅ Sincronizado exitosamente. ID Recurrente: {$decodedResponse['id']}\n";
                $synced++;
            } else {
                throw new Exception('Respuesta inválida de Recurrente');
            }
            
        } catch (Exception $e) {
            echo "  ❌ Error: " . $e->getMessage() . "\n";
            
            // Marcar como error en la base de datos
            $errorStmt = $db->prepare("
                UPDATE products 
                SET recurrente_sync_status = 'error', 
                    recurrente_synced_at = ?
                WHERE id = ?
            ");
            $errorStmt->execute([date('Y-m-d H:i:s'), $product['id']]);
            
            $errors++;
        }
        
        // Pequeña pausa para no sobrecargar la API
        usleep(500000); // 0.5 segundos
    }
    
    echo "\n📊 RESUMEN DE SINCRONIZACIÓN:\n";
    echo "============================\n";
    echo "Total procesados: " . count($products) . "\n";
    echo "Sincronizados: {$synced}\n";
    echo "Errores: {$errors}\n";
    echo "Omitidos: {$skipped}\n";
    
    if ($synced > 0) {
        echo "\n🎉 Sincronización completada exitosamente!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error inesperado: " . $e->getMessage() . "\n";
    exit(1);
}

function showHelp() {
    echo "Sincronización de Productos con Recurrente - MrCell Guatemala\n";
    echo "============================================================\n\n";
    echo "Este script sincroniza productos activos con la plataforma Recurrente.\n\n";
    echo "Uso:\n";
    echo "  php sync_products_recurrente.php [opciones]\n\n";
    echo "Opciones:\n";
    echo "  --dry-run    Solo muestra qué productos se sincronizarían (no ejecuta)\n";
    echo "  --limit=N    Limita la sincronización a N productos\n";
    echo "  --force      Fuerza la sincronización de todos los productos (incluso los ya sincronizados)\n";
    echo "  --help       Muestra esta ayuda\n\n";
    echo "Ejemplos:\n";
    echo "  php sync_products_recurrente.php --dry-run           # Ver qué se sincronizaría\n";
    echo "  php sync_products_recurrente.php --limit=10          # Sincronizar solo 10 productos\n";
    echo "  php sync_products_recurrente.php --force             # Forzar sincronización de todos\n\n";
}
