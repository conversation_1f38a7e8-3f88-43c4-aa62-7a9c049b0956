<?php

namespace App\Models;

use CodeIgniter\Model;

class UserAddressModel extends Model
{
    protected $table            = 'user_addresses';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'session_id',
        'type',
        'address_name',
        'nombre_completo',
        'telefono',
        'direccion_linea_1',
        'direccion_linea_2',
        'ciudad',
        'estado_departamento',
        'codigo_postal',
        'pais',
        'is_default'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'nombre_completo' => 'required|max_length[255]',
        'telefono' => 'permit_empty|max_length[20]',
        'direccion_linea_1' => 'required|max_length[255]',
        'direccion_linea_2' => 'permit_empty|max_length[255]',
        'ciudad' => 'required|max_length[100]',
        'estado_departamento' => 'required|max_length[100]',
        'codigo_postal' => 'permit_empty|max_length[20]',
        'pais' => 'permit_empty|max_length[100]'
    ];

    protected $validationMessages = [
        'nombre_completo' => [
            'required' => 'El nombre del destinatario es requerido',
            'max_length' => 'El nombre del destinatario no puede exceder 255 caracteres'
        ],
        'telefono' => [
            'max_length' => 'El teléfono no puede exceder 20 caracteres'
        ],
        'direccion_linea_1' => [
            'required' => 'La dirección es requerida',
            'max_length' => 'La dirección no puede exceder 255 caracteres'
        ],
        'ciudad' => [
            'required' => 'La ciudad es requerida',
            'max_length' => 'La ciudad no puede exceder 100 caracteres'
        ],
        'estado_departamento' => [
            'required' => 'El departamento es requerido',
            'max_length' => 'El departamento no puede exceder 100 caracteres'
        ]
    ];

    /**
     * Obtener direcciones del usuario
     */
    public function getUserAddresses($userId = null, $sessionId = null)
    {
        $builder = $this;

        if ($userId) {
            $builder = $builder->where('user_id', $userId);
        } elseif ($sessionId) {
            $builder = $builder->where('session_id', $sessionId);
        } else {
            return [];
        }

        return $builder->orderBy('is_default', 'DESC')
                      ->orderBy('created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Obtener dirección por defecto
     */
    public function getDefaultAddress($userId = null, $sessionId = null)
    {
        $builder = $this->where('is_default', 1);

        if ($userId) {
            $builder = $builder->where('user_id', $userId);
        } elseif ($sessionId) {
            $builder = $builder->where('session_id', $sessionId);
        } else {
            return null;
        }

        return $builder->first();
    }

    /**
     * Establecer dirección como por defecto
     */
    public function setAsDefault($addressId, $userId = null, $sessionId = null)
    {
        $this->db->transStart();

        try {
            // Quitar default de todas las direcciones del usuario
            $builder = $this->builder();
            if ($userId) {
                $builder->where('user_id', $userId);
            } elseif ($sessionId) {
                $builder->where('session_id', $sessionId);
            }
            $builder->set('is_default', 0)->update();

            // Establecer la nueva dirección como default solo si se proporciona addressId
            if ($addressId) {
                $this->update($addressId, ['is_default' => 1]);
            }

            $this->db->transComplete();

            return $this->db->transStatus();

        } catch (\Exception $e) {
            $this->db->transRollback();
            return false;
        }
    }

    /**
     * Crear nueva dirección
     */
    public function createAddress($data, $userId = null, $sessionId = null)
    {
        // Agregar user_id o session_id
        if ($userId) {
            $data['user_id'] = $userId;
        } elseif ($sessionId) {
            $data['session_id'] = $sessionId;
        }

        // Si es la primera dirección, hacerla por defecto
        $existingAddresses = $this->getUserAddresses($userId, $sessionId);
        if (empty($existingAddresses)) {
            $data['is_default'] = 1;
        }

        // Si se marca como predeterminada, quitar el default de las otras
        if (isset($data['is_default']) && $data['is_default'] == 1) {
            $this->setAsDefault(null, $userId, $sessionId);
        }

        // Agregar timestamps manualmente
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->insert($data);
    }

    /**
     * Calcular zona de envío basada en la dirección
     */
    public function calculateShippingZone($addressId)
    {
        $address = $this->find($addressId);
        if (!$address) {
            return null;
        }
        
        $db = \Config\Database::connect();
        
        // Buscar zona de envío basada en departamento y municipio
        $location = $db->table('guatemala_locations gl')
                      ->select('gl.*, sz.name as zone_name, sz.base_distance_km as zone_distance, sz.additional_cost')
                      ->join('shipping_zones sz', 'sz.id = gl.shipping_zone_id', 'left')
                      ->where('gl.department', $address['estado_departamento'])
                      ->where('gl.municipality', $address['ciudad'])
                      ->where('gl.is_active', 1)
                      ->get()
                      ->getRowArray();
        
        if ($location) {
            return [
                'zone_id' => $location['shipping_zone_id'],
                'zone_name' => $location['zone_name'],
                'distance_km' => $location['base_distance_km'],
                'additional_cost' => $location['additional_cost'] ?? 0
            ];
        }
        
        // Si no encuentra ubicación específica, usar zona por defecto
        $defaultZone = $db->table('shipping_zones')
                         ->where('is_active', 1)
                         ->orderBy('id', 'ASC')
                         ->get()
                         ->getRowArray();
        
        return [
            'zone_id' => $defaultZone['id'] ?? 1,
            'zone_name' => $defaultZone['name'] ?? 'Zona Estándar',
            'distance_km' => 15, // Distancia por defecto
            'additional_cost' => 25 // Costo adicional por defecto
        ];
    }

    /**
     * Obtener ubicaciones de Guatemala para formularios
     */
    public function getGuatemalaLocations()
    {
        $db = \Config\Database::connect();

        try {
            // Intentar obtener de la tabla guatemala_locations si existe
            if ($db->tableExists('guatemala_locations')) {
                $locations = $db->table('guatemala_locations')
                               ->select('department, municipality, zone')
                               ->orderBy('department', 'ASC')
                               ->orderBy('municipality', 'ASC')
                               ->orderBy('zone', 'ASC')
                               ->get()
                               ->getResultArray();

                // Organizar por departamento
                $organized = [];
                foreach ($locations as $location) {
                    $dept = $location['department'];
                    if (!isset($organized[$dept])) {
                        $organized[$dept] = [];
                    }

                    $city = $location['municipality'];
                    if (!empty($location['zone'])) {
                        $city .= ' - ' . $location['zone'];
                    }

                    if (!in_array($city, $organized[$dept])) {
                        $organized[$dept][] = $city;
                    }
                }

                return $organized;
            }
        } catch (\Exception $e) {
            log_message('error', 'Error al obtener ubicaciones de Guatemala: ' . $e->getMessage());
        }

        // Datos por defecto si no existe la tabla o hay error
        return [
            'Guatemala' => ['Guatemala', 'Mixco', 'Villa Nueva', 'San José Pinula', 'Santa Catarina Pinula'],
            'Sacatepéquez' => ['Antigua Guatemala', 'Ciudad Vieja', 'Jocotenango', 'San Lucas Sacatepéquez'],
            'Chimaltenango' => ['Chimaltenango', 'San José Poaquil', 'Tecpán Guatemala'],
            'Escuintla' => ['Escuintla', 'Santa Lucía Cotzumalguapa', 'La Democracia'],
            'Quetzaltenango' => ['Quetzaltenango', 'Salcajá', 'San Mateo'],
            'Alta Verapaz' => ['Cobán', 'San Pedro Carchá', 'Tactic'],
            'Petén' => ['Flores', 'San Benito', 'La Libertad']
        ];
    }
}
