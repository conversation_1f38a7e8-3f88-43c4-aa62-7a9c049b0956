<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class OptimizeIndexes extends Migration
{
    public function up()
    {
        // Optimizar índices de cupones
        $this->optimizeCouponsIndexes();
        
        // Optimizar índices de seguimiento
        $this->optimizeTrackingIndexes();
        
        // Optimizar índices de inventario
        $this->optimizeInventoryIndexes();
        
        // Optimizar índices de facturación
        $this->optimizeBillingIndexes();
        
        // Optimizar índices de seguridad
        $this->optimizeSecurityIndexes();
        
        // Crear índices compuestos para consultas complejas
        $this->createCompositeIndexes();
        
        // Crear índices de texto completo
        $this->createFullTextIndexes();
    }

    public function down()
    {
        // Eliminar índices optimizados
        $this->removeOptimizedIndexes();
    }

    private function optimizeCouponsIndexes(): void
    {
        // Índices optimizados para cupones
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupons_active_dates ON coupons (is_active, valid_from, valid_until)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupons_code_active ON coupons (code, is_active)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupons_type_value ON coupons (type, value)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupons_priority_created ON coupons (priority DESC, created_at DESC)");
        
        // Índices para uso de cupones
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupon_usage_user_date ON coupon_usage (user_id, used_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_date ON coupon_usage (coupon_id, used_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupon_usage_amount ON coupon_usage (discount_amount)");
        
        // Índices para campañas promocionales
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_promotional_campaigns_active_dates ON promotional_campaigns (is_active, start_date, end_date)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_promotional_campaigns_type ON promotional_campaigns (type, is_active)");
        
        // Índices para descuentos por volumen
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_bulk_discounts_active_quantity ON bulk_discounts (is_active, min_quantity)");
    }

    private function optimizeTrackingIndexes(): void
    {
        // Índices optimizados para envíos
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipments_tracking_company ON shipments (tracking_number, company_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipments_status_date ON shipments (status, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipments_customer_status ON shipments (customer_email, status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipments_delivered_date ON shipments (delivered_at, status)");
        
        // Índices para eventos de seguimiento
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_tracking_events_shipment_date ON tracking_events (shipment_id, created_at DESC)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_tracking_events_status ON tracking_events (status, created_at)");
        
        // Índices para empresas de envío
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipping_companies_active_code ON shipping_companies (is_active, code)");
        
        // Índices para tarifas de envío
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipping_rates_company_active ON shipping_rates (company_id, is_active)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipping_rates_weight_cost ON shipping_rates (max_weight, base_cost)");
    }

    private function optimizeInventoryIndexes(): void
    {
        // Índices optimizados para inventario
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_product_warehouse ON inventory (product_id, warehouse_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_quantity_updated ON inventory (quantity, updated_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_quantity ON inventory (warehouse_id, quantity)");
        
        // Índices para movimientos de inventario
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_movements_product_date ON inventory_movements (product_id, created_at DESC)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_movements_type_date ON inventory_movements (movement_type, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_movements_warehouse ON inventory_movements (warehouse_id, created_at)");
        
        // Índices para alertas de inventario
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_alerts_active_type ON inventory_alerts (is_active, alert_type)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_alerts_product_resolved ON inventory_alerts (product_id, is_resolved)");
        
        // Índices para ubicaciones de almacén
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_warehouse_locations_active_type ON warehouse_locations (is_active, type)");
    }

    private function optimizeBillingIndexes(): void
    {
        // Índices optimizados para facturación electrónica
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_electronic_invoices_order_status ON electronic_invoices (order_id, status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_electronic_invoices_status_date ON electronic_invoices (status, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_electronic_invoices_customer_date ON electronic_invoices (customer_nit, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_electronic_invoices_uuid ON electronic_invoices (uuid)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_electronic_invoices_invoice_number ON electronic_invoices (invoice_number)");
        
        // Índices para logs de facturación
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_billing_logs_invoice_action ON billing_logs (invoice_id, action)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_billing_logs_status_date ON billing_logs (status, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_billing_logs_order_date ON billing_logs (order_id, created_at DESC)");
        
        // Índices para publicaciones programadas
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_scheduled_posts_status_scheduled ON scheduled_posts (status, scheduled_for)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_scheduled_posts_type_status ON scheduled_posts (post_type, status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_scheduled_posts_product_status ON scheduled_posts (product_id, status)");
    }

    private function optimizeSecurityIndexes(): void
    {
        // Índices optimizados para eventos de seguridad
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_security_events_type_severity ON security_events (event_type, severity)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_security_events_ip_date ON security_events (ip_address, created_at DESC)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_security_events_resolved_date ON security_events (is_resolved, created_at)");
        
        // Índices para violaciones de rate limit
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_identifier_type ON rate_limit_violations (identifier, limit_type)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_ip_date ON rate_limit_violations (ip_address, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_blocked ON rate_limit_violations (is_blocked, blocked_until)");
        
        // Índices para IPs bloqueadas
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_blocked_ips_active_expires ON blocked_ips (is_active, expires_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_blocked_ips_severity_active ON blocked_ips (severity, is_active)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_blocked_ips_violation_count ON blocked_ips (violation_count DESC)");
        
        // Índices para intentos de login fallidos
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_ip_date ON failed_login_attempts (ip_address, created_at DESC)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_username_date ON failed_login_attempts (username, created_at)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_type_date ON failed_login_attempts (attempt_type, created_at)");
        
        // Índices para logs de auditoría
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs (user_id, action)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_date ON audit_logs (resource, resource_id, created_at DESC)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_date ON audit_logs (ip_address, created_at)");
    }

    private function createCompositeIndexes(): void
    {
        // Índices compuestos para consultas complejas frecuentes
        
        // Para búsqueda de cupones disponibles
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupons_complex_search ON coupons (is_active, valid_from, valid_until, min_order_amount, usage_limit_per_user)");
        
        // Para análisis de uso de cupones
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_coupon_usage_analysis ON coupon_usage (coupon_id, user_id, used_at, discount_amount)");
        
        // Para seguimiento de envíos
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_shipments_tracking_analysis ON shipments (tracking_number, company_id, status, created_at)");
        
        // Para inventario por ubicación
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_inventory_location_analysis ON inventory (warehouse_id, product_id, quantity, updated_at)");
        
        // Para análisis de seguridad
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_security_analysis ON security_events (event_type, severity, ip_address, created_at)");
        
        // Para facturación por cliente
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_billing_customer_analysis ON electronic_invoices (customer_nit, status, created_at, total_amount)");
    }

    private function createFullTextIndexes(): void
    {
        // Índices de texto completo para búsquedas
        try {
            // Para búsqueda en cupones
            $this->db->query("CREATE FULLTEXT INDEX IF NOT EXISTS idx_coupons_fulltext ON coupons (name, description)");
            
            // Para búsqueda en productos (si la tabla existe)
            $this->db->query("CREATE FULLTEXT INDEX IF NOT EXISTS idx_products_fulltext ON products (name, description, sku)");
            
            // Para búsqueda en logs de seguridad
            $this->db->query("CREATE FULLTEXT INDEX IF NOT EXISTS idx_security_events_fulltext ON security_events (event_data)");
            
        } catch (\Exception $e) {
            // Los índices FULLTEXT pueden fallar en algunas configuraciones
            log_message('warning', 'Could not create FULLTEXT indexes: ' . $e->getMessage());
        }
    }

    private function removeOptimizedIndexes(): void
    {
        $indexes = [
            // Cupones
            'DROP INDEX IF EXISTS idx_coupons_active_dates ON coupons',
            'DROP INDEX IF EXISTS idx_coupons_code_active ON coupons',
            'DROP INDEX IF EXISTS idx_coupons_type_value ON coupons',
            'DROP INDEX IF EXISTS idx_coupons_priority_created ON coupons',
            'DROP INDEX IF EXISTS idx_coupon_usage_user_date ON coupon_usage',
            'DROP INDEX IF EXISTS idx_coupon_usage_coupon_date ON coupon_usage',
            'DROP INDEX IF EXISTS idx_coupon_usage_amount ON coupon_usage',
            
            // Seguimiento
            'DROP INDEX IF EXISTS idx_shipments_tracking_company ON shipments',
            'DROP INDEX IF EXISTS idx_shipments_status_date ON shipments',
            'DROP INDEX IF EXISTS idx_shipments_customer_status ON shipments',
            'DROP INDEX IF EXISTS idx_tracking_events_shipment_date ON tracking_events',
            'DROP INDEX IF EXISTS idx_tracking_events_status ON tracking_events',
            
            // Inventario
            'DROP INDEX IF EXISTS idx_inventory_product_warehouse ON inventory',
            'DROP INDEX IF EXISTS idx_inventory_quantity_updated ON inventory',
            'DROP INDEX IF EXISTS idx_inventory_movements_product_date ON inventory_movements',
            'DROP INDEX IF EXISTS idx_inventory_movements_type_date ON inventory_movements',
            
            // Facturación
            'DROP INDEX IF EXISTS idx_electronic_invoices_order_status ON electronic_invoices',
            'DROP INDEX IF EXISTS idx_electronic_invoices_status_date ON electronic_invoices',
            'DROP INDEX IF EXISTS idx_electronic_invoices_customer_date ON electronic_invoices',
            'DROP INDEX IF EXISTS idx_billing_logs_invoice_action ON billing_logs',
            
            // Seguridad
            'DROP INDEX IF EXISTS idx_security_events_type_severity ON security_events',
            'DROP INDEX IF EXISTS idx_security_events_ip_date ON security_events',
            'DROP INDEX IF EXISTS idx_rate_limit_violations_identifier_type ON rate_limit_violations',
            'DROP INDEX IF EXISTS idx_blocked_ips_active_expires ON blocked_ips',
            'DROP INDEX IF EXISTS idx_audit_logs_user_action ON audit_logs',
            
            // Índices compuestos
            'DROP INDEX IF EXISTS idx_coupons_complex_search ON coupons',
            'DROP INDEX IF EXISTS idx_coupon_usage_analysis ON coupon_usage',
            'DROP INDEX IF EXISTS idx_shipments_tracking_analysis ON shipments',
            'DROP INDEX IF EXISTS idx_inventory_location_analysis ON inventory',
            'DROP INDEX IF EXISTS idx_security_analysis ON security_events',
            'DROP INDEX IF EXISTS idx_billing_customer_analysis ON electronic_invoices',
            
            // Índices FULLTEXT
            'DROP INDEX IF EXISTS idx_coupons_fulltext ON coupons',
            'DROP INDEX IF EXISTS idx_products_fulltext ON products',
            'DROP INDEX IF EXISTS idx_security_events_fulltext ON security_events'
        ];

        foreach ($indexes as $dropQuery) {
            try {
                $this->db->query($dropQuery);
            } catch (\Exception $e) {
                // Ignorar errores al eliminar índices que no existen
            }
        }
    }
}
