<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\SystemSettingsModel;

class Settings extends BaseController
{
    protected $settingsModel;

    public function __construct()
    {
        $this->settingsModel = new SystemSettingsModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Configuraciones del Sistema',
            'settings' => $this->settingsModel->getActiveSettings(),
            'groups' => $this->getSettingsGroups()
        ];

        return view('admin/settings/index', $data);
    }

    public function taxes()
    {
        $data = [
            'title' => 'Configuración de Impuestos',
            'tax_settings' => $this->settingsModel->getTaxSettings(),
            'settings' => $this->settingsModel->getActiveSettings('taxes')
        ];

        return view('admin/settings/taxes', $data);
    }

    public function updateTaxes()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Acceso no válido');
        }

        $rules = [
            'tax_enabled' => 'required|in_list[0,1]',
            'tax_rate' => 'required|decimal|greater_than_equal_to[0]|less_than_equal_to[100]',
            'tax_name' => 'required|max_length[50]',
            'tax_included_in_price' => 'required|in_list[0,1]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Datos inválidos',
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $this->settingsModel->setSetting('tax_enabled', $this->request->getPost('tax_enabled'), 'boolean');
            $this->settingsModel->setSetting('tax_rate', $this->request->getPost('tax_rate'), 'decimal');
            $this->settingsModel->setSetting('tax_name', $this->request->getPost('tax_name'), 'string');
            $this->settingsModel->setSetting('tax_included_in_price', $this->request->getPost('tax_included_in_price'), 'boolean');

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Configuración de impuestos actualizada correctamente'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error al actualizar configuración: ' . $e->getMessage()
            ]);
        }
    }

    public function update()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Acceso no válido');
        }

        $settings = $this->request->getPost('settings');
        
        if (!$settings || !is_array($settings)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No se recibieron configuraciones para actualizar'
            ]);
        }

        try {
            foreach ($settings as $key => $value) {
                // Get setting info to determine type
                $settingInfo = $this->settingsModel->where('setting_key', $key)->first();

                if ($settingInfo && $settingInfo['is_active']) {
                    $this->settingsModel->setSetting($key, $value, $settingInfo['setting_type']);
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Configuraciones actualizadas correctamente'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error al actualizar configuraciones: ' . $e->getMessage()
            ]);
        }
    }

    private function getSettingsGroups()
    {
        $groups = $this->settingsModel->select('setting_group')
                                    ->distinct()
                                    ->where('is_active', 1)
                                    ->findAll();

        $result = [];
        foreach ($groups as $group) {
            $result[] = $group['setting_group'];
        }

        return $result;
    }

    public function getSettingValue($key)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $value = $this->settingsModel->getSetting($key);

        return $this->response->setJSON([
            'success' => true,
            'value' => $value
        ]);
    }

    public function updateRecurrente()
    {
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->back()->with('error', 'Método no válido');
        }

        try {
            $db = \Config\Database::connect();

            // Configuraciones de Recurrente
            $recurrenteSettings = [
                'recurrente_enabled' => $this->request->getPost('recurrente_enabled') ? '1' : '0',
                'recurrente_mode' => $this->request->getPost('recurrente_mode') ?: 'test',
                'recurrente_public_key' => $this->request->getPost('recurrente_public_key') ?: '',
                'recurrente_secret_key' => $this->request->getPost('recurrente_secret_key') ?: '',
                'recurrente_webhook_secret' => $this->request->getPost('recurrente_webhook_secret') ?: '',
                'recurrente_currency' => $this->request->getPost('recurrente_currency') ?: 'GTQ',
                'recurrente_fee_percentage' => $this->request->getPost('recurrente_fee_percentage') ?: '3.9',
                'recurrente_allow_cards' => $this->request->getPost('recurrente_allow_cards') ? '1' : '0',
                'recurrente_allow_transfers' => $this->request->getPost('recurrente_allow_transfers') ? '1' : '0',
                'recurrente_installments_enabled' => $this->request->getPost('recurrente_installments_enabled') ? '1' : '0',
                'recurrente_installments_min_amount' => $this->request->getPost('recurrente_installments_min_amount') ?: '500',
                'recurrente_installments_available' => $this->request->getPost('recurrente_installments_available') ?: '3,6,12,18,24',
            ];

            // Actualizar cada configuración
            foreach ($recurrenteSettings as $key => $value) {
                $existing = $db->query("SELECT * FROM system_settings WHERE setting_key = ?", [$key])->getRowArray();

                if ($existing) {
                    $db->query("
                        UPDATE system_settings
                        SET setting_value = ?, updated_at = NOW()
                        WHERE setting_key = ?
                    ", [$value, $key]);
                } else {
                    $db->query("
                        INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, is_active, created_at, updated_at)
                        VALUES (?, ?, 'text', 'integrations', 1, NOW(), NOW())
                    ", [$key, $value]);
                }
            }

            return redirect()->to('/admin/settings?tab=integrations')->with('success', 'Configuración de Recurrente actualizada correctamente');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error al actualizar configuración: ' . $e->getMessage());
        }
    }
}
