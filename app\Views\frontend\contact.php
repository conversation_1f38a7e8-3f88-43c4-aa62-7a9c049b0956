<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 80px 0;
    }

    .contact-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: -50px;
        position: relative;
        z-index: 2;
    }

    .contact-info-card {
        background: var(--gray-100);
        border-radius: 15px;
        padding: 30px;
        height: 100%;
    }

    .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 10px;
        transition: transform 0.3s ease;
    }

    .contact-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
    }

    .contact-icon.email { background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); }
    .contact-icon.phone { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .contact-icon.whatsapp { background: linear-gradient(135deg, #25d366, #128c7e); }
    .contact-icon.address { background: linear-gradient(135deg, #4facfe, #00f2fe); }

    .form-control {
        border: 2px solid var(--gray-200);
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }

    .btn-send {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white;
    }

    .btn-send:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 38, 38, 0.4);
        color: white;
    }

    .social-links a {
        display: inline-block;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        text-align: center;
        line-height: 45px;
        margin: 0 5px;
        color: white;
        text-decoration: none;
        transition: transform 0.3s ease;
    }

    .social-links a:hover {
        transform: translateY(-3px);
    }

    .social-links .facebook { background: #3b5998; }
    .social-links .instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
    .social-links .twitter { background: #1da1f2; }
    .social-links .youtube { background: #ff0000; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-3">Contáctanos</h1>
            <p class="lead">Estamos aquí para ayudarte. Ponte en contacto con nosotros.</p>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-5">
        <div class="container">
            <div class="contact-card">
                <div class="row">
                    <!-- Contact Form -->
                    <div class="col-lg-8">
                        <h3 class="mb-4">Envíanos un mensaje</h3>
                        
                        <?php if (session()->getFlashdata('success')): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= session()->getFlashdata('success') ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (session()->getFlashdata('errors')): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <ul class="mb-0">
                                    <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                        <li><?= $error ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form action="<?= base_url('contacto') ?>" method="post">
                            <?= csrf_field() ?>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Nombre completo</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name') ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= old('email') ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">Asunto</label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       value="<?= old('subject') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Mensaje</label>
                                <textarea class="form-control" id="message" name="message" rows="5" 
                                          required><?= old('message') ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-send">
                                <i class="fas fa-paper-plane me-2"></i>Enviar Mensaje
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="col-lg-4">
                        <div class="contact-info-card">
                            <h4 class="mb-4">Información de contacto</h4>
                            
                            <div class="contact-item">
                                <div class="contact-icon email">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Email</h6>
                                    <p class="mb-0 text-muted"><?= $contact_info['email'] ?? '<EMAIL>' ?></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon phone">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Teléfono</h6>
                                    <p class="mb-0 text-muted"><?= $contact_info['phone'] ?? '+502 2234-5678' ?></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">WhatsApp</h6>
                                    <p class="mb-0 text-muted"><?= $contact_info['whatsapp'] ?? '+502 5555-1234' ?></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon address">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Dirección</h6>
                                    <p class="mb-0 text-muted"><?= $contact_info['address'] ?? 'Ciudad de Guatemala, Guatemala' ?></p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h6 class="mb-3">Horarios de atención</h6>
                            <div class="mb-3">
                                <small class="text-muted">
                                    <strong>Lunes - Viernes:</strong> 9:00 AM - 6:00 PM<br>
                                    <strong>Sábado:</strong> 9:00 AM - 4:00 PM<br>
                                    <strong>Domingo:</strong> Cerrado
                                </small>
                            </div>
                            
                            <h6 class="mb-3">Síguenos</h6>
                            <div class="social-links">
                                <a href="<?= $contact_info['social_media']['facebook'] ?? '#' ?>" class="facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="<?= $contact_info['social_media']['instagram'] ?? '#' ?>" class="instagram">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="<?= $contact_info['social_media']['twitter'] ?? '#' ?>" class="twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="<?= $contact_info['social_media']['youtube'] ?? '#' ?>" class="youtube">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Contact form functionality
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Add loading state to submit button
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
                    submitBtn.disabled = true;
                }
            });
        }
    });
</script>
<?= $this->endSection() ?>
