<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .order-card {
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .order-card:hover {
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.1);
        border-color: var(--primary-light);
    }

    .order-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    .status-pending {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-processing {
        background: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-shipped {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-delivered {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }

    .status-cancelled {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .empty-orders {
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
    }

    .empty-orders i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .order-timeline {
        position: relative;
        padding-left: 2rem;
    }

    .order-timeline::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: var(--gray-300);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 1rem;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -0.5rem;
        top: 0.25rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: var(--primary-color);
        border: 3px solid white;
        box-shadow: 0 0 0 2px var(--primary-color);
    }

    .filter-tabs {
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: 2rem;
    }

    .filter-tabs .nav-link {
        border: none;
        color: var(--text-muted);
        padding: 1rem 1.5rem;
    }

    .filter-tabs .nav-link.active {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        background: none;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</h1>
                <p class="mb-0 mt-2 opacity-75">Historial y estado de tus pedidos</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
                    <li class="breadcrumb-item active">Mis Pedidos</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= ($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') ?: 'Usuario' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>" class="active"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <div class="dashboard-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Historial de Pedidos</h2>
                    <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Realizar Nuevo Pedido
                    </a>
                </div>

                <!-- Filter Tabs -->
                <ul class="nav nav-tabs filter-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#all-orders">
                            Todos <span class="badge bg-secondary ms-1"><?= $order_stats['total'] ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#pending-orders">
                            Pendientes <span class="badge bg-warning ms-1"><?= $order_stats['pending'] ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#shipped-orders">
                            Enviados <span class="badge bg-info ms-1"><?= $order_stats['shipped'] ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#delivered-orders">
                            Entregados <span class="badge bg-success ms-1"><?= $order_stats['delivered'] ?></span>
                        </a>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="all-orders">
                        <?php if (empty($orders)): ?>
                            <div class="empty-orders">
                                <i class="fas fa-shopping-bag"></i>
                                <h4>No tienes pedidos aún</h4>
                                <p class="mb-4">Cuando realices tu primera compra, aparecerá aquí.</p>
                                <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart me-2"></i>Comenzar a Comprar
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($orders as $order): ?>
                                <div class="order-card">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <strong>#<?= esc($order['order_number'] ?? 'MRC-000') ?></strong>
                                            <br><small class="text-muted"><?= date('d M Y', strtotime($order['created_at'] ?? 'now')) ?></small>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="mb-1"><?= esc($order['product_name'] ?? 'Producto') ?></h6>
                                            <small class="text-muted"><?= $order['total_items'] ?? 1 ?> producto(s)</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong>Q <?= number_format($order['total_amount'] ?? 0, 2) ?></strong>
                                        </div>
                                        <div class="col-md-2">
                                            <span class="order-status status-<?= strtolower($order['status'] ?? 'pending') ?>">
                                                <?= ucfirst($order['status'] ?? 'Pendiente') ?>
                                            </span>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <div class="btn-group-vertical">
                                                <a href="<?= base_url('cuenta/pedidos/' . ($order['id'] ?? '1')) ?>" 
                                                   class="btn btn-outline-primary btn-sm mb-1">
                                                    <i class="fas fa-eye me-1"></i>Ver
                                                </a>
                                                <?php if (($order['status'] ?? '') === 'shipped'): ?>
                                                    <button class="btn btn-outline-info btn-sm">
                                                        <i class="fas fa-truck me-1"></i>Rastrear
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Order Timeline (collapsed by default) -->
                                    <div class="collapse mt-3" id="timeline-<?= $order['id'] ?? '1' ?>">
                                        <div class="order-timeline">
                                            <div class="timeline-item">
                                                <strong>Pedido realizado</strong>
                                                <p class="text-muted mb-0"><?= date('d/m/Y H:i', strtotime($order['created_at'] ?? 'now')) ?></p>
                                            </div>
                                            <div class="timeline-item">
                                                <strong>Pago confirmado</strong>
                                                <p class="text-muted mb-0">En proceso de verificación</p>
                                            </div>
                                            <div class="timeline-item">
                                                <strong>En preparación</strong>
                                                <p class="text-muted mb-0">Tu pedido está siendo preparado</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end mt-2">
                                        <button class="btn btn-link btn-sm" data-bs-toggle="collapse" 
                                                data-bs-target="#timeline-<?= $order['id'] ?? '1' ?>">
                                            <i class="fas fa-history me-1"></i>Ver cronología
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <!-- Pagination -->
                            <nav aria-label="Paginación de pedidos" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <span class="page-link">Anterior</span>
                                    </li>
                                    <li class="page-item active">
                                        <span class="page-link">1</span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link">Siguiente</span>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Pending Orders Tab -->
                    <div class="tab-pane fade" id="pending-orders">
                        <?php if (empty($orders_by_status['pending'])): ?>
                            <div class="empty-orders">
                                <i class="fas fa-clock"></i>
                                <h4>No tienes pedidos pendientes</h4>
                                <p>Todos tus pedidos han sido procesados.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($orders_by_status['pending'] as $order): ?>
                                <div class="order-card">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <strong>#<?= esc($order['order_number'] ?? 'MRC-' . $order['id']) ?></strong>
                                            <br><small class="text-muted"><?= date('d M Y', strtotime($order['created_at'])) ?></small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="order-products">
                                                <small class="text-muted"><?= $order['total_items'] ?? 1 ?> producto(s)</small>
                                                <br><span class="product-names"><?= esc(substr($order['product_names'] ?? 'Productos varios', 0, 50)) ?><?= strlen($order['product_names'] ?? '') > 50 ? '...' : '' ?></span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <span class="order-status status-<?= strtolower($order['status']) ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </div>
                                        <div class="col-md-2">
                                            <strong>Q<?= number_format($order['total_amount'], 2) ?></strong>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <a href="<?= base_url('cuenta/pedidos/' . $order['id']) ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>Ver Detalles
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Shipped Orders Tab -->
                    <div class="tab-pane fade" id="shipped-orders">
                        <?php if (empty($orders_by_status['shipped'])): ?>
                            <div class="empty-orders">
                                <i class="fas fa-truck"></i>
                                <h4>No tienes pedidos enviados</h4>
                                <p>Cuando tus pedidos sean enviados, aparecerán aquí.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($orders_by_status['shipped'] as $order): ?>
                                <div class="order-card">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <strong>#<?= esc($order['order_number'] ?? 'MRC-' . $order['id']) ?></strong>
                                            <br><small class="text-muted"><?= date('d M Y', strtotime($order['created_at'])) ?></small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="order-products">
                                                <small class="text-muted"><?= $order['total_items'] ?? 1 ?> producto(s)</small>
                                                <br><span class="product-names"><?= esc(substr($order['product_names'] ?? 'Productos varios', 0, 50)) ?><?= strlen($order['product_names'] ?? '') > 50 ? '...' : '' ?></span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <span class="order-status status-<?= strtolower($order['status']) ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </div>
                                        <div class="col-md-2">
                                            <strong>Q<?= number_format($order['total_amount'], 2) ?></strong>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <a href="<?= base_url('cuenta/pedidos/' . $order['id']) ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>Ver Detalles
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Delivered Orders Tab -->
                    <div class="tab-pane fade" id="delivered-orders">
                        <?php if (empty($orders_by_status['delivered'])): ?>
                            <div class="empty-orders">
                                <i class="fas fa-check-circle"></i>
                                <h4>No tienes pedidos entregados</h4>
                                <p>Cuando tus pedidos sean entregados, aparecerán aquí.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($orders_by_status['delivered'] as $order): ?>
                                <div class="order-card">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <strong>#<?= esc($order['order_number'] ?? 'MRC-' . $order['id']) ?></strong>
                                            <br><small class="text-muted"><?= date('d M Y', strtotime($order['created_at'])) ?></small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="order-products">
                                                <small class="text-muted"><?= $order['total_items'] ?? 1 ?> producto(s)</small>
                                                <br><span class="product-names"><?= esc(substr($order['product_names'] ?? 'Productos varios', 0, 50)) ?><?= strlen($order['product_names'] ?? '') > 50 ? '...' : '' ?></span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <span class="order-status status-<?= strtolower($order['status']) ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </div>
                                        <div class="col-md-2">
                                            <strong>Q<?= number_format($order['total_amount'], 2) ?></strong>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <a href="<?= base_url('cuenta/pedidos/' . $order['id']) ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>Ver Detalles
                                            </a>
                                            <a href="<?= base_url('cuenta/pedidos/' . $order['id'] . '/reorder') ?>" class="btn btn-outline-success btn-sm ms-1">
                                                <i class="fas fa-redo me-1"></i>Reordenar
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Filter functionality
document.querySelectorAll('.filter-tabs .nav-link').forEach(tab => {
    tab.addEventListener('click', function() {
        // Update active state
        document.querySelectorAll('.filter-tabs .nav-link').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        
        // Here you would typically filter the orders based on status
        // For now, we'll just show/hide the appropriate tab content
    });
});

// Track order function
function trackOrder(orderId) {
    alert('Funcionalidad de rastreo en desarrollo para pedido #' + orderId);
}

// Reorder function
function reorderItems(orderId) {
    if (confirm('¿Deseas volver a pedir los mismos productos?')) {
        alert('Funcionalidad de reorden en desarrollo');
    }
}
</script>
<?= $this->endSection() ?>
