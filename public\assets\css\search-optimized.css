/**
 * Estilos para Sistema de Búsqueda Optimizada
 * MrCell Guatemala - Compatible con todos los navegadores
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

/* ===== CONTENEDOR DE BÚSQUEDA ===== */
.search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    transition: all 0.3s ease;
    background: #fff;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
    color: #999;
}

/* ===== SUGERENCIAS ===== */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.active {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.suggestion-text {
    flex: 1;
    min-width: 0;
}

.suggestion-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.suggestion-name strong {
    color: #007bff;
}

.suggestion-price {
    font-size: 14px;
    color: #666;
    font-weight: 600;
}

/* ===== LOADING ===== */
.search-loading {
    text-align: center;
    padding: 20px;
    display: none;
}

.search-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== RESULTADOS ===== */
.search-results {
    margin-top: 20px;
}

.search-info {
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
}

.search-info p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-results h3 {
    margin-bottom: 10px;
    color: #333;
}

.search-error {
    text-align: center;
    padding: 20px;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px 0;
}

/* ===== GRID DE PRODUCTOS ===== */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.product-card {
    position: relative;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.product-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.product-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.3;
    height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    margin-bottom: 8px;
}

.price {
    font-size: 18px;
    font-weight: 700;
    color: #007bff;
}

.price-sale {
    font-size: 18px;
    font-weight: 700;
    color: #dc3545;
    margin-right: 8px;
}

.price-regular {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    font-size: 14px;
    color: #ffc107;
}

.rating-count {
    font-size: 12px;
    color: #666;
}

.product-stock {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.product-stock.in-stock {
    background: #d4edda;
    color: #155724;
}

.product-stock.out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* ===== PAGINACIÓN ===== */
.search-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 30px 0;
}

.search-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    min-width: 40px;
}

.search-pagination-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.search-pagination-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.search-pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .search-container {
        max-width: 100%;
        padding: 0 15px;
    }
    
    .search-input {
        font-size: 16px; /* Evitar zoom en iOS */
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .product-card {
        border-radius: 6px;
    }
    
    .product-image {
        height: 150px;
    }
    
    .product-info {
        padding: 12px;
    }
    
    .product-name {
        font-size: 14px;
    }
    
    .price,
    .price-sale {
        font-size: 16px;
    }
    
    .suggestion-content {
        gap: 8px;
    }
    
    .suggestion-image {
        width: 35px;
        height: 35px;
    }
    
    .search-pagination {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .search-pagination-btn {
        padding: 6px 10px;
        font-size: 13px;
        min-width: 35px;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 10px;
    }
    
    .product-image {
        height: 120px;
    }
    
    .product-info {
        padding: 10px;
    }
    
    .product-name {
        font-size: 13px;
        height: auto;
        -webkit-line-clamp: 3;
    }
    
    .price,
    .price-sale {
        font-size: 14px;
    }
}

/* ===== ANIMACIONES ===== */
.product-card {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-suggestions {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== UTILIDADES ===== */
.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

/* ===== ACCESIBILIDAD ===== */
.search-input:focus,
.search-pagination-btn:focus,
.suggestion-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
