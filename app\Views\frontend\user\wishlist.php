<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .wishlist-item {
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .wishlist-item:hover {
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.1);
        border-color: var(--primary-light);
    }

    .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 10px;
    }

    .empty-wishlist {
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
    }

    .empty-wishlist i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-heart me-2"></i>Lista de Deseos</h1>
                <p class="mb-0 mt-2 opacity-75">Tus productos favoritos</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
                    <li class="breadcrumb-item active">Lista de Deseos</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= $user['first_name'] ?? 'Usuario' ?> <?= $user['last_name'] ?? '' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>" class="active"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <div class="dashboard-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Mi Lista de Deseos</h2>
                    <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Seguir Comprando
                    </a>
                </div>

                <?php if (empty($wishlist_items)): ?>
                    <div class="empty-wishlist">
                        <i class="fas fa-heart-broken"></i>
                        <h4>Tu lista de deseos está vacía</h4>
                        <p class="mb-4">Agrega productos que te gusten para encontrarlos fácilmente más tarde.</p>
                        <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                            <i class="fas fa-shopping-bag me-2"></i>Explorar Productos
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($wishlist_items as $item): ?>
                        <div class="wishlist-item">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <img src="<?= $item['image'] ?? base_url('assets/img/no-image.jpg') ?>" 
                                         alt="<?= esc($item['name']) ?>" 
                                         class="product-image">
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-1"><?= esc($item['name']) ?></h5>
                                    <p class="text-muted mb-2"><?= esc($item['category'] ?? 'Sin categoría') ?></p>
                                    <div class="text-primary fw-bold">
                                        <?= isset($item['price_sale']) && $item['price_sale'] > 0 ? 
                                            'Q ' . number_format($item['price_sale'], 2) : 
                                            'Q ' . number_format($item['price_regular'], 2) ?>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group">
                                        <a href="<?= base_url('producto/' . $item['slug']) ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>Ver
                                        </a>
                                        <button class="btn btn-primary btn-sm" onclick="addToCart(<?= $item['id'] ?>)">
                                            <i class="fas fa-shopping-cart me-1"></i>Agregar
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeFromWishlist(<?= $item['id'] ?>)">
                                            <i class="fas fa-trash me-1"></i>Quitar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function addToCart(productId) {
    // TODO: Implementar agregar al carrito
    alert('Funcionalidad de carrito en desarrollo');
}

function removeFromWishlist(productId) {
    if (confirm('¿Estás seguro de que quieres quitar este producto de tu lista de deseos?')) {
        // TODO: Implementar quitar de wishlist
        alert('Funcionalidad de wishlist en desarrollo');
    }
}
</script>
<?= $this->endSection() ?>
