<?php

namespace App\Controllers;

use App\Models\UserModel;

class SimpleLoginController extends BaseController
{
    protected $session;
    protected $userModel;

    public function __construct()
    {
        // Models will be initialized in initController
    }

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        // Call parent initController
        parent::initController($request, $response, $logger);

        // Initialize models and session
        $this->session = \Config\Services::session();
        $this->userModel = new UserModel();
    }

    public function authenticate()
    {
        log_message('error', 'SimpleLoginController::authenticate - MÉTODO INICIADO');

        try {
            log_message('error', 'SimpleLoginController::authenticate - Paso 1: Validación');
            $validation = \Config\Services::validation();
        
        $validation->setRules([
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('error', 'Por favor completa todos los campos correctamente');
        }

            log_message('error', 'SimpleLoginController::authenticate - Paso 2: Obteniendo datos POST');
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
            log_message('error', 'SimpleLoginController::authenticate - Email: ' . $email);

            log_message('error', 'SimpleLoginController::authenticate - Paso 3: Buscando usuario');
            $user = $this->userModel->where('email', $email)->where('status', 'active')->first();
            log_message('error', 'SimpleLoginController::authenticate - Usuario encontrado: ' . ($user ? 'SÍ' : 'NO'));

            if ($user && password_verify($password, $user['password'])) {
                // Login exitoso
                $fullName = trim($user['first_name'] . ' ' . $user['last_name']);
                $sessionData = [
                    'user_id' => $user['id'],
                    'user_name' => $fullName,
                    'user_email' => $user['email'],
                    'is_logged_in' => true
                ];

                log_message('error', 'SimpleLoginController: Estableciendo datos de sesión: ' . json_encode($sessionData));
                $this->session->set($sessionData);

                // Verificar que la sesión se estableció correctamente
                $verifySession = $this->session->get('user_id');
                log_message('error', 'SimpleLoginController: Verificación de sesión después de set: user_id=' . $verifySession);

                // Actualizar último login
                $this->userModel->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);

                log_message('error', 'SimpleLoginController: Redirigiendo a /cuenta');
                return redirect()->to('/cuenta')->with('success', 'Bienvenido de vuelta, ' . $fullName);
            } else {
                return redirect()->back()->withInput()->with('error', 'Email o contraseña incorrectos');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en login simple: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->back()->withInput()->with('error', 'Error al iniciar sesión. Inténtalo de nuevo.');
        }
    }

    public function test()
    {
        log_message('error', 'SimpleLoginController::test - MÉTODO DE TEST EJECUTADO');
        return 'SimpleLoginController funciona correctamente';
    }

    public function testPost()
    {
        log_message('error', 'SimpleLoginController::testPost - MÉTODO POST DE TEST EJECUTADO');
        return 'POST funciona correctamente';
    }
}
