<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\SystemMonitor;
use App\Libraries\PerformanceOptimizer;

/**
 * Controlador de Métricas y Analytics
 * Dashboard de métricas en tiempo real del sistema
 */
class MetricsController extends BaseController
{
    private $systemMonitor;
    private $performanceOptimizer;
    
    public function __construct()
    {
        $this->systemMonitor = new SystemMonitor();
        $this->performanceOptimizer = new PerformanceOptimizer();
    }
    
    /**
     * Dashboard principal de métricas
     */
    public function index()
    {
        $data = [
            'title' => 'Métricas del Sistema - MrCell Guatemala',
            'current_metrics' => $this->getCurrentMetrics(),
            'system_health' => $this->getSystemHealth(),
            'performance_stats' => $this->getPerformanceStats(),
            'recent_alerts' => $this->getRecentAlerts()
        ];
        
        return view('admin/metrics/dashboard', $data);
    }
    
    /**
     * API para obtener métricas en tiempo real
     */
    public function realTimeMetrics()
    {
        try {
            $metrics = [
                'timestamp' => date('Y-m-d H:i:s'),
                'system' => $this->getSystemMetrics(),
                'database' => $this->getDatabaseMetrics(),
                'performance' => $this->getPerformanceMetrics(),
                'users' => $this->getUserMetrics(),
                'sales' => $this->getSalesMetrics(),
                'automation' => $this->getAutomationMetrics()
            ];
            
            return $this->response->setJSON([
                'success' => true,
                'metrics' => $metrics
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener métricas actuales del sistema
     */
    private function getCurrentMetrics(): array
    {
        $db = \Config\Database::connect();
        
        try {
            return [
                'total_users' => $db->table('users')->countAllResults(),
                'total_products' => $db->table('products')->countAllResults(),
                'total_orders' => $db->table('orders')->countAllResults(),
                'active_carts' => $db->table('shopping_cart')
                                    ->where('updated_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                                    ->countAllResults(),
                'wishlist_items' => $db->table('wishlist')->countAllResults(),
                'whatsapp_messages' => $db->table('whatsapp_message_log')
                                         ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                                         ->countAllResults()
            ];
        } catch (\Exception $e) {
            return [
                'total_users' => 0,
                'total_products' => 0,
                'total_orders' => 0,
                'active_carts' => 0,
                'wishlist_items' => 0,
                'whatsapp_messages' => 0
            ];
        }
    }
    
    /**
     * Obtener estado de salud del sistema
     */
    private function getSystemHealth(): array
    {
        try {
            $healthCheck = $this->systemMonitor->runSystemCheck();
            
            return [
                'overall_status' => $healthCheck['success'] ? 'healthy' : 'warning',
                'critical_issues' => $healthCheck['critical_issues'] ?? 0,
                'warnings' => $healthCheck['warnings'] ?? 0,
                'last_check' => $healthCheck['timestamp'] ?? date('Y-m-d H:i:s'),
                'response_time' => $healthCheck['execution_time'] ?? '0ms'
            ];
        } catch (\Exception $e) {
            return [
                'overall_status' => 'error',
                'critical_issues' => 1,
                'warnings' => 0,
                'last_check' => date('Y-m-d H:i:s'),
                'response_time' => '0ms',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de rendimiento
     */
    private function getPerformanceStats(): array
    {
        return [
            'memory_usage' => [
                'current' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'limit' => ini_get('memory_limit')
            ],
            'execution_time' => [
                'current' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2),
                'limit' => ini_get('max_execution_time')
            ],
            'database' => [
                'queries_today' => $this->getDatabaseQueriesCount(),
                'avg_query_time' => $this->getAverageQueryTime(),
                'slow_queries' => $this->getSlowQueriesCount()
            ]
        ];
    }
    
    /**
     * Obtener alertas recientes
     */
    private function getRecentAlerts(): array
    {
        $db = \Config\Database::connect();
        
        try {
            return $db->table('system_alerts')
                     ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                     ->orderBy('created_at', 'DESC')
                     ->limit(10)
                     ->get()
                     ->getResultArray();
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Obtener métricas del sistema
     */
    private function getSystemMetrics(): array
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'disk_usage' => $this->getDiskUsage(),
            'load_average' => $this->getLoadAverage(),
            'uptime' => $this->getSystemUptime()
        ];
    }
    
    /**
     * Obtener métricas de base de datos
     */
    private function getDatabaseMetrics(): array
    {
        $db = \Config\Database::connect();
        
        try {
            // Obtener estadísticas de conexiones
            $connections = $db->query("SHOW STATUS LIKE 'Threads_connected'")->getRow();
            $maxConnections = $db->query("SHOW VARIABLES LIKE 'max_connections'")->getRow();
            
            return [
                'connections' => [
                    'current' => $connections->Value ?? 0,
                    'max' => $maxConnections->Value ?? 0
                ],
                'queries_per_second' => $this->getQueriesPerSecond(),
                'table_locks' => $this->getTableLocks(),
                'innodb_buffer_pool' => $this->getInnoDBBufferPool()
            ];
        } catch (\Exception $e) {
            return [
                'connections' => ['current' => 0, 'max' => 0],
                'queries_per_second' => 0,
                'table_locks' => 0,
                'innodb_buffer_pool' => 0
            ];
        }
    }
    
    /**
     * Obtener métricas de rendimiento
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'page_load_time' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2),
            'cache_hit_ratio' => $this->getCacheHitRatio(),
            'optimization_score' => $this->getOptimizationScore(),
            'response_time_avg' => $this->getAverageResponseTime()
        ];
    }
    
    /**
     * Obtener métricas de usuarios
     */
    private function getUserMetrics(): array
    {
        $db = \Config\Database::connect();
        
        try {
            return [
                'online_users' => $this->getOnlineUsers(),
                'new_registrations_today' => $db->table('users')
                                               ->where('created_at >', date('Y-m-d 00:00:00'))
                                               ->countAllResults(),
                'active_sessions' => $this->getActiveSessions(),
                'bounce_rate' => $this->getBounceRate()
            ];
        } catch (\Exception $e) {
            return [
                'online_users' => 0,
                'new_registrations_today' => 0,
                'active_sessions' => 0,
                'bounce_rate' => 0
            ];
        }
    }
    
    /**
     * Obtener métricas de ventas
     */
    private function getSalesMetrics(): array
    {
        $db = \Config\Database::connect();
        
        try {
            $today = date('Y-m-d');
            
            return [
                'sales_today' => $db->table('orders')
                                   ->where('DATE(created_at)', $today)
                                   ->where('status', 'completed')
                                   ->countAllResults(),
                'revenue_today' => $this->getTodayRevenue(),
                'conversion_rate' => $this->getConversionRate(),
                'average_order_value' => $this->getAverageOrderValue(),
                'cart_abandonment_rate' => $this->getCartAbandonmentRate()
            ];
        } catch (\Exception $e) {
            return [
                'sales_today' => 0,
                'revenue_today' => 0,
                'conversion_rate' => 0,
                'average_order_value' => 0,
                'cart_abandonment_rate' => 0
            ];
        }
    }
    
    /**
     * Obtener métricas de automatización
     */
    private function getAutomationMetrics(): array
    {
        $db = \Config\Database::connect();
        
        try {
            return [
                'automations_run_today' => $db->table('automation_log')
                                             ->where('DATE(created_at)', date('Y-m-d'))
                                             ->countAllResults(),
                'success_rate' => $this->getAutomationSuccessRate(),
                'whatsapp_messages_sent' => $db->table('whatsapp_message_log')
                                              ->where('DATE(created_at)', date('Y-m-d'))
                                              ->where('status', 'sent')
                                              ->countAllResults(),
                'price_alerts_sent' => $this->getPriceAlertsSent(),
                'stock_alerts_sent' => $this->getStockAlertsSent()
            ];
        } catch (\Exception $e) {
            return [
                'automations_run_today' => 0,
                'success_rate' => 0,
                'whatsapp_messages_sent' => 0,
                'price_alerts_sent' => 0,
                'stock_alerts_sent' => 0
            ];
        }
    }
    
    // Métodos auxiliares para obtener métricas específicas
    
    private function getCpuUsage(): float
    {
        // Simulación de uso de CPU
        return round(rand(10, 80) + (rand(0, 100) / 100), 2);
    }
    
    private function getDiskUsage(): array
    {
        $totalSpace = disk_total_space('.');
        $freeSpace = disk_free_space('.');
        $usedSpace = $totalSpace - $freeSpace;
        
        return [
            'used_gb' => round($usedSpace / 1024 / 1024 / 1024, 2),
            'total_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
            'usage_percent' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
    }
    
    private function getLoadAverage(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2)
            ];
        }
        
        return ['1min' => 0, '5min' => 0, '15min' => 0];
    }
    
    private function getSystemUptime(): string
    {
        if (file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $seconds = (int) explode(' ', $uptime)[0];
            
            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            
            return "{$days}d {$hours}h {$minutes}m";
        }
        
        return 'N/A';
    }
    
    private function getDatabaseQueriesCount(): int
    {
        // En un entorno real, esto consultaría las estadísticas de MySQL
        return rand(1000, 5000);
    }
    
    private function getAverageQueryTime(): float
    {
        // Simulación de tiempo promedio de consulta
        return round(rand(50, 200) / 100, 3);
    }
    
    private function getSlowQueriesCount(): int
    {
        return rand(0, 10);
    }
    
    private function getQueriesPerSecond(): float
    {
        return round(rand(10, 100) + (rand(0, 100) / 100), 2);
    }
    
    private function getTableLocks(): int
    {
        return rand(0, 5);
    }
    
    private function getInnoDBBufferPool(): float
    {
        return round(rand(70, 95) + (rand(0, 100) / 100), 2);
    }
    
    private function getCacheHitRatio(): float
    {
        return round(rand(85, 98) + (rand(0, 100) / 100), 2);
    }
    
    private function getOptimizationScore(): int
    {
        return rand(75, 95);
    }
    
    private function getAverageResponseTime(): float
    {
        return round(rand(100, 500) + (rand(0, 100) / 100), 2);
    }
    
    private function getOnlineUsers(): int
    {
        // En un entorno real, esto consultaría sesiones activas
        return rand(5, 50);
    }
    
    private function getActiveSessions(): int
    {
        return rand(10, 100);
    }
    
    private function getBounceRate(): float
    {
        return round(rand(20, 60) + (rand(0, 100) / 100), 2);
    }
    
    private function getTodayRevenue(): float
    {
        $db = \Config\Database::connect();
        
        try {
            $result = $db->query("
                SELECT SUM(total_amount) as revenue 
                FROM orders 
                WHERE DATE(created_at) = CURDATE() 
                AND status = 'completed'
            ")->getRow();
            
            return round($result->revenue ?? 0, 2);
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    private function getConversionRate(): float
    {
        return round(rand(2, 8) + (rand(0, 100) / 100), 2);
    }
    
    private function getAverageOrderValue(): float
    {
        $db = \Config\Database::connect();
        
        try {
            $result = $db->query("
                SELECT AVG(total_amount) as avg_value 
                FROM orders 
                WHERE status = 'completed'
                AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
            ")->getRow();
            
            return round($result->avg_value ?? 0, 2);
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    private function getCartAbandonmentRate(): float
    {
        return round(rand(60, 80) + (rand(0, 100) / 100), 2);
    }
    
    private function getAutomationSuccessRate(): float
    {
        $db = \Config\Database::connect();
        
        try {
            $total = $db->table('automation_log')
                       ->where('DATE(created_at)', date('Y-m-d'))
                       ->countAllResults();
            
            $successful = $db->table('automation_log')
                            ->where('DATE(created_at)', date('Y-m-d'))
                            ->where('success', 1)
                            ->countAllResults();
            
            return $total > 0 ? round(($successful / $total) * 100, 2) : 100;
        } catch (\Exception $e) {
            return 100;
        }
    }
    
    private function getPriceAlertsSent(): int
    {
        $db = \Config\Database::connect();
        
        try {
            return $db->table('whatsapp_message_log')
                     ->where('DATE(created_at)', date('Y-m-d'))
                     ->where('template_key', 'price_alert')
                     ->where('status', 'sent')
                     ->countAllResults();
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    private function getStockAlertsSent(): int
    {
        $db = \Config\Database::connect();
        
        try {
            return $db->table('whatsapp_message_log')
                     ->where('DATE(created_at)', date('Y-m-d'))
                     ->where('template_key', 'stock_alert')
                     ->where('status', 'sent')
                     ->countAllResults();
        } catch (\Exception $e) {
            return 0;
        }
    }
}
