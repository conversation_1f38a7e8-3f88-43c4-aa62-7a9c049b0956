/**
 * Responsive Design CSS
 * MrCell Guatemala - Mobile-First Responsive Styles
 */

/* Base Variables */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* Global Responsive Utilities */
.container-fluid-mobile {
    padding-left: 15px;
    padding-right: 15px;
}

/* Typography Responsive */
@media (max-width: 576px) {
    h1, .h1 { font-size: 1.75rem; }
    h2, .h2 { font-size: 1.5rem; }
    h3, .h3 { font-size: 1.25rem; }
    h4, .h4 { font-size: 1.1rem; }
    h5, .h5 { font-size: 1rem; }
    
    .display-1 { font-size: 2.5rem; }
    .display-2 { font-size: 2rem; }
    .display-3 { font-size: 1.75rem; }
    .display-4 { font-size: 1.5rem; }
    .display-5 { font-size: 1.25rem; }
    .display-6 { font-size: 1.1rem; }
    
    .lead { font-size: 1rem; }
}

/* Navigation Responsive */
.navbar-brand {
    font-size: 1.1rem;
}

@media (max-width: 991px) {
    .navbar-collapse {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(255,255,255,0.1);
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
}

/* Cards Responsive */
.card {
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-header {
        padding: 0.75rem 1rem;
    }
}

/* Product Grid Responsive */
.product-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Product Cards */
.product-card {
    transition: var(--transition);
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

@media (max-width: 768px) {
    .product-image {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .product-image {
        height: 180px;
    }
}

/* Buttons Responsive */
@media (max-width: 576px) {
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
}

/* Forms Responsive */
.form-floating {
    margin-bottom: 1rem;
}

@media (max-width: 576px) {
    .form-floating {
        margin-bottom: 0.75rem;
    }
    
    .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .form-select {
        font-size: 16px;
    }
}

/* Tables Responsive */
.table-responsive-mobile {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .table-responsive-mobile table {
        font-size: 0.875rem;
    }
    
    .table-responsive-mobile th,
    .table-responsive-mobile td {
        padding: 0.5rem;
        white-space: nowrap;
    }
}

/* Modal Responsive */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-content {
        border-radius: 10px;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
    }
}

/* Spacing Responsive */
@media (max-width: 768px) {
    .py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }
    .py-4 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
    .my-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
    .my-4 { margin-top: 2rem !important; margin-bottom: 2rem !important; }
}

@media (max-width: 576px) {
    .py-5 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
    .py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
    .my-5 { margin-top: 2rem !important; margin-bottom: 2rem !important; }
    .my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }
}

/* Cart Responsive */
.cart-item {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .cart-item .row > div {
        margin-bottom: 0.5rem;
    }
    
    .cart-item .row > div:last-child {
        margin-bottom: 0;
    }
}

/* Checkout Responsive */
.checkout-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .checkout-section {
        margin-bottom: 1rem;
    }
}

/* Dashboard Responsive */
.dashboard-sidebar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

@media (max-width: 991px) {
    .dashboard-sidebar {
        position: static !important;
        margin-bottom: 2rem;
    }
    
    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0.5rem;
    }
    
    .sidebar-menu li {
        border-bottom: none;
        border-right: 1px solid #e9ecef;
        min-width: 150px;
        flex-shrink: 0;
    }
    
    .sidebar-menu li:last-child {
        border-right: none;
    }
    
    .sidebar-menu a {
        justify-content: center;
        text-align: center;
        flex-direction: column;
        padding: 1rem 0.5rem;
    }
    
    .sidebar-menu i {
        margin-right: 0;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }
}

/* Stats Cards Responsive */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    margin-bottom: 1rem;
}

@media (max-width: 576px) {
    .stats-card {
        padding: 1rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Search Responsive */
.search-bar {
    max-width: 500px;
}

@media (max-width: 768px) {
    .search-bar {
        max-width: 100%;
        margin-bottom: 1rem;
    }
}

/* Filters Responsive */
.filters-sidebar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

@media (max-width: 991px) {
    .filters-sidebar {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .filters-sidebar {
        padding: 1rem;
    }
}

/* Utilities */
.text-truncate-mobile {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 576px) {
    .text-truncate-mobile {
        max-width: 150px;
    }
}

.hide-mobile {
    display: block;
}

@media (max-width: 768px) {
    .hide-mobile {
        display: none !important;
    }
}

.show-mobile {
    display: none;
}

@media (max-width: 768px) {
    .show-mobile {
        display: block !important;
    }
}

/* Touch Improvements */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-control {
        min-height: 44px;
    }
    
    .form-select {
        min-height: 44px;
    }
    
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 10px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .sidebar-menu,
    .no-print {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
