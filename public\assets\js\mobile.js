/**
 * Mobile Enhancement JavaScript
 * MrCell Guatemala - Mobile-First Experience
 */

// Mobile Detection and Utilities
const MobileUtils = {
    // Check if device is mobile
    isMobile: function() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // Check if device is touch-enabled
    isTouch: function() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // Get viewport dimensions
    getViewport: function() {
        return {
            width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),
            height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)
        };
    },

    // Debounce function for resize events
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Mobile Navigation Enhancement
const MobileNav = {
    init: function() {
        this.setupMobileMenu();
        this.setupSearchToggle();
        this.setupStickyHeader();
    },

    setupMobileMenu: function() {
        // Auto-close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const navbar = document.querySelector('.navbar-collapse');
            const toggler = document.querySelector('.navbar-toggler');
            
            if (navbar && navbar.classList.contains('show') && 
                !navbar.contains(e.target) && !toggler.contains(e.target)) {
                const bsCollapse = new bootstrap.Collapse(navbar, {
                    toggle: false
                });
                bsCollapse.hide();
            }
        });

        // Close menu when clicking on nav links
        document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
            link.addEventListener('click', function() {
                const navbar = document.querySelector('.navbar-collapse');
                if (navbar && navbar.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(navbar, {
                        toggle: false
                    });
                    bsCollapse.hide();
                }
            });
        });
    },

    setupSearchToggle: function() {
        // Create mobile search toggle if it doesn't exist
        const searchForm = document.querySelector('.navbar .search-form, .navbar form[onsubmit*="search"]');
        if (searchForm && MobileUtils.isMobile()) {
            this.createMobileSearchToggle(searchForm);
        }
    },

    createMobileSearchToggle: function(searchForm) {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'btn btn-outline-light d-lg-none me-2';
        toggleBtn.innerHTML = '<i class="fas fa-search"></i>';
        toggleBtn.type = 'button';
        
        const searchContainer = document.createElement('div');
        searchContainer.className = 'mobile-search-container d-lg-none';
        searchContainer.style.display = 'none';
        
        // Clone search form for mobile
        const mobileSearch = searchForm.cloneNode(true);
        mobileSearch.className = 'mt-3 pt-3 border-top border-light';
        searchContainer.appendChild(mobileSearch);
        
        // Insert toggle button
        searchForm.parentNode.insertBefore(toggleBtn, searchForm);
        searchForm.parentNode.insertBefore(searchContainer, searchForm.nextSibling);
        
        // Hide original search on mobile
        searchForm.classList.add('d-none', 'd-lg-flex');
        
        // Toggle functionality
        toggleBtn.addEventListener('click', function() {
            const isVisible = searchContainer.style.display !== 'none';
            searchContainer.style.display = isVisible ? 'none' : 'block';
            toggleBtn.querySelector('i').className = isVisible ? 'fas fa-search' : 'fas fa-times';
        });
    },

    setupStickyHeader: function() {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        let lastScrollTop = 0;
        const scrollThreshold = 100;

        window.addEventListener('scroll', MobileUtils.debounce(function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (MobileUtils.isMobile()) {
                if (scrollTop > lastScrollTop && scrollTop > scrollThreshold) {
                    // Scrolling down - hide navbar
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up - show navbar
                    navbar.style.transform = 'translateY(0)';
                }
            }
            
            lastScrollTop = scrollTop;
        }, 100));
    }
};

// Mobile Forms Enhancement
const MobileForms = {
    init: function() {
        this.setupFormValidation();
        this.setupInputEnhancements();
        this.setupQuantityControls();
    },

    setupFormValidation: function() {
        // Add visual feedback for form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const invalidInputs = form.querySelectorAll(':invalid');
                if (invalidInputs.length > 0) {
                    e.preventDefault();
                    invalidInputs[0].focus();
                    invalidInputs[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    },

    setupInputEnhancements: function() {
        // Auto-format phone numbers
        document.querySelectorAll('input[type="tel"]').forEach(input => {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 8) {
                    value = value.replace(/(\d{4})(\d{4})/, '$1-$2');
                }
                e.target.value = value;
            });
        });

        // Auto-format currency inputs
        document.querySelectorAll('input[data-currency]').forEach(input => {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^\d.]/g, '');
                if (value) {
                    value = parseFloat(value).toFixed(2);
                    e.target.value = 'Q' + value;
                }
            });
        });
    },

    setupQuantityControls: function() {
        // Enhanced quantity controls for mobile
        document.querySelectorAll('.quantity-controls').forEach(container => {
            const input = container.querySelector('input[type="number"]');
            const minusBtn = container.querySelector('.quantity-btn:first-child');
            const plusBtn = container.querySelector('.quantity-btn:last-child');

            if (input && minusBtn && plusBtn) {
                // Add touch feedback
                [minusBtn, plusBtn].forEach(btn => {
                    btn.addEventListener('touchstart', function() {
                        this.style.transform = 'scale(0.95)';
                    });
                    
                    btn.addEventListener('touchend', function() {
                        this.style.transform = 'scale(1)';
                    });
                });

                // Prevent double-tap zoom on buttons
                [minusBtn, plusBtn].forEach(btn => {
                    btn.addEventListener('touchend', function(e) {
                        e.preventDefault();
                    });
                });
            }
        });
    }
};

// Mobile UI Enhancements
const MobileUI = {
    init: function() {
        this.setupTouchFeedback();
        this.setupSwipeGestures();
        this.setupLazyLoading();
        this.setupInfiniteScroll();
    },

    setupTouchFeedback: function() {
        // Add touch feedback to interactive elements
        const interactiveElements = document.querySelectorAll('.btn, .card, .product-card, .nav-link');
        
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.opacity = '0.8';
            });
            
            element.addEventListener('touchend', function() {
                this.style.opacity = '1';
            });
            
            element.addEventListener('touchcancel', function() {
                this.style.opacity = '1';
            });
        });
    },

    setupSwipeGestures: function() {
        // Simple swipe detection for carousels or image galleries
        let startX, startY, distX, distY;
        const threshold = 100;

        document.addEventListener('touchstart', function(e) {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
        });

        document.addEventListener('touchmove', function(e) {
            if (!startX || !startY) return;
            
            const touch = e.touches[0];
            distX = touch.clientX - startX;
            distY = touch.clientY - startY;
        });

        document.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;
            
            if (Math.abs(distX) > Math.abs(distY) && Math.abs(distX) > threshold) {
                const direction = distX > 0 ? 'right' : 'left';
                
                // Trigger custom swipe event
                const swipeEvent = new CustomEvent('swipe', {
                    detail: { direction: direction, target: e.target }
                });
                document.dispatchEvent(swipeEvent);
            }
            
            startX = startY = distX = distY = null;
        });
    },

    setupLazyLoading: function() {
        // Lazy load images for better performance
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }
    },

    setupInfiniteScroll: function() {
        // Infinite scroll for product listings
        const productContainer = document.querySelector('.product-grid, .products-container');
        if (!productContainer) return;

        let loading = false;
        let page = 1;

        const loadMoreProducts = async function() {
            if (loading) return;
            loading = true;

            try {
                // Show loading indicator
                const loadingEl = document.createElement('div');
                loadingEl.className = 'text-center py-4';
                loadingEl.innerHTML = '<div class="spinner-border text-primary" role="status"></div>';
                productContainer.appendChild(loadingEl);

                // Simulate API call (replace with actual API)
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Remove loading indicator
                loadingEl.remove();
                
                page++;
                loading = false;
            } catch (error) {
                console.error('Error loading more products:', error);
                loading = false;
            }
        };

        // Intersection Observer for infinite scroll
        const sentinel = document.createElement('div');
        sentinel.style.height = '1px';
        productContainer.appendChild(sentinel);

        const scrollObserver = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting && !loading) {
                loadMoreProducts();
            }
        }, { threshold: 0.1 });

        scrollObserver.observe(sentinel);
    }
};

// Mobile Performance Optimizations
const MobilePerformance = {
    init: function() {
        this.optimizeImages();
        this.setupServiceWorker();
        this.preloadCriticalResources();
    },

    optimizeImages: function() {
        // Add loading="lazy" to images
        document.querySelectorAll('img:not([loading])').forEach(img => {
            img.loading = 'lazy';
        });

        // Convert images to WebP if supported
        if (this.supportsWebP()) {
            document.querySelectorAll('img[data-webp]').forEach(img => {
                img.src = img.dataset.webp;
            });
        }
    },

    supportsWebP: function() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    },

    setupServiceWorker: function() {
        // Service Worker registration is completely disabled in mobile.js
        // PWA Manager handles all Service Worker functionality
        console.log('Mobile.js: Service Worker registration skipped - handled by PWA Manager');
    },

    preloadCriticalResources: function() {
        // Critical resources are already preloaded by the layout
        // This prevents duplicate preloading and conflicts
        console.log('Critical resources preloading handled by layout');
    }
};

// Initialize all mobile enhancements
document.addEventListener('DOMContentLoaded', function() {
    MobileNav.init();
    MobileForms.init();
    MobileUI.init();
    MobilePerformance.init();

    // Add mobile class to body
    if (MobileUtils.isMobile()) {
        document.body.classList.add('mobile-device');
    }

    if (MobileUtils.isTouch()) {
        document.body.classList.add('touch-device');
    }
});

// Handle orientation change
window.addEventListener('orientationchange', function() {
    // Force repaint after orientation change
    setTimeout(() => {
        window.scrollTo(0, window.scrollY);
    }, 100);
});

// Handle resize events
window.addEventListener('resize', MobileUtils.debounce(function() {
    // Update mobile state
    if (MobileUtils.isMobile()) {
        document.body.classList.add('mobile-device');
    } else {
        document.body.classList.remove('mobile-device');
    }
}, 250));

// Export for global use
window.MobileUtils = MobileUtils;
window.MobileNav = MobileNav;
window.MobileForms = MobileForms;
window.MobileUI = MobileUI;
