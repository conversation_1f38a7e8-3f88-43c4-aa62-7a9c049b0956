<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?? 'Editar Dirección' ?> - MrCell Guatemala
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-edit me-3"></i>Editar Dirección
                </h1>
                <p class="lead mb-0">Modifica los datos de tu dirección de envío</p>
            </div>
            <div class="col-lg-4 text-end">
                <img src="<?= base_url('assets/images/logo.png') ?>" alt="MrCell" class="img-fluid" style="max-height: 80px;">
            </div>
        </div>
    </div>
</section>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="<?= base_url('/') ?>" class="text-decoration-none">Inicio</a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= base_url('cuenta') ?>" class="text-decoration-none">Mi Cuenta</a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= base_url('cuenta/direcciones') ?>" class="text-decoration-none">Direcciones</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Editar</li>
        </ol>
    </div>
</nav>

<!-- Main Content -->
<div class="container my-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title"><?= ($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') ?: 'Usuario' ?></h5>
                    <p class="card-text text-muted"><?= $user['email'] ?? '<EMAIL>' ?></p>
                </div>
                <div class="list-group list-group-flush">
                    <a href="<?= base_url('cuenta') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="<?= base_url('cuenta/pedidos') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>Mis Pedidos
                    </a>
                    <a href="<?= base_url('cuenta/wishlist') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Lista de Deseos
                    </a>
                    <a href="<?= base_url('cuenta/direcciones') ?>" class="list-group-item list-group-item-action active">
                        <i class="fas fa-map-marker-alt me-2"></i>Direcciones
                    </a>
                    <a href="<?= base_url('cuenta/perfil') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i>Mi Perfil
                    </a>
                    <a href="<?= base_url('cuenta/seguridad') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt me-2"></i>Seguridad
                    </a>
                    <a href="<?= base_url('logout') ?>" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-edit me-2"></i>Editar Dirección
                        </h4>
                        <a href="<?= base_url('cuenta/direcciones') ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Volver
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Mensajes de error/éxito -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Por favor corrige los siguientes errores:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= $error ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Formulario de edición -->
                    <form method="POST" action="<?= base_url('cuenta/direcciones/update/' . $address['id']) ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="address_name" class="form-label">Nombre de la dirección *</label>
                                <input type="text" class="form-control" id="address_name" name="address_name"
                                       placeholder="Casa, Oficina, etc."
                                       value="<?= old('address_name', $address['address_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Nombre completo *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       placeholder="Nombre del destinatario"
                                       value="<?= old('full_name', $address['nombre_completo']) ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Teléfono *</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="+502 1234-5678"
                                       value="<?= old('phone', $address['telefono']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">Departamento *</label>
                                <select class="form-select" id="department" name="department" required>
                                    <option value="">Seleccionar departamento</option>
                                    <?php foreach ($departments as $department): ?>
                                        <option value="<?= $department ?>"
                                                <?= (old('department', $address['estado_departamento']) == $department) ? 'selected' : '' ?>>
                                            <?= $department ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="municipality" class="form-label">Municipio *</label>
                                <input type="text" class="form-control" id="municipality" name="municipality"
                                       placeholder="Municipio"
                                       value="<?= old('municipality', $address['ciudad']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="postal_code" class="form-label">Código postal *</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code"
                                       placeholder="01001"
                                       value="<?= old('postal_code', $address['codigo_postal']) ?>" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address_line" class="form-label">Dirección completa *</label>
                            <textarea class="form-control" id="address_line" name="address_line" rows="3"
                                      placeholder="Calle, número, zona, referencias..." required><?= old('address_line', $address['direccion_linea_1']) ?></textarea>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1"
                                       <?= $address['is_default'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_default">
                                    Establecer como dirección predeterminada
                                </label>
                            </div>
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Actualizar Dirección
                            </button>
                            <a href="<?= base_url('cuenta/direcciones') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    }
    
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.75rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
    }
    
    .btn-primary {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
    }
</style>

<?= $this->endSection() ?>
