# Disable directory browsing
Options -Indexes

# ----------------------------------------------------------------------
# Rewrite engine
# ----------------------------------------------------------------------

# Turning on the rewrite engine is necessary for the following rules and features.
# FollowSymLinks must be enabled for this to work.
<IfModule mod_rewrite.c>
	Options +FollowSymlinks
	RewriteEngine On

	# If you installed CodeIgniter in a subfolder, you will need to
	# change the following line to match the subfolder you need.
	# http://httpd.apache.org/docs/current/mod/mod_rewrite.html#rewritebase
	RewriteBase /

	# Force HTTPS (uncomment if you have SSL certificate)
	RewriteCond %{HTTPS} !=on
	RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

	# Redirect Trailing Slashes...
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteCond %{REQUEST_URI} (.+)/$
	RewriteRule ^ %1 [L,R=301]

	# Rewrite "www.example.com -> example.com" (optional)
	# RewriteCond %{HTTPS} !=on
	# RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
	# RewriteRule ^ http://%1%{REQUEST_URI} [R=301,L]

	# Handle static files first (including Service Worker)
	RewriteCond %{REQUEST_FILENAME} -f
	RewriteRule ^.*$ - [L]

	# Checks to see if the user is attempting to access a valid file,
	# such as an image or css document, if this isn't true it sends the
	# request to the front controller, index.php
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule ^(.*)$ index.php?/$1 [L,QSA]

	# Ensure Authorization header is passed along
	RewriteCond %{HTTP:Authorization} .
	RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
</IfModule>

<IfModule !mod_rewrite.c>
	# If we don't have mod_rewrite installed, all 404's
	# can be sent to index.php, and everything works as normal.
	ErrorDocument 404 index.php
</IfModule>

# Disable server signature start
ServerSignature Off
# Disable server signature end

# ----------------------------------------------------------------------
# MIME Types
# ----------------------------------------------------------------------

<IfModule mod_mime.c>
	# Service Worker files
	AddType application/javascript .js
	AddType application/json .json

	# Ensure Service Worker is served with correct MIME type
	<Files "sw.js">
		Header set Content-Type "application/javascript"
		Header set Service-Worker-Allowed "/"
	</Files>

	# Manifest file
	<Files "manifest.json">
		Header set Content-Type "application/json"
	</Files>
</IfModule>

# ----------------------------------------------------------------------
# Security Headers
# ----------------------------------------------------------------------

<IfModule mod_headers.c>
	# Security headers
	Header always set X-Content-Type-Options nosniff
	Header always set X-Frame-Options DENY
	Header always set X-XSS-Protection "1; mode=block"
	Header always set Referrer-Policy "strict-origin-when-cross-origin"

	# Remove server information
	Header unset Server
	Header unset X-Powered-By

	# Cache control for static assets
	<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
		Header set Cache-Control "public, max-age=31536000"
	</FilesMatch>

	# Service Worker should not be cached
	<Files "sw.js">
		Header set Cache-Control "no-cache, no-store, must-revalidate"
		Header set Pragma "no-cache"
		Header set Expires "0"
	</Files>
</IfModule>

# ----------------------------------------------------------------------
# File Security
# ----------------------------------------------------------------------

# Prevent access to sensitive files
<FilesMatch "^(\.env|\.htaccess|composer\.(json|lock)|package\.(json|lock)|\.git)">
	Order Allow,Deny
	Deny from all
</FilesMatch>

# Prevent access to PHP files in uploads directory
<FilesMatch "\.php$">
	<If "%{REQUEST_URI} =~ m#^/uploads/#">
		Order Allow,Deny
		Deny from all
	</If>
</FilesMatch>

# ----------------------------------------------------------------------
# Compression
# ----------------------------------------------------------------------

<IfModule mod_deflate.c>
	# Compress HTML, CSS, JavaScript, Text, XML and fonts
	AddOutputFilterByType DEFLATE application/javascript
	AddOutputFilterByType DEFLATE application/rss+xml
	AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
	AddOutputFilterByType DEFLATE application/x-font
	AddOutputFilterByType DEFLATE application/x-font-opentype
	AddOutputFilterByType DEFLATE application/x-font-otf
	AddOutputFilterByType DEFLATE application/x-font-truetype
	AddOutputFilterByType DEFLATE application/x-font-ttf
	AddOutputFilterByType DEFLATE application/x-javascript
	AddOutputFilterByType DEFLATE application/xhtml+xml
	AddOutputFilterByType DEFLATE application/xml
	AddOutputFilterByType DEFLATE font/opentype
	AddOutputFilterByType DEFLATE font/otf
	AddOutputFilterByType DEFLATE font/ttf
	AddOutputFilterByType DEFLATE image/svg+xml
	AddOutputFilterByType DEFLATE image/x-icon
	AddOutputFilterByType DEFLATE text/css
	AddOutputFilterByType DEFLATE text/html
	AddOutputFilterByType DEFLATE text/javascript
	AddOutputFilterByType DEFLATE text/plain
	AddOutputFilterByType DEFLATE text/xml
</IfModule>

# ----------------------------------------------------------------------
# Expires Headers
# ----------------------------------------------------------------------

<IfModule mod_expires.c>
	ExpiresActive on

	# Images
	ExpiresByType image/jpg "access plus 1 year"
	ExpiresByType image/jpeg "access plus 1 year"
	ExpiresByType image/gif "access plus 1 year"
	ExpiresByType image/png "access plus 1 year"
	ExpiresByType image/svg+xml "access plus 1 year"
	ExpiresByType image/x-icon "access plus 1 year"

	# CSS and JavaScript
	ExpiresByType text/css "access plus 1 month"
	ExpiresByType application/javascript "access plus 1 month"
	ExpiresByType text/javascript "access plus 1 month"

	# Fonts
	ExpiresByType font/ttf "access plus 1 year"
	ExpiresByType font/otf "access plus 1 year"
	ExpiresByType font/woff "access plus 1 year"
	ExpiresByType font/woff2 "access plus 1 year"
	ExpiresByType application/font-woff "access plus 1 year"

	# HTML
	ExpiresByType text/html "access plus 1 hour"
</IfModule>
