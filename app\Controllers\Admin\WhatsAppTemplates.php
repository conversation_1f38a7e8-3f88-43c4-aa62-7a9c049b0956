<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\WhatsAppNotifier;

/**
 * Controlador para gestión de templates de WhatsApp
 * Panel de administración para configurar mensajes
 */
class WhatsAppTemplates extends BaseController
{
    protected $db;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }
    
    /**
     * Listar templates
     */
    public function index()
    {
        $builder = $this->db->table('whatsapp_templates');
        $builder->orderBy('category', 'ASC');
        $builder->orderBy('name', 'ASC');
        
        $templates = $builder->get()->getResultArray();
        
        // Agrupar por categoría
        $groupedTemplates = [];
        foreach ($templates as $template) {
            $groupedTemplates[$template['category']][] = $template;
        }
        
        $data = [
            'title' => 'Templates de WhatsApp',
            'templates' => $templates,
            'grouped_templates' => $groupedTemplates,
            'categories' => [
                'price_alert' => 'Alertas de Precio',
                'stock_alert' => 'Alertas de Stock',
                'wishlist_reminder' => 'Recordatorios',
                'newsletter' => 'Newsletter',
                'general' => 'General'
            ]
        ];
        
        return view('admin/whatsapp_templates/index', $data);
    }
    
    /**
     * Crear nuevo template
     */
    public function create()
    {
        if ($this->request->getMethod() === 'POST') {
            return $this->store();
        }
        
        $data = [
            'title' => 'Crear Template de WhatsApp',
            'template' => null,
            'categories' => [
                'price_alert' => 'Alertas de Precio',
                'stock_alert' => 'Alertas de Stock',
                'wishlist_reminder' => 'Recordatorios',
                'newsletter' => 'Newsletter',
                'general' => 'General'
            ],
            'available_variables' => $this->getAvailableVariables()
        ];
        
        return view('admin/whatsapp_templates/form', $data);
    }
    
    /**
     * Guardar nuevo template
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'name' => 'required|max_length[100]',
            'key' => 'required|max_length[50]|is_unique[whatsapp_templates.key]',
            'title' => 'required|max_length[200]',
            'message' => 'required',
            'category' => 'required|in_list[price_alert,stock_alert,wishlist_reminder,newsletter,general]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }
        
        $data = [
            'name' => $this->request->getPost('name'),
            'key' => $this->request->getPost('key'),
            'title' => $this->request->getPost('title'),
            'message' => $this->request->getPost('message'),
            'category' => $this->request->getPost('category'),
            'variables' => json_encode($this->extractVariables($this->request->getPost('message'))),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->db->table('whatsapp_templates')->insert($data)) {
            return redirect()->to('/admin/whatsapp-templates')->with('success', 'Template creado exitosamente');
        } else {
            return redirect()->back()->withInput()->with('error', 'Error al crear el template');
        }
    }
    
    /**
     * Editar template
     */
    public function edit($id)
    {
        $template = $this->db->table('whatsapp_templates')->where('id', $id)->get()->getRowArray();
        
        if (!$template) {
            return redirect()->to('/admin/whatsapp-templates')->with('error', 'Template no encontrado');
        }
        
        if ($this->request->getMethod() === 'POST') {
            return $this->update($id);
        }
        
        $data = [
            'title' => 'Editar Template de WhatsApp',
            'template' => $template,
            'categories' => [
                'price_alert' => 'Alertas de Precio',
                'stock_alert' => 'Alertas de Stock',
                'wishlist_reminder' => 'Recordatorios',
                'newsletter' => 'Newsletter',
                'general' => 'General'
            ],
            'available_variables' => $this->getAvailableVariables()
        ];
        
        return view('admin/whatsapp_templates/form', $data);
    }
    
    /**
     * Actualizar template
     */
    public function update($id)
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'name' => 'required|max_length[100]',
            'key' => "required|max_length[50]|is_unique[whatsapp_templates.key,id,{$id}]",
            'title' => 'required|max_length[200]',
            'message' => 'required',
            'category' => 'required|in_list[price_alert,stock_alert,wishlist_reminder,newsletter,general]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }
        
        $data = [
            'name' => $this->request->getPost('name'),
            'key' => $this->request->getPost('key'),
            'title' => $this->request->getPost('title'),
            'message' => $this->request->getPost('message'),
            'category' => $this->request->getPost('category'),
            'variables' => json_encode($this->extractVariables($this->request->getPost('message'))),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->db->table('whatsapp_templates')->where('id', $id)->update($data)) {
            return redirect()->to('/admin/whatsapp-templates')->with('success', 'Template actualizado exitosamente');
        } else {
            return redirect()->back()->withInput()->with('error', 'Error al actualizar el template');
        }
    }
    
    /**
     * Eliminar template
     */
    public function delete($id)
    {
        $template = $this->db->table('whatsapp_templates')->where('id', $id)->get()->getRowArray();
        
        if (!$template) {
            return redirect()->to('/admin/whatsapp-templates')->with('error', 'Template no encontrado');
        }
        
        if ($this->db->table('whatsapp_templates')->where('id', $id)->delete()) {
            return redirect()->to('/admin/whatsapp-templates')->with('success', 'Template eliminado exitosamente');
        } else {
            return redirect()->to('/admin/whatsapp-templates')->with('error', 'Error al eliminar el template');
        }
    }
    
    /**
     * Probar template
     */
    public function test($id)
    {
        $template = $this->db->table('whatsapp_templates')->where('id', $id)->get()->getRowArray();
        
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'error' => 'Template no encontrado']);
        }
        
        if ($this->request->getMethod() === 'POST') {
            $testPhone = $this->request->getPost('test_phone');
            $testData = $this->request->getPost('test_data') ?? [];
            
            if (empty($testPhone)) {
                return $this->response->setJSON(['success' => false, 'error' => 'Número de teléfono requerido']);
            }
            
            // Reemplazar variables con datos de prueba
            $message = $this->replaceVariables($template['message'], $testData);
            
            // Enviar mensaje de prueba
            $whatsapp = new WhatsAppNotifier();
            $result = $whatsapp->sendCustomMessage($testPhone, $message);
            
            if ($result['success']) {
                // Incrementar contador de uso
                $this->db->table('whatsapp_templates')
                        ->where('id', $id)
                        ->set('usage_count', 'usage_count + 1', false)
                        ->update();
            }
            
            return $this->response->setJSON($result);
        }
        
        $data = [
            'title' => 'Probar Template: ' . $template['name'],
            'template' => $template,
            'variables' => json_decode($template['variables'] ?? '[]', true),
            'sample_data' => $this->getSampleData($template['category'])
        ];
        
        return view('admin/whatsapp_templates/test', $data);
    }
    
    /**
     * Configuración de WhatsApp
     */
    public function settings()
    {
        if ($this->request->getMethod() === 'POST') {
            return $this->saveSettings();
        }
        
        $whatsapp = new WhatsAppNotifier();
        $config = $whatsapp->getConfig();
        
        $data = [
            'title' => 'Configuración de WhatsApp',
            'config' => $config,
            'current_settings' => [
                'enabled' => env('WHATSAPP_NOTIFICATIONS_ENABLED', false),
                'api_url' => env('WHATSAPP_API_URL', ''),
                'api_key' => env('WHATSAPP_API_KEY', ''),
                'device_token' => env('WHATSAPP_DEVICE_TOKEN', ''),
                'test_phone' => env('WHATSAPP_TEST_PHONE', '')
            ]
        ];
        
        return view('admin/whatsapp_templates/settings', $data);
    }
    
    /**
     * Guardar configuración
     */
    private function saveSettings()
    {
        $settings = [
            'WHATSAPP_NOTIFICATIONS_ENABLED' => $this->request->getPost('enabled') ? 'true' : 'false',
            'WHATSAPP_API_URL' => $this->request->getPost('api_url'),
            'WHATSAPP_API_KEY' => $this->request->getPost('api_key'),
            'WHATSAPP_DEVICE_TOKEN' => $this->request->getPost('device_token'),
            'WHATSAPP_TEST_PHONE' => $this->request->getPost('test_phone')
        ];
        
        // Actualizar archivo .env (implementación básica)
        $envFile = ROOTPATH . '.env';
        $envContent = file_get_contents($envFile);
        
        foreach ($settings as $key => $value) {
            if (strpos($envContent, $key) !== false) {
                $envContent = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $envContent);
            } else {
                $envContent .= "\n{$key}={$value}";
            }
        }
        
        if (file_put_contents($envFile, $envContent)) {
            return redirect()->to('/admin/whatsapp-templates/settings')->with('success', 'Configuración guardada exitosamente');
        } else {
            return redirect()->back()->with('error', 'Error al guardar la configuración');
        }
    }
    
    /**
     * Extraer variables del mensaje
     */
    private function extractVariables(string $message): array
    {
        preg_match_all('/\{([^}]+)\}/', $message, $matches);
        return array_unique($matches[1]);
    }
    
    /**
     * Reemplazar variables en el mensaje
     */
    private function replaceVariables(string $message, array $data): string
    {
        foreach ($data as $key => $value) {
            $message = str_replace('{' . $key . '}', $value, $message);
        }
        return $message;
    }
    
    /**
     * Obtener variables disponibles por categoría
     */
    private function getAvailableVariables(): array
    {
        return [
            'general' => ['name', 'store_url', 'support_phone', 'support_email'],
            'price_alert' => ['name', 'product_name', 'old_price', 'new_price', 'savings', 'percentage', 'product_url'],
            'stock_alert' => ['name', 'product_name', 'price', 'stock_quantity', 'product_url'],
            'wishlist_reminder' => ['name', 'count', 'product_list', 'wishlist_url'],
            'newsletter' => ['name', 'offer_title', 'discount', 'expiry_date', 'offer_description', 'offer_url']
        ];
    }
    
    /**
     * Obtener datos de muestra para pruebas
     */
    private function getSampleData(string $category): array
    {
        $samples = [
            'general' => [
                'name' => 'Juan Pérez',
                'store_url' => base_url(),
                'support_phone' => '+502 1234-5678',
                'support_email' => '<EMAIL>'
            ],
            'price_alert' => [
                'name' => 'María García',
                'product_name' => 'iPhone 13 Pro',
                'old_price' => '1200.00',
                'new_price' => '999.00',
                'savings' => '201.00',
                'percentage' => '16.8',
                'product_url' => base_url('producto/iphone-13-pro')
            ],
            'stock_alert' => [
                'name' => 'Carlos López',
                'product_name' => 'Samsung Galaxy S23',
                'price' => '899.00',
                'stock_quantity' => '5',
                'product_url' => base_url('producto/samsung-galaxy-s23')
            ],
            'wishlist_reminder' => [
                'name' => 'Ana Rodríguez',
                'count' => '3',
                'product_list' => "1. iPhone 13 Pro - Q999.00\n2. AirPods Pro - Q299.00\n3. MacBook Air - Q1,299.00",
                'wishlist_url' => base_url('wishlist')
            ],
            'newsletter' => [
                'name' => 'Pedro Morales',
                'offer_title' => 'Black Friday 2025',
                'discount' => '30',
                'expiry_date' => '30 de Noviembre',
                'offer_description' => 'Descuentos increíbles en toda la tienda',
                'offer_url' => base_url('ofertas/black-friday')
            ]
        ];
        
        return $samples[$category] ?? $samples['general'];
    }
}
