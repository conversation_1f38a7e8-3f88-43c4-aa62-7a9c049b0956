/**
 * Mr<PERSON><PERSON> - Tema Personalizado
 * Colores: <PERSON><PERSON><PERSON> (#DC2626), <PERSON> (#000000), <PERSON> (#FFFFFF)
 */

:root {
    /* Colores principales de MrCell */
    --mrcell-red: #DC2626;
    --mrcell-red-dark: #B91C1C;
    --mrcell-red-light: #EF4444;
    --mrcell-black: #000000;
    --mrcell-black-light: #1F2937;
    --mrcell-white: #FFFFFF;
    --mrcell-gray-light: #F9FAFB;
    --mrcell-gray: #6B7280;
    --mrcell-gray-dark: #374151;
    
    /* Colores de estado */
    --mrcell-success: #059669;
    --mrcell-warning: #D97706;
    --mrcell-danger: #DC2626;
    --mrcell-info: #2563EB;
    
    /* Tipografía */
    --mrcell-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --mrcell-font-size-base: 16px;
    --mrcell-line-height-base: 1.6;
    
    /* Espaciado */
    --mrcell-spacing-xs: 0.25rem;
    --mrcell-spacing-sm: 0.5rem;
    --mrcell-spacing-md: 1rem;
    --mrcell-spacing-lg: 1.5rem;
    --mrcell-spacing-xl: 2rem;
    --mrcell-spacing-2xl: 3rem;
    
    /* Bordes */
    --mrcell-border-radius: 8px;
    --mrcell-border-radius-lg: 12px;
    --mrcell-border-width: 1px;
    
    /* Sombras */
    --mrcell-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --mrcell-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --mrcell-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Estilos base */
body {
    font-family: var(--mrcell-font-family);
    font-size: var(--mrcell-font-size-base);
    line-height: var(--mrcell-line-height-base);
    color: var(--mrcell-black);
    background-color: var(--mrcell-white);
}

/* Header y navegación */
.navbar-brand {
    color: var(--mrcell-red) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--mrcell-black) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--mrcell-red) !important;
}

/* Botones */
.btn-primary {
    background-color: var(--mrcell-red);
    border-color: var(--mrcell-red);
    color: var(--mrcell-white);
    font-weight: 600;
    border-radius: var(--mrcell-border-radius);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--mrcell-red-dark);
    border-color: var(--mrcell-red-dark);
    transform: translateY(-1px);
    box-shadow: var(--mrcell-shadow-lg);
}

.btn-secondary {
    background-color: var(--mrcell-black);
    border-color: var(--mrcell-black);
    color: var(--mrcell-white);
}

.btn-secondary:hover {
    background-color: var(--mrcell-black-light);
    border-color: var(--mrcell-black-light);
}

.btn-outline-primary {
    color: var(--mrcell-red);
    border-color: var(--mrcell-red);
}

.btn-outline-primary:hover {
    background-color: var(--mrcell-red);
    border-color: var(--mrcell-red);
    color: var(--mrcell-white);
}

/* Cards de productos */
.product-card {
    background: var(--mrcell-white);
    border: var(--mrcell-border-width) solid #E5E7EB;
    border-radius: var(--mrcell-border-radius-lg);
    box-shadow: var(--mrcell-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--mrcell-shadow-lg);
    border-color: var(--mrcell-red);
}

.product-card .card-title {
    color: var(--mrcell-black);
    font-weight: 600;
    font-size: 1.1rem;
}

.product-card .price {
    color: var(--mrcell-red);
    font-weight: 700;
    font-size: 1.25rem;
}

.product-card .price-original {
    color: var(--mrcell-gray);
    text-decoration: line-through;
    font-size: 1rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--mrcell-black) 0%, var(--mrcell-black-light) 100%);
    color: var(--mrcell-white);
    padding: var(--mrcell-spacing-2xl) 0;
}

.footer h5 {
    color: var(--mrcell-red);
    font-weight: 600;
    margin-bottom: var(--mrcell-spacing-lg);
}

.footer p, .footer a {
    color: #D1D5DB;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--mrcell-red);
}

.footer .social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--mrcell-red);
    color: var(--mrcell-white);
    border-radius: 50%;
    margin-right: var(--mrcell-spacing-sm);
    transition: all 0.3s ease;
}

.footer .social-links a:hover {
    background-color: var(--mrcell-red-light);
    transform: translateY(-2px);
}

/* Alertas y notificaciones */
.alert-success {
    background-color: #D1FAE5;
    border-color: var(--mrcell-success);
    color: #065F46;
}

.alert-danger {
    background-color: #FEE2E2;
    border-color: var(--mrcell-danger);
    color: #991B1B;
}

.alert-warning {
    background-color: #FEF3C7;
    border-color: var(--mrcell-warning);
    color: #92400E;
}

.alert-info {
    background-color: #DBEAFE;
    border-color: var(--mrcell-info);
    color: #1E40AF;
}

/* Formularios */
.form-control {
    border-radius: var(--mrcell-border-radius);
    border-color: #D1D5DB;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--mrcell-red);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
}

/* Badges */
.badge-primary {
    background-color: var(--mrcell-red);
}

.badge-secondary {
    background-color: var(--mrcell-black);
}

.badge-success {
    background-color: var(--mrcell-success);
}

.badge-warning {
    background-color: var(--mrcell-warning);
}

.badge-danger {
    background-color: var(--mrcell-danger);
}

/* Carrito de compras */
.cart-item {
    background: var(--mrcell-white);
    border: var(--mrcell-border-width) solid #E5E7EB;
    border-radius: var(--mrcell-border-radius);
    padding: var(--mrcell-spacing-lg);
    margin-bottom: var(--mrcell-spacing-md);
}

.cart-total {
    background: var(--mrcell-gray-light);
    border-radius: var(--mrcell-border-radius);
    padding: var(--mrcell-spacing-lg);
}

.cart-total .total-amount {
    color: var(--mrcell-red);
    font-weight: 700;
    font-size: 1.5rem;
}

/* Breadcrumbs */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--mrcell-red);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--mrcell-gray);
}

/* Paginación */
.pagination .page-link {
    color: var(--mrcell-red);
    border-color: #E5E7EB;
}

.pagination .page-link:hover {
    background-color: var(--mrcell-red);
    border-color: var(--mrcell-red);
    color: var(--mrcell-white);
}

.pagination .page-item.active .page-link {
    background-color: var(--mrcell-red);
    border-color: var(--mrcell-red);
}

/* Spinner de carga */
.spinner-border-primary {
    color: var(--mrcell-red);
}

/* Responsive */
@media (max-width: 768px) {
    .product-card {
        margin-bottom: var(--mrcell-spacing-lg);
    }
    
    .footer {
        padding: var(--mrcell-spacing-xl) 0;
    }
    
    .footer .social-links {
        text-align: center;
        margin-top: var(--mrcell-spacing-lg);
    }
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* PWA Install Button */
.pwa-install-btn {
    background: linear-gradient(135deg, var(--mrcell-red) 0%, var(--mrcell-red-dark) 100%);
    color: var(--mrcell-white);
    border: none;
    border-radius: var(--mrcell-border-radius);
    padding: var(--mrcell-spacing-sm) var(--mrcell-spacing-lg);
    font-weight: 600;
    box-shadow: var(--mrcell-shadow);
    transition: all 0.3s ease;
}

.pwa-install-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--mrcell-shadow-lg);
}

/* Utilidades */
.text-mrcell-red { color: var(--mrcell-red) !important; }
.text-mrcell-black { color: var(--mrcell-black) !important; }
.text-mrcell-white { color: var(--mrcell-white) !important; }
.bg-mrcell-red { background-color: var(--mrcell-red) !important; }
.bg-mrcell-black { background-color: var(--mrcell-black) !important; }
.bg-mrcell-white { background-color: var(--mrcell-white) !important; }
