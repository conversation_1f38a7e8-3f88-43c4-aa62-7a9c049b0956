<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Integrations extends BaseConfig
{
    /**
     * Configuración de Facturación Electrónica
     */
    public array $billing = [
        'default_provider' => 'facturalo',
        'providers' => [
            'facturalo' => [
                'name' => 'Facturalo by MayanSource',
                'api_url' => 'https://facturalo.mayansource.com/api',
                'sandbox_url' => 'https://sandbox.facturalo.mayansource.com/api',
                'timeout' => 30,
                'retry_attempts' => 3,
                'supported_features' => [
                    'electronic_invoice',
                    'credit_note',
                    'debit_note',
                    'receipt',
                    'xml_generation',
                    'pdf_generation',
                    'email_sending',
                    'webhook_notifications'
                ],
                'required_fields' => [
                    'api_key',
                    'company_nit',
                    'company_name',
                    'company_address'
                ]
            ]
        ],
        'auto_generate' => false,
        'send_email' => true,
        'store_xml' => true,
        'store_pdf' => true,
        'webhook_secret' => null
    ];

    /**
     * Configuración de Social Manager
     */
    public array $social = [
        'default_provider' => 'socialmanager',
        'providers' => [
            'socialmanager' => [
                'name' => 'Social Manager by MayanSource',
                'api_url' => 'https://socialmanager.mayansource.com/api',
                'sandbox_url' => 'https://sandbox.socialmanager.mayansource.com/api',
                'timeout' => 30,
                'supported_platforms' => [
                    'facebook' => [
                        'name' => 'Facebook',
                        'icon' => 'fab fa-facebook',
                        'color' => '#1877F2',
                        'features' => ['post', 'image', 'video', 'story', 'schedule']
                    ],
                    'instagram' => [
                        'name' => 'Instagram',
                        'icon' => 'fab fa-instagram',
                        'color' => '#E4405F',
                        'features' => ['post', 'image', 'video', 'story', 'reel', 'schedule']
                    ],
                    'twitter' => [
                        'name' => 'Twitter/X',
                        'icon' => 'fab fa-twitter',
                        'color' => '#1DA1F2',
                        'features' => ['tweet', 'image', 'video', 'thread', 'schedule']
                    ],
                    'linkedin' => [
                        'name' => 'LinkedIn',
                        'icon' => 'fab fa-linkedin',
                        'color' => '#0A66C2',
                        'features' => ['post', 'image', 'video', 'article', 'schedule']
                    ],
                    'tiktok' => [
                        'name' => 'TikTok',
                        'icon' => 'fab fa-tiktok',
                        'color' => '#000000',
                        'features' => ['video', 'schedule']
                    ],
                    'youtube' => [
                        'name' => 'YouTube',
                        'icon' => 'fab fa-youtube',
                        'color' => '#FF0000',
                        'features' => ['video', 'short', 'schedule']
                    ]
                ],
                'required_fields' => [
                    'api_key'
                ]
            ]
        ],
        'auto_publish' => false,
        'publish_new_products' => false,
        'publish_promotions' => true,
        'default_hashtags' => '#MrCell #Guatemala #Tecnologia',
        'content_templates' => [
            'new_product' => '🆕 ¡Nuevo producto disponible! {product_name} por solo Q{price}. ¡No te lo pierdas! 📱✨',
            'promotion' => '🔥 ¡Oferta especial! {promotion_name} - {discount}% de descuento. ¡Aprovecha ahora! 💥',
            'sale' => '💰 ¡Gran descuento! {product_name} ahora por Q{sale_price} (antes Q{original_price}). ¡Oferta limitada! ⏰'
        ]
    ];

    /**
     * Configuración de Envíos
     */
    public array $shipping = [
        'default_companies' => [
            'cargoexpreso' => [
                'name' => 'Cargo Expreso',
                'code' => 'cargoexpreso',
                'phone' => '2234-5678',
                'website' => 'https://cargoexpreso.com.gt',
                'tracking_url' => 'https://cargoexpreso.com.gt/tracking/{tracking_number}',
                'supports_tracking' => true,
                'supports_pickup' => true,
                'max_weight' => 50.00,
                'coverage_areas' => ['Guatemala', 'Sacatepéquez', 'Chimaltenango', 'Escuintla']
            ],
            'guatex' => [
                'name' => 'GuatEx',
                'code' => 'guatex',
                'phone' => '2345-6789',
                'website' => 'https://guatex.gt',
                'tracking_url' => 'https://guatex.gt/rastreo/{tracking_number}',
                'supports_tracking' => true,
                'supports_pickup' => true,
                'max_weight' => 30.00,
                'coverage_areas' => ['Guatemala', 'Mixco', 'Villa Nueva', 'San Miguel Petapa']
            ],
            'forza' => [
                'name' => 'Forza',
                'code' => 'forza',
                'phone' => '2456-7890',
                'website' => 'https://forza.com.gt',
                'tracking_url' => 'https://forza.com.gt/seguimiento/{tracking_number}',
                'supports_tracking' => true,
                'supports_pickup' => false,
                'max_weight' => 25.00,
                'coverage_areas' => ['Guatemala', 'Quetzaltenango', 'Huehuetenango', 'Cobán']
            ]
        ],
        'default_rates' => [
            'guatemala_capital' => [
                'zone' => 'Guatemala Capital',
                'standard' => ['base_cost' => 25.00, 'cost_per_kg' => 5.00, 'days_min' => 1, 'days_max' => 2],
                'express' => ['base_cost' => 45.00, 'cost_per_kg' => 8.00, 'days_min' => 1, 'days_max' => 1]
            ],
            'zona_metropolitana' => [
                'zone' => 'Zona Metropolitana',
                'standard' => ['base_cost' => 35.00, 'cost_per_kg' => 6.00, 'days_min' => 2, 'days_max' => 3],
                'express' => ['base_cost' => 55.00, 'cost_per_kg' => 10.00, 'days_min' => 1, 'days_max' => 2]
            ],
            'departamentos' => [
                'zone' => 'Departamentos',
                'standard' => ['base_cost' => 45.00, 'cost_per_kg' => 8.00, 'days_min' => 3, 'days_max' => 5],
                'express' => ['base_cost' => 75.00, 'cost_per_kg' => 12.00, 'days_min' => 2, 'days_max' => 3]
            ]
        ],
        'free_shipping_threshold' => 200.00,
        'default_weight' => 0.5,
        'max_weight' => 50.00,
        'tracking_update_interval' => 3600, // 1 hora en segundos
        'notification_settings' => [
            'email' => true,
            'sms' => false,
            'push' => true
        ]
    ];

    /**
     * Configuración de Cupones
     */
    public array $coupons = [
        'default_settings' => [
            'usage_limit_per_user' => 1,
            'stackable' => false,
            'auto_apply' => false,
            'first_order_only' => false,
            'min_order_amount' => 0
        ],
        'types' => [
            'percentage' => [
                'name' => 'Porcentaje',
                'icon' => 'fas fa-percentage',
                'max_value' => 100,
                'min_value' => 1
            ],
            'fixed' => [
                'name' => 'Cantidad Fija',
                'icon' => 'fas fa-dollar-sign',
                'max_value' => null,
                'min_value' => 1
            ],
            'free_shipping' => [
                'name' => 'Envío Gratis',
                'icon' => 'fas fa-truck',
                'max_value' => null,
                'min_value' => null
            ],
            'buy_x_get_y' => [
                'name' => 'Compra X Lleva Y',
                'icon' => 'fas fa-gift',
                'max_value' => null,
                'min_value' => 1
            ]
        ],
        'auto_coupons' => [
            'welcome' => [
                'code_prefix' => 'BIENVENIDO',
                'type' => 'percentage',
                'value' => 10,
                'usage_limit_per_user' => 1,
                'first_order_only' => true,
                'valid_days' => 30
            ],
            'birthday' => [
                'code_prefix' => 'BIRTHDAY',
                'type' => 'percentage',
                'value' => 15,
                'usage_limit_per_user' => 1,
                'valid_days' => 7
            ],
            'loyalty' => [
                'code_prefix' => 'LOYALTY',
                'type' => 'percentage',
                'value' => 20,
                'min_orders' => 5,
                'usage_limit_per_user' => 3
            ]
        ],
        'validation_rules' => [
            'code_min_length' => 3,
            'code_max_length' => 50,
            'code_pattern' => '/^[A-Z0-9_-]+$/',
            'name_max_length' => 255,
            'description_max_length' => 1000
        ]
    ];

    /**
     * Configuración de KPIs y Analytics
     */
    public array $analytics = [
        'kpi_metrics' => [
            'sales' => [
                'total_revenue',
                'average_order_value',
                'conversion_rate',
                'sales_growth',
                'top_products',
                'sales_by_category'
            ],
            'inventory' => [
                'stock_levels',
                'low_stock_alerts',
                'inventory_turnover',
                'out_of_stock_items',
                'inventory_value'
            ],
            'customer' => [
                'new_customers',
                'customer_retention',
                'customer_lifetime_value',
                'repeat_purchase_rate',
                'customer_satisfaction'
            ],
            'shipping' => [
                'delivery_performance',
                'shipping_costs',
                'delivery_times',
                'shipping_issues',
                'carrier_performance'
            ],
            'marketing' => [
                'coupon_usage',
                'promotion_effectiveness',
                'social_engagement',
                'email_performance',
                'traffic_sources'
            ]
        ],
        'refresh_intervals' => [
            'real_time' => 60,      // 1 minuto
            'hourly' => 3600,       // 1 hora
            'daily' => 86400,       // 24 horas
            'weekly' => 604800,     // 7 días
            'monthly' => 2592000    // 30 días
        ],
        'alert_thresholds' => [
            'low_stock' => 10,
            'high_cart_abandonment' => 70,
            'low_conversion_rate' => 2,
            'high_shipping_issues' => 5
        ]
    ];

    /**
     * Configuración de Notificaciones
     */
    public array $notifications = [
        'channels' => [
            'email' => true,
            'sms' => false,
            'push' => true,
            'webhook' => false
        ],
        'templates' => [
            'order_confirmation',
            'shipping_notification',
            'delivery_confirmation',
            'invoice_generated',
            'coupon_expiring',
            'low_stock_alert',
            'new_product_published'
        ],
        'email_settings' => [
            'from_name' => 'MrCell Guatemala',
            'from_email' => '<EMAIL>',
            'reply_to' => '<EMAIL>'
        ]
    ];
}
