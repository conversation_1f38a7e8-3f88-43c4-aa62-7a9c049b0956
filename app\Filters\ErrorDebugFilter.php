<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class ErrorDebugFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Habilitar captura de errores para rutas de admin
        if (strpos($request->getUri()->getPath(), 'admin') !== false) {
            // Configurar manejo de errores personalizado
            set_error_handler([$this, 'customErrorHandler']);
            set_exception_handler([$this, 'customExceptionHandler']);
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an exception or error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Verificar si hay error 500 y agregar información de debug
        if ($response->getStatusCode() === 500) {
            $this->logErrorDetails($request, $response);
        }
    }

    /**
     * Manejador personalizado de errores
     */
    public function customErrorHandler($severity, $message, $file, $line)
    {
        $errorInfo = [
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => current_url(),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'post_data' => $_POST ?? [],
            'get_data' => $_GET ?? []
        ];

        log_message('error', 'Error capturado por ErrorDebugFilter: ' . json_encode($errorInfo));

        // Si es un error fatal, mostrar información detallada
        if ($severity === E_ERROR || $severity === E_CORE_ERROR || $severity === E_COMPILE_ERROR) {
            $this->showDetailedError($errorInfo);
        }

        return false; // Permitir que el manejador de errores por defecto también procese
    }

    /**
     * Manejador personalizado de excepciones
     */
    public function customExceptionHandler($exception)
    {
        $exceptionInfo = [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => current_url(),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'post_data' => $_POST ?? [],
            'get_data' => $_GET ?? []
        ];

        log_message('error', 'Excepción capturada por ErrorDebugFilter: ' . json_encode($exceptionInfo));

        $this->showDetailedError($exceptionInfo);
    }

    /**
     * Mostrar error detallado
     */
    private function showDetailedError($errorInfo)
    {
        // Solo mostrar detalles en desarrollo
        if (ENVIRONMENT === 'development') {
            http_response_code(500);
            header('Content-Type: text/html; charset=utf-8');
            
            echo '<!DOCTYPE html>';
            echo '<html><head><title>Error 500 - Debug Info</title>';
            echo '<style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .error-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .error-title { color: #d32f2f; font-size: 24px; margin-bottom: 20px; }
                .error-section { margin-bottom: 20px; }
                .error-label { font-weight: bold; color: #333; }
                .error-value { background: #f8f8f8; padding: 10px; border-radius: 4px; margin: 5px 0; }
                .error-trace { background: #f0f0f0; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
                .error-data { background: #e3f2fd; padding: 10px; border-radius: 4px; }
            </style></head><body>';
            
            echo '<div class="error-container">';
            echo '<div class="error-title">🚨 Error 500 - Información de Debug</div>';
            
            echo '<div class="error-section">';
            echo '<div class="error-label">Mensaje:</div>';
            echo '<div class="error-value">' . htmlspecialchars($errorInfo['message']) . '</div>';
            echo '</div>';
            
            echo '<div class="error-section">';
            echo '<div class="error-label">Archivo:</div>';
            echo '<div class="error-value">' . htmlspecialchars($errorInfo['file']) . ':' . $errorInfo['line'] . '</div>';
            echo '</div>';
            
            echo '<div class="error-section">';
            echo '<div class="error-label">URL:</div>';
            echo '<div class="error-value">' . htmlspecialchars($errorInfo['url']) . '</div>';
            echo '</div>';
            
            echo '<div class="error-section">';
            echo '<div class="error-label">Método:</div>';
            echo '<div class="error-value">' . htmlspecialchars($errorInfo['method']) . '</div>';
            echo '</div>';
            
            if (!empty($errorInfo['post_data'])) {
                echo '<div class="error-section">';
                echo '<div class="error-label">Datos POST:</div>';
                echo '<div class="error-data">' . htmlspecialchars(json_encode($errorInfo['post_data'], JSON_PRETTY_PRINT)) . '</div>';
                echo '</div>';
            }
            
            if (!empty($errorInfo['get_data'])) {
                echo '<div class="error-section">';
                echo '<div class="error-label">Datos GET:</div>';
                echo '<div class="error-data">' . htmlspecialchars(json_encode($errorInfo['get_data'], JSON_PRETTY_PRINT)) . '</div>';
                echo '</div>';
            }
            
            if (isset($errorInfo['trace'])) {
                echo '<div class="error-section">';
                echo '<div class="error-label">Stack Trace:</div>';
                echo '<div class="error-trace">' . htmlspecialchars($errorInfo['trace']) . '</div>';
                echo '</div>';
            }
            
            echo '</div></body></html>';
            exit;
        }
    }

    /**
     * Registrar detalles del error
     */
    private function logErrorDetails($request, $response)
    {
        $errorDetails = [
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => (string) $request->getUri(),
            'method' => $request->getMethod(),
            'user_agent' => $request->getUserAgent(),
            'ip' => $request->getIPAddress(),
            'post_data' => $request->getPost(),
            'get_data' => $request->getGet(),
            'headers' => $request->headers(),
            'response_status' => $response->getStatusCode()
        ];

        log_message('error', 'Error 500 detectado - Detalles: ' . json_encode($errorDetails));
    }
}
