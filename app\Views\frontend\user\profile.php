<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: bold;
        margin: 0 auto 1rem;
        position: relative;
        cursor: pointer;
    }

    .profile-avatar:hover {
        transform: scale(1.05);
        transition: transform 0.3s ease;
    }

    .avatar-upload {
        position: absolute;
        bottom: 0;
        right: 0;
        background: var(--primary-color);
        border: 3px solid white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .form-floating label {
        color: var(--text-muted);
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-user-edit me-2"></i>Mi Perfil</h1>
                <p class="mb-0 mt-2 opacity-75">Actualiza tu información personal</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
                    <li class="breadcrumb-item active">Mi Perfil</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= $user['first_name'] ?? 'Usuario' ?> <?= $user['last_name'] ?? '' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>" class="active"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Profile Form -->
            <div class="dashboard-card">
                <h2 class="mb-4">Información Personal</h2>
                
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('cuenta/perfil') ?>" method="POST" id="profileForm">
                    <?= csrf_field() ?>
                    
                    <!-- Avatar Section -->
                    <div class="text-center mb-4">
                        <div class="profile-avatar">
                            <?= strtoupper(substr($user['first_name'] ?? 'U', 0, 1)) ?>
                            <div class="avatar-upload">
                                <i class="fas fa-camera" style="font-size: 0.8rem;"></i>
                            </div>
                        </div>
                        <p class="text-muted">Haz clic para cambiar tu foto de perfil</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= esc($user['first_name'] ?? '') ?>" required>
                                <label for="first_name">Nombre *</label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= esc($user['last_name'] ?? '') ?>" required>
                                <label for="last_name">Apellido *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= esc($user['email'] ?? '') ?>" required readonly>
                                <label for="email">Correo Electrónico *</label>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Para cambiar tu email, contacta al soporte
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= esc($user['phone'] ?? '') ?>" required>
                                <label for="phone">Teléfono *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= esc($user['username'] ?? '') ?>" readonly>
                                <label for="username">Nombre de Usuario</label>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Tu nombre de usuario único
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-floating">
                                <select class="form-select" id="status" name="status" disabled>
                                    <option value="active" <?= ($user['status'] ?? '') === 'active' ? 'selected' : '' ?>>Activo</option>
                                    <option value="inactive" <?= ($user['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactivo</option>
                                </select>
                                <label for="status">Estado de la Cuenta</label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Fecha de Registro</label>
                        <div class="form-control-plaintext">
                            <i class="fas fa-calendar me-2"></i>
                            <?= date('d/m/Y H:i', strtotime($user['created_at'] ?? 'now')) ?>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('cuenta') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Volver al Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Cambios
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Information -->
            <div class="dashboard-card">
                <h4 class="mb-3">Información de la Cuenta</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="fas fa-id-card fa-2x text-primary"></i>
                            </div>
                            <div>
                                <strong>ID de Usuario</strong>
                                <p class="mb-0 text-muted"><?= $user['uuid'] ?? 'N/A' ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="fas fa-clock fa-2x text-info"></i>
                            </div>
                            <div>
                                <strong>Última Actualización</strong>
                                <p class="mb-0 text-muted">
                                    <?= isset($user['updated_at']) ? date('d/m/Y H:i', strtotime($user['updated_at'])) : 'Nunca' ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Form validation
document.getElementById('profileForm').addEventListener('submit', function(e) {
    const firstName = document.getElementById('first_name').value.trim();
    const lastName = document.getElementById('last_name').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!firstName || !lastName || !phone) {
        e.preventDefault();
        alert('Por favor, completa todos los campos obligatorios.');
        return;
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    if (!phoneRegex.test(phone)) {
        e.preventDefault();
        alert('Por favor, ingresa un número de teléfono válido.');
        return;
    }
});

// Avatar upload (placeholder)
document.querySelector('.profile-avatar').addEventListener('click', function() {
    alert('Funcionalidad de carga de avatar en desarrollo');
});
</script>
<?= $this->endSection() ?>
