<?php

/**
 * Script para probar la eliminación sincronizada de productos con Recurrente
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "=== PRUEBA DE ELIMINACIÓN SINCRONIZADA ===\n";
    echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
    echo "=========================================\n\n";

    // Función para eliminar producto de Recurrente
    function deleteFromRecurrente($db, $recurrenteProductId) {
        // Obtener configuración de Recurrente
        $stmt = $db->prepare("
            SELECT setting_key, setting_value 
            FROM system_settings 
            WHERE setting_key IN ('recurrente_public_key', 'recurrente_secret_key', 'recurrente_enabled')
            AND is_active = 1
        ");
        $stmt->execute();
        
        $config = [];
        while ($row = $stmt->fetch()) {
            $config[$row['setting_key']] = $row['setting_value'];
        }

        if (empty($config['recurrente_public_key']) || empty($config['recurrente_secret_key'])) {
            echo "   ❌ Recurrente no está configurado correctamente\n";
            return false;
        }

        if ($config['recurrente_enabled'] !== '1') {
            echo "   ⚠️  Recurrente está deshabilitado\n";
            return false;
        }

        // Eliminar de Recurrente
        $url = "https://app.recurrente.com/api/products/{$recurrenteProductId}";
        
        $headers = [
            'X-PUBLIC-KEY: ' . $config['recurrente_public_key'],
            'X-SECRET-KEY: ' . $config['recurrente_secret_key'],
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'DELETE',
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "   ❌ Error de conexión: {$error}\n";
            return false;
        }
        
        if ($httpCode === 404) {
            echo "   ℹ️  Producto ya no existe en Recurrente (404)\n";
            return true; // Consideramos esto como éxito
        }
        
        if ($httpCode >= 400) {
            echo "   ❌ Error HTTP {$httpCode}: {$response}\n";
            return false;
        }
        
        echo "   ✅ Producto eliminado de Recurrente exitosamente\n";
        return true;
    }

    // Buscar productos de prueba que se pueden eliminar
    echo "🔍 Buscando productos de prueba para eliminar...\n";
    
    $stmt = $db->prepare("
        SELECT id, name, sku, recurrente_product_id, recurrente_sync_status
        FROM products 
        WHERE (name LIKE '%prueba%' OR name LIKE '%test%' OR sku LIKE '%TEST%')
        AND recurrente_product_id IS NOT NULL
        AND is_active = 1
        AND deleted_at IS NULL
        ORDER BY created_at DESC
        LIMIT 3
    ");
    $stmt->execute();
    $testProducts = $stmt->fetchAll();

    if (empty($testProducts)) {
        echo "❌ No hay productos de prueba sincronizados para eliminar\n";
        echo "Creando un producto de prueba primero...\n\n";
        
        // Crear producto de prueba
        $testProductData = [
            'uuid' => sprintf(
                '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            ),
            'sku' => 'TEST-DELETE-' . time(),
            'name' => 'Producto de Prueba para Eliminación',
            'slug' => 'producto-prueba-eliminacion-' . time(),
            'description' => 'Este producto será eliminado para probar la sincronización.',
            'short_description' => 'Producto de prueba para eliminación',
            'category_id' => 1,
            'price_regular' => 100.00,
            'price_sale' => null,
            'currency' => 'GTQ',
            'stock_quantity' => 1,
            'featured_image' => 'assets/img/products/test-delete.jpg',
            'is_active' => 1,
            'recurrente_sync_status' => 'pending'
        ];
        
        $sql = "
            INSERT INTO products (
                uuid, sku, name, slug, description, short_description, category_id, 
                price_regular, price_sale, currency, stock_quantity, 
                featured_image, is_active, recurrente_sync_status,
                created_at, updated_at
            ) VALUES (
                :uuid, :sku, :name, :slug, :description, :short_description, :category_id,
                :price_regular, :price_sale, :currency, :stock_quantity,
                :featured_image, :is_active, :recurrente_sync_status,
                NOW(), NOW()
            )
        ";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($testProductData);
        $newProductId = $db->lastInsertId();
        
        echo "✅ Producto de prueba creado con ID: {$newProductId}\n";
        echo "⚠️  Nota: Este producto necesita ser sincronizado con Recurrente primero\n";
        echo "Ejecute: php sync_products_recurrente.php --limit=1\n\n";
        
        exit(0);
    }

    echo "📦 Productos de prueba encontrados:\n";
    foreach ($testProducts as $product) {
        echo "   ID: {$product['id']} | SKU: {$product['sku']} | Nombre: {$product['name']}\n";
        echo "   Recurrente ID: {$product['recurrente_product_id']} | Estado: {$product['recurrente_sync_status']}\n\n";
    }

    // Probar eliminación del primer producto
    $productToDelete = $testProducts[0];
    
    echo "🗑️  Probando eliminación del producto: {$productToDelete['name']}\n";
    echo "   ID Local: {$productToDelete['id']}\n";
    echo "   ID Recurrente: {$productToDelete['recurrente_product_id']}\n\n";

    // Paso 1: Eliminar de Recurrente
    echo "1️⃣ Eliminando de Recurrente...\n";
    $recurrenteDeleted = deleteFromRecurrente($db, $productToDelete['recurrente_product_id']);
    
    if ($recurrenteDeleted) {
        echo "   ✅ Eliminado de Recurrente exitosamente\n\n";
        
        // Paso 2: Actualizar estado en base de datos local
        echo "2️⃣ Actualizando estado en base de datos local...\n";
        
        $stmt = $db->prepare("
            UPDATE products
            SET recurrente_product_id = NULL,
                recurrente_sync_status = 'disabled',
                recurrente_synced_at = NOW(),
                recurrente_storefront_link = NULL
            WHERE id = ?
        ");
        $stmt->execute([$productToDelete['id']]);
        
        echo "   ✅ Estado actualizado en base de datos local\n\n";
        
        // Paso 3: Soft delete del producto local
        echo "3️⃣ Realizando soft delete del producto local...\n";
        
        $stmt = $db->prepare("
            UPDATE products 
            SET deleted_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$productToDelete['id']]);
        
        echo "   ✅ Producto marcado como eliminado localmente\n\n";
        
        // Verificar estado final
        echo "4️⃣ Verificando estado final...\n";
        
        $stmt = $db->prepare("
            SELECT id, name, recurrente_product_id, recurrente_sync_status, 
                   deleted_at, recurrente_synced_at
            FROM products 
            WHERE id = ?
        ");
        $stmt->execute([$productToDelete['id']]);
        $finalState = $stmt->fetch();
        
        echo "   📊 Estado final del producto:\n";
        echo "      ID: {$finalState['id']}\n";
        echo "      Nombre: {$finalState['name']}\n";
        echo "      Recurrente ID: " . ($finalState['recurrente_product_id'] ?? 'NULL') . "\n";
        echo "      Estado Sync: {$finalState['recurrente_sync_status']}\n";
        echo "      Eliminado: " . ($finalState['deleted_at'] ?? 'NO') . "\n";
        echo "      Última Sync: " . ($finalState['recurrente_synced_at'] ?? 'NULL') . "\n";
        
        echo "\n✅ Eliminación sincronizada completada exitosamente!\n";
        
    } else {
        echo "   ❌ Error eliminando de Recurrente\n";
        echo "   ⚠️  No se procederá con la eliminación local\n";
    }

    echo "\n📊 RESUMEN:\n";
    echo "===========\n";
    echo "La eliminación sincronizada funciona en 3 pasos:\n";
    echo "1. Eliminar producto de Recurrente via API\n";
    echo "2. Limpiar campos de Recurrente en BD local\n";
    echo "3. Soft delete del producto local\n\n";
    
    echo "🔧 IMPLEMENTACIÓN AUTOMÁTICA:\n";
    echo "=============================\n";
    echo "✅ Callback beforeDelete en ProductModel\n";
    echo "✅ Método deleteFromRecurrente en RecurrenteService\n";
    echo "✅ Manejo de errores robusto\n";
    echo "✅ Logging detallado\n\n";

    echo "🎉 Prueba completada.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
