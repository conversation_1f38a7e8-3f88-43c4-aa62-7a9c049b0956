<?php
/**
 * Wrapper mejorado para operaciones de productos
 * Archivo: app/Libraries/ProductOperationWrapper.php
 */

namespace App\Libraries;

use App\Libraries\ProductErrorLogger;

class ProductOperationWrapper
{
    private $db;
    private $logger;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->logger = new ProductErrorLogger();
    }
    
    /**
     * Actualizar producto con manejo de errores mejorado
     */
    public function updateProduct($productId, $data)
    {
        try {
            // Validar datos básicos
            if (!$productId || !is_numeric($productId)) {
                throw new \Exception("ID de producto inválido: $productId");
            }
            
            // Verificar que el producto existe
            $existingProduct = $this->db->table("products")->where("id", $productId)->get()->getRowArray();
            if (!$existingProduct) {
                throw new \Exception("Producto no encontrado: $productId");
            }
            
            // Log del intento
            $this->logger->logProductError($productId, "update_attempt", "Iniciando actualización", [
                "data_keys" => array_keys($data),
                "existing_sku" => $existingProduct["sku"] ?? null
            ]);
            
            // Preparar parámetros para SP
            $params = [
                $productId,
                $data["name"] ?? $existingProduct["name"],
                $data["sku"] ?? $existingProduct["sku"],
                $data["description"] ?? $existingProduct["description"],
                $data["short_description"] ?? $existingProduct["short_description"],
                $data["category_id"] ?? $existingProduct["category_id"],
                $data["brand_id"] ?? $existingProduct["brand_id"],
                $data["price_regular"] ?? $existingProduct["price_regular"],
                $data["price_sale"] ?? $existingProduct["price_sale"],
                $data["currency"] ?? $existingProduct["currency"] ?? "GTQ",
                $data["stock_quantity"] ?? $existingProduct["stock_quantity"],
                $data["stock_min"] ?? $existingProduct["stock_min"],
                $data["weight"] ?? $existingProduct["weight"],
                $data["dimensions"] ?? $existingProduct["dimensions"],
                $data["dimension_length"] ?? $existingProduct["dimension_length"],
                $data["dimension_width"] ?? $existingProduct["dimension_width"],
                $data["dimension_height"] ?? $existingProduct["dimension_height"],
                $data["dimension_unit"] ?? $existingProduct["dimension_unit"] ?? "cm",
                $data["featured_image"] ?? $existingProduct["featured_image"],
                $data["gallery_images"] ?? $existingProduct["gallery_images"],
                $data["is_active"] ?? $existingProduct["is_active"],
                $data["is_featured"] ?? $existingProduct["is_featured"]
            ];
            
            // Ejecutar SP con timeout
            $this->db->query("SET SESSION wait_timeout = 300");
            $this->db->query("SET SESSION interactive_timeout = 300");
            
            $sql = "CALL sp_admin_update_product(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @p_result)";
            $result = $this->db->query($sql, $params);
            
            if (!$result) {
                $error = $this->db->error();
                throw new \Exception("Error ejecutando SP: " . $error["message"]);
            }
            
            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @p_result as result");
            $spResult = $resultQuery->getRowArray();
            
            if (!$spResult) {
                throw new \Exception("No se pudo obtener resultado del SP");
            }
            
            $resultValue = $spResult["result"];
            
            // Verificar resultado
            if (strpos($resultValue, "SUCCESS") !== 0) {
                throw new \Exception("SP retornó error: $resultValue");
            }
            
            // Actualizar campos adicionales si es necesario
            if (isset($data["has_expiration"]) || isset($data["expiration_date"]) || isset($data["expiration_alert_days"])) {
                $additionalData = [];
                
                if (isset($data["has_expiration"])) {
                    $additionalData["has_expiration"] = $data["has_expiration"];
                }
                if (isset($data["expiration_date"])) {
                    $additionalData["expiration_date"] = $data["expiration_date"];
                }
                if (isset($data["expiration_alert_days"])) {
                    $additionalData["expiration_alert_days"] = $data["expiration_alert_days"];
                }
                
                if (!empty($additionalData)) {
                    $additionalData["updated_at"] = date("Y-m-d H:i:s");
                    $updateResult = $this->db->table("products")->where("id", $productId)->update($additionalData);
                    
                    if (!$updateResult) {
                        $this->logger->logProductError($productId, "update_additional", "Error actualizando campos adicionales");
                    }
                }
            }
            
            // Log de éxito
            $this->logger->logProductError($productId, "update_success", "Producto actualizado exitosamente", [
                "sp_result" => $resultValue
            ]);
            
            return [
                "success" => true,
                "message" => "Producto actualizado exitosamente",
                "sp_result" => $resultValue
            ];
            
        } catch (\Exception $e) {
            // Log detallado del error
            $this->logger->logProductError($productId, "update_error", $e->getMessage(), [
                "file" => $e->getFile(),
                "line" => $e->getLine(),
                "trace" => $e->getTraceAsString(),
                "data" => $data
            ]);
            
            return [
                "success" => false,
                "message" => $e->getMessage(),
                "error_details" => [
                    "file" => $e->getFile(),
                    "line" => $e->getLine()
                ]
            ];
        }
    }
    
    /**
     * Crear producto con manejo de errores mejorado
     */
    public function createProduct($data)
    {
        try {
            // Similar al update pero para crear
            $this->logger->logProductError(0, "create_attempt", "Iniciando creación de producto", [
                "data_keys" => array_keys($data)
            ]);
            
            // Aquí iría la lógica de creación...
            
            return [
                "success" => true,
                "message" => "Producto creado exitosamente"
            ];
            
        } catch (\Exception $e) {
            $this->logger->logProductError(0, "create_error", $e->getMessage(), [
                "file" => $e->getFile(),
                "line" => $e->getLine(),
                "data" => $data
            ]);
            
            return [
                "success" => false,
                "message" => $e->getMessage()
            ];
        }
    }
}
?>