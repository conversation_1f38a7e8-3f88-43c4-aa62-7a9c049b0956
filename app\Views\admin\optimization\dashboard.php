<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .optimization-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .optimization-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .progress-ring {
            width: 60px;
            height: 60px;
        }
        .progress-ring circle {
            fill: transparent;
            stroke-width: 4;
            stroke-linecap: round;
        }
        .progress-ring .background {
            stroke: rgba(255,255,255,0.3);
        }
        .progress-ring .progress {
            stroke: #28a745;
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            transition: stroke-dashoffset 0.5s ease;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">🚀 Panel de Optimización</h1>
                        <p class="text-muted mb-0">Sistema completo de optimización para MrCell Guatemala</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="runFullOptimization()">
                            <i class="fas fa-magic"></i> Optimización Completa
                        </button>
                        <a href="<?= base_url('admin/automation') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Volver
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estadísticas Generales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center mb-2">
                            <svg class="progress-ring">
                                <circle class="background" cx="30" cy="30" r="25"></circle>
                                <circle class="progress" cx="30" cy="30" r="25" id="seo-progress"></circle>
                            </svg>
                        </div>
                        <h5 class="card-title">SEO Score</h5>
                        <p class="card-text" id="seo-score">85%</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center mb-2">
                            <svg class="progress-ring">
                                <circle class="background" cx="30" cy="30" r="25"></circle>
                                <circle class="progress" cx="30" cy="30" r="25" id="assets-progress"></circle>
                            </svg>
                        </div>
                        <h5 class="card-title">Assets</h5>
                        <p class="card-text" id="assets-optimized"><?= $optimization_stats['assets']['cache_files'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center mb-2">
                            <svg class="progress-ring">
                                <circle class="background" cx="30" cy="30" r="25"></circle>
                                <circle class="progress" cx="30" cy="30" r="25" id="performance-progress"></circle>
                            </svg>
                        </div>
                        <h5 class="card-title">Rendimiento</h5>
                        <p class="card-text" id="performance-score">92%</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center mb-2">
                            <svg class="progress-ring">
                                <circle class="background" cx="30" cy="30" r="25"></circle>
                                <circle class="progress" cx="30" cy="30" r="25" id="cache-progress"></circle>
                            </svg>
                        </div>
                        <h5 class="card-title">Cache</h5>
                        <p class="card-text" id="cache-size"><?= formatBytes($optimization_stats['assets']['cache_size'] ?? 0) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Módulos de Optimización -->
        <div class="row">
            <!-- SEO Optimization -->
            <div class="col-md-4 mb-4">
                <div class="card optimization-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary text-white rounded-circle p-2 me-3">
                                <i class="fas fa-search fa-lg"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0">Optimización SEO</h5>
                                <small class="text-muted">Sitemap, robots.txt, meta tags</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Sitemap:</span>
                                <span class="badge <?= $optimization_stats['seo']['sitemap_exists'] ? 'bg-success' : 'bg-warning' ?>">
                                    <?= $optimization_stats['seo']['sitemap_exists'] ? 'Generado' : 'Pendiente' ?>
                                </span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Robots.txt:</span>
                                <span class="badge <?= $optimization_stats['seo']['robots_exists'] ? 'bg-success' : 'bg-warning' ?>">
                                    <?= $optimization_stats['seo']['robots_exists'] ? 'Generado' : 'Pendiente' ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('admin/optimization/seo') ?>" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Configurar SEO
                            </a>
                            <button class="btn btn-outline-primary" onclick="generateSitemap()">
                                <i class="fas fa-sitemap"></i> Generar Sitemap
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asset Optimization -->
            <div class="col-md-4 mb-4">
                <div class="card optimization-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-success text-white rounded-circle p-2 me-3">
                                <i class="fas fa-compress-alt fa-lg"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0">Optimización de Assets</h5>
                                <small class="text-muted">CSS, JS, imágenes</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>CSS optimizados:</span>
                                <span class="badge bg-info"><?= $optimization_stats['assets']['css_files'] ?? 0 ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>JS optimizados:</span>
                                <span class="badge bg-info"><?= $optimization_stats['assets']['js_files'] ?? 0 ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Imágenes:</span>
                                <span class="badge bg-info"><?= $optimization_stats['assets']['image_files'] ?? 0 ?></span>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('admin/optimization/assets') ?>" class="btn btn-success">
                                <i class="fas fa-cog"></i> Gestionar Assets
                            </a>
                            <button class="btn btn-outline-success" onclick="clearAssetCache()">
                                <i class="fas fa-trash"></i> Limpiar Cache
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Optimization -->
            <div class="col-md-4 mb-4">
                <div class="card optimization-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-warning text-white rounded-circle p-2 me-3">
                                <i class="fas fa-tachometer-alt fa-lg"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0">Optimización de Rendimiento</h5>
                                <small class="text-muted">Base de datos, cache, limpieza</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Memoria PHP:</span>
                                <span class="badge bg-secondary"><?= formatBytes($performance_config['memory_usage'] ?? 0) ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Tiempo ejecución:</span>
                                <span class="badge bg-secondary"><?= number_format(($performance_config['execution_time'] ?? 0) * 1000, 2) ?>ms</span>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('admin/optimization/performance') ?>" class="btn btn-warning">
                                <i class="fas fa-cog"></i> Ver Rendimiento
                            </a>
                            <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                <i class="fas fa-database"></i> Optimizar BD
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">🔧 Acciones Rápidas</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-primary w-100" onclick="generateSitemap()">
                                    <i class="fas fa-sitemap"></i> Generar Sitemap
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-success w-100" onclick="optimizeAssets()">
                                    <i class="fas fa-compress"></i> Optimizar Assets
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-warning w-100" onclick="optimizeDatabase()">
                                    <i class="fas fa-database"></i> Optimizar BD
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-danger w-100" onclick="clearAllCache()">
                                    <i class="fas fa-trash"></i> Limpiar Todo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log de Actividades -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">📋 Log de Optimizaciones</h5>
                    </div>
                    <div class="card-body">
                        <div id="optimization-log" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
                            <div class="text-success">[<?= date('Y-m-d H:i:s') ?>] Sistema de optimización iniciado</div>
                            <div class="text-info">[<?= date('Y-m-d H:i:s') ?>] Configuraciones cargadas correctamente</div>
                            <div class="text-warning">[<?= date('Y-m-d H:i:s') ?>] Esperando comandos de optimización...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funciones de optimización
        function runFullOptimization() {
            addLog('Iniciando optimización completa...', 'info');
            
            fetch('<?= base_url('admin/optimization/run-full-optimization') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('✅ Optimización completa exitosa', 'success');
                    updateStats();
                } else {
                    addLog('❌ Error en optimización: ' + data.error, 'error');
                }
            })
            .catch(error => {
                addLog('❌ Error de conexión: ' + error.message, 'error');
            });
        }

        function generateSitemap() {
            addLog('Generando sitemap...', 'info');
            
            fetch('<?= base_url('admin/optimization/generate-sitemap') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('✅ Sitemap generado: ' + data.size + ' bytes', 'success');
                } else {
                    addLog('❌ Error generando sitemap: ' + data.error, 'error');
                }
            });
        }

        function clearAssetCache() {
            addLog('Limpiando cache de assets...', 'info');
            
            fetch('<?= base_url('admin/optimization/clear-asset-cache') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('✅ Cache limpiado: ' + data.deleted_files + ' archivos', 'success');
                    updateStats();
                } else {
                    addLog('❌ Error limpiando cache: ' + data.error, 'error');
                }
            });
        }

        function optimizeAssets() {
            addLog('Optimizando assets...', 'info');
            // Implementar optimización de assets
        }

        function optimizeDatabase() {
            addLog('Optimizando base de datos...', 'info');
            // Implementar optimización de BD
        }

        function clearAllCache() {
            if (confirm('¿Estás seguro de limpiar todo el cache?')) {
                addLog('Limpiando todo el cache...', 'info');
                // Implementar limpieza completa
            }
        }

        function addLog(message, type = 'info') {
            const log = document.getElementById('optimization-log');
            const timestamp = new Date().toLocaleString();
            const colorClass = {
                'info': 'text-info',
                'success': 'text-success',
                'warning': 'text-warning',
                'error': 'text-danger'
            }[type] || 'text-light';
            
            const logEntry = document.createElement('div');
            logEntry.className = colorClass;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        function updateStats() {
            // Actualizar estadísticas en tiempo real
            location.reload();
        }

        // Inicializar progress rings
        function setProgress(elementId, percentage) {
            const circle = document.getElementById(elementId);
            const circumference = 2 * Math.PI * 25;
            const offset = circumference - (percentage / 100) * circumference;
            circle.style.strokeDashoffset = offset;
        }

        // Configurar progress rings
        document.addEventListener('DOMContentLoaded', function() {
            setProgress('seo-progress', 85);
            setProgress('assets-progress', 70);
            setProgress('performance-progress', 92);
            setProgress('cache-progress', 60);
        });
    </script>
</body>
</html>

<?php
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
?>
