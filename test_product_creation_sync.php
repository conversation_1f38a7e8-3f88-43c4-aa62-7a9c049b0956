<?php

/**
 * Script para probar la creación de productos y sincronización automática con Recurrente
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "=== PRUEBA DE CREACIÓN Y SINCRONIZACIÓN AUTOMÁTICA ===\n";
    echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
    echo "=====================================================\n\n";

    // Función para crear un producto de prueba
    function createTestProduct($db, $productData) {
        // Generar UUID
        $productData['uuid'] = sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );

        // Generar slug
        $productData['slug'] = strtolower(str_replace(' ', '-', preg_replace('/[^A-Za-z0-9 ]/', '', $productData['name']))) . '-' . time();

        $sql = "
            INSERT INTO products (
                uuid, sku, name, slug, description, short_description, category_id,
                price_regular, price_sale, currency, stock_quantity,
                featured_image, is_active, recurrente_sync_status,
                created_at, updated_at
            ) VALUES (
                :uuid, :sku, :name, :slug, :description, :short_description, :category_id,
                :price_regular, :price_sale, :currency, :stock_quantity,
                :featured_image, :is_active, :recurrente_sync_status,
                NOW(), NOW()
            )
        ";

        $stmt = $db->prepare($sql);
        $stmt->execute($productData);

        return $db->lastInsertId();
    }

    // Función para verificar sincronización
    function checkSyncStatus($db, $productId) {
        $stmt = $db->prepare("
            SELECT id, name, sku, recurrente_product_id, recurrente_sync_status, 
                   recurrente_synced_at, recurrente_storefront_link
            FROM products 
            WHERE id = ?
        ");
        $stmt->execute([$productId]);
        return $stmt->fetch();
    }

    // Función para sincronizar manualmente (simulando el callback)
    function syncProductManually($db, $productId) {
        // Obtener configuración de Recurrente
        $stmt = $db->prepare("
            SELECT setting_key, setting_value 
            FROM system_settings 
            WHERE setting_key IN ('recurrente_public_key', 'recurrente_secret_key', 'recurrente_enabled')
            AND is_active = 1
        ");
        $stmt->execute();
        
        $config = [];
        while ($row = $stmt->fetch()) {
            $config[$row['setting_key']] = $row['setting_value'];
        }

        if (empty($config['recurrente_public_key']) || empty($config['recurrente_secret_key'])) {
            echo "   ❌ Recurrente no está configurado correctamente\n";
            return false;
        }

        if ($config['recurrente_enabled'] !== '1') {
            echo "   ⚠️  Recurrente está deshabilitado\n";
            return false;
        }

        // Obtener datos del producto
        $stmt = $db->prepare("
            SELECT * FROM products WHERE id = ?
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();

        if (!$product) {
            echo "   ❌ Producto no encontrado\n";
            return false;
        }

        // Verificar límite de precio
        $price = !empty($product['price_sale']) && $product['price_sale'] > 0
            ? $product['price_sale']
            : $product['price_regular'];
        
        $maxPriceUSD = 15000;
        $priceInUSD = $price;
        
        if (($product['currency'] ?? 'GTQ') === 'GTQ') {
            $priceInUSD = $price / 7.8;
        }
        
        if ($priceInUSD > $maxPriceUSD) {
            echo "   ⚠️  Precio demasiado alto para Recurrente: $" . number_format($priceInUSD, 2) . " USD\n";
            
            // Marcar como disabled
            $stmt = $db->prepare("
                UPDATE products 
                SET recurrente_sync_status = 'disabled', recurrente_synced_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$productId]);
            
            return false;
        }

        // Preparar datos para Recurrente
        $imageUrl = null;
        if (!empty($product['featured_image'])) {
            $productionUrl = 'https://mrcell.com.gt';
            $featuredImage = $product['featured_image'];
            
            if (strpos($featuredImage, 'http') === 0) {
                $imageUrl = $featuredImage;
            } elseif (strpos($featuredImage, 'assets/') === 0) {
                $imageUrl = $productionUrl . '/' . $featuredImage;
            } else {
                $imageUrl = $productionUrl . '/assets/img/products/' . $featuredImage;
            }
        }

        $productData = [
            'product' => [
                'name' => $product['name'],
                'description' => $product['description'] ?: $product['short_description'] ?: '',
                'image_url' => $imageUrl,
                'prices_attributes' => [
                    [
                        'currency' => $product['currency'] ?: 'GTQ',
                        'charge_type' => 'one_time',
                        'amount_in_cents' => intval($price * 100)
                    ]
                ],
                'cancel_url' => 'https://mrcell.com.gt/checkout/cancel',
                'success_url' => 'https://mrcell.com.gt/checkout/success',
                'custom_terms_and_conditions' => 'Términos y condiciones de MrCell Guatemala.',
                'phone_requirement' => 'none',
                'address_requirement' => 'none',
                'billing_info_requirement' => 'none'
            ],
            'adjustable_quantity' => true,
            'inventory_quantity' => $product['stock_quantity'],
            'metadata' => [
                'local_product_id' => (string)$product['id'],
                'sku' => $product['sku'],
                'platform' => 'MrCell_CI4'
            ]
        ];

        // Enviar a Recurrente
        $url = 'https://app.recurrente.com/api/products/';
        
        $headers = [
            'X-PUBLIC-KEY: ' . $config['recurrente_public_key'],
            'X-SECRET-KEY: ' . $config['recurrente_secret_key'],
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($productData),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "   ❌ Error de conexión: {$error}\n";
            return false;
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = $decodedResponse['message'] ?? $decodedResponse['error'] ?? 'Error desconocido';
            echo "   ❌ Error HTTP {$httpCode}: {$errorMessage}\n";
            return false;
        }
        
        if (isset($decodedResponse['id'])) {
            // Actualizar producto local
            $stmt = $db->prepare("
                UPDATE products 
                SET recurrente_product_id = ?, 
                    recurrente_sync_status = 'synced', 
                    recurrente_synced_at = NOW(),
                    recurrente_storefront_link = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $decodedResponse['id'],
                $decodedResponse['storefront_link'] ?? null,
                $productId
            ]);
            
            echo "   ✅ Sincronizado exitosamente. ID Recurrente: {$decodedResponse['id']}\n";
            return true;
        }
        
        echo "   ❌ Respuesta inválida de Recurrente\n";
        return false;
    }

    // Casos de prueba
    $testProducts = [
        [
            'name' => 'Producto de Prueba Barato',
            'data' => [
                'sku' => 'TEST-CHEAP-' . time(),
                'name' => 'Producto de Prueba Barato',
                'description' => 'Este es un producto de prueba con precio bajo para verificar la sincronización automática.',
                'short_description' => 'Producto de prueba barato',
                'category_id' => 1,
                'price_regular' => 150.00,
                'price_sale' => null,
                'currency' => 'GTQ',
                'stock_quantity' => 10,
                'featured_image' => 'assets/img/products/test-product.jpg',
                'is_active' => 1,
                'recurrente_sync_status' => 'pending'
            ]
        ],
        [
            'name' => 'Producto de Prueba Caro',
            'data' => [
                'sku' => 'TEST-EXPENSIVE-' . time(),
                'name' => 'Producto de Prueba Caro',
                'description' => 'Este es un producto de prueba con precio alto que debería ser marcado como disabled.',
                'short_description' => 'Producto de prueba caro',
                'category_id' => 1,
                'price_regular' => 200000.00,
                'price_sale' => null,
                'currency' => 'GTQ',
                'stock_quantity' => 1,
                'featured_image' => 'assets/img/products/test-expensive.jpg',
                'is_active' => 1,
                'recurrente_sync_status' => 'pending'
            ]
        ]
    ];

    foreach ($testProducts as $testProduct) {
        echo "🧪 Probando: {$testProduct['name']}\n";
        echo "   Creando producto...\n";
        
        try {
            $productId = createTestProduct($db, $testProduct['data']);
            echo "   ✅ Producto creado con ID: {$productId}\n";
            
            echo "   Sincronizando con Recurrente...\n";
            $syncResult = syncProductManually($db, $productId);
            
            echo "   Verificando estado final...\n";
            $finalStatus = checkSyncStatus($db, $productId);
            
            echo "   📊 Estado final:\n";
            echo "      ID: {$finalStatus['id']}\n";
            echo "      SKU: {$finalStatus['sku']}\n";
            echo "      Recurrente ID: " . ($finalStatus['recurrente_product_id'] ?? 'NULL') . "\n";
            echo "      Estado: {$finalStatus['recurrente_sync_status']}\n";
            echo "      Sincronizado: " . ($finalStatus['recurrente_synced_at'] ?? 'NULL') . "\n";
            
            if ($finalStatus['recurrente_storefront_link']) {
                echo "      Storefront: {$finalStatus['recurrente_storefront_link']}\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }

    echo "🎉 Pruebas completadas.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
