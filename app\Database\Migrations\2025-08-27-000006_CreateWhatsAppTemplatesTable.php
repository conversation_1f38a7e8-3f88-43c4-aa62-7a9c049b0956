<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tabla de templates de WhatsApp
 * Permite configurar mensajes desde el panel de administración
 */
class CreateWhatsAppTemplatesTable extends Migration
{
    public function up()
    {
        // Tabla de templates de WhatsApp
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Nombre del template',
            ],
            'key' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => 'Clave única del template',
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
                'comment' => 'Título descriptivo',
            ],
            'message' => [
                'type' => 'TEXT',
                'null' => false,
                'comment' => 'Contenido del mensaje con variables',
            ],
            'variables' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Variables disponibles en el template',
            ],
            'category' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'wishlist_reminder', 'newsletter', 'general'],
                'default' => 'general',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'usage_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Contador de uso',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('key', false, true); // Unique
        $this->forge->addKey('category');
        $this->forge->addKey('is_active');
        
        $this->forge->createTable('whatsapp_templates');
        
        // Insertar templates por defecto
        $this->insertDefaultTemplates();
    }
    
    /**
     * Insertar templates por defecto
     */
    private function insertDefaultTemplates()
    {
        $templates = [
            [
                'name' => 'Alerta de Precio',
                'key' => 'price_alert',
                'title' => '🔥 ¡Alerta de Precio!',
                'message' => "¡Hola {name}! 👋\n\nEl producto que tienes en tu lista de deseos ha bajado de precio:\n\n📱 *{product_name}*\n💰 Precio anterior: Q{old_price}\n🎉 Precio actual: Q{new_price}\n💸 Te ahorras: Q{savings} ({percentage}%)\n\n¡No te lo pierdas! 🏃‍♂️\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'product_name', 'old_price', 'new_price', 'savings', 'percentage', 'product_url']),
                'category' => 'price_alert'
            ],
            [
                'name' => 'Producto Disponible',
                'key' => 'back_in_stock',
                'title' => '📦 ¡Producto Disponible!',
                'message' => "¡Hola {name}! 👋\n\nEl producto que esperabas ya está disponible:\n\n📱 *{product_name}*\n💰 Precio: Q{price}\n📦 Stock: {stock_quantity} unidades\n\n¡Cómpralo antes de que se agote! 🏃‍♂️\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'product_name', 'price', 'stock_quantity', 'product_url']),
                'category' => 'stock_alert'
            ],
            [
                'name' => 'Recordatorio de Lista',
                'key' => 'wishlist_reminder',
                'title' => '❤️ Recordatorio de Lista de Deseos',
                'message' => "¡Hola {name}! 👋\n\nTienes {count} productos en tu lista de deseos esperándote:\n\n{product_list}\n\n¿Qué tal si les echas un vistazo? 😊\n{wishlist_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'count', 'product_list', 'wishlist_url']),
                'category' => 'wishlist_reminder'
            ],
            [
                'name' => 'Producto Similar',
                'key' => 'new_similar_product',
                'title' => '🆕 ¡Producto Similar Disponible!',
                'message' => "¡Hola {name}! 👋\n\nTenemos un nuevo producto que podría interesarte:\n\n📱 *{product_name}*\n💰 Precio: Q{price}\n⭐ Rating: {rating}/5\n\nEs similar a: {similar_to}\n\n¡Échale un vistazo! 👀\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'product_name', 'price', 'rating', 'similar_to', 'product_url']),
                'category' => 'newsletter'
            ],
            [
                'name' => 'Bienvenida',
                'key' => 'welcome',
                'title' => '🎉 ¡Bienvenido a MrCell!',
                'message' => "¡Hola {name}! 👋\n\n¡Bienvenido a MrCell Guatemala! 🇬🇹\n\nGracias por registrarte en nuestra tienda de tecnología. Aquí encontrarás:\n\n📱 Los mejores smartphones\n💻 Laptops y computadoras\n🎧 Accesorios tecnológicos\n⚡ Precios increíbles\n\n¡Explora nuestro catálogo!\n{store_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'store_url']),
                'category' => 'general'
            ],
            [
                'name' => 'Confirmación de Pedido',
                'key' => 'order_confirmation',
                'title' => '✅ Pedido Confirmado',
                'message' => "¡Hola {name}! 👋\n\n¡Tu pedido ha sido confirmado! 🎉\n\n📋 *Pedido #{order_number}*\n💰 Total: Q{total}\n📦 Productos: {product_count}\n🚚 Entrega estimada: {delivery_date}\n\nPuedes seguir tu pedido aquí:\n{tracking_url}\n\n¡Gracias por confiar en nosotros! 😊\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'order_number', 'total', 'product_count', 'delivery_date', 'tracking_url']),
                'category' => 'general'
            ],
            [
                'name' => 'Pedido Enviado',
                'key' => 'order_shipped',
                'title' => '🚚 ¡Tu pedido está en camino!',
                'message' => "¡Hola {name}! 👋\n\n¡Excelentes noticias! Tu pedido ya está en camino 🚚\n\n📋 *Pedido #{order_number}*\n📦 Guía de envío: {tracking_number}\n🏠 Dirección: {delivery_address}\n⏰ Llegada estimada: {delivery_date}\n\nSigue tu envío:\n{tracking_url}\n\n¡Pronto tendrás tus productos! 📱✨\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'order_number', 'tracking_number', 'delivery_address', 'delivery_date', 'tracking_url']),
                'category' => 'general'
            ],
            [
                'name' => 'Oferta Especial',
                'key' => 'special_offer',
                'title' => '🔥 ¡Oferta Especial!',
                'message' => "¡Hola {name}! 👋\n\n¡Tenemos una oferta especial para ti! 🔥\n\n🎯 *{offer_title}*\n💸 Descuento: {discount}%\n⏰ Válida hasta: {expiry_date}\n\n{offer_description}\n\n¡No te la pierdas!\n{offer_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱",
                'variables' => json_encode(['name', 'offer_title', 'discount', 'expiry_date', 'offer_description', 'offer_url']),
                'category' => 'newsletter'
            ]
        ];
        
        foreach ($templates as $template) {
            $template['created_at'] = date('Y-m-d H:i:s');
            $template['updated_at'] = date('Y-m-d H:i:s');
            
            $this->db->table('whatsapp_templates')->insert($template);
        }
    }

    public function down()
    {
        $this->forge->dropTable('whatsapp_templates', true);
    }
}
