<?php

/**
 * Helper de Upload Optimizado
 * Integra automáticamente la optimización de imágenes
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

if (!function_exists('optimized_upload')) {
    /**
     * Upload de archivo con optimización automática de imágenes
     * 
     * @param mixed $file Archivo subido ($_FILES o CodeIgniter file)
     * @param string $destination Directorio de destino
     * @param array $options Opciones de upload y optimización
     * @return array Resultado del upload
     */
    function optimized_upload($file, string $destination = 'uploads', array $options = []): array
    {
        try {
            // Configuración por defecto
            $defaultOptions = [
                'allowed_types' => 'jpg,jpeg,png,gif,webp',
                'max_size' => 10240, // 10MB
                'optimize_images' => true,
                'create_thumbnail' => false,
                'create_webp' => true,
                'sizes' => [], // Tamaños adicionales
                'overwrite' => false,
                'encrypt_name' => true
            ];
            
            $options = array_merge($defaultOptions, $options);
            
            // Configurar CodeIgniter upload
            $uploadConfig = [
                'upload_path' => FCPATH . $destination,
                'allowed_types' => $options['allowed_types'],
                'max_size' => $options['max_size'],
                'overwrite' => $options['overwrite'],
                'encrypt_name' => $options['encrypt_name']
            ];
            
            // Crear directorio si no existe
            if (!is_dir($uploadConfig['upload_path'])) {
                mkdir($uploadConfig['upload_path'], 0755, true);
            }
            
            $upload = \Config\Services::upload($uploadConfig);
            
            // Realizar upload
            if (!$upload->do_upload('file')) {
                return [
                    'success' => false,
                    'error' => $upload->display_errors('', ''),
                    'error_code' => 'UPLOAD_FAILED'
                ];
            }
            
            $uploadData = $upload->data();
            $filePath = $uploadData['full_path'];
            
            $result = [
                'success' => true,
                'file_name' => $uploadData['file_name'],
                'file_path' => $filePath,
                'file_size' => $uploadData['file_size'],
                'file_type' => $uploadData['file_type'],
                'image_width' => $uploadData['image_width'] ?? null,
                'image_height' => $uploadData['image_height'] ?? null,
                'relative_path' => $destination . '/' . $uploadData['file_name']
            ];
            
            // Optimizar imagen si es necesario
            if ($options['optimize_images'] && $uploadData['is_image']) {
                $imageOptimizer = new \App\Libraries\ImageOptimizer();
                
                $optimizationOptions = [
                    'create_thumbnail' => $options['create_thumbnail'],
                    'sizes' => $options['sizes']
                ];
                
                $optimizationResult = $imageOptimizer->optimizeUploadedImage($filePath, $optimizationOptions);
                
                if ($optimizationResult['success']) {
                    $result['optimization'] = $optimizationResult;
                    $result['optimized'] = true;
                    
                    // Actualizar información del archivo optimizado
                    $result['file_size'] = $optimizationResult['new_size'];
                    $result['original_size'] = $optimizationResult['original_size'];
                    $result['savings'] = $optimizationResult['savings'];
                    $result['savings_percentage'] = $optimizationResult['percentage'];
                    
                    // Agregar rutas de versiones adicionales
                    if (isset($optimizationResult['webp_created'])) {
                        $result['webp_version'] = str_replace(FCPATH, '', $optimizationResult['webp_created']);
                    }
                    
                    if (isset($optimizationResult['thumbnail_created'])) {
                        $result['thumbnail'] = str_replace(FCPATH, '', $optimizationResult['thumbnail_created']);
                    }
                    
                    if (isset($optimizationResult['additional_sizes'])) {
                        $result['additional_sizes'] = [];
                        foreach ($optimizationResult['additional_sizes'] as $sizeName => $sizePath) {
                            $result['additional_sizes'][$sizeName] = str_replace(FCPATH, '', $sizePath);
                        }
                    }
                } else {
                    $result['optimization_error'] = $optimizationResult['error'];
                    $result['optimized'] = false;
                }
            }
            
            return $result;
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'EXCEPTION'
            ];
        }
    }
}

if (!function_exists('upload_product_image')) {
    /**
     * Upload específico para imágenes de productos
     * 
     * @param mixed $file Archivo subido
     * @param array $options Opciones específicas
     * @return array Resultado del upload
     */
    function upload_product_image($file, array $options = []): array
    {
        $productOptions = [
            'allowed_types' => 'jpg,jpeg,png,webp',
            'max_size' => 5120, // 5MB
            'optimize_images' => true,
            'create_thumbnail' => true,
            'create_webp' => true,
            'sizes' => [
                'medium' => [800, 600],
                'small' => [400, 300]
            ]
        ];
        
        $options = array_merge($productOptions, $options);
        
        return optimized_upload($file, 'uploads/products', $options);
    }
}

if (!function_exists('upload_user_avatar')) {
    /**
     * Upload específico para avatares de usuario
     * 
     * @param mixed $file Archivo subido
     * @param array $options Opciones específicas
     * @return array Resultado del upload
     */
    function upload_user_avatar($file, array $options = []): array
    {
        $avatarOptions = [
            'allowed_types' => 'jpg,jpeg,png',
            'max_size' => 2048, // 2MB
            'optimize_images' => true,
            'create_thumbnail' => true,
            'create_webp' => true,
            'sizes' => [
                'large' => [200, 200],
                'medium' => [100, 100],
                'small' => [50, 50]
            ]
        ];
        
        $options = array_merge($avatarOptions, $options);
        
        return optimized_upload($file, 'uploads/avatars', $options);
    }
}

if (!function_exists('get_optimized_image_url')) {
    /**
     * Obtener URL de imagen optimizada
     * Devuelve WebP si está disponible y el navegador lo soporta
     * 
     * @param string $imagePath Ruta de la imagen
     * @param string $size Tamaño específico (opcional)
     * @return string URL de la imagen
     */
    function get_optimized_image_url(string $imagePath, string $size = null): string
    {
        if (empty($imagePath)) {
            return base_url('assets/images/no-image.jpg');
        }
        
        // Si se especifica un tamaño, buscar esa versión
        if ($size) {
            $pathInfo = pathinfo($imagePath);
            $sizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$size}." . $pathInfo['extension'];
            
            if (file_exists(FCPATH . $sizedPath)) {
                $imagePath = $sizedPath;
            }
        }
        
        // Verificar si existe versión WebP y si el navegador la soporta
        $webpPath = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $imagePath);
        
        if (file_exists(FCPATH . $webpPath) && supports_webp()) {
            return base_url($webpPath);
        }
        
        return base_url($imagePath);
    }
}

if (!function_exists('supports_webp')) {
    /**
     * Verificar si el navegador soporta WebP
     * 
     * @return bool
     */
    function supports_webp(): bool
    {
        $request = \Config\Services::request();
        $accept = $request->getHeaderLine('Accept');
        
        return strpos($accept, 'image/webp') !== false;
    }
}

if (!function_exists('generate_responsive_image_html')) {
    /**
     * Generar HTML para imagen responsiva con múltiples tamaños
     * 
     * @param string $imagePath Ruta base de la imagen
     * @param string $alt Texto alternativo
     * @param array $sizes Tamaños disponibles
     * @param array $attributes Atributos adicionales
     * @return string HTML de la imagen
     */
    function generate_responsive_image_html(string $imagePath, string $alt = '', array $sizes = [], array $attributes = []): string
    {
        if (empty($imagePath)) {
            $imagePath = 'assets/images/no-image.jpg';
        }
        
        $pathInfo = pathinfo($imagePath);
        $baseUrl = base_url();
        
        // Generar srcset si hay tamaños disponibles
        $srcset = [];
        $webpSrcset = [];
        
        foreach ($sizes as $sizeName => $dimensions) {
            $sizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$sizeName}." . $pathInfo['extension'];
            $webpSizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$sizeName}.webp";
            
            if (file_exists(FCPATH . $sizedPath)) {
                $width = is_array($dimensions) ? $dimensions[0] : $dimensions;
                $srcset[] = $baseUrl . $sizedPath . " {$width}w";
                
                if (file_exists(FCPATH . $webpSizedPath)) {
                    $webpSrcset[] = $baseUrl . $webpSizedPath . " {$width}w";
                }
            }
        }
        
        // Atributos por defecto
        $defaultAttributes = [
            'loading' => 'lazy',
            'class' => 'img-fluid'
        ];
        
        $attributes = array_merge($defaultAttributes, $attributes);
        
        // Construir atributos HTML
        $attributesHtml = '';
        foreach ($attributes as $key => $value) {
            $attributesHtml .= " {$key}=\"{$value}\"";
        }
        
        // Generar HTML
        $html = '';
        
        if (!empty($webpSrcset) || !empty($srcset)) {
            $html .= '<picture>';
            
            // Source WebP
            if (!empty($webpSrcset)) {
                $webpSrcsetStr = implode(', ', $webpSrcset);
                $html .= "<source srcset=\"{$webpSrcsetStr}\" type=\"image/webp\">";
            }
            
            // Source original
            if (!empty($srcset)) {
                $srcsetStr = implode(', ', $srcset);
                $html .= "<source srcset=\"{$srcsetStr}\">";
            }
            
            // Imagen fallback
            $html .= "<img src=\"{$baseUrl}{$imagePath}\" alt=\"{$alt}\"{$attributesHtml}>";
            $html .= '</picture>';
        } else {
            // Imagen simple
            $html = "<img src=\"{$baseUrl}{$imagePath}\" alt=\"{$alt}\"{$attributesHtml}>";
        }
        
        return $html;
    }
}

if (!function_exists('cleanup_old_uploads')) {
    /**
     * Limpiar uploads antiguos
     * 
     * @param int $days Días de antigüedad
     * @param string $directory Directorio a limpiar
     * @return array Resultado de la limpieza
     */
    function cleanup_old_uploads(int $days = 30, string $directory = 'uploads/temp'): array
    {
        $uploadPath = FCPATH . $directory;
        
        if (!is_dir($uploadPath)) {
            return [
                'success' => false,
                'error' => 'Directorio no encontrado'
            ];
        }
        
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $files = glob($uploadPath . '/*');
        $deleted = [];
        $errors = [];
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deleted[] = basename($file);
                } else {
                    $errors[] = basename($file);
                }
            }
        }
        
        return [
            'success' => true,
            'deleted' => $deleted,
            'deleted_count' => count($deleted),
            'errors' => $errors,
            'error_count' => count($errors)
        ];
    }
}

if (!function_exists('get_upload_stats')) {
    /**
     * Obtener estadísticas de uploads
     * 
     * @param string $directory Directorio a analizar
     * @return array Estadísticas
     */
    function get_upload_stats(string $directory = 'uploads'): array
    {
        $uploadPath = FCPATH . $directory;
        
        if (!is_dir($uploadPath)) {
            return [
                'error' => 'Directorio no encontrado'
            ];
        }
        
        $files = glob($uploadPath . '/**/*', GLOB_BRACE);
        $totalSize = 0;
        $fileTypes = [];
        $totalFiles = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalFiles++;
                $size = filesize($file);
                $totalSize += $size;
                
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (!isset($fileTypes[$extension])) {
                    $fileTypes[$extension] = ['count' => 0, 'size' => 0];
                }
                $fileTypes[$extension]['count']++;
                $fileTypes[$extension]['size'] += $size;
            }
        }
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_size_formatted' => format_bytes($totalSize),
            'file_types' => $fileTypes,
            'directory' => $directory
        ];
    }
}

if (!function_exists('format_bytes')) {
    /**
     * Formatear bytes en formato legible
     * 
     * @param int $bytes Bytes a formatear
     * @return string Formato legible
     */
    function format_bytes(int $bytes): string
    {
        if ($bytes >= 1073741824) {
            return round($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
