<?php

namespace App\Controllers\Admin;

use CodeIgniter\Controller;

class SetupController extends Controller
{
    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    public function index()
    {
        echo "<h1>Configuración de Administradores - MrCell</h1>";
        echo "<p>Este script configurará automáticamente la tabla de administradores y los stored procedures necesarios.</p>";
        
        echo '<div style="margin: 20px 0;">';
        echo '<a href="/admin/setup/run" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;">🚀 Ejecutar Configuración</a>';
        echo '</div>';
        
        echo '<div style="margin: 20px 0;">';
        echo '<a href="/admin/debug/check-auth" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔍 Verificar Estado</a>';
        echo '</div>';
    }

    public function run()
    {
        echo "<h1>Ejecutando Configuración...</h1>";
        
        try {
            // 1. Crear tabla administradores si no existe
            echo "<h3>1. Configurando tabla administradores</h3>";
            $this->createAdministradoresTable();
            
            // 2. Crear stored procedures
            echo "<h3>2. Configurando stored procedures</h3>";
            $this->createStoredProcedures();
            
            // 3. Insertar administradores
            echo "<h3>3. Creando administradores</h3>";
            $this->insertAdministradores();
            
            echo "<h3>✅ Configuración completada exitosamente</h3>";
            echo '<p><a href="/admin/login" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Ir al Login</a></p>';
            
        } catch (\Exception $e) {
            echo "<h3>❌ Error en la configuración</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
        }
    }

    private function createAdministradoresTable()
    {
        // Verificar si la tabla existe
        $tableExists = $this->db->query("SHOW TABLES LIKE 'administradores'")->getResult();
        
        if (count($tableExists) == 0) {
            echo "Creando tabla administradores...<br>";
            
            $sql = "
                CREATE TABLE administradores (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    uuid VARCHAR(36) UNIQUE,
                    nombre VARCHAR(100),
                    email VARCHAR(100) UNIQUE,
                    password VARCHAR(255),
                    rol_admin ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
                    is_active BOOLEAN DEFAULT TRUE,
                    ultimo_login DATETIME NULL,
                    intentos_login INT DEFAULT 0,
                    bloqueado_hasta DATETIME NULL,
                    session_timeout INT DEFAULT 3600,
                    created_at DATETIME NULL,
                    updated_at DATETIME NULL,
                    deleted_at DATETIME NULL
                )
            ";
            
            $this->db->query($sql);
            echo "✅ Tabla administradores creada<br>";
        } else {
            echo "✅ Tabla administradores ya existe<br>";
        }
    }

    private function createStoredProcedures()
    {
        echo "Verificando stored procedures...<br>";

        // Verificar si ya existen
        $procedures = $this->db->query("SHOW PROCEDURE STATUS WHERE Name = 'sp_admin_authenticate'")->getResult();

        if (count($procedures) > 0) {
            echo "✅ Stored procedure sp_admin_authenticate ya existe<br>";
        } else {
            echo "❌ Stored procedure sp_admin_authenticate NO existe<br>";
            echo "Por favor ejecuta el archivo database/stored_procedures.sql<br>";
        }

        $procedures2 = $this->db->query("SHOW PROCEDURE STATUS WHERE Name = 'sp_admin_login_success'")->getResult();

        if (count($procedures2) > 0) {
            echo "✅ Stored procedure sp_admin_login_success ya existe<br>";
        } else {
            echo "❌ Stored procedure sp_admin_login_success NO existe<br>";
            echo "Por favor ejecuta el archivo database/stored_procedures.sql<br>";
        }
    }

    private function insertAdministradores()
    {
        $admins = [
            [
                'uuid' => $this->generateUUID(),
                'nombre' => 'Engy Calderon',
                'email' => '<EMAIL>',
                'password' => password_hash('Clairo!23', PASSWORD_DEFAULT),
                'rol_admin' => 'super_admin'
            ],
            [
                'uuid' => $this->generateUUID(),
                'nombre' => 'Administrador MrCell',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'rol_admin' => 'admin'
            ]
        ];

        foreach ($admins as $admin) {
            // Verificar si ya existe
            $existing = $this->db->query("SELECT id FROM administradores WHERE email = ?", [$admin['email']])->getRow();

            if (!$existing) {
                $admin['created_at'] = date('Y-m-d H:i:s');
                $admin['updated_at'] = date('Y-m-d H:i:s');

                $this->db->table('administradores')->insert($admin);
                echo "✅ Creado administrador: {$admin['email']}<br>";
            } else {
                // Actualizar contraseña
                $this->db->query("UPDATE administradores SET password = ?, updated_at = NOW() WHERE email = ?", [
                    $admin['password'], $admin['email']
                ]);
                echo "✅ Actualizado administrador: {$admin['email']}<br>";
            }
        }
    }

    private function generateUUID()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
