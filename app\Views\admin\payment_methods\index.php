<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1><i class="fas fa-credit-card me-2"></i>Gestión de Métodos de Pago</h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Métodos de Pago Disponibles</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMethodModal">
                    <i class="fas fa-plus me-2"></i>Nuevo Método
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($paymentMethods)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay métodos de pago configurados</h5>
                        <p class="text-muted">Agrega tu primer método de pago para comenzar</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Método</th>
                                    <th>Tipo</th>
                                    <th>Estado</th>
                                    <th>Cuentas/Puntos</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($paymentMethods as $method): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="<?= esc($method['icon'] ?? 'fas fa-credit-card') ?> fa-lg me-3 text-primary"></i>
                                                <div>
                                                    <h6 class="mb-0"><?= esc($method['name']) ?></h6>
                                                    <small class="text-muted"><?= esc($method['description']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $typeLabels = [
                                                'bank_transfer' => '<span class="badge bg-info">Transferencia</span>',
                                                'cash' => '<span class="badge bg-success">Efectivo</span>',
                                                'credit_card' => '<span class="badge bg-warning">Tarjeta</span>',
                                                'digital_wallet' => '<span class="badge bg-purple">Billetera Digital</span>',
                                                'other' => '<span class="badge bg-secondary">Otro</span>'
                                            ];
                                            echo $typeLabels[$method['type']] ?? '<span class="badge bg-secondary">Desconocido</span>';
                                            ?>
                                        </td>
                                        <td>
                                            <?php if ($method['is_active']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Activo
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Inactivo
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <?php if ($method['type'] === 'bank_transfer'): ?>
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-university me-1"></i>
                                                        <?= $method['bank_accounts_count'] ?> Cuentas
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <?php if ($method['type'] === 'cash'): ?>
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?= $method['pickup_locations_count'] ?> Puntos
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/admin/payment-methods/view/<?= $method['id'] ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Ver detalles">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <a href="/admin/payment-methods/toggle-status/<?= $method['id'] ?>" 
                                                   class="btn btn-sm btn-outline-<?= $method['is_active'] ? 'danger' : 'success' ?>"
                                                   title="<?= $method['is_active'] ? 'Desactivar' : 'Activar' ?>"
                                                   onclick="return confirm('¿Estás seguro de cambiar el estado de este método de pago?')">
                                                    <i class="fas fa-<?= $method['is_active'] ? 'times' : 'check' ?>"></i>
                                                </a>
                                                
                                                <?php if ($method['type'] === 'bank_transfer'): ?>
                                                    <a href="/admin/payment-methods/add-bank-account/<?= $method['id'] ?>" 
                                                       class="btn btn-sm btn-outline-info" title="Agregar cuenta bancaria">
                                                        <i class="fas fa-plus"></i> Cuenta
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <?php if ($method['type'] === 'cash'): ?>
                                                    <a href="/admin/payment-methods/add-pickup-location/<?= $method['id'] ?>" 
                                                       class="btn btn-sm btn-outline-success" title="Agregar punto de recogida">
                                                        <i class="fas fa-plus"></i> Punto
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal para agregar nuevo método de pago -->
<div class="modal fade" id="addMethodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Nuevo Método de Pago
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/admin/payment-methods/store">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="methodName" class="form-label">Nombre del Método</label>
                        <input type="text" class="form-control" id="methodName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="methodType" class="form-label">Tipo</label>
                        <select class="form-select" id="methodType" name="type" required>
                            <option value="">Seleccionar tipo...</option>
                            <option value="bank_transfer">Transferencia Bancaria</option>
                            <option value="cash">Pago en Efectivo</option>
                            <option value="credit_card">Tarjeta de Crédito/Débito</option>
                            <option value="digital_wallet">Billetera Digital</option>
                            <option value="other">Otro</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="methodDescription" class="form-label">Descripción</label>
                        <textarea class="form-control" id="methodDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="methodIcon" class="form-label">Icono (FontAwesome)</label>
                        <input type="text" class="form-control" id="methodIcon" name="icon" 
                               placeholder="fas fa-credit-card" value="fas fa-credit-card">
                    </div>
                    
                    <div class="mb-3">
                        <label for="methodInstructions" class="form-label">Instrucciones</label>
                        <textarea class="form-control" id="methodInstructions" name="instructions" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Guardar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.bg-purple {
    background-color: #6f42c1 !important;
}
</style>
<?= $this->endSection() ?>
