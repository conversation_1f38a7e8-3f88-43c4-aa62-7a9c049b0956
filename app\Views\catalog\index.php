<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MrCell - Catálogo de Productos</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .search-bar {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .filters-sidebar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .product-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            height: 250px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .product-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        
        .product-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--danger-color);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .product-badge.featured {
            background: var(--warning-color);
            color: #333;
        }
        
        .product-info {
            padding: 20px;
        }
        
        .product-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .product-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .product-price-old {
            text-decoration: line-through;
            color: var(--secondary-color);
            font-size: 0.9rem;
            margin-right: 10px;
        }
        
        .stock-status {
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 600;
        }
        
        .stock-status.in-stock {
            background: #d4edda;
            color: #155724;
        }
        
        .stock-status.low-stock {
            background: #fff3cd;
            color: #856404;
        }
        
        .stock-status.out-of-stock {
            background: #f8d7da;
            color: #721c24;
        }
        
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 40px;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 40px;
        }
        
        .no-products {
            text-align: center;
            padding: 60px 20px;
            color: var(--secondary-color);
        }
        
        .category-filter {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin-bottom: 5px;
            display: block;
            text-decoration: none;
            color: #333;
        }
        
        .category-filter:hover,
        .category-filter.active {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
        }
        
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .suggestion-item:hover {
            background: #f8f9fa;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-mobile-alt me-2"></i>MrCell
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/catalog">Catálogo</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">Nosotros</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">Contacto</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/carrito">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="badge bg-primary" id="cartCount">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/login">
                            <i class="fas fa-user"></i> Ingresar
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Encuentra tu próximo smartphone</h1>
            <p class="lead mb-4">Los mejores precios en celulares, accesorios y tecnología</p>
            
            <div class="search-bar position-relative">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control" placeholder="Buscar productos..." id="searchInput">
                    <button class="btn btn-light" type="button" onclick="searchProducts()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="search-suggestions" id="searchSuggestions"></div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3">
                <div class="filters-sidebar">
                    <h5 class="mb-3">Filtros</h5>
                    
                    <!-- Categorías -->
                    <div class="mb-4">
                        <h6>Categorías</h6>
                        <div id="categoriesList">
                            <div class="text-center py-3">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rango de Precios -->
                    <div class="mb-4">
                        <h6>Precio</h6>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Mín" id="minPrice">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Máx" id="maxPrice">
                            </div>
                        </div>
                        <button class="btn btn-sm btn-primary w-100 mt-2" onclick="applyPriceFilter()">
                            Aplicar
                        </button>
                    </div>
                    
                    <!-- Ordenamiento -->
                    <div class="mb-4">
                        <h6>Ordenar por</h6>
                        <select class="form-select form-select-sm" id="sortBy" onchange="loadProducts()">
                            <option value="newest">Más recientes</option>
                            <option value="name">Nombre A-Z</option>
                            <option value="price_asc">Precio: menor a mayor</option>
                            <option value="price_desc">Precio: mayor a menor</option>
                            <option value="popular">Más populares</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>Limpiar filtros
                    </button>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-0">Productos</h4>
                        <small class="text-muted" id="productsCount">Cargando...</small>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary btn-sm" onclick="toggleView('grid')" id="gridViewBtn">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="toggleView('list')" id="listViewBtn">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                
                <div id="productsContainer">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>Cargando productos...</p>
                    </div>
                </div>
                
                <div class="pagination-wrapper" id="paginationWrapper" style="display: none;">
                    <button class="btn btn-primary" onclick="loadMoreProducts()" id="loadMoreBtn">
                        Cargar más productos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>MrCell</h5>
                    <p>Tu tienda de confianza para celulares y tecnología en Guatemala.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 MrCell. Todos los derechos reservados.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentProducts = [];
        let currentOffset = 0;
        let currentLimit = 24;
        let currentFilters = {
            category_id: null,
            search: null,
            min_price: null,
            max_price: null,
            sort_by: 'newest'
        };
        let searchTimeout;

        // Inicializar al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            loadProducts();
            setupSearchSuggestions();
        });

        // Cargar categorías
        async function loadCategories() {
            try {
                const response = await fetch('/api/catalog/categories');
                const data = await response.json();
                
                if (data.status === 'success') {
                    displayCategories(data.data);
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Mostrar categorías
        function displayCategories(categories) {
            const container = document.getElementById('categoriesList');
            
            let html = '<a href="#" class="category-filter" onclick="filterByCategory(null)">Todas las categorías</a>';
            
            categories.forEach(category => {
                if (category.products_count > 0) {
                    html += `
                        <a href="#" class="category-filter" onclick="filterByCategory(${category.id})">
                            ${category.name} (${category.products_count})
                        </a>
                    `;
                }
            });
            
            container.innerHTML = html;
        }

        // Cargar productos
        async function loadProducts(append = false) {
            try {
                if (!append) {
                    currentOffset = 0;
                    document.getElementById('productsContainer').innerHTML = `
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>Cargando productos...</p>
                        </div>
                    `;
                }

                const params = new URLSearchParams({
                    limit: currentLimit,
                    offset: append ? currentOffset : 0,
                    sort_by: currentFilters.sort_by
                });

                if (currentFilters.category_id) params.append('category_id', currentFilters.category_id);
                if (currentFilters.search) params.append('search', currentFilters.search);
                if (currentFilters.min_price) params.append('min_price', currentFilters.min_price);
                if (currentFilters.max_price) params.append('max_price', currentFilters.max_price);

                const response = await fetch(`/api/catalog/products?${params}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    if (append) {
                        currentProducts = [...currentProducts, ...data.data.products];
                        currentOffset += data.data.products.length;
                    } else {
                        currentProducts = data.data.products;
                        currentOffset = data.data.products.length;
                    }
                    
                    displayProducts(currentProducts, append);
                    updateProductsCount();
                    
                    // Mostrar/ocultar botón "Cargar más"
                    const loadMoreBtn = document.getElementById('paginationWrapper');
                    if (data.data.pagination.has_more) {
                        loadMoreBtn.style.display = 'block';
                    } else {
                        loadMoreBtn.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('productsContainer').innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h5>Error al cargar productos</h5>
                        <p>Por favor, intenta nuevamente.</p>
                    </div>
                `;
            }
        }

        // Mostrar productos
        function displayProducts(products, append = false) {
            const container = document.getElementById('productsContainer');
            
            if (products.length === 0 && !append) {
                container.innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>No se encontraron productos</h5>
                        <p>Intenta ajustar los filtros de búsqueda.</p>
                    </div>
                `;
                return;
            }

            const productsHtml = products.map(product => `
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card">
                        <div class="product-image">
                            ${product.featured_image ? 
                                `<img src="${product.featured_image}" alt="${product.name}">` :
                                `<i class="fas fa-mobile-alt fa-3x text-muted"></i>`
                            }
                            ${product.discount_percentage > 0 ? 
                                `<span class="product-badge">-${product.discount_percentage}%</span>` : ''
                            }
                            ${product.is_featured ? 
                                `<span class="product-badge featured">Destacado</span>` : ''
                            }
                        </div>
                        <div class="product-info">
                            <h6 class="product-title">${product.name}</h6>
                            <div class="mb-2">
                                ${product.price_sale ? 
                                    `<span class="product-price-old">Q${product.price_regular.toLocaleString()}</span>` : ''
                                }
                                <span class="product-price">Q${product.final_price.toLocaleString()}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="stock-status ${product.stock_status.replace('_', '-')}">
                                    ${getStockStatusText(product.stock_status)}
                                </span>
                                <button class="btn btn-primary btn-sm" onclick="viewProduct(${product.id})">
                                    Ver detalles
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            if (append) {
                container.innerHTML += productsHtml;
            } else {
                container.innerHTML = `<div class="row">${productsHtml}</div>`;
            }
        }

        // Funciones auxiliares
        function getStockStatusText(status) {
            const statusTexts = {
                'in_stock': 'En stock',
                'low_stock': 'Pocas unidades',
                'out_of_stock': 'Agotado'
            };
            return statusTexts[status] || 'Desconocido';
        }

        function updateProductsCount() {
            document.getElementById('productsCount').textContent = 
                `${currentProducts.length} productos encontrados`;
        }

        function filterByCategory(categoryId) {
            currentFilters.category_id = categoryId;
            
            // Actualizar UI de categorías
            document.querySelectorAll('.category-filter').forEach(el => {
                el.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadProducts();
        }

        function searchProducts() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            currentFilters.search = searchTerm || null;
            loadProducts();
        }

        function applyPriceFilter() {
            currentFilters.min_price = document.getElementById('minPrice').value || null;
            currentFilters.max_price = document.getElementById('maxPrice').value || null;
            loadProducts();
        }

        function clearFilters() {
            currentFilters = {
                category_id: null,
                search: null,
                min_price: null,
                max_price: null,
                sort_by: 'newest'
            };
            
            document.getElementById('searchInput').value = '';
            document.getElementById('minPrice').value = '';
            document.getElementById('maxPrice').value = '';
            document.getElementById('sortBy').value = 'newest';
            
            document.querySelectorAll('.category-filter').forEach(el => {
                el.classList.remove('active');
            });
            document.querySelector('.category-filter').classList.add('active');
            
            loadProducts();
        }

        function loadMoreProducts() {
            loadProducts(true);
        }

        function viewProduct(productId) {
            window.location.href = `/catalog/product/${productId}`;
        }

        function toggleView(viewType) {
            // Implementar cambio de vista grid/list
            console.log('Toggle view:', viewType);
        }

        // Configurar sugerencias de búsqueda
        function setupSearchSuggestions() {
            const searchInput = document.getElementById('searchInput');
            const suggestionsContainer = document.getElementById('searchSuggestions');

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(async () => {
                    try {
                        const response = await fetch(`/api/catalog/search/suggestions?q=${encodeURIComponent(query)}&limit=5`);
                        const data = await response.json();

                        if (data.status === 'success' && data.data.length > 0) {
                            const suggestionsHtml = data.data.map(product => `
                                <div class="suggestion-item" onclick="selectSuggestion('${product.name}')">
                                    <strong>${product.name}</strong>
                                    <div class="text-muted small">Q${product.final_price.toLocaleString()}</div>
                                </div>
                            `).join('');

                            suggestionsContainer.innerHTML = suggestionsHtml;
                            suggestionsContainer.style.display = 'block';
                        } else {
                            suggestionsContainer.style.display = 'none';
                        }
                    } catch (error) {
                        console.error('Error loading suggestions:', error);
                    }
                }, 300);
            });

            // Ocultar sugerencias al hacer clic fuera
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-bar')) {
                    suggestionsContainer.style.display = 'none';
                }
            });
        }

        function selectSuggestion(productName) {
            document.getElementById('searchInput').value = productName;
            document.getElementById('searchSuggestions').style.display = 'none';
            searchProducts();
        }
    </script>
</body>
</html>
