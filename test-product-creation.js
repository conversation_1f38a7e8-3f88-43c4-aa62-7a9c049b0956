const { chromium } = require('playwright');

async function testProductCreation() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('🧪 PROBANDO CREACIÓN DE PRODUCTO...\n');

        // Login como admin
        console.log('🔐 Haciendo login como admin...');
        await page.goto('http://localhost:8080/admin/login');
        await page.waitForLoadState('networkidle');

        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'Clairo!23');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);

        // Ir a crear producto
        console.log('➕ Navegando a crear producto...');
        await page.goto('http://localhost:8080/admin/products/create');
        await page.waitForLoadState('networkidle');

        // Llenar formulario básico
        console.log('📝 Llenando formulario...');
        await page.fill('input[name="name"]', 'Producto de Prueba Error 500');
        await page.fill('input[name="sku"]', 'TEST-ERROR-500-' + Date.now());
        await page.fill('textarea[name="description"]', 'Producto para probar el error 500');
        await page.fill('textarea[name="short_description"]', 'Descripción corta de prueba');
        
        // Seleccionar categoría
        const categorySelect = page.locator('select[name="category_id"]');
        if (await categorySelect.count() > 0) {
            await categorySelect.selectOption({ index: 1 });
        }
        
        // Seleccionar marca
        const brandSelect = page.locator('select[name="brand_id"]');
        if (await brandSelect.count() > 0) {
            await brandSelect.selectOption({ index: 1 });
        }
        
        // Precios
        await page.fill('input[name="price_regular"]', '100.00');
        await page.fill('input[name="price_sale"]', '85.00');
        
        // Stock
        await page.fill('input[name="stock_quantity"]', '50');
        await page.fill('input[name="stock_min"]', '5');

        console.log('🚀 Enviando formulario...');
        
        // Interceptar respuestas para capturar errores
        page.on('response', response => {
            if (response.status() >= 400) {
                console.log(`❌ Error HTTP ${response.status()}: ${response.url()}`);
            }
        });

        // Enviar formulario
        await page.click('button[type="submit"]');
        
        // Esperar respuesta
        await page.waitForTimeout(5000);
        
        const currentUrl = page.url();
        console.log(`🔗 URL actual: ${currentUrl}`);
        
        // Verificar si hay error 500
        const pageContent = await page.textContent('body');
        
        if (pageContent.includes('500') || pageContent.includes('Internal Server Error')) {
            console.log('❌ ERROR 500 DETECTADO');
            console.log('📄 Contenido de la página:');
            console.log(pageContent.substring(0, 1000) + '...');
        } else if (currentUrl.includes('/admin/products') && !currentUrl.includes('/create')) {
            console.log('✅ Producto creado exitosamente - redirigido a lista');
        } else if (currentUrl.includes('/create')) {
            console.log('⚠️ Permanece en página de creación - posible error de validación');
            
            // Buscar mensajes de error
            const errorMessages = await page.locator('.alert-danger, .error, .text-danger').allTextContents();
            if (errorMessages.length > 0) {
                console.log('📋 Mensajes de error encontrados:');
                errorMessages.forEach(msg => console.log(`  - ${msg}`));
            }
        }
        
        // Verificar si el producto se creó en la base de datos
        console.log('\n🔍 Verificando en lista de productos...');
        await page.goto('http://localhost:8080/admin/products');
        await page.waitForLoadState('networkidle');
        
        const productExists = await page.locator('td:has-text("Producto de Prueba Error 500")').count() > 0;
        console.log(`📦 Producto en lista: ${productExists ? '✅ SÍ' : '❌ NO'}`);

    } catch (error) {
        console.error('❌ Error durante la prueba:', error.message);
    } finally {
        await browser.close();
    }
}

// Ejecutar prueba
testProductCreation().catch(console.error);
