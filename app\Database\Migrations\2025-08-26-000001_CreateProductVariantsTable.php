<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProductVariantsTable extends Migration
{
    public function up()
    {
        // Crear tabla product_variants
        $this->forge->addField([
            'id' => [
                'type' => 'INTEGER',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_id' => [
                'type' => 'INTEGER',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID del producto padre'
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Nombre de la variante (ej: MetaPod, Gastly)'
            ],
            'sku' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'SKU único de la variante'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Descripción específica de la variante'
            ],
            'price_regular' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'comment' => 'Precio regular de la variante'
            ],
            'price_sale' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Precio de oferta de la variante'
            ],
            'stock_quantity' => [
                'type' => 'INTEGER',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Stock disponible de la variante'
            ],
            'stock_min' => [
                'type' => 'INTEGER',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Stock mínimo de la variante'
            ],
            'featured_image' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'Imagen principal de la variante'
            ],
            'gallery_images' => [
                'type' => 'LONGTEXT',
                'null' => true,
                'comment' => 'Galería de imágenes de la variante (JSON)'
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
                'comment' => 'Si la variante está activa'
            ],
            'is_default' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si es la variante por defecto del producto'
            ],
            'sort_order' => [
                'type' => 'INTEGER',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Orden de visualización'
            ],
            'attributes' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Atributos adicionales de la variante (color, tamaño, etc.)'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        // Definir claves
        $this->forge->addKey('id', true);
        $this->forge->addKey('product_id');
        $this->forge->addKey('sku');
        $this->forge->addKey(['is_active', 'sort_order']);
        
        // Crear tabla
        $this->forge->createTable('product_variants');

        // Agregar foreign key constraint
        $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');

        // Crear índices únicos
        $this->db->query('ALTER TABLE product_variants ADD UNIQUE KEY unique_sku (sku)');
        $this->db->query('ALTER TABLE product_variants ADD UNIQUE KEY unique_default_per_product (product_id, is_default) WHERE is_default = 1');

        // Agregar campo has_variants a la tabla products para optimización
        $this->forge->addColumn('products', [
            'has_variants' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Indica si el producto tiene variantes',
                'after' => 'is_featured'
            ]
        ]);

        echo "Tabla product_variants creada exitosamente.\n";
        echo "Campo has_variants agregado a la tabla products.\n";
    }

    public function down()
    {
        // Eliminar foreign key constraint
        $this->forge->dropForeignKey('product_variants', 'product_variants_product_id_foreign');
        
        // Eliminar tabla
        $this->forge->dropTable('product_variants');

        // Eliminar campo has_variants de products
        $this->forge->dropColumn('products', 'has_variants');

        echo "Tabla product_variants eliminada.\n";
        echo "Campo has_variants eliminado de la tabla products.\n";
    }
}
