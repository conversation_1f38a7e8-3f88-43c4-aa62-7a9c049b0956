<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\AdvancedSearchEngine;
use App\Libraries\SmartFilters;
use App\Libraries\SimpleCache;

/**
 * API de Búsqueda Avanzada
 * Sistema inteligente con relevancia, voz y filtros dinámicos
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class AdvancedSearchApi extends ResourceController
{
    protected $searchEngine;
    protected $smartFilters;
    protected $format = 'json';
    
    public function __construct()
    {
        $this->searchEngine = new AdvancedSearchEngine();
        $this->smartFilters = new SmartFilters();
    }
    
    /**
     * Búsqueda inteligente principal
     * GET /api/search/advanced
     * 
     * Parámetros:
     * - q: término de búsqueda
     * - filters: filtros aplicados (JSON)
     * - page: página (default: 1)
     * - limit: resultados por página (default: 20)
     * - include_suggestions: incluir sugerencias (default: true)
     * - include_analytics: incluir analytics (default: false)
     * - boost_popular: potenciar productos populares (default: true)
     * - enable_fuzzy: habilitar búsqueda difusa (default: true)
     */
    public function search()
    {
        try {
            $startTime = microtime(true);
            
            // Obtener parámetros
            $query = $this->request->getGet('q') ?? '';
            $filtersJson = $this->request->getGet('filters') ?? '{}';
            $page = max(1, (int) ($this->request->getGet('page') ?? 1));
            $limit = min(50, max(1, (int) ($this->request->getGet('limit') ?? 20)));
            $includeSuggestions = filter_var($this->request->getGet('include_suggestions') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            $includeAnalytics = filter_var($this->request->getGet('include_analytics') ?? 'false', FILTER_VALIDATE_BOOLEAN);
            $boostPopular = filter_var($this->request->getGet('boost_popular') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            $enableFuzzy = filter_var($this->request->getGet('enable_fuzzy') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            
            // Decodificar filtros
            $filters = json_decode($filtersJson, true) ?? [];
            
            // Opciones de búsqueda
            $options = [
                'limit' => $limit,
                'offset' => ($page - 1) * $limit,
                'include_suggestions' => $includeSuggestions,
                'include_analytics' => $includeAnalytics,
                'boost_popular' => $boostPopular,
                'enable_fuzzy' => $enableFuzzy
            ];
            
            // Realizar búsqueda inteligente
            $searchResults = $this->searchEngine->intelligentSearch($query, $filters, $options);
            
            // Formatear productos
            $formattedProducts = array_map([$this, 'formatProduct'], $searchResults['products']);
            
            // Calcular paginación
            $totalPages = ceil($searchResults['total'] / $limit);
            
            $response = [
                'status' => 'success',
                'data' => [
                    'products' => $formattedProducts,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $searchResults['total'],
                        'total_pages' => $totalPages,
                        'has_previous' => $page > 1,
                        'has_next' => $page < $totalPages,
                        'previous_page' => $page > 1 ? $page - 1 : null,
                        'next_page' => $page < $totalPages ? $page + 1 : null
                    ],
                    'query_info' => $searchResults['query_info'],
                    'suggestions' => $searchResults['suggestions'] ?? [],
                    'analytics' => $searchResults['analytics'] ?? []
                ],
                'meta' => [
                    'execution_time' => round(microtime(true) - $startTime, 3),
                    'cached' => $searchResults['cached'] ?? false,
                    'api_version' => '2.0'
                ]
            ];
            
            return $this->respond($response);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en AdvancedSearchApi::search: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SEARCH_ERROR'
            ], 500);
        }
    }
    
    /**
     * Obtener filtros inteligentes
     * GET /api/search/filters
     * 
     * Parámetros:
     * - q: término de búsqueda (opcional)
     * - current_filters: filtros ya aplicados (JSON)
     */
    public function filters()
    {
        try {
            $query = $this->request->getGet('q') ?? '';
            $currentFiltersJson = $this->request->getGet('current_filters') ?? '{}';
            $currentFilters = json_decode($currentFiltersJson, true) ?? [];
            
            // Obtener filtros inteligentes
            $filters = $this->smartFilters->getSmartFilters($query, $currentFilters);
            
            return $this->respond([
                'status' => 'success',
                'data' => $filters,
                'meta' => [
                    'query' => $query,
                    'current_filters' => $currentFilters
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en AdvancedSearchApi::filters: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al obtener filtros',
                'error_code' => 'FILTERS_ERROR'
            ], 500);
        }
    }
    
    /**
     * Sugerencias de búsqueda avanzadas
     * GET /api/search/suggestions
     * 
     * Parámetros:
     * - q: término parcial de búsqueda
     * - limit: número máximo de sugerencias (default: 10)
     * - include_products: incluir productos (default: true)
     * - include_categories: incluir categorías (default: true)
     * - include_brands: incluir marcas (default: true)
     */
    public function suggestions()
    {
        try {
            $query = $this->request->getGet('q') ?? '';
            $limit = min(20, max(1, (int) ($this->request->getGet('limit') ?? 10)));
            $includeProducts = filter_var($this->request->getGet('include_products') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            $includeCategories = filter_var($this->request->getGet('include_categories') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            $includeBrands = filter_var($this->request->getGet('include_brands') ?? 'true', FILTER_VALIDATE_BOOLEAN);
            
            if (strlen($query) < 2) {
                return $this->respond([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'Query too short'
                ]);
            }
            
            $suggestions = [];
            
            // Sugerencias de productos
            if ($includeProducts) {
                $productSuggestions = $this->getProductSuggestions($query, $limit);
                $suggestions = array_merge($suggestions, $productSuggestions);
            }
            
            // Sugerencias de categorías
            if ($includeCategories) {
                $categorySuggestions = $this->getCategorySuggestions($query, $limit);
                $suggestions = array_merge($suggestions, $categorySuggestions);
            }
            
            // Sugerencias de marcas
            if ($includeBrands) {
                $brandSuggestions = $this->getBrandSuggestions($query, $limit);
                $suggestions = array_merge($suggestions, $brandSuggestions);
            }
            
            // Ordenar por relevancia y limitar
            usort($suggestions, function($a, $b) {
                return $b['relevance'] <=> $a['relevance'];
            });
            
            $suggestions = array_slice($suggestions, 0, $limit);
            
            return $this->respond([
                'status' => 'success',
                'data' => $suggestions,
                'meta' => [
                    'query' => $query,
                    'total_suggestions' => count($suggestions)
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en AdvancedSearchApi::suggestions: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al obtener sugerencias',
                'error_code' => 'SUGGESTIONS_ERROR'
            ], 500);
        }
    }
    
    /**
     * Procesar búsqueda por voz
     * POST /api/search/voice
     * 
     * Body:
     * - transcript: texto transcrito de la voz
     * - confidence: nivel de confianza (0-1)
     * - language: idioma detectado
     */
    public function voice()
    {
        try {
            $json = $this->request->getJSON(true);
            $transcript = $json['transcript'] ?? '';
            $confidence = $json['confidence'] ?? 1.0;
            $language = $json['language'] ?? 'es-GT';
            
            if (empty($transcript)) {
                return $this->failValidationError('Transcript is required');
            }
            
            // Procesar y limpiar transcript
            $cleanedQuery = $this->processVoiceTranscript($transcript);
            
            // Realizar búsqueda con el query limpio
            $searchResults = $this->searchEngine->intelligentSearch($cleanedQuery, [], [
                'limit' => 20,
                'offset' => 0,
                'include_suggestions' => true,
                'include_analytics' => true
            ]);
            
            // Formatear productos
            $formattedProducts = array_map([$this, 'formatProduct'], $searchResults['products']);
            
            return $this->respond([
                'status' => 'success',
                'data' => [
                    'original_transcript' => $transcript,
                    'cleaned_query' => $cleanedQuery,
                    'confidence' => $confidence,
                    'language' => $language,
                    'products' => $formattedProducts,
                    'total_results' => $searchResults['total'],
                    'suggestions' => $searchResults['suggestions'] ?? []
                ],
                'meta' => [
                    'voice_processed' => true,
                    'execution_time' => $searchResults['execution_time']
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en AdvancedSearchApi::voice: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al procesar búsqueda por voz',
                'error_code' => 'VOICE_SEARCH_ERROR'
            ], 500);
        }
    }
    
    /**
     * Analytics de búsqueda
     * GET /api/search/analytics
     * 
     * Parámetros:
     * - period: período de análisis (day, week, month)
     * - limit: número de resultados (default: 20)
     */
    public function analytics()
    {
        try {
            $period = $this->request->getGet('period') ?? 'week';
            $limit = min(100, max(1, (int) ($this->request->getGet('limit') ?? 20)));
            
            // Validar período
            if (!in_array($period, ['day', 'week', 'month'])) {
                $period = 'week';
            }
            
            $analytics = $this->getSearchAnalytics($period, $limit);
            
            return $this->respond([
                'status' => 'success',
                'data' => $analytics,
                'meta' => [
                    'period' => $period,
                    'limit' => $limit
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en AdvancedSearchApi::analytics: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al obtener analytics',
                'error_code' => 'ANALYTICS_ERROR'
            ], 500);
        }
    }
    
    /**
     * Formatear producto para respuesta
     */
    private function formatProduct(array $product): array
    {
        $finalPrice = $product['price_sale'] ?? $product['price_regular'];
        $hasDiscount = !empty($product['price_sale']) && $product['price_sale'] < $product['price_regular'];
        
        return [
            'id' => (int) $product['id'],
            'name' => $product['name'],
            'slug' => $product['slug'],
            'sku' => $product['sku'],
            'description' => $product['short_description'] ?? substr($product['description'] ?? '', 0, 150),
            'category' => [
                'id' => (int) $product['category_id'],
                'name' => $product['category_name']
            ],
            'brand' => [
                'id' => (int) $product['brand_id'],
                'name' => $product['brand_name']
            ],
            'price' => [
                'regular' => (float) $product['price_regular'],
                'sale' => $product['price_sale'] ? (float) $product['price_sale'] : null,
                'final' => (float) $finalPrice,
                'formatted' => 'Q' . number_format($finalPrice, 2),
                'has_discount' => $hasDiscount,
                'discount_percentage' => $hasDiscount ? 
                    round((($product['price_regular'] - $product['price_sale']) / $product['price_regular']) * 100) : 0
            ],
            'stock' => [
                'quantity' => (int) $product['stock_quantity'],
                'status' => $product['stock_status'],
                'in_stock' => $product['stock_quantity'] > 0 && $product['stock_status'] === 'in_stock'
            ],
            'rating' => [
                'average' => (float) $product['rating_average'],
                'count' => (int) $product['rating_count']
            ],
            'image' => [
                'featured' => $product['featured_image'] ? base_url('uploads/' . $product['featured_image']) : null,
                'thumbnail' => $product['featured_image'] ? base_url('uploads/thumbs/' . $product['featured_image']) : null
            ],
            'flags' => [
                'is_featured' => (bool) $product['is_featured'],
                'is_new' => strtotime($product['created_at']) > strtotime('-30 days')
            ],
            'url' => base_url('producto/' . $product['slug']),
            'relevance' => [
                'score' => $product['final_relevance'] ?? $product['relevance_score'] ?? 0,
                'details' => $product['scoring_details'] ?? null
            ]
        ];
    }
    
    /**
     * Obtener sugerencias de productos
     */
    private function getProductSuggestions(string $query, int $limit): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('products p');
        
        $builder->select('p.id, p.name, p.slug, p.featured_image, 
                         MATCH(p.name, p.description, p.sku) AGAINST(? IN BOOLEAN MODE) as relevance', false);
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        $builder->where("MATCH(p.name, p.description, p.sku) AGAINST(? IN BOOLEAN MODE)", "+{$query}*");
        $builder->orderBy('relevance', 'DESC');
        $builder->limit($limit);
        
        $results = $builder->get()->getResultArray();
        
        return array_map(function($item) {
            return [
                'type' => 'product',
                'id' => $item['id'],
                'text' => $item['name'],
                'slug' => $item['slug'],
                'image' => $item['featured_image'] ? base_url('uploads/thumbs/' . $item['featured_image']) : null,
                'relevance' => $item['relevance'],
                'url' => base_url('producto/' . $item['slug'])
            ];
        }, $results);
    }
    
    /**
     * Obtener sugerencias de categorías
     */
    private function getCategorySuggestions(string $query, int $limit): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('categories c');
        
        $builder->select('c.id, c.name, c.slug, COUNT(p.id) as product_count');
        $builder->join('products p', 'c.id = p.category_id', 'left');
        $builder->where('c.name LIKE', "%{$query}%");
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        $builder->groupBy('c.id, c.name, c.slug');
        $builder->orderBy('product_count', 'DESC');
        $builder->limit($limit);
        
        $results = $builder->get()->getResultArray();
        
        return array_map(function($item) {
            return [
                'type' => 'category',
                'id' => $item['id'],
                'text' => $item['name'],
                'slug' => $item['slug'],
                'product_count' => $item['product_count'],
                'relevance' => $item['product_count'] / 10, // Normalizar
                'url' => base_url('categoria/' . $item['slug'])
            ];
        }, $results);
    }
    
    /**
     * Obtener sugerencias de marcas
     */
    private function getBrandSuggestions(string $query, int $limit): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('brands b');
        
        $builder->select('b.id, b.name, b.slug, b.logo, COUNT(p.id) as product_count');
        $builder->join('products p', 'b.id = p.brand_id', 'left');
        $builder->where('b.name LIKE', "%{$query}%");
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        $builder->groupBy('b.id, b.name, b.slug, b.logo');
        $builder->orderBy('product_count', 'DESC');
        $builder->limit($limit);
        
        $results = $builder->get()->getResultArray();
        
        return array_map(function($item) {
            return [
                'type' => 'brand',
                'id' => $item['id'],
                'text' => $item['name'],
                'slug' => $item['slug'],
                'logo' => $item['logo'] ? base_url('uploads/brands/' . $item['logo']) : null,
                'product_count' => $item['product_count'],
                'relevance' => $item['product_count'] / 10, // Normalizar
                'url' => base_url('marca/' . $item['slug'])
            ];
        }, $results);
    }
    
    /**
     * Procesar transcript de voz
     */
    private function processVoiceTranscript(string $transcript): string
    {
        // Convertir a minúsculas
        $cleaned = strtolower(trim($transcript));
        
        // Correcciones comunes de reconocimiento de voz
        $corrections = [
            'iphone' => 'iPhone',
            'samsung' => 'Samsung',
            'huawei' => 'Huawei',
            'xiaomi' => 'Xiaomi',
            'bluetooth' => 'bluetooth',
            'wifi' => 'WiFi',
            'usb' => 'USB'
        ];
        
        foreach ($corrections as $wrong => $correct) {
            $cleaned = str_ireplace($wrong, $correct, $cleaned);
        }
        
        return $cleaned;
    }
    
    /**
     * Obtener analytics de búsqueda
     */
    private function getSearchAnalytics(string $period, int $limit): array
    {
        $db = \Config\Database::connect();
        
        // Determinar rango de fechas
        switch ($period) {
            case 'day':
                $dateFilter = "created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $dateFilter = "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $dateFilter = "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            default:
                $dateFilter = "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
        }
        
        // Consultas más populares
        $popularQueries = $db->query("
            SELECT query, COUNT(*) as search_count, AVG(result_count) as avg_results
            FROM search_logs 
            WHERE {$dateFilter} AND result_count > 0
            GROUP BY query 
            ORDER BY search_count DESC 
            LIMIT {$limit}
        ")->getResultArray();
        
        // Consultas sin resultados
        $noResultQueries = $db->query("
            SELECT query, COUNT(*) as search_count
            FROM search_logs 
            WHERE {$dateFilter} AND result_count = 0
            GROUP BY query 
            ORDER BY search_count DESC 
            LIMIT {$limit}
        ")->getResultArray();
        
        // Estadísticas generales
        $stats = $db->query("
            SELECT 
                COUNT(*) as total_searches,
                COUNT(DISTINCT query) as unique_queries,
                AVG(result_count) as avg_results_per_search,
                SUM(CASE WHEN result_count = 0 THEN 1 ELSE 0 END) as no_result_searches
            FROM search_logs 
            WHERE {$dateFilter}
        ")->getRowArray();
        
        return [
            'period' => $period,
            'stats' => $stats,
            'popular_queries' => $popularQueries,
            'no_result_queries' => $noResultQueries
        ];
    }
}
