<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-eye me-2"></i>Ver Producto</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/admin/products">Productos</a></li>
                <li class="breadcrumb-item active">Ver <?= esc($product['name']) ?></li>
            </ol>
        </nav>
    </div>
    <div class="mt-3">
        <a href="/admin/products" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
        <a href="/admin/products/edit/<?= $product['id'] ?>" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>Editar
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-box me-2"></i><?= esc($product['name']) ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <?php if (!empty($product['featured_image'])): ?>
                            <img src="<?= base_url($product['featured_image']) ?>"
                                 alt="<?= esc($product['name']) ?>"
                                 class="img-fluid rounded"
                                 style="max-height: 300px; object-fit: cover;">
                        <?php else: ?>
                            <div class="text-center p-5 bg-light rounded">
                                <i class="fas fa-image fa-3x text-muted"></i>
                                <p class="mt-2 text-muted">Sin imagen</p>
                            </div>
                        <?php endif; ?>

                        <?php
                        $galleryImages = [];
                        if (!empty($product['gallery_images'])) {
                            $galleryImages = json_decode($product['gallery_images'], true) ?: [];
                        }
                        ?>

                        <?php if (!empty($galleryImages)): ?>
                            <div class="mt-3">
                                <h6>Galería de Imágenes:</h6>
                                <div class="row">
                                    <?php foreach ($galleryImages as $index => $image): ?>
                                        <div class="col-6 mb-2">
                                            <img src="<?= base_url($image) ?>"
                                                 alt="Imagen <?= $index + 1 ?>"
                                                 class="img-fluid rounded cursor-pointer"
                                                 style="height: 80px; object-fit: cover;"
                                                 onclick="showImageModal('<?= base_url($image) ?>', 'Imagen <?= $index + 1 ?>')">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>SKU:</strong></td>
                                <td><?= esc($product['sku']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Categoría:</strong></td>
                                <td><?= esc($product['category_name'] ?? 'Sin categoría') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Precio Regular:</strong></td>
                                <td>
                                    <?php
                                    $currency = $product['currency'] ?? 'GTQ';
                                    $symbol = $currency === 'USD' ? '$' : 'Q';
                                    echo $symbol . number_format($product['price_regular'], 2) . ' ' . $currency;

                                    // Mostrar conversión si es diferente moneda
                                    if ($currency === 'USD') {
                                        $convertedPrice = $product['price_regular'] * 7.75; // Usar tipo de cambio
                                        echo '<br><small class="text-muted">≈ Q' . number_format($convertedPrice, 2) . ' GTQ</small>';
                                    } elseif ($currency === 'GTQ') {
                                        $convertedPrice = $product['price_regular'] / 7.75;
                                        echo '<br><small class="text-muted">≈ $' . number_format($convertedPrice, 2) . ' USD</small>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php if ($product['price_sale']): ?>
                            <tr>
                                <td><strong>Precio de Oferta:</strong></td>
                                <td>
                                    <?php
                                    echo $symbol . number_format($product['price_sale'], 2) . ' ' . $currency;

                                    // Mostrar conversión si es diferente moneda
                                    if ($currency === 'USD') {
                                        $convertedSale = $product['price_sale'] * 7.75;
                                        echo '<br><small class="text-muted">≈ Q' . number_format($convertedSale, 2) . ' GTQ</small>';
                                    } elseif ($currency === 'GTQ') {
                                        $convertedSale = $product['price_sale'] / 7.75;
                                        echo '<br><small class="text-muted">≈ $' . number_format($convertedSale, 2) . ' USD</small>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td><strong>Stock:</strong></td>
                                <td>
                                    <?php 
                                    $stockClass = 'text-success';
                                    if ($product['stock_quantity'] < 5) $stockClass = 'text-danger';
                                    elseif ($product['stock_quantity'] < 20) $stockClass = 'text-warning';
                                    ?>
                                    <span class="<?= $stockClass ?>"><?= $product['stock_quantity'] ?> unidades</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Stock Mínimo:</strong></td>
                                <td><?= $product['stock_min'] ?> unidades</td>
                            </tr>
                            <tr>
                                <td><strong>Estado:</strong></td>
                                <td>
                                    <?php if ($product['is_active']): ?>
                                        <span class="badge bg-success">Activo</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactivo</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Destacado:</strong></td>
                                <td>
                                    <?php if ($product['is_featured']): ?>
                                        <span class="badge bg-warning">Destacado</span>
                                    <?php else: ?>
                                        <span class="badge bg-light text-dark">No destacado</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <hr>
                <h6>Descripción:</h6>
                <?php if (!empty($product['description'])): ?>
                    <p><?= nl2br(esc($product['description'])) ?></p>
                <?php else: ?>
                    <p class="text-muted"><em>No hay descripción disponible</em></p>
                <?php endif; ?>

                <hr>
                <h6>Descripción Corta:</h6>
                <?php if (!empty($product['short_description'])): ?>
                    <p><?= nl2br(esc($product['short_description'])) ?></p>
                <?php else: ?>
                    <p class="text-muted"><em>No hay descripción corta disponible</em></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información del Producto</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td><?= $product['id'] ?></td>
                    </tr>
                    <tr>
                        <td><strong>UUID:</strong></td>
                        <td><small><?= $product['uuid'] ?></small></td>
                    </tr>
                    <tr>
                        <td><strong>Slug:</strong></td>
                        <td><small><?= $product['slug'] ?></small></td>
                    </tr>
                    <tr>
                        <td><strong>Peso:</strong></td>
                        <td>
                            <?php if (!empty($product['weight']) && $product['weight'] > 0): ?>
                                <?= $product['weight'] ?> kg
                            <?php else: ?>
                                <span class="text-muted">No especificado</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Dimensiones:</strong></td>
                        <td>
                            <?php
                            $hasNewDimensions = !empty($product['dimension_length']) || !empty($product['dimension_width']) || !empty($product['dimension_height']);
                            if ($hasNewDimensions):
                                $dimensions = [];
                                if (!empty($product['dimension_length'])) $dimensions[] = "L: {$product['dimension_length']}";
                                if (!empty($product['dimension_width'])) $dimensions[] = "A: {$product['dimension_width']}";
                                if (!empty($product['dimension_height'])) $dimensions[] = "H: {$product['dimension_height']}";
                                $unit = $product['dimension_unit'] ?? 'cm';
                                echo implode(' × ', $dimensions) . ' ' . $unit;
                            elseif (!empty($product['dimensions'])):
                                echo esc($product['dimensions']) . ' <small class="text-muted">(formato anterior)</small>';
                            else: ?>
                                <span class="text-muted">No especificadas</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Creado:</strong></td>
                        <td><?= date('d/m/Y H:i', strtotime($product['created_at'])) ?></td>
                    </tr>
                    <?php if ($product['updated_at']): ?>
                    <tr>
                        <td><strong>Actualizado:</strong></td>
                        <td><?= date('d/m/Y H:i', strtotime($product['updated_at'])) ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>Información de Envío</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Peso para envío:</strong></td>
                        <td>
                            <?php if (!empty($product['weight']) && $product['weight'] > 0): ?>
                                <?= $product['weight'] ?> kg
                                <?php
                                $weightLb = $product['weight'] * 2.20462;
                                echo '<br><small class="text-muted">(' . number_format($weightLb, 2) . ' lb)</small>';
                                ?>
                            <?php else: ?>
                                <span class="text-muted">Peso no especificado</span>
                                <br><small class="text-warning">⚠️ Requerido para cálculo de envío</small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Dimensiones:</strong></td>
                        <td>
                            <?php
                            $hasNewDimensions = !empty($product['dimension_length']) || !empty($product['dimension_width']) || !empty($product['dimension_height']);
                            if ($hasNewDimensions):
                                $unit = $product['dimension_unit'] ?? 'cm';
                                echo '<div class="mb-1">';
                                if (!empty($product['dimension_length'])) echo "<span class='badge bg-secondary me-1'>L: {$product['dimension_length']} {$unit}</span>";
                                if (!empty($product['dimension_width'])) echo "<span class='badge bg-secondary me-1'>A: {$product['dimension_width']} {$unit}</span>";
                                if (!empty($product['dimension_height'])) echo "<span class='badge bg-secondary me-1'>H: {$product['dimension_height']} {$unit}</span>";
                                echo '</div>';

                                // Calcular volumen si todas las dimensiones están disponibles
                                if (!empty($product['dimension_length']) && !empty($product['dimension_width']) && !empty($product['dimension_height'])) {
                                    $volume = $product['dimension_length'] * $product['dimension_width'] * $product['dimension_height'];
                                    $volumeUnit = $unit == 'cm' ? 'cm³' : ($unit == 'in' ? 'in³' : 'mm³');
                                    echo "<small class='text-info'>Volumen: " . number_format($volume, 2) . " {$volumeUnit}</small>";
                                }
                            ?>
                            <?php elseif (!empty($product['dimensions'])): ?>
                                <?= esc($product['dimensions']) ?><br><small class="text-muted">(formato anterior)</small>
                            <?php else: ?>
                                <span class="text-muted">Dimensiones no especificadas</span>
                                <br><small class="text-warning">⚠️ Recomendado para envíos</small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Tipo de producto:</strong></td>
                        <td>
                            <?php if (isset($product['is_digital']) && $product['is_digital']): ?>
                                <span class="badge bg-info">Digital</span>
                                <br><small class="text-muted">No requiere envío físico</small>
                            <?php else: ?>
                                <span class="badge bg-primary">Físico</span>
                                <br><small class="text-muted">Requiere envío físico</small>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Acciones</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/products/edit/<?= $product['id'] ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Editar Producto
                    </a>
                    <button class="btn btn-secondary" onclick="duplicateProduct(<?= $product['id'] ?>, '<?= esc($product['name']) ?>')">
                        <i class="fas fa-copy me-2"></i>Duplicar Producto
                    </button>
                    <button class="btn btn-danger" onclick="deleteProduct(<?= $product['id'] ?>, '<?= esc($product['name']) ?>')">
                        <i class="fas fa-trash me-2"></i>Eliminar Producto
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para ver imágenes -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Imagen</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    console.log('Ver producto cargado');

    function showImageModal(imageSrc, imageTitle) {
        document.getElementById('modalImage').src = imageSrc;
        document.getElementById('imageModalLabel').textContent = imageTitle;
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
    }

    function duplicateProduct(id, name) {
        if (confirm(`¿Estás seguro de que deseas duplicar el producto "${name}"?`)) {
            fetch(`/admin/products/duplicate/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    window.location.href = '/admin/products';
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al duplicar el producto');
            });
        }
    }

    function deleteProduct(id, name) {
        if (confirm(`¿Estás seguro de que deseas eliminar el producto "${name}"?`)) {
            fetch(`/admin/products/delete/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    window.location.href = '/admin/products';
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al eliminar el producto');
            });
        }
    }
</script>
<?= $this->endSection() ?>
