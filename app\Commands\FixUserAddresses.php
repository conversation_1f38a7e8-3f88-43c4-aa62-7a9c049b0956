<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class FixUserAddresses extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:fix-user-addresses';
    protected $description = 'Agregar columna session_id a la tabla user_addresses';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== AGREGANDO COLUMNA session_id A user_addresses ===', 'yellow');
            CLI::newLine();
            
            // Verificar si la columna ya existe
            $query = $db->query("SHOW COLUMNS FROM user_addresses LIKE 'session_id'");
            $columnExists = $query->getNumRows() > 0;
            
            if ($columnExists) {
                CLI::write('✅ La columna session_id ya existe en user_addresses', 'green');
            } else {
                CLI::write('🔧 Agregando columna session_id...', 'white');
                
                // Agregar la columna
                $db->query("ALTER TABLE user_addresses ADD COLUMN session_id VARCHAR(128) NULL AFTER user_id");
                
                // Agregar índice
                $db->query("ALTER TABLE user_addresses ADD INDEX idx_session_id (session_id)");
                
                CLI::write('✅ Columna session_id agregada exitosamente', 'green');
            }
            
            // Verificar la estructura de la tabla
            CLI::newLine();
            CLI::write('=== ESTRUCTURA ACTUAL DE user_addresses ===', 'yellow');
            $columns = $db->query("SHOW COLUMNS FROM user_addresses")->getResultArray();
            
            foreach ($columns as $column) {
                $line = "- {$column['Field']} ({$column['Type']})";
                if ($column['Null'] === 'NO') {
                    $line .= ' NOT NULL';
                }
                if (!empty($column['Key'])) {
                    $line .= " [{$column['Key']}]";
                }
                CLI::write($line, 'white');
            }
            
            CLI::newLine();
            CLI::write('✅ Script completado exitosamente', 'green');
            
        } catch (\Exception $e) {
            CLI::error('❌ Error: ' . $e->getMessage());
            return;
        }
    }
}
