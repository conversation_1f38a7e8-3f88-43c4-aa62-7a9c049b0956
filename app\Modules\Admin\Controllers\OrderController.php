<?php

namespace App\Modules\Admin\Controllers;

use App\Controllers\BaseController;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use App\Models\ProductModel;

class OrderController extends BaseController
{
    protected $orderModel;
    protected $orderItemModel;
    protected $productModel;

    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->productModel = new ProductModel();
    }

    /**
     * Lista de pedidos
     */
    public function index()
    {
        $perPage = 20;
        $page = $this->request->getGet('page') ?? 1;
        $status = $this->request->getGet('status');
        $search = $this->request->getGet('search');

        $builder = $this->orderModel->orderBy('created_at', 'DESC');

        // Filtrar por estado
        if ($status && $status !== 'all') {
            $builder->where('status', $status);
        }

        // Búsqueda
        if ($search) {
            $builder->groupStart()
                   ->like('order_number', $search)
                   ->orLike('customer_name', $search)
                   ->orLike('customer_email', $search)
                   ->orLike('customer_phone', $search)
                   ->groupEnd();
        }

        $orders = $builder->paginate($perPage, 'default', $page);
        $pager = $this->orderModel->pager;

        // Estadísticas
        $stats = $this->getOrderStats();

        $data = [
            'title' => 'Gestión de Pedidos - Admin MrCell',
            'orders' => $orders,
            'pager' => $pager,
            'stats' => $stats,
            'current_status' => $status,
            'search' => $search,
            'statuses' => $this->getOrderStatuses()
        ];

        return view('admin/orders/index', $data);
    }

    /**
     * Ver detalles de un pedido
     */
    public function show($id)
    {
        $order = $this->orderModel->find($id);
        
        if (!$order) {
            return redirect()->to('/admin/orders')->with('error', 'Pedido no encontrado');
        }

        $orderItems = $this->orderItemModel->getOrderItemsWithProducts($id);

        $data = [
            'title' => 'Pedido #' . $order['order_number'] . ' - Admin MrCell',
            'order' => $order,
            'order_items' => $orderItems,
            'statuses' => $this->getOrderStatuses()
        ];

        return view('admin/orders/show', $data);
    }

    /**
     * Actualizar estado del pedido
     */
    public function updateStatus($id)
    {
        $order = $this->orderModel->find($id);
        
        if (!$order) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Pedido no encontrado'
            ]);
        }

        $newStatus = $this->request->getPost('status');
        $notes = $this->request->getPost('notes');
        $trackingNumber = $this->request->getPost('tracking_number');

        $updateData = ['status' => $newStatus];
        
        if ($notes) {
            $updateData['notes'] = $notes;
        }

        if ($trackingNumber) {
            $updateData['tracking_number'] = $trackingNumber;
        }

        // Agregar timestamps específicos según el estado
        switch ($newStatus) {
            case 'shipped':
                $updateData['shipped_at'] = date('Y-m-d H:i:s');
                break;
            case 'delivered':
                $updateData['delivered_at'] = date('Y-m-d H:i:s');
                break;
        }

        if ($this->orderModel->update($id, $updateData)) {
            // Enviar notificación por email (implementar después)
            $this->sendStatusNotification($order, $newStatus);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Estado del pedido actualizado correctamente'
            ]);
        } else {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error al actualizar el estado del pedido'
            ]);
        }
    }

    /**
     * Generar factura PDF
     */
    public function generateInvoice($id)
    {
        $order = $this->orderModel->find($id);
        
        if (!$order) {
            return redirect()->to('/admin/orders')->with('error', 'Pedido no encontrado');
        }

        $orderItems = $this->orderItemModel->getOrderItemsWithProducts($id);

        // Aquí se implementaría la generación de PDF
        // Por ahora retornamos una vista HTML que se puede imprimir
        $data = [
            'title' => 'Factura - Pedido #' . $order['order_number'],
            'order' => $order,
            'order_items' => $orderItems
        ];

        return view('admin/orders/invoice', $data);
    }

    /**
     * Exportar pedidos a CSV
     */
    public function export()
    {
        $status = $this->request->getGet('status');
        $startDate = $this->request->getGet('start_date');
        $endDate = $this->request->getGet('end_date');

        $builder = $this->orderModel->orderBy('created_at', 'DESC');

        if ($status && $status !== 'all') {
            $builder->where('status', $status);
        }

        if ($startDate) {
            $builder->where('created_at >=', $startDate . ' 00:00:00');
        }

        if ($endDate) {
            $builder->where('created_at <=', $endDate . ' 23:59:59');
        }

        $orders = $builder->findAll();

        // Generar CSV
        $filename = 'pedidos_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Encabezados
        fputcsv($output, [
            'Número de Pedido',
            'Cliente',
            'Email',
            'Teléfono',
            'Estado',
            'Total',
            'Método de Pago',
            'Fecha de Creación',
            'Fecha de Envío',
            'Fecha de Entrega'
        ]);

        // Datos
        foreach ($orders as $order) {
            fputcsv($output, [
                $order['order_number'],
                $order['customer_name'],
                $order['customer_email'],
                $order['customer_phone'],
                $this->getStatusLabel($order['status']),
                'Q' . number_format($order['total'], 2),
                $this->getPaymentMethodLabel($order['payment_method']),
                $order['created_at'],
                $order['shipped_at'] ?? '',
                $order['delivered_at'] ?? ''
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Dashboard de estadísticas
     */
    public function dashboard()
    {
        $stats = $this->getOrderStats();
        $recentOrders = $this->orderModel->getRecentOrders(10);
        $topProducts = $this->orderItemModel->getTopSellingProducts(10);
        $salesByMonth = $this->orderModel->getSalesByMonth();

        $data = [
            'title' => 'Dashboard de Pedidos - Admin MrCell',
            'stats' => $stats,
            'recent_orders' => $recentOrders,
            'top_products' => $topProducts,
            'sales_by_month' => $salesByMonth
        ];

        return view('admin/orders/dashboard', $data);
    }

    /**
     * Obtener estadísticas de pedidos
     */
    private function getOrderStats()
    {
        $today = date('Y-m-d');
        $thisMonth = date('Y-m');
        $lastMonth = date('Y-m', strtotime('-1 month'));

        return [
            'total_orders' => $this->orderModel->countAll(),
            'pending_orders' => $this->orderModel->where('status', 'pending')->countAllResults(false),
            'processing_orders' => $this->orderModel->where('status', 'processing')->countAllResults(false),
            'shipped_orders' => $this->orderModel->where('status', 'shipped')->countAllResults(false),
            'delivered_orders' => $this->orderModel->where('status', 'delivered')->countAllResults(false),
            'today_orders' => $this->orderModel->where('DATE(created_at)', $today)->countAllResults(false),
            'this_month_orders' => $this->orderModel->like('created_at', $thisMonth)->countAllResults(false),
            'last_month_orders' => $this->orderModel->like('created_at', $lastMonth)->countAllResults(false),
            'total_revenue' => $this->orderModel->selectSum('total')->where('status !=', 'cancelled')->first()['total'] ?? 0,
            'this_month_revenue' => $this->orderModel->selectSum('total')->like('created_at', $thisMonth)->where('status !=', 'cancelled')->first()['total'] ?? 0
        ];
    }

    /**
     * Obtener estados de pedidos
     */
    private function getOrderStatuses()
    {
        return [
            'pending' => 'Pendiente',
            'confirmed' => 'Confirmado',
            'processing' => 'Procesando',
            'shipped' => 'Enviado',
            'delivered' => 'Entregado',
            'cancelled' => 'Cancelado'
        ];
    }

    /**
     * Obtener etiqueta del estado
     */
    private function getStatusLabel($status)
    {
        $statuses = $this->getOrderStatuses();
        return $statuses[$status] ?? $status;
    }

    /**
     * Obtener etiqueta del método de pago
     */
    private function getPaymentMethodLabel($method)
    {
        $methods = [
            'transfer' => 'Transferencia Bancaria',
            'cash' => 'Pago Contra Entrega',
            'card' => 'Tarjeta de Crédito/Débito',
            'paypal' => 'PayPal'
        ];

        return $methods[$method] ?? $method;
    }

    /**
     * Enviar notificación de cambio de estado
     */
    private function sendStatusNotification($order, $newStatus)
    {
        // Implementar envío de email
        // Por ahora solo registramos en log
        log_message('info', "Pedido {$order['order_number']} cambió a estado: {$newStatus}");
        
        // Aquí se implementaría el envío de email al cliente
        // usando la librería de email de CodeIgniter
    }
}
