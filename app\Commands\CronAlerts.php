<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CronAlerts extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'MrCell';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'cron:alerts';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Ejecuta las alertas programadas del sistema cada 12 horas';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'cron:alerts [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--force' => 'Forzar ejecución aunque no hayan pasado 12 horas',
        '--test'  => 'Modo de prueba - no envía notificaciones reales'
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('🚀 Iniciando sistema de alertas programadas...', 'green');
        CLI::newLine();

        try {
            $cronService = \Config\Services::cronService();
            
            // Verificar opciones
            $force = CLI::getOption('force');
            $testMode = CLI::getOption('test');
            
            if ($force) {
                CLI::write('⚠️  Modo forzado activado - ignorando intervalo de 12 horas', 'yellow');
            }
            
            if ($testMode) {
                CLI::write('🧪 Modo de prueba activado - no se enviarán notificaciones', 'yellow');
            }
            
            CLI::newLine();
            
            // Ejecutar tareas programadas
            if ($force) {
                $this->runForcedTasks($cronService, $testMode);
            } else {
                $cronService->runScheduledTasks();
            }
            
            CLI::write('✅ Alertas programadas completadas exitosamente', 'green');
            
        } catch (\Exception $e) {
            CLI::write('❌ Error ejecutando alertas: ' . $e->getMessage(), 'red');
            CLI::write('📋 Trace: ' . $e->getTraceAsString(), 'red');
            return EXIT_ERROR;
        }
        
        return EXIT_SUCCESS;
    }

    /**
     * Ejecutar tareas forzadas (ignorar intervalo de 12 horas)
     */
    private function runForcedTasks($cronService, $testMode = false)
    {
        $db = \Config\Database::connect();
        
        CLI::write('🔍 Verificando alertas...', 'cyan');
        
        // Verificar productos con bajo stock
        CLI::write('📦 Verificando productos con bajo stock...', 'white');
        $lowStock = $db->query("
            SELECT COUNT(*) as count
            FROM products 
            WHERE stock_quantity <= stock_min 
                AND stock_quantity > 0
                AND is_active = 1 
                AND deleted_at IS NULL
        ")->getRowArray();
        CLI::write("   Encontrados: {$lowStock['count']} productos", 'yellow');
        
        // Verificar productos sin ventas
        CLI::write('📊 Verificando productos sin ventas (>1 mes)...', 'white');
        $noSales = $db->query("
            SELECT COUNT(*) as count
            FROM (
                SELECT p.id, p.created_at,
                       COALESCE(MAX(oi.created_at), p.created_at) as last_sale
                FROM products p
                LEFT JOIN order_items oi ON p.id = oi.product_id
                LEFT JOIN orders o ON oi.order_id = o.id AND o.status != 'cancelled'
                WHERE p.is_active = 1
                    AND p.deleted_at IS NULL
                    AND p.created_at <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                GROUP BY p.id, p.created_at
                HAVING last_sale <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            ) as subquery
        ")->getRowArray();
        CLI::write("   Encontrados: {$noSales['count']} productos", 'yellow');
        
        // Verificar productos próximos a caducar
        CLI::write('⏰ Verificando productos próximos a caducar...', 'white');
        $expiring = $db->query("
            SELECT COUNT(*) as count
            FROM products 
            WHERE has_expiration = 1 
                AND is_active = 1 
                AND deleted_at IS NULL
                AND expiration_date IS NOT NULL
                AND (
                    expiration_date <= CURDATE() 
                    OR DATEDIFF(expiration_date, CURDATE()) <= expiration_alert_days
                )
        ")->getRowArray();
        CLI::write("   Encontrados: {$expiring['count']} productos", 'yellow');
        
        // Verificar pedidos pendientes
        CLI::write('📦 Verificando pedidos pendientes...', 'white');
        $pendingOrders = $db->query("
            SELECT 
                COUNT(CASE WHEN status = 'pending' AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'shipped' AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 END) as shipped
            FROM orders
        ")->getRowArray();
        CLI::write("   Pendientes (>24h): {$pendingOrders['pending']}", 'yellow');
        CLI::write("   Enviados (>3 días): {$pendingOrders['shipped']}", 'yellow');
        
        // Verificar reseñas pendientes
        CLI::write('⭐ Verificando reseñas pendientes...', 'white');
        $tableExists = $db->query("
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
                AND table_name = 'product_reviews'
        ")->getRowArray();
        
        if ($tableExists['count'] > 0) {
            // Verificar estructura de la tabla primero
            $columns = $db->query("SHOW COLUMNS FROM product_reviews")->getResultArray();
            $hasStatusColumn = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'status') {
                    $hasStatusColumn = true;
                    break;
                }
            }

            if ($hasStatusColumn) {
                $pendingReviews = $db->query("
                    SELECT COUNT(*) as count
                    FROM product_reviews
                    WHERE status = 'pending'
                        AND created_at <= DATE_SUB(NOW(), INTERVAL 48 HOUR)
                ")->getRowArray();
                CLI::write("   Encontradas: {$pendingReviews['count']} reseñas", 'yellow');
            } else {
                // Si no hay columna status, contar todas las reseñas recientes
                $allReviews = $db->query("
                    SELECT COUNT(*) as count
                    FROM product_reviews
                    WHERE created_at <= DATE_SUB(NOW(), INTERVAL 48 HOUR)
                ")->getRowArray();
                CLI::write("   Encontradas: {$allReviews['count']} reseñas (sin estado)", 'yellow');
            }
        } else {
            CLI::write("   Tabla de reseñas no existe", 'yellow');
        }
        
        CLI::newLine();
        
        if (!$testMode) {
            CLI::write('📤 Enviando notificaciones...', 'cyan');

            // Crear tabla de cron_executions si no existe
            $db->query("
                CREATE TABLE IF NOT EXISTS cron_executions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    task_name VARCHAR(100) NOT NULL,
                    status ENUM('success', 'error') DEFAULT 'success',
                    message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");

            // Forzar ejecución eliminando registro previo
            $db->query("DELETE FROM cron_executions WHERE task_name = 'scheduled_alerts' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 HOUR)");

            // Ejecutar tareas normalmente
            $cronService->runScheduledTasks();
        } else {
            CLI::write('🧪 Modo de prueba - notificaciones no enviadas', 'yellow');
        }
    }
}
