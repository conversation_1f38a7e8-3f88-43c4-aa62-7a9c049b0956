<?php

if (!function_exists('cache_remember')) {
    /**
     * Cache remember - obtiene del cache o ejecuta callback y guarda resultado
     */
    function cache_remember(string $key, int $ttl, callable $callback)
    {
        $cache = \Config\Services::cache();
        
        $value = $cache->get($key);
        
        if ($value === null) {
            $value = $callback();
            $cache->save($key, $value, $ttl);
        }
        
        return $value;
    }
}

if (!function_exists('cache_forget')) {
    /**
     * Eliminar clave específica del cache
     */
    function cache_forget(string $key): bool
    {
        $cache = \Config\Services::cache();
        return $cache->delete($key);
    }
}

if (!function_exists('cache_flush_pattern')) {
    /**
     * Limpiar cache por patrón
     */
    function cache_flush_pattern(string $pattern): bool
    {
        $cache = \Config\Services::cache();
        return $cache->deleteMatching($pattern);
    }
}

if (!function_exists('cache_tags')) {
    /**
     * Cache con tags para invalidación grupal
     */
    function cache_tags(array $tags, string $key, int $ttl, callable $callback)
    {
        $cache = \Config\Services::cache();
        
        // Crear clave con tags
        $taggedKey = 'tagged_' . implode('_', $tags) . '_' . $key;
        
        $value = $cache->get($taggedKey);
        
        if ($value === null) {
            $value = $callback();
            $cache->save($taggedKey, $value, $ttl);
            
            // Guardar referencia de tags
            foreach ($tags as $tag) {
                $tagKeys = $cache->get("tag_" . $tag) ?: [];
                $tagKeys[] = $taggedKey;
                $cache->save("tag_" . $tag, array_unique($tagKeys), $ttl + 3600);
            }
        }
        
        return $value;
    }
}

if (!function_exists('cache_flush_tag')) {
    /**
     * Limpiar cache por tag
     */
    function cache_flush_tag(string $tag): bool
    {
        $cache = \Config\Services::cache();

        $tagKeys = $cache->get("tag_" . $tag) ?: [];

        foreach ($tagKeys as $key) {
            $cache->delete($key);
        }

        return $cache->delete("tag_" . $tag);
    }
}

if (!function_exists('get_cached_coupons')) {
    /**
     * Obtener cupones disponibles con cache optimizado
     */
    function get_cached_coupons(array $filters = [], int $ttl = 300): array
    {
        $cacheKey = 'coupons_available_' . md5(serialize($filters));
        
        return cache_remember($cacheKey, $ttl, function() use ($filters) {
            $optimizer = new \App\Libraries\QueryOptimizer();
            return $optimizer->getAvailableCoupons($filters);
        });
    }
}

if (!function_exists('get_cached_shipping_rates')) {
    /**
     * Obtener tarifas de envío con cache
     */
    function get_cached_shipping_rates(string $department, float $weight, array $options = [], int $ttl = 3600): array
    {
        $cacheKey = "shipping_rates_" . md5($department . '_' . $weight . '_' . serialize($options));
        
        return cache_remember($cacheKey, $ttl, function() use ($department, $weight, $options) {
            $optimizer = new \App\Libraries\QueryOptimizer();
            return $optimizer->calculateShippingCosts($department, $weight, $options);
        });
    }
}

if (!function_exists('get_cached_tracking_info')) {
    /**
     * Obtener información de seguimiento con cache
     */
    function get_cached_tracking_info(string $trackingNumber, string $company = null, int $ttl = 1800): ?array
    {
        $cacheKey = "tracking_info_" . md5($trackingNumber . '_' . ($company ?? 'null'));
        
        return cache_remember($cacheKey, $ttl, function() use ($trackingNumber, $company) {
            $optimizer = new \App\Libraries\QueryOptimizer();
            return $optimizer->getTrackingInfo($trackingNumber, $company);
        });
    }
}

if (!function_exists('get_cached_inventory_status')) {
    /**
     * Obtener estado de inventario con cache
     */
    function get_cached_inventory_status(array $filters = [], int $ttl = 300): array
    {
        $cacheKey = 'inventory_status_' . md5(serialize($filters));
        
        return cache_remember($cacheKey, $ttl, function() use ($filters) {
            $optimizer = new \App\Libraries\QueryOptimizer();
            return $optimizer->getInventoryStatus($filters);
        });
    }
}

if (!function_exists('get_cached_kpi_metrics')) {
    /**
     * Obtener métricas KPI con cache
     */
    function get_cached_kpi_metrics(string $period = '30 days', array $metrics = [], int $ttl = 600): array
    {
        $cacheKey = "kpi_metrics_" . md5($period . '_' . serialize($metrics));
        
        return cache_remember($cacheKey, $ttl, function() use ($period, $metrics) {
            $optimizer = new \App\Libraries\QueryOptimizer();
            return $optimizer->getKPIMetrics($period, $metrics);
        });
    }
}

if (!function_exists('invalidate_coupon_cache')) {
    /**
     * Invalidar cache relacionado con cupones
     */
    function invalidate_coupon_cache(int $couponId = null): bool
    {
        $patterns = [
            'coupons:available:*',
            'coupon_stats_*'
        ];
        
        if ($couponId) {
            $patterns[] = "coupon:{$couponId}:*";
        }
        
        $success = true;
        foreach ($patterns as $pattern) {
            $success = $success && cache_flush_pattern($pattern);
        }
        
        return $success;
    }
}

if (!function_exists('invalidate_shipping_cache')) {
    /**
     * Invalidar cache relacionado con envíos
     */
    function invalidate_shipping_cache(string $trackingNumber = null): bool
    {
        $patterns = [
            'shipping:rates:*',
            'shipping_costs_*'
        ];
        
        if ($trackingNumber) {
            $patterns[] = "tracking:info:{$trackingNumber}:*";
        } else {
            $patterns[] = 'tracking:*';
        }
        
        $success = true;
        foreach ($patterns as $pattern) {
            $success = $success && cache_flush_pattern($pattern);
        }
        
        return $success;
    }
}

if (!function_exists('invalidate_inventory_cache')) {
    /**
     * Invalidar cache relacionado con inventario
     */
    function invalidate_inventory_cache(int $productId = null, int $warehouseId = null): bool
    {
        $patterns = [
            'inventory:status:*',
            'kpi:metrics:*'
        ];
        
        if ($productId) {
            $patterns[] = "inventory:product:{$productId}:*";
        }
        
        if ($warehouseId) {
            $patterns[] = "inventory:warehouse:{$warehouseId}:*";
        }
        
        $success = true;
        foreach ($patterns as $pattern) {
            $success = $success && cache_flush_pattern($pattern);
        }
        
        return $success;
    }
}

if (!function_exists('warm_cache')) {
    /**
     * Precalentar cache con datos frecuentemente accedidos
     */
    function warm_cache(): bool
    {
        try {
            // Precalentar cupones disponibles
            get_cached_coupons();
            
            // Precalentar tarifas de envío para departamentos principales
            $mainDepartments = ['Guatemala', 'Sacatepéquez', 'Escuintla', 'Chimaltenango'];
            foreach ($mainDepartments as $dept) {
                get_cached_shipping_rates($dept, 1.0); // 1kg promedio
                get_cached_shipping_rates($dept, 5.0); // 5kg promedio
            }
            
            // Precalentar estado de inventario
            get_cached_inventory_status();
            
            // Precalentar KPIs principales
            get_cached_kpi_metrics('7 days', ['sales', 'coupons']);
            get_cached_kpi_metrics('30 days', ['sales', 'shipping']);
            
            return true;
            
        } catch (\Exception $e) {
            log_message('error', 'Error warming cache: ' . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('get_cache_stats')) {
    /**
     * Obtener estadísticas del cache
     */
    function get_cache_stats(): array
    {
        $cache = \Config\Services::cache();
        
        try {
            // Intentar obtener estadísticas del cache
            $info = $cache->getCacheInfo();
            
            return [
                'driver' => get_class($cache),
                'info' => $info,
                'status' => 'active'
            ];
            
        } catch (\Exception $e) {
            return [
                'driver' => get_class($cache),
                'info' => null,
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
}

if (!function_exists('optimize_cache_size')) {
    /**
     * Optimizar tamaño del cache eliminando entradas antiguas
     */
    function optimize_cache_size(int $maxAge = 3600): bool
    {
        try {
            $cache = \Config\Services::cache();
            
            // Limpiar cache antiguo (implementación básica)
            $oldPatterns = [
                'temp:*',
                'session:*',
                'query:*'
            ];
            
            foreach ($oldPatterns as $pattern) {
                cache_flush_pattern($pattern);
            }
            
            return true;
            
        } catch (\Exception $e) {
            log_message('error', 'Error optimizing cache: ' . $e->getMessage());
            return false;
        }
    }
}
