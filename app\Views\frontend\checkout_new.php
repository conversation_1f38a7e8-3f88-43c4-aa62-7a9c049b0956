<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Finalizar Compra - MrCell Guatemala<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Checkout Page -->
<div class="container my-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('carrito') ?>">Carrito</a></li>
            <li class="breadcrumb-item active" aria-current="page">Checkout</li>
        </ol>
    </nav>

    <!-- Page Title -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 mb-0">
                <i class="fas fa-credit-card me-2"></i>
                Finalizar Compra
            </h1>
            <p class="text-muted">Complete su información para procesar el pedido</p>
        </div>
    </div>

    <?php if (empty($cart_items)): ?>
        <!-- Empty Cart -->
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning text-center">
                    <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                    <h4>Su carrito está vacío</h4>
                    <p>Agregue productos a su carrito antes de proceder al checkout.</p>
                    <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                        <i class="fas fa-shopping-bag me-2"></i>Ir a la Tienda
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Checkout Steps -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="checkout-steps d-flex justify-content-center">
                    <div class="step active">
                        <i class="fas fa-user me-2"></i>
                        <span>Información</span>
                    </div>
                    <div class="step">
                        <i class="fas fa-truck me-2"></i>
                        <span>Envío</span>
                    </div>
                    <div class="step">
                        <i class="fas fa-credit-card me-2"></i>
                        <span>Pago</span>
                    </div>
                </div>
            </div>
        </div>

        <form id="checkoutForm" method="POST" action="<?= base_url('checkout/process') ?>">
            <div class="row">
                <!-- Left Column - Forms -->
                <div class="col-lg-8">
                    <!-- Customer Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                Información del Cliente
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">Nombre *</label>
                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">Apellido *</label>
                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Teléfono *</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Dirección de Envío
                            </h5>
                            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#newAddressModal">
                                <i class="fas fa-plus me-1"></i>Nueva Dirección
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Saved Addresses -->
                            <div id="savedAddresses" class="mb-3">
                                <p class="text-muted">Cargando direcciones guardadas...</p>
                            </div>
                            
                            <!-- Manual Address Form -->
                            <div id="manualAddressForm" style="display: none;">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Dirección *</label>
                                    <input type="text" class="form-control" id="address" name="address" placeholder="Calle, número, colonia">
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="city" class="form-label">Ciudad *</label>
                                            <input type="text" class="form-control" id="city" name="city" value="Guatemala">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="state" class="form-label">Departamento *</label>
                                            <select class="form-select" id="state" name="state">
                                                <option value="Guatemala">Guatemala</option>
                                                <option value="Sacatepéquez">Sacatepéquez</option>
                                                <option value="Escuintla">Escuintla</option>
                                                <!-- Add more departments -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="zipCode" class="form-label">Código Postal</label>
                                            <input type="text" class="form-control" id="zipCode" name="zip_code">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="country" class="form-label">País</label>
                                            <input type="text" class="form-control" id="country" name="country" value="Guatemala" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                Método de Pago
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="paymentCash" value="cash" checked>
                                        <label class="form-check-label" for="paymentCash">
                                            <i class="fas fa-money-bill-wave me-2"></i>
                                            Pago Contra Entrega
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="paymentCard" value="card">
                                        <label class="form-check-label" for="paymentCard">
                                            <i class="fas fa-credit-card me-2"></i>
                                            Tarjeta de Crédito/Débito
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Notes -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-sticky-note me-2"></i>
                                Notas del Pedido (Opcional)
                            </h5>
                        </div>
                        <div class="card-body">
                            <textarea class="form-control" id="orderNotes" name="order_notes" rows="3" placeholder="Instrucciones especiales para la entrega..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Order Summary -->
                <div class="col-lg-4">
                    <div class="card sticky-top" style="top: 20px;">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>
                                Resumen del Pedido
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Cart Items -->
                            <div class="order-items mb-3">
                                <?php 
                                $subtotal = 0;
                                foreach ($cart_items as $item): 
                                    $itemTotal = $item['price'] * $item['quantity'];
                                    $subtotal += $itemTotal;
                                ?>
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                        <img src="<?= base_url($item['image'] ?? 'assets/img/products/default.jpg') ?>" 
                                             alt="<?= esc($item['name']) ?>" 
                                             class="rounded me-3" 
                                             style="width: 60px; height: 60px; object-fit: cover;">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?= esc($item['name']) ?></h6>
                                            <small class="text-muted">Cantidad: <?= $item['quantity'] ?></small>
                                        </div>
                                        <div class="text-end">
                                            <strong>Q<?= number_format($itemTotal, 2) ?></strong>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Order Totals -->
                            <div class="order-totals">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span id="subtotal">Q<?= number_format($subtotal, 2) ?></span>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Envío:</span>
                                    <span id="shipping">Q0.00</span>
                                </div>

                                <?php if (($tax_settings['tax_enabled'] ?? false)): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span><?= ($tax_settings['tax_name'] ?? 'IVA') ?> (<?= ($tax_settings['tax_rate'] ?? 12) ?>%):</span>
                                    <span id="tax">Q<?= number_format($subtotal * (($tax_settings['tax_rate'] ?? 0) / 100), 2) ?></span>
                                </div>
                                <?php endif; ?>

                                <hr>
                                <div class="d-flex justify-content-between mb-3">
                                    <strong>Total:</strong>
                                    <strong id="total">Q<?= number_format($subtotal + ($tax_settings['tax_enabled'] ? $subtotal * (($tax_settings['tax_rate'] ?? 0) / 100) : 0), 2) ?></strong>
                                </div>
                            </div>

                            <!-- Place Order Button -->
                            <button type="submit" class="btn btn-primary w-100 btn-lg">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Realizar Pedido
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    <?php endif; ?>
</div>

<!-- New Address Modal -->
<div class="modal fade" id="newAddressModal" tabindex="-1" aria-labelledby="newAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newAddressModalLabel">
                    <i class="fas fa-plus me-2"></i>Agregar Nueva Dirección
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="newAddressForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modalAddressName" class="form-label">Nombre de la Dirección *</label>
                        <input type="text" class="form-control" id="modalAddressName" name="address_name" placeholder="Casa, Oficina, etc." required>
                    </div>
                    <div class="mb-3">
                        <label for="modalAddress" class="form-label">Dirección Completa *</label>
                        <input type="text" class="form-control" id="modalAddress" name="address" placeholder="Calle, número, colonia" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalCity" class="form-label">Ciudad *</label>
                                <input type="text" class="form-control" id="modalCity" name="city" value="Guatemala" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalState" class="form-label">Departamento *</label>
                                <select class="form-select" id="modalState" name="state" required>
                                    <option value="Guatemala">Guatemala</option>
                                    <option value="Sacatepéquez">Sacatepéquez</option>
                                    <option value="Escuintla">Escuintla</option>
                                    <option value="Chimaltenango">Chimaltenango</option>
                                    <option value="El Progreso">El Progreso</option>
                                    <option value="Izabal">Izabal</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Chiquimula">Chiquimula</option>
                                    <option value="Jalapa">Jalapa</option>
                                    <option value="Jutiapa">Jutiapa</option>
                                    <option value="Santa Rosa">Santa Rosa</option>
                                    <option value="Sololá">Sololá</option>
                                    <option value="Totonicapán">Totonicapán</option>
                                    <option value="Quetzaltenango">Quetzaltenango</option>
                                    <option value="Suchitepéquez">Suchitepéquez</option>
                                    <option value="Retalhuleu">Retalhuleu</option>
                                    <option value="San Marcos">San Marcos</option>
                                    <option value="Huehuetenango">Huehuetenango</option>
                                    <option value="Quiché">Quiché</option>
                                    <option value="Baja Verapaz">Baja Verapaz</option>
                                    <option value="Alta Verapaz">Alta Verapaz</option>
                                    <option value="Petén">Petén</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalZipCode" class="form-label">Código Postal</label>
                                <input type="text" class="form-control" id="modalZipCode" name="zip_code">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalCountry" class="form-label">País</label>
                                <input type="text" class="form-control" id="modalCountry" name="country" value="Guatemala" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="modalIsDefault" name="is_default">
                        <label class="form-check-label" for="modalIsDefault">
                            Establecer como dirección predeterminada
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Guardar Dirección
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.checkout-steps {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    background: #f8f9fa;
    border-radius: 25px;
    margin: 0 10px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step.active {
    background: var(--bs-primary);
    color: white;
}

.step.completed {
    background: #28a745;
    color: white;
}

.address-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.address-option:hover {
    border-color: var(--bs-primary);
    background-color: #f8f9fa;
}

.address-option.selected {
    border-color: var(--bs-primary);
    background-color: rgba(13, 110, 253, 0.1);
}

.order-items {
    max-height: 300px;
    overflow-y: auto;
}

.sticky-top {
    position: sticky;
    top: 20px;
    z-index: 1020;
}

@media (max-width: 768px) {
    .checkout-steps {
        flex-direction: column;
        align-items: center;
    }

    .step {
        margin: 5px 0;
        width: 200px;
        justify-content: center;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load saved addresses
    loadSavedAddresses();

    // Handle new address form submission
    document.getElementById('newAddressForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveNewAddress();
    });

    // Handle checkout form submission
    document.getElementById('checkoutForm').addEventListener('submit', function(e) {
        e.preventDefault();
        processCheckout();
    });
});

// Load saved addresses
function loadSavedAddresses() {
    fetch('<?= base_url('api/user/addresses') ?>')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('savedAddresses');

            if (data.success && data.addresses && data.addresses.length > 0) {
                let html = '<h6 class="mb-3">Direcciones Guardadas:</h6>';

                data.addresses.forEach((address, index) => {
                    html += `
                        <div class="address-option" data-address-id="${address.id}" onclick="selectAddress(${address.id})">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="selected_address" id="address_${address.id}" value="${address.id}" ${index === 0 ? 'checked' : ''}>
                                <label class="form-check-label" for="address_${address.id}">
                                    <strong>${address.address_name || 'Dirección'}</strong>
                                    <br>
                                    <small class="text-muted">
                                        ${address.address}, ${address.city}, ${address.state}
                                        ${address.zip_code ? ', ' + address.zip_code : ''}
                                    </small>
                                </label>
                            </div>
                        </div>
                    `;
                });

                html += `
                    <div class="mt-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleManualAddress()">
                            <i class="fas fa-edit me-1"></i>Usar Dirección Diferente
                        </button>
                    </div>
                `;

                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No tiene direcciones guardadas. Complete la información de envío abajo.
                    </div>
                `;
                document.getElementById('manualAddressForm').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading addresses:', error);
            document.getElementById('savedAddresses').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error al cargar direcciones. Complete la información de envío abajo.
                </div>
            `;
            document.getElementById('manualAddressForm').style.display = 'block';
        });
}

// Select address
function selectAddress(addressId) {
    document.querySelectorAll('.address-option').forEach(option => {
        option.classList.remove('selected');
    });

    const selectedOption = document.querySelector(`[data-address-id="${addressId}"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected');
    }

    document.getElementById('manualAddressForm').style.display = 'none';
}

// Toggle manual address form
function toggleManualAddress() {
    const form = document.getElementById('manualAddressForm');
    form.style.display = form.style.display === 'none' ? 'block' : 'none';

    // Uncheck all saved addresses
    document.querySelectorAll('input[name="selected_address"]').forEach(input => {
        input.checked = false;
    });

    document.querySelectorAll('.address-option').forEach(option => {
        option.classList.remove('selected');
    });
}

// Save new address
function saveNewAddress() {
    const formData = new FormData(document.getElementById('newAddressForm'));

    fetch('<?= base_url('api/user/addresses/save') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('newAddressModal'));
            modal.hide();

            // Show success message
            showAlert('success', 'Dirección guardada correctamente');

            // Reload addresses
            loadSavedAddresses();

            // Reset form
            document.getElementById('newAddressForm').reset();
        } else {
            showAlert('error', data.message || 'Error al guardar la dirección');
        }
    })
    .catch(error => {
        console.error('Error saving address:', error);
        showAlert('error', 'Error al guardar la dirección');
    });
}

// Process checkout
function processCheckout() {
    const formData = new FormData(document.getElementById('checkoutForm'));

    // Add selected address if any
    const selectedAddress = document.querySelector('input[name="selected_address"]:checked');
    if (selectedAddress) {
        formData.append('selected_address_id', selectedAddress.value);
    }

    fetch('<?= base_url('checkout/process') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to success page
            window.location.href = data.redirect_url || '<?= base_url('checkout/success') ?>';
        } else {
            showAlert('error', data.message || 'Error al procesar el pedido');
        }
    })
    .catch(error => {
        console.error('Error processing checkout:', error);
        showAlert('error', 'Error al procesar el pedido');
    });
}

// Show alert
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert alert at the top of the container
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
<?= $this->endSection() ?>
