<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestProductCreation extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'MrCell';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:product-creation';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Prueba la creación de productos para identificar el error 500';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'test:product-creation';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('🧪 PROBANDO CREACIÓN DE PRODUCTO...', 'green');
        CLI::newLine();

        try {
            $db = \Config\Database::connect();
            
            // Verificar conexión a la base de datos
            CLI::write('🔗 Verificando conexión a la base de datos...', 'cyan');
            $dbTest = $db->query("SELECT 1 as test")->getRowArray();
            if (!$dbTest) {
                throw new \Exception('No se puede conectar a la base de datos');
            }
            CLI::write('✅ Conexión a la base de datos OK', 'green');
            
            // Obtener categorías disponibles
            CLI::write('📂 Obteniendo categorías...', 'cyan');
            $categories = $db->query("SELECT id, name FROM categories WHERE deleted_at IS NULL LIMIT 5")->getResultArray();
            if (empty($categories)) {
                throw new \Exception('No hay categorías disponibles');
            }
            CLI::write('✅ Categorías encontradas: ' . count($categories), 'green');
            foreach ($categories as $cat) {
                CLI::write("   - ID: {$cat['id']}, Nombre: {$cat['name']}", 'white');
            }
            
            // Obtener marcas disponibles
            CLI::write('🏷️ Obteniendo marcas...', 'cyan');
            $brands = $db->query("SELECT id, name FROM brands WHERE deleted_at IS NULL LIMIT 5")->getResultArray();
            if (empty($brands)) {
                throw new \Exception('No hay marcas disponibles');
            }
            CLI::write('✅ Marcas encontradas: ' . count($brands), 'green');
            foreach ($brands as $brand) {
                CLI::write("   - ID: {$brand['id']}, Nombre: {$brand['name']}", 'white');
            }
            
            // Preparar datos del producto de prueba
            $testSku = 'TEST-CLI-' . date('YmdHis');
            $productData = [
                'uuid' => $this->generateUUID(),
                'sku' => $testSku,
                'name' => 'Producto de Prueba CLI - ' . date('Y-m-d H:i:s'),
                'slug' => 'producto-prueba-cli-' . date('ymdHis'),
                'description' => 'Producto creado desde CLI para probar el error 500',
                'short_description' => 'Descripción corta de prueba',
                'category_id' => $categories[0]['id'],
                'brand_id' => $brands[0]['id'],
                'price_regular' => 100.00,
                'price_sale' => 85.00,
                'price_wholesale' => null,
                'cost_price' => null,
                'currency' => 'GTQ',
                'stock_quantity' => 50,
                'stock_min' => 5,
                'weight' => null,
                'dimensions' => null,
                'dimension_length' => null,
                'dimension_width' => null,
                'dimension_height' => null,
                'dimension_unit' => 'cm',
                'featured_image' => null,
                'gallery_images' => null,
                'is_active' => 1,
                'is_featured' => 0,
                'has_expiration' => 0,
                'expiration_date' => null,
                'expiration_alert_days' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            CLI::write('📝 Datos del producto preparados:', 'cyan');
            CLI::write('   - SKU: ' . $productData['sku'], 'white');
            CLI::write('   - Nombre: ' . $productData['name'], 'white');
            CLI::write('   - Categoría ID: ' . $productData['category_id'], 'white');
            CLI::write('   - Marca ID: ' . $productData['brand_id'], 'white');
            CLI::write('   - Precio: Q' . $productData['price_regular'], 'white');
            
            // Verificar que el SKU no existe
            CLI::write('🔍 Verificando que el SKU no existe...', 'cyan');
            $skuExists = $db->query("SELECT id FROM products WHERE sku = ? AND deleted_at IS NULL", [$testSku])->getRowArray();
            if ($skuExists) {
                throw new \Exception('El SKU ya existe: ' . $testSku);
            }
            CLI::write('✅ SKU disponible', 'green');
            
            // Intentar insertar el producto
            CLI::write('💾 Insertando producto en la base de datos...', 'cyan');
            
            $insertResult = $db->table('products')->insert($productData);
            
            if (!$insertResult) {
                $error = $db->error();
                CLI::write('❌ Error al insertar producto:', 'red');
                CLI::write('   Código: ' . $error['code'], 'red');
                CLI::write('   Mensaje: ' . $error['message'], 'red');
                throw new \Exception('Error de inserción: ' . $error['message']);
            }
            
            $productId = $db->insertID();
            if (!$productId) {
                throw new \Exception('No se pudo obtener el ID del producto insertado');
            }
            
            CLI::write('✅ Producto creado exitosamente!', 'green');
            CLI::write('   ID del producto: ' . $productId, 'green');
            
            // Verificar que el producto se insertó correctamente
            CLI::write('🔍 Verificando producto insertado...', 'cyan');
            $insertedProduct = $db->query("SELECT id, sku, name FROM products WHERE id = ?", [$productId])->getRowArray();
            
            if ($insertedProduct) {
                CLI::write('✅ Producto verificado en la base de datos:', 'green');
                CLI::write('   ID: ' . $insertedProduct['id'], 'white');
                CLI::write('   SKU: ' . $insertedProduct['sku'], 'white');
                CLI::write('   Nombre: ' . $insertedProduct['name'], 'white');
            } else {
                CLI::write('❌ No se pudo verificar el producto en la base de datos', 'red');
            }
            
            CLI::newLine();
            CLI::write('🎉 PRUEBA COMPLETADA EXITOSAMENTE', 'green');
            CLI::write('El problema del error 500 NO está en la inserción básica de productos.', 'yellow');
            CLI::write('El error podría estar en:', 'yellow');
            CLI::write('  - Validación de archivos de imagen', 'white');
            CLI::write('  - Sincronización con servicios externos', 'white');
            CLI::write('  - Manejo de variantes de productos', 'white');
            CLI::write('  - Redirección después de la creación', 'white');
            
        } catch (\Exception $e) {
            CLI::write('❌ ERROR DURANTE LA PRUEBA:', 'red');
            CLI::write('   ' . $e->getMessage(), 'red');
            CLI::write('   Archivo: ' . $e->getFile() . ':' . $e->getLine(), 'red');
            
            // Mostrar información adicional de la base de datos
            try {
                $db = \Config\Database::connect();
                $dbError = $db->error();
                if (!empty($dbError['message'])) {
                    CLI::write('   Error de BD: ' . $dbError['message'], 'red');
                }
            } catch (\Exception $dbE) {
                CLI::write('   No se pudo obtener información adicional de la BD', 'red');
            }
            
            return EXIT_ERROR;
        }
        
        return EXIT_SUCCESS;
    }
    
    /**
     * Generar UUID único
     */
    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
