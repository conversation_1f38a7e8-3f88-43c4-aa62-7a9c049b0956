<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .address-card {
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .address-card:hover {
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.1);
        border-color: var(--primary-light);
    }

    .address-card.default {
        border-color: var(--primary-color);
        background: rgba(220, 38, 38, 0.02);
    }

    .default-badge {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: bold;
        z-index: 5;
    }

    .address-card.default .btn-group-vertical {
        margin-top: 2rem;
    }

    /* Estilos para los modales */
    #deleteAddressModal .modal-content,
    #setDefaultModal .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    #deleteAddressModal .modal-header,
    #setDefaultModal .modal-header {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 15px 15px 0 0;
    }

    #deleteAddressModal .text-danger {
        color: #dc3545 !important;
        text-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
    }

    #setDefaultModal .text-warning {
        color: #ffc107 !important;
        text-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
    }

    #deleteAddressModal .btn,
    #setDefaultModal .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #deleteAddressModal .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    }

    #setDefaultModal .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }

    #deleteAddressModal .btn-secondary:hover,
    #setDefaultModal .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    }

    .empty-addresses {
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
    }

    .empty-addresses i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .add-address-form {
        display: none;
        background: var(--gray-50);
        border-radius: 15px;
        padding: 2rem;
        margin-top: 1rem;
        border: 1px solid var(--gray-200);
    }

    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Mis Direcciones</h1>
                <p class="mb-0 mt-2 opacity-75">Gestiona tus direcciones de envío</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
                    <li class="breadcrumb-item active">Direcciones</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= $user['first_name'] ?? 'Usuario' ?> <?= $user['last_name'] ?? '' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>" class="active"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <div class="dashboard-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Mis Direcciones</h2>
                    <button class="btn btn-primary" onclick="toggleAddressForm()">
                        <i class="fas fa-plus me-2"></i>Agregar Dirección
                    </button>
                </div>

                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Add Address Form -->
                <div id="addAddressForm" class="add-address-form">
                    <h5 class="mb-3">Nueva Dirección</h5>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('cuenta/direcciones') ?>" method="POST" id="addressForm">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nombre de la dirección *</label>
                                <input type="text" class="form-control" name="address_name"
                                       value="<?= old('address_name') ?>"
                                       placeholder="Casa, Oficina, etc." required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nombre completo *</label>
                                <input type="text" class="form-control" name="recipient_name"
                                       value="<?= old('recipient_name') ?>"
                                       placeholder="Nombre del destinatario" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Teléfono *</label>
                                <input type="tel" class="form-control" name="phone"
                                       value="<?= old('phone') ?>"
                                       placeholder="+502 1234-5678" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Departamento *</label>
                                <select class="form-select" name="department" required>
                                    <option value="">Seleccionar departamento</option>
                                    <option value="Guatemala" <?= old('department') === 'Guatemala' ? 'selected' : '' ?>>Guatemala</option>
                                    <option value="Sacatepéquez" <?= old('department') === 'Sacatepéquez' ? 'selected' : '' ?>>Sacatepéquez</option>
                                    <option value="Chimaltenango" <?= old('department') === 'Chimaltenango' ? 'selected' : '' ?>>Chimaltenango</option>
                                    <option value="Escuintla" <?= old('department') === 'Escuintla' ? 'selected' : '' ?>>Escuintla</option>
                                    <option value="Santa Rosa" <?= old('department') === 'Santa Rosa' ? 'selected' : '' ?>>Santa Rosa</option>
                                    <option value="Sololá" <?= old('department') === 'Sololá' ? 'selected' : '' ?>>Sololá</option>
                                    <option value="Totonicapán" <?= old('department') === 'Totonicapán' ? 'selected' : '' ?>>Totonicapán</option>
                                    <option value="Quetzaltenango" <?= old('department') === 'Quetzaltenango' ? 'selected' : '' ?>>Quetzaltenango</option>
                                    <option value="Suchitepéquez" <?= old('department') === 'Suchitepéquez' ? 'selected' : '' ?>>Suchitepéquez</option>
                                    <option value="Retalhuleu" <?= old('department') === 'Retalhuleu' ? 'selected' : '' ?>>Retalhuleu</option>
                                    <option value="San Marcos" <?= old('department') === 'San Marcos' ? 'selected' : '' ?>>San Marcos</option>
                                    <option value="Huehuetenango" <?= old('department') === 'Huehuetenango' ? 'selected' : '' ?>>Huehuetenango</option>
                                    <option value="Quiché" <?= old('department') === 'Quiché' ? 'selected' : '' ?>>Quiché</option>
                                    <option value="Baja Verapaz" <?= old('department') === 'Baja Verapaz' ? 'selected' : '' ?>>Baja Verapaz</option>
                                    <option value="Alta Verapaz" <?= old('department') === 'Alta Verapaz' ? 'selected' : '' ?>>Alta Verapaz</option>
                                    <option value="Petén" <?= old('department') === 'Petén' ? 'selected' : '' ?>>Petén</option>
                                    <option value="Izabal" <?= old('department') === 'Izabal' ? 'selected' : '' ?>>Izabal</option>
                                    <option value="Zacapa" <?= old('department') === 'Zacapa' ? 'selected' : '' ?>>Zacapa</option>
                                    <option value="Chiquimula" <?= old('department') === 'Chiquimula' ? 'selected' : '' ?>>Chiquimula</option>
                                    <option value="Jalapa" <?= old('department') === 'Jalapa' ? 'selected' : '' ?>>Jalapa</option>
                                    <option value="Jutiapa" <?= old('department') === 'Jutiapa' ? 'selected' : '' ?>>Jutiapa</option>
                                    <option value="El Progreso" <?= old('department') === 'El Progreso' ? 'selected' : '' ?>>El Progreso</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Municipio *</label>
                                <input type="text" class="form-control" name="municipality"
                                       value="<?= old('municipality') ?>"
                                       placeholder="Municipio" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Código postal *</label>
                                <input type="text" class="form-control" name="postal_code"
                                       value="<?= old('postal_code') ?>"
                                       placeholder="01001" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Dirección completa *</label>
                            <textarea class="form-control" name="full_address" rows="3"
                                      placeholder="Calle, número, zona, referencias..." required><?= old('full_address') ?></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_default"
                                       id="defaultAddress" value="1" <?= old('is_default') ? 'checked' : '' ?>>
                                <label class="form-check-label" for="defaultAddress">
                                    Establecer como dirección predeterminada
                                </label>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Guardar Dirección
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="toggleAddressForm()">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </button>
                        </div>
                    </form>
                </div>

                <?php if (empty($addresses)): ?>
                    <div class="empty-addresses">
                        <i class="fas fa-map-marker-alt"></i>
                        <h4>No tienes direcciones guardadas</h4>
                        <p class="mb-4">Agrega una dirección para facilitar tus compras futuras.</p>
                        <button class="btn btn-primary" onclick="toggleAddressForm()">
                            <i class="fas fa-plus me-2"></i>Agregar Primera Dirección
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($addresses as $address): ?>
                        <div class="address-card <?= $address['is_default'] ? 'default' : '' ?>">
                            <?php if ($address['is_default']): ?>
                                <span class="default-badge">Predeterminada</span>
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="mb-2"><?= esc($address['address_name'] ?? 'Dirección') ?></h5>
                                    <p class="mb-1"><strong><?= esc($address['nombre_completo']) ?></strong></p>
                                    <p class="mb-1"><?= esc($address['direccion_linea_1']) ?></p>
                                    <p class="mb-1"><?= esc($address['ciudad']) ?>, <?= esc($address['estado_departamento']) ?></p>
                                    <p class="mb-1">Código postal: <?= esc($address['codigo_postal']) ?></p>
                                    <p class="mb-0 text-muted">
                                        <i class="fas fa-phone me-1"></i><?= esc($address['telefono']) ?>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group-vertical">
                                        <button class="btn btn-outline-primary btn-sm mb-1" onclick="editAddress(<?= $address['id'] ?>)">
                                            <i class="fas fa-edit me-1"></i>Editar
                                        </button>
                                        <?php if (!$address['is_default']): ?>
                                            <button class="btn btn-outline-success btn-sm mb-1" onclick="setDefaultAddress(<?= $address['id'] ?>)">
                                                <i class="fas fa-star me-1"></i>Predeterminada
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteAddress(<?= $address['id'] ?>)">
                                            <i class="fas fa-trash me-1"></i>Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación de Eliminación -->
<div class="modal fade" id="deleteAddressModal" tabindex="-1" aria-labelledby="deleteAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <div class="w-100 text-center">
                    <div class="mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="modal-title fw-bold text-dark" id="deleteAddressModalLabel">
                        ¿Eliminar Dirección?
                    </h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center pt-0">
                <p class="text-muted mb-4">
                    Esta acción eliminará permanentemente la dirección seleccionada.<br>
                    <strong>No podrás deshacer esta acción.</strong>
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-danger px-4" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>Sí, Eliminar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación para Establecer como Predeterminada -->
<div class="modal fade" id="setDefaultModal" tabindex="-1" aria-labelledby="setDefaultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <div class="w-100 text-center">
                    <div class="mb-3">
                        <i class="fas fa-star text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="modal-title fw-bold text-dark" id="setDefaultModalLabel">
                        ¿Establecer como Predeterminada?
                    </h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center pt-0">
                <p class="text-muted mb-4">
                    Esta dirección se establecerá como tu dirección predeterminada.<br>
                    <strong>Se usará automáticamente en tus próximas compras.</strong>
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-success px-4" id="confirmSetDefaultBtn">
                        <i class="fas fa-star me-2"></i>Sí, Establecer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function toggleAddressForm() {
    const form = document.getElementById('addAddressForm');
    if (form.style.display === 'none' || form.style.display === '') {
        form.style.display = 'block';
        // Scroll to form
        form.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        form.style.display = 'none';
        // Clear form
        document.getElementById('addressForm').reset();
    }
}

// Form validation
document.getElementById('addressForm').addEventListener('submit', function(e) {
    const requiredFields = [
        'address_name',
        'recipient_name',
        'phone',
        'department',
        'municipality',
        'postal_code',
        'full_address'
    ];

    let isValid = true;
    let firstInvalidField = null;

    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            if (!firstInvalidField) {
                firstInvalidField = field;
            }
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    // Phone validation
    const phone = document.querySelector('[name="phone"]').value;
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    if (phone && !phoneRegex.test(phone)) {
        document.querySelector('[name="phone"]').classList.add('is-invalid');
        if (!firstInvalidField) {
            firstInvalidField = document.querySelector('[name="phone"]');
        }
        isValid = false;
    }

    if (!isValid) {
        e.preventDefault();
        if (firstInvalidField) {
            firstInvalidField.focus();
        }
        alert('Por favor, completa todos los campos obligatorios correctamente.');
    }
});

// Remove validation classes on input
document.querySelectorAll('#addressForm input, #addressForm select, #addressForm textarea').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});

// Show form if there are validation errors
<?php if (session()->getFlashdata('errors') || old('address_name')): ?>
document.addEventListener('DOMContentLoaded', function() {
    toggleAddressForm();
});
<?php endif; ?>

// Función para editar dirección
function editAddress(addressId) {
    // Redirigir a la página de edición (se puede implementar después)
    window.location.href = '<?= base_url('cuenta/direcciones/edit') ?>/' + addressId;
}

// Variables globales para almacenar IDs
let addressToSetDefault = null;

// Función para establecer dirección como predeterminada
function setDefaultAddress(addressId) {
    // Guardar el ID de la dirección
    addressToSetDefault = addressId;

    // Mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('setDefaultModal'));
    modal.show();
}

// Función para confirmar establecer como predeterminada
function confirmSetDefaultAddress() {
    if (addressToSetDefault) {
        // Crear formulario para enviar POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('cuenta/direcciones/set-default') ?>';

        // Agregar token CSRF
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        // Agregar ID de dirección
        const addressInput = document.createElement('input');
        addressInput.type = 'hidden';
        addressInput.name = 'address_id';
        addressInput.value = addressToSetDefault;
        form.appendChild(addressInput);

        // Enviar formulario
        document.body.appendChild(form);
        form.submit();
    }
}

// Variable global para almacenar el ID de la dirección a eliminar
let addressToDelete = null;

// Función para eliminar dirección
function deleteAddress(addressId) {
    // Guardar el ID de la dirección
    addressToDelete = addressId;

    // Mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('deleteAddressModal'));
    modal.show();
}

// Función para confirmar la eliminación
function confirmDeleteAddress() {
    if (addressToDelete) {
        // Crear formulario para enviar POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('cuenta/direcciones/delete') ?>';

        // Agregar token CSRF
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        // Agregar ID de dirección
        const addressInput = document.createElement('input');
        addressInput.type = 'hidden';
        addressInput.name = 'address_id';
        addressInput.value = addressToDelete;
        form.appendChild(addressInput);

        // Enviar formulario
        document.body.appendChild(form);
        form.submit();
    }
}

// Event listeners para los botones de confirmación
document.addEventListener('DOMContentLoaded', function() {
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', confirmDeleteAddress);
    }

    const confirmSetDefaultBtn = document.getElementById('confirmSetDefaultBtn');
    if (confirmSetDefaultBtn) {
        confirmSetDefaultBtn.addEventListener('click', confirmSetDefaultAddress);
    }
});
</script>
<?= $this->endSection() ?>
