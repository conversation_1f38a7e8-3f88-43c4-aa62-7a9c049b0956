<?php

namespace App\Controllers;

use App\Controllers\BaseController;

/**
 * Controlador de Configuración Inicial
 * Configura automáticamente el sistema después de la instalación
 */
class SetupController extends BaseController
{
    /**
     * Página de configuración inicial
     */
    public function index()
    {
        // Verificar si ya está configurado
        if ($this->isSystemConfigured()) {
            return redirect()->to('/admin/automation')->with('info', 'El sistema ya está configurado');
        }
        
        $data = [
            'title' => 'Configuración Inicial - MrCell Guatemala',
            'system_status' => $this->getSystemStatus(),
            'required_tables' => $this->getRequiredTables(),
            'configuration_steps' => $this->getConfigurationSteps()
        ];
        
        return view('setup/initial_setup', $data);
    }
    
    /**
     * Ejecutar configuración automática
     */
    public function runSetup()
    {
        try {
            $results = [];
            
            // Paso 1: Verificar tablas
            $results['tables'] = $this->verifyTables();
            
            // Paso 2: Insertar configuraciones por defecto
            $results['settings'] = $this->insertDefaultSettings();
            
            // Paso 3: Crear templates de WhatsApp
            $results['templates'] = $this->createWhatsAppTemplates();
            
            // Paso 4: Configurar preferencias móviles por defecto
            $results['mobile'] = $this->setupMobileDefaults();
            
            // Paso 5: Marcar sistema como configurado
            $results['system'] = $this->markSystemAsConfigured();
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Configuración completada exitosamente',
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Verificar si el sistema está configurado
     */
    private function isSystemConfigured(): bool
    {
        try {
            $db = \Config\Database::connect();
            
            $setting = $db->table('system_settings')
                         ->where('category', 'system')
                         ->where('key', 'configured')
                         ->get()
                         ->getRowArray();
            
            return $setting && $setting['value'] === 'true';
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Obtener estado del sistema
     */
    private function getSystemStatus(): array
    {
        $status = [
            'database' => false,
            'tables' => false,
            'settings' => false,
            'templates' => false
        ];
        
        try {
            $db = \Config\Database::connect();
            
            // Verificar conexión a BD
            $db->query('SELECT 1');
            $status['database'] = true;
            
            // Verificar tablas principales
            $requiredTables = $this->getRequiredTables();
            $existingTables = 0;
            
            foreach ($requiredTables as $table) {
                if ($this->tableExists($db, $table)) {
                    $existingTables++;
                }
            }
            
            $status['tables'] = $existingTables === count($requiredTables);
            
            // Verificar configuraciones
            $settingsCount = $db->table('system_settings')->countAllResults();
            $status['settings'] = $settingsCount > 0;
            
            // Verificar templates
            $templatesCount = $db->table('whatsapp_templates')->countAllResults();
            $status['templates'] = $templatesCount > 0;
            
        } catch (\Exception $e) {
            // Mantener valores por defecto (false)
        }
        
        return $status;
    }
    
    /**
     * Obtener tablas requeridas
     */
    private function getRequiredTables(): array
    {
        return [
            'whatsapp_templates',
            'whatsapp_message_log',
            'price_history',
            'user_preferences',
            'push_subscriptions',
            'automation_log',
            'system_settings',
            'notification_log',
            'pending_analytics_events',
            'system_metrics',
            'system_alerts',
            'social_media_posts',
            'scheduled_social_posts',
            'push_notification_log',
            'mobile_user_preferences'
        ];
    }
    
    /**
     * Obtener pasos de configuración
     */
    private function getConfigurationSteps(): array
    {
        return [
            [
                'step' => 1,
                'title' => 'Verificar Base de Datos',
                'description' => 'Verificar conexión y tablas requeridas',
                'status' => 'pending'
            ],
            [
                'step' => 2,
                'title' => 'Configuraciones del Sistema',
                'description' => 'Insertar configuraciones por defecto',
                'status' => 'pending'
            ],
            [
                'step' => 3,
                'title' => 'Templates de WhatsApp',
                'description' => 'Crear templates de mensajes predefinidos',
                'status' => 'pending'
            ],
            [
                'step' => 4,
                'title' => 'Configuración Móvil',
                'description' => 'Configurar preferencias móviles por defecto',
                'status' => 'pending'
            ],
            [
                'step' => 5,
                'title' => 'Finalizar Configuración',
                'description' => 'Marcar sistema como configurado',
                'status' => 'pending'
            ]
        ];
    }
    
    /**
     * Verificar tablas
     */
    private function verifyTables(): array
    {
        $db = \Config\Database::connect();
        $requiredTables = $this->getRequiredTables();
        $results = [];
        
        foreach ($requiredTables as $table) {
            $exists = $this->tableExists($db, $table);
            $results[$table] = $exists;
        }
        
        return $results;
    }
    
    /**
     * Insertar configuraciones por defecto
     */
    private function insertDefaultSettings(): array
    {
        $db = \Config\Database::connect();
        
        $defaultSettings = [
            // Sistema
            ['system', 'configured', 'false', 'boolean', 'Sistema configurado', 0, 0],
            ['system', 'version', '1.0.0', 'string', 'Versión del sistema', 1, 0],
            ['system', 'installation_date', date('Y-m-d H:i:s'), 'string', 'Fecha de instalación', 1, 0],
            
            // Automatizaciones
            ['automation', 'enabled', 'true', 'boolean', 'Habilitar automatizaciones', 1, 1],
            ['automation', 'max_execution_time', '300', 'integer', 'Tiempo máximo de ejecución (segundos)', 1, 1],
            ['automation', 'batch_size', '100', 'integer', 'Tamaño de lote para procesamiento', 1, 1],
            ['automation', 'retry_attempts', '3', 'integer', 'Intentos de reintento en caso de error', 1, 1],
            
            // Notificaciones
            ['notifications', 'enabled', 'true', 'boolean', 'Habilitar notificaciones', 1, 1],
            ['notifications', 'max_daily_per_user', '5', 'integer', 'Máximo de notificaciones diarias por usuario', 1, 1],
            ['notifications', 'cooldown_minutes', '30', 'integer', 'Tiempo de espera entre notificaciones (minutos)', 1, 1],
            ['notifications', 'whatsapp_enabled', 'true', 'boolean', 'Habilitar notificaciones WhatsApp', 1, 1],
            ['notifications', 'push_enabled', 'true', 'boolean', 'Habilitar notificaciones push', 1, 1],
            
            // Monitoreo
            ['monitoring', 'enabled', 'true', 'boolean', 'Habilitar monitoreo del sistema', 1, 1],
            ['monitoring', 'price_change_threshold', '5.0', 'float', 'Umbral de cambio de precio para alertas (%)', 1, 1],
            ['monitoring', 'stock_alert_threshold', '10', 'integer', 'Umbral de stock bajo para alertas', 1, 1],
            ['monitoring', 'response_time_threshold', '5000', 'integer', 'Umbral de tiempo de respuesta (ms)', 1, 1],
            
            // Backup
            ['backup', 'enabled', 'true', 'boolean', 'Habilitar backups automáticos', 1, 1],
            ['backup', 'retention_days', '30', 'integer', 'Días de retención de backups', 1, 1],
            ['backup', 'compress', 'true', 'boolean', 'Comprimir archivos de backup', 1, 1],
            
            // Analytics
            ['analytics', 'ga4_enabled', 'false', 'boolean', 'Habilitar Google Analytics 4', 1, 1],
            ['analytics', 'track_ecommerce', 'true', 'boolean', 'Trackear eventos de ecommerce', 1, 1],
            ['analytics', 'sync_interval_hours', '2', 'integer', 'Intervalo de sincronización (horas)', 1, 1],
            
            // Redes Sociales
            ['social', 'enabled', 'false', 'boolean', 'Habilitar publicaciones en redes sociales', 1, 1],
            ['social', 'facebook_enabled', 'false', 'boolean', 'Habilitar Facebook', 1, 1],
            ['social', 'instagram_enabled', 'false', 'boolean', 'Habilitar Instagram', 1, 1],
            ['social', 'twitter_enabled', 'false', 'boolean', 'Habilitar Twitter', 1, 1]
        ];
        
        $inserted = 0;
        $errors = [];
        
        foreach ($defaultSettings as $setting) {
            try {
                $existing = $db->table('system_settings')
                              ->where('category', $setting[0])
                              ->where('key', $setting[1])
                              ->get()
                              ->getRowArray();
                
                if (!$existing) {
                    $db->table('system_settings')->insert([
                        'category' => $setting[0],
                        'key' => $setting[1],
                        'value' => $setting[2],
                        'type' => $setting[3],
                        'description' => $setting[4],
                        'is_public' => $setting[5],
                        'is_editable' => $setting[6],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    $inserted++;
                }
            } catch (\Exception $e) {
                $errors[] = $setting[0] . '.' . $setting[1] . ': ' . $e->getMessage();
            }
        }
        
        return [
            'inserted' => $inserted,
            'total' => count($defaultSettings),
            'errors' => $errors
        ];
    }
    
    /**
     * Crear templates de WhatsApp
     */
    private function createWhatsAppTemplates(): array
    {
        $db = \Config\Database::connect();
        
        $templates = [
            [
                'name' => 'Alerta de Precio',
                'type' => 'price_alert',
                'message' => '🔥 ¡Alerta de Precio!\n\n📱 {product_name}\n💰 Precio anterior: Q{old_price}\n💸 Precio actual: Q{new_price}\n📉 Ahorro: Q{savings} ({percentage}%)\n\n🛒 Ver producto: {product_url}',
                'variables' => json_encode(['product_name', 'old_price', 'new_price', 'savings', 'percentage', 'product_url'])
            ],
            [
                'name' => 'Stock Disponible',
                'type' => 'stock_alert',
                'message' => '📦 ¡Producto Disponible!\n\n📱 {product_name}\n✅ Ya está en stock\n💰 Precio: Q{price}\n\n🛒 Comprar ahora: {product_url}\n\n⚡ ¡No te quedes sin el tuyo!',
                'variables' => json_encode(['product_name', 'price', 'product_url'])
            ],
            [
                'name' => 'Actualización de Pedido',
                'type' => 'order_update',
                'message' => '📋 Actualización de Pedido\n\n🆔 Pedido: #{order_id}\n📊 Estado: {status}\n📅 Fecha: {date}\n\n{additional_info}\n\n📞 Dudas: {contact_phone}',
                'variables' => json_encode(['order_id', 'status', 'date', 'additional_info', 'contact_phone'])
            ],
            [
                'name' => 'Promoción Especial',
                'type' => 'promotion',
                'message' => '🎉 ¡Oferta Especial!\n\n{promotion_title}\n\n📱 {product_name}\n💸 Precio especial: Q{special_price}\n⏰ Válido hasta: {expiry_date}\n\n🛒 Aprovechar oferta: {product_url}',
                'variables' => json_encode(['promotion_title', 'product_name', 'special_price', 'expiry_date', 'product_url'])
            ],
            [
                'name' => 'Bienvenida',
                'type' => 'welcome',
                'message' => '👋 ¡Bienvenido a MrCell Guatemala!\n\n🎉 Gracias por registrarte, {user_name}\n\n📱 Encuentra los mejores celulares y accesorios\n💰 Precios increíbles\n🚚 Envío a todo Guatemala\n\n🛒 Explorar productos: {store_url}',
                'variables' => json_encode(['user_name', 'store_url'])
            ],
            [
                'name' => 'Recordatorio Wishlist',
                'type' => 'general',
                'message' => '❤️ Recordatorio de Wishlist\n\n¡Hola {user_name}!\n\nTienes {items_count} productos en tu lista de deseos:\n{wishlist_items}\n\n🛒 Ver wishlist: {wishlist_url}\n\n💡 ¡No olvides revisar si hay ofertas!',
                'variables' => json_encode(['user_name', 'items_count', 'wishlist_items', 'wishlist_url'])
            ]
        ];
        
        $inserted = 0;
        $errors = [];
        
        foreach ($templates as $template) {
            try {
                $existing = $db->table('whatsapp_templates')
                              ->where('name', $template['name'])
                              ->get()
                              ->getRowArray();
                
                if (!$existing) {
                    $db->table('whatsapp_templates')->insert([
                        'name' => $template['name'],
                        'type' => $template['type'],
                        'message' => $template['message'],
                        'variables' => $template['variables'],
                        'is_active' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    $inserted++;
                }
            } catch (\Exception $e) {
                $errors[] = $template['name'] . ': ' . $e->getMessage();
            }
        }
        
        return [
            'inserted' => $inserted,
            'total' => count($templates),
            'errors' => $errors
        ];
    }
    
    /**
     * Configurar preferencias móviles por defecto
     */
    private function setupMobileDefaults(): array
    {
        // Por ahora solo retornamos éxito, las preferencias se crean por usuario
        return [
            'configured' => true,
            'message' => 'Configuración móvil lista para usuarios'
        ];
    }
    
    /**
     * Marcar sistema como configurado
     */
    private function markSystemAsConfigured(): array
    {
        try {
            $db = \Config\Database::connect();
            
            $db->table('system_settings')
               ->where('category', 'system')
               ->where('key', 'configured')
               ->update([
                   'value' => 'true',
                   'updated_at' => date('Y-m-d H:i:s')
               ]);
            
            return [
                'success' => true,
                'message' => 'Sistema marcado como configurado'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verificar si una tabla existe
     */
    private function tableExists($db, string $tableName): bool
    {
        try {
            $result = $db->query("SELECT 1 FROM `{$tableName}` LIMIT 1");
            return $result !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
