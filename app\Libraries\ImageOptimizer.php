<?php

namespace App\Libraries;

/**
 * Optimizador de Imágenes Automático
 * Compatible con cPanel hosting - Solo usa GD
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class ImageOptimizer
{
    private $config;
    
    public function __construct()
    {
        $this->config = [
            'jpeg_quality' => 85,
            'png_compression' => 9,
            'webp_quality' => 80,
            'max_width' => 1920,
            'max_height' => 1080,
            'thumbnail_width' => 300,
            'thumbnail_height' => 300,
            'enable_webp' => function_exists('imagewebp'),
            'enable_progressive' => true,
            'strip_metadata' => true
        ];
    }
    
    /**
     * Optimizar imagen automáticamente al subirla
     * 
     * @param string $imagePath Ruta de la imagen
     * @param array $options Opciones de optimización
     * @return array Resultado de la optimización
     */
    public function optimizeUploadedImage(string $imagePath, array $options = []): array
    {
        try {
            if (!file_exists($imagePath)) {
                return [
                    'success' => false,
                    'error' => 'Archivo no encontrado'
                ];
            }
            
            $originalSize = filesize($imagePath);
            $imageInfo = getimagesize($imagePath);
            
            if (!$imageInfo) {
                return [
                    'success' => false,
                    'error' => 'No es una imagen válida'
                ];
            }
            
            $mimeType = $imageInfo['mime'];
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            
            // Crear imagen desde archivo
            $sourceImage = $this->createImageFromFile($imagePath, $mimeType);
            if (!$sourceImage) {
                return [
                    'success' => false,
                    'error' => 'No se pudo procesar la imagen'
                ];
            }
            
            $results = [
                'success' => true,
                'original_size' => $originalSize,
                'original_dimensions' => [$width, $height],
                'optimizations' => []
            ];
            
            // Redimensionar si es necesario
            $resized = false;
            if ($width > $this->config['max_width'] || $height > $this->config['max_height']) {
                $sourceImage = $this->resizeImage($sourceImage, $width, $height, $this->config['max_width'], $this->config['max_height']);
                $resized = true;
                $results['optimizations'][] = 'Redimensionada';
            }
            
            // Optimizar imagen principal
            $optimized = $this->saveOptimizedImage($sourceImage, $imagePath, $mimeType);
            if ($optimized) {
                $results['optimizations'][] = 'Comprimida';
            }
            
            // Crear versión WebP si está habilitado
            if ($this->config['enable_webp']) {
                $webpPath = $this->createWebPVersion($sourceImage, $imagePath);
                if ($webpPath) {
                    $results['webp_created'] = $webpPath;
                    $results['optimizations'][] = 'WebP creada';
                }
            }
            
            // Crear thumbnail si se solicita
            if (isset($options['create_thumbnail']) && $options['create_thumbnail']) {
                $thumbnailPath = $this->createThumbnail($sourceImage, $imagePath);
                if ($thumbnailPath) {
                    $results['thumbnail_created'] = $thumbnailPath;
                    $results['optimizations'][] = 'Thumbnail creada';
                }
            }
            
            // Crear versiones adicionales si se solicitan
            if (isset($options['sizes']) && is_array($options['sizes'])) {
                $results['additional_sizes'] = [];
                foreach ($options['sizes'] as $sizeName => $dimensions) {
                    $sizedPath = $this->createSizedVersion($sourceImage, $imagePath, $dimensions, $sizeName);
                    if ($sizedPath) {
                        $results['additional_sizes'][$sizeName] = $sizedPath;
                        $results['optimizations'][] = "Versión {$sizeName} creada";
                    }
                }
            }
            
            imagedestroy($sourceImage);
            
            // Calcular ahorros
            $newSize = filesize($imagePath);
            $results['new_size'] = $newSize;
            $results['savings'] = $originalSize - $newSize;
            $results['percentage'] = $originalSize > 0 ? round(($results['savings'] / $originalSize) * 100, 2) : 0;
            
            return $results;
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Crear imagen desde archivo
     */
    private function createImageFromFile(string $imagePath, string $mimeType)
    {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($imagePath);
            case 'image/png':
                return imagecreatefrompng($imagePath);
            case 'image/gif':
                return imagecreatefromgif($imagePath);
            case 'image/webp':
                return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($imagePath) : false;
            default:
                return false;
        }
    }
    
    /**
     * Redimensionar imagen manteniendo proporción
     */
    private function resizeImage($sourceImage, int $originalWidth, int $originalHeight, int $maxWidth, int $maxHeight)
    {
        // Calcular nuevas dimensiones manteniendo proporción
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);
        
        // Crear nueva imagen
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preservar transparencia para PNG y GIF
        imagealphablending($resizedImage, false);
        imagesavealpha($resizedImage, true);
        $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
        imagefilledrectangle($resizedImage, 0, 0, $newWidth, $newHeight, $transparent);
        
        // Redimensionar
        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // Liberar memoria de la imagen original
        imagedestroy($sourceImage);
        
        return $resizedImage;
    }
    
    /**
     * Guardar imagen optimizada
     */
    private function saveOptimizedImage($image, string $imagePath, string $mimeType): bool
    {
        switch ($mimeType) {
            case 'image/jpeg':
                // Habilitar JPEG progresivo si está disponible
                if ($this->config['enable_progressive'] && function_exists('imageinterlace')) {
                    imageinterlace($image, 1);
                }
                return imagejpeg($image, $imagePath, $this->config['jpeg_quality']);
                
            case 'image/png':
                // PNG usa compresión sin pérdida
                return imagepng($image, $imagePath, $this->config['png_compression']);
                
            case 'image/gif':
                return imagegif($image, $imagePath);
                
            case 'image/webp':
                if (function_exists('imagewebp')) {
                    return imagewebp($image, $imagePath, $this->config['webp_quality']);
                }
                return false;
                
            default:
                return false;
        }
    }
    
    /**
     * Crear versión WebP
     */
    private function createWebPVersion($image, string $originalPath): ?string
    {
        if (!function_exists('imagewebp')) {
            return null;
        }
        
        $webpPath = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $originalPath);
        
        if (imagewebp($image, $webpPath, $this->config['webp_quality'])) {
            return $webpPath;
        }
        
        return null;
    }
    
    /**
     * Crear thumbnail
     */
    private function createThumbnail($sourceImage, string $originalPath): ?string
    {
        $pathInfo = pathinfo($originalPath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
        
        $originalWidth = imagesx($sourceImage);
        $originalHeight = imagesy($sourceImage);
        
        // Crear thumbnail cuadrado con crop centrado
        $thumbnailImage = $this->createSquareThumbnail($sourceImage, $originalWidth, $originalHeight, $this->config['thumbnail_width']);
        
        if ($thumbnailImage) {
            $mimeType = 'image/' . strtolower($pathInfo['extension']);
            if ($pathInfo['extension'] === 'jpg') {
                $mimeType = 'image/jpeg';
            }
            
            $saved = $this->saveOptimizedImage($thumbnailImage, $thumbnailPath, $mimeType);
            imagedestroy($thumbnailImage);
            
            return $saved ? $thumbnailPath : null;
        }
        
        return null;
    }
    
    /**
     * Crear thumbnail cuadrado con crop centrado
     */
    private function createSquareThumbnail($sourceImage, int $originalWidth, int $originalHeight, int $size)
    {
        $thumbnailImage = imagecreatetruecolor($size, $size);
        
        // Preservar transparencia
        imagealphablending($thumbnailImage, false);
        imagesavealpha($thumbnailImage, true);
        $transparent = imagecolorallocatealpha($thumbnailImage, 255, 255, 255, 127);
        imagefilledrectangle($thumbnailImage, 0, 0, $size, $size, $transparent);
        
        // Calcular crop centrado
        $cropSize = min($originalWidth, $originalHeight);
        $cropX = ($originalWidth - $cropSize) / 2;
        $cropY = ($originalHeight - $cropSize) / 2;
        
        // Crear thumbnail con crop centrado
        imagecopyresampled($thumbnailImage, $sourceImage, 0, 0, $cropX, $cropY, $size, $size, $cropSize, $cropSize);
        
        return $thumbnailImage;
    }
    
    /**
     * Crear versión de tamaño específico
     */
    private function createSizedVersion($sourceImage, string $originalPath, array $dimensions, string $sizeName): ?string
    {
        $pathInfo = pathinfo($originalPath);
        $sizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$sizeName}." . $pathInfo['extension'];
        
        $originalWidth = imagesx($sourceImage);
        $originalHeight = imagesy($sourceImage);
        
        $targetWidth = $dimensions['width'] ?? $dimensions[0];
        $targetHeight = $dimensions['height'] ?? $dimensions[1];
        
        $sizedImage = $this->resizeImage($sourceImage, $originalWidth, $originalHeight, $targetWidth, $targetHeight);
        
        if ($sizedImage) {
            $mimeType = 'image/' . strtolower($pathInfo['extension']);
            if ($pathInfo['extension'] === 'jpg') {
                $mimeType = 'image/jpeg';
            }
            
            $saved = $this->saveOptimizedImage($sizedImage, $sizedPath, $mimeType);
            imagedestroy($sizedImage);
            
            return $saved ? $sizedPath : null;
        }
        
        return null;
    }
    
    /**
     * Optimizar imagen existente
     */
    public function optimizeExistingImage(string $imagePath): array
    {
        return $this->optimizeUploadedImage($imagePath);
    }
    
    /**
     * Optimizar múltiples imágenes en lote
     */
    public function batchOptimize(array $imagePaths): array
    {
        $results = [];
        
        foreach ($imagePaths as $imagePath) {
            $results[$imagePath] = $this->optimizeUploadedImage($imagePath);
        }
        
        return $results;
    }
    
    /**
     * Obtener información de imagen
     */
    public function getImageInfo(string $imagePath): array
    {
        if (!file_exists($imagePath)) {
            return ['error' => 'Archivo no encontrado'];
        }
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return ['error' => 'No es una imagen válida'];
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'file_size' => filesize($imagePath),
            'file_size_formatted' => $this->formatBytes(filesize($imagePath))
        ];
    }
    
    /**
     * Verificar si una imagen necesita optimización
     */
    public function needsOptimization(string $imagePath): bool
    {
        $info = $this->getImageInfo($imagePath);
        
        if (isset($info['error'])) {
            return false;
        }
        
        // Necesita optimización si:
        // - Es muy grande (más de 2MB)
        // - Dimensiones muy grandes
        // - No tiene versión WebP
        
        $needsOptimization = false;
        
        if ($info['file_size'] > 2097152) { // 2MB
            $needsOptimization = true;
        }
        
        if ($info['width'] > $this->config['max_width'] || $info['height'] > $this->config['max_height']) {
            $needsOptimization = true;
        }
        
        // Verificar si existe versión WebP
        if ($this->config['enable_webp']) {
            $webpPath = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $imagePath);
            if (!file_exists($webpPath)) {
                $needsOptimization = true;
            }
        }
        
        return $needsOptimization;
    }
    
    /**
     * Configurar opciones de optimización
     */
    public function setConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return $this->config;
    }
    
    /**
     * Formatear bytes
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
    
    /**
     * Limpiar versiones antiguas de imágenes
     */
    public function cleanupOldVersions(string $imagePath): array
    {
        $pathInfo = pathinfo($imagePath);
        $pattern = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_*.' . $pathInfo['extension'];
        
        $oldVersions = glob($pattern);
        $deleted = [];
        
        foreach ($oldVersions as $oldVersion) {
            if (unlink($oldVersion)) {
                $deleted[] = basename($oldVersion);
            }
        }
        
        return $deleted;
    }
}
