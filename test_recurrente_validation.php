<?php

/**
 * Script para probar la validación de precios de Recurrente
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "=== PRUEBA DE VALIDACIÓN DE RECURRENTE ===\n";
    echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
    echo "=========================================\n\n";

    // Función para verificar elegibilidad de Recurrente
    function isRecurrenteEligible($amount, $currency = 'GTQ') {
        $maxPriceUSD = 15000; // Límite de Recurrente
        $amountInUSD = $amount;
        
        // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
        if ($currency === 'GTQ') {
            $amountInUSD = $amount / 7.8;
        }
        
        return $amountInUSD <= $maxPriceUSD;
    }

    // Función para obtener mensaje de límite
    function getRecurrentePriceLimitMessage($amount, $currency = 'GTQ') {
        $maxPriceUSD = 15000;
        $maxPriceGTQ = $maxPriceUSD * 7.8;
        
        if ($currency === 'GTQ') {
            return "El total (Q" . number_format($amount, 2) . ") excede el límite de Q" . number_format($maxPriceGTQ, 0) . " para pagos con Recurrente.";
        } else {
            return "El total ($" . number_format($amount, 2) . ") excede el límite de $" . number_format($maxPriceUSD, 0) . " USD para pagos con Recurrente.";
        }
    }

    // Casos de prueba
    $testCases = [
        ['amount' => 500, 'currency' => 'GTQ', 'description' => 'Producto barato en GTQ'],
        ['amount' => 5000, 'currency' => 'GTQ', 'description' => 'Producto medio en GTQ'],
        ['amount' => 50000, 'currency' => 'GTQ', 'description' => 'Producto caro en GTQ'],
        ['amount' => 117000, 'currency' => 'GTQ', 'description' => 'Límite exacto en GTQ (15000 USD)'],
        ['amount' => 120000, 'currency' => 'GTQ', 'description' => 'Sobre el límite en GTQ'],
        ['amount' => 500, 'currency' => 'USD', 'description' => 'Producto barato en USD'],
        ['amount' => 5000, 'currency' => 'USD', 'description' => 'Producto medio en USD'],
        ['amount' => 15000, 'currency' => 'USD', 'description' => 'Límite exacto en USD'],
        ['amount' => 20000, 'currency' => 'USD', 'description' => 'Sobre el límite en USD'],
    ];

    echo "🧪 CASOS DE PRUEBA:\n";
    echo "==================\n\n";

    foreach ($testCases as $test) {
        $eligible = isRecurrenteEligible($test['amount'], $test['currency']);
        $status = $eligible ? '✅ ELEGIBLE' : '❌ NO ELEGIBLE';
        
        echo "📋 {$test['description']}\n";
        echo "   Monto: {$test['currency']} " . number_format($test['amount'], 2) . "\n";
        echo "   Estado: {$status}\n";
        
        if (!$eligible) {
            echo "   Razón: " . getRecurrentePriceLimitMessage($test['amount'], $test['currency']) . "\n";
        }
        
        echo "\n";
    }

    // Probar con productos reales de la base de datos
    echo "🛍️  PRODUCTOS REALES DE LA BASE DE DATOS:\n";
    echo "========================================\n\n";

    $stmt = $db->prepare("
        SELECT 
            id, name, sku, 
            COALESCE(price_sale, price_regular) as price,
            currency,
            recurrente_sync_status
        FROM products 
        WHERE is_active = 1 
        AND deleted_at IS NULL
        ORDER BY price DESC
        LIMIT 10
    ");
    
    $stmt->execute();
    $products = $stmt->fetchAll();

    foreach ($products as $product) {
        $price = $product['price'];
        $currency = $product['currency'] ?? 'GTQ';
        $eligible = isRecurrenteEligible($price, $currency);
        $status = $eligible ? '✅ ELEGIBLE' : '❌ NO ELEGIBLE';
        
        echo "🛒 {$product['name']} (ID: {$product['id']})\n";
        echo "   SKU: {$product['sku']}\n";
        echo "   Precio: {$currency} " . number_format($price, 2) . "\n";
        echo "   Estado Sync: " . ($product['recurrente_sync_status'] ?? 'NULL') . "\n";
        echo "   Recurrente: {$status}\n";
        
        if (!$eligible) {
            echo "   Razón: " . getRecurrentePriceLimitMessage($price, $currency) . "\n";
        }
        
        echo "\n";
    }

    // Estadísticas generales
    echo "📊 ESTADÍSTICAS GENERALES:\n";
    echo "=========================\n\n";

    $stats = $db->query("
        SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN recurrente_sync_status = 'synced' THEN 1 END) as synced_products,
            COUNT(CASE WHEN recurrente_sync_status = 'disabled' THEN 1 END) as disabled_products,
            COUNT(CASE WHEN recurrente_sync_status = 'error' THEN 1 END) as error_products,
            AVG(COALESCE(price_sale, price_regular)) as avg_price,
            MAX(COALESCE(price_sale, price_regular)) as max_price,
            MIN(COALESCE(price_sale, price_regular)) as min_price
        FROM products 
        WHERE is_active = 1 
        AND deleted_at IS NULL
    ")->fetch();

    echo "Total de productos activos: {$stats['total_products']}\n";
    echo "Productos sincronizados con Recurrente: {$stats['synced_products']}\n";
    echo "Productos deshabilitados (precio alto): {$stats['disabled_products']}\n";
    echo "Productos con error: {$stats['error_products']}\n";
    echo "Precio promedio: Q" . number_format($stats['avg_price'], 2) . "\n";
    echo "Precio máximo: Q" . number_format($stats['max_price'], 2) . "\n";
    echo "Precio mínimo: Q" . number_format($stats['min_price'], 2) . "\n";

    // Calcular cuántos productos son elegibles para Recurrente
    $eligibleCount = 0;
    $notEligibleCount = 0;

    $allProducts = $db->query("
        SELECT COALESCE(price_sale, price_regular) as price, currency
        FROM products 
        WHERE is_active = 1 
        AND deleted_at IS NULL
    ")->fetchAll();

    foreach ($allProducts as $product) {
        $price = $product['price'];
        $currency = $product['currency'] ?? 'GTQ';
        
        if (isRecurrenteEligible($price, $currency)) {
            $eligibleCount++;
        } else {
            $notEligibleCount++;
        }
    }

    echo "\n🎯 ELEGIBILIDAD PARA RECURRENTE:\n";
    echo "===============================\n";
    echo "Productos elegibles: {$eligibleCount} (" . round(($eligibleCount / count($allProducts)) * 100, 1) . "%)\n";
    echo "Productos no elegibles: {$notEligibleCount} (" . round(($notEligibleCount / count($allProducts)) * 100, 1) . "%)\n";

    echo "\n✅ Prueba completada exitosamente.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
