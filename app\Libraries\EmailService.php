<?php

namespace App\Libraries;

/**
 * Servicio de Email Avanzado
 * Sistema completo de envío de emails con templates y configuración SMTP
 */
class EmailService
{
    private $config;
    private $mailer;
    
    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'host' => env('SMTP_HOST', 'smtp.gmail.com'),
            'port' => env('SMTP_PORT', 587),
            'username' => env('SMTP_USER', ''),
            'password' => env('SMTP_PASS', ''),
            'encryption' => env('SMTP_ENCRYPTION', 'tls'),
            'from_email' => env('FROM_EMAIL', '<EMAIL>'),
            'from_name' => env('FROM_NAME', 'MrCell Guatemala'),
            'timeout' => 30,
            'debug' => env('EMAIL_DEBUG', false)
        ], $config);
        
        $this->initializeMailer();
    }
    
    /**
     * Enviar email simple
     */
    public function send(string $to, string $subject, string $body, ?string $toName = null): bool
    {
        try {
            if (!$this->isConfigured()) {
                throw new \Exception('Email service not properly configured');
            }
            
            $email = \Config\Services::email();
            
            $email->setFrom($this->config['from_email'], $this->config['from_name']);
            $email->setTo($to, $toName);
            $email->setSubject($subject);
            $email->setMessage($body);
            $email->setMailType('html');
            
            $result = $email->send();
            
            if (!$result && $this->config['debug']) {
                error_log('Email send failed: ' . $email->printDebugger());
            }
            
            return $result;
            
        } catch (\Exception $e) {
            if ($this->config['debug']) {
                error_log('Email service error: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Enviar email con template
     */
    public function sendTemplate(string $to, string $template, array $data = [], ?string $toName = null): bool
    {
        try {
            $templateContent = $this->loadTemplate($template, $data);
            
            if (!$templateContent) {
                throw new \Exception("Template '$template' not found or invalid");
            }
            
            return $this->send($to, $templateContent['subject'], $templateContent['body'], $toName);
            
        } catch (\Exception $e) {
            if ($this->config['debug']) {
                error_log('Email template error: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Enviar email de bienvenida
     */
    public function sendWelcomeEmail(string $to, string $userName): bool
    {
        $data = [
            'user_name' => $userName,
            'site_url' => base_url(),
            'login_url' => base_url('login'),
            'support_email' => $this->config['from_email']
        ];
        
        return $this->sendTemplate($to, 'welcome', $data, $userName);
    }
    
    /**
     * Enviar email de recuperación de contraseña
     */
    public function sendPasswordResetEmail(string $to, string $userName, string $resetToken): bool
    {
        $data = [
            'user_name' => $userName,
            'reset_url' => base_url("reset-password?token=$resetToken"),
            'site_url' => base_url(),
            'support_email' => $this->config['from_email']
        ];
        
        return $this->sendTemplate($to, 'password_reset', $data, $userName);
    }
    
    /**
     * Enviar email de confirmación de pedido
     */
    public function sendOrderConfirmation(string $to, array $orderData): bool
    {
        $data = array_merge($orderData, [
            'site_url' => base_url(),
            'order_url' => base_url("orders/{$orderData['order_id']}"),
            'support_email' => $this->config['from_email']
        ]);
        
        return $this->sendTemplate($to, 'order_confirmation', $data, $orderData['customer_name'] ?? null);
    }
    
    /**
     * Enviar email de actualización de pedido
     */
    public function sendOrderUpdate(string $to, array $orderData): bool
    {
        $data = array_merge($orderData, [
            'site_url' => base_url(),
            'order_url' => base_url("orders/{$orderData['order_id']}"),
            'support_email' => $this->config['from_email']
        ]);
        
        return $this->sendTemplate($to, 'order_update', $data, $orderData['customer_name'] ?? null);
    }
    
    /**
     * Enviar email de newsletter
     */
    public function sendNewsletter(string $to, string $subject, string $content, ?string $toName = null): bool
    {
        $data = [
            'content' => $content,
            'subject' => $subject,
            'site_url' => base_url(),
            'unsubscribe_url' => base_url("unsubscribe?email=" . urlencode($to)),
            'user_name' => $toName ?? 'Cliente'
        ];
        
        $templateContent = $this->loadTemplate('newsletter', $data);
        
        if (!$templateContent) {
            // Fallback a email simple si no hay template
            return $this->send($to, $subject, $content, $toName);
        }
        
        return $this->send($to, $subject, $templateContent['body'], $toName);
    }
    
    /**
     * Enviar email masivo
     */
    public function sendBulkEmail(array $recipients, string $subject, string $body): array
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($recipients as $recipient) {
            $email = is_array($recipient) ? $recipient['email'] : $recipient;
            $name = is_array($recipient) ? ($recipient['name'] ?? null) : null;
            
            if ($this->send($email, $subject, $body, $name)) {
                $results['sent']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Failed to send to: $email";
            }
            
            // Pequeña pausa para evitar spam
            usleep(100000); // 0.1 segundos
        }
        
        return $results;
    }
    
    /**
     * Cargar template de email
     */
    private function loadTemplate(string $template, array $data = []): ?array
    {
        $templatePath = APPPATH . "Views/emails/$template.php";
        
        if (!file_exists($templatePath)) {
            return $this->getDefaultTemplate($template, $data);
        }
        
        try {
            ob_start();
            extract($data);
            include $templatePath;
            $body = ob_get_clean();
            
            // Extraer subject del template si existe
            $subject = $data['subject'] ?? $this->getDefaultSubject($template);
            
            return [
                'subject' => $subject,
                'body' => $body
            ];
            
        } catch (\Exception $e) {
            ob_end_clean();
            return null;
        }
    }
    
    /**
     * Obtener template por defecto
     */
    private function getDefaultTemplate(string $template, array $data): ?array
    {
        $templates = [
            'welcome' => [
                'subject' => '¡Bienvenido a MrCell Guatemala!',
                'body' => $this->getWelcomeTemplate($data)
            ],
            'password_reset' => [
                'subject' => 'Recuperación de contraseña - MrCell Guatemala',
                'body' => $this->getPasswordResetTemplate($data)
            ],
            'order_confirmation' => [
                'subject' => 'Confirmación de pedido #' . ($data['order_id'] ?? ''),
                'body' => $this->getOrderConfirmationTemplate($data)
            ],
            'order_update' => [
                'subject' => 'Actualización de pedido #' . ($data['order_id'] ?? ''),
                'body' => $this->getOrderUpdateTemplate($data)
            ],
            'newsletter' => [
                'subject' => $data['subject'] ?? 'Newsletter - MrCell Guatemala',
                'body' => $this->getNewsletterTemplate($data)
            ]
        ];
        
        return $templates[$template] ?? null;
    }
    
    /**
     * Template de bienvenida
     */
    private function getWelcomeTemplate(array $data): string
    {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #007bff; text-align: center;'>¡Bienvenido a MrCell Guatemala!</h1>
                
                <p>Hola <strong>{$data['user_name']}</strong>,</p>
                
                <p>¡Gracias por registrarte en MrCell Guatemala! Estamos emocionados de tenerte como parte de nuestra comunidad.</p>
                
                <p>En MrCell Guatemala encontrarás:</p>
                <ul>
                    <li>📱 Los últimos modelos de smartphones</li>
                    <li>🔧 Accesorios de alta calidad</li>
                    <li>💰 Los mejores precios de Guatemala</li>
                    <li>🚚 Envío rápido y seguro</li>
                    <li>🛡️ Garantía en todos nuestros productos</li>
                </ul>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$data['site_url']}' style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Explorar Productos</a>
                </div>
                
                <p>Si tienes alguna pregunta, no dudes en contactarnos en <a href='mailto:{$data['support_email']}'>{$data['support_email']}</a></p>
                
                <p>¡Bienvenido a la familia MrCell!</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    MrCell Guatemala - La tienda de tecnología más avanzada de Guatemala<br>
                    <a href='{$data['site_url']}'>{$data['site_url']}</a>
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de recuperación de contraseña
     */
    private function getPasswordResetTemplate(array $data): string
    {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #007bff; text-align: center;'>Recuperación de Contraseña</h1>
                
                <p>Hola <strong>{$data['user_name']}</strong>,</p>
                
                <p>Hemos recibido una solicitud para restablecer la contraseña de tu cuenta en MrCell Guatemala.</p>
                
                <p>Si solicitaste este cambio, haz clic en el siguiente botón para crear una nueva contraseña:</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$data['reset_url']}' style='background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Restablecer Contraseña</a>
                </div>
                
                <p><strong>Este enlace expirará en 24 horas por seguridad.</strong></p>
                
                <p>Si no solicitaste este cambio, puedes ignorar este email. Tu contraseña permanecerá sin cambios.</p>
                
                <p>Por tu seguridad, nunca compartas este enlace con nadie.</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    MrCell Guatemala - Seguridad y confianza<br>
                    <a href='{$data['site_url']}'>{$data['site_url']}</a>
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de confirmación de pedido
     */
    private function getOrderConfirmationTemplate(array $data): string
    {
        $itemsHtml = '';
        if (isset($data['items'])) {
            foreach ($data['items'] as $item) {
                $itemsHtml .= "<tr>
                    <td>{$item['name']}</td>
                    <td style='text-align: center;'>{$item['quantity']}</td>
                    <td style='text-align: right;'>Q" . number_format($item['price'], 2) . "</td>
                </tr>";
            }
        }
        
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #28a745; text-align: center;'>¡Pedido Confirmado!</h1>
                
                <p>Hola <strong>{$data['customer_name']}</strong>,</p>
                
                <p>¡Gracias por tu pedido! Hemos recibido tu orden y la estamos procesando.</p>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    <h3 style='margin-top: 0;'>Detalles del Pedido</h3>
                    <p><strong>Número de pedido:</strong> #{$data['order_id']}</p>
                    <p><strong>Fecha:</strong> {$data['order_date']}</p>
                    <p><strong>Estado:</strong> {$data['status']}</p>
                </div>
                
                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                    <thead>
                        <tr style='background: #007bff; color: white;'>
                            <th style='padding: 10px; text-align: left;'>Producto</th>
                            <th style='padding: 10px; text-align: center;'>Cantidad</th>
                            <th style='padding: 10px; text-align: right;'>Precio</th>
                        </tr>
                    </thead>
                    <tbody>
                        $itemsHtml
                        <tr style='background: #f8f9fa; font-weight: bold;'>
                            <td colspan='2' style='padding: 10px; text-align: right;'>Total:</td>
                            <td style='padding: 10px; text-align: right;'>Q" . number_format($data['total'] ?? 0, 2) . "</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$data['order_url']}' style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Ver Pedido</a>
                </div>
                
                <p>Te mantendremos informado sobre el estado de tu pedido.</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    MrCell Guatemala - Tu pedido, nuestra prioridad<br>
                    <a href='{$data['site_url']}'>{$data['site_url']}</a>
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de actualización de pedido
     */
    private function getOrderUpdateTemplate(array $data): string
    {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #007bff; text-align: center;'>Actualización de Pedido</h1>
                
                <p>Hola <strong>{$data['customer_name']}</strong>,</p>
                
                <p>Tu pedido #{$data['order_id']} ha sido actualizado.</p>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    <h3 style='margin-top: 0;'>Estado Actual</h3>
                    <p><strong>Estado:</strong> {$data['status']}</p>
                    <p><strong>Fecha de actualización:</strong> {$data['updated_at']}</p>
                    " . (isset($data['tracking_number']) ? "<p><strong>Número de seguimiento:</strong> {$data['tracking_number']}</p>" : "") . "
                </div>
                
                " . (isset($data['notes']) ? "<p><strong>Notas:</strong> {$data['notes']}</p>" : "") . "
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$data['order_url']}' style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Ver Detalles</a>
                </div>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    MrCell Guatemala - Seguimiento en tiempo real<br>
                    <a href='{$data['site_url']}'>{$data['site_url']}</a>
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de newsletter
     */
    private function getNewsletterTemplate(array $data): string
    {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #007bff; text-align: center;'>MrCell Guatemala Newsletter</h1>
                
                <p>Hola <strong>{$data['user_name']}</strong>,</p>
                
                <div style='margin: 30px 0;'>
                    {$data['content']}
                </div>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    MrCell Guatemala - Mantente informado<br>
                    <a href='{$data['site_url']}'>{$data['site_url']}</a><br>
                    <a href='{$data['unsubscribe_url']}' style='color: #666;'>Cancelar suscripción</a>
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Obtener subject por defecto
     */
    private function getDefaultSubject(string $template): string
    {
        $subjects = [
            'welcome' => '¡Bienvenido a MrCell Guatemala!',
            'password_reset' => 'Recuperación de contraseña',
            'order_confirmation' => 'Confirmación de pedido',
            'order_update' => 'Actualización de pedido',
            'newsletter' => 'Newsletter MrCell Guatemala'
        ];
        
        return $subjects[$template] ?? 'MrCell Guatemala';
    }
    
    /**
     * Inicializar configuración de email
     */
    private function initializeMailer(): void
    {
        $config = [
            'protocol' => 'smtp',
            'SMTPHost' => $this->config['host'],
            'SMTPPort' => $this->config['port'],
            'SMTPUser' => $this->config['username'],
            'SMTPPass' => $this->config['password'],
            'SMTPCrypto' => $this->config['encryption'],
            'SMTPTimeout' => $this->config['timeout'],
            'mailType' => 'html',
            'charset' => 'utf-8',
            'newline' => "\r\n"
        ];
        
        // Configurar CodeIgniter Email
        $email = \Config\Services::email();
        $email->initialize($config);
    }
    
    /**
     * Verificar si el servicio está configurado
     */
    public function isConfigured(): bool
    {
        return !empty($this->config['username']) && 
               !empty($this->config['password']) && 
               !empty($this->config['host']);
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'configured' => $this->isConfigured(),
            'host' => $this->config['host'],
            'port' => $this->config['port'],
            'encryption' => $this->config['encryption'],
            'from_email' => $this->config['from_email'],
            'from_name' => $this->config['from_name'],
            'debug' => $this->config['debug']
        ];
    }
}
