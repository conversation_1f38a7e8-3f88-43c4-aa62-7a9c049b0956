/**
 * PWA Functionality
 * 
 * Handles Progressive Web App features
 */

class PWAManager {
    constructor() {
        this.swRegistration = null;
        this.deferredPrompt = null;
        this.isOnline = navigator.onLine;
        
        this.init();
    }

    async init() {
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup offline/online handlers
        this.setupNetworkHandlers();
        
        // Setup install prompt
        this.setupInstallPrompt();
        
        // Setup push notifications
        this.setupPushNotifications();
        
        // Setup background sync
        this.setupBackgroundSync();
        
        // Update UI based on connection status
        this.updateConnectionStatus();
    }

    /**
     * Register service worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                console.log('PWA Manager: Registering Service Worker...');

                // Simple, direct path configuration
                const swPath = '/sw.js';
                const scope = '/';

                console.log('PWA Manager: SW Path:', swPath, 'Scope:', scope);

                // Unregister any existing service workers first
                const registrations = await navigator.serviceWorker.getRegistrations();
                for (let registration of registrations) {
                    console.log('PWA Manager: Unregistering existing SW:', registration.scope);
                    await registration.unregister();
                }

                // Register new service worker
                this.swRegistration = await navigator.serviceWorker.register(swPath, {
                    scope: scope
                });

                console.log('Service Worker registered successfully:', this.swRegistration);

                // Setup update handling
                this.swRegistration.addEventListener('updatefound', () => {
                    console.log('Service Worker update found');
                });

                return this.swRegistration;

            } catch (error) {
                console.error('Service Worker registration failed:', error);
                console.log('Trying fallback registration...');
                return await this.registerFallbackServiceWorker();
            }
        } else {
            console.warn('Service Worker not supported in this browser');
            return null;
        }
    }

    /**
     * Register a minimal fallback Service Worker
     */
    async registerFallbackServiceWorker() {
        console.log('Attempting to register fallback Service Worker...');

        // Create a minimal Service Worker as a blob
        const swCode = `
            console.log('Fallback Service Worker activated');

            self.addEventListener('install', event => {
                console.log('Fallback SW: Installing...');
                self.skipWaiting();
            });

            self.addEventListener('activate', event => {
                console.log('Fallback SW: Activating...');
                self.clients.claim();
            });

            self.addEventListener('fetch', event => {
                // Just pass through all requests
                return;
            });
        `;

        const blob = new Blob([swCode], { type: 'application/javascript' });
        const swUrl = URL.createObjectURL(blob);

        this.swRegistration = await navigator.serviceWorker.register(swUrl);
        console.log('Fallback Service Worker registered:', this.swRegistration);

        // Clean up the blob URL
        URL.revokeObjectURL(swUrl);
    }

    /**
     * Setup network status handlers
     */
    setupNetworkHandlers() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateConnectionStatus();
            this.syncOfflineData();
            this.showNotification('Conexión restaurada', 'success');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateConnectionStatus();
            this.showNotification('Sin conexión - Modo offline activado', 'warning');
        });
    }

    /**
     * Setup install prompt
     */
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA Manager: Install prompt available');
            // Only prevent default if we want to show custom banner
            // e.preventDefault();
            this.deferredPrompt = e;

            // Show install banner after 5 seconds (optional)
            setTimeout(() => {
                if (this.deferredPrompt) {
                    this.showInstallBanner();
                }
            }, 5000);
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA Manager: App installed successfully');
            this.hideInstallBanner();
            this.deferredPrompt = null;
            this.showNotification('¡App instalada correctamente!', 'success');
        });
    }

    /**
     * Setup push notifications
     */
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            // Only check permission, don't request automatically
            if (Notification.permission === 'granted' && this.swRegistration) {
                await this.subscribeToPush();
            }

            // Add button to request notifications manually
            this.addNotificationButton();
        }
    }

    /**
     * Setup background sync
     */
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Register for background sync when going offline
            window.addEventListener('offline', () => {
                this.registerBackgroundSync();
            });
        }
    }

    /**
     * Show install banner
     */
    showInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.classList.add('show');
        }
    }

    /**
     * Hide install banner
     */
    hideInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.classList.remove('show');
        }
    }

    /**
     * Install PWA
     */
    async installPWA() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
            }
            
            this.deferredPrompt = null;
            this.hideInstallBanner();
        }
    }

    /**
     * Setup offline detection
     */
    setupOfflineDetection() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('App is back online');
            this.isOnline = true;
            this.showNotification('Conexión restaurada', 'success');
            this.updateConnectionStatus();
        });

        window.addEventListener('offline', () => {
            console.log('App is offline');
            this.isOnline = false;
            this.showNotification('Sin conexión a internet', 'warning');
            this.updateConnectionStatus();
        });

        // Initial status
        this.isOnline = navigator.onLine;
        this.updateConnectionStatus();
    }

    /**
     * Update connection status UI
     */
    updateConnectionStatus() {
        const statusIndicator = document.getElementById('connection-status');

        if (statusIndicator) {
            statusIndicator.className = this.isOnline ? 'online' : 'offline';
            statusIndicator.textContent = this.isOnline ? 'En línea' : 'Sin conexión';
        }

        // Update body class for styling
        document.body.classList.toggle('offline', !this.isOnline);
        document.body.classList.toggle('online', this.isOnline);
    }

    /**
     * Sync offline data when back online
     */
    async syncOfflineData() {
        if (!this.isOnline || !this.swRegistration) return;

        try {
            // Trigger background sync
            await this.swRegistration.sync.register('cart-sync');
            await this.swRegistration.sync.register('order-sync');
            
            console.log('Background sync registered');
        } catch (error) {
            console.error('Background sync registration failed:', error);
        }
    }

    /**
     * Store data for offline use
     */
    async storeOfflineData(type, data) {
        if (!this.swRegistration) return;

        const messageChannel = new MessageChannel();
        
        return new Promise((resolve) => {
            messageChannel.port1.onmessage = (event) => {
                resolve(event.data);
            };
            
            this.swRegistration.active.postMessage({
                type: `STORE_${type.toUpperCase()}`,
                [type]: data
            }, [messageChannel.port2]);
        });
    }

    /**
     * Get offline data
     */
    async getOfflineData(type) {
        if (!this.swRegistration) return null;

        const messageChannel = new MessageChannel();
        
        return new Promise((resolve) => {
            messageChannel.port1.onmessage = (event) => {
                resolve(event.data);
            };
            
            this.swRegistration.active.postMessage({
                type: `GET_OFFLINE_${type.toUpperCase()}`
            }, [messageChannel.port2]);
        });
    }

    /**
     * Add to cart offline
     */
    async addToCartOffline(productId, quantity = 1) {
        const action = {
            type: 'add',
            method: 'POST',
            data: { product_id: productId, quantity }
        };

        await this.storeOfflineData('cart_action', action);
        this.showNotification('Producto agregado (se sincronizará cuando tengas conexión)', 'info');
    }

    /**
     * Request notification permission (only when user clicks)
     */
    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();

            if (permission === 'granted') {
                this.showNotification('Notificaciones activadas', 'success');
                if (this.swRegistration) {
                    await this.subscribeToPush();
                }
            }

            return permission;
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return 'denied';
        }
    }

    /**
     * Add notification button for manual permission request
     */
    addNotificationButton() {
        if (Notification.permission === 'default') {
            // Add a button or UI element for users to manually enable notifications
            console.log('Notifications available - user can enable manually');
        }
    }

    /**
     * Fallback Service Worker registration
     */
    async registerFallbackServiceWorker() {
        try {
            console.log('Trying fallback Service Worker registration...');

            // Try without scope first
            this.swRegistration = await navigator.serviceWorker.register('/sw.js');
            console.log('Fallback Service Worker registered successfully:', this.swRegistration);
            return this.swRegistration;

        } catch (error) {
            console.error('Fallback Service Worker registration also failed:', error);
            console.log('PWA will work without Service Worker');
            return null;
        }
    }

    /**
     * Subscribe to push notifications
     */
    async subscribeToPush() {
        try {
            // Check if push notifications are supported
            if (!('PushManager' in window)) {
                console.warn('Push notifications not supported');
                return;
            }

            // For now, we'll skip push notifications until VAPID keys are properly configured
            // This prevents errors in the console
            console.log('Push notifications available but not configured');

            // TODO: Implement proper VAPID key configuration
            // const subscription = await this.swRegistration.pushManager.subscribe({
            //     userVisibleOnly: true,
            //     applicationServerKey: this.urlBase64ToUint8Array('VAPID_PUBLIC_KEY')
            // });
            // await this.sendSubscriptionToServer(subscription);

        } catch (error) {
            console.error('Push subscription failed:', error);
        }
    }

    /**
     * Send subscription to server
     */
    async sendSubscriptionToServer(subscription) {
        try {
            await fetch('/api/push/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscription)
            });
        } catch (error) {
            console.error('Failed to send subscription to server:', error);
        }
    }

    /**
     * Register background sync
     */
    async registerBackgroundSync() {
        if (this.swRegistration && 'sync' in this.swRegistration) {
            try {
                await this.swRegistration.sync.register('background-sync');
            } catch (error) {
                console.error('Background sync registration failed:', error);
            }
        }
    }

    /**
     * Show update available notification
     */
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="container">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <strong>Nueva versión disponible</strong>
                        <br>
                        <small>Actualiza para obtener las últimas mejoras</small>
                    </div>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="pwaManager.updateApp()">
                            Actualizar
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                            Después
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(updateBanner);
    }

    /**
     * Update app
     */
    updateApp() {
        if (this.swRegistration && this.swRegistration.waiting) {
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getAlertClass(type)} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Show update available notification
     */
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="container">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <strong>Nueva versión disponible</strong>
                        <br>
                        <small>Actualiza para obtener las últimas mejoras</small>
                    </div>
                    <div>
                        <button class="btn btn-light btn-sm me-2" id="update-app-btn">
                            <i class="fas fa-sync me-1"></i>Actualizar
                        </button>
                        <button class="btn btn-outline-light btn-sm" id="dismiss-update-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        updateBanner.querySelector('#update-app-btn').onclick = () => {
            this.updateApp();
            updateBanner.remove();
        };

        updateBanner.querySelector('#dismiss-update-btn').onclick = () => {
            updateBanner.remove();
        };

        document.body.appendChild(updateBanner);
    }

    /**
     * Get Bootstrap alert class
     */
    getAlertClass(type) {
        const classes = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };

        return classes[type] || 'info';
    }

    /**
     * Convert VAPID key
     */
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        
        return outputArray;
    }
}

// Initialize PWA Manager
const pwaManager = new PWAManager();

// Global functions for HTML
window.installPWA = () => pwaManager.installPWA();
window.dismissPWA = () => pwaManager.hideInstallBanner();
