<?php

namespace App\Models;

use CodeIgniter\Model;

class ShippingPackageTypeModel extends Model
{
    protected $table            = 'shipping_package_types';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'description',
        'max_length_cm',
        'max_width_cm',
        'max_height_cm',
        'max_weight_lbs',
        'min_weight_lbs',
        'base_cost',
        'cost_per_km',
        'is_active',
        'sort_order'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'max_length_cm' => 'required|decimal|greater_than[0]',
        'max_width_cm' => 'required|decimal|greater_than[0]',
        'max_height_cm' => 'required|decimal|greater_than[0]',
        'max_weight_lbs' => 'required|decimal|greater_than[0]',
        'min_weight_lbs' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'base_cost' => 'required|decimal|greater_than_equal_to[0]',
        'cost_per_km' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'sort_order' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'El nombre del tipo de paquete es requerido',
            'max_length' => 'El nombre no puede exceder 100 caracteres'
        ],
        'max_length_cm' => [
            'required' => 'La longitud máxima es requerida',
            'decimal' => 'La longitud debe ser un número decimal',
            'greater_than' => 'La longitud debe ser mayor a 0'
        ],
        'base_cost' => [
            'required' => 'El costo base es requerido',
            'decimal' => 'El costo debe ser un número decimal',
            'greater_than_equal_to' => 'El costo no puede ser negativo'
        ]
    ];

    /**
     * Obtener tipos de paquetes activos ordenados
     */
    public function getActivePackageTypes()
    {
        return $this->where('is_active', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Determinar el tipo de paquete apropiado basado en dimensiones y peso
     */
    public function determinePackageType($length, $width, $height, $weight)
    {
        $packageTypes = $this->getActivePackageTypes();
        
        foreach ($packageTypes as $type) {
            // Verificar si el paquete cumple con las dimensiones y peso
            if ($length <= $type['max_length_cm'] && 
                $width <= $type['max_width_cm'] && 
                $height <= $type['max_height_cm'] && 
                $weight <= $type['max_weight_lbs'] &&
                $weight >= $type['min_weight_lbs']) {
                return $type;
            }
        }
        
        // Si no encuentra un tipo apropiado, devolver el más grande
        return end($packageTypes);
    }

    /**
     * Calcular costo de envío
     */
    public function calculateShippingCost($packageTypeId, $distance = 0, $zoneAdditionalCost = 0)
    {
        $packageType = $this->find($packageTypeId);
        
        if (!$packageType) {
            return 0;
        }
        
        $baseCost = $packageType['base_cost'];
        $distanceCost = $distance * $packageType['cost_per_km'];
        
        return $baseCost + $distanceCost + $zoneAdditionalCost;
    }

    /**
     * Obtener estadísticas de uso de tipos de paquetes
     */
    public function getUsageStats()
    {
        $db = \Config\Database::connect();
        
        try {
            $query = $db->query("
                SELECT 
                    spt.name,
                    spt.id,
                    COUNT(o.id) as usage_count,
                    AVG(o.shipping_cost) as avg_shipping_cost
                FROM shipping_package_types spt
                LEFT JOIN orders o ON o.package_type_id = spt.id
                WHERE spt.is_active = 1
                GROUP BY spt.id, spt.name
                ORDER BY spt.sort_order ASC
            ");
            
            return $query->getResultArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Validar dimensiones del producto
     */
    public function validateProductDimensions($length, $width, $height, $weight)
    {
        $packageTypes = $this->getActivePackageTypes();
        $maxPackageType = end($packageTypes);
        
        $errors = [];
        
        if ($length > $maxPackageType['max_length_cm']) {
            $errors[] = "La longitud ({$length}cm) excede el máximo permitido ({$maxPackageType['max_length_cm']}cm)";
        }
        
        if ($width > $maxPackageType['max_width_cm']) {
            $errors[] = "El ancho ({$width}cm) excede el máximo permitido ({$maxPackageType['max_width_cm']}cm)";
        }
        
        if ($height > $maxPackageType['max_height_cm']) {
            $errors[] = "La altura ({$height}cm) excede el máximo permitido ({$maxPackageType['max_height_cm']}cm)";
        }
        
        if ($weight > $maxPackageType['max_weight_lbs']) {
            $errors[] = "El peso ({$weight}lbs) excede el máximo permitido ({$maxPackageType['max_weight_lbs']}lbs)";
        }
        
        return $errors;
    }
}
