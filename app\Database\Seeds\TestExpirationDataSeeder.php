<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TestExpirationDataSeeder extends Seeder
{
    public function run()
    {
        try {
            echo "🧪 PROBANDO DATOS DE CADUCIDAD...\n\n";
            
            // Verificar productos con caducidad
            $productsWithExpiration = $this->db->query("
                SELECT COUNT(*) as total 
                FROM products 
                WHERE has_expiration = 1 
                    AND is_active = 1 
                    AND deleted_at IS NULL
            ")->getRowArray();
            
            echo "📈 Total productos con caducidad: " . $productsWithExpiration['total'] . "\n";
            
            // Mostrar productos con caducidad
            $products = $this->db->query("
                SELECT name, sku, expiration_date, has_expiration, is_active,
                       DATEDIFF(expiration_date, CURDATE()) as days_until_expiration
                FROM products 
                WHERE has_expiration = 1 
                    AND is_active = 1 
                    AND deleted_at IS NULL
                ORDER BY expiration_date ASC
                LIMIT 10
            ")->getResultArray();
            
            echo "\n📋 Productos con caducidad:\n";
            foreach ($products as $product) {
                $status = '';
                if ($product['days_until_expiration'] < 0) {
                    $status = 'CADUCADO';
                } elseif ($product['days_until_expiration'] == 0) {
                    $status = 'CADUCA HOY';
                } elseif ($product['days_until_expiration'] <= 30) {
                    $status = 'PRÓXIMO A CADUCAR';
                } else {
                    $status = 'OK';
                }
                
                echo "  - {$product['name']} (SKU: {$product['sku']}) - Caduca: {$product['expiration_date']} - Días: {$product['days_until_expiration']} - Estado: {$status}\n";
            }
            
            // Probar la consulta del helper
            $expiringQuery = "
                SELECT 
                    p.id,
                    p.name,
                    p.sku,
                    p.expiration_date,
                    p.expiration_alert_days,
                    p.stock_quantity,
                    DATEDIFF(p.expiration_date, CURDATE()) as days_until_expiration,
                    CASE 
                        WHEN DATEDIFF(p.expiration_date, CURDATE()) < 0 THEN 'expired'
                        WHEN DATEDIFF(p.expiration_date, CURDATE()) = 0 THEN 'expires_today'
                        WHEN DATEDIFF(p.expiration_date, CURDATE()) <= p.expiration_alert_days THEN 'expires_soon'
                        ELSE 'ok'
                    END as expiration_status
                FROM products p
                WHERE p.has_expiration = 1 
                    AND p.is_active = 1 
                    AND p.deleted_at IS NULL
                    AND p.expiration_date IS NOT NULL
                    AND (
                        p.expiration_date <= CURDATE() 
                        OR DATEDIFF(p.expiration_date, CURDATE()) <= p.expiration_alert_days
                    )
                ORDER BY p.expiration_date ASC
            ";
            
            $expiringProducts = $this->db->query($expiringQuery)->getResultArray();
            
            echo "\n📦 Productos próximos a caducar (consulta del helper): " . count($expiringProducts) . "\n";
            
            if (!empty($expiringProducts)) {
                echo "📋 Lista de productos próximos a caducar:\n";
                foreach ($expiringProducts as $product) {
                    echo "  - {$product['name']} (SKU: {$product['sku']}) - Caduca: {$product['expiration_date']} - Estado: {$product['expiration_status']}\n";
                }
            } else {
                echo "⚠️ No se encontraron productos próximos a caducar con la consulta del helper\n";
            }
            
            // Resumen de caducidad
            $summaryQuery = "
                SELECT 
                    COUNT(CASE WHEN DATEDIFF(expiration_date, CURDATE()) < 0 THEN 1 END) as expired,
                    COUNT(CASE WHEN DATEDIFF(expiration_date, CURDATE()) = 0 THEN 1 END) as expires_today,
                    COUNT(CASE WHEN DATEDIFF(expiration_date, CURDATE()) BETWEEN 1 AND 7 THEN 1 END) as expires_this_week,
                    COUNT(CASE WHEN DATEDIFF(expiration_date, CURDATE()) BETWEEN 8 AND 30 THEN 1 END) as expires_this_month
                FROM products 
                WHERE has_expiration = 1 
                    AND is_active = 1 
                    AND deleted_at IS NULL
                    AND expiration_date IS NOT NULL
            ";
            
            $summary = $this->db->query($summaryQuery)->getRowArray();
            
            echo "\n📊 Resumen de caducidad:\n";
            echo "  - Caducados: " . ($summary['expired'] ?? 0) . "\n";
            echo "  - Caducan hoy: " . ($summary['expires_today'] ?? 0) . "\n";
            echo "  - Caducan esta semana: " . ($summary['expires_this_week'] ?? 0) . "\n";
            echo "  - Caducan este mes: " . ($summary['expires_this_month'] ?? 0) . "\n";
            
            echo "\n✅ Prueba completada\n";
            
        } catch (\Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
        }
    }
}
