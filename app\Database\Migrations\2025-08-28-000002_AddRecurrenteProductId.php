<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddRecurrenteProductId extends Migration
{
    public function up()
    {
        // Agregar campo para almacenar el ID del producto en Recurrente
        $fields = [
            'recurrente_product_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'ID del producto en Recurrente',
                'after' => 'uuid'
            ],
            'recurrente_synced_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Última fecha de sincronización con Recurrente',
                'after' => 'recurrente_product_id'
            ],
            'recurrente_sync_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'synced', 'error', 'disabled'],
                'default' => 'pending',
                'comment' => 'Estado de sincronización con Recurrente',
                'after' => 'recurrente_synced_at'
            ]
        ];

        $this->forge->addColumn('products', $fields);

        // Agregar índices para optimizar consultas
        $this->forge->addKey('recurrente_product_id', false, false, 'products');
        $this->forge->addKey('recurrente_sync_status', false, false, 'products');
    }

    public function down()
    {
        // Eliminar los campos agregados
        $this->forge->dropColumn('products', [
            'recurrente_product_id',
            'recurrente_synced_at',
            'recurrente_sync_status'
        ]);
    }
}
