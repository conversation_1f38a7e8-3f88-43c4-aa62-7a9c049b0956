<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class ExpirationTestProductsSeeder extends Seeder
{
    public function run()
    {
        try {
            // Obtener una categoría existente
            $category = $this->db->query("
                SELECT id FROM categories 
                WHERE deleted_at IS NULL 
                LIMIT 1
            ")->getRowArray();

            if (!$category) {
                echo "❌ No hay categorías disponibles\n";
                return;
            }

            $categoryId = $category['id'];

            // Obtener una marca existente
            $brand = $this->db->query("
                SELECT id FROM brands 
                WHERE deleted_at IS NULL 
                LIMIT 1
            ")->getRowArray();

            $brandId = $brand['id'] ?? null;

            // Productos de prueba con diferentes estados de caducidad
            $testProducts = [
                [
                    'uuid' => uniqid('test-exp-', true),
                    'name' => 'Producto Caducado - Leche UHT',
                    'sku' => 'TEST-EXPIRED-001',
                    'description' => 'Producto de prueba que ya caducó',
                    'short_description' => 'Leche UHT caducada para pruebas',
                    'category_id' => $categoryId,
                    'brand_id' => $brandId,
                    'price_regular' => 25.00,
                    'price_sale' => 20.00,
                    'currency' => 'GTQ',
                    'stock_quantity' => 5,
                    'stock_min' => 2,
                    'weight' => 1.0,
                    'is_active' => 1,
                    'is_featured' => 0,
                    'has_expiration' => 1,
                    'expiration_date' => date('Y-m-d', strtotime('-5 days')), // Caducado hace 5 días
                    'expiration_alert_days' => 30,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => uniqid('test-exp-', true),
                    'name' => 'Producto Caduca Hoy - Yogurt Natural',
                    'slug' => 'producto-caduca-hoy-yogurt-natural-' . time(),
                    'sku' => 'TEST-TODAY-002',
                    'description' => 'Producto de prueba que caduca hoy',
                    'short_description' => 'Yogurt natural que caduca hoy',
                    'category_id' => $categoryId,
                    'brand_id' => $brandId,
                    'price_regular' => 15.00,
                    'price_sale' => 12.00,
                    'currency' => 'GTQ',
                    'stock_quantity' => 8,
                    'stock_min' => 3,
                    'weight' => 0.5,
                    'is_active' => 1,
                    'is_featured' => 0,
                    'has_expiration' => 1,
                    'expiration_date' => date('Y-m-d'), // Caduca hoy
                    'expiration_alert_days' => 15,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => uniqid('test-exp-', true),
                    'name' => 'Producto Próximo a Caducar - Pan Integral',
                    'slug' => 'producto-proximo-caducar-pan-integral-' . (time() + 1),
                    'sku' => 'TEST-SOON-003',
                    'description' => 'Producto de prueba próximo a caducar',
                    'short_description' => 'Pan integral próximo a caducar',
                    'category_id' => $categoryId,
                    'brand_id' => $brandId,
                    'price_regular' => 18.00,
                    'price_sale' => 15.00,
                    'currency' => 'GTQ',
                    'stock_quantity' => 12,
                    'stock_min' => 5,
                    'weight' => 0.8,
                    'is_active' => 1,
                    'is_featured' => 0,
                    'has_expiration' => 1,
                    'expiration_date' => date('Y-m-d', strtotime('+3 days')), // Caduca en 3 días
                    'expiration_alert_days' => 7,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => uniqid('test-exp-', true),
                    'name' => 'Producto Sin Caducidad - Arroz Blanco',
                    'slug' => 'producto-sin-caducidad-arroz-blanco-' . (time() + 2),
                    'sku' => 'TEST-NO-EXP-004',
                    'description' => 'Producto de prueba sin fecha de caducidad',
                    'short_description' => 'Arroz blanco sin caducidad',
                    'category_id' => $categoryId,
                    'brand_id' => $brandId,
                    'price_regular' => 35.00,
                    'price_sale' => 30.00,
                    'currency' => 'GTQ',
                    'stock_quantity' => 20,
                    'stock_min' => 10,
                    'weight' => 2.0,
                    'is_active' => 1,
                    'is_featured' => 0,
                    'has_expiration' => 0,
                    'expiration_date' => null,
                    'expiration_alert_days' => 30,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => uniqid('test-exp-', true),
                    'name' => 'Producto Caduca en 1 Mes - Aceite de Oliva',
                    'slug' => 'producto-caduca-mes-aceite-oliva-' . (time() + 3),
                    'sku' => 'TEST-MONTH-005',
                    'description' => 'Producto de prueba que caduca en un mes',
                    'short_description' => 'Aceite de oliva con caducidad lejana',
                    'category_id' => $categoryId,
                    'brand_id' => $brandId,
                    'price_regular' => 85.00,
                    'price_sale' => 75.00,
                    'currency' => 'GTQ',
                    'stock_quantity' => 15,
                    'stock_min' => 5,
                    'weight' => 1.5,
                    'is_active' => 1,
                    'is_featured' => 1,
                    'has_expiration' => 1,
                    'expiration_date' => date('Y-m-d', strtotime('+25 days')), // Caduca en 25 días
                    'expiration_alert_days' => 30,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            foreach ($testProducts as $product) {
                // Verificar si ya existe
                $existing = $this->db->query("
                    SELECT id FROM products 
                    WHERE sku = ? 
                    LIMIT 1
                ", [$product['sku']])->getRowArray();

                if (!$existing) {
                    $this->db->table('products')->insert($product);
                    echo "✅ Producto creado: {$product['name']} (SKU: {$product['sku']})\n";
                } else {
                    echo "⚠️ Producto {$product['sku']} ya existe\n";
                }
            }

            echo "\n✅ Productos de prueba con fecha de caducidad creados exitosamente\n";
            echo "📋 Resumen:\n";
            echo "  - 1 producto caducado\n";
            echo "  - 1 producto que caduca hoy\n";
            echo "  - 1 producto próximo a caducar (3 días)\n";
            echo "  - 1 producto sin fecha de caducidad\n";
            echo "  - 1 producto con caducidad lejana (25 días)\n";

        } catch (\Exception $e) {
            echo "❌ Error creando productos de prueba: " . $e->getMessage() . "\n";
        }
    }
}
