<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class InitialDataSeeder extends Seeder
{
    public function run()
    {
        // Insertar empresas de envío guatemaltecas
        $shippingCompanies = [
            [
                'name' => 'Cargo Expreso',
                'code' => 'cargoexpreso',
                'description' => 'Empresa líder en envíos nacionales con cobertura en toda Guatemala',
                'phone' => '2234-5678',
                'email' => '<EMAIL>',
                'website' => 'https://cargoexpreso.com.gt',
                'tracking_url_template' => 'https://cargoexpreso.com.gt/tracking/{tracking_number}',
                'supports_tracking' => 1,
                'supports_pickup' => 1,
                'max_weight' => 50.00,
                'coverage_areas' => json_encode(['Guatemala', 'Sacatepéquez', 'Chimaltenango', 'Escuintla', 'Sololá']),
                'business_hours' => json_encode([
                    'monday' => '08:00-18:00',
                    'tuesday' => '08:00-18:00',
                    'wednesday' => '08:00-18:00',
                    'thursday' => '08:00-18:00',
                    'friday' => '08:00-18:00',
                    'saturday' => '08:00-12:00',
                    'sunday' => 'closed'
                ]),
                'is_active' => 1,
                'sort_order' => 1
            ],
            [
                'name' => 'GuatEx',
                'code' => 'guatex',
                'description' => 'Servicio express especializado en zona metropolitana',
                'phone' => '2345-6789',
                'email' => '<EMAIL>',
                'website' => 'https://guatex.gt',
                'tracking_url_template' => 'https://guatex.gt/rastreo/{tracking_number}',
                'supports_tracking' => 1,
                'supports_pickup' => 1,
                'max_weight' => 30.00,
                'coverage_areas' => json_encode(['Guatemala', 'Mixco', 'Villa Nueva', 'San Miguel Petapa']),
                'business_hours' => json_encode([
                    'monday' => '07:00-19:00',
                    'tuesday' => '07:00-19:00',
                    'wednesday' => '07:00-19:00',
                    'thursday' => '07:00-19:00',
                    'friday' => '07:00-19:00',
                    'saturday' => '07:00-15:00',
                    'sunday' => 'closed'
                ]),
                'is_active' => 1,
                'sort_order' => 2
            ],
            [
                'name' => 'Forza',
                'code' => 'forza',
                'description' => 'Envíos rápidos y seguros en toda Guatemala',
                'phone' => '2456-7890',
                'email' => '<EMAIL>',
                'website' => 'https://forza.com.gt',
                'tracking_url_template' => 'https://forza.com.gt/seguimiento/{tracking_number}',
                'supports_tracking' => 1,
                'supports_pickup' => 0,
                'max_weight' => 25.00,
                'coverage_areas' => json_encode(['Guatemala', 'Quetzaltenango', 'Huehuetenango', 'Cobán']),
                'business_hours' => json_encode([
                    'monday' => '08:00-17:00',
                    'tuesday' => '08:00-17:00',
                    'wednesday' => '08:00-17:00',
                    'thursday' => '08:00-17:00',
                    'friday' => '08:00-17:00',
                    'saturday' => '08:00-12:00',
                    'sunday' => 'closed'
                ]),
                'is_active' => 1,
                'sort_order' => 3
            ]
        ];

        $this->db->table('shipping_companies')->insertBatch($shippingCompanies);

        // Insertar tarifas de envío
        $shippingRates = [
            // Cargo Expreso - Guatemala
            ['company_id' => 1, 'zone' => 'Guatemala Capital', 'service_type' => 'standard', 'base_cost' => 25.00, 'cost_per_kg' => 5.00, 'free_shipping_threshold' => 200.00, 'estimated_days_min' => 1, 'estimated_days_max' => 2],
            ['company_id' => 1, 'zone' => 'Guatemala Capital', 'service_type' => 'express', 'base_cost' => 45.00, 'cost_per_kg' => 8.00, 'free_shipping_threshold' => 300.00, 'estimated_days_min' => 1, 'estimated_days_max' => 1],
            
            // Cargo Expreso - Zona Metro
            ['company_id' => 1, 'zone' => 'Zona Metropolitana', 'service_type' => 'standard', 'base_cost' => 35.00, 'cost_per_kg' => 6.00, 'free_shipping_threshold' => 250.00, 'estimated_days_min' => 2, 'estimated_days_max' => 3],
            ['company_id' => 1, 'zone' => 'Zona Metropolitana', 'service_type' => 'express', 'base_cost' => 55.00, 'cost_per_kg' => 10.00, 'free_shipping_threshold' => 350.00, 'estimated_days_min' => 1, 'estimated_days_max' => 2],
            
            // Cargo Expreso - Departamentos
            ['company_id' => 1, 'zone' => 'Departamentos', 'service_type' => 'standard', 'base_cost' => 45.00, 'cost_per_kg' => 8.00, 'free_shipping_threshold' => 300.00, 'estimated_days_min' => 3, 'estimated_days_max' => 5],
            ['company_id' => 1, 'zone' => 'Departamentos', 'service_type' => 'express', 'base_cost' => 75.00, 'cost_per_kg' => 12.00, 'free_shipping_threshold' => 400.00, 'estimated_days_min' => 2, 'estimated_days_max' => 3],
            
            // GuatEx - Solo zona metro
            ['company_id' => 2, 'zone' => 'Guatemala Capital', 'service_type' => 'express', 'base_cost' => 35.00, 'cost_per_kg' => 7.00, 'free_shipping_threshold' => 250.00, 'estimated_days_min' => 1, 'estimated_days_max' => 1],
            ['company_id' => 2, 'zone' => 'Zona Metropolitana', 'service_type' => 'express', 'base_cost' => 45.00, 'cost_per_kg' => 9.00, 'free_shipping_threshold' => 300.00, 'estimated_days_min' => 1, 'estimated_days_max' => 2],
            
            // Forza
            ['company_id' => 3, 'zone' => 'Guatemala Capital', 'service_type' => 'standard', 'base_cost' => 30.00, 'cost_per_kg' => 6.00, 'free_shipping_threshold' => 200.00, 'estimated_days_min' => 2, 'estimated_days_max' => 3],
            ['company_id' => 3, 'zone' => 'Departamentos', 'service_type' => 'standard', 'base_cost' => 50.00, 'cost_per_kg' => 10.00, 'free_shipping_threshold' => 350.00, 'estimated_days_min' => 3, 'estimated_days_max' => 5]
        ];

        $this->db->table('shipping_rates')->insertBatch($shippingRates);

        // Insertar ubicaciones de almacén
        $warehouseLocations = [
            [
                'name' => 'Almacén Principal',
                'code' => 'MAIN',
                'type' => 'warehouse',
                'address' => 'Zona 12, Ciudad de Guatemala',
                'capacity' => 10000,
                'is_active' => 1
            ],
            [
                'name' => 'Tienda Centro',
                'code' => 'STORE_CENTRO',
                'type' => 'store',
                'address' => 'Zona 1, Ciudad de Guatemala',
                'capacity' => 500,
                'is_active' => 1
            ],
            [
                'name' => 'Tienda Zona 10',
                'code' => 'STORE_Z10',
                'type' => 'store',
                'address' => 'Zona 10, Ciudad de Guatemala',
                'capacity' => 300,
                'is_active' => 1
            ],
            [
                'name' => 'Inventario Virtual',
                'code' => 'VIRTUAL',
                'type' => 'virtual',
                'address' => null,
                'capacity' => null,
                'is_active' => 1
            ]
        ];

        $this->db->table('warehouse_locations')->insertBatch($warehouseLocations);

        // Insertar cupones de ejemplo
        $sampleCoupons = [
            [
                'code' => 'BIENVENIDO10',
                'name' => 'Cupón de Bienvenida',
                'description' => 'Descuento del 10% para nuevos clientes',
                'type' => 'percentage',
                'value' => 10.00,
                'min_order_amount' => 100.00,
                'usage_limit' => null,
                'usage_limit_per_user' => 1,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+1 year')),
                'first_order_only' => 1,
                'is_active' => 1
            ],
            [
                'code' => 'ENVIOGRATIS',
                'name' => 'Envío Gratis',
                'description' => 'Envío gratuito en compras mayores a Q150',
                'type' => 'free_shipping',
                'value' => 0,
                'min_order_amount' => 150.00,
                'usage_limit' => null,
                'usage_limit_per_user' => 5,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => null,
                'first_order_only' => 0,
                'is_active' => 1
            ],
            [
                'code' => 'DESCUENTO50',
                'name' => 'Descuento Q50',
                'description' => 'Q50 de descuento en compras mayores a Q300',
                'type' => 'fixed',
                'value' => 50.00,
                'min_order_amount' => 300.00,
                'max_discount_amount' => 50.00,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+3 months')),
                'first_order_only' => 0,
                'is_active' => 1
            ],
            [
                'code' => 'LOYALTY5',
                'name' => 'Cliente Fiel',
                'description' => '15% de descuento para clientes con 5+ compras',
                'type' => 'percentage',
                'value' => 15.00,
                'min_order_amount' => 0,
                'usage_limit' => null,
                'usage_limit_per_user' => 3,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => null,
                'first_order_only' => 0,
                'is_active' => 1
            ],
            [
                'code' => 'VIP10',
                'name' => 'Cliente VIP',
                'description' => '20% de descuento para clientes VIP',
                'type' => 'percentage',
                'value' => 20.00,
                'min_order_amount' => 0,
                'max_discount_amount' => 200.00,
                'usage_limit' => null,
                'usage_limit_per_user' => 10,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => null,
                'first_order_only' => 0,
                'is_active' => 1
            ]
        ];

        $this->db->table('coupons')->insertBatch($sampleCoupons);

        // Insertar descuentos por volumen
        $bulkDiscounts = [
            [
                'name' => 'Descuento por Volumen - Accesorios',
                'min_quantity' => 3,
                'discount_type' => 'percentage',
                'discount_value' => 10.00,
                'applicable_categories' => json_encode([1, 2]), // IDs de categorías de accesorios
                'is_active' => 1
            ],
            [
                'name' => 'Descuento por Volumen - Fundas',
                'min_quantity' => 5,
                'discount_type' => 'percentage',
                'discount_value' => 15.00,
                'applicable_categories' => json_encode([3]), // ID de categoría de fundas
                'is_active' => 1
            ],
            [
                'name' => 'Descuento Mayorista',
                'min_quantity' => 10,
                'discount_type' => 'percentage',
                'discount_value' => 20.00,
                'applicable_products' => null,
                'applicable_categories' => null,
                'is_active' => 1
            ]
        ];

        $this->db->table('bulk_discounts')->insertBatch($bulkDiscounts);

        // Insertar campañas promocionales estacionales
        $promotionalCampaigns = [
            [
                'name' => 'Black Friday',
                'description' => 'Descuentos especiales por Black Friday',
                'code' => 'BLACKFRIDAY',
                'type' => 'percentage',
                'value' => 25.00,
                'start_date' => date('Y-11-24 00:00:00'),
                'end_date' => date('Y-11-26 23:59:59'),
                'is_active' => 1
            ],
            [
                'name' => 'Cyber Monday',
                'description' => 'Ofertas exclusivas online',
                'code' => 'CYBERMONDAY',
                'type' => 'percentage',
                'value' => 30.00,
                'start_date' => date('Y-11-27 00:00:00'),
                'end_date' => date('Y-11-27 23:59:59'),
                'is_active' => 1
            ],
            [
                'name' => 'Navidad',
                'description' => 'Descuentos navideños',
                'code' => 'NAVIDAD',
                'type' => 'percentage',
                'value' => 20.00,
                'start_date' => date('Y-12-01 00:00:00'),
                'end_date' => date('Y-12-25 23:59:59'),
                'is_active' => 1
            ]
        ];

        $this->db->table('promotional_campaigns')->insertBatch($promotionalCampaigns);

        echo "Datos iniciales insertados correctamente.\n";
    }
}
