<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use App\Models\UserModel;

/**
 * Controlador de Autenticación
 * 
 * Maneja login, logout, refresh de tokens y verificación de autenticación
 */
class AuthController extends ResourceController
{
    use ResponseTrait;

    protected $format = 'json';

    /**
     * Login de usuario
     * POST /api/auth/login
     * 
     * @return mixed
     */
    public function login()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validación
            $validation = \Config\Services::validation();
            $validation->setRules([
                'email' => 'required|valid_email',
                'password' => 'required'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            $model = new UserModel();
            $usuario = $model->where('email', $data['email'])->first();

            if (!$usuario || !password_verify($data['password'], $usuario['password'])) {
                return $this->failUnauthorized('Credenciales inválidas');
            }

            // Verificar que el usuario esté activo
            if ($usuario['status'] !== 'active') {
                return $this->failUnauthorized('Usuario inactivo');
            }

            // Generar token simple (sin JWT por ahora)
            $token = bin2hex(random_bytes(32));
            
            // Guardar token en sesión
            session()->set([
                'user_id' => $usuario['id'],
                'user_email' => $usuario['email'],
                'user_name' => $usuario['name'],
                'access_token' => $token,
                'logged_in' => true
            ]);

            // Log de seguridad
            log_message('info', "Login exitoso para usuario: {$usuario['email']} desde IP: " . $this->request->getIPAddress());

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'usuario' => [
                        'id' => $usuario['id'],
                        'nombre' => $usuario['name'],
                        'email' => $usuario['email']
                    ],
                    'access_token' => $token,
                    'token_type' => 'Bearer'
                ],
                'message' => 'Login exitoso'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en login: ' . $e->getMessage());
            return $this->failServerError('Error en el servidor');
        }
    }

    /**
     * Logout de usuario
     * POST /api/auth/logout
     * 
     * @return mixed
     */
    public function logout()
    {
        try {
            // Destruir sesión
            session()->destroy();

            return $this->respond([
                'status' => 'success',
                'message' => 'Logout exitoso'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en logout: ' . $e->getMessage());
            return $this->failServerError('Error en el servidor');
        }
    }

    /**
     * Verificar autenticación
     * GET /api/auth/me
     * 
     * @return mixed
     */
    public function me()
    {
        try {
            if (!session()->get('logged_in')) {
                return $this->failUnauthorized('No autenticado');
            }

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'usuario' => [
                        'id' => session()->get('user_id'),
                        'nombre' => session()->get('user_name'),
                        'email' => session()->get('user_email')
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en verificación: ' . $e->getMessage());
            return $this->failServerError('Error en el servidor');
        }
    }

    /**
     * Registro de usuario
     * POST /api/auth/register
     * 
     * @return mixed
     */
    public function register()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validación
            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|min_length[2]',
                'email' => 'required|valid_email|is_unique[users.email]',
                'password' => 'required|min_length[6]',
                'password_confirm' => 'required|matches[password]'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            $model = new UserModel();
            
            $userData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $userId = $model->insert($userData);

            if (!$userId) {
                return $this->failServerError('Error al crear usuario');
            }

            // Log de seguridad
            log_message('info', "Usuario registrado: {$data['email']} desde IP: " . $this->request->getIPAddress());

            return $this->respondCreated([
                'status' => 'success',
                'data' => [
                    'usuario' => [
                        'id' => $userId,
                        'nombre' => $data['name'],
                        'email' => $data['email']
                    ]
                ],
                'message' => 'Usuario registrado exitosamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en registro: ' . $e->getMessage());
            return $this->failServerError('Error en el servidor');
        }
    }
}
