<?= $this->extend('layouts/frontend_modern') ?>

<?= $this->section('title') ?>
<?= $page_title ?> - MrCell Guatemala
<?= $this->endSection() ?>

<?= $this->section('meta') ?>
<meta name="description" content="<?= $page_description ?>">
<meta name="robots" content="noindex, nofollow">
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<link rel="stylesheet" href="<?= base_url('assets/css/wishlist-modern.css') ?>">
<style>
    .wishlist-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        margin-bottom: 40px;
    }
    
    .wishlist-stats {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #666;
        margin-top: 5px;
    }
    
    .wishlist-filters {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .filter-group {
        margin-bottom: 15px;
    }
    
    .filter-group:last-child {
        margin-bottom: 0;
    }
    
    .filter-label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        color: #333;
    }
    
    .filter-select {
        width: 100%;
        padding: 10px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }
    
    .filter-select:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .wishlist-content {
        min-height: 400px;
    }
    
    .empty-wishlist {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-wishlist i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }
    
    .empty-wishlist h3 {
        margin-bottom: 15px;
        color: #333;
    }
    
    .btn-continue-shopping {
        background: #667eea;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        text-decoration: none;
        display: inline-block;
        margin-top: 20px;
        transition: all 0.3s ease;
    }
    
    .btn-continue-shopping:hover {
        background: #5a6fd8;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Hero Section -->
<section class="wishlist-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-heart me-3"></i>
                    Mi Lista de Deseos
                </h1>
                <p class="lead mb-0">
                    Gestiona tus productos favoritos y recibe notificaciones cuando bajen de precio
                </p>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <!-- Estadísticas -->
    <div class="wishlist-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number" id="total-items"><?= $wishlist_stats['total_items'] ?? 0 ?></span>
                    <div class="stat-label">Productos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number" id="high-priority">
                        <?php
                        $highPriority = 0;
                        if (isset($wishlist_stats['priority_breakdown'])) {
                            foreach ($wishlist_stats['priority_breakdown'] as $priority) {
                                if ($priority['priority'] === 'high') {
                                    $highPriority = $priority['count'];
                                    break;
                                }
                            }
                        }
                        echo $highPriority;
                        ?>
                    </span>
                    <div class="stat-label">Alta Prioridad</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number" id="with-alerts"><?= $wishlist_stats['with_price_alerts'] ?? 0 ?></span>
                    <div class="stat-label">Con Alertas</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number" id="in-stock">-</span>
                    <div class="stat-label">En Stock</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Filtros -->
        <div class="col-lg-3">
            <div class="wishlist-filters">
                <h5 class="mb-3">
                    <i class="fas fa-filter me-2"></i>
                    Filtros
                </h5>
                
                <div class="filter-group">
                    <label class="filter-label">Prioridad</label>
                    <select class="filter-select" id="priority-filter">
                        <option value="">Todas las prioridades</option>
                        <option value="high">Alta prioridad</option>
                        <option value="medium">Prioridad media</option>
                        <option value="low">Baja prioridad</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Ordenar por</label>
                    <select class="filter-select" id="sort-filter">
                        <option value="created_at">Fecha agregado</option>
                        <option value="name">Nombre A-Z</option>
                        <option value="price">Precio</option>
                        <option value="priority">Prioridad</option>
                        <option value="stock">Disponibilidad</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Orden</label>
                    <select class="filter-select" id="order-filter">
                        <option value="DESC">Descendente</option>
                        <option value="ASC">Ascendente</option>
                    </select>
                </div>
                
                <button class="btn btn-outline-primary w-100 mt-3" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>
                    Limpiar Filtros
                </button>
            </div>
        </div>
        
        <!-- Contenido de Wishlist -->
        <div class="col-lg-9">
            <div class="wishlist-content">
                <!-- Loading -->
                <div id="wishlist-loading" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-3 text-muted">Cargando tu lista de deseos...</p>
                </div>
                
                <!-- Lista de productos -->
                <div id="wishlist-items">
                    <!-- Los productos se cargarán aquí via AJAX -->
                </div>
                
                <!-- Paginación -->
                <div id="wishlist-pagination" class="d-flex justify-content-center mt-4">
                    <!-- La paginación se cargará aquí -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para editar item de wishlist -->
<div class="modal fade" id="editWishlistModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Editar en Lista de Deseos
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="edit-wishlist-form">
                    <input type="hidden" id="edit-product-id">
                    
                    <div class="mb-3">
                        <label class="form-label">Prioridad</label>
                        <select class="form-select" id="edit-priority">
                            <option value="low">Baja prioridad</option>
                            <option value="medium">Prioridad media</option>
                            <option value="high">Alta prioridad</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Notas personales</label>
                        <textarea class="form-control" id="edit-notes" rows="3" 
                                placeholder="Agrega notas sobre este producto..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit-notifications">
                            <label class="form-check-label" for="edit-notifications">
                                Recibir notificaciones de precio
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Alerta de precio (opcional)</label>
                        <div class="input-group">
                            <span class="input-group-text">Q</span>
                            <input type="number" class="form-control" id="edit-price-alert" 
                                   step="0.01" placeholder="0.00">
                        </div>
                        <div class="form-text">
                            Te notificaremos cuando el precio sea igual o menor a este valor
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="saveWishlistChanges()">
                    <i class="fas fa-save me-2"></i>
                    Guardar Cambios
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="<?= base_url('assets/js/wishlist-modern.js') ?>"></script>
<script>
    // Configuración global
    window.MrCellWishlist = {
        userId: <?= $user_id ?>,
        apiUrl: '<?= base_url('api/wishlist') ?>',
        baseUrl: '<?= base_url() ?>'
    };
    
    // Inicializar cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof WishlistManager !== 'undefined') {
            window.wishlistManager = new WishlistManager();
        }
    });
</script>
<?= $this->endSection() ?>
