<?php

namespace App\Models;

use CodeIgniter\Model;

class ContactMessageModel extends Model
{
    protected $table = 'contact_messages';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'uuid',
        'name',
        'email',
        'subject',
        'message',
        'phone',
        'ip_address',
        'user_agent',
        'status',
        'priority',
        'assigned_to',
        'admin_notes',
        'replied_at',
        'replied_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'email' => 'required|valid_email|max_length[255]',
        'subject' => 'required|min_length[5]|max_length[200]',
        'message' => 'required|min_length[10]|max_length[2000]',
        'status' => 'permit_empty|in_list[pending,read,replied,closed]',
        'priority' => 'permit_empty|in_list[low,normal,high,urgent]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'El nombre es requerido',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 100 caracteres'
        ],
        'email' => [
            'required' => 'El email es requerido',
            'valid_email' => 'Debe ser un email válido',
            'max_length' => 'El email no puede exceder 255 caracteres'
        ],
        'subject' => [
            'required' => 'El asunto es requerido',
            'min_length' => 'El asunto debe tener al menos 5 caracteres',
            'max_length' => 'El asunto no puede exceder 200 caracteres'
        ],
        'message' => [
            'required' => 'El mensaje es requerido',
            'min_length' => 'El mensaje debe tener al menos 10 caracteres',
            'max_length' => 'El mensaje no puede exceder 2000 caracteres'
        ]
    ];

    /**
     * Generar UUID antes de insertar
     */
    protected $beforeInsert = ['generateUUID'];

    protected function generateUUID(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->generateUUIDv4();
        }
        return $data;
    }

    /**
     * Generar UUID v4
     */
    private function generateUUIDv4(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Obtener mensajes con paginación y filtros
     */
    public function getMessagesWithFilters($filters = [], $limit = 20, $offset = 0)
    {
        $builder = $this->builder();
        
        // Aplicar filtros
        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }
        
        if (!empty($filters['priority'])) {
            $builder->where('priority', $filters['priority']);
        }
        
        if (!empty($filters['assigned_to'])) {
            $builder->where('assigned_to', $filters['assigned_to']);
        }
        
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('name', $filters['search'])
                   ->orLike('email', $filters['search'])
                   ->orLike('subject', $filters['search'])
                   ->orLike('message', $filters['search'])
                   ->groupEnd();
        }
        
        if (!empty($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        return $builder->orderBy('created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Contar mensajes con filtros
     */
    public function countMessagesWithFilters($filters = [])
    {
        $builder = $this->builder();
        
        // Aplicar los mismos filtros que en getMessagesWithFilters
        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }
        
        if (!empty($filters['priority'])) {
            $builder->where('priority', $filters['priority']);
        }
        
        if (!empty($filters['assigned_to'])) {
            $builder->where('assigned_to', $filters['assigned_to']);
        }
        
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('name', $filters['search'])
                   ->orLike('email', $filters['search'])
                   ->orLike('subject', $filters['search'])
                   ->orLike('message', $filters['search'])
                   ->groupEnd();
        }
        
        if (!empty($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        return $builder->countAllResults();
    }

    /**
     * Obtener estadísticas de mensajes
     */
    public function getMessageStats()
    {
        $stats = [];
        
        // Total de mensajes
        $stats['total'] = $this->countAll();
        
        // Mensajes por estado
        $statusStats = $this->select('status, COUNT(*) as count')
                           ->groupBy('status')
                           ->get()
                           ->getResultArray();
        
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = $stat['count'];
        }
        
        // Mensajes por prioridad
        $priorityStats = $this->select('priority, COUNT(*) as count')
                             ->groupBy('priority')
                             ->get()
                             ->getResultArray();
        
        foreach ($priorityStats as $stat) {
            $stats['by_priority'][$stat['priority']] = $stat['count'];
        }
        
        // Mensajes de hoy
        $stats['today'] = $this->where('DATE(created_at)', date('Y-m-d'))->countAllResults();
        
        // Mensajes de esta semana
        $stats['this_week'] = $this->where('created_at >=', date('Y-m-d', strtotime('-7 days')))->countAllResults();
        
        // Mensajes pendientes
        $stats['pending'] = $this->where('status', 'pending')->countAllResults();
        
        return $stats;
    }

    /**
     * Marcar mensaje como leído
     */
    public function markAsRead($id, $adminId = null)
    {
        $data = ['status' => 'read'];
        
        if ($adminId) {
            $data['assigned_to'] = $adminId;
        }
        
        return $this->update($id, $data);
    }

    /**
     * Marcar mensaje como respondido
     */
    public function markAsReplied($id, $adminId)
    {
        return $this->update($id, [
            'status' => 'replied',
            'replied_at' => date('Y-m-d H:i:s'),
            'replied_by' => $adminId
        ]);
    }

    /**
     * Obtener mensajes recientes
     */
    public function getRecentMessages($limit = 10)
    {
        return $this->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->get()
                   ->getResultArray();
    }

    /**
     * Buscar mensajes por email
     */
    public function getMessagesByEmail($email, $limit = 10)
    {
        return $this->where('email', $email)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->get()
                   ->getResultArray();
    }
}
