<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductVariantModel extends Model
{
    protected $table            = 'product_variants';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'product_id',
        'name',
        'sku',
        'description',
        'price_regular',
        'price_sale',
        'stock_quantity',
        'stock_min',
        'featured_image',
        'gallery_images',
        'is_active',
        'is_default',
        'sort_order',
        'attributes'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'product_id' => 'required|integer|is_not_unique[products.id]',
        'name' => 'required|min_length[2]|max_length[255]',
        'sku' => 'required|min_length[2]|max_length[100]|is_unique[product_variants.sku,id,{id}]',
        'price_regular' => 'required|decimal|greater_than[0]',
        'price_sale' => 'permit_empty|decimal|greater_than[0]',
        'stock_quantity' => 'permit_empty|integer|greater_than_equal_to[0]',
        'stock_min' => 'permit_empty|integer|greater_than_equal_to[0]',
        'sort_order' => 'permit_empty|integer|greater_than_equal_to[0]'
    ];

    protected $validationMessages = [
        'product_id' => [
            'required' => 'El ID del producto es requerido',
            'integer' => 'El ID del producto debe ser un número entero',
            'is_not_unique' => 'El producto especificado no existe'
        ],
        'name' => [
            'required' => 'El nombre de la variante es requerido',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 255 caracteres'
        ],
        'sku' => [
            'required' => 'El SKU de la variante es requerido',
            'is_unique' => 'Este SKU ya está en uso por otra variante'
        ],
        'price_regular' => [
            'required' => 'El precio regular es requerido',
            'decimal' => 'El precio debe ser un número decimal válido',
            'greater_than' => 'El precio debe ser mayor a 0'
        ],
        'price_sale' => [
            'decimal' => 'El precio de oferta debe ser un número decimal válido',
            'greater_than' => 'El precio de oferta debe ser mayor a 0'
        ],
        'stock_quantity' => [
            'integer' => 'La cantidad de stock debe ser un número entero',
            'greater_than_equal_to' => 'La cantidad de stock debe ser mayor o igual a 0'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['beforeInsert'];
    protected $afterInsert    = ['afterInsert'];
    protected $beforeUpdate   = ['beforeUpdate'];
    protected $afterUpdate    = ['afterUpdate'];
    protected $beforeDelete   = ['beforeDelete'];
    protected $afterDelete    = ['afterDelete'];

    /**
     * Obtener todas las variantes de un producto
     */
    public function getVariantsByProduct($productId, $activeOnly = true)
    {
        $builder = $this->where('product_id', $productId);
        
        if ($activeOnly) {
            $builder->where('is_active', 1);
        }
        
        return $builder->orderBy('sort_order', 'ASC')
                      ->orderBy('name', 'ASC')
                      ->findAll();
    }

    /**
     * Obtener la variante por defecto de un producto
     */
    public function getDefaultVariant($productId)
    {
        return $this->where('product_id', $productId)
                   ->where('is_default', 1)
                   ->where('is_active', 1)
                   ->first();
    }

    /**
     * Obtener variante por SKU
     */
    public function getVariantBySku($sku)
    {
        return $this->where('sku', $sku)
                   ->where('is_active', 1)
                   ->first();
    }

    /**
     * Establecer variante por defecto
     */
    public function setDefaultVariant($variantId, $productId)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Quitar default de todas las variantes del producto
            $this->where('product_id', $productId)
                 ->set('is_default', 0)
                 ->update();

            // Establecer la nueva variante por defecto
            $this->update($variantId, ['is_default' => 1]);

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Callback antes de insertar
     */
    protected function beforeInsert(array $data)
    {
        // Si es la primera variante del producto, hacerla por defecto
        if (!isset($data['data']['is_default'])) {
            $existingVariants = $this->where('product_id', $data['data']['product_id'])->countAllResults();
            if ($existingVariants == 0) {
                $data['data']['is_default'] = 1;
            }
        }

        return $data;
    }

    /**
     * Callback después de insertar
     */
    protected function afterInsert(array $data)
    {
        // Actualizar el campo has_variants del producto padre
        $this->updateProductHasVariants($data['data']['product_id']);
        return $data;
    }

    /**
     * Callback antes de actualizar
     */
    protected function beforeUpdate(array $data)
    {
        return $data;
    }

    /**
     * Callback después de actualizar
     */
    protected function afterUpdate(array $data)
    {
        return $data;
    }

    /**
     * Callback antes de eliminar
     */
    protected function beforeDelete(array $data)
    {
        return $data;
    }

    /**
     * Callback después de eliminar
     */
    protected function afterDelete(array $data)
    {
        // Si se eliminó una variante, verificar si el producto aún tiene variantes
        if (isset($data['data']['product_id'])) {
            $this->updateProductHasVariants($data['data']['product_id']);
        }
        return $data;
    }

    /**
     * Actualizar el campo has_variants del producto padre
     */
    private function updateProductHasVariants($productId)
    {
        $variantCount = $this->where('product_id', $productId)->countAllResults();
        $hasVariants = $variantCount > 0 ? 1 : 0;

        $productModel = new \App\Models\ProductModel();
        $productModel->update($productId, ['has_variants' => $hasVariants]);
    }

    /**
     * Procesar imágenes de galería
     */
    public function processGalleryImages($images)
    {
        if (is_array($images)) {
            return json_encode($images);
        }
        return $images;
    }

    /**
     * Obtener imágenes de galería como array
     */
    public function getGalleryImagesArray($galleryImages)
    {
        if (is_string($galleryImages)) {
            return json_decode($galleryImages, true) ?: [];
        }
        return is_array($galleryImages) ? $galleryImages : [];
    }
}
