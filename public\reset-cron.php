<?php
/**
 * SCRIPT PARA RESETEAR EL CRON Y LIMPIAR EJECUCIONES
 */

echo "🧹 RESETEANDO CRON DE ALERTAS\n";
echo "=============================\n\n";

try {
    // Configuración de la base de datos
    $host = '**************';
    $dbname = 'mayansourcecom_mrcell';
    $username = 'mayansourcecom_mrcell';
    $password = 'Clairo!23';
    
    echo "🔗 Conectando a la base de datos...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conectado exitosamente\n\n";
    
    // Limpiar ejecuciones anteriores
    echo "🗑️ Limpiando ejecuciones anteriores...\n";
    $stmt = $pdo->exec("DELETE FROM cron_executions WHERE task_name = 'scheduled_alerts'");
    echo "✅ Eliminadas $stmt ejecuciones anteriores\n\n";
    
    // Mostrar configuración actual
    echo "📱 Configuración actual de WhatsApp:\n";
    $stmt = $pdo->query("
        SELECT setting_value FROM system_settings 
        WHERE setting_key = 'whatsapp_alerts_group' 
        LIMIT 1
    ");
    $groupSetting = $stmt->fetch(PDO::FETCH_ASSOC);
    $groupNumber = $groupSetting['setting_value'] ?? 'NO CONFIGURADO';
    echo "Grupo: $groupNumber\n\n";
    
    // Verificar datos para alertas
    echo "🔍 Estado actual de alertas:\n";
    
    // Productos con bajo stock
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND is_active = 1 
            AND deleted_at IS NULL
    ");
    $lowStock = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📦 Productos con bajo stock: {$lowStock['count']}\n";
    
    // Productos próximos a caducar
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND expiration_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ");
    $expiring = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "⏰ Productos próximos a caducar: {$expiring['count']}\n";
    
    // Pedidos pendientes
    $stmt = $pdo->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
            COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped
        FROM orders
    ");
    $orders = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📋 Pedidos pendientes: {$orders['pending']}\n";
    echo "🚚 Pedidos enviados: {$orders['shipped']}\n\n";
    
    echo "🎯 CRON RESETEADO EXITOSAMENTE\n";
    echo "==============================\n\n";
    
    echo "📋 PRÓXIMOS PASOS:\n";
    echo "1. Ejecutar: https://mrcell.com.gt/cron-simple.php\n";
    echo "2. El cron ahora se ejecutará SIEMPRE sin restricciones\n";
    echo "3. Cada ejecución enviará alertas si encuentra problemas\n\n";
    
    echo "🎉 ¡LISTO PARA USAR!\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n📅 Reset completado: " . date('Y-m-d H:i:s') . "\n";
?>
