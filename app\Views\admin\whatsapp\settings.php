<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-cog me-2"></i>Configuración WhatsApp</h1>
        <a href="/admin/whatsapp" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Volver
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <strong>Errores de validación:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-server me-2"></i>Configuración de API</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/whatsapp/settings">
                    <div class="mb-3">
                        <label for="api_url" class="form-label">URL de la API</label>
                        <input type="url" class="form-control" id="api_url" name="api_url" 
                               value="<?= esc($settings['api_url'] ?? 'http://167.114.111.52/api/sendMessage') ?>" 
                               required>
                        <div class="form-text">URL completa del endpoint de la API de WhatsApp</div>
                    </div>

                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" 
                                   value="<?= esc($settings['api_key'] ?? '') ?>" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Clave de autenticación para la API (X-API-Key)</div>
                    </div>

                    <div class="mb-3">
                        <label for="device_token" class="form-label">Device Token</label>
                        <input type="text" class="form-control" id="device_token" name="device_token" 
                               value="<?= esc($settings['device_token'] ?? '') ?>" required>
                        <div class="form-text">Token del dispositivo WhatsApp configurado</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enabled" name="enabled" value="1"
                                   <?= (isset($settings['enabled']) && $settings['enabled'] === '1') ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enabled">
                                <strong>Habilitar notificaciones WhatsApp</strong>
                            </label>
                        </div>
                        <div class="form-text">Cuando está deshabilitado, no se enviarán mensajes</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Configuración
                        </button>
                        <a href="/admin/whatsapp/test-connection" class="btn btn-outline-success">
                            <i class="fas fa-plug me-2"></i>Probar Conexión
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Estado Actual -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Estado Actual</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <?php if (isset($settings['enabled']) && $settings['enabled'] === '1'): ?>
                        <span class="badge bg-success me-2">Activo</span>
                        <span class="text-success">Servicio habilitado</span>
                    <?php else: ?>
                        <span class="badge bg-danger me-2">Inactivo</span>
                        <span class="text-danger">Servicio deshabilitado</span>
                    <?php endif; ?>
                </div>

                <hr>

                <div class="mb-2">
                    <small class="text-muted">API URL:</small><br>
                    <code class="small"><?= esc(substr($settings['api_url'] ?? '', 0, 30)) ?>...</code>
                </div>

                <div class="mb-2">
                    <small class="text-muted">API Key:</small><br>
                    <code class="small">
                        <?= !empty($settings['api_key']) ? str_repeat('*', 20) . substr($settings['api_key'], -4) : 'No configurada' ?>
                    </code>
                </div>

                <div class="mb-2">
                    <small class="text-muted">Device Token:</small><br>
                    <code class="small"><?= esc($settings['device_token'] ?? 'No configurado') ?></code>
                </div>
            </div>
        </div>

        <!-- Información de Ayuda -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>Ayuda</h6>
            </div>
            <div class="card-body">
                <h6>Configuración de API</h6>
                <p class="small text-muted">
                    Para configurar correctamente la integración de WhatsApp:
                </p>
                <ul class="small text-muted">
                    <li><strong>URL de API:</strong> Debe ser la URL completa del endpoint</li>
                    <li><strong>API Key:</strong> Clave única de autenticación proporcionada</li>
                    <li><strong>Device Token:</strong> Token del dispositivo WhatsApp configurado</li>
                </ul>

                <hr>

                <h6>Tipos de Mensaje</h6>
                <ul class="small text-muted">
                    <li><strong>1:</strong> Mensaje de texto</li>
                    <li><strong>2:</strong> Archivo</li>
                    <li><strong>3:</strong> Ubicación</li>
                    <li><strong>4:</strong> Audio</li>
                </ul>

                <hr>

                <div class="text-center">
                    <a href="/admin/whatsapp/test-connection" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-vial me-1"></i>Probar Configuración
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Toggle para mostrar/ocultar API Key
document.getElementById('toggleApiKey').addEventListener('click', function() {
    const apiKeyInput = document.getElementById('api_key');
    const icon = this.querySelector('i');
    
    if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        apiKeyInput.type = 'password';
        icon.className = 'fas fa-eye';
    }
});

// Validación en tiempo real
document.getElementById('api_url').addEventListener('blur', function() {
    const url = this.value;
    if (url && !url.startsWith('http')) {
        this.classList.add('is-invalid');
        if (!this.nextElementSibling || !this.nextElementSibling.classList.contains('invalid-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'La URL debe comenzar con http:// o https://';
            this.parentNode.appendChild(feedback);
        }
    } else {
        this.classList.remove('is-invalid');
        const feedback = this.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
});

// Validación de API Key
document.getElementById('api_key').addEventListener('input', function() {
    if (this.value.length < 10) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// Validación de Device Token
document.getElementById('device_token').addEventListener('input', function() {
    if (this.value.length < 3) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});
</script>
<?= $this->endSection() ?>
