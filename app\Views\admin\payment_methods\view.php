<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1>
            <i class="<?= esc($paymentMethod['icon'] ?? 'fas fa-credit-card') ?> me-2"></i>
            <?= esc($paymentMethod['name']) ?>
        </h1>
        <p class="text-muted mb-0"><?= esc($paymentMethod['description']) ?></p>
    </div>
    <div>
        <a href="/admin/payment-methods" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row">
    <!-- Información General -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información General</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Estado:</strong>
                    <?php if ($paymentMethod['is_active']): ?>
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-check me-1"></i>Activo
                        </span>
                    <?php else: ?>
                        <span class="badge bg-danger ms-2">
                            <i class="fas fa-times me-1"></i>Inactivo
                        </span>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <strong>Tipo:</strong>
                    <?php
                    $typeLabels = [
                        'bank_transfer' => 'Transferencia Bancaria',
                        'cash' => 'Pago en Efectivo',
                        'credit_card' => 'Tarjeta de Crédito/Débito',
                        'digital_wallet' => 'Billetera Digital',
                        'other' => 'Otro'
                    ];
                    echo '<span class="ms-2">' . ($typeLabels[$paymentMethod['type']] ?? 'Desconocido') . '</span>';
                    ?>
                </div>
                
                <?php if (!empty($paymentMethod['instructions'])): ?>
                    <div class="mb-3">
                        <strong>Instrucciones:</strong>
                        <p class="mt-2 text-muted"><?= nl2br(esc($paymentMethod['instructions'])) ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="d-grid gap-2">
                    <a href="/admin/payment-methods/toggle-status/<?= $paymentMethod['id'] ?>" 
                       class="btn btn-<?= $paymentMethod['is_active'] ? 'danger' : 'success' ?>"
                       onclick="return confirm('¿Estás seguro de cambiar el estado de este método de pago?')">
                        <i class="fas fa-<?= $paymentMethod['is_active'] ? 'times' : 'check' ?> me-2"></i>
                        <?= $paymentMethod['is_active'] ? 'Desactivar' : 'Activar' ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Cuentas Bancarias (solo para transferencias) -->
    <?php if ($paymentMethod['type'] === 'bank_transfer'): ?>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-university me-2"></i>Cuentas Bancarias</h5>
                    <a href="/admin/payment-methods/add-bank-account/<?= $paymentMethod['id'] ?>" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i>Agregar Cuenta
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($bankAccounts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-university fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No hay cuentas bancarias configuradas</h6>
                            <p class="text-muted">Agrega la primera cuenta bancaria para este método de pago</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($bankAccounts as $account): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-university me-2 text-primary"></i>
                                                <?= esc($account['bank_name']) ?>
                                            </h6>
                                            
                                            <div class="mb-2">
                                                <strong>Cuenta:</strong> <?= esc($account['account_number']) ?>
                                            </div>
                                            
                                            <div class="mb-2">
                                                <strong>Tipo:</strong> 
                                                <span class="badge bg-light text-dark">
                                                    <?= ucfirst(esc($account['account_type'])) ?>
                                                </span>
                                            </div>
                                            
                                            <div class="mb-2">
                                                <strong>Titular:</strong> <?= esc($account['account_holder']) ?>
                                            </div>
                                            
                                            <?php if (!empty($account['account_holder_id'])): ?>
                                                <div class="mb-2">
                                                    <strong>NIT/DPI:</strong> <?= esc($account['account_holder_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($account['instructions'])): ?>
                                                <div class="mb-2">
                                                    <strong>Instrucciones:</strong>
                                                    <small class="text-muted d-block">
                                                        <?= nl2br(esc($account['instructions'])) ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="d-flex gap-2 mt-3">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="editBankAccount(<?= $account['id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteBankAccount(<?= $account['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Puntos de Recogida (solo para efectivo) -->
    <?php if ($paymentMethod['type'] === 'cash'): ?>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Puntos de Recogida</h5>
                    <a href="/admin/payment-methods/add-pickup-location/<?= $paymentMethod['id'] ?>" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-2"></i>Agregar Punto
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($pickupLocations)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No hay puntos de recogida configurados</h6>
                            <p class="text-muted">Agrega el primer punto de recogida para este método de pago</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($pickupLocations as $location): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-map-marker-alt me-2 text-success"></i>
                                                <?= esc($location['name']) ?>
                                            </h6>
                                            
                                            <div class="mb-2">
                                                <strong>Dirección:</strong>
                                                <small class="text-muted d-block"><?= esc($location['address']) ?></small>
                                            </div>
                                            
                                            <?php if (!empty($location['phone'])): ?>
                                                <div class="mb-2">
                                                    <strong>Teléfono:</strong> 
                                                    <a href="tel:<?= esc($location['phone']) ?>"><?= esc($location['phone']) ?></a>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($location['schedule'])): ?>
                                                <div class="mb-2">
                                                    <strong>Horario:</strong>
                                                    <small class="text-muted d-block"><?= esc($location['schedule']) ?></small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="mb-2">
                                                <strong>Costo de entrega:</strong> 
                                                <span class="badge bg-info">Q<?= number_format($location['delivery_fee'], 2) ?></span>
                                            </div>
                                            
                                            <?php if (!empty($location['coverage_zones'])): ?>
                                                <div class="mb-2">
                                                    <strong>Zonas de cobertura:</strong>
                                                    <small class="text-muted d-block"><?= esc($location['coverage_zones']) ?></small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="d-flex gap-2 mt-3">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="editPickupLocation(<?= $location['id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deletePickupLocation(<?= $location['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function editBankAccount(id) {
    window.location.href = '/admin/payment-methods/edit-bank-account/' + id;
}

function deleteBankAccount(id) {
    if (confirm('¿Estás seguro de eliminar esta cuenta bancaria? Esta acción no se puede deshacer.')) {
        window.location.href = '/admin/payment-methods/delete-bank-account/' + id;
    }
}

function editPickupLocation(id) {
    window.location.href = '/admin/payment-methods/edit-pickup-location/' + id;
}

function deletePickupLocation(id) {
    if (confirm('¿Estás seguro de eliminar este punto de recogida? Esta acción no se puede deshacer.')) {
        window.location.href = '/admin/payment-methods/delete-pickup-location/' + id;
    }
}
</script>
<?= $this->endSection() ?>
