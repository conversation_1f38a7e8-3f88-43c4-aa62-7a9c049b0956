<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddCheckoutFieldsToOrders extends Migration
{
    public function up()
    {
        // Add new fields to orders table
        $fields = [
            'customer_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'user_id'
            ],
            'customer_email' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'customer_name'
            ],
            'customer_phone' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
                'after' => 'customer_email'
            ],
            'shipping_city' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'shipping_address'
            ],
            'shipping_department' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'shipping_city'
            ],
            'shipping_method' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'after' => 'shipping_department'
            ],
            'shipping_notes' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'shipping_method'
            ],
            'tracking_number' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'notes'
            ],
            'shipped_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'after' => 'tracking_number'
            ],
            'delivered_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'after' => 'shipped_at'
            ]
        ];

        $this->forge->addColumn('orders', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('orders', [
            'customer_name',
            'customer_email',
            'customer_phone',
            'shipping_city',
            'shipping_department',
            'shipping_method',
            'shipping_notes',
            'tracking_number',
            'shipped_at',
            'delivered_at'
        ]);
    }
}
