<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\PriceMonitor;
use App\Libraries\WhatsAppNotifier;
use App\Libraries\EmailNotifier;

/**
 * Comando para monitoreo automático de precios
 * Ejecuta el sistema de alertas y notificaciones
 * 
 * Uso: php spark monitor:prices [options]
 */
class MonitorPrices extends BaseCommand
{
    protected $group       = 'MrCell';
    protected $name        = 'monitor:prices';
    protected $description = 'Monitoreo automático de precios y envío de alertas';
    protected $usage       = 'monitor:prices [options]';
    protected $arguments   = [];
    protected $options     = [
        '--dry-run' => 'Mostrar qué se haría sin ejecutar',
        '--force' => 'Forzar ejecución aunque esté deshabilitado',
        '--batch-size' => 'Tamaño del lote de productos a procesar',
        '--test-notifications' => 'Enviar notificaciones de prueba',
        '--stats' => 'Mostrar estadísticas del monitor',
        '--cleanup' => 'Limpiar datos antiguos',
        '--verbose' => 'Mostrar información detallada'
    ];

    public function run(array $params)
    {
        CLI::write('💰 MONITOR DE PRECIOS - MrCell Guatemala', 'green');
        CLI::newLine();
        
        $dryRun = CLI::getOption('dry-run');
        $force = CLI::getOption('force');
        $batchSize = CLI::getOption('batch-size');
        $testNotifications = CLI::getOption('test-notifications');
        $showStats = CLI::getOption('stats');
        $cleanup = CLI::getOption('cleanup');
        $verbose = CLI::getOption('verbose');
        
        $monitor = new PriceMonitor();
        
        // Mostrar estadísticas si se solicita
        if ($showStats) {
            $this->showStats($monitor);
            return;
        }
        
        // Limpiar datos antiguos si se solicita
        if ($cleanup) {
            $this->performCleanup();
            return;
        }
        
        // Probar notificaciones si se solicita
        if ($testNotifications) {
            $this->testNotifications();
            return;
        }
        
        // Verificar si está habilitado
        if (!$monitor->isEnabled() && !$force) {
            CLI::write('❌ El monitoreo de precios está deshabilitado.', 'red');
            CLI::write('   Usa --force para ejecutar de todas formas.', 'yellow');
            return;
        }
        
        // Mostrar configuración
        $this->showConfiguration($monitor, $verbose);
        
        if ($dryRun) {
            $this->showDryRun($monitor);
            return;
        }
        
        // Ejecutar monitoreo
        CLI::write('🔍 Iniciando monitoreo de precios...', 'yellow');
        CLI::newLine();
        
        $startTime = microtime(true);
        
        try {
            $results = $monitor->runPriceMonitoring();
            
            $this->displayResults($results, $verbose);
            
            // Mostrar resumen
            $executionTime = round(microtime(true) - $startTime, 2);
            CLI::newLine();
            CLI::write('✅ Monitoreo completado exitosamente!', 'green');
            CLI::write("⏱️  Tiempo total: {$executionTime} segundos", 'blue');
            
        } catch (\Exception $e) {
            CLI::write('❌ Error durante el monitoreo:', 'red');
            CLI::write('   ' . $e->getMessage(), 'red');
            
            if ($verbose) {
                CLI::newLine();
                CLI::write('Stack trace:', 'yellow');
                CLI::write($e->getTraceAsString(), 'red');
            }
        }
    }
    
    /**
     * Mostrar configuración actual
     */
    private function showConfiguration(PriceMonitor $monitor, bool $verbose = false)
    {
        $config = $monitor->getConfig();
        
        CLI::write('⚙️  Configuración:', 'blue');
        CLI::write("   Estado: " . ($config['enabled'] ? '✅ Habilitado' : '❌ Deshabilitado'));
        CLI::write("   Intervalo de verificación: {$config['check_interval']} segundos");
        CLI::write("   Cambio mínimo: Q{$config['min_price_change']}");
        CLI::write("   Porcentaje mínimo: {$config['min_percentage_change']}%");
        CLI::write("   WhatsApp: " . ($config['whatsapp_enabled'] ? '✅ Habilitado' : '❌ Deshabilitado'));
        CLI::write("   Email: " . ($config['email_enabled'] ? '✅ Habilitado' : '❌ Deshabilitado'));
        
        if ($verbose) {
            CLI::newLine();
            CLI::write('📊 Canales de notificación:', 'blue');
            
            $whatsapp = new WhatsAppNotifier();
            $whatsappConfig = $whatsapp->getConfig();
            CLI::write("   WhatsApp Provider: {$whatsappConfig['provider']}");
            CLI::write("   WhatsApp Configurado: " . ($whatsappConfig['provider_configured'] ? '✅' : '❌'));
            
            $email = new EmailNotifier();
            $emailConfig = $email->getConfig();
            CLI::write("   Email From: {$emailConfig['from_email']}");
            CLI::write("   Email Name: {$emailConfig['from_name']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Mostrar qué se haría en dry-run
     */
    private function showDryRun(PriceMonitor $monitor)
    {
        CLI::write('🔍 MODO DRY-RUN - Mostrando qué se ejecutaría:', 'yellow');
        CLI::newLine();
        
        try {
            // Simular obtención de productos con alertas
            $db = \Config\Database::connect();
            $builder = $db->table('wishlist w');
            $builder->select('COUNT(*) as count');
            $builder->join('products p', 'w.product_id = p.id');
            $builder->join('users u', 'w.user_id = u.id');
            $builder->where('w.notification_enabled', 1);
            $builder->where('w.price_alert_threshold IS NOT NULL');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');
            
            $result = $builder->get()->getRowArray();
            $productCount = $result['count'] ?? 0;
            
            CLI::write("📋 Productos con alertas activas: {$productCount}");
            
            if ($productCount > 0) {
                CLI::write('📋 Acciones que se ejecutarían:');
                CLI::write('  • Verificar cambios de precio en productos');
                CLI::write('  • Comparar con umbrales de alerta configurados');
                CLI::write('  • Enviar notificaciones por WhatsApp y Email');
                CLI::write('  • Actualizar historial de precios');
                CLI::write('  • Registrar notificaciones enviadas');
                CLI::write('  • Monitorear productos que volvieron a tener stock');
                CLI::write('  • Limpiar notificaciones antiguas');
            } else {
                CLI::write('ℹ️  No hay productos con alertas activas para procesar.');
            }
            
        } catch (\Exception $e) {
            CLI::write('❌ Error obteniendo información: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();
        CLI::write('💡 Para ejecutar realmente, quita la opción --dry-run', 'yellow');
    }
    
    /**
     * Mostrar resultados del monitoreo
     */
    private function displayResults(array $results, bool $verbose = false)
    {
        if (!$results['success']) {
            CLI::write('❌ El monitoreo falló:', 'red');
            CLI::write('   ' . ($results['error'] ?? 'Error desconocido'), 'red');
            return;
        }
        
        CLI::write('📊 RESULTADOS DEL MONITOREO:', 'yellow');
        CLI::newLine();
        
        CLI::write("🔍 Productos verificados: {$results['products_checked']}", 'white');
        CLI::write("📉 Cambios de precio detectados: {$results['price_changes_detected']}", 'white');
        CLI::write("📨 Notificaciones enviadas: {$results['notifications_sent']}", 'white');
        
        if (isset($results['stock_alerts_sent'])) {
            CLI::write("📦 Alertas de stock enviadas: {$results['stock_alerts_sent']}", 'white');
        }
        
        if ($results['errors'] > 0) {
            CLI::write("⚠️  Errores encontrados: {$results['errors']}", 'yellow');
        }
        
        CLI::write("⏱️  Tiempo de ejecución: {$results['execution_time']} segundos", 'blue');
        
        // Mostrar detalles si está en modo verbose
        if ($verbose && !empty($results['details'])) {
            CLI::newLine();
            CLI::write('📋 DETALLES:', 'blue');
            
            foreach ($results['details'] as $detail) {
                if (isset($detail['error'])) {
                    CLI::write("   ❌ Producto {$detail['product_id']}: {$detail['error']}", 'red');
                } else {
                    $productName = $detail['product_name'] ?? 'Producto ' . $detail['product_id'];
                    $oldPrice = number_format($detail['old_price'] ?? 0, 2);
                    $newPrice = number_format($detail['new_price'] ?? 0, 2);
                    
                    CLI::write("   ✅ {$productName}", 'green');
                    CLI::write("      Precio: Q{$oldPrice} → Q{$newPrice}", 'white');
                    
                    if (isset($detail['notification_result'])) {
                        $notif = $detail['notification_result'];
                        CLI::write("      Notificaciones: {$notif['notifications_sent']} enviadas", 'white');
                        
                        if (!empty($notif['errors'])) {
                            foreach ($notif['errors'] as $error) {
                                CLI::write("      ⚠️  {$error}", 'yellow');
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Mostrar estadísticas
     */
    private function showStats(PriceMonitor $monitor)
    {
        CLI::write('📊 ESTADÍSTICAS DEL MONITOR DE PRECIOS', 'green');
        CLI::newLine();
        
        $periods = [7, 30];
        
        foreach ($periods as $days) {
            $stats = $monitor->getStats($days);
            
            if (isset($stats['error'])) {
                CLI::write("❌ Error obteniendo estadísticas de {$days} días: {$stats['error']}", 'red');
                continue;
            }
            
            CLI::write("📅 Últimos {$days} días:", 'blue');
            CLI::write("   Total notificaciones: {$stats['total_notifications']}");
            CLI::write("   Alertas de precio: {$stats['price_alerts']}");
            CLI::write("   Alertas de stock: {$stats['stock_alerts']}");
            CLI::write("   Usuarios únicos: {$stats['unique_users']}");
            CLI::write("   Productos únicos: {$stats['unique_products']}");
            CLI::write("   Promedio por día: {$stats['avg_notifications_per_day']}");
            CLI::newLine();
        }
    }
    
    /**
     * Probar notificaciones
     */
    private function testNotifications()
    {
        CLI::write('🧪 PRUEBA DE NOTIFICACIONES', 'green');
        CLI::newLine();
        
        $testPhone = CLI::prompt('Número de WhatsApp para prueba (ej: +50212345678)');
        $testEmail = CLI::prompt('Email para prueba');
        
        if (empty($testPhone) && empty($testEmail)) {
            CLI::write('❌ Debes proporcionar al menos un número o email.', 'red');
            return;
        }
        
        CLI::write('📤 Enviando notificaciones de prueba...', 'yellow');
        
        // Probar WhatsApp
        if (!empty($testPhone)) {
            CLI::write('📱 Probando WhatsApp...', 'white');
            
            $whatsapp = new WhatsAppNotifier();
            if ($whatsapp->isEnabled()) {
                $result = $whatsapp->testConnection();
                
                if ($result['success']) {
                    CLI::write('   ✅ WhatsApp: Mensaje enviado correctamente', 'green');
                } else {
                    CLI::write('   ❌ WhatsApp: ' . ($result['error'] ?? 'Error desconocido'), 'red');
                }
            } else {
                CLI::write('   ⚠️  WhatsApp: No está habilitado', 'yellow');
            }
        }
        
        // Probar Email
        if (!empty($testEmail)) {
            CLI::write('📧 Probando Email...', 'white');
            
            $email = new EmailNotifier();
            if ($email->isEnabled()) {
                $result = $email->testEmail($testEmail);
                
                if ($result['success']) {
                    CLI::write('   ✅ Email: Mensaje enviado correctamente', 'green');
                } else {
                    CLI::write('   ❌ Email: ' . ($result['error'] ?? 'Error desconocido'), 'red');
                }
            } else {
                CLI::write('   ⚠️  Email: No está habilitado', 'yellow');
            }
        }
        
        CLI::newLine();
        CLI::write('✅ Prueba de notificaciones completada', 'green');
    }
    
    /**
     * Realizar limpieza de datos antiguos
     */
    private function performCleanup()
    {
        CLI::write('🧹 LIMPIEZA DE DATOS ANTIGUOS', 'green');
        CLI::newLine();
        
        try {
            $db = \Config\Database::connect();
            
            // Limpiar logs de notificaciones antiguos (más de 90 días)
            CLI::write('🗑️  Limpiando logs de notificaciones...', 'white');
            $cutoffDate = date('Y-m-d H:i:s', strtotime('-90 days'));
            
            $deleted = $db->table('notification_log')
                         ->where('created_at <', $cutoffDate)
                         ->delete();
            
            CLI::write("   ✅ {$deleted} registros eliminados", 'green');
            
            // Limpiar historial de precios antiguo (más de 1 año)
            CLI::write('🗑️  Limpiando historial de precios...', 'white');
            $cutoffDate = date('Y-m-d H:i:s', strtotime('-1 year'));
            
            $deleted = $db->table('price_history')
                         ->where('created_at <', $cutoffDate)
                         ->delete();
            
            CLI::write("   ✅ {$deleted} registros eliminados", 'green');
            
            // Optimizar tablas
            CLI::write('⚡ Optimizando tablas...', 'white');
            $db->query('OPTIMIZE TABLE notification_log, price_history');
            CLI::write('   ✅ Tablas optimizadas', 'green');
            
            CLI::newLine();
            CLI::write('✅ Limpieza completada exitosamente', 'green');
            
        } catch (\Exception $e) {
            CLI::write('❌ Error durante la limpieza: ' . $e->getMessage(), 'red');
        }
    }
}
