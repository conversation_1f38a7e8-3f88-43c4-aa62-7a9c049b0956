<?php

namespace App\Services;

use App\Models\ProductModel;
use App\Services\RecurrenteService;

class ProductSyncService
{
    private $productModel;
    private $recurrenteService;

    public function __construct()
    {
        $this->productModel = new ProductModel();
        $this->recurrenteService = new RecurrenteService();
    }

    /**
     * Sincronizar producto con Recurrente al crearlo
     */
    public function syncProductCreate($productId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se sincroniza producto {$productId}");
                return false;
            }

            $product = $this->productModel->find($productId);
            if (!$product) {
                throw new \Exception("Producto {$productId} no encontrado");
            }

            // Preparar datos para Recurrente
            $productData = $this->prepareProductDataForRecurrente($product);
            
            // Crear producto en Recurrente
            $response = $this->recurrenteService->createProduct($productData);
            
            if (isset($response['id'])) {
                // Actualizar producto local con ID de Recurrente
                $updateData = [
                    'recurrente_product_id' => $response['id'],
                    'recurrente_synced_at' => date('Y-m-d H:i:s'),
                    'recurrente_sync_status' => 'synced'
                ];

                // Agregar storefront_link si está disponible
                if (isset($response['storefront_link'])) {
                    $updateData['recurrente_storefront_link'] = $response['storefront_link'];
                }

                $this->productModel->update($productId, $updateData);

                log_message('info', "Producto {$productId} sincronizado con Recurrente. ID: {$response['id']}");
                return true;
            } else {
                throw new \Exception('Respuesta inválida de Recurrente: ' . json_encode($response));
            }

        } catch (\Exception $e) {
            log_message('error', "Error sincronizando producto {$productId} con Recurrente: " . $e->getMessage());
            
            // Marcar como error en la base de datos
            $this->productModel->update($productId, [
                'recurrente_sync_status' => 'error',
                'recurrente_synced_at' => date('Y-m-d H:i:s')
            ]);
            
            return false;
        }
    }

    /**
     * Sincronizar producto con Recurrente al actualizarlo
     */
    public function syncProductUpdate($productId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se sincroniza producto {$productId}");
                return false;
            }

            $product = $this->productModel->find($productId);
            if (!$product) {
                throw new \Exception("Producto {$productId} no encontrado");
            }

            // Si no tiene ID de Recurrente, crear nuevo
            if (empty($product['recurrente_product_id'])) {
                return $this->syncProductCreate($productId);
            }

            // Preparar datos para Recurrente
            $productData = $this->prepareProductDataForRecurrente($product);
            
            // Actualizar producto en Recurrente
            $response = $this->recurrenteService->updateProduct($product['recurrente_product_id'], $productData);
            
            // Actualizar estado de sincronización
            $this->productModel->update($productId, [
                'recurrente_synced_at' => date('Y-m-d H:i:s'),
                'recurrente_sync_status' => 'synced'
            ]);

            log_message('info', "Producto {$productId} actualizado en Recurrente");
            return true;

        } catch (\Exception $e) {
            log_message('error', "Error actualizando producto {$productId} en Recurrente: " . $e->getMessage());
            
            // Marcar como error
            $this->productModel->update($productId, [
                'recurrente_sync_status' => 'error',
                'recurrente_synced_at' => date('Y-m-d H:i:s')
            ]);
            
            return false;
        }
    }

    /**
     * Eliminar producto de Recurrente
     */
    public function syncProductDelete($productId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se elimina producto {$productId}");
                return false;
            }

            $product = $this->productModel->find($productId);
            if (!$product || empty($product['recurrente_product_id'])) {
                log_message('info', "Producto {$productId} no tiene ID de Recurrente, no se elimina");
                return false;
            }

            // Eliminar producto en Recurrente
            $this->recurrenteService->deleteProduct($product['recurrente_product_id']);
            
            log_message('info', "Producto {$productId} eliminado de Recurrente");
            return true;

        } catch (\Exception $e) {
            log_message('error', "Error eliminando producto {$productId} de Recurrente: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Preparar datos del producto para enviar a Recurrente
     */
    private function prepareProductDataForRecurrente($product)
    {
        try {
            // Log del producto para debug
            log_message('debug', 'Preparando producto ID: ' . $product['id'] . ' - ' . $product['name']);

            // Determinar precio a usar (sale si existe, sino regular)
            $price = !empty($product['price_sale']) && $product['price_sale'] > 0
                ? $product['price_sale']
                : $product['price_regular'];

            // Asegurar que el precio es numérico
            $price = is_numeric($price) ? floatval($price) : 0;

            // Construir URL de imagen con la URL de producción correcta
            $imageUrl = null;
            if (!empty($product['featured_image'])) {
                $productionUrl = 'https://mrcell.com.gt';

                // Asegurar que featured_image es string
                $featuredImage = is_array($product['featured_image'])
                    ? (isset($product['featured_image'][0]) ? $product['featured_image'][0] : '')
                    : $product['featured_image'];

                if (!empty($featuredImage)) {
                    // Si ya es una URL completa, usarla tal como está
                    if (strpos($featuredImage, 'http') === 0) {
                        $imageUrl = $featuredImage;
                    }
                    // Si contiene la ruta relativa completa (assets/img/products/...)
                    elseif (strpos($featuredImage, 'assets/') === 0) {
                        $imageUrl = $productionUrl . '/' . $featuredImage;
                    }
                    // Si es solo el nombre del archivo
                    else {
                        $imageUrl = $productionUrl . '/assets/img/products/' . $featuredImage;
                    }

                    // Log de la URL de imagen generada
                    log_message('debug', "URL de imagen generada para producto {$product['id']}: {$imageUrl}");
                }
            }

            // Asegurar que todos los campos son strings o valores apropiados
            $description = $product['description'] ?? $product['short_description'] ?? '';
            if (is_array($description)) {
                $description = is_string($description[0] ?? '') ? $description[0] : '';
            }

            $name = is_array($product['name']) ? (string)($product['name'][0] ?? '') : (string)$product['name'];
            $sku = is_array($product['sku']) ? (string)($product['sku'][0] ?? '') : (string)$product['sku'];

            $productData = [
                'id' => $product['id'],
                'name' => $name,
                'description' => (string)$description,
                'sku' => $sku,
                'price' => $price,
                'stock_quantity' => is_numeric($product['stock_quantity']) ? intval($product['stock_quantity']) : null,
                'image_url' => $imageUrl
            ];

            log_message('debug', 'Datos preparados para producto ' . $product['id'] . ': ' . json_encode($productData));

            return $productData;

        } catch (\Exception $e) {
            log_message('error', 'Error preparando datos del producto ' . $product['id'] . ': ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Obtener productos que necesitan sincronización
     */
    public function getProductsNeedingSync()
    {
        return $this->productModel
            ->where('recurrente_sync_status', 'pending')
            ->orWhere('recurrente_sync_status', 'error')
            ->orWhere('recurrente_product_id IS NULL')
            ->where('is_active', 1)
            ->where('deleted_at IS NULL')
            ->findAll();
    }

    /**
     * Sincronizar todos los productos pendientes
     */
    public function syncAllPendingProducts()
    {
        $products = $this->getProductsNeedingSync();
        $synced = 0;
        $errors = 0;

        foreach ($products as $product) {
            if (empty($product['recurrente_product_id'])) {
                $result = $this->syncProductCreate($product['id']);
            } else {
                $result = $this->syncProductUpdate($product['id']);
            }

            if ($result) {
                $synced++;
            } else {
                $errors++;
            }
        }

        return [
            'total' => count($products),
            'synced' => $synced,
            'errors' => $errors
        ];
    }

    /**
     * Obtener todos los productos activos (para forzar actualización)
     */
    public function getAllActiveProducts()
    {
        return $this->productModel
            ->where('is_active', 1)
            ->where('deleted_at IS NULL')
            ->findAll();
    }

    /**
     * Forzar actualización de todos los productos (incluso los ya sincronizados)
     */
    public function forceUpdateAllProducts($limit = null)
    {
        $query = $this->productModel
            ->where('is_active', 1)
            ->where('deleted_at IS NULL');

        if ($limit) {
            $query->limit($limit);
        }

        $products = $query->findAll();
        $synced = 0;
        $errors = 0;

        foreach ($products as $product) {
            // Forzar actualización independientemente del estado
            if (empty($product['recurrente_product_id'])) {
                $result = $this->syncProductCreate($product['id']);
            } else {
                $result = $this->syncProductUpdate($product['id']);
            }

            if ($result) {
                $synced++;
            } else {
                $errors++;
            }
        }

        return [
            'total' => count($products),
            'synced' => $synced,
            'errors' => $errors
        ];
    }

    /**
     * Sincronizar un producto específico con Recurrente (método unificado)
     */
    public function syncSingleProduct($productId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se sincroniza producto {$productId}");
                return false;
            }

            $product = $this->productModel->find($productId);

            if (!$product) {
                log_message('error', "Producto no encontrado: {$productId}");
                return false;
            }

            // Verificar si el producto está activo
            if ($product['is_active'] != 1) {
                log_message('info', "Producto {$productId} no está activo, omitiendo sincronización");
                return false;
            }

            // Verificar límite de precio de Recurrente
            $price = !empty($product['price_sale']) && $product['price_sale'] > 0
                ? $product['price_sale']
                : $product['price_regular'];

            if (!$this->isWithinRecurrentePriceLimit($price, $product['currency'] ?? 'GTQ')) {
                // Marcar como disabled en lugar de error
                $this->productModel->update($productId, [
                    'recurrente_sync_status' => 'disabled',
                    'recurrente_synced_at' => date('Y-m-d H:i:s')
                ]);

                log_message('info', "Producto {$productId} deshabilitado para Recurrente por precio alto");
                return false;
            }

            // Determinar si es creación o actualización
            $isUpdate = !empty($product['recurrente_product_id']);

            if ($isUpdate) {
                return $this->syncProductUpdate($productId);
            } else {
                return $this->syncProductCreate($productId);
            }

        } catch (\Exception $e) {
            log_message('error', "Error en syncSingleProduct para producto {$productId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verificar si el precio está dentro del límite de Recurrente
     */
    private function isWithinRecurrentePriceLimit($amount, $currency = 'GTQ')
    {
        $maxPriceUSD = 15000; // Límite de Recurrente
        $amountInUSD = $amount;

        // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
        if ($currency === 'GTQ') {
            $amountInUSD = $amount / 7.8;
        }

        return $amountInUSD <= $maxPriceUSD;
    }

    /**
     * Eliminar un producto de Recurrente
     */
    public function deleteProductFromRecurrente($productId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se elimina producto {$productId}");
                return false;
            }

            $product = $this->productModel->find($productId);

            if (!$product) {
                log_message('error', "Producto no encontrado para eliminación: {$productId}");
                return false;
            }

            if (empty($product['recurrente_product_id'])) {
                log_message('info', "Producto {$productId} no tiene ID de Recurrente, omitiendo eliminación");
                return true; // No es un error, simplemente no está sincronizado
            }

            // Eliminar de Recurrente
            $response = $this->recurrenteService->deleteProduct($product['recurrente_product_id']);

            // Limpiar campos de Recurrente en la base de datos local
            $this->productModel->update($productId, [
                'recurrente_product_id' => null,
                'recurrente_sync_status' => 'disabled',
                'recurrente_synced_at' => date('Y-m-d H:i:s'),
                'recurrente_storefront_link' => null
            ]);

            log_message('info', "Producto {$productId} eliminado de Recurrente exitosamente");
            return true;

        } catch (\Exception $e) {
            log_message('error', "Error eliminando producto {$productId} de Recurrente: " . $e->getMessage());

            // Marcar como error en la base de datos local
            try {
                $this->productModel->update($productId, [
                    'recurrente_sync_status' => 'error',
                    'recurrente_synced_at' => date('Y-m-d H:i:s')
                ]);
            } catch (\Exception $updateError) {
                log_message('error', "Error actualizando estado de eliminación: " . $updateError->getMessage());
            }

            return false;
        }
    }

    /**
     * Limpiar productos eliminados de Recurrente (utilidad de mantenimiento)
     */
    public function cleanupDeletedProducts()
    {
        try {
            // Obtener productos que fueron eliminados localmente pero aún tienen ID de Recurrente
            $deletedProducts = $this->productModel->onlyDeleted()
                                                  ->where('recurrente_product_id IS NOT NULL')
                                                  ->where('recurrente_sync_status !=', 'deleted')
                                                  ->findAll();

            $cleaned = 0;
            $errors = 0;

            foreach ($deletedProducts as $product) {
                try {
                    $response = $this->recurrenteService->deleteProduct($product['recurrente_product_id']);

                    // Actualizar estado
                    $this->productModel->update($product['id'], [
                        'recurrente_product_id' => null,
                        'recurrente_sync_status' => 'disabled',
                        'recurrente_synced_at' => date('Y-m-d H:i:s'),
                        'recurrente_storefront_link' => null
                    ]);

                    $cleaned++;
                    log_message('info', "Producto eliminado {$product['id']} limpiado de Recurrente");

                } catch (\Exception $e) {
                    $errors++;
                    log_message('error', "Error limpiando producto eliminado {$product['id']}: " . $e->getMessage());
                }

                // Pausa para no sobrecargar la API
                usleep(500000); // 0.5 segundos
            }

            return [
                'total' => count($deletedProducts),
                'cleaned' => $cleaned,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            log_message('error', "Error en cleanupDeletedProducts: " . $e->getMessage());
            throw $e;
        }
    }
}
