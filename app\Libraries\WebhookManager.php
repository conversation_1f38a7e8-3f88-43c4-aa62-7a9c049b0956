<?php

namespace App\Libraries;

/**
 * Gestor de Webhooks
 * Sistema completo de webhooks para integraciones externas
 */
class WebhookManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('WEBHOOKS_ENABLED', true),
            'max_retries' => env('WEBHOOK_MAX_RETRIES', 3),
            'retry_delay' => env('WEBHOOK_RETRY_DELAY', 60), // segundos
            'timeout' => env('WEBHOOK_TIMEOUT', 30),
            'verify_ssl' => env('WEBHOOK_VERIFY_SSL', true),
            'signature_header' => env('WEBHOOK_SIGNATURE_HEADER', 'X-Webhook-Signature'),
            'user_agent' => env('WEBHOOK_USER_AGENT', 'MrCell-Webhook/1.0'),
            'queue_enabled' => env('WEBHOOK_QUEUE_ENABLED', true),
            'batch_size' => env('WEBHOOK_BATCH_SIZE', 10)
        ];
        
        $this->createWebhookTables();
    }
    
    /**
     * Registrar webhook
     */
    public function registerWebhook(array $webhookData): array
    {
        try {
            $data = [
                'name' => $webhookData['name'],
                'url' => $webhookData['url'],
                'events' => json_encode($webhookData['events'] ?? []),
                'secret' => $webhookData['secret'] ?? $this->generateSecret(),
                'is_active' => $webhookData['is_active'] ?? 1,
                'headers' => json_encode($webhookData['headers'] ?? []),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $webhookId = $this->db->table('webhooks')->insert($data);
            
            if ($webhookId) {
                $this->logger->info("Webhook registered", [
                    'webhook_id' => $webhookId,
                    'name' => $data['name'],
                    'url' => $data['url']
                ]);
                
                return [
                    'success' => true,
                    'webhook_id' => $webhookId,
                    'secret' => $data['secret']
                ];
            }
            
            return [
                'success' => false,
                'error' => 'Failed to register webhook'
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook registration error: " . $e->getMessage(), $webhookData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Disparar webhook
     */
    public function triggerWebhook(string $event, array $payload, ?int $webhookId = null): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'Webhooks disabled'];
        }
        
        try {
            // Obtener webhooks que escuchan este evento
            $webhooks = $this->getWebhooksForEvent($event, $webhookId);
            
            if (empty($webhooks)) {
                return [
                    'success' => true,
                    'message' => 'No webhooks found for event',
                    'triggered' => 0
                ];
            }
            
            $results = [
                'success' => true,
                'triggered' => 0,
                'failed' => 0,
                'queued' => 0,
                'details' => []
            ];
            
            foreach ($webhooks as $webhook) {
                if ($this->config['queue_enabled']) {
                    // Agregar a cola
                    $this->queueWebhook($webhook, $event, $payload);
                    $results['queued']++;
                } else {
                    // Ejecutar inmediatamente
                    $result = $this->executeWebhook($webhook, $event, $payload);
                    
                    if ($result['success']) {
                        $results['triggered']++;
                    } else {
                        $results['failed']++;
                    }
                    
                    $results['details'][] = [
                        'webhook_id' => $webhook['id'],
                        'name' => $webhook['name'],
                        'success' => $result['success'],
                        'response_code' => $result['response_code'] ?? null,
                        'error' => $result['error'] ?? null
                    ];
                }
            }
            
            $this->logger->info("Webhooks triggered for event: $event", $results);
            
            return $results;
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook trigger error: " . $e->getMessage(), [
                'event' => $event,
                'payload' => $payload
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Ejecutar webhook
     */
    public function executeWebhook(array $webhook, string $event, array $payload): array
    {
        try {
            $webhookPayload = [
                'event' => $event,
                'timestamp' => date('c'),
                'data' => $payload,
                'webhook_id' => $webhook['id']
            ];
            
            // Generar firma
            $signature = $this->generateSignature($webhookPayload, $webhook['secret']);
            
            // Preparar headers
            $headers = array_merge(
                json_decode($webhook['headers'] ?? '[]', true),
                [
                    'Content-Type: application/json',
                    'User-Agent: ' . $this->config['user_agent'],
                    $this->config['signature_header'] . ': ' . $signature
                ]
            );
            
            // Ejecutar request HTTP
            $startTime = microtime(true);
            $response = $this->sendHttpRequest($webhook['url'], $webhookPayload, $headers);
            $duration = microtime(true) - $startTime;
            
            // Registrar ejecución
            $this->logWebhookExecution($webhook['id'], $event, $response, $duration);
            
            if ($response['success']) {
                $this->logger->debug("Webhook executed successfully", [
                    'webhook_id' => $webhook['id'],
                    'event' => $event,
                    'response_code' => $response['http_code'],
                    'duration' => $duration
                ]);
                
                return [
                    'success' => true,
                    'response_code' => $response['http_code'],
                    'response_body' => $response['body'],
                    'duration' => $duration
                ];
            } else {
                $this->logger->warning("Webhook execution failed", [
                    'webhook_id' => $webhook['id'],
                    'event' => $event,
                    'error' => $response['error'],
                    'response_code' => $response['http_code']
                ]);
                
                // Programar reintento si es necesario
                $this->scheduleRetry($webhook, $event, $payload);
                
                return [
                    'success' => false,
                    'error' => $response['error'],
                    'response_code' => $response['http_code']
                ];
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook execution error: " . $e->getMessage(), [
                'webhook_id' => $webhook['id'],
                'event' => $event
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Procesar cola de webhooks
     */
    public function processWebhookQueue(): array
    {
        try {
            $queuedWebhooks = $this->db->table('webhook_queue')
                                      ->where('status', 'pending')
                                      ->where('scheduled_at <=', date('Y-m-d H:i:s'))
                                      ->orderBy('created_at', 'ASC')
                                      ->limit($this->config['batch_size'])
                                      ->get()
                                      ->getResultArray();
            
            $results = [
                'processed' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            foreach ($queuedWebhooks as $queueItem) {
                try {
                    // Marcar como procesando
                    $this->db->table('webhook_queue')
                            ->where('id', $queueItem['id'])
                            ->update(['status' => 'processing']);
                    
                    // Obtener webhook
                    $webhook = $this->db->table('webhooks')
                                       ->where('id', $queueItem['webhook_id'])
                                       ->where('is_active', 1)
                                       ->get()
                                       ->getRowArray();
                    
                    if (!$webhook) {
                        throw new \Exception("Webhook not found or inactive");
                    }
                    
                    // Ejecutar webhook
                    $payload = json_decode($queueItem['payload'], true);
                    $result = $this->executeWebhook($webhook, $queueItem['event'], $payload);
                    
                    if ($result['success']) {
                        // Marcar como completado
                        $this->db->table('webhook_queue')
                                ->where('id', $queueItem['id'])
                                ->update([
                                    'status' => 'completed',
                                    'completed_at' => date('Y-m-d H:i:s'),
                                    'response_code' => $result['response_code']
                                ]);
                        
                        $results['processed']++;
                    } else {
                        // Manejar fallo
                        $this->handleQueueItemFailure($queueItem, $result['error']);
                        $results['failed']++;
                    }
                    
                } catch (\Exception $e) {
                    $this->handleQueueItemFailure($queueItem, $e->getMessage());
                    $results['failed']++;
                    $results['errors'][] = $e->getMessage();
                }
            }
            
            if ($results['processed'] > 0 || $results['failed'] > 0) {
                $this->logger->info("Webhook queue processed", $results);
            }
            
            return $results;
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook queue processing error: " . $e->getMessage());
            
            return [
                'processed' => 0,
                'failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de webhooks
     */
    public function getWebhookStats(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Total de webhooks registrados
            $totalWebhooks = $this->db->table('webhooks')
                                     ->where('is_active', 1)
                                     ->countAllResults();
            
            // Ejecuciones por día
            $dailyExecutions = $this->db->query("
                SELECT DATE(created_at) as date, COUNT(*) as executions,
                       SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                       SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed
                FROM webhook_executions 
                WHERE created_at >= ? 
                GROUP BY DATE(created_at) 
                ORDER BY date
            ", [$dateFrom])->getResultArray();
            
            // Top eventos
            $topEvents = $this->db->query("
                SELECT event, COUNT(*) as count
                FROM webhook_executions 
                WHERE created_at >= ? 
                GROUP BY event 
                ORDER BY count DESC 
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Webhooks más activos
            $topWebhooks = $this->db->query("
                SELECT w.name, w.url, COUNT(we.id) as executions,
                       AVG(we.response_time) as avg_response_time
                FROM webhooks w
                LEFT JOIN webhook_executions we ON w.id = we.webhook_id
                WHERE we.created_at >= ?
                GROUP BY w.id, w.name, w.url
                ORDER BY executions DESC
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => [
                    'total_webhooks' => $totalWebhooks,
                    'daily_executions' => $dailyExecutions,
                    'top_events' => $topEvents,
                    'top_webhooks' => $topWebhooks
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener webhooks para un evento
     */
    private function getWebhooksForEvent(string $event, ?int $webhookId = null): array
    {
        $builder = $this->db->table('webhooks')
                           ->where('is_active', 1);
        
        if ($webhookId) {
            $builder->where('id', $webhookId);
        } else {
            $builder->where("JSON_CONTAINS(events, '\"$event\"')", null, false);
        }
        
        return $builder->get()->getResultArray();
    }
    
    /**
     * Agregar webhook a la cola
     */
    private function queueWebhook(array $webhook, string $event, array $payload): void
    {
        $this->db->table('webhook_queue')->insert([
            'webhook_id' => $webhook['id'],
            'event' => $event,
            'payload' => json_encode($payload),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s'),
            'scheduled_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Enviar request HTTP
     */
    private function sendHttpRequest(string $url, array $payload, array $headers): array
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['timeout'],
            CURLOPT_SSL_VERIFYPEER => $this->config['verify_ssl'],
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false) {
            return [
                'success' => false,
                'error' => $error ?: 'HTTP request failed',
                'http_code' => 0,
                'body' => null
            ];
        }
        
        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'http_code' => $httpCode,
            'body' => $response,
            'error' => $httpCode >= 400 ? "HTTP $httpCode" : null
        ];
    }
    
    /**
     * Generar firma para webhook
     */
    private function generateSignature(array $payload, string $secret): string
    {
        $data = json_encode($payload, JSON_UNESCAPED_SLASHES);
        return 'sha256=' . hash_hmac('sha256', $data, $secret);
    }
    
    /**
     * Generar secreto para webhook
     */
    private function generateSecret(): string
    {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Registrar ejecución de webhook
     */
    private function logWebhookExecution(int $webhookId, string $event, array $response, float $duration): void
    {
        try {
            $this->db->table('webhook_executions')->insert([
                'webhook_id' => $webhookId,
                'event' => $event,
                'success' => $response['success'] ? 1 : 0,
                'response_code' => $response['http_code'],
                'response_body' => substr($response['body'] ?? '', 0, 1000), // Limitar tamaño
                'response_time' => round($duration * 1000, 2), // en milisegundos
                'error_message' => $response['error'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Ignorar errores de logging
        }
    }
    
    /**
     * Programar reintento
     */
    private function scheduleRetry(array $webhook, string $event, array $payload): void
    {
        // Obtener número de reintentos actuales
        $retries = $this->db->table('webhook_queue')
                           ->where('webhook_id', $webhook['id'])
                           ->where('event', $event)
                           ->where('status', 'failed')
                           ->countAllResults();
        
        if ($retries < $this->config['max_retries']) {
            $delay = $this->config['retry_delay'] * pow(2, $retries); // Backoff exponencial
            
            $this->db->table('webhook_queue')->insert([
                'webhook_id' => $webhook['id'],
                'event' => $event,
                'payload' => json_encode($payload),
                'status' => 'pending',
                'retry_count' => $retries + 1,
                'created_at' => date('Y-m-d H:i:s'),
                'scheduled_at' => date('Y-m-d H:i:s', time() + $delay)
            ]);
        }
    }
    
    /**
     * Manejar fallo en item de cola
     */
    private function handleQueueItemFailure(array $queueItem, string $error): void
    {
        $this->db->table('webhook_queue')
                ->where('id', $queueItem['id'])
                ->update([
                    'status' => 'failed',
                    'error_message' => $error,
                    'failed_at' => date('Y-m-d H:i:s')
                ]);
    }
    
    /**
     * Crear tablas de webhooks
     */
    private function createWebhookTables(): void
    {
        try {
            // Tabla de webhooks
            $this->db->query("
                CREATE TABLE IF NOT EXISTS webhooks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    url VARCHAR(500) NOT NULL,
                    events JSON NOT NULL,
                    secret VARCHAR(255) NOT NULL,
                    headers JSON,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_is_active (is_active)
                )
            ");
            
            // Tabla de ejecuciones
            $this->db->query("
                CREATE TABLE IF NOT EXISTS webhook_executions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    webhook_id INT NOT NULL,
                    event VARCHAR(255) NOT NULL,
                    success TINYINT(1) NOT NULL,
                    response_code INT,
                    response_body TEXT,
                    response_time DECIMAL(10,2),
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_webhook_id (webhook_id),
                    INDEX idx_event (event),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (webhook_id) REFERENCES webhooks(id) ON DELETE CASCADE
                )
            ");
            
            // Tabla de cola
            $this->db->query("
                CREATE TABLE IF NOT EXISTS webhook_queue (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    webhook_id INT NOT NULL,
                    event VARCHAR(255) NOT NULL,
                    payload JSON NOT NULL,
                    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
                    retry_count INT DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    scheduled_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP NULL,
                    failed_at TIMESTAMP NULL,
                    INDEX idx_status (status),
                    INDEX idx_scheduled_at (scheduled_at),
                    INDEX idx_webhook_id (webhook_id),
                    FOREIGN KEY (webhook_id) REFERENCES webhooks(id) ON DELETE CASCADE
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'max_retries' => $this->config['max_retries'],
            'retry_delay' => $this->config['retry_delay'],
            'timeout' => $this->config['timeout'],
            'queue_enabled' => $this->config['queue_enabled'],
            'batch_size' => $this->config['batch_size']
        ];
    }
}
