<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateBillingTables extends Migration
{
    public function up()
    {
        // Tabla de configuración de facturación
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'api_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
            ],
            'api_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'company_nit' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => false,
            ],
            'company_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'company_address' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'company_phone' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'company_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'environment' => [
                'type' => 'ENUM',
                'constraint' => ['sandbox', 'production'],
                'null' => false,
                'default' => 'sandbox',
            ],
            'auto_generate' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'send_email' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 1,
            ],
            'timeout' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 30,
            ],
            'retry_attempts' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 3,
            ],
            'webhook_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'updated_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('environment');
        
        $this->forge->createTable('billing_config');

        // Tabla de facturas electrónicas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'order_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'invoice_number' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'external_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'series' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
            ],
            'folio' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'uuid' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'processing', 'generated', 'sent', 'failed', 'cancelled'],
                'null' => false,
                'default' => 'pending',
            ],
            'customer_nit' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'customer_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'customer_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'customer_address' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'subtotal' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
            ],
            'tax_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'default' => 0,
            ],
            'total_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
            ],
            'currency' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'GTQ',
            ],
            'items_data' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'xml_content' => [
                'type' => 'LONGTEXT',
                'null' => true,
            ],
            'pdf_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'xml_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'qr_code' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'certification_date' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'expiration_date' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'retry_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 0,
            ],
            'last_retry_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'sent_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('order_id');
        $this->forge->addKey('invoice_number');
        $this->forge->addKey('external_id');
        $this->forge->addKey('uuid');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('electronic_invoices');

        // Tabla de logs de facturación
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'order_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'action' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['success', 'error', 'warning', 'info'],
                'null' => false,
            ],
            'message' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'request_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'response_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'execution_time' => [
                'type' => 'DECIMAL',
                'constraint' => '8,4',
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('invoice_id');
        $this->forge->addKey('order_id');
        $this->forge->addKey(['action', 'status']);
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('billing_logs');

        // Tabla de configuración de Social Manager
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'api_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
                'default' => 'https://socialmanager.mayansource.com/api',
            ],
            'api_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'enabled_platforms' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'auto_publish' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'publish_new_products' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'publish_promotions' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 1,
            ],
            'default_hashtags' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'posting_schedule' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'content_templates' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'updated_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('is_active');
        
        $this->forge->createTable('social_manager_config');

        // Tabla de publicaciones programadas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'promotion_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'post_type' => [
                'type' => 'ENUM',
                'constraint' => ['product', 'promotion', 'custom'],
                'null' => false,
            ],
            'platforms' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'content' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'images' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'hashtags' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'scheduled_for' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'processing', 'published', 'failed', 'cancelled'],
                'null' => false,
                'default' => 'pending',
            ],
            'published_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'external_ids' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['post_type', 'status']);
        $this->forge->addKey('scheduled_for');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('scheduled_posts');
    }

    public function down()
    {
        $this->forge->dropTable('scheduled_posts');
        $this->forge->dropTable('social_manager_config');
        $this->forge->dropTable('billing_logs');
        $this->forge->dropTable('electronic_invoices');
        $this->forge->dropTable('billing_config');
    }
}
