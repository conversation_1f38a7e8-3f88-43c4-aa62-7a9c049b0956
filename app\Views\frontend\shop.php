<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    /* Product Cards */
    .product-card {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        height: 100%;
        background: var(--white-color);
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        border-color: var(--primary-light);
    }

    .product-image {
        height: 250px;
        object-fit: cover;
        width: 100%;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image {
        transform: scale(1.05);
    }

    .product-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        height: 2.4rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: var(--gray-800);
    }

    .price {
        font-size: 1.25rem;
        font-weight: bold;
        color: var(--primary-color);
    }

    .old-price {
        text-decoration: line-through;
        color: var(--gray-500);
        font-size: 0.9rem;
    }

    .discount-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: var(--primary-color);
        color: var(--white-color);
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }

    .featured-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: var(--gray-800);
        color: var(--white-color);
        padding: 5px 8px;
        border-radius: 50%;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .filters-sidebar {
        background: var(--gray-100);
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .filter-section {
        margin-bottom: 1.5rem;
    }

    .filter-section h6 {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-add-cart {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: var(--white-color);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-add-cart:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
        color: var(--white-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .search-bar {
        border-radius: 25px;
        border: 2px solid var(--gray-200);
        padding: 12px 20px;
        transition: all 0.3s ease;
    }

    .search-bar:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        outline: none;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: var(--white-color);
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    /* Price Range Slider Styles */
    .price-slider {
        position: relative;
        margin: 1rem 0;
    }

    .range-slider {
        position: relative;
        height: 40px;
    }

    .range-slider input[type="range"] {
        position: absolute;
        width: 100%;
        height: 6px;
        background: none;
        pointer-events: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }

    .range-slider input[type="range"]::-webkit-slider-thumb {
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: var(--primary-color);
        border: 2px solid var(--white-color);
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        pointer-events: all;
        -webkit-appearance: none;
        cursor: pointer;
    }

    .range-slider input[type="range"]::-moz-range-thumb {
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: var(--primary-color);
        border: 2px solid var(--white-color);
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        pointer-events: all;
        cursor: pointer;
    }

    .range-slider input[type="range"]::-webkit-slider-track {
        height: 6px;
        background: var(--gray-300);
        border-radius: 3px;
    }

    .range-slider input[type="range"]::-moz-range-track {
        height: 6px;
        background: var(--gray-300);
        border-radius: 3px;
        border: none;
    }

    .quick-price-filters .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .quick-price-filters .btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--white-color);
    }

    .subcategory {
        font-size: 0.9rem;
    }

    .subcategory .form-check-label {
        color: var(--gray-600);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-store me-2"></i>Nuestra Tienda</h1>
                <p class="mb-0 mt-2 opacity-75">Descubre los mejores productos tecnológicos</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                        <li class="breadcrumb-item active">Tienda</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3">
                <div class="filters-sidebar">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filtros</h5>
                    
                    <!-- Search -->
                    <div class="filter-section">
                        <h6>Buscar</h6>
                        <input type="text" class="form-control search-bar" placeholder="Buscar productos..." id="searchInput">
                    </div>
                    
                    <!-- Categories -->
                    <div class="filter-section">
                        <h6>Categorías</h6>
                        <div class="categories-list">
                            <div class="text-center py-3">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="small">Cargando categorías...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Filters -->
                    <div class="filter-section">
                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearAllFilters()">
                            <i class="fas fa-times me-1"></i>Limpiar Filtros
                        </button>
                    </div>
                    
                    <!-- Brands -->
                    <div class="filter-section">
                        <h6>Marcas</h6>
                        <div class="brands-list">
                            <p class="text-muted small">Cargando marcas...</p>
                        </div>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="filter-section">
                        <h6>Rango de Precio</h6>

                        <!-- Manual Price Inputs -->
                        <div class="price-inputs mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <label class="form-label small">Mínimo</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Q</span>
                                        <input type="number" class="form-control" id="priceMin"
                                               placeholder="0" min="0" step="1">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Máximo</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Q</span>
                                        <input type="number" class="form-control" id="priceMax"
                                               placeholder="10000" min="0" step="1">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Price Range Slider -->
                        <div class="price-slider mb-3">
                            <div class="range-slider">
                                <input type="range" class="form-range" id="priceRangeMin"
                                       min="0" max="10000" value="0" step="100">
                                <input type="range" class="form-range" id="priceRangeMax"
                                       min="0" max="10000" value="10000" step="100">
                            </div>
                            <div class="range-values d-flex justify-content-between small text-muted">
                                <span id="rangeMinValue">Q0</span>
                                <span id="rangeMaxValue">Q10,000</span>
                            </div>
                        </div>

                        <!-- Quick Price Filters -->
                        <div class="quick-price-filters">
                            <small class="text-muted d-block mb-2">Filtros rápidos:</small>
                            <div class="d-flex flex-wrap gap-1">
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-price"
                                        data-min="0" data-max="1000">Q0-1K</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-price"
                                        data-min="1000" data-max="3000">Q1K-3K</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-price"
                                        data-min="3000" data-max="5000">Q3K-5K</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-price"
                                        data-min="5000" data-max="10000">Q5K+</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-store me-2"></i>Nuestros Productos</h2>
                    <div class="d-flex align-items-center">
                        <label class="me-2">Ordenar por:</label>
                        <select class="form-select" style="width: auto;" id="sortSelect">
                            <option value="newest">Más recientes</option>
                            <option value="price_asc">Precio: menor a mayor</option>
                            <option value="price_desc">Precio: mayor a menor</option>
                            <option value="popular">Más populares</option>
                            <option value="name">Nombre A-Z</option>
                        </select>
                    </div>
                </div>
                
                <div class="row g-4" id="products-grid">
                    <?php if (!empty($products)): ?>
                        <?php foreach ($products as $product): ?>
                            <div class="col-md-4">
                                <div class="card product-card"
                                     data-category-id="<?= $product['category_id'] ?? '' ?>"
                                     data-category-name="<?= esc($product['category_name'] ?? 'Sin categoría') ?>"
                                     data-brand-id="<?= $product['brand_id'] ?? '' ?>"
                                     data-brand-name="<?= esc($product['brand_name'] ?? '') ?>">
                                    <div class="position-relative">
                                        <a href="<?= base_url('producto/' . $product['slug']) ?>" class="d-block">
                                            <img src="<?= product_image_url($product['featured_image']) ?>"
                                                 class="card-img-top product-image"
                                                 alt="<?= esc($product['name']) ?>"
                                                 onerror="this.src='<?= base_url('assets/img/no-image.jpg') ?>'">
                                        </a>
                                        <?php if (!empty($product['price_sale']) && $product['price_sale'] < $product['price_regular']): ?>
                                            <?php $discount = round((($product['price_regular'] - $product['price_sale']) / $product['price_regular']) * 100); ?>
                                            <span class="discount-badge">-<?= $discount ?>%</span>
                                        <?php endif; ?>
                                        <?php if ($product['is_featured']): ?>
                                            <span class="featured-badge">
                                                <i class="fas fa-star"></i>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="product-title">
                                            <a href="<?= base_url('producto/' . $product['slug']) ?>" class="text-decoration-none">
                                                <?= esc($product['name']) ?>
                                            </a>
                                        </h5>
                                        <p class="text-muted small mb-2">
                                            <?= esc($product['category_name'] ?? 'Sin categoría') ?>
                                            <?php if (!empty($product['brand_name'])): ?>
                                                | <?= esc($product['brand_name']) ?>
                                            <?php endif; ?>
                                        </p>
                                        <div class="mb-3">
                                            <?php if (!empty($product['price_sale']) && $product['price_sale'] < $product['price_regular']): ?>
                                                <span class="price"><?= format_currency($product['price_sale'], $product['currency'] ?? 'GTQ') ?></span>
                                                <span class="old-price ms-2"><?= format_currency($product['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                            <?php else: ?>
                                                <span class="price"><?= format_currency($product['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Rating Stars -->
                                        <div class="mb-2">
                                            <div class="rating-stars">
                                                <?php
                                                $rating = isset($product['rating_average']) ? (float)$product['rating_average'] : 0;
                                                $ratingCount = isset($product['rating_count']) ? (int)$product['rating_count'] : 0;
                                                ?>
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= floor($rating)): ?>
                                                        <i class="fas fa-star text-warning"></i>
                                                    <?php elseif ($i <= ceil($rating) && $rating > floor($rating)): ?>
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star text-muted"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                                <small class="text-muted ms-1">
                                                    <?php if ($ratingCount > 0): ?>
                                                        (<?= $rating ?>/5 - <?= $ratingCount ?> reseña<?= $ratingCount != 1 ? 's' : '' ?>)
                                                    <?php else: ?>
                                                        Sin reseñas
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>

                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-box me-1"></i>
                                                Stock: <?= $product['stock_quantity'] ?> disponibles
                                            </small>
                                        </div>
                                        <div class="d-grid">
                                            <button class="btn btn-add-cart" data-product-id="<?= $product['id'] ?>">
                                                <i class="fas fa-cart-plus me-2"></i>Agregar al Carrito
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                                <h3>No hay productos disponibles</h3>
                                <p class="text-muted">Vuelve pronto para ver nuestros productos.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                    <nav aria-label="Paginación de productos" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Button -->
                            <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
                                <?php if ($pagination['has_previous']): ?>
                                    <?php
                                    $prevParams = $_GET;
                                    $prevParams['page'] = $pagination['current_page'] - 1;
                                    $prevUrl = base_url('tienda?' . http_build_query($prevParams));
                                    ?>
                                    <a class="page-link" href="<?= $prevUrl ?>">Anterior</a>
                                <?php else: ?>
                                    <span class="page-link">Anterior</span>
                                <?php endif; ?>
                            </li>

                            <!-- Page Numbers -->
                            <?php
                            $start = max(1, $pagination['current_page'] - 2);
                            $end = min($pagination['total_pages'], $pagination['current_page'] + 2);
                            ?>

                            <?php if ($start > 1): ?>
                                <li class="page-item">
                                    <?php
                                    $firstParams = $_GET;
                                    $firstParams['page'] = 1;
                                    $firstUrl = base_url('tienda?' . http_build_query($firstParams));
                                    ?>
                                    <a class="page-link" href="<?= $firstUrl ?>">1</a>
                                </li>
                                <?php if ($start > 2): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start; $i <= $end; $i++): ?>
                                <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                    <?php if ($i == $pagination['current_page']): ?>
                                        <span class="page-link"><?= $i ?></span>
                                    <?php else: ?>
                                        <?php
                                        $pageParams = $_GET;
                                        $pageParams['page'] = $i;
                                        $pageUrl = base_url('tienda?' . http_build_query($pageParams));
                                        ?>
                                        <a class="page-link" href="<?= $pageUrl ?>"><?= $i ?></a>
                                    <?php endif; ?>
                                </li>
                            <?php endfor; ?>

                            <?php if ($end < $pagination['total_pages']): ?>
                                <?php if ($end < $pagination['total_pages'] - 1): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <?php
                                    $lastParams = $_GET;
                                    $lastParams['page'] = $pagination['total_pages'];
                                    $lastUrl = base_url('tienda?' . http_build_query($lastParams));
                                    ?>
                                    <a class="page-link" href="<?= $lastUrl ?>"><?= $pagination['total_pages'] ?></a>
                                </li>
                            <?php endif; ?>

                            <!-- Next Button -->
                            <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                                <?php if ($pagination['has_next']): ?>
                                    <?php
                                    $nextParams = $_GET;
                                    $nextParams['page'] = $pagination['current_page'] + 1;
                                    $nextUrl = base_url('tienda?' . http_build_query($nextParams));
                                    ?>
                                    <a class="page-link" href="<?= $nextUrl ?>">Siguiente</a>
                                <?php else: ?>
                                    <span class="page-link">Siguiente</span>
                                <?php endif; ?>
                            </li>
                        </ul>

                        <!-- Pagination Info -->
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                Mostrando <?= (($pagination['current_page'] - 1) * $pagination['per_page']) + 1 ?> -
                                <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_products']) ?>
                                de <?= $pagination['total_products'] ?> productos
                            </small>
                        </div>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>





<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Shop specific functionality
    let allProducts = [];
    let filteredProducts = [];

    // Initialize shop functionality
    document.addEventListener('DOMContentLoaded', function() {
        initializeProducts();
        loadCategories();
        loadBrandsByCategories(); // Load all brands initially
        setupEventListeners();

        // Check if we have filters but no products, redirect to page 1
        setTimeout(checkFiltersAndProducts, 1000); // Wait for products to load
    });

    // Check if we have filters applied but no products showing
    function checkFiltersAndProducts() {
        const urlParams = new URLSearchParams(window.location.search);
        const hasFilters = urlParams.has('category') || urlParams.has('search') || urlParams.has('price_min') || urlParams.has('price_max');
        const currentPage = parseInt(urlParams.get('page')) || 1;

        if (hasFilters && currentPage > 1) {
            // Check if there are products visible on the page
            const productCards = document.querySelectorAll('.product-card');
            const visibleProducts = Array.from(productCards).filter(card => {
                const style = window.getComputedStyle(card.closest('.col-md-4'));
                return style.display !== 'none';
            });

            if (visibleProducts.length === 0) {
                // No products visible, redirect to page 1
                const newUrl = new URL(window.location.href);
                newUrl.searchParams.set('page', '1');
                window.location.href = newUrl.href;
            }
        }
    }

    // Load categories from API
    function loadCategories() {
        fetch('<?= base_url('api/categories') ?>')
            .then(response => response.json())
            .then(response => {
                const categoriesList = document.querySelector('.categories-list');
                const data = response.data || response;
                if (data && data.length > 0) {
                    let html = '';

                    // Render categories with hierarchy
                    function renderCategoryTree(categories, level = 0) {
                        let treeHtml = '';
                        categories.forEach(category => {
                            // Use the products_count from the API response directly
                            const productCount = parseInt(category.products_count) || 0;
                            const levelClass = level > 0 ? 'subcategory' : 'main-category';

                            treeHtml += `
                                <div class="form-check ${levelClass}" style="margin-left: ${level * 15}px;">
                                    <input class="form-check-input category-filter" type="checkbox"
                                           id="category_${category.id}" value="${category.id}"
                                           data-level="${level}">
                                    <label class="form-check-label" for="category_${category.id}">
                                        ${level > 0 ? '└ ' : ''}${category.name}
                                        <span class="text-muted">(${productCount})</span>
                                    </label>
                                </div>
                            `;

                            // Render children if they exist
                            if (category.children && category.children.length > 0) {
                                treeHtml += renderCategoryTree(category.children, level + 1);
                            }
                        });
                        return treeHtml;
                    }

                    html = renderCategoryTree(data);
                    categoriesList.innerHTML = html;

                    // Add event listeners to category filters
                    document.querySelectorAll('.category-filter').forEach(checkbox => {
                        checkbox.addEventListener('change', function() {
                            loadBrandsByCategories();
                            applyFilters();
                        });
                    });

                    // Apply URL filters after categories are loaded
                    applyUrlFilters();
                } else {
                    categoriesList.innerHTML = '<p class="text-muted small">No hay categorías disponibles</p>';
                }
            })
            .catch(error => {
                console.error('Error loading categories:', error);
                document.querySelector('.categories-list').innerHTML =
                    '<p class="text-danger small">Error al cargar categorías</p>';
            });
    }

    // Load brands based on selected categories
    function loadBrandsByCategories() {
        const selectedCategories = Array.from(document.querySelectorAll('.category-filter:checked')).map(cb => cb.value);
        const brandsList = document.querySelector('.brands-list');

        // Store currently selected brands to maintain selection
        const currentlySelectedBrands = Array.from(document.querySelectorAll('.brand-filter:checked')).map(cb => cb.value);

        // Show loading state
        brandsList.innerHTML = '<p class="text-muted small">Cargando marcas...</p>';

        // Build API URL
        let apiUrl = '<?= base_url('api/brands') ?>';
        if (selectedCategories.length > 0) {
            apiUrl = '<?= base_url('api/brands/by-categories') ?>?category_ids=' + selectedCategories.join(',');
        }

        fetch(apiUrl)
            .then(response => response.json())
            .then(response => {
                const data = response.data || response;
                if (data && data.length > 0) {
                    let html = '';
                    data.forEach(brand => {
                        const isSelected = currentlySelectedBrands.includes(brand.id.toString());
                        html += `
                            <div class="form-check">
                                <input class="form-check-input brand-filter" type="checkbox"
                                       id="brand_${brand.id}"
                                       value="${brand.id}"
                                       data-brand-name="${brand.name}"
                                       ${isSelected ? 'checked' : ''}>
                                <label class="form-check-label" for="brand_${brand.id}">
                                    ${brand.name} <span class="text-muted">(${brand.products_count || 0})</span>
                                </label>
                            </div>
                        `;
                    });
                    brandsList.innerHTML = html;

                    // Add event listeners to brand filters
                    document.querySelectorAll('.brand-filter').forEach(checkbox => {
                        checkbox.addEventListener('change', applyFilters);
                    });
                } else {
                    brandsList.innerHTML = '<p class="text-muted small">No hay marcas disponibles para las categorías seleccionadas</p>';
                }
            })
            .catch(error => {
                console.error('Error loading brands:', error);
                brandsList.innerHTML = '<p class="text-danger small">Error al cargar marcas</p>';
            });
    }

    // Note: countProductsInCategory function removed - now using products_count directly from API

    // Note: flattenCategories function removed - no longer needed

    // Initialize products array
    function initializeProducts() {
        const productCards = document.querySelectorAll('.product-card');

        allProducts = Array.from(productCards).map(card => {
            const titleElement = card.querySelector('.product-title a');
            const priceElement = card.querySelector('.price');
            const categoryElement = card.querySelector('.text-muted.small');
            const categoryId = card.dataset.categoryId || '';
            const categoryName = card.dataset.categoryName || '';
            const brandId = card.dataset.brandId || '';
            const brandName = card.dataset.brandName || '';
            const parentElement = card.closest('.col-md-4');

            return {
                element: parentElement,
                title: titleElement ? titleElement.textContent.trim() : '',
                price: priceElement ? parseFloat(priceElement.textContent.replace(/[^\d.]/g, '')) : 0,
                category: categoryElement ? categoryElement.textContent.trim() : '',
                categoryId: categoryId,
                categoryName: categoryName,
                brandId: brandId,
                brandName: brandName,
                featured: card.querySelector('.featured-badge') !== null
            };
        });

        filteredProducts = [...allProducts];

        // Call displayProducts initially to show all products
        displayProducts();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', applyFilters);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    applyFilters();
                }
            });
        }

        // Sort select
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', applySorting);
        }

        // Brand filters (dynamic)
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('brand-filter')) {
                applyFilters();
            }
        });

        // Price range inputs
        const priceMin = document.getElementById('priceMin');
        const priceMax = document.getElementById('priceMax');
        const priceRangeMin = document.getElementById('priceRangeMin');
        const priceRangeMax = document.getElementById('priceRangeMax');

        if (priceMin && priceMax) {
            priceMin.addEventListener('input', function() {
                updatePriceSlider();
                applyFilters();
            });
            priceMax.addEventListener('input', function() {
                updatePriceSlider();
                applyFilters();
            });
        }

        if (priceRangeMin && priceRangeMax) {
            priceRangeMin.addEventListener('input', function() {
                updatePriceInputs();
                applyFilters();
            });
            priceRangeMax.addEventListener('input', function() {
                updatePriceInputs();
                applyFilters();
            });
        }

        // Quick price filter buttons
        document.querySelectorAll('.quick-price').forEach(button => {
            button.addEventListener('click', function() {
                const min = this.dataset.min;
                const max = this.dataset.max;
                setPrice(min, max);

                // Update active state
                document.querySelectorAll('.quick-price').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Add to cart buttons
        document.querySelectorAll('.btn-add-cart').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToCart(productId);
            });
        });

        // Initialize price slider
        initializePriceSlider();
    }

    // Initialize price slider with debounce to prevent excessive requests
    function initializePriceSlider() {
        // Set initial values based on product prices
        if (allProducts.length > 0) {
            const prices = allProducts.map(p => p.price).filter(p => p > 0);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);

            // Update slider max values
            document.getElementById('priceRangeMin').max = maxPrice;
            document.getElementById('priceRangeMax').max = maxPrice;
            document.getElementById('priceRangeMax').value = maxPrice;

            // Update display
            document.getElementById('rangeMaxValue').textContent = `Q${maxPrice.toLocaleString()}`;
            document.getElementById('priceMax').placeholder = maxPrice;
        }

        // Add debounced event listeners to prevent excessive filtering
        const minSlider = document.getElementById('priceRangeMin');
        const maxSlider = document.getElementById('priceRangeMax');

        if (minSlider && maxSlider) {
            let debounceTimer;
            let isMouseDown = false;

            function debouncedFilter() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    if (!isMouseDown) {
                        applyFilters();
                    }
                }, 500); // Wait 500ms after user stops moving
            }

            function immediateFilter() {
                clearTimeout(debounceTimer);
                applyFilters();
            }

            // Update display immediately but debounce filter application
            function handleSliderInput() {
                updatePriceInputs();
                debouncedFilter();
            }

            // Apply filters immediately when user releases slider
            function handleSliderChange() {
                updatePriceInputs();
                immediateFilter();
            }

            // Track mouse state
            minSlider.addEventListener('mousedown', () => { isMouseDown = true; });
            maxSlider.addEventListener('mousedown', () => { isMouseDown = true; });
            minSlider.addEventListener('mouseup', () => {
                isMouseDown = false;
                immediateFilter();
            });
            maxSlider.addEventListener('mouseup', () => {
                isMouseDown = false;
                immediateFilter();
            });

            // Add event listeners
            minSlider.addEventListener('input', handleSliderInput);
            maxSlider.addEventListener('input', handleSliderInput);
            minSlider.addEventListener('change', handleSliderChange);
            maxSlider.addEventListener('change', handleSliderChange);

            // Touch events for mobile
            minSlider.addEventListener('touchstart', () => { isMouseDown = true; });
            maxSlider.addEventListener('touchstart', () => { isMouseDown = true; });
            minSlider.addEventListener('touchend', () => {
                isMouseDown = false;
                immediateFilter();
            });
            maxSlider.addEventListener('touchend', () => {
                isMouseDown = false;
                immediateFilter();
            });
        }
    }

    // Update price inputs from sliders
    function updatePriceInputs() {
        const minSlider = document.getElementById('priceRangeMin');
        const maxSlider = document.getElementById('priceRangeMax');
        const minInput = document.getElementById('priceMin');
        const maxInput = document.getElementById('priceMax');

        let minVal = parseInt(minSlider.value);
        let maxVal = parseInt(maxSlider.value);

        // Ensure min is not greater than max
        if (minVal >= maxVal) {
            minVal = maxVal - 100;
            minSlider.value = minVal;
        }

        minInput.value = minVal;
        maxInput.value = maxVal;

        // Update display values
        document.getElementById('rangeMinValue').textContent = `Q${minVal.toLocaleString()}`;
        document.getElementById('rangeMaxValue').textContent = `Q${maxVal.toLocaleString()}`;
    }

    // Update sliders from price inputs
    function updatePriceSlider() {
        const minInput = document.getElementById('priceMin');
        const maxInput = document.getElementById('priceMax');
        const minSlider = document.getElementById('priceRangeMin');
        const maxSlider = document.getElementById('priceRangeMax');

        const minVal = parseInt(minInput.value) || 0;
        const maxVal = parseInt(maxInput.value) || parseInt(maxSlider.max);

        minSlider.value = minVal;
        maxSlider.value = maxVal;

        // Update display values
        document.getElementById('rangeMinValue').textContent = `Q${minVal.toLocaleString()}`;
        document.getElementById('rangeMaxValue').textContent = `Q${maxVal.toLocaleString()}`;
    }

    // Set price range
    function setPrice(min, max) {
        document.getElementById('priceMin').value = min;
        document.getElementById('priceMax').value = max;
        document.getElementById('priceRangeMin').value = min;
        document.getElementById('priceRangeMax').value = max;

        // Update display values
        document.getElementById('rangeMinValue').textContent = `Q${parseInt(min).toLocaleString()}`;
        document.getElementById('rangeMaxValue').textContent = `Q${parseInt(max).toLocaleString()}`;

        applyFilters();
    }

    // Apply all filters
    function applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
        const selectedCategories = Array.from(document.querySelectorAll('.category-filter:checked')).map(cb => cb.value);
        const selectedBrands = Array.from(document.querySelectorAll('.brand-filter:checked')).map(cb => cb.dataset.brandName);

        // Get price range from inputs
        const minPrice = parseInt(document.getElementById('priceMin').value) || 0;
        const maxPrice = parseInt(document.getElementById('priceMax').value) || Infinity;

        // Check if we have server-side filters that need a page redirect
        const urlParams = new URLSearchParams(window.location.search);
        const hasServerFilters = urlParams.has('category') || urlParams.has('search') || urlParams.has('price_min') || urlParams.has('price_max');
        const hasClientFilters = searchTerm || selectedCategories.length > 0 || selectedBrands.length > 0 || minPrice > 0 || maxPrice < Infinity;

        // If we have client-side filters and server-side filters, redirect to apply server-side filters
        if (hasClientFilters && (searchTerm || selectedCategories.length > 0 || minPrice > 0 || maxPrice < Infinity)) {
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('page'); // Remove page parameter

            // Update URL parameters
            if (searchTerm) {
                newUrl.searchParams.set('search', searchTerm);
            } else {
                newUrl.searchParams.delete('search');
            }

            if (selectedCategories.length > 0) {
                newUrl.searchParams.set('category', selectedCategories[0]); // Use first selected category
            } else {
                newUrl.searchParams.delete('category');
            }

            if (minPrice > 0) {
                newUrl.searchParams.set('price_min', minPrice);
            } else {
                newUrl.searchParams.delete('price_min');
            }

            if (maxPrice < Infinity) {
                newUrl.searchParams.set('price_max', maxPrice);
            } else {
                newUrl.searchParams.delete('price_max');
            }

            // Only redirect if URL actually changed
            if (newUrl.href !== window.location.href) {
                window.location.href = newUrl.href;
                return;
            }
        }

        filteredProducts = allProducts.filter(product => {
            // Search filter
            if (searchTerm && !product.title.toLowerCase().includes(searchTerm)) {
                return false;
            }

            // Category filter
            if (selectedCategories.length > 0) {
                if (!selectedCategories.includes(product.categoryId)) {
                    return false;
                }
            }

            // Brand filter
            if (selectedBrands.length > 0) {
                const hasMatchingBrand = selectedBrands.some(brand => {
                    if (!product.brandName || !brand) return false;
                    return product.brandName.toLowerCase().trim() === brand.toLowerCase().trim();
                });

                if (!hasMatchingBrand) return false;
            }

            // Price filter
            if (product.price < minPrice || product.price > maxPrice) {
                return false;
            }

            return true;
        });

        displayProducts();
        updateProductCount();
    }

    // Apply sorting
    function applySorting() {
        const sortValue = document.getElementById('sortSelect').value;

        filteredProducts.sort((a, b) => {
            switch(sortValue) {
                case 'price_asc':
                    return a.price - b.price;
                case 'price_desc':
                    return b.price - a.price;
                case 'name':
                    return a.title.localeCompare(b.title);
                case 'popular':
                    return b.featured - a.featured;
                case 'newest':
                default:
                    return 0; // Keep original order
            }
        });

        displayProducts();
    }

    // Display filtered and sorted products
    function displayProducts() {
        const productsGrid = document.getElementById('products-grid');

        // Remove existing no-results message first
        const existingNoResults = document.getElementById('no-results');
        if (existingNoResults) {
            existingNoResults.remove();
        }

        // Hide all products first
        allProducts.forEach(product => {
            if (product.element) {
                product.element.style.display = 'none';
            }
        });

        // Show filtered products
        if (filteredProducts.length > 0) {
            filteredProducts.forEach((product, index) => {
                if (product.element) {
                    product.element.style.display = 'block';
                    product.element.style.order = index;
                }
            });
        } else {
            // Show no results message
            const noResultsHtml = `
                <div class="col-12" id="no-results">
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-4x text-muted mb-3"></i>
                        <h3>No se encontraron productos</h3>
                        <p class="text-muted">Intenta ajustar los filtros de búsqueda.</p>
                    </div>
                </div>
            `;

            productsGrid.insertAdjacentHTML('beforeend', noResultsHtml);
        }
    }

    // Add to cart functionality
    async function addToCart(productId) {
        try {
            const response = await fetch('<?= base_url('api/cart/add') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Mostrar modal elegante en lugar de alerta
                if (window.showCartModal) {
                    window.showCartModal(
                        data.product_name || 'Producto',
                        data.cart_count,
                        data.cart_total
                    );
                } else {
                    // Fallback a alerta
                    alert('Producto agregado al carrito');
                }

                // Update cart count using global function
                if (window.updateCartCount) {
                    window.updateCartCount();
                }
            } else {
                alert('Error al agregar producto: ' + (data.message || 'Error desconocido'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al agregar producto al carrito');
        }
    }

    // Update cart count display
    function updateCartCount(count) {
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            if (element) {
                element.textContent = count || 0;
                element.style.display = (count && count > 0) ? 'inline' : 'none';
            }
        });
    }

    // Update product count display
    function updateProductCount() {
        const count = filteredProducts.length;
        const total = allProducts.length;

        // Update count display if element exists
        const countElement = document.getElementById('productsCount');
        if (countElement) {
            countElement.textContent = `${count} de ${total} productos`;
        }
    }

    // Apply filters from URL parameters
    function applyUrlFilters() {
        const urlParams = new URLSearchParams(window.location.search);

        // Apply category filter
        const categoryParam = urlParams.get('category');
        if (categoryParam) {
            const categoryCheckbox = document.querySelector(`input.category-filter[value="${categoryParam}"]`);
            if (categoryCheckbox) {
                categoryCheckbox.checked = true;
                // Load brands for this category
                loadBrandsByCategories();
            }
        }

        // Apply search filter
        const searchParam = urlParams.get('search');
        if (searchParam) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = searchParam;
            }
        }

        // Apply price filters
        const priceMin = urlParams.get('price_min');
        const priceMax = urlParams.get('price_max');
        if (priceMin || priceMax) {
            const minValue = priceMin || 0;
            const maxValue = priceMax || 50000;
            setPrice(minValue, maxValue);
        }
    }

    // Clear all filters
    function clearAllFilters() {
        // Clear search
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';

        // Clear category filters
        document.querySelectorAll('.category-filter').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Clear brand filters
        document.querySelectorAll('.brand-filter').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Clear price inputs and sliders
        document.getElementById('priceMin').value = '';
        document.getElementById('priceMax').value = '';

        // Reset sliders to default values
        const maxSlider = document.getElementById('priceRangeMax');
        const maxValue = maxSlider.max;
        document.getElementById('priceRangeMin').value = 0;
        document.getElementById('priceRangeMax').value = maxValue;

        // Update display values
        document.getElementById('rangeMinValue').textContent = 'Q0';
        document.getElementById('rangeMaxValue').textContent = `Q${parseInt(maxValue).toLocaleString()}`;

        // Clear quick price filter active states
        document.querySelectorAll('.quick-price').forEach(button => {
            button.classList.remove('active');
        });

        // Remove no-results message if exists
        const noResults = document.getElementById('no-results');
        if (noResults) {
            noResults.remove();
        }

        // Reload all brands since no categories are selected
        loadBrandsByCategories();

        // Redirect to base URL without any parameters to clear all filters
        window.location.href = '<?= base_url('tienda') ?>';
    }
</script>
<?= $this->endSection() ?>
