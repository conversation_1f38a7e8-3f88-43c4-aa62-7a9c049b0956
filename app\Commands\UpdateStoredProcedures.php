<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class UpdateStoredProcedures extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:update-procedures';
    protected $description = 'Actualizar stored procedures específicos para admin users';

    public function run(array $params)
    {
        CLI::write('=== ACTUALIZANDO STORED PROCEDURES ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();

        try {
            // Actualizar sp_admin_create_user
            CLI::write('🔧 Actualizando sp_admin_create_user...', 'white');

            // Primero eliminar el procedimiento
            $db->query("DROP PROCEDURE IF EXISTS sp_admin_create_user");

            // Luego crear el nuevo
            $createUserSP = "CREATE PROCEDURE sp_admin_create_user(
                IN p_uuid VARCHAR(36),
                IN p_username VARCHAR(50),
                IN p_email VARCHAR(100),
                IN p_password VARCHAR(255),
                IN p_first_name VARCHAR(50),
                IN p_last_name VARCHAR(50),
                IN p_phone VARCHAR(20),
                OUT p_user_id INT,
                OUT p_result VARCHAR(100)
            )
            BEGIN
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    ROLLBACK;
                    SET p_result = 'ERROR';
                    SET p_user_id = NULL;
                END;

                START TRANSACTION;

                INSERT INTO users (uuid, username, email, password, first_name, last_name, phone, status, created_at)
                VALUES (p_uuid, p_username, p_email, p_password, p_first_name, p_last_name, p_phone, 'active', NOW());

                SET p_user_id = LAST_INSERT_ID();
                SET p_result = 'SUCCESS';

                COMMIT;
            END";

            $db->query($createUserSP);
            CLI::write('   ✅ sp_admin_create_user actualizado', 'green');

            // Actualizar sp_admin_update_user
            CLI::write('🔧 Actualizando sp_admin_update_user...', 'white');

            // Primero eliminar el procedimiento
            $db->query("DROP PROCEDURE IF EXISTS sp_admin_update_user");

            // Luego crear el nuevo
            $updateUserSP = "CREATE PROCEDURE sp_admin_update_user(
                IN p_user_id INT,
                IN p_first_name VARCHAR(50),
                IN p_last_name VARCHAR(50),
                IN p_email VARCHAR(100),
                IN p_username VARCHAR(50),
                IN p_phone VARCHAR(20),
                IN p_status VARCHAR(20),
                OUT p_result VARCHAR(100)
            )
            BEGIN
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    ROLLBACK;
                    SET p_result = 'ERROR';
                END;

                START TRANSACTION;

                UPDATE users
                SET first_name = p_first_name,
                    last_name = p_last_name,
                    email = p_email,
                    username = p_username,
                    phone = p_phone,
                    status = p_status,
                    updated_at = NOW()
                WHERE id = p_user_id;

                SET p_result = 'SUCCESS';

                COMMIT;
            END";

            $db->query($updateUserSP);
            CLI::write('   ✅ sp_admin_update_user actualizado', 'green');

            // Actualizar sp_admin_list_users
            CLI::write('🔧 Actualizando sp_admin_list_users...', 'white');

            // Primero eliminar el procedimiento
            $db->query("DROP PROCEDURE IF EXISTS sp_admin_list_users");

            // Luego crear el nuevo (versión simplificada para evitar problemas de sintaxis)
            $listUsersSP = "CREATE PROCEDURE sp_admin_list_users(
                IN p_search VARCHAR(255),
                IN p_role VARCHAR(50),
                IN p_limit INT,
                IN p_offset INT
            )
            BEGIN
                SELECT u.id,
                       CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as name,
                       u.username, u.email, u.phone, u.status as is_active, u.created_at, u.updated_at,
                       'customer' as roles,
                       '2' as role_ids,
                       0 as total_orders,
                       0 as total_spent
                FROM users u
                WHERE 1=1
                ORDER BY u.created_at DESC
                LIMIT p_limit OFFSET p_offset;
            END";

            $db->query($listUsersSP);
            CLI::write('   ✅ sp_admin_list_users actualizado', 'green');

            CLI::newLine();
            CLI::write('✅ Stored procedures actualizados exitosamente', 'green');

        } catch (\Exception $e) {
            CLI::error('❌ Error actualizando stored procedures: ' . $e->getMessage());
            return;
        }

        CLI::newLine();
        CLI::write('=== ACTUALIZACIÓN COMPLETADA ===', 'yellow');
    }
}
