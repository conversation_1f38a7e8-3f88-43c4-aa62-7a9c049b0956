<?php

namespace App\Libraries;

/**
 * Gestor <PERSON><PERSON><PERSON> de Backups
 * Sistema completo de backup incremental, restauración y verificación
 */
class BackupManager
{
    private $db;
    private $config;
    private $backupPath;
    private $logFile;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->backupPath = WRITEPATH . 'backups/';
        $this->logFile = $this->backupPath . 'backup.log';
        
        $this->config = [
            'max_backups' => 30, // Mantener 30 backups
            'compression' => true,
            'encryption' => false,
            'encryption_key' => env('BACKUP_ENCRYPTION_KEY', ''),
            'cloud_storage' => false,
            'cloud_provider' => env('BACKUP_CLOUD_PROVIDER', ''), // aws, gcp, azure
            'cloud_bucket' => env('BACKUP_CLOUD_BUCKET', ''),
            'incremental_enabled' => true,
            'verify_integrity' => true,
            'notification_enabled' => true,
            'admin_email' => env('BACKUP_ADMIN_EMAIL', '<EMAIL>')
        ];
        
        // Crear directorio de backups si no existe
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * Crear backup completo del sistema
     */
    public function createFullBackup(): array
    {
        $startTime = microtime(true);
        $backupId = 'full_' . date('Y-m-d_H-i-s');
        
        $this->log("Iniciando backup completo: $backupId");
        
        try {
            $result = [
                'backup_id' => $backupId,
                'type' => 'full',
                'start_time' => date('Y-m-d H:i:s'),
                'status' => 'in_progress',
                'components' => [],
                'files_backed_up' => 0,
                'total_size' => 0,
                'compressed_size' => 0,
                'success' => false
            ];
            
            // 1. Backup de base de datos
            $dbBackup = $this->backupDatabase($backupId);
            $result['components']['database'] = $dbBackup;
            
            // 2. Backup de archivos del sistema
            $filesBackup = $this->backupFiles($backupId);
            $result['components']['files'] = $filesBackup;
            
            // 3. Backup de configuraciones
            $configBackup = $this->backupConfigurations($backupId);
            $result['components']['config'] = $configBackup;
            
            // 4. Backup de uploads y media
            $mediaBackup = $this->backupMedia($backupId);
            $result['components']['media'] = $mediaBackup;
            
            // 5. Crear archivo de manifiesto
            $manifest = $this->createManifest($backupId, $result);
            
            // 6. Comprimir backup si está habilitado
            if ($this->config['compression']) {
                $compressed = $this->compressBackup($backupId);
                $result['compressed_size'] = $compressed['size'];
                $result['compression_ratio'] = $compressed['ratio'];
            }
            
            // 7. Verificar integridad
            if ($this->config['verify_integrity']) {
                $integrity = $this->verifyBackupIntegrity($backupId);
                $result['integrity_check'] = $integrity;
            }
            
            // 8. Subir a la nube si está configurado
            if ($this->config['cloud_storage']) {
                $cloudUpload = $this->uploadToCloud($backupId);
                $result['cloud_upload'] = $cloudUpload;
            }
            
            // 9. Limpiar backups antiguos
            $this->cleanupOldBackups();
            
            $duration = round((microtime(true) - $startTime), 2);
            $result['duration'] = $duration;
            $result['end_time'] = date('Y-m-d H:i:s');
            $result['status'] = 'completed';
            $result['success'] = true;
            
            // 10. Registrar backup en base de datos
            $this->registerBackup($result);
            
            // 11. Enviar notificación
            if ($this->config['notification_enabled']) {
                $this->sendBackupNotification($result);
            }
            
            $this->log("Backup completo finalizado: $backupId en {$duration}s");
            
            return $result;
            
        } catch (\Exception $e) {
            $this->log("Error en backup completo: " . $e->getMessage(), 'ERROR');
            
            return [
                'backup_id' => $backupId,
                'success' => false,
                'error' => $e->getMessage(),
                'duration' => round((microtime(true) - $startTime), 2)
            ];
        }
    }
    
    /**
     * Crear backup incremental
     */
    public function createIncrementalBackup(): array
    {
        if (!$this->config['incremental_enabled']) {
            return ['success' => false, 'error' => 'Backup incremental deshabilitado'];
        }
        
        $startTime = microtime(true);
        $backupId = 'inc_' . date('Y-m-d_H-i-s');
        
        $this->log("Iniciando backup incremental: $backupId");
        
        try {
            // Obtener último backup
            $lastBackup = $this->getLastBackup();
            if (!$lastBackup) {
                return $this->createFullBackup();
            }
            
            $result = [
                'backup_id' => $backupId,
                'type' => 'incremental',
                'base_backup' => $lastBackup['backup_id'],
                'start_time' => date('Y-m-d H:i:s'),
                'changes' => [],
                'success' => false
            ];
            
            // Detectar cambios desde último backup
            $changes = $this->detectChanges($lastBackup['created_at']);
            $result['changes'] = $changes;
            
            if (empty($changes['database']) && empty($changes['files'])) {
                $result['success'] = true;
                $result['message'] = 'No hay cambios desde el último backup';
                return $result;
            }
            
            // Backup solo de cambios
            if (!empty($changes['database'])) {
                $dbIncremental = $this->backupDatabaseChanges($backupId, $changes['database']);
                $result['components']['database'] = $dbIncremental;
            }
            
            if (!empty($changes['files'])) {
                $filesIncremental = $this->backupFileChanges($backupId, $changes['files']);
                $result['components']['files'] = $filesIncremental;
            }
            
            $duration = round((microtime(true) - $startTime), 2);
            $result['duration'] = $duration;
            $result['end_time'] = date('Y-m-d H:i:s');
            $result['success'] = true;
            
            $this->registerBackup($result);
            $this->log("Backup incremental finalizado: $backupId en {$duration}s");
            
            return $result;
            
        } catch (\Exception $e) {
            $this->log("Error en backup incremental: " . $e->getMessage(), 'ERROR');
            
            return [
                'backup_id' => $backupId,
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Restaurar backup
     */
    public function restoreBackup(string $backupId, array $options = []): array
    {
        $this->log("Iniciando restauración de backup: $backupId");
        
        try {
            $backup = $this->getBackupInfo($backupId);
            if (!$backup) {
                throw new \Exception("Backup no encontrado: $backupId");
            }
            
            $result = [
                'backup_id' => $backupId,
                'restore_time' => date('Y-m-d H:i:s'),
                'components_restored' => [],
                'success' => false
            ];
            
            // Crear backup de seguridad antes de restaurar
            if ($options['create_safety_backup'] ?? true) {
                $safetyBackup = $this->createFullBackup();
                $result['safety_backup'] = $safetyBackup['backup_id'];
            }
            
            // Restaurar componentes
            if ($options['restore_database'] ?? true) {
                $dbRestore = $this->restoreDatabase($backupId);
                $result['components_restored']['database'] = $dbRestore;
            }
            
            if ($options['restore_files'] ?? true) {
                $filesRestore = $this->restoreFiles($backupId);
                $result['components_restored']['files'] = $filesRestore;
            }
            
            if ($options['restore_config'] ?? true) {
                $configRestore = $this->restoreConfigurations($backupId);
                $result['components_restored']['config'] = $configRestore;
            }
            
            if ($options['restore_media'] ?? true) {
                $mediaRestore = $this->restoreMedia($backupId);
                $result['components_restored']['media'] = $mediaRestore;
            }
            
            // Verificar restauración
            $verification = $this->verifyRestoration($backupId);
            $result['verification'] = $verification;
            
            $result['success'] = true;
            $this->log("Restauración completada: $backupId");
            
            return $result;
            
        } catch (\Exception $e) {
            $this->log("Error en restauración: " . $e->getMessage(), 'ERROR');
            
            return [
                'backup_id' => $backupId,
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup de base de datos
     */
    private function backupDatabase(string $backupId): array
    {
        $this->log("Iniciando backup de base de datos");
        
        try {
            $dbConfig = $this->db->getDatabase();
            $backupFile = $this->backupPath . $backupId . '_database.sql';
            
            // Obtener todas las tablas
            $tables = $this->db->listTables();
            
            $sql = "-- Backup de Base de Datos MrCell Guatemala\n";
            $sql .= "-- Fecha: " . date('Y-m-d H:i:s') . "\n";
            $sql .= "-- Backup ID: $backupId\n\n";
            
            $totalRows = 0;
            
            foreach ($tables as $table) {
                $sql .= "-- Tabla: $table\n";
                $sql .= "DROP TABLE IF EXISTS `$table`;\n";
                
                // Estructura de la tabla
                $createTable = $this->db->query("SHOW CREATE TABLE `$table`")->getRow();
                $sql .= $createTable->{'Create Table'} . ";\n\n";
                
                // Datos de la tabla
                $rows = $this->db->table($table)->get()->getResultArray();
                $totalRows += count($rows);
                
                if (!empty($rows)) {
                    $sql .= "INSERT INTO `$table` VALUES\n";
                    $values = [];
                    
                    foreach ($rows as $row) {
                        $escapedValues = array_map(function($value) {
                            return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                        }, $row);
                        $values[] = '(' . implode(',', $escapedValues) . ')';
                    }
                    
                    $sql .= implode(",\n", $values) . ";\n\n";
                }
            }
            
            file_put_contents($backupFile, $sql);
            
            return [
                'success' => true,
                'file' => $backupFile,
                'tables_count' => count($tables),
                'rows_count' => $totalRows,
                'file_size' => filesize($backupFile)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup de archivos del sistema
     */
    private function backupFiles(string $backupId): array
    {
        $this->log("Iniciando backup de archivos");
        
        try {
            $backupDir = $this->backupPath . $backupId . '_files/';
            mkdir($backupDir, 0755, true);
            
            $criticalDirs = [
                'app/Controllers/',
                'app/Libraries/',
                'app/Views/',
                'app/Config/',
                'app/Models/',
                'public/assets/'
            ];
            
            $totalFiles = 0;
            $totalSize = 0;
            
            foreach ($criticalDirs as $dir) {
                if (is_dir($dir)) {
                    $copied = $this->copyDirectory($dir, $backupDir . $dir);
                    $totalFiles += $copied['files'];
                    $totalSize += $copied['size'];
                }
            }
            
            return [
                'success' => true,
                'directory' => $backupDir,
                'files_count' => $totalFiles,
                'total_size' => $totalSize
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup de configuraciones
     */
    private function backupConfigurations(string $backupId): array
    {
        try {
            $configFile = $this->backupPath . $backupId . '_config.json';
            
            $config = [
                'env_file' => file_exists('.env') ? file_get_contents('.env') : '',
                'system_settings' => $this->db->table('system_settings')->get()->getResultArray(),
                'whatsapp_templates' => $this->db->table('whatsapp_templates')->get()->getResultArray(),
                'backup_timestamp' => date('Y-m-d H:i:s')
            ];
            
            file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
            
            return [
                'success' => true,
                'file' => $configFile,
                'file_size' => filesize($configFile)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup de archivos media
     */
    private function backupMedia(string $backupId): array
    {
        try {
            $mediaDir = $this->backupPath . $backupId . '_media/';
            mkdir($mediaDir, 0755, true);
            
            $mediaDirs = [
                'public/uploads/',
                'writable/uploads/'
            ];
            
            $totalFiles = 0;
            $totalSize = 0;
            
            foreach ($mediaDirs as $dir) {
                if (is_dir($dir)) {
                    $copied = $this->copyDirectory($dir, $mediaDir . basename($dir));
                    $totalFiles += $copied['files'];
                    $totalSize += $copied['size'];
                }
            }
            
            return [
                'success' => true,
                'directory' => $mediaDir,
                'files_count' => $totalFiles,
                'total_size' => $totalSize
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Crear manifiesto del backup
     */
    private function createManifest(string $backupId, array $backupData): array
    {
        $manifestFile = $this->backupPath . $backupId . '_manifest.json';
        
        $manifest = [
            'backup_id' => $backupId,
            'version' => '2.0.0',
            'created_at' => date('Y-m-d H:i:s'),
            'type' => $backupData['type'],
            'components' => $backupData['components'],
            'system_info' => [
                'php_version' => PHP_VERSION,
                'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
            ],
            'checksums' => $this->calculateChecksums($backupId)
        ];
        
        file_put_contents($manifestFile, json_encode($manifest, JSON_PRETTY_PRINT));
        
        return $manifest;
    }
    
    /**
     * Comprimir backup
     */
    private function compressBackup(string $backupId): array
    {
        if (!extension_loaded('zip')) {
            return ['success' => false, 'error' => 'Extensión ZIP no disponible'];
        }
        
        $zipFile = $this->backupPath . $backupId . '.zip';
        $zip = new \ZipArchive();
        
        if ($zip->open($zipFile, \ZipArchive::CREATE) !== TRUE) {
            return ['success' => false, 'error' => 'No se pudo crear archivo ZIP'];
        }
        
        // Agregar archivos al ZIP
        $files = glob($this->backupPath . $backupId . '_*');
        $originalSize = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $zip->addFile($file, basename($file));
                $originalSize += filesize($file);
            } elseif (is_dir($file)) {
                $this->addDirectoryToZip($zip, $file, basename($file));
                $originalSize += $this->getDirectorySize($file);
            }
        }
        
        $zip->close();
        
        $compressedSize = filesize($zipFile);
        $ratio = round((($originalSize - $compressedSize) / $originalSize) * 100, 2);
        
        // Eliminar archivos originales después de comprimir
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            } elseif (is_dir($file)) {
                $this->removeDirectory($file);
            }
        }
        
        return [
            'success' => true,
            'zip_file' => $zipFile,
            'original_size' => $originalSize,
            'compressed_size' => $compressedSize,
            'ratio' => $ratio
        ];
    }
    
    /**
     * Verificar integridad del backup
     */
    private function verifyBackupIntegrity(string $backupId): array
    {
        try {
            $manifestFile = $this->backupPath . $backupId . '_manifest.json';
            
            if (!file_exists($manifestFile)) {
                return ['success' => false, 'error' => 'Archivo de manifiesto no encontrado'];
            }
            
            $manifest = json_decode(file_get_contents($manifestFile), true);
            $currentChecksums = $this->calculateChecksums($backupId);
            
            $verified = true;
            $errors = [];
            
            foreach ($manifest['checksums'] as $file => $expectedChecksum) {
                if (!isset($currentChecksums[$file])) {
                    $verified = false;
                    $errors[] = "Archivo faltante: $file";
                } elseif ($currentChecksums[$file] !== $expectedChecksum) {
                    $verified = false;
                    $errors[] = "Checksum incorrecto: $file";
                }
            }
            
            return [
                'success' => $verified,
                'errors' => $errors,
                'files_verified' => count($manifest['checksums'])
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener lista de backups
     */
    public function getBackupsList(): array
    {
        try {
            return $this->db->table('system_backups')
                           ->orderBy('created_at', 'DESC')
                           ->get()
                           ->getResultArray();
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Obtener información de un backup específico
     */
    public function getBackupInfo(string $backupId): ?array
    {
        try {
            return $this->db->table('system_backups')
                           ->where('backup_id', $backupId)
                           ->get()
                           ->getRowArray();
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Eliminar backup
     */
    public function deleteBackup(string $backupId): array
    {
        try {
            // Eliminar archivos físicos
            $files = glob($this->backupPath . $backupId . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                } elseif (is_dir($file)) {
                    $this->removeDirectory($file);
                }
            }
            
            // Eliminar registro de base de datos
            $this->db->table('system_backups')
                    ->where('backup_id', $backupId)
                    ->delete();
            
            return ['success' => true, 'message' => 'Backup eliminado correctamente'];
            
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    // Métodos auxiliares privados...
    
    private function copyDirectory(string $source, string $destination): array
    {
        $files = 0;
        $size = 0;
        
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($source, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $destPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                mkdir($destPath, 0755, true);
            } else {
                copy($item, $destPath);
                $files++;
                $size += $item->getSize();
            }
        }
        
        return ['files' => $files, 'size' => $size];
    }
    
    private function calculateChecksums(string $backupId): array
    {
        $checksums = [];
        $files = glob($this->backupPath . $backupId . '_*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $checksums[basename($file)] = md5_file($file);
            }
        }
        
        return $checksums;
    }
    
    private function registerBackup(array $backupData): void
    {
        try {
            $this->db->table('system_backups')->insert([
                'backup_id' => $backupData['backup_id'],
                'type' => $backupData['type'],
                'status' => $backupData['status'],
                'file_size' => $backupData['total_size'] ?? 0,
                'compressed_size' => $backupData['compressed_size'] ?? 0,
                'duration' => $backupData['duration'] ?? 0,
                'components' => json_encode($backupData['components'] ?? []),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            $this->log("Error registrando backup: " . $e->getMessage(), 'ERROR');
        }
    }
    
    private function log(string $message, string $level = 'INFO'): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] [$level] $message\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    private function getLastBackup(): ?array
    {
        try {
            return $this->db->table('system_backups')
                           ->orderBy('created_at', 'DESC')
                           ->limit(1)
                           ->get()
                           ->getRowArray();
        } catch (\Exception $e) {
            return null;
        }
    }
    
    private function detectChanges(string $since): array
    {
        // Implementar detección de cambios desde fecha específica
        return [
            'database' => [],
            'files' => []
        ];
    }
    
    private function cleanupOldBackups(): void
    {
        try {
            $oldBackups = $this->db->table('system_backups')
                                  ->orderBy('created_at', 'DESC')
                                  ->limit(1000, $this->config['max_backups'])
                                  ->get()
                                  ->getResultArray();
            
            foreach ($oldBackups as $backup) {
                $this->deleteBackup($backup['backup_id']);
            }
        } catch (\Exception $e) {
            $this->log("Error limpiando backups antiguos: " . $e->getMessage(), 'ERROR');
        }
    }
    
    private function sendBackupNotification(array $result): void
    {
        // Implementar notificación por email/WhatsApp
        $this->log("Notificación de backup enviada");
    }
    
    private function removeDirectory(string $dir): void
    {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), ['.', '..']);
            foreach ($files as $file) {
                $path = $dir . DIRECTORY_SEPARATOR . $file;
                is_dir($path) ? $this->removeDirectory($path) : unlink($path);
            }
            rmdir($dir);
        }
    }
    
    private function getDirectorySize(string $dir): int
    {
        $size = 0;
        foreach (glob(rtrim($dir, '/') . '/*', GLOB_NOSORT) as $each) {
            $size += is_file($each) ? filesize($each) : $this->getDirectorySize($each);
        }
        return $size;
    }
    
    private function addDirectoryToZip(\ZipArchive $zip, string $dir, string $zipPath): void
    {
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipPath . '/' . substr($filePath, strlen($dir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
    }
}
