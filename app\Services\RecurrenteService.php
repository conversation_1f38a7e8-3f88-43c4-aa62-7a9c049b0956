<?php

namespace App\Services;

use App\Models\SettingModel;

class RecurrenteService
{
    private $config;
    private $settingModel;
    private $baseUrl;
    private $publicKey;
    private $secretKey;

    public function __construct()
    {
        $this->settingModel = new SettingModel();
        $this->config = $this->settingModel->getRecurrenteConfig();
        
        $this->baseUrl = $this->config['base_url'];
        $this->publicKey = $this->config['public_key'];
        $this->secretKey = $this->config['secret_key'];
    }

    /**
     * Verificar si Recurrente está configurado y habilitado
     */
    public function isEnabled()
    {
        return $this->config['enabled'] && 
               !empty($this->config['public_key']) && 
               !empty($this->config['secret_key']);
    }

    /**
     * Obtener configuración pública (sin claves secretas)
     */
    public function getPublicConfig()
    {
        return [
            'enabled' => $this->config['enabled'],
            'mode' => $this->config['mode'],
            'public_key' => $this->config['public_key'],
            'currency' => $this->config['currency'],
            'fee_percentage' => $this->config['fee_percentage']
        ];
    }

    /**
     * Crear un checkout de Recurrente
     */
    public function createCheckout($orderData, $cartItems = [])
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        // Si no se proporcionan items del carrito, crear un item único con el total
        if (empty($cartItems)) {
            $items = [
                [
                    'name' => 'Pedido #' . $orderData['order_number'] . ' - MrCell Guatemala',
                    'currency' => $this->config['currency'],
                    'amount_in_cents' => intval($orderData['total'] * 100),
                    'quantity' => 1
                ]
            ];
        } else {
            // Crear items individuales del carrito
            $items = [];
            foreach ($cartItems as $item) {
                $items[] = [
                    'name' => $item['name'],
                    'currency' => $this->config['currency'],
                    'amount_in_cents' => intval($item['price'] * 100),
                    'quantity' => $item['quantity'],
                    'image_url' => !empty($item['image']) ? base_url('uploads/products/' . $item['image']) : null
                ];
            }
        }

        $checkoutData = [
            'items' => $items,
            'success_url' => base_url('checkout/success/' . $orderData['id']),
            'cancel_url' => base_url('checkout/cancel/' . $orderData['id']),
            'user_id' => 'user_' . ($orderData['id'] ?? uniqid()),
            'metadata' => [
                'order_id' => $orderData['id'],
                'order_number' => $orderData['order_number'],
                'platform' => 'MrCell_CI4',
                'customer_name' => $orderData['customer_name'],
                'customer_email' => $orderData['customer_email'],
                'customer_phone' => $orderData['customer_phone'] ?? ''
            ]
        ];

        return $this->makeRequest('POST', '/checkouts', $checkoutData);
    }

    /**
     * Obtener información de un checkout
     */
    public function getCheckout($checkoutId)
    {
        return $this->makeRequest('GET', '/checkouts/' . $checkoutId);
    }

    /**
     * Crear un pago directo
     */
    public function createPayment($paymentData)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        $data = [
            'amount' => $paymentData['amount'],
            'currency' => $this->config['currency'],
            'description' => $paymentData['description'],
            'payment_method' => [
                'type' => 'card',
                'card' => [
                    'number' => $paymentData['card_number'],
                    'exp_month' => $paymentData['exp_month'],
                    'exp_year' => $paymentData['exp_year'],
                    'cvc' => $paymentData['cvc'],
                    'name' => $paymentData['card_holder']
                ]
            ],
            'customer' => [
                'name' => $paymentData['customer_name'],
                'email' => $paymentData['customer_email']
            ],
            'metadata' => $paymentData['metadata'] ?? []
        ];

        return $this->makeRequest('POST', '/payments', $data);
    }

    /**
     * Obtener información de un pago
     */
    public function getPayment($paymentId)
    {
        return $this->makeRequest('GET', '/payments/' . $paymentId);
    }

    /**
     * Reembolsar un pago
     */
    public function refundPayment($paymentId, $amount = null)
    {
        $data = [];
        if ($amount !== null) {
            $data['amount'] = $amount;
        }

        return $this->makeRequest('POST', '/payments/' . $paymentId . '/refunds', $data);
    }

    /**
     * Verificar webhook signature
     */
    public function verifyWebhookSignature($payload, $signature)
    {
        if (empty($this->config['webhook_secret'])) {
            return false;
        }

        $expectedSignature = hash_hmac('sha256', $payload, $this->config['webhook_secret']);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Realizar petición HTTP a la API de Recurrente
     */
    private function makeRequest($method, $endpoint, $data = null)
    {
        try {
            $url = $this->baseUrl . $endpoint;

            $headers = [
                'X-PUBLIC-KEY: ' . $this->publicKey,
                'X-SECRET-KEY: ' . $this->secretKey,
                'Content-Type: application/json',
                'Accept: application/json',
                'User-Agent: MrCell-CI4/1.0'
            ];

            log_message('debug', 'RecurrenteService::makeRequest - URL: ' . $url);
            log_message('debug', 'RecurrenteService::makeRequest - Method: ' . $method);
            if ($data) {
                log_message('debug', 'RecurrenteService::makeRequest - Data: ' . json_encode($data));
            }

            $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);

        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    $jsonData = json_encode($data);
                    if ($jsonData === false) {
                        $jsonError = json_last_error_msg();
                        log_message('error', 'Error encoding JSON: ' . $jsonError);
                        throw new \Exception('Error encoding JSON: ' . $jsonError);
                    }
                    log_message('debug', 'JSON encoded successfully: ' . strlen($jsonData) . ' bytes');
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('Error de conexión con Recurrente: ' . $error);
        }

        $decodedResponse = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = $decodedResponse['message'] ?? $decodedResponse['error'] ?? 'Error desconocido de Recurrente';
            $errorDetails = '';

            // Agregar detalles adicionales si están disponibles
            if (isset($decodedResponse['errors']) && is_array($decodedResponse['errors'])) {
                $errorDetails = ' - Detalles: ' . implode(', ', $decodedResponse['errors']);
            } elseif (isset($decodedResponse['details'])) {
                $errorDetails = ' - Detalles: ' . $decodedResponse['details'];
            }

            log_message('error', 'Error de API Recurrente (' . $httpCode . '): ' . $errorMessage . $errorDetails . ' - Respuesta completa: ' . $response);
            throw new \Exception('Error de Recurrente (' . $httpCode . '): ' . $errorMessage . $errorDetails);
        }

            return $decodedResponse;

        } catch (\Exception $e) {
            log_message('error', 'Error en RecurrenteService::makeRequest: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Formatear monto para Recurrente (en centavos)
     */
    public function formatAmount($amount)
    {
        return intval($amount * 100);
    }

    /**
     * Formatear monto desde Recurrente (de centavos a decimal)
     */
    public function parseAmount($amount)
    {
        return $amount / 100;
    }

    /**
     * Crear un producto en Recurrente
     */
    public function createProduct($productData)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        try {
            // Log de datos recibidos para debug
            log_message('debug', 'RecurrenteService::createProduct - Datos recibidos: ' . json_encode($productData));

            // Asegurar que todos los valores son del tipo correcto
            $name = is_string($productData['name']) ? $productData['name'] : (string)$productData['name'];
            $description = is_string($productData['description'] ?? '') ? ($productData['description'] ?? '') : (string)($productData['description'] ?? '');
            $imageUrl = is_string($productData['image_url'] ?? null) ? ($productData['image_url'] ?? null) : null;
            $price = is_numeric($productData['price']) ? floatval($productData['price']) : 0;
            $stockQuantity = is_numeric($productData['stock_quantity'] ?? null) ? intval($productData['stock_quantity']) : null;
            $sku = is_string($productData['sku'] ?? '') ? ($productData['sku'] ?? '') : (string)($productData['sku'] ?? '');

            $data = [
                'product' => [
                    'name' => $name,
                    'description' => $description,
                    'image_url' => $imageUrl,
                    'prices_attributes' => [
                        [
                            'currency' => $this->config['currency'],
                            'charge_type' => 'one_time',
                            'amount_in_cents' => intval($price * 100)
                        ]
                    ],
                    'cancel_url' => 'https://mrcell.com.gt/pago/recurrente/cancel/{order_id}',
                    'success_url' => 'https://mrcell.com.gt/pago/recurrente/success/{order_id}',
                    'custom_terms_and_conditions' => 'Términos y condiciones de MrCell Guatemala.',
                    'phone_requirement' => 'none',
                    'address_requirement' => 'none',
                    'billing_info_requirement' => 'none'
                ],
                'adjustable_quantity' => true,
                'inventory_quantity' => $stockQuantity,
                'metadata' => [
                    'local_product_id' => (string)$productData['id'],
                    'sku' => $sku,
                    'platform' => 'MrCell_CI4'
                ]
            ];

            log_message('debug', 'RecurrenteService::createProduct - Datos a enviar: ' . json_encode($data));

            return $this->makeRequest('POST', '/products', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en RecurrenteService::createProduct: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Actualizar un producto en Recurrente
     */
    public function updateProduct($recurrenteProductId, $productData)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        $data = [
            'product' => [
                'name' => $productData['name'],
                'description' => $productData['description'] ?? '',
                'image_url' => $productData['image_url'] ?? null,
                'prices_attributes' => [
                    [
                        'currency' => $this->config['currency'],
                        'charge_type' => 'one_time',
                        'amount_in_cents' => intval($productData['price'] * 100)
                    ]
                ]
            ],
            'adjustable_quantity' => true,
            'inventory_quantity' => $productData['stock_quantity'] ?? null,
            'metadata' => [
                'local_product_id' => $productData['id'],
                'sku' => $productData['sku'] ?? '',
                'platform' => 'MrCell_CI4'
            ]
        ];

        return $this->makeRequest('PUT', '/products/' . $recurrenteProductId, $data);
    }

    /**
     * Eliminar un producto en Recurrente
     */
    public function deleteProduct($recurrenteProductId)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        if (empty($recurrenteProductId)) {
            throw new \Exception('ID de producto de Recurrente requerido para eliminación');
        }

        try {
            $response = $this->makeRequest('DELETE', '/products/' . $recurrenteProductId);

            log_message('info', "Producto eliminado de Recurrente: {$recurrenteProductId}");
            return $response;

        } catch (\Exception $e) {
            // Si el producto no existe en Recurrente (404), considerarlo como éxito
            if (strpos($e->getMessage(), '404') !== false || strpos($e->getMessage(), 'not found') !== false) {
                log_message('info', "Producto ya no existe en Recurrente: {$recurrenteProductId}");
                return ['status' => 'not_found', 'message' => 'Producto no encontrado en Recurrente'];
            }

            log_message('error', "Error eliminando producto de Recurrente {$recurrenteProductId}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Obtener un producto de Recurrente
     */
    public function getProduct($recurrenteProductId)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        return $this->makeRequest('GET', '/products/' . $recurrenteProductId);
    }

    /**
     * Obtener todos los productos de Recurrente
     */
    public function getProducts($page = 1, $limit = 100)
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Recurrente no está configurado o habilitado');
        }

        $params = http_build_query([
            'page' => $page,
            'limit' => $limit
        ]);

        return $this->makeRequest('GET', '/products?' . $params);
    }

    /**
     * Obtener métodos de pago disponibles
     */
    public function getPaymentMethods()
    {
        return $this->makeRequest('GET', '/payment-methods');
    }

    /**
     * Validar configuración
     */
    public function validateConfig()
    {
        $errors = [];

        if (empty($this->config['public_key'])) {
            $errors[] = 'Clave pública de Recurrente no configurada';
        }

        if (empty($this->config['secret_key'])) {
            $errors[] = 'Clave secreta de Recurrente no configurada';
        }

        if (!in_array($this->config['currency'], ['GTQ', 'USD'])) {
            $errors[] = 'Moneda no válida para Recurrente';
        }

        return $errors;
    }
}
