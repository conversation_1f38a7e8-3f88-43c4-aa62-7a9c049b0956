/**
 * 🎨 Mr<PERSON>ell Main Stylesheet
 * Modern CSS for the e-commerce platform
 */

/* Import modern variables */
@import url('modern-variables.css');

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all var(--transition-normal);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-warning {
    background: var(--warning-color);
    color: #000;
}

/* Header Styles */
.header-top {
    font-size: 0.875rem;
}

.header-main {
    backdrop-filter: var(--navbar-backdrop);
    background-color: var(--navbar-bg);
}

.navbar-brand img {
    transition: transform var(--transition-normal);
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

/* Search Styles */
.search-form {
    position: relative;
}

.search-suggestions {
    max-height: 400px;
    overflow-y: auto;
    z-index: 1050;
}

.search-suggestions::-webkit-scrollbar {
    width: 4px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

/* Navigation */
.navbar-nav .nav-link {
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--warning-color);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Mega Menu */
.mega-menu {
    min-width: 600px;
    padding: 2rem;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

.mega-menu .dropdown-header {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.mega-menu .dropdown-item {
    padding: 0.5rem 0;
    transition: all var(--transition-fast);
}

.mega-menu .dropdown-item:hover {
    background: none;
    color: var(--primary-color);
    padding-left: 0.5rem;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-50px, -50px) rotate(360deg); }
}

/* Cards */
.card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Product Cards */
.product-card {
    position: relative;
}

.product-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    background: #f8f9fa;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 3;
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: all var(--transition-normal);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

/* Category Cards */
.category-card {
    border-radius: var(--radius-xl);
    overflow: hidden;
    position: relative;
    height: 200px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    transition: all var(--transition-normal);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,123,255,0.8), rgba(0,123,255,0.6));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.category-card:hover::before {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-content {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    z-index: 2;
    color: white;
}

/* Features */
.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1rem;
    transition: all var(--transition-normal);
}

.feature-icon:hover {
    transform: scale(1.1);
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-xl);
    position: relative;
    overflow: hidden;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.back-to-top:hover {
    transform: translateY(-3px);
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .category-card {
        height: 150px;
    }
    
    .mega-menu {
        min-width: auto;
        padding: 1rem;
    }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: var(--card-bg);
        color: var(--text-primary);
    }
    
    .product-image {
        background: #2d2d2d;
    }
}

.dark-mode .card {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

.dark-mode .product-image {
    background: #2d2d2d;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Utilities */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-modern {
    box-shadow: var(--shadow-md);
}

.shadow-modern-lg {
    box-shadow: var(--shadow-lg);
}

.rounded-modern {
    border-radius: var(--radius-md);
}

.rounded-modern-lg {
    border-radius: var(--radius-lg);
}
