<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddRecurrenteFieldsToProducts extends Migration
{
    public function up()
    {
        // Agregar campos de Recurrente a la tabla products
        $fields = [
            'recurrente_product_id' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'ID del producto en Recurrente'
            ],
            'recurrente_sync_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'synced', 'error', 'disabled'],
                'default' => 'pending',
                'comment' => 'Estado de sincronización con Recurrente'
            ],
            'recurrente_synced_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Fecha de última sincronización con Recurrente'
            ],
            'recurrente_storefront_link' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'Link del storefront de Recurrente'
            ]
        ];

        $this->forge->addColumn('products', $fields);

        // Agregar índices
        $this->forge->addKey('recurrente_product_id', false, false, 'products');
        $this->forge->addKey('recurrente_sync_status', false, false, 'products');
    }

    public function down()
    {
        // Eliminar campos de Recurrente
        $this->forge->dropColumn('products', [
            'recurrente_product_id',
            'recurrente_sync_status', 
            'recurrente_synced_at',
            'recurrente_storefront_link'
        ]);
    }
}
