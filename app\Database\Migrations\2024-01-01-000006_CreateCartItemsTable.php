<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCartItemsTable extends Migration
{
    public function up()
    {
        // Tabla de items del carrito
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => false,
                'comment' => 'ID de sesión del carrito',
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'quantity' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 1,
            ],
            'price' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Precio del producto al momento de agregarlo al carrito',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('session_id');
        $this->forge->addKey('product_id');
        $this->forge->addKey(['session_id', 'product_id'], false, true); // Unique constraint
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('cart_items');

        // Agregar foreign key para product_id si la tabla products existe
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }
    }

    public function down()
    {
        $this->forge->dropTable('cart_items');
    }
}
