const { chromium } = require('playwright');

async function testSimple() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('🧪 PRUEBAS SIMPLES EN LOCALHOST...\n');

        // ========================================
        // PRUEBA 1: VERIFICAR QUE EL SITIO CARGA
        // ========================================
        console.log('📝 PRUEBA 1: Verificar que el sitio carga');
        await page.goto('http://localhost:8080/');
        await page.waitForLoadState('networkidle');
        
        const title = await page.title();
        console.log(`📄 Título de la página: ${title}`);
        
        if (title.includes('MrCell') || title.includes('Guatemala')) {
            console.log('✅ Sitio carga correctamente');
        } else {
            console.log('❌ Sitio no carga correctamente');
        }

        // ========================================
        // PRUEBA 2: VERIFICAR PÁGINA DE TIENDA
        // ========================================
        console.log('\n🛍️ PRUEBA 2: Página de Tienda');
        await page.goto('http://localhost:8080/tienda');
        await page.waitForLoadState('networkidle');
        
        // Esperar a que carguen los productos
        await page.waitForTimeout(3000);
        
        const productsCount = await page.locator('.product-card').count();
        console.log(`📦 Productos encontrados: ${productsCount}`);
        
        if (productsCount > 0) {
            console.log('✅ Productos cargados correctamente');
        } else {
            console.log('❌ No se encontraron productos');
        }

        // ========================================
        // PRUEBA 3: VERIFICAR FILTRO DE MARCAS
        // ========================================
        console.log('\n🏷️ PRUEBA 3: Filtro de Marcas');
        
        // Esperar a que se carguen las marcas
        await page.waitForTimeout(5000);
        
        const brandsCount = await page.locator('.brands-list .form-check').count();
        console.log(`🏷️ Marcas disponibles: ${brandsCount}`);
        
        if (brandsCount > 0) {
            console.log('✅ Marcas cargadas correctamente');
            
            // Probar hacer click en una marca
            const firstBrandCheckbox = page.locator('.brands-list .form-check-input').first();
            const firstBrandLabel = page.locator('.brands-list .form-check-label').first();
            
            if (await firstBrandCheckbox.count() > 0) {
                const brandName = await firstBrandLabel.textContent();
                console.log(`🔍 Probando filtro por marca: ${brandName}`);
                
                await firstBrandCheckbox.check();
                await page.waitForTimeout(2000);
                
                const productsAfterFilter = await page.locator('.product-card').count();
                console.log(`📦 Productos después del filtro: ${productsAfterFilter}`);
                
                if (productsAfterFilter >= 0) {
                    console.log('✅ Filtro de marcas funciona');
                } else {
                    console.log('❌ Filtro de marcas no funciona');
                }
            }
        } else {
            console.log('❌ No se cargaron marcas');
        }

        // ========================================
        // PRUEBA 4: VERIFICAR PÁGINA DE LOGIN
        // ========================================
        console.log('\n🔐 PRUEBA 4: Página de Login');
        await page.goto('http://localhost:8080/login');
        await page.waitForLoadState('networkidle');
        
        const hasEmailField = await page.locator('input[name="email"], input[type="email"]').count() > 0;
        const hasPasswordField = await page.locator('input[name="password"], input[type="password"]').count() > 0;
        const hasSubmitButton = await page.locator('button[type="submit"], input[type="submit"]').count() > 0;
        
        console.log(`📧 Campo email: ${hasEmailField ? '✅' : '❌'}`);
        console.log(`🔒 Campo password: ${hasPasswordField ? '✅' : '❌'}`);
        console.log(`🚀 Botón submit: ${hasSubmitButton ? '✅' : '❌'}`);
        
        if (hasEmailField && hasPasswordField && hasSubmitButton) {
            console.log('✅ Formulario de login completo');
        } else {
            console.log('❌ Formulario de login incompleto');
        }

        // ========================================
        // PRUEBA 5: VERIFICAR PRODUCTO INDIVIDUAL
        // ========================================
        console.log('\n📱 PRUEBA 5: Página de Producto Individual');
        await page.goto('http://localhost:8080/tienda');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const firstProductLink = page.locator('.product-card a').first();
        if (await firstProductLink.count() > 0) {
            await firstProductLink.click();
            await page.waitForLoadState('networkidle');
            
            // Buscar botón de wishlist
            const wishlistButton = page.locator('button[onclick*="toggleWishlist"], button:has-text("Lista de Deseos")');
            const hasWishlistButton = await wishlistButton.count() > 0;
            
            console.log(`❤️ Botón de wishlist: ${hasWishlistButton ? '✅' : '❌'}`);
            
            // Buscar botón de agregar al carrito
            const addToCartButton = page.locator('button:has-text("Agregar al Carrito"), button[onclick*="addToCart"]');
            const hasAddToCartButton = await addToCartButton.count() > 0;
            
            console.log(`🛒 Botón agregar al carrito: ${hasAddToCartButton ? '✅' : '❌'}`);
            
            if (hasWishlistButton && hasAddToCartButton) {
                console.log('✅ Página de producto completa');
            } else {
                console.log('⚠️ Página de producto incompleta');
            }
        } else {
            console.log('❌ No se pudo acceder a página de producto');
        }

        // ========================================
        // PRUEBA 6: VERIFICAR FOOTER DINÁMICO
        // ========================================
        console.log('\n🦶 PRUEBA 6: Footer Dinámico');
        await page.goto('http://localhost:8080/');
        await page.waitForLoadState('networkidle');
        
        const footerContent = await page.locator('footer').textContent();
        const hasContactInfo = footerContent.includes('@') || footerContent.includes('+502');
        const hasSiteName = footerContent.includes('MrCell') || footerContent.includes('Guatemala');
        
        console.log(`📧 Información de contacto: ${hasContactInfo ? '✅' : '❌'}`);
        console.log(`🏢 Nombre del sitio: ${hasSiteName ? '✅' : '❌'}`);
        
        if (hasContactInfo && hasSiteName) {
            console.log('✅ Footer dinámico funciona');
        } else {
            console.log('⚠️ Footer usando valores estáticos');
        }

        console.log('\n🎉 PRUEBAS COMPLETADAS');

    } catch (error) {
        console.error('❌ Error durante las pruebas:', error.message);
    } finally {
        await browser.close();
    }
}

// Ejecutar pruebas
testSimple().catch(console.error);
