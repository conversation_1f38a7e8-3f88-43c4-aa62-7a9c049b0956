-- =====================================================
-- Tabla para auditoría de acciones de administradores
-- =====================================================

CREATE TABLE IF NOT EXISTS `admin_audit_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `admin_id` int(11) unsigned NOT NULL,
    `action` varchar(50) NOT NULL COMMENT 'Tipo de acción realizada',
    `resource` varchar(50) NOT NULL COMMENT 'Recurso afectado (users, products, etc)',
    `resource_id` int(11) DEFAULT NULL COMMENT 'ID del recurso afectado',
    `details` json DEFAULT NULL COMMENT 'Detalles adicionales de la acción',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'Dirección IP del administrador',
    `user_agent` text DEFAULT NULL COMMENT 'User agent del navegador',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_action` (`action`),
    KEY `idx_resource` (`resource`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_admin_audit_admin` FOREIGN KEY (`admin_id`) REFERENCES `administradores` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Log de auditoría para acciones de administradores';

-- =====================================================
-- Índices adicionales para optimizar consultas
-- =====================================================

-- Índice compuesto para consultas por admin y fecha
CREATE INDEX `idx_admin_date` ON `admin_audit_log` (`admin_id`, `created_at`);

-- Índice compuesto para consultas por recurso y acción
CREATE INDEX `idx_resource_action` ON `admin_audit_log` (`resource`, `action`);

-- =====================================================
-- Stored Procedure para limpiar logs antiguos
-- =====================================================

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_cleanup_admin_audit_log$$
CREATE PROCEDURE sp_cleanup_admin_audit_log(
    IN p_days_to_keep INT
)
BEGIN
    DECLARE v_cutoff_date DATETIME;
    DECLARE v_deleted_count INT DEFAULT 0;
    
    -- Calcular fecha de corte
    SET v_cutoff_date = DATE_SUB(NOW(), INTERVAL p_days_to_keep DAY);
    
    -- Eliminar registros antiguos
    DELETE FROM admin_audit_log 
    WHERE created_at < v_cutoff_date;
    
    -- Obtener cantidad de registros eliminados
    SET v_deleted_count = ROW_COUNT();
    
    -- Log de la operación
    SELECT CONCAT('Eliminados ', v_deleted_count, ' registros de auditoría anteriores a ', v_cutoff_date) as result;
    
END$$

DELIMITER ;

-- =====================================================
-- Stored Procedure para obtener actividad de admin
-- =====================================================

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_get_admin_activity$$
CREATE PROCEDURE sp_get_admin_activity(
    IN p_admin_id INT,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT 
        aal.id,
        aal.action,
        aal.resource,
        aal.resource_id,
        aal.details,
        aal.ip_address,
        aal.created_at,
        a.nombre as admin_name,
        a.email as admin_email
    FROM admin_audit_log aal
    INNER JOIN administradores a ON aal.admin_id = a.id
    WHERE aal.admin_id = p_admin_id
    ORDER BY aal.created_at DESC
    LIMIT p_limit OFFSET p_offset;
    
    -- También devolver el total de registros
    SELECT COUNT(*) as total_records
    FROM admin_audit_log
    WHERE admin_id = p_admin_id;
    
END$$

DELIMITER ;

-- =====================================================
-- Stored Procedure para obtener estadísticas de auditoría
-- =====================================================

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_get_audit_stats$$
CREATE PROCEDURE sp_get_audit_stats(
    IN p_days INT
)
BEGIN
    DECLARE v_start_date DATETIME;
    
    SET v_start_date = DATE_SUB(NOW(), INTERVAL p_days DAY);
    
    -- Estadísticas por acción
    SELECT 
        action,
        COUNT(*) as count,
        COUNT(DISTINCT admin_id) as unique_admins
    FROM admin_audit_log
    WHERE created_at >= v_start_date
    GROUP BY action
    ORDER BY count DESC;
    
    -- Estadísticas por administrador
    SELECT 
        a.nombre as admin_name,
        a.email as admin_email,
        COUNT(*) as total_actions,
        COUNT(DISTINCT aal.action) as unique_actions,
        MAX(aal.created_at) as last_activity
    FROM admin_audit_log aal
    INNER JOIN administradores a ON aal.admin_id = a.id
    WHERE aal.created_at >= v_start_date
    GROUP BY aal.admin_id, a.nombre, a.email
    ORDER BY total_actions DESC;
    
    -- Estadísticas por recurso
    SELECT 
        resource,
        COUNT(*) as count,
        COUNT(DISTINCT admin_id) as unique_admins
    FROM admin_audit_log
    WHERE created_at >= v_start_date
    GROUP BY resource
    ORDER BY count DESC;
    
END$$

DELIMITER ;
