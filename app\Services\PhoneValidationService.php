<?php

namespace App\Services;

class PhoneValidationService
{
    private $apiKeys;
    private $cache;
    
    public function __construct()
    {
        $this->apiKeys = [
            // Numverify (gratuito hasta 1000 requests/mes)
            'numverify' => env('NUMVERIFY_API_KEY', ''),
            
            // Twilio Lookup (muy confiable, pago)
            'twilio_sid' => env('TWILIO_ACCOUNT_SID', ''),
            'twilio_token' => env('TWILIO_AUTH_TOKEN', ''),
            
            // Abstract API (gratuito hasta 1000 requests/mes)
            'abstract' => env('ABSTRACT_PHONE_API_KEY', ''),
        ];
        
        $this->cache = \Config\Services::cache();
    }

    /**
     * Validar número de teléfono usando múltiples APIs
     */
    public function validatePhoneNumber($phoneNumber, $countryCode = 'GT')
    {
        try {
            // Limpiar número
            $cleanNumber = $this->cleanPhoneNumber($phoneNumber);
            if (!$cleanNumber) {
                return [
                    'valid' => false,
                    'error' => 'Formato de número inválido',
                    'method' => 'format_validation'
                ];
            }

            // Verificar cache primero
            $cacheKey = 'phone_validation_' . md5($cleanNumber);
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }

            // Intentar validación con diferentes APIs
            $result = $this->tryValidationAPIs($cleanNumber, $countryCode);
            
            // Guardar en cache por 24 horas
            $this->cache->save($cacheKey, $result, 86400);
            
            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Error en validación de teléfono: ' . $e->getMessage());
            
            // En caso de error, hacer validación básica
            return $this->basicValidation($phoneNumber, $countryCode);
        }
    }

    /**
     * Intentar validación con diferentes APIs
     */
    private function tryValidationAPIs($phoneNumber, $countryCode)
    {
        // 1. Intentar con Numverify (gratuito)
        if (!empty($this->apiKeys['numverify'])) {
            $result = $this->validateWithNumverify($phoneNumber);
            if ($result !== null) {
                return $result;
            }
        }

        // 2. Intentar con Abstract API (gratuito)
        if (!empty($this->apiKeys['abstract'])) {
            $result = $this->validateWithAbstract($phoneNumber);
            if ($result !== null) {
                return $result;
            }
        }

        // 3. Intentar con Twilio (pago pero muy confiable)
        if (!empty($this->apiKeys['twilio_sid']) && !empty($this->apiKeys['twilio_token'])) {
            $result = $this->validateWithTwilio($phoneNumber);
            if ($result !== null) {
                return $result;
            }
        }

        // 4. Validación básica como fallback
        return $this->basicValidation($phoneNumber, $countryCode);
    }

    /**
     * Validación con Numverify API
     */
    private function validateWithNumverify($phoneNumber)
    {
        try {
            $url = "http://apilayer.net/api/validate?access_key={$this->apiKeys['numverify']}&number={$phoneNumber}&country_code=GT&format=1";
            
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if (isset($data['valid'])) {
                return [
                    'valid' => $data['valid'],
                    'carrier' => $data['carrier'] ?? 'Desconocido',
                    'line_type' => $data['line_type'] ?? 'Desconocido',
                    'country' => $data['country_name'] ?? 'Guatemala',
                    'method' => 'numverify',
                    'formatted_number' => $data['international_format'] ?? $phoneNumber
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error con Numverify API: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Validación con Abstract API
     */
    private function validateWithAbstract($phoneNumber)
    {
        try {
            $url = "https://phonevalidation.abstractapi.com/v1/?api_key={$this->apiKeys['abstract']}&phone={$phoneNumber}";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                
                return [
                    'valid' => $data['valid'] ?? false,
                    'carrier' => $data['carrier'] ?? 'Desconocido',
                    'line_type' => $data['type'] ?? 'Desconocido',
                    'country' => $data['country']['name'] ?? 'Guatemala',
                    'method' => 'abstract',
                    'formatted_number' => $data['format']['international'] ?? $phoneNumber
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error con Abstract API: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Validación con Twilio Lookup API
     */
    private function validateWithTwilio($phoneNumber)
    {
        try {
            $url = "https://lookups.twilio.com/v1/PhoneNumbers/{$phoneNumber}?Type=carrier";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERPWD, $this->apiKeys['twilio_sid'] . ':' . $this->apiKeys['twilio_token']);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                
                return [
                    'valid' => true,
                    'carrier' => $data['carrier']['name'] ?? 'Desconocido',
                    'line_type' => $data['carrier']['type'] ?? 'Desconocido',
                    'country' => $data['country_code'] ?? 'GT',
                    'method' => 'twilio',
                    'formatted_number' => $data['phone_number'] ?? $phoneNumber
                ];
            } elseif ($httpCode === 404) {
                return [
                    'valid' => false,
                    'error' => 'Número no válido o no existe',
                    'method' => 'twilio'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error con Twilio API: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Validación básica (fallback)
     */
    private function basicValidation($phoneNumber, $countryCode = 'GT')
    {
        $cleanNumber = $this->cleanPhoneNumber($phoneNumber);
        
        // Validaciones básicas para Guatemala
        if ($countryCode === 'GT') {
            // Números guatemaltecos: 8 dígitos, empiezan con 2, 3, 4, 5, 6, 7
            if (strlen($cleanNumber) === 11 && substr($cleanNumber, 0, 3) === '502') {
                $localNumber = substr($cleanNumber, 3);
                $firstDigit = substr($localNumber, 0, 1);
                
                $valid = in_array($firstDigit, ['2', '3', '4', '5', '6', '7']);
                
                return [
                    'valid' => $valid,
                    'carrier' => 'Desconocido',
                    'line_type' => $firstDigit === '2' ? 'landline' : 'mobile',
                    'country' => 'Guatemala',
                    'method' => 'basic_validation',
                    'formatted_number' => '+502 ' . substr($localNumber, 0, 4) . '-' . substr($localNumber, 4),
                    'note' => 'Validación básica - se recomienda verificar con API externa'
                ];
            }
        }

        return [
            'valid' => false,
            'error' => 'Formato de número no válido para Guatemala',
            'method' => 'basic_validation'
        ];
    }

    /**
     * Limpiar número de teléfono
     */
    private function cleanPhoneNumber($phoneNumber)
    {
        // Remover espacios, guiones y otros caracteres
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);
        
        // Remover el + si existe
        $cleaned = ltrim($cleaned, '+');
        
        // Si tiene 8 dígitos, agregar código de país 502
        if (strlen($cleaned) === 8) {
            $cleaned = '502' . $cleaned;
        }
        
        return $cleaned;
    }

    /**
     * Validar múltiples números en lote
     */
    public function validateBatch($phoneNumbers, $countryCode = 'GT')
    {
        $results = [];
        
        foreach ($phoneNumbers as $number) {
            $results[] = $this->validatePhoneNumber($number, $countryCode);
            
            // Pequeña pausa para no sobrecargar las APIs
            usleep(100000); // 0.1 segundos
        }
        
        return $results;
    }
}
