<?php

if (!function_exists('generate_product_slug')) {
    /**
     * Genera un slug consistente para productos
     * Esta función debe usarse en todo el sistema para mantener consistencia
     * 
     * @param string $name Nombre del producto
     * @return string Slug generado
     */
    function generate_product_slug($name)
    {
        // Convertir a minúsculas
        $slug = strtolower($name);
        
        // Reemplazar caracteres especiales con sus equivalentes ASCII
        $replacements = [
            'á' => 'a', 'à' => 'a', 'ä' => 'a', 'â' => 'a', 'ā' => 'a', 'ã' => 'a',
            'é' => 'e', 'è' => 'e', 'ë' => 'e', 'ê' => 'e', 'ē' => 'e',
            'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'î' => 'i', 'ī' => 'i',
            'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'ô' => 'o', 'ō' => 'o', 'õ' => 'o',
            'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'û' => 'u', 'ū' => 'u',
            'ñ' => 'n',
            'ç' => 'c',
            // Caracteres adicionales
            '&' => 'and',
            '@' => 'at',
            '+' => 'plus',
            '%' => 'percent',
            '$' => 'dollar',
            '€' => 'euro',
            '£' => 'pound',
            '¥' => 'yen',
        ];
        
        // Aplicar reemplazos
        $slug = str_replace(array_keys($replacements), array_values($replacements), $slug);
        
        // Remover caracteres que no sean letras, números, espacios o guiones
        $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug);
        
        // Reemplazar espacios múltiples con uno solo
        $slug = preg_replace('/\s+/', ' ', $slug);
        
        // Reemplazar espacios con guiones
        $slug = str_replace(' ', '-', $slug);
        
        // Remover guiones múltiples
        $slug = preg_replace('/-+/', '-', $slug);
        
        // Remover guiones al inicio y final
        $slug = trim($slug, '-');
        
        return $slug;
    }
}

if (!function_exists('generate_unique_product_slug')) {
    /**
     * Genera un slug único para un producto
     * 
     * @param string $name Nombre del producto
     * @param int|null $excludeId ID del producto a excluir (para actualizaciones)
     * @return string Slug único generado
     */
    function generate_unique_product_slug($name, $excludeId = null)
    {
        $db = \Config\Database::connect();
        
        $baseSlug = generate_product_slug($name);
        $slug = $baseSlug;
        $counter = 1;
        
        // Verificar si el slug ya existe
        while (slug_exists_in_products($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
}

if (!function_exists('slug_exists_in_products')) {
    /**
     * Verifica si un slug ya existe en la tabla de productos
     * 
     * @param string $slug Slug a verificar
     * @param int|null $excludeId ID del producto a excluir
     * @return bool True si existe, false si no
     */
    function slug_exists_in_products($slug, $excludeId = null)
    {
        $db = \Config\Database::connect();
        
        $builder = $db->table('products');
        $builder->where('slug', $slug);
        $builder->where('deleted_at IS NULL');
        
        if ($excludeId !== null) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() > 0;
    }
}

if (!function_exists('url_encode_slug')) {
    /**
     * Codifica un slug para uso en URLs
     * 
     * @param string $slug Slug a codificar
     * @return string Slug codificado para URL
     */
    function url_encode_slug($slug)
    {
        return rawurlencode($slug);
    }
}

if (!function_exists('url_decode_slug')) {
    /**
     * Decodifica un slug desde una URL
     * 
     * @param string $slug Slug codificado
     * @return string Slug decodificado
     */
    function url_decode_slug($slug)
    {
        return rawurldecode($slug);
    }
}
