<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Services\RecurrenteService;
use App\Models\PaymentModel;
use App\Models\OrderModel;

class RecurrenteApiController extends ResourceController
{
    protected $format = 'json';
    protected $recurrenteService;
    protected $paymentModel;
    protected $orderModel;

    public function __construct()
    {
        $this->recurrenteService = new RecurrenteService();
        $this->paymentModel = new PaymentModel();
        $this->orderModel = new OrderModel();
    }

    /**
     * Crear checkout de Recurrente
     */
    public function createCheckout()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['order_id'])) {
                return $this->fail('order_id es requerido', 400);
            }

            $orderId = (int) $json['order_id'];
            $order = $this->orderModel->find($orderId);

            if (!$order) {
                return $this->fail('Pedido no encontrado', 404);
            }

            if ($order['payment_status'] === 'paid') {
                return $this->fail('Este pedido ya ha sido pagado', 400);
            }

            if (!$this->recurrenteService->isEnabled()) {
                return $this->fail('Recurrente no está disponible', 503);
            }

            // Crear checkout (sin items específicos, usará el total como un solo item)
            $checkoutData = $this->recurrenteService->createCheckout($order);

            // Crear registro de pago pendiente
            $paymentData = [
                'order_id' => $order['id'],
                'payment_method' => 'recurrente',
                'amount' => $order['total'],
                'currency' => 'GTQ',
                'status' => 'pending',
                'reference_number' => $checkoutData['id'] ?? 'REC_' . time(),
                'transaction_id' => $checkoutData['id'] ?? null,
                'gateway_response' => json_encode($checkoutData),
                'payment_date' => date('Y-m-d H:i:s'),
                'notes' => 'Checkout creado via API',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->paymentModel->insert($paymentData);

            return $this->respond([
                'status' => 'success',
                'message' => 'Checkout creado exitosamente',
                'data' => [
                    'checkout_id' => $checkoutData['id'] ?? null,
                    'checkout_url' => $checkoutData['checkout_url'] ?? null,
                    'amount' => $order['total'],
                    'currency' => 'GTQ'
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error creando checkout Recurrente: ' . $e->getMessage());
            return $this->fail('Error al crear checkout: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Verificar estado de checkout
     */
    public function checkoutStatus($checkoutId)
    {
        try {
            if (!$this->recurrenteService->isEnabled()) {
                return $this->fail('Recurrente no está disponible', 503);
            }

            $checkoutData = $this->recurrenteService->getCheckout($checkoutId);

            return $this->respond([
                'status' => 'success',
                'data' => $checkoutData
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error verificando checkout Recurrente: ' . $e->getMessage());
            return $this->fail('Error al verificar checkout: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Procesar pago directo
     */
    public function processPayment()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'order_id' => 'required|integer',
                'card_number' => 'required|min_length[13]|max_length[19]',
                'card_holder' => 'required|max_length[100]',
                'exp_month' => 'required|integer|greater_than[0]|less_than[13]',
                'exp_year' => 'required|integer|greater_than[2024]',
                'cvc' => 'required|min_length[3]|max_length[4]',
                'amount' => 'required|decimal|greater_than[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $orderId = (int) $json['order_id'];
            $order = $this->orderModel->find($orderId);

            if (!$order) {
                return $this->fail('Pedido no encontrado', 404);
            }

            if (!$this->recurrenteService->isEnabled()) {
                return $this->fail('Recurrente no está disponible', 503);
            }

            // Preparar datos para Recurrente
            $paymentData = [
                'amount' => $json['amount'],
                'description' => 'Pedido #' . $order['order_number'] . ' - MrCell Guatemala',
                'card_number' => $json['card_number'],
                'exp_month' => $json['exp_month'],
                'exp_year' => $json['exp_year'],
                'cvc' => $json['cvc'],
                'card_holder' => $json['card_holder'],
                'customer_name' => $order['customer_name'],
                'customer_email' => $order['customer_email'],
                'metadata' => [
                    'order_id' => $order['id'],
                    'order_number' => $order['order_number']
                ]
            ];

            // Procesar pago
            $result = $this->recurrenteService->createPayment($paymentData);

            // Enmascarar número de tarjeta para logs
            $maskedCard = str_repeat('*', strlen($json['card_number']) - 4) . substr($json['card_number'], -4);
            log_message('info', "Procesando pago Recurrente con tarjeta terminada en: {$maskedCard}");

            if ($result['status'] === 'succeeded') {
                // Crear registro de pago exitoso
                $paymentRecord = [
                    'order_id' => $orderId,
                    'payment_method' => 'recurrente',
                    'amount' => $json['amount'],
                    'currency' => 'GTQ',
                    'status' => 'completed',
                    'reference_number' => $result['id'],
                    'transaction_id' => $result['id'],
                    'gateway_response' => json_encode($result),
                    'card_last_four' => substr($json['card_number'], -4),
                    'card_type' => $this->detectCardType($json['card_number']),
                    'payment_date' => date('Y-m-d H:i:s'),
                    'notes' => 'Pago procesado exitosamente con Recurrente',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $this->paymentModel->insert($paymentRecord);

                // Actualizar estado del pedido
                $this->orderModel->update($orderId, [
                    'payment_status' => 'paid',
                    'status' => 'confirmed',
                    'notes' => 'Pago completado con Recurrente - Tarjeta terminada en ' . substr($json['card_number'], -4)
                ]);

                return $this->respond([
                    'status' => 'success',
                    'message' => 'Pago procesado exitosamente',
                    'data' => [
                        'transaction_id' => $result['id'],
                        'amount' => $json['amount'],
                        'currency' => 'GTQ',
                        'card_last_four' => substr($json['card_number'], -4)
                    ]
                ]);
            } else {
                return $this->fail('Pago rechazado: ' . ($result['failure_reason'] ?? 'Motivo desconocido'), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error procesando pago Recurrente: ' . $e->getMessage());
            return $this->fail('Error al procesar pago: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Webhook de Recurrente
     */
    public function webhook()
    {
        try {
            // Log del webhook recibido para debugging
            $payload = $this->request->getBody();
            $headers = $this->request->getHeaders();

            log_message('info', 'Webhook Recurrente recibido - Headers: ' . json_encode($headers));
            log_message('info', 'Webhook Recurrente recibido - Payload: ' . $payload);

            // Obtener firma del webhook (puede venir en diferentes headers según la versión)
            $signature = $this->request->getHeaderLine('X-Recurrente-Signature')
                      ?: $this->request->getHeaderLine('X-Signature')
                      ?: $this->request->getHeaderLine('Recurrente-Signature');

            // Verificar firma del webhook si está configurada
            if (!empty($signature)) {
                if (!$this->recurrenteService->verifyWebhookSignature($payload, $signature)) {
                    log_message('warning', 'Webhook Recurrente con firma inválida');
                    return $this->fail('Firma inválida', 401);
                }
            } else {
                log_message('warning', 'Webhook Recurrente sin firma - verificar configuración');
            }

            $data = json_decode($payload, true);

            if (!$data) {
                log_message('error', 'Webhook Recurrente - JSON inválido: ' . $payload);
                return $this->fail('JSON inválido', 400);
            }

            // Manejar diferentes formatos de webhook
            $eventType = $data['type'] ?? $data['event'] ?? $data['event_type'] ?? null;
            $eventData = $data['data'] ?? $data['object'] ?? $data;

            if (!$eventType) {
                log_message('error', 'Webhook Recurrente sin tipo de evento: ' . json_encode($data));
                return $this->fail('Tipo de evento requerido', 400);
            }

            log_message('info', 'Webhook Recurrente procesando evento: ' . $eventType);

            // Manejar eventos según la documentación de Recurrente
            switch ($eventType) {
                // Eventos de checkout
                case 'checkout.completed':
                case 'checkout.succeeded':
                case 'checkout.paid':
                    $this->handleCheckoutCompleted($eventData);
                    break;

                case 'checkout.failed':
                case 'checkout.cancelled':
                case 'checkout.expired':
                    $this->handleCheckoutFailed($eventData);
                    break;

                // Eventos de pago
                case 'payment.succeeded':
                case 'payment.completed':
                case 'payment.paid':
                    $this->handlePaymentSucceeded($eventData);
                    break;

                case 'payment.failed':
                case 'payment.declined':
                case 'payment.cancelled':
                    $this->handlePaymentFailed($eventData);
                    break;

                // Eventos de suscripción (si se usan en el futuro)
                case 'subscription.created':
                case 'subscription.updated':
                case 'subscription.cancelled':
                    $this->handleSubscriptionEvent($eventType, $eventData);
                    break;

                // Eventos de reembolso
                case 'refund.created':
                case 'refund.succeeded':
                    $this->handleRefundEvent($eventData);
                    break;

                default:
                    log_message('info', 'Tipo de webhook Recurrente no manejado: ' . $eventType);
                    // Aún así devolver success para evitar reintentos innecesarios
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Webhook procesado correctamente',
                'event_type' => $eventType
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error procesando webhook Recurrente: ' . $e->getMessage());
            log_message('error', 'Webhook payload: ' . ($payload ?? 'N/A'));
            return $this->fail('Error interno', 500);
        }
    }

    /**
     * Manejar checkout completado
     */
    private function handleCheckoutCompleted($checkoutData)
    {
        // Extraer order_id de diferentes posibles ubicaciones
        $orderId = $checkoutData['metadata']['order_id']
                ?? $checkoutData['order_id']
                ?? $checkoutData['external_id']
                ?? null;

        if (!$orderId) {
            log_message('warning', 'Webhook checkout completado sin order_id: ' . json_encode($checkoutData));
            return;
        }

        // Buscar pago por diferentes criterios
        $checkoutId = $checkoutData['id'] ?? $checkoutData['checkout_id'] ?? null;

        $payment = null;
        if ($checkoutId) {
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->where('transaction_id', $checkoutId)
                                         ->first();
        }

        // Si no se encuentra por transaction_id, buscar por order_id
        if (!$payment) {
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->where('status', 'pending')
                                         ->orderBy('created_at', 'DESC')
                                         ->first();
        }

        if ($payment && $payment['status'] !== 'completed') {
            // Extraer información adicional del webhook
            $amount = isset($checkoutData['amount']) ? $checkoutData['amount'] / 100 : $payment['amount'];
            $currency = $checkoutData['currency'] ?? 'GTQ';
            $paymentMethod = $checkoutData['payment_method'] ?? 'card';

            // Actualizar pago
            $updateData = [
                'status' => 'completed',
                'amount' => $amount,
                'currency' => strtoupper($currency),
                'gateway_response' => json_encode($checkoutData),
                'notes' => "Pago completado via webhook - Método: {$paymentMethod}",
                'payment_date' => date('Y-m-d H:i:s')
            ];

            if ($checkoutId) {
                $updateData['transaction_id'] = $checkoutId;
            }

            $this->paymentModel->update($payment['id'], $updateData);

            // Actualizar pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'notes' => "Pago completado con Recurrente via webhook - Monto: {$currency} {$amount}"
            ]);

            log_message('info', "Pago Recurrente completado para pedido {$orderId} - Monto: {$currency} {$amount}");

            // Enviar notificación de pago completado (opcional)
            $this->sendPaymentNotification($orderId, 'completed', $amount, $currency);

        } else {
            log_message('warning', "No se encontró pago pendiente para pedido {$orderId} o ya está completado");
        }
    }

    /**
     * Manejar checkout fallido
     */
    private function handleCheckoutFailed($checkoutData)
    {
        // Extraer order_id de diferentes posibles ubicaciones
        $orderId = $checkoutData['metadata']['order_id']
                ?? $checkoutData['order_id']
                ?? $checkoutData['external_id']
                ?? null;

        if (!$orderId) {
            log_message('warning', 'Webhook checkout fallido sin order_id: ' . json_encode($checkoutData));
            return;
        }

        // Buscar pago
        $checkoutId = $checkoutData['id'] ?? $checkoutData['checkout_id'] ?? null;

        $payment = null;
        if ($checkoutId) {
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->where('transaction_id', $checkoutId)
                                         ->first();
        }

        // Si no se encuentra por transaction_id, buscar por order_id
        if (!$payment) {
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->where('status', 'pending')
                                         ->orderBy('created_at', 'DESC')
                                         ->first();
        }

        if ($payment) {
            // Extraer información del error
            $failureReason = $checkoutData['failure_reason']
                          ?? $checkoutData['error_message']
                          ?? $checkoutData['decline_reason']
                          ?? 'Motivo desconocido';

            $errorCode = $checkoutData['error_code']
                      ?? $checkoutData['decline_code']
                      ?? null;

            $updateData = [
                'status' => 'failed',
                'gateway_response' => json_encode($checkoutData),
                'notes' => "Pago fallido: {$failureReason}" . ($errorCode ? " (Código: {$errorCode})" : '')
            ];

            if ($checkoutId) {
                $updateData['transaction_id'] = $checkoutId;
            }

            $this->paymentModel->update($payment['id'], $updateData);

            // Actualizar pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'failed',
                'status' => 'payment_failed',
                'notes' => "Pago fallido con Recurrente: {$failureReason}"
            ]);

            log_message('info', "Pago Recurrente fallido para pedido {$orderId} - Razón: {$failureReason}");

            // Enviar notificación de pago fallido (opcional)
            $this->sendPaymentNotification($orderId, 'failed', 0, 'GTQ', $failureReason);
        }
    }

    /**
     * Manejar pago exitoso
     */
    private function handlePaymentSucceeded($paymentData)
    {
        // Similar a handleCheckoutCompleted pero para pagos directos
        $this->handleCheckoutCompleted($paymentData);
    }

    /**
     * Manejar pago fallido
     */
    private function handlePaymentFailed($paymentData)
    {
        // Similar a handleCheckoutFailed pero para pagos directos
        $this->handleCheckoutFailed($paymentData);
    }

    /**
     * Manejar eventos de suscripción
     */
    private function handleSubscriptionEvent($eventType, $subscriptionData)
    {
        log_message('info', "Evento de suscripción recibido: {$eventType}");

        // Aquí se puede implementar lógica para manejar suscripciones en el futuro
        // Por ahora solo loggeamos el evento

        $orderId = $subscriptionData['metadata']['order_id'] ?? null;
        if ($orderId) {
            log_message('info', "Evento de suscripción {$eventType} para pedido {$orderId}");
        }
    }

    /**
     * Manejar eventos de reembolso
     */
    private function handleRefundEvent($refundData)
    {
        log_message('info', 'Evento de reembolso recibido');

        $orderId = $refundData['metadata']['order_id'] ?? null;
        $refundAmount = $refundData['amount'] ?? 0;

        if (!$orderId) {
            log_message('warning', 'Evento de reembolso sin order_id');
            return;
        }

        // Buscar el pago original
        $payment = $this->paymentModel->where('order_id', $orderId)
                                     ->where('payment_method', 'recurrente')
                                     ->where('status', 'completed')
                                     ->first();

        if ($payment) {
            // Crear registro de reembolso
            $refundRecord = [
                'payment_id' => $payment['id'],
                'order_id' => $orderId,
                'amount' => $refundAmount / 100, // Convertir de centavos
                'currency' => $refundData['currency'] ?? 'GTQ',
                'reason' => $refundData['reason'] ?? 'Reembolso procesado',
                'status' => 'completed',
                'gateway_response' => json_encode($refundData),
                'processed_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Insertar en tabla de reembolsos (si existe)
            try {
                $this->db->table('refunds')->insert($refundRecord);
            } catch (\Exception $e) {
                log_message('error', 'Error guardando reembolso: ' . $e->getMessage());
            }

            // Actualizar estado del pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'refunded',
                'status' => 'refunded',
                'notes' => 'Reembolso procesado via webhook - Monto: ' . ($refundAmount / 100)
            ]);

            log_message('info', "Reembolso procesado para pedido {$orderId} - Monto: " . ($refundAmount / 100));
        }
    }

    /**
     * Enviar notificación de pago
     */
    private function sendPaymentNotification($orderId, $status, $amount = 0, $currency = 'GTQ', $reason = null)
    {
        try {
            // Obtener información del pedido
            $order = $this->orderModel->find($orderId);
            if (!$order) {
                return;
            }

            $message = '';
            $subject = '';

            switch ($status) {
                case 'completed':
                    $subject = 'Pago Confirmado - Pedido #' . $order['order_number'];
                    $message = "Su pago de {$currency} " . number_format($amount, 2) . " ha sido procesado exitosamente.";
                    break;

                case 'failed':
                    $subject = 'Pago Fallido - Pedido #' . $order['order_number'];
                    $message = "Su pago no pudo ser procesado.";
                    if ($reason) {
                        $message .= " Razón: {$reason}";
                    }
                    break;
            }

            // Aquí se puede implementar el envío de email, SMS, push notification, etc.
            log_message('info', "Notificación de pago {$status} para pedido {$orderId}: {$message}");

            // Ejemplo de integración con sistema de notificaciones
            /*
            $notificationService = new \App\Services\NotificationService();
            $notificationService->sendEmail([
                'to' => $order['customer_email'],
                'subject' => $subject,
                'message' => $message,
                'template' => 'payment_' . $status
            ]);
            */

        } catch (\Exception $e) {
            log_message('error', 'Error enviando notificación de pago: ' . $e->getMessage());
        }
    }

    /**
     * Detectar tipo de tarjeta
     */
    private function detectCardType($cardNumber)
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber)) {
            return 'mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        } else {
            return 'unknown';
        }
    }
}
