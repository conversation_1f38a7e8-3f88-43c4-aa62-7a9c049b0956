<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Modelo de Wishlist/Lista de Deseos
 * Compatible con cPanel hosting
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class WishlistModel extends Model
{
    protected $table = 'wishlist';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'product_id',
        'notes',
        'priority',
        'notification_enabled',
        'price_alert_threshold',
        'ip_address',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'product_id' => 'required|integer',
        'notes' => 'max_length[500]',
        'priority' => 'in_list[low,medium,high]',
        'notification_enabled' => 'in_list[0,1]',
        'price_alert_threshold' => 'decimal'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'El ID de usuario es requerido',
            'integer' => 'El ID de usuario debe ser un número'
        ],
        'product_id' => [
            'required' => 'El ID de producto es requerido',
            'integer' => 'El ID de producto debe ser un número'
        ],
        'notes' => [
            'max_length' => 'Las notas no pueden exceder 500 caracteres'
        ],
        'priority' => [
            'in_list' => 'La prioridad debe ser: low, medium o high'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['beforeInsert'];
    protected $beforeUpdate = ['beforeUpdate'];

    /**
     * Callback antes de insertar
     */
    protected function beforeInsert(array $data)
    {
        $data['data']['ip_address'] = $this->request->getIPAddress();
        return $data;
    }

    /**
     * Callback antes de actualizar
     */
    protected function beforeUpdate(array $data)
    {
        return $data;
    }

    /**
     * Obtener wishlist de un usuario con detalles de productos
     * 
     * @param int $userId ID del usuario
     * @param array $options Opciones adicionales
     * @return array
     */
    public function getUserWishlistWithProducts(int $userId, array $options = []): array
    {
        $limit = $options['limit'] ?? 50;
        $offset = $options['offset'] ?? 0;
        $priority = $options['priority'] ?? null;
        $sortBy = $options['sort_by'] ?? 'created_at';
        $sortOrder = $options['sort_order'] ?? 'DESC';

        $builder = $this->db->table($this->table . ' w');
        $builder->select('w.*, p.name, p.slug, p.price_regular, p.price_sale, p.featured_image, 
                         p.stock_quantity, p.stock_status, p.is_active, p.rating_average, p.rating_count,
                         c.name as category_name, b.name as brand_name');
        $builder->join('products p', 'w.product_id = p.id', 'left');
        $builder->join('categories c', 'p.category_id = c.id', 'left');
        $builder->join('brands b', 'p.brand_id = b.id', 'left');
        $builder->where('w.user_id', $userId);
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');

        if ($priority) {
            $builder->where('w.priority', $priority);
        }

        // Ordenamiento
        switch ($sortBy) {
            case 'name':
                $builder->orderBy('p.name', $sortOrder);
                break;
            case 'price':
                $builder->orderBy('COALESCE(p.price_sale, p.price_regular)', $sortOrder);
                break;
            case 'priority':
                $builder->orderBy("FIELD(w.priority, 'high', 'medium', 'low')", 'ASC');
                break;
            case 'stock':
                $builder->orderBy('p.stock_quantity', $sortOrder);
                break;
            default:
                $builder->orderBy('w.' . $sortBy, $sortOrder);
        }

        $builder->limit($limit, $offset);

        $query = $builder->get();
        $results = $query->getResultArray();

        // Procesar resultados
        foreach ($results as &$item) {
            $item['final_price'] = $item['price_sale'] ?? $item['price_regular'];
            $item['has_discount'] = !empty($item['price_sale']) && $item['price_sale'] < $item['price_regular'];
            $item['discount_percentage'] = $item['has_discount'] ? 
                round((($item['price_regular'] - $item['price_sale']) / $item['price_regular']) * 100) : 0;
            $item['in_stock'] = $item['stock_quantity'] > 0 && $item['stock_status'] === 'in_stock';
            $item['image_url'] = $item['featured_image'] ? base_url('uploads/' . $item['featured_image']) : null;
            $item['product_url'] = base_url('producto/' . $item['slug']);
            
            // Verificar si el precio actual es menor al threshold de alerta
            $item['price_alert_triggered'] = false;
            if ($item['price_alert_threshold'] && $item['final_price'] <= $item['price_alert_threshold']) {
                $item['price_alert_triggered'] = true;
            }
        }

        return $results;
    }

    /**
     * Contar items en wishlist de usuario
     * 
     * @param int $userId ID del usuario
     * @return int
     */
    public function getUserWishlistCount(int $userId): int
    {
        return $this->where('user_id', $userId)
                   ->join('products p', 'product_id = p.id', 'left')
                   ->where('p.is_active', 1)
                   ->where('p.deleted_at IS NULL')
                   ->countAllResults();
    }

    /**
     * Verificar si un producto está en wishlist del usuario
     * 
     * @param int $userId ID del usuario
     * @param int $productId ID del producto
     * @return bool
     */
    public function isInWishlist(int $userId, int $productId): bool
    {
        return $this->where('user_id', $userId)
                   ->where('product_id', $productId)
                   ->countAllResults() > 0;
    }

    /**
     * Agregar producto a wishlist
     * 
     * @param int $userId ID del usuario
     * @param int $productId ID del producto
     * @param array $options Opciones adicionales
     * @return array
     */
    public function addToWishlist(int $userId, int $productId, array $options = []): array
    {
        try {
            // Verificar si ya existe
            if ($this->isInWishlist($userId, $productId)) {
                return [
                    'success' => false,
                    'message' => 'El producto ya está en tu lista de deseos',
                    'code' => 'ALREADY_EXISTS'
                ];
            }

            // Verificar que el producto existe y está activo
            $productModel = new \App\Models\ProductModel();
            $product = $productModel->where('id', $productId)
                                  ->where('is_active', 1)
                                  ->where('deleted_at IS NULL')
                                  ->first();

            if (!$product) {
                return [
                    'success' => false,
                    'message' => 'Producto no encontrado o no disponible',
                    'code' => 'PRODUCT_NOT_FOUND'
                ];
            }

            // Preparar datos
            $data = [
                'user_id' => $userId,
                'product_id' => $productId,
                'notes' => $options['notes'] ?? null,
                'priority' => $options['priority'] ?? 'medium',
                'notification_enabled' => $options['notification_enabled'] ?? 1,
                'price_alert_threshold' => $options['price_alert_threshold'] ?? null
            ];

            // Insertar
            $insertId = $this->insert($data);

            if ($insertId) {
                $count = $this->getUserWishlistCount($userId);
                
                return [
                    'success' => true,
                    'message' => 'Producto agregado a tu lista de deseos',
                    'data' => [
                        'wishlist_id' => $insertId,
                        'count' => $count,
                        'product' => [
                            'id' => $product['id'],
                            'name' => $product['name'],
                            'slug' => $product['slug']
                        ]
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Error al agregar producto a la lista de deseos',
                    'code' => 'INSERT_FAILED'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistModel::addToWishlist: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Error interno del servidor',
                'code' => 'SERVER_ERROR'
            ];
        }
    }

    /**
     * Remover producto de wishlist
     * 
     * @param int $userId ID del usuario
     * @param int $productId ID del producto
     * @return array
     */
    public function removeFromWishlist(int $userId, int $productId): array
    {
        try {
            $deleted = $this->where('user_id', $userId)
                           ->where('product_id', $productId)
                           ->delete();

            if ($deleted) {
                $count = $this->getUserWishlistCount($userId);
                
                return [
                    'success' => true,
                    'message' => 'Producto eliminado de tu lista de deseos',
                    'data' => [
                        'count' => $count
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Producto no encontrado en tu lista de deseos',
                    'code' => 'NOT_FOUND'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistModel::removeFromWishlist: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Error interno del servidor',
                'code' => 'SERVER_ERROR'
            ];
        }
    }

    /**
     * Actualizar item de wishlist
     * 
     * @param int $userId ID del usuario
     * @param int $productId ID del producto
     * @param array $data Datos a actualizar
     * @return array
     */
    public function updateWishlistItem(int $userId, int $productId, array $data): array
    {
        try {
            $allowedFields = ['notes', 'priority', 'notification_enabled', 'price_alert_threshold'];
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            if (empty($updateData)) {
                return [
                    'success' => false,
                    'message' => 'No hay datos válidos para actualizar',
                    'code' => 'NO_DATA'
                ];
            }

            $updated = $this->where('user_id', $userId)
                           ->where('product_id', $productId)
                           ->set($updateData)
                           ->update();

            if ($updated) {
                return [
                    'success' => true,
                    'message' => 'Lista de deseos actualizada correctamente',
                    'data' => $updateData
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Producto no encontrado en tu lista de deseos',
                    'code' => 'NOT_FOUND'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistModel::updateWishlistItem: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Error interno del servidor',
                'code' => 'SERVER_ERROR'
            ];
        }
    }

    /**
     * Obtener productos con alertas de precio activadas
     * 
     * @param int $userId ID del usuario (opcional)
     * @return array
     */
    public function getProductsWithPriceAlerts(int $userId = null): array
    {
        $builder = $this->db->table($this->table . ' w');
        $builder->select('w.*, p.name, p.price_regular, p.price_sale, u.email, u.phone');
        $builder->join('products p', 'w.product_id = p.id', 'left');
        $builder->join('users u', 'w.user_id = u.id', 'left');
        $builder->where('w.notification_enabled', 1);
        $builder->where('w.price_alert_threshold IS NOT NULL');
        $builder->where('COALESCE(p.price_sale, p.price_regular) <= w.price_alert_threshold');
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');

        if ($userId) {
            $builder->where('w.user_id', $userId);
        }

        $query = $builder->get();
        return $query->getResultArray();
    }

    /**
     * Limpiar wishlist de productos inactivos o eliminados
     * 
     * @return int Número de items eliminados
     */
    public function cleanupInactiveProducts(): int
    {
        $builder = $this->db->table($this->table . ' w');
        $builder->join('products p', 'w.product_id = p.id', 'left');
        $builder->where('(p.is_active = 0 OR p.deleted_at IS NOT NULL OR p.id IS NULL)');
        
        $query = $builder->get();
        $itemsToDelete = $query->getResultArray();
        
        if (!empty($itemsToDelete)) {
            $ids = array_column($itemsToDelete, 'id');
            $this->whereIn('id', $ids)->delete();
        }
        
        return count($itemsToDelete);
    }

    /**
     * Obtener estadísticas de wishlist
     * 
     * @param int $userId ID del usuario (opcional)
     * @return array
     */
    public function getWishlistStats(int $userId = null): array
    {
        $builder = $this->db->table($this->table . ' w');
        
        if ($userId) {
            $builder->where('w.user_id', $userId);
        }
        
        // Total items
        $totalItems = $builder->countAllResults(false);
        
        // Por prioridad
        $builder->select('w.priority, COUNT(*) as count');
        $builder->groupBy('w.priority');
        $priorityStats = $builder->get()->getResultArray();
        
        // Con alertas de precio
        $builder->resetQuery();
        $builder->where('w.notification_enabled', 1);
        $builder->where('w.price_alert_threshold IS NOT NULL');
        if ($userId) {
            $builder->where('w.user_id', $userId);
        }
        $withAlerts = $builder->countAllResults();
        
        return [
            'total_items' => $totalItems,
            'priority_breakdown' => $priorityStats,
            'with_price_alerts' => $withAlerts
        ];
    }
}
