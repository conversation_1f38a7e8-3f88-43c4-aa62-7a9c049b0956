<?php

/**
 * Script para probar los webhooks de Recurrente
 */

echo "=== PRUEBA DE WEBHOOKS DE RECURRENTE ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "=======================================\n\n";

// URL del webhook
$webhookUrl = 'https://mrcell.com.gt/api/webhooks/recurrente';

// Función para enviar webhook
function sendWebhook($url, $data, $signature = null) {
    $headers = [
        'Content-Type: application/json',
        'User-Agent: Recurrente-Webhook/1.0'
    ];
    
    if ($signature) {
        $headers[] = 'X-Recurrente-Signature: ' . $signature;
    }
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false, // Solo para pruebas
        CURLOPT_SSL_VERIFYHOST => false  // Solo para pruebas
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// Casos de prueba de webhooks
$webhookTests = [
    [
        'name' => 'Checkout Completado',
        'data' => [
            'type' => 'checkout.completed',
            'data' => [
                'id' => 'checkout_test_' . time(),
                'amount' => 15000, // En centavos (Q150.00)
                'currency' => 'GTQ',
                'status' => 'completed',
                'payment_method' => 'card',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '1', // Usar un order_id que exista
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Checkout Fallido',
        'data' => [
            'type' => 'checkout.failed',
            'data' => [
                'id' => 'checkout_failed_' . time(),
                'amount' => 25000, // En centavos (Q250.00)
                'currency' => 'GTQ',
                'status' => 'failed',
                'failure_reason' => 'Tarjeta declinada',
                'error_code' => 'card_declined',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '2', // Usar un order_id que exista
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Pago Exitoso',
        'data' => [
            'type' => 'payment.succeeded',
            'data' => [
                'id' => 'payment_success_' . time(),
                'amount' => 35000, // En centavos (Q350.00)
                'currency' => 'GTQ',
                'status' => 'succeeded',
                'payment_method' => 'visa',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '3', // Usar un order_id que exista
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Pago Fallido',
        'data' => [
            'type' => 'payment.failed',
            'data' => [
                'id' => 'payment_failed_' . time(),
                'amount' => 45000, // En centavos (Q450.00)
                'currency' => 'GTQ',
                'status' => 'failed',
                'failure_reason' => 'Fondos insuficientes',
                'error_code' => 'insufficient_funds',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '4', // Usar un order_id que exista
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Reembolso Creado',
        'data' => [
            'type' => 'refund.created',
            'data' => [
                'id' => 'refund_' . time(),
                'amount' => 15000, // En centavos (Q150.00)
                'currency' => 'GTQ',
                'status' => 'succeeded',
                'reason' => 'Solicitud del cliente',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '1', // Usar un order_id que exista
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Evento No Manejado',
        'data' => [
            'type' => 'subscription.updated',
            'data' => [
                'id' => 'sub_' . time(),
                'status' => 'active',
                'created_at' => date('c'),
                'metadata' => [
                    'order_id' => '5',
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'Webhook Sin Firma',
        'data' => [
            'type' => 'checkout.completed',
            'data' => [
                'id' => 'checkout_no_sig_' . time(),
                'amount' => 10000,
                'currency' => 'GTQ',
                'status' => 'completed',
                'metadata' => [
                    'order_id' => '6',
                    'platform' => 'MrCell_CI4'
                ]
            ]
        ]
    ],
    [
        'name' => 'JSON Inválido',
        'data' => 'invalid_json_data'
    ]
];

// Ejecutar pruebas
foreach ($webhookTests as $test) {
    echo "🧪 Probando: {$test['name']}\n";
    
    // Generar firma HMAC (simulada)
    $signature = null;
    if ($test['name'] !== 'Webhook Sin Firma' && $test['name'] !== 'JSON Inválido') {
        $payload = json_encode($test['data']);
        $secret = 'test_webhook_secret'; // Usar el secret real en producción
        $signature = hash_hmac('sha256', $payload, $secret);
    }
    
    echo "   Enviando webhook...\n";
    
    $result = sendWebhook($webhookUrl, $test['data'], $signature);
    
    echo "   HTTP Code: {$result['http_code']}\n";
    
    if ($result['error']) {
        echo "   ❌ Error de cURL: {$result['error']}\n";
    } else {
        $decodedResponse = json_decode($result['response'], true);
        
        if ($decodedResponse) {
            echo "   📄 Respuesta:\n";
            echo "      Status: " . ($decodedResponse['status'] ?? 'N/A') . "\n";
            echo "      Message: " . ($decodedResponse['message'] ?? 'N/A') . "\n";
            
            if (isset($decodedResponse['event_type'])) {
                echo "      Event Type: {$decodedResponse['event_type']}\n";
            }
        } else {
            echo "   📄 Respuesta cruda: " . substr($result['response'], 0, 200) . "\n";
        }
    }
    
    // Determinar si la prueba fue exitosa
    $success = false;
    switch ($test['name']) {
        case 'JSON Inválido':
            $success = $result['http_code'] == 400;
            break;
        case 'Webhook Sin Firma':
            $success = $result['http_code'] == 200; // Debería funcionar sin firma
            break;
        default:
            $success = $result['http_code'] == 200;
    }
    
    echo "   Resultado: " . ($success ? '✅ EXITOSO' : '❌ FALLIDO') . "\n";
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
    
    // Pausa entre pruebas
    sleep(1);
}

echo "📊 RESUMEN DE PRUEBAS:\n";
echo "====================\n";
echo "Total de pruebas ejecutadas: " . count($webhookTests) . "\n";
echo "\n";

echo "🔧 CONFIGURACIÓN REQUERIDA:\n";
echo "===========================\n";
echo "URL del Webhook: {$webhookUrl}\n";
echo "URL de Éxito: https://mrcell.com.gt/payment/recurrente/success/{order_id}\n";
echo "URL de Cancelación: https://mrcell.com.gt/payment/recurrente/cancel/{order_id}\n";
echo "\n";

echo "📋 EVENTOS SOPORTADOS:\n";
echo "======================\n";
echo "✅ checkout.completed - Checkout completado exitosamente\n";
echo "✅ checkout.failed - Checkout fallido o cancelado\n";
echo "✅ payment.succeeded - Pago procesado exitosamente\n";
echo "✅ payment.failed - Pago fallido o declinado\n";
echo "✅ refund.created - Reembolso procesado\n";
echo "ℹ️  subscription.* - Eventos de suscripción (logged)\n";
echo "ℹ️  Otros eventos - Se registran pero no se procesan\n";
echo "\n";

echo "🔒 SEGURIDAD:\n";
echo "=============\n";
echo "✅ Verificación de firma HMAC SHA256\n";
echo "✅ Validación de JSON\n";
echo "✅ Logging detallado de eventos\n";
echo "✅ Manejo de errores robusto\n";
echo "\n";

echo "🎉 Pruebas de webhooks completadas.\n";
