<?php

namespace App\Libraries;

/**
 * Optimizador SEO Avanzado
 * Sistema completo de optimización SEO para MrCell Guatemala
 */
class SEOOptimizer
{
    private $config;
    private $db;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        
        $this->config = [
            'site_name' => 'MrCell Guatemala',
            'site_description' => 'La tienda de celulares y tecnología más avanzada de Guatemala. Encuentra los mejores smartphones, accesorios y más con garantía y envío gratis.',
            'site_keywords' => 'celulares guatemala, smartphones, iphone guatemala, samsung guatemala, tecnología, accesorios móviles',
            'site_url' => base_url(),
            'default_image' => base_url('assets/images/mrcell-og-image.jpg'),
            'twitter_handle' => '@MrCellGuatemala',
            'facebook_page' => 'https://facebook.com/MrCellGuatemala',
            'instagram_handle' => '@mrcellguatemala',
            'organization_schema' => [
                'name' => 'MrCell Guatemala',
                'type' => 'ElectronicsStore',
                'address' => [
                    'streetAddress' => 'Guatemala City',
                    'addressLocality' => 'Guatemala',
                    'addressCountry' => 'GT'
                ],
                'telephone' => '+502-1234-5678',
                'email' => '<EMAIL>'
            ]
        ];
    }
    
    /**
     * Generar meta tags para una página
     */
    public function generateMetaTags(array $pageData = []): string
    {
        $title = $this->generateTitle($pageData);
        $description = $this->generateDescription($pageData);
        $keywords = $this->generateKeywords($pageData);
        $image = $pageData['image'] ?? $this->config['default_image'];
        $url = $pageData['url'] ?? current_url();
        
        $metaTags = [];
        
        // Meta tags básicos
        $metaTags[] = "<title>$title</title>";
        $metaTags[] = "<meta name=\"description\" content=\"$description\">";
        $metaTags[] = "<meta name=\"keywords\" content=\"$keywords\">";
        $metaTags[] = "<meta name=\"robots\" content=\"index, follow\">";
        $metaTags[] = "<meta name=\"author\" content=\"{$this->config['site_name']}\">";
        $metaTags[] = "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";
        $metaTags[] = "<meta charset=\"UTF-8\">";
        
        // Open Graph (Facebook)
        $metaTags[] = "<meta property=\"og:title\" content=\"$title\">";
        $metaTags[] = "<meta property=\"og:description\" content=\"$description\">";
        $metaTags[] = "<meta property=\"og:image\" content=\"$image\">";
        $metaTags[] = "<meta property=\"og:url\" content=\"$url\">";
        $metaTags[] = "<meta property=\"og:type\" content=\"" . ($pageData['type'] ?? 'website') . "\">";
        $metaTags[] = "<meta property=\"og:site_name\" content=\"{$this->config['site_name']}\">";
        $metaTags[] = "<meta property=\"og:locale\" content=\"es_GT\">";
        
        // Twitter Cards
        $metaTags[] = "<meta name=\"twitter:card\" content=\"summary_large_image\">";
        $metaTags[] = "<meta name=\"twitter:site\" content=\"{$this->config['twitter_handle']}\">";
        $metaTags[] = "<meta name=\"twitter:title\" content=\"$title\">";
        $metaTags[] = "<meta name=\"twitter:description\" content=\"$description\">";
        $metaTags[] = "<meta name=\"twitter:image\" content=\"$image\">";
        
        // Canonical URL
        $metaTags[] = "<link rel=\"canonical\" href=\"$url\">";
        
        // Hreflang para Guatemala
        $metaTags[] = "<link rel=\"alternate\" hreflang=\"es-gt\" href=\"$url\">";
        $metaTags[] = "<link rel=\"alternate\" hreflang=\"es\" href=\"$url\">";
        
        return implode("\n", $metaTags);
    }
    
    /**
     * Generar Schema.org JSON-LD
     */
    public function generateSchema(array $pageData = []): string
    {
        $schemas = [];
        
        // Schema de organización
        $schemas[] = $this->getOrganizationSchema();
        
        // Schema específico según el tipo de página
        switch ($pageData['type'] ?? 'website') {
            case 'product':
                $schemas[] = $this->getProductSchema($pageData);
                break;
            case 'article':
                $schemas[] = $this->getArticleSchema($pageData);
                break;
            case 'breadcrumb':
                $schemas[] = $this->getBreadcrumbSchema($pageData);
                break;
            case 'website':
            default:
                $schemas[] = $this->getWebsiteSchema($pageData);
                break;
        }
        
        $jsonLd = '<script type="application/ld+json">' . "\n";
        $jsonLd .= json_encode($schemas, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $jsonLd .= "\n" . '</script>';
        
        return $jsonLd;
    }
    
    /**
     * Generar sitemap XML
     */
    public function generateSitemap(): string
    {
        $urls = [];
        
        // Página principal
        $urls[] = [
            'loc' => $this->config['site_url'],
            'changefreq' => 'daily',
            'priority' => '1.0',
            'lastmod' => date('Y-m-d')
        ];
        
        // Páginas estáticas importantes
        $staticPages = [
            'productos' => ['changefreq' => 'daily', 'priority' => '0.9'],
            'ofertas' => ['changefreq' => 'daily', 'priority' => '0.8'],
            'contacto' => ['changefreq' => 'monthly', 'priority' => '0.7'],
            'sobre-nosotros' => ['changefreq' => 'monthly', 'priority' => '0.6'],
            'terminos' => ['changefreq' => 'yearly', 'priority' => '0.3'],
            'privacidad' => ['changefreq' => 'yearly', 'priority' => '0.3']
        ];
        
        foreach ($staticPages as $page => $config) {
            $urls[] = array_merge([
                'loc' => $this->config['site_url'] . $page,
                'lastmod' => date('Y-m-d')
            ], $config);
        }
        
        // Productos (simulado - en producción obtener de BD)
        $urls = array_merge($urls, $this->getProductUrls());
        
        // Categorías (simulado - en producción obtener de BD)
        $urls = array_merge($urls, $this->getCategoryUrls());
        
        return $this->buildSitemapXML($urls);
    }
    
    /**
     * Generar robots.txt
     */
    public function generateRobotsTxt(): string
    {
        $robotsTxt = [];
        
        $robotsTxt[] = "User-agent: *";
        $robotsTxt[] = "Allow: /";
        $robotsTxt[] = "";
        $robotsTxt[] = "# Disallow admin areas";
        $robotsTxt[] = "Disallow: /admin/";
        $robotsTxt[] = "Disallow: /api/";
        $robotsTxt[] = "Disallow: /cron/";
        $robotsTxt[] = "Disallow: /test/";
        $robotsTxt[] = "";
        $robotsTxt[] = "# Disallow sensitive files";
        $robotsTxt[] = "Disallow: /*.env";
        $robotsTxt[] = "Disallow: /writable/";
        $robotsTxt[] = "Disallow: /vendor/";
        $robotsTxt[] = "";
        $robotsTxt[] = "# Allow important resources";
        $robotsTxt[] = "Allow: /assets/";
        $robotsTxt[] = "Allow: /public/";
        $robotsTxt[] = "Allow: /sw.js";
        $robotsTxt[] = "Allow: /manifest.json";
        $robotsTxt[] = "";
        $robotsTxt[] = "# Sitemap";
        $robotsTxt[] = "Sitemap: {$this->config['site_url']}sitemap.xml";
        $robotsTxt[] = "";
        $robotsTxt[] = "# Crawl-delay for respectful crawling";
        $robotsTxt[] = "Crawl-delay: 1";
        
        return implode("\n", $robotsTxt);
    }
    
    /**
     * Optimizar imágenes para SEO
     */
    public function optimizeImageSEO(string $imagePath, array $imageData = []): array
    {
        $optimizations = [];
        
        // Generar alt text si no existe
        if (empty($imageData['alt'])) {
            $imageData['alt'] = $this->generateImageAlt($imagePath, $imageData);
            $optimizations[] = 'Generated alt text';
        }
        
        // Generar title si no existe
        if (empty($imageData['title'])) {
            $imageData['title'] = $imageData['alt'];
            $optimizations[] = 'Generated title attribute';
        }
        
        // Verificar formato de imagen
        $imageInfo = $this->analyzeImage($imagePath);
        if ($imageInfo['needs_optimization']) {
            $optimizations[] = 'Image format optimization recommended';
        }
        
        return [
            'alt' => $imageData['alt'],
            'title' => $imageData['title'],
            'optimizations' => $optimizations,
            'image_info' => $imageInfo
        ];
    }
    
    /**
     * Analizar SEO de una página
     */
    public function analyzePage(string $url, string $content): array
    {
        $analysis = [
            'score' => 0,
            'issues' => [],
            'recommendations' => [],
            'good_practices' => []
        ];
        
        // Analizar título
        $titleAnalysis = $this->analyzeTitle($content);
        $analysis = array_merge_recursive($analysis, $titleAnalysis);
        
        // Analizar meta description
        $descriptionAnalysis = $this->analyzeMetaDescription($content);
        $analysis = array_merge_recursive($analysis, $descriptionAnalysis);
        
        // Analizar headings
        $headingAnalysis = $this->analyzeHeadings($content);
        $analysis = array_merge_recursive($analysis, $headingAnalysis);
        
        // Analizar imágenes
        $imageAnalysis = $this->analyzeImages($content);
        $analysis = array_merge_recursive($analysis, $imageAnalysis);
        
        // Analizar enlaces
        $linkAnalysis = $this->analyzeLinks($content);
        $analysis = array_merge_recursive($analysis, $linkAnalysis);
        
        // Calcular score final
        $analysis['score'] = $this->calculateSEOScore($analysis);
        
        return $analysis;
    }
    
    /**
     * Generar título optimizado
     */
    private function generateTitle(array $pageData): string
    {
        if (!empty($pageData['title'])) {
            $title = $pageData['title'];
        } else {
            $title = $this->config['site_name'];
        }
        
        // Agregar site name si no está presente
        if (strpos($title, $this->config['site_name']) === false) {
            $title .= ' | ' . $this->config['site_name'];
        }
        
        // Limitar longitud (máximo 60 caracteres)
        if (strlen($title) > 60) {
            $title = substr($title, 0, 57) . '...';
        }
        
        return htmlspecialchars($title);
    }
    
    /**
     * Generar descripción optimizada
     */
    private function generateDescription(array $pageData): string
    {
        if (!empty($pageData['description'])) {
            $description = $pageData['description'];
        } else {
            $description = $this->config['site_description'];
        }
        
        // Limitar longitud (máximo 160 caracteres)
        if (strlen($description) > 160) {
            $description = substr($description, 0, 157) . '...';
        }
        
        return htmlspecialchars($description);
    }
    
    /**
     * Generar keywords optimizadas
     */
    private function generateKeywords(array $pageData): string
    {
        $keywords = [];
        
        if (!empty($pageData['keywords'])) {
            $keywords = array_merge($keywords, explode(',', $pageData['keywords']));
        }
        
        // Agregar keywords por defecto
        $defaultKeywords = explode(',', $this->config['site_keywords']);
        $keywords = array_merge($keywords, $defaultKeywords);
        
        // Limpiar y limitar
        $keywords = array_map('trim', $keywords);
        $keywords = array_unique($keywords);
        $keywords = array_slice($keywords, 0, 10); // Máximo 10 keywords
        
        return htmlspecialchars(implode(', ', $keywords));
    }
    
    /**
     * Schema de organización
     */
    private function getOrganizationSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => $this->config['organization_schema']['type'],
            'name' => $this->config['organization_schema']['name'],
            'url' => $this->config['site_url'],
            'logo' => $this->config['site_url'] . 'assets/images/logo.png',
            'description' => $this->config['site_description'],
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $this->config['organization_schema']['address']['streetAddress'],
                'addressLocality' => $this->config['organization_schema']['address']['addressLocality'],
                'addressCountry' => $this->config['organization_schema']['address']['addressCountry']
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => $this->config['organization_schema']['telephone'],
                'contactType' => 'customer service',
                'availableLanguage' => 'Spanish'
            ],
            'sameAs' => [
                $this->config['facebook_page'],
                'https://instagram.com/' . str_replace('@', '', $this->config['instagram_handle'])
            ]
        ];
    }
    
    /**
     * Schema de producto
     */
    private function getProductSchema(array $pageData): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $pageData['name'] ?? 'Producto',
            'description' => $pageData['description'] ?? '',
            'image' => $pageData['image'] ?? $this->config['default_image'],
            'brand' => [
                '@type' => 'Brand',
                'name' => $pageData['brand'] ?? 'MrCell'
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => $pageData['price'] ?? '0',
                'priceCurrency' => 'GTQ',
                'availability' => 'https://schema.org/InStock',
                'seller' => [
                    '@type' => 'Organization',
                    'name' => $this->config['site_name']
                ]
            ]
        ];
    }
    
    /**
     * Schema de sitio web
     */
    private function getWebsiteSchema(array $pageData): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->config['site_name'],
            'url' => $this->config['site_url'],
            'description' => $this->config['site_description'],
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => $this->config['site_url'] . 'buscar?q={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }
    
    /**
     * Obtener URLs de productos para sitemap
     */
    private function getProductUrls(): array
    {
        // En producción, obtener de la base de datos
        // Por ahora, URLs de ejemplo
        return [
            [
                'loc' => $this->config['site_url'] . 'producto/iphone-15-pro',
                'changefreq' => 'weekly',
                'priority' => '0.8',
                'lastmod' => date('Y-m-d')
            ],
            [
                'loc' => $this->config['site_url'] . 'producto/samsung-galaxy-s24',
                'changefreq' => 'weekly',
                'priority' => '0.8',
                'lastmod' => date('Y-m-d')
            ]
        ];
    }
    
    /**
     * Obtener URLs de categorías para sitemap
     */
    private function getCategoryUrls(): array
    {
        return [
            [
                'loc' => $this->config['site_url'] . 'categoria/smartphones',
                'changefreq' => 'daily',
                'priority' => '0.7',
                'lastmod' => date('Y-m-d')
            ],
            [
                'loc' => $this->config['site_url'] . 'categoria/accesorios',
                'changefreq' => 'weekly',
                'priority' => '0.6',
                'lastmod' => date('Y-m-d')
            ]
        ];
    }
    
    /**
     * Construir XML del sitemap
     */
    private function buildSitemapXML(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['loc']) . "</loc>\n";
            $xml .= "    <lastmod>" . $url['lastmod'] . "</lastmod>\n";
            $xml .= "    <changefreq>" . $url['changefreq'] . "</changefreq>\n";
            $xml .= "    <priority>" . $url['priority'] . "</priority>\n";
            $xml .= "  </url>\n";
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
    
    /**
     * Generar alt text para imagen
     */
    private function generateImageAlt(string $imagePath, array $imageData): string
    {
        $filename = basename($imagePath, '.' . pathinfo($imagePath, PATHINFO_EXTENSION));
        $alt = str_replace(['-', '_'], ' ', $filename);
        $alt = ucwords($alt);
        
        if (!empty($imageData['product_name'])) {
            $alt = $imageData['product_name'] . ' - ' . $alt;
        }
        
        return $alt . ' | ' . $this->config['site_name'];
    }
    
    /**
     * Analizar información de imagen
     */
    private function analyzeImage(string $imagePath): array
    {
        $info = [
            'exists' => file_exists($imagePath),
            'size' => 0,
            'format' => '',
            'dimensions' => [],
            'needs_optimization' => false
        ];
        
        if ($info['exists']) {
            $info['size'] = filesize($imagePath);
            $imageInfo = getimagesize($imagePath);
            
            if ($imageInfo) {
                $info['dimensions'] = ['width' => $imageInfo[0], 'height' => $imageInfo[1]];
                $info['format'] = $imageInfo['mime'];
                
                // Verificar si necesita optimización
                if ($info['size'] > 500000) { // > 500KB
                    $info['needs_optimization'] = true;
                }
            }
        }
        
        return $info;
    }
    
    /**
     * Analizar título de la página
     */
    private function analyzeTitle(string $content): array
    {
        $analysis = ['score' => 0, 'issues' => [], 'recommendations' => [], 'good_practices' => []];
        
        preg_match('/<title[^>]*>(.*?)<\/title>/i', $content, $matches);
        $title = $matches[1] ?? '';
        
        if (empty($title)) {
            $analysis['issues'][] = 'Falta el título de la página';
        } else {
            $titleLength = strlen($title);
            
            if ($titleLength < 30) {
                $analysis['issues'][] = 'Título muy corto (menos de 30 caracteres)';
            } elseif ($titleLength > 60) {
                $analysis['issues'][] = 'Título muy largo (más de 60 caracteres)';
            } else {
                $analysis['good_practices'][] = 'Título con longitud óptima';
                $analysis['score'] += 20;
            }
        }
        
        return $analysis;
    }
    
    /**
     * Analizar meta description
     */
    private function analyzeMetaDescription(string $content): array
    {
        $analysis = ['score' => 0, 'issues' => [], 'recommendations' => [], 'good_practices' => []];
        
        preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches);
        $description = $matches[1] ?? '';
        
        if (empty($description)) {
            $analysis['issues'][] = 'Falta la meta descripción';
        } else {
            $descLength = strlen($description);
            
            if ($descLength < 120) {
                $analysis['recommendations'][] = 'Meta descripción podría ser más descriptiva';
            } elseif ($descLength > 160) {
                $analysis['issues'][] = 'Meta descripción muy larga (más de 160 caracteres)';
            } else {
                $analysis['good_practices'][] = 'Meta descripción con longitud óptima';
                $analysis['score'] += 15;
            }
        }
        
        return $analysis;
    }
    
    /**
     * Analizar estructura de headings
     */
    private function analyzeHeadings(string $content): array
    {
        $analysis = ['score' => 0, 'issues' => [], 'recommendations' => [], 'good_practices' => []];
        
        // Verificar H1
        preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $content, $h1Matches);
        $h1Count = count($h1Matches[0]);
        
        if ($h1Count === 0) {
            $analysis['issues'][] = 'Falta el heading H1';
        } elseif ($h1Count > 1) {
            $analysis['issues'][] = 'Múltiples H1 encontrados (debería ser único)';
        } else {
            $analysis['good_practices'][] = 'H1 único presente';
            $analysis['score'] += 10;
        }
        
        // Verificar estructura jerárquica
        preg_match_all('/<h([1-6])[^>]*>/i', $content, $allHeadings);
        if (!empty($allHeadings[1])) {
            $headingLevels = array_map('intval', $allHeadings[1]);
            if ($this->hasProperHeadingHierarchy($headingLevels)) {
                $analysis['good_practices'][] = 'Estructura jerárquica de headings correcta';
                $analysis['score'] += 10;
            } else {
                $analysis['recommendations'][] = 'Mejorar jerarquía de headings (H1 > H2 > H3...)';
            }
        }
        
        return $analysis;
    }
    
    /**
     * Verificar jerarquía de headings
     */
    private function hasProperHeadingHierarchy(array $levels): bool
    {
        $previousLevel = 0;
        
        foreach ($levels as $level) {
            if ($level > $previousLevel + 1) {
                return false; // Salto de nivel muy grande
            }
            $previousLevel = $level;
        }
        
        return true;
    }
    
    /**
     * Analizar imágenes
     */
    private function analyzeImages(string $content): array
    {
        $analysis = ['score' => 0, 'issues' => [], 'recommendations' => [], 'good_practices' => []];
        
        preg_match_all('/<img[^>]*>/i', $content, $images);
        $imageCount = count($images[0]);
        
        if ($imageCount > 0) {
            $imagesWithAlt = 0;
            
            foreach ($images[0] as $img) {
                if (preg_match('/alt=["\'][^"\']*["\']/i', $img)) {
                    $imagesWithAlt++;
                }
            }
            
            $altPercentage = ($imagesWithAlt / $imageCount) * 100;
            
            if ($altPercentage === 100) {
                $analysis['good_practices'][] = 'Todas las imágenes tienen texto alternativo';
                $analysis['score'] += 15;
            } elseif ($altPercentage >= 80) {
                $analysis['recommendations'][] = 'Algunas imágenes sin texto alternativo';
                $analysis['score'] += 10;
            } else {
                $analysis['issues'][] = 'Muchas imágenes sin texto alternativo';
            }
        }
        
        return $analysis;
    }
    
    /**
     * Analizar enlaces
     */
    private function analyzeLinks(string $content): array
    {
        $analysis = ['score' => 0, 'issues' => [], 'recommendations' => [], 'good_practices' => []];
        
        preg_match_all('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)<\/a>/i', $content, $links);
        $linkCount = count($links[0]);
        
        if ($linkCount > 0) {
            $internalLinks = 0;
            $externalLinks = 0;
            
            foreach ($links[1] as $href) {
                if (strpos($href, $this->config['site_url']) === 0 || strpos($href, '/') === 0) {
                    $internalLinks++;
                } else {
                    $externalLinks++;
                }
            }
            
            if ($internalLinks > 0) {
                $analysis['good_practices'][] = 'Enlaces internos presentes';
                $analysis['score'] += 5;
            }
            
            if ($externalLinks > 0) {
                $analysis['recommendations'][] = 'Verificar que enlaces externos tengan rel="noopener"';
            }
        }
        
        return $analysis;
    }
    
    /**
     * Calcular score SEO final
     */
    private function calculateSEOScore(array $analysis): int
    {
        $maxScore = 100;
        $currentScore = $analysis['score'];
        
        // Penalizar por issues críticos
        $criticalIssues = count($analysis['issues']);
        $currentScore -= ($criticalIssues * 10);
        
        // Bonificar por buenas prácticas
        $goodPractices = count($analysis['good_practices']);
        $currentScore += ($goodPractices * 2);
        
        return max(0, min($maxScore, $currentScore));
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
