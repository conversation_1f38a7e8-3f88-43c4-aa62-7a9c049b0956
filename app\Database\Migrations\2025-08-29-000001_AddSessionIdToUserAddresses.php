<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddSessionIdToUserAddresses extends Migration
{
    public function up()
    {
        // Agregar columna session_id a la tabla user_addresses
        $this->forge->addColumn('user_addresses', [
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => true,
                'after' => 'user_id',
                'comment' => 'ID de sesión para usuarios no registrados',
            ],
        ]);

        // Agregar índice para session_id
        $this->forge->addKey('session_id');
    }

    public function down()
    {
        // Eliminar la columna session_id
        $this->forge->dropColumn('user_addresses', 'session_id');
    }
}
