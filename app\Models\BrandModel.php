<?php

namespace App\Models;

use CodeIgniter\Model;

class BrandModel extends Model
{
    protected $table            = 'brands';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'uuid',
        'name',
        'slug',
        'description',
        'logo',
        'website',
        'email',
        'phone',
        'address',
        'is_active',
        'sort_order',
        'meta_title',
        'meta_description'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'name' => 'required|min_length[2]|max_length[100]',
        'slug' => 'permit_empty|min_length[2]|max_length[120]|is_unique[brands.slug,id,{id}]',
        'email' => 'permit_empty|valid_email',
        'website' => 'permit_empty|valid_url',
        'phone' => 'permit_empty|min_length[8]|max_length[20]',
        'is_active' => 'required|in_list[0,1]',
        'sort_order' => 'permit_empty|integer'
    ];

    protected $validationMessages   = [
        'name' => [
            'required' => 'El nombre de la marca es obligatorio',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 100 caracteres'
        ],
        'slug' => [
            'required' => 'El slug es obligatorio',
            'is_unique' => 'Ya existe una marca con este slug'
        ],
        'email' => [
            'valid_email' => 'Debe proporcionar un email válido'
        ],
        'website' => [
            'valid_url' => 'Debe proporcionar una URL válida'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateSlugAndUuid'];
    protected $beforeUpdate   = ['generateSlug'];

    /**
     * Generate slug and UUID for new brands
     */
    protected function generateSlugAndUuid(array $data)
    {
        if (isset($data['data']['name'])) {
            if (empty($data['data']['slug'])) {
                $data['data']['slug'] = $this->generateUniqueSlug($data['data']['name']);
            }
            if (empty($data['data']['uuid'])) {
                // Generar UUID simple
                $data['data']['uuid'] = sprintf(
                    '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                    mt_rand(0, 0xffff),
                    mt_rand(0, 0x0fff) | 0x4000,
                    mt_rand(0, 0x3fff) | 0x8000,
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
                );
            }
        }
        return $data;
    }

    /**
     * Generate slug from name if not provided
     */
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = $this->generateUniqueSlug($data['data']['name'], $data['id'] ?? null);
        }
        return $data;
    }

    /**
     * Generate unique slug for brand
     */
    private function generateUniqueSlug($name, $excludeId = null)
    {
        $baseSlug = url_title($name, '-', true);
        $slug = $baseSlug;
        $counter = 1;

        // Verificar si el slug ya existe
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists in database
     */
    private function slugExists($slug, $excludeId = null)
    {
        $builder = $this->builder();
        $builder->where('slug', $slug);

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Get active brands for dropdowns
     */
    public function getActiveBrands()
    {
        return $this->where('is_active', 1)
                    ->orderBy('sort_order', 'ASC')
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get brand with product count
     */
    public function getBrandsWithProductCount()
    {
        $db = \Config\Database::connect();
        
        return $db->query("
            SELECT b.*, 
                   COUNT(p.id) as products_count
            FROM brands b
            LEFT JOIN products p ON b.id = p.brand_id AND p.deleted_at IS NULL AND p.is_active = 1
            WHERE b.deleted_at IS NULL
            GROUP BY b.id
            ORDER BY b.sort_order ASC, b.name ASC
        ")->getResultArray();
    }

    /**
     * Check if brand can be deleted (no products associated)
     */
    public function canDelete($brandId)
    {
        $db = \Config\Database::connect();
        
        $result = $db->query("
            SELECT COUNT(*) as count 
            FROM products 
            WHERE brand_id = ? AND deleted_at IS NULL
        ", [$brandId])->getRow();
        
        return $result->count == 0;
    }
}
