<?php
/**
 * Script de Cron para Alertas del Sistema MrCell
 * Optimizado para cPanel y estructura /public
 * 
 * Uso desde cPanel:
 * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-alerts.php
 */

// Configurar zona horaria
date_default_timezone_set('America/Guatemala');

// Configurar límites para hosting compartido
ini_set('max_execution_time', 300); // 5 minutos máximo
ini_set('memory_limit', '128M');

// Detectar rutas del proyecto
$projectRoot = dirname(__DIR__); // Subir un nivel desde /public
$logFile = $projectRoot . '/writable/logs/cron-alerts.log';
$logDir = dirname($logFile);

if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry; // También mostrar en consola
}

logMessage("🚀 Iniciando cron de alertas del sistema MrCell");
logMessage("📁 Directorio actual: " . __DIR__);
logMessage("📁 Raíz del proyecto: $projectRoot");

try {
    // Verificar que estamos en la estructura correcta
    if (!file_exists($projectRoot . '/app/Config/App.php')) {
        throw new Exception('No se encontró la aplicación CodeIgniter en: ' . $projectRoot);
    }

    // Cargar CodeIgniter
    $autoloadPath = $projectRoot . '/vendor/autoload.php';
    if (!file_exists($autoloadPath)) {
        throw new Exception('Autoloader no encontrado en: ' . $autoloadPath);
    }
    
    require_once $autoloadPath;

    // Configurar rutas de CodeIgniter
    $paths = new Config\Paths();
    $paths->systemDirectory = $projectRoot . '/system';
    $paths->appDirectory = $projectRoot . '/app';
    $paths->writableDirectory = $projectRoot . '/writable';
    $paths->testsDirectory = $projectRoot . '/tests';
    $paths->viewDirectory = $projectRoot . '/app/Views';

    // Inicializar CodeIgniter
    $bootstrap = \CodeIgniter\Boot::bootWeb($paths);
    $app = $bootstrap->getApp();

    logMessage("✅ CodeIgniter inicializado correctamente");

    // Verificar conexión a la base de datos
    $db = \Config\Database::connect();
    $testQuery = $db->query("SELECT 1 as test")->getRowArray();
    
    if (!$testQuery) {
        throw new Exception('No se pudo conectar a la base de datos');
    }

    logMessage("✅ Conexión a la base de datos establecida");

    // Crear tabla de cron_executions si no existe
    $db->query("
        CREATE TABLE IF NOT EXISTS cron_executions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_name VARCHAR(100) NOT NULL,
            status ENUM('success', 'error') DEFAULT 'success',
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_task_name (task_name),
            INDEX idx_created_at (created_at)
        )
    ");

    // Verificar si debe ejecutarse (cada 12 horas)
    $lastExecution = $db->query("
        SELECT MAX(created_at) as last_run 
        FROM cron_executions 
        WHERE task_name = 'scheduled_alerts'
    ")->getRowArray();

    $shouldRun = true;
    if ($lastExecution && $lastExecution['last_run']) {
        $lastRun = new DateTime($lastExecution['last_run']);
        $now = new DateTime();
        $diff = $now->diff($lastRun);
        
        // Solo ejecutar si han pasado más de 12 horas
        if ($diff->h < 12 && $diff->days == 0) {
            $shouldRun = false;
            logMessage("⏭️ Cron ya ejecutado en las últimas 12 horas, saltando ejecución");
        }
    }

    if (!$shouldRun) {
        logMessage("✅ Cron completado (sin ejecución necesaria)");
        exit(0);
    }

    logMessage("🔍 Iniciando verificación de alertas...");

    // Obtener número de grupo de WhatsApp
    $groupSetting = $db->query("
        SELECT setting_value FROM system_settings 
        WHERE setting_key = 'whatsapp_alerts_group' 
        LIMIT 1
    ")->getRowArray();

    $groupNumber = $groupSetting['setting_value'] ?? '120363416393766854';
    logMessage("📱 Número de grupo configurado: $groupNumber");

    $alerts = [];

    // 1. Verificar productos con bajo stock
    logMessage("📦 Verificando productos con bajo stock...");
    $lowStockProducts = $db->query("
        SELECT id, name, sku, stock_quantity, stock_min
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND stock_quantity >= 0
            AND is_active = 1 
            AND deleted_at IS NULL
        ORDER BY stock_quantity ASC
        LIMIT 15
    ")->getResultArray();

    if (!empty($lowStockProducts)) {
        $message = "🔴 *PRODUCTOS CON BAJO STOCK*\n\n";
        foreach ($lowStockProducts as $product) {
            $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
            $message .= "  Stock: {$product['stock_quantity']} | Mínimo: {$product['stock_min']}\n\n";
        }
        
        $alerts[] = [
            'type' => 'low_stock',
            'message' => $message,
            'count' => count($lowStockProducts)
        ];
        
        logMessage("⚠️ Encontrados " . count($lowStockProducts) . " productos con bajo stock");
    }

    // 2. Verificar productos próximos a caducar
    logMessage("⏰ Verificando productos próximos a caducar...");
    $expiringProducts = $db->query("
        SELECT id, name, sku, expiration_date, stock_quantity,
               DATEDIFF(expiration_date, CURDATE()) as days_until_expiration
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND (
                expiration_date <= CURDATE() 
                OR DATEDIFF(expiration_date, CURDATE()) <= COALESCE(expiration_alert_days, 7)
            )
        ORDER BY expiration_date ASC
        LIMIT 15
    ")->getResultArray();

    if (!empty($expiringProducts)) {
        $expired = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] < 0);
        $expiresToday = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] == 0);
        $expiresSoon = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] > 0);

        $message = "⏰ *PRODUCTOS POR CADUCAR*\n\n";

        if (!empty($expired)) {
            $message .= "🔴 *CADUCADOS:*\n";
            foreach ($expired as $product) {
                $message .= "• *{$product['name']}* - " . date('d/m/Y', strtotime($product['expiration_date'])) . "\n";
            }
            $message .= "\n";
        }

        if (!empty($expiresToday)) {
            $message .= "🟡 *CADUCAN HOY:*\n";
            foreach ($expiresToday as $product) {
                $message .= "• *{$product['name']}*\n";
            }
            $message .= "\n";
        }

        if (!empty($expiresSoon)) {
            $message .= "🟠 *PRÓXIMOS A CADUCAR:*\n";
            foreach ($expiresSoon as $product) {
                $message .= "• *{$product['name']}* - {$product['days_until_expiration']} días\n";
            }
        }

        $alerts[] = [
            'type' => 'expiration',
            'message' => $message,
            'count' => count($expiringProducts)
        ];

        logMessage("⚠️ Encontrados " . count($expiringProducts) . " productos próximos a caducar");
    }

    // 3. Verificar pedidos pendientes
    logMessage("📋 Verificando pedidos pendientes...");
    $pendingOrders = $db->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'shipped' AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 END) as shipped_count
        FROM orders
    ")->getRowArray();

    $pendingCount = $pendingOrders['pending_count'] ?? 0;
    $shippedCount = $pendingOrders['shipped_count'] ?? 0;

    if ($pendingCount > 0 || $shippedCount > 0) {
        $message = "📦 *PEDIDOS PENDIENTES*\n\n";
        
        if ($pendingCount > 0) {
            $message .= "🔴 *Pendientes (>24h):* $pendingCount\n";
        }
        
        if ($shippedCount > 0) {
            $message .= "🚚 *Enviados (>3 días):* $shippedCount\n";
        }

        $alerts[] = [
            'type' => 'orders',
            'message' => $message,
            'pending_count' => $pendingCount,
            'shipped_count' => $shippedCount
        ];

        logMessage("⚠️ Encontrados $pendingCount pedidos pendientes y $shippedCount enviados");
    }

    // Enviar notificaciones si hay alertas
    if (!empty($alerts)) {
        logMessage("📤 Preparando envío de " . count($alerts) . " alertas...");

        // Crear mensaje consolidado
        $fullMessage = "🚨 *ALERTAS DEL SISTEMA MRCELL*\n";
        $fullMessage .= "📅 " . date('d/m/Y H:i:s') . "\n\n";

        foreach ($alerts as $alert) {
            $fullMessage .= $alert['message'] . "\n";
            $fullMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
        }

        $fullMessage .= "💻 *Panel de Administración:*\n";
        $fullMessage .= "https://mrcell.com.gt/admin/dashboard\n\n";
        $fullMessage .= "🤖 _Mensaje automático del sistema_";

        // Enviar usando el servicio de WhatsApp
        try {
            $whatsappService = new \App\Services\WhatsAppService();
            $result = $whatsappService->sendMessage($groupNumber, $fullMessage);
            
            if ($result['success']) {
                logMessage("✅ Alertas enviadas exitosamente al grupo: $groupNumber");
            } else {
                logMessage("❌ Error enviando alertas: " . ($result['error'] ?? 'Error desconocido'));
            }
        } catch (Exception $e) {
            logMessage("❌ Excepción enviando alertas: " . $e->getMessage());
        }
    } else {
        logMessage("✅ No se encontraron alertas que enviar");
    }

    // Registrar ejecución del cron
    $db->table('cron_executions')->insert([
        'task_name' => 'scheduled_alerts',
        'status' => 'success',
        'message' => 'Alertas procesadas: ' . count($alerts),
        'created_at' => date('Y-m-d H:i:s')
    ]);

    logMessage("✅ Cron de alertas completado exitosamente");

} catch (Exception $e) {
    $errorMsg = "❌ Error en cron de alertas: " . $e->getMessage();
    logMessage($errorMsg);
    logMessage("📍 Archivo: " . $e->getFile() . ":" . $e->getLine());
    
    // Registrar error en la base de datos si es posible
    try {
        if (isset($db)) {
            $db->table('cron_executions')->insert([
                'task_name' => 'scheduled_alerts',
                'status' => 'error',
                'message' => $e->getMessage(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    } catch (Exception $dbError) {
        logMessage("❌ No se pudo registrar error en BD: " . $dbError->getMessage());
    }
    
    exit(1);
}

logMessage("🏁 Script de cron finalizado");
?>
