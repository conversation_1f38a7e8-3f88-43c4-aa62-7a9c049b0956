<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\AutomationManager;

/**
 * Comando para ejecutar automatizaciones
 * Permite ejecutar tareas automáticas desde la línea de comandos
 */
class AutomationCommand extends BaseCommand
{
    protected $group = 'MrCell';
    protected $name = 'automation:run';
    protected $description = 'Ejecutar automatizaciones del sistema';
    protected $usage = 'automation:run [task] [options]';
    protected $arguments = [
        'task' => 'Tarea específica a ejecutar (opcional)'
    ];
    protected $options = [
        '--all' => 'Ejecutar todas las tareas programadas',
        '--prices' => 'Solo monitor de precios',
        '--stock' => 'Solo alertas de stock',
        '--wishlist' => 'Solo recordatorios de wishlist',
        '--cleanup' => 'Solo limpieza de datos',
        '--backup' => 'Solo backup',
        '--performance' => 'Solo análisis de rendimiento',
        '--analytics' => 'Solo sincronización de analytics',
        '--cache' => 'Solo mantenimiento de cache',
        '--stats' => 'Mostrar estadísticas de automatización',
        '--verbose' => 'Mostrar información detallada',
        '--dry-run' => 'Simular ejecución sin realizar cambios'
    ];
    
    private $automationManager;
    
    public function run(array $params)
    {
        $this->automationManager = new AutomationManager();
        
        CLI::write('🤖 MrCell Automation System', 'green');
        CLI::write('================================', 'green');
        CLI::newLine();
        
        // Verificar si las automatizaciones están habilitadas
        if (!env('AUTOMATION_ENABLED', true)) {
            CLI::error('❌ Las automatizaciones están deshabilitadas en .env');
            return;
        }
        
        $task = $params[0] ?? null;
        $verbose = CLI::getOption('verbose');
        $dryRun = CLI::getOption('dry-run');
        
        if ($dryRun) {
            CLI::write('🧪 MODO DRY-RUN - No se realizarán cambios reales', 'yellow');
            CLI::newLine();
        }
        
        try {
            // Mostrar estadísticas
            if (CLI::getOption('stats')) {
                $this->showStats();
                return;
            }
            
            // Ejecutar todas las tareas
            if (CLI::getOption('all') || $task === 'all') {
                $this->runAllTasks($verbose, $dryRun);
                return;
            }
            
            // Ejecutar tareas específicas
            if (CLI::getOption('prices') || $task === 'prices') {
                $this->runPriceMonitoring($verbose, $dryRun);
            }
            
            if (CLI::getOption('stock') || $task === 'stock') {
                $this->runStockAlerts($verbose, $dryRun);
            }
            
            if (CLI::getOption('wishlist') || $task === 'wishlist') {
                $this->runWishlistReminders($verbose, $dryRun);
            }
            
            if (CLI::getOption('cleanup') || $task === 'cleanup') {
                $this->runDataCleanup($verbose, $dryRun);
            }
            
            if (CLI::getOption('backup') || $task === 'backup') {
                $this->runBackup($verbose, $dryRun);
            }
            
            if (CLI::getOption('performance') || $task === 'performance') {
                $this->runPerformanceAnalysis($verbose, $dryRun);
            }
            
            if (CLI::getOption('analytics') || $task === 'analytics') {
                $this->runAnalyticsSync($verbose, $dryRun);
            }
            
            if (CLI::getOption('cache') || $task === 'cache') {
                $this->runCacheMaintenance($verbose, $dryRun);
            }
            
            // Si no se especificó ninguna tarea, mostrar ayuda
            if (!$task && !CLI::getOption('all') && !CLI::getOption('stats') &&
                !CLI::getOption('prices') && !CLI::getOption('stock') &&
                !CLI::getOption('wishlist') && !CLI::getOption('cleanup') &&
                !CLI::getOption('backup') && !CLI::getOption('performance') &&
                !CLI::getOption('analytics') && !CLI::getOption('cache')) {
                $this->showHelp();
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ Error en automatización: ' . $e->getMessage());
            if ($verbose) {
                CLI::error('Stack trace: ' . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Ejecutar todas las tareas programadas
     */
    private function runAllTasks(bool $verbose, bool $dryRun): void
    {
        CLI::write('🚀 Ejecutando todas las automatizaciones...', 'cyan');
        CLI::newLine();
        
        if ($dryRun) {
            CLI::write('🧪 Simulando ejecución de todas las tareas...', 'yellow');
            CLI::newLine();
            return;
        }
        
        $startTime = microtime(true);
        $results = $this->automationManager->runScheduledTasks();
        $duration = round(microtime(true) - $startTime, 2);
        
        CLI::write("⏱️  Tiempo total: {$duration}s", 'blue');
        CLI::write("✅ Tareas exitosas: {$results['total_success']}", 'green');
        CLI::write("❌ Tareas fallidas: {$results['total_failed']}", 'red');
        CLI::newLine();
        
        if ($verbose) {
            foreach ($results['tasks'] as $taskName => $taskResult) {
                $status = $taskResult['success'] ? '✅' : '❌';
                $duration = $taskResult['duration_ms'] ?? 0;
                CLI::write("{$status} {$taskName}: {$duration}ms");
                
                if (!$taskResult['success'] && isset($taskResult['error'])) {
                    CLI::write("   Error: {$taskResult['error']}", 'red');
                }
                
                if (isset($taskResult['skipped']) && $taskResult['skipped']) {
                    CLI::write("   ⏭️  Omitida: {$taskResult['reason']}", 'yellow');
                }
            }
        }
    }
    
    /**
     * Ejecutar monitor de precios
     */
    private function runPriceMonitoring(bool $verbose, bool $dryRun): void
    {
        CLI::write('💰 Ejecutando monitor de precios...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando monitor de precios...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runPriceMonitoring();
        
        if ($result['success']) {
            CLI::write("✅ Monitor de precios completado", 'green');
            CLI::write("   📦 Productos procesados: {$result['products_processed']}");
            CLI::write("   📱 Notificaciones enviadas: {$result['notifications_sent']}");
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            
            if (!empty($result['errors']) && $verbose) {
                CLI::write("   ⚠️  Errores:", 'yellow');
                foreach ($result['errors'] as $error) {
                    CLI::write("      - {$error}", 'red');
                }
            }
        } else {
            CLI::error("❌ Error en monitor de precios: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar alertas de stock
     */
    private function runStockAlerts(bool $verbose, bool $dryRun): void
    {
        CLI::write('📦 Ejecutando alertas de stock...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando alertas de stock...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runStockAlerts();
        
        if ($result['success']) {
            CLI::write("✅ Alertas de stock completadas", 'green');
            CLI::write("   📦 Productos procesados: {$result['products_processed']}");
            CLI::write("   📱 Notificaciones enviadas: {$result['notifications_sent']}");
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            
            if (!empty($result['errors']) && $verbose) {
                CLI::write("   ⚠️  Errores:", 'yellow');
                foreach ($result['errors'] as $error) {
                    CLI::write("      - {$error}", 'red');
                }
            }
        } else {
            CLI::error("❌ Error en alertas de stock: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar recordatorios de wishlist
     */
    private function runWishlistReminders(bool $verbose, bool $dryRun): void
    {
        CLI::write('❤️  Ejecutando recordatorios de wishlist...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando recordatorios de wishlist...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runWishlistReminders();
        
        if ($result['success']) {
            if (isset($result['skipped']) && $result['skipped']) {
                CLI::write("⏭️  Recordatorios omitidos: {$result['reason']}", 'yellow');
            } else {
                CLI::write("✅ Recordatorios de wishlist completados", 'green');
                CLI::write("   👥 Usuarios procesados: {$result['users_processed']}");
                CLI::write("   📱 Recordatorios enviados: {$result['reminders_sent']}");
                CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
                
                if (!empty($result['errors']) && $verbose) {
                    CLI::write("   ⚠️  Errores:", 'yellow');
                    foreach ($result['errors'] as $error) {
                        CLI::write("      - {$error}", 'red');
                    }
                }
            }
        } else {
            CLI::error("❌ Error en recordatorios: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar limpieza de datos
     */
    private function runDataCleanup(bool $verbose, bool $dryRun): void
    {
        CLI::write('🧹 Ejecutando limpieza de datos...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando limpieza de datos...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runDataCleanup();
        
        if ($result['success']) {
            CLI::write("✅ Limpieza de datos completada", 'green');
            CLI::write("   🗑️  Total registros limpiados: {$result['total_records_cleaned']}");
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            
            if ($verbose) {
                foreach ($result['cleaned'] as $type => $count) {
                    CLI::write("      - {$type}: {$count} registros");
                }
            }
        } else {
            CLI::error("❌ Error en limpieza: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar backup
     */
    private function runBackup(bool $verbose, bool $dryRun): void
    {
        CLI::write('💾 Ejecutando backup...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando backup...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runBackup();
        
        if ($result['success']) {
            if (isset($result['skipped']) && $result['skipped']) {
                CLI::write("⏭️  Backup omitido: {$result['reason']}", 'yellow');
            } else {
                CLI::write("✅ Backup completado", 'green');
                CLI::write("   📁 Archivo: {$result['backup_file']}");
                CLI::write("   📊 Tamaño original: " . $this->formatBytes($result['original_size']));
                CLI::write("   📦 Tamaño comprimido: " . $this->formatBytes($result['compressed_size']));
                CLI::write("   🗜️  Compresión: {$result['compression_ratio']}%");
                CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            }
        } else {
            CLI::error("❌ Error en backup: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar análisis de rendimiento
     */
    private function runPerformanceAnalysis(bool $verbose, bool $dryRun): void
    {
        CLI::write('📊 Ejecutando análisis de rendimiento...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando análisis de rendimiento...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runPerformanceAnalysis();
        
        if ($result['success']) {
            CLI::write("✅ Análisis de rendimiento completado", 'green');
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            
            if (!empty($result['alerts'])) {
                CLI::write("   🚨 Alertas generadas:", 'yellow');
                foreach ($result['alerts'] as $alert) {
                    CLI::write("      - {$alert}", 'red');
                }
            } else {
                CLI::write("   ✅ Sin alertas de rendimiento", 'green');
            }
            
            if ($verbose && isset($result['analysis'])) {
                CLI::write("   📈 Análisis detallado:");
                foreach ($result['analysis'] as $category => $data) {
                    CLI::write("      {$category}:");
                    foreach ($data as $key => $value) {
                        if (!is_array($value)) {
                            CLI::write("         {$key}: {$value}");
                        }
                    }
                }
            }
        } else {
            CLI::error("❌ Error en análisis: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar sincronización de analytics
     */
    private function runAnalyticsSync(bool $verbose, bool $dryRun): void
    {
        CLI::write('📈 Ejecutando sincronización de analytics...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando sincronización de analytics...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runAnalyticsSync();
        
        if ($result['success']) {
            CLI::write("✅ Sincronización de analytics completada", 'green');
            CLI::write("   📊 Eventos procesados: {$result['events_processed']}");
            CLI::write("   ✅ Eventos sincronizados: {$result['events_synced']}");
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
            
            if (!empty($result['errors']) && $verbose) {
                CLI::write("   ⚠️  Errores:", 'yellow');
                foreach ($result['errors'] as $error) {
                    CLI::write("      - {$error}", 'red');
                }
            }
        } else {
            CLI::error("❌ Error en sincronización: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Ejecutar mantenimiento de cache
     */
    private function runCacheMaintenance(bool $verbose, bool $dryRun): void
    {
        CLI::write('🗄️  Ejecutando mantenimiento de cache...', 'cyan');
        
        if ($dryRun) {
            CLI::write('🧪 Simulando mantenimiento de cache...', 'yellow');
            return;
        }
        
        $result = $this->automationManager->runCacheMaintenance();
        
        if ($result['success']) {
            CLI::write("✅ Mantenimiento de cache completado", 'green');
            CLI::write("   🗑️  Elementos limpiados: {$result['maintenance']['cleared_expired']}");
            CLI::write("   ⏱️  Duración: {$result['duration_ms']}ms");
        } else {
            CLI::error("❌ Error en mantenimiento: {$result['error']}");
        }
        
        CLI::newLine();
    }
    
    /**
     * Mostrar estadísticas de automatización
     */
    private function showStats(): void
    {
        CLI::write('📊 Estadísticas de Automatización (últimos 7 días)', 'cyan');
        CLI::write('================================================', 'cyan');
        CLI::newLine();
        
        $stats = $this->automationManager->getAutomationStats(7);
        
        if (isset($stats['error'])) {
            CLI::error("❌ Error obteniendo estadísticas: {$stats['error']}");
            return;
        }
        
        CLI::write("📈 Total de ejecuciones: {$stats['total_executions']}", 'green');
        CLI::write("✅ Ejecuciones exitosas: {$stats['total_successful']}", 'green');
        CLI::write("❌ Ejecuciones fallidas: " . ($stats['total_executions'] - $stats['total_successful']), 'red');
        CLI::newLine();
        
        if (!empty($stats['tasks'])) {
            CLI::write('📋 Detalle por tarea:', 'blue');
            foreach ($stats['tasks'] as $task) {
                $successRate = round(($task['successful_executions'] / $task['executions']) * 100, 1);
                $avgDuration = round($task['avg_duration'], 2);
                
                CLI::write("   {$task['task_name']}:");
                CLI::write("      Ejecuciones: {$task['executions']}");
                CLI::write("      Tasa de éxito: {$successRate}%");
                CLI::write("      Duración promedio: {$avgDuration}s");
                CLI::newLine();
            }
        }
    }
    
    /**
     * Mostrar ayuda del comando
     */
    public function showHelp(): void
    {
        CLI::write('🤖 Sistema de Automatización MrCell', 'green');
        CLI::write('===================================', 'green');
        CLI::newLine();
        
        CLI::write('Uso:', 'yellow');
        CLI::write('  php spark automation:run [opciones]');
        CLI::newLine();
        
        CLI::write('Opciones disponibles:', 'yellow');
        CLI::write('  --all           Ejecutar todas las tareas programadas');
        CLI::write('  --prices        Solo monitor de precios');
        CLI::write('  --stock         Solo alertas de stock');
        CLI::write('  --wishlist      Solo recordatorios de wishlist');
        CLI::write('  --cleanup       Solo limpieza de datos');
        CLI::write('  --backup        Solo backup de base de datos');
        CLI::write('  --performance   Solo análisis de rendimiento');
        CLI::write('  --analytics     Solo sincronización de analytics');
        CLI::write('  --cache         Solo mantenimiento de cache');
        CLI::write('  --stats         Mostrar estadísticas');
        CLI::write('  --verbose       Mostrar información detallada');
        CLI::write('  --dry-run       Simular sin realizar cambios');
        CLI::newLine();
        
        CLI::write('Ejemplos:', 'yellow');
        CLI::write('  php spark automation:run --all --verbose');
        CLI::write('  php spark automation:run --prices --dry-run');
        CLI::write('  php spark automation:run --stats');
        CLI::newLine();
        
        CLI::write('Para configurar cron jobs:', 'blue');
        CLI::write('  # Ejecutar cada hora');
        CLI::write('  0 * * * * php /path/to/project/spark automation:run --all');
        CLI::write('  # Solo precios cada 30 minutos');
        CLI::write('  */30 * * * * php /path/to/project/spark automation:run --prices');
    }
    
    /**
     * Formatear bytes a formato legible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
