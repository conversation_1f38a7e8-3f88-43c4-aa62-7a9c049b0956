<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="text-center mb-5">
                <h1 class="display-4 text-primary">
                    <i class="fas fa-tags me-3"></i>Cupones Disponibles
                </h1>
                <p class="lead text-muted">Aprovecha nuestros descuentos especiales</p>
            </div>

            <!-- Cupones Disponibles -->
            <?php if (!empty($available_coupons)): ?>
                <div class="row">
                    <?php foreach ($available_coupons as $coupon): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 shadow-sm border-0 coupon-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h5 class="card-title text-primary mb-0">
                                            <?= esc($coupon['name']) ?>
                                        </h5>
                                        <span class="badge bg-<?= $coupon['type'] === 'percentage' ? 'success' : ($coupon['type'] === 'fixed' ? 'info' : 'warning') ?> fs-6">
                                            <?php if ($coupon['type'] === 'percentage'): ?>
                                                <?= $coupon['value'] ?>% OFF
                                            <?php elseif ($coupon['type'] === 'fixed'): ?>
                                                Q<?= number_format($coupon['value'], 2) ?> OFF
                                            <?php else: ?>
                                                <?= strtoupper(str_replace('_', ' ', $coupon['type'])) ?>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    
                                    <p class="card-text text-muted mb-3">
                                        <?= esc($coupon['description']) ?>
                                    </p>
                                    
                                    <div class="coupon-code-container mb-3">
                                        <div class="input-group">
                                            <input type="text" class="form-control bg-light" 
                                                   value="<?= esc($coupon['code']) ?>" 
                                                   id="coupon-<?= $coupon['id'] ?>" readonly>
                                            <button class="btn btn-outline-primary" type="button" 
                                                    onclick="copyCouponCode('<?= $coupon['id'] ?>')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="coupon-details">
                                        <?php if ($coupon['min_order_amount']): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                Compra mínima: Q<?= number_format($coupon['min_order_amount'], 2) ?>
                                            </small>
                                        <?php endif; ?>
                                        
                                        <?php if ($coupon['valid_until']): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-calendar me-1"></i>
                                                Válido hasta: <?= date('d/m/Y', strtotime($coupon['valid_until'])) ?>
                                            </small>
                                        <?php endif; ?>
                                        
                                        <?php if ($coupon['usage_limit_per_user']): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-user me-1"></i>
                                                Límite por usuario: <?= $coupon['usage_limit_per_user'] ?> vez(es)
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent border-0">
                                    <button class="btn btn-primary w-100" onclick="applyCoupon('<?= esc($coupon['code']) ?>')">
                                        <i class="fas fa-check me-2"></i>Usar Cupón
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-tags fa-4x text-muted"></i>
                    </div>
                    <h3 class="text-muted">No hay cupones disponibles</h3>
                    <p class="text-muted">Vuelve pronto para ver nuestras ofertas especiales</p>
                    <a href="<?= base_url('/') ?>" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Volver al Inicio
                    </a>
                </div>
            <?php endif; ?>

            <!-- Historial de Cupones del Usuario -->
            <?php if (!empty($user_coupons)): ?>
                <div class="mt-5">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-history me-2"></i>Mis Cupones Utilizados
                    </h3>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Cupón</th>
                                    <th>Descuento</th>
                                    <th>Fecha de Uso</th>
                                    <th>Ahorro</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($user_coupons as $used): ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($used['name']) ?></strong><br>
                                            <small class="text-muted"><?= esc($used['code']) ?></small>
                                        </td>
                                        <td>
                                            <?php if ($used['type'] === 'percentage'): ?>
                                                <span class="badge bg-success"><?= $used['value'] ?>% OFF</span>
                                            <?php elseif ($used['type'] === 'fixed'): ?>
                                                <span class="badge bg-info">Q<?= number_format($used['value'], 2) ?> OFF</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><?= strtoupper(str_replace('_', ' ', $used['type'])) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('d/m/Y H:i', strtotime($used['used_at'])) ?></td>
                                        <td class="text-success">
                                            <strong>Q<?= number_format($used['discount_amount'], 2) ?></strong>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Newsletter para Cupones -->
            <div class="mt-5">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="card-title">
                            <i class="fas fa-envelope me-2"></i>¿Quieres más cupones?
                        </h4>
                        <p class="card-text">Suscríbete a nuestro newsletter y recibe cupones exclusivos</p>
                        
                        <form id="newsletterForm" class="row g-2 justify-content-center">
                            <div class="col-md-6">
                                <input type="email" class="form-control" placeholder="Tu email" required>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-light w-100">
                                    <i class="fas fa-paper-plane me-1"></i>Suscribirse
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Copiar código de cupón
function copyCouponCode(couponId) {
    const input = document.getElementById('coupon-' + couponId);
    input.select();
    input.setSelectionRange(0, 99999); // Para móviles
    
    navigator.clipboard.writeText(input.value).then(function() {
        // Mostrar notificación de éxito
        showNotification('Código copiado: ' + input.value, 'success');
    }).catch(function() {
        // Fallback para navegadores antiguos
        document.execCommand('copy');
        showNotification('Código copiado: ' + input.value, 'success');
    });
}

// Aplicar cupón (redirigir al carrito)
function applyCoupon(code) {
    // Guardar el código en localStorage para aplicarlo en el carrito
    localStorage.setItem('pendingCoupon', code);
    
    // Redirigir al carrito
    window.location.href = '<?= base_url('cart') ?>?coupon=' + encodeURIComponent(code);
}

// Mostrar notificaciones
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remover después de 3 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Suscripción al newsletter
document.getElementById('newsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = this.querySelector('input[type="email"]').value;
    
    // Aquí harías la llamada AJAX para suscribir al newsletter
    fetch('<?= base_url('coupons/subscribe-newsletter') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?= csrf_token() ?>'
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('¡Suscripción exitosa! Recibirás cupones exclusivos.', 'success');
            this.reset();
        } else {
            showNotification(data.message || 'Error en la suscripción', 'danger');
        }
    })
    .catch(error => {
        showNotification('Error de conexión', 'danger');
    });
});

// Animaciones para las tarjetas de cupones
document.addEventListener('DOMContentLoaded', function() {
    const couponCards = document.querySelectorAll('.coupon-card');
    
    couponCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<style>
.coupon-card {
    transition: all 0.3s ease;
    border-left: 4px solid var(--bs-primary);
}

.coupon-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.coupon-code-container {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 10px;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.coupon-details small {
    margin-bottom: 2px;
}

.table th {
    border-top: none;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .coupon-card {
        margin-bottom: 1rem;
    }
}
</style>
<?= $this->endSection() ?>
