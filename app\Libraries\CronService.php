<?php

namespace App\Libraries;

use CodeIgniter\Database\BaseConnection;

class CronService
{
    protected $db;
    protected $whatsappService;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->whatsappService = new \App\Services\WhatsAppService();
    }

    /**
     * Ejecutar todas las tareas programadas
     */
    public function runScheduledTasks()
    {
        log_message('info', 'Iniciando tareas programadas del cron');

        try {
            // Verificar si ya se ejecutó en las últimas 12 horas
            if (!$this->shouldRunCron()) {
                log_message('info', 'Cron ya ejecutado en las últimas 12 horas, saltando');
                return;
            }

            $alerts = [];

            // 1. Verificar productos con bajo stock
            $lowStockAlerts = $this->checkLowStock();
            if (!empty($lowStockAlerts)) {
                $alerts[] = $lowStockAlerts;
            }

            // 2. Verificar productos sin ventas por más de 1 mes
            $noSalesAlerts = $this->checkProductsWithoutSales();
            if (!empty($noSalesAlerts)) {
                $alerts[] = $noSalesAlerts;
            }

            // 3. Verificar productos próximos a caducar
            $expirationAlerts = $this->checkExpiringProducts();
            if (!empty($expirationAlerts)) {
                $alerts[] = $expirationAlerts;
            }

            // 4. Verificar pedidos pendientes y enviados
            $orderAlerts = $this->checkPendingOrders();
            if (!empty($orderAlerts)) {
                $alerts[] = $orderAlerts;
            }

            // 5. Verificar reseñas pendientes
            $reviewAlerts = $this->checkPendingReviews();
            if (!empty($reviewAlerts)) {
                $alerts[] = $reviewAlerts;
            }

            // Enviar notificaciones si hay alertas
            if (!empty($alerts)) {
                $this->sendNotifications($alerts);
            }

            // Registrar ejecución del cron
            $this->recordCronExecution();

            log_message('info', 'Tareas programadas completadas exitosamente');

        } catch (\Exception $e) {
            log_message('error', 'Error en tareas programadas: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Verificar si debe ejecutarse el cron (cada 12 horas)
     */
    private function shouldRunCron(): bool
    {
        $lastExecution = $this->db->query("
            SELECT MAX(created_at) as last_run 
            FROM cron_executions 
            WHERE task_name = 'scheduled_alerts'
        ")->getRowArray();

        if (!$lastExecution || !$lastExecution['last_run']) {
            return true;
        }

        $lastRun = new \DateTime($lastExecution['last_run']);
        $now = new \DateTime();
        $diff = $now->diff($lastRun);

        // Ejecutar si han pasado más de 12 horas
        return ($diff->h >= 12 || $diff->days > 0);
    }

    /**
     * Verificar productos con bajo stock
     */
    private function checkLowStock(): array
    {
        $lowStockProducts = $this->db->query("
            SELECT id, name, sku, stock_quantity, stock_min
            FROM products 
            WHERE stock_quantity <= stock_min 
                AND stock_quantity > 0
                AND is_active = 1 
                AND deleted_at IS NULL
            ORDER BY stock_quantity ASC
            LIMIT 20
        ")->getResultArray();

        if (empty($lowStockProducts)) {
            return [];
        }

        $message = "🔴 *ALERTA: PRODUCTOS CON BAJO STOCK*\n\n";
        foreach ($lowStockProducts as $product) {
            $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
            $message .= "  Stock actual: {$product['stock_quantity']} | Mínimo: {$product['stock_min']}\n\n";
        }

        return [
            'type' => 'low_stock',
            'title' => 'Productos con Bajo Stock',
            'message' => $message,
            'count' => count($lowStockProducts)
        ];
    }

    /**
     * Verificar productos sin ventas por más de 1 mes
     */
    private function checkProductsWithoutSales(): array
    {
        $productsWithoutSales = $this->db->query("
            SELECT p.id, p.name, p.sku, p.created_at,
                   COALESCE(MAX(oi.created_at), p.created_at) as last_sale
            FROM products p
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.status != 'cancelled'
            WHERE p.is_active = 1 
                AND p.deleted_at IS NULL
                AND p.created_at <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            GROUP BY p.id
            HAVING last_sale <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            ORDER BY last_sale ASC
            LIMIT 15
        ")->getResultArray();

        if (empty($productsWithoutSales)) {
            return [];
        }

        $message = "📊 *PRODUCTOS SIN VENTAS (>1 MES)*\n\n";
        foreach ($productsWithoutSales as $product) {
            $lastSaleDate = date('d/m/Y', strtotime($product['last_sale']));
            $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
            $message .= "  Última venta: {$lastSaleDate}\n\n";
        }

        return [
            'type' => 'no_sales',
            'title' => 'Productos sin Ventas',
            'message' => $message,
            'count' => count($productsWithoutSales)
        ];
    }

    /**
     * Verificar productos próximos a caducar
     */
    private function checkExpiringProducts(): array
    {
        helper('expiration');
        
        $expiringProducts = $this->db->query("
            SELECT id, name, sku, expiration_date, stock_quantity,
                   DATEDIFF(expiration_date, CURDATE()) as days_until_expiration,
                   CASE 
                       WHEN DATEDIFF(expiration_date, CURDATE()) < 0 THEN 'expired'
                       WHEN DATEDIFF(expiration_date, CURDATE()) = 0 THEN 'expires_today'
                       WHEN DATEDIFF(expiration_date, CURDATE()) <= expiration_alert_days THEN 'expires_soon'
                       ELSE 'ok'
                   END as expiration_status
            FROM products 
            WHERE has_expiration = 1 
                AND is_active = 1 
                AND deleted_at IS NULL
                AND expiration_date IS NOT NULL
                AND (
                    expiration_date <= CURDATE() 
                    OR DATEDIFF(expiration_date, CURDATE()) <= expiration_alert_days
                )
            ORDER BY expiration_date ASC
        ")->getResultArray();

        if (empty($expiringProducts)) {
            return [];
        }

        $expired = array_filter($expiringProducts, fn($p) => $p['expiration_status'] === 'expired');
        $expiresToday = array_filter($expiringProducts, fn($p) => $p['expiration_status'] === 'expires_today');
        $expiresSoon = array_filter($expiringProducts, fn($p) => $p['expiration_status'] === 'expires_soon');

        $message = "⏰ *ALERTA: PRODUCTOS POR CADUCAR*\n\n";

        if (!empty($expired)) {
            $message .= "🔴 *PRODUCTOS CADUCADOS:*\n";
            foreach ($expired as $product) {
                $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
                $message .= "  Caducó: " . date('d/m/Y', strtotime($product['expiration_date'])) . "\n";
                $message .= "  Stock: {$product['stock_quantity']}\n\n";
            }
        }

        if (!empty($expiresToday)) {
            $message .= "🟡 *CADUCAN HOY:*\n";
            foreach ($expiresToday as $product) {
                $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
                $message .= "  Stock: {$product['stock_quantity']}\n\n";
            }
        }

        if (!empty($expiresSoon)) {
            $message .= "🟠 *PRÓXIMOS A CADUCAR:*\n";
            foreach ($expiresSoon as $product) {
                $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
                $message .= "  Caduca: " . date('d/m/Y', strtotime($product['expiration_date'])) . "\n";
                $message .= "  Días restantes: {$product['days_until_expiration']}\n";
                $message .= "  Stock: {$product['stock_quantity']}\n\n";
            }
        }

        return [
            'type' => 'expiration',
            'title' => 'Productos por Caducar',
            'message' => $message,
            'count' => count($expiringProducts),
            'expired' => count($expired),
            'expires_today' => count($expiresToday),
            'expires_soon' => count($expiresSoon)
        ];
    }

    /**
     * Verificar pedidos pendientes y enviados
     */
    private function checkPendingOrders(): array
    {
        $pendingOrders = $this->db->query("
            SELECT COUNT(*) as pending_count
            FROM orders 
            WHERE status = 'pending' 
                AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")->getRowArray();

        $shippedOrders = $this->db->query("
            SELECT COUNT(*) as shipped_count
            FROM orders 
            WHERE status = 'shipped' 
                AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY)
        ")->getRowArray();

        $pendingCount = $pendingOrders['pending_count'] ?? 0;
        $shippedCount = $shippedOrders['shipped_count'] ?? 0;

        if ($pendingCount == 0 && $shippedCount == 0) {
            return [];
        }

        $message = "📦 *ALERTA: PEDIDOS PENDIENTES*\n\n";

        if ($pendingCount > 0) {
            $message .= "🔴 *PEDIDOS PENDIENTES (>24h):* {$pendingCount}\n";
            $message .= "Pedidos que llevan más de 24 horas sin procesar\n\n";
        }

        if ($shippedCount > 0) {
            $message .= "🚚 *PEDIDOS ENVIADOS (>3 días):* {$shippedCount}\n";
            $message .= "Pedidos enviados hace más de 3 días sin entregar\n\n";
        }

        return [
            'type' => 'orders',
            'title' => 'Pedidos Pendientes',
            'message' => $message,
            'pending_count' => $pendingCount,
            'shipped_count' => $shippedCount
        ];
    }

    /**
     * Verificar reseñas pendientes por revisar
     */
    private function checkPendingReviews(): array
    {
        // Verificar si existe la tabla de reseñas
        $tableExists = $this->db->query("
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
                AND table_name = 'product_reviews'
        ")->getRowArray();

        if (!$tableExists || $tableExists['count'] == 0) {
            return [];
        }

        // Verificar si la columna status existe
        $columns = $this->db->query("SHOW COLUMNS FROM product_reviews")->getResultArray();
        $hasStatusColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'status') {
                $hasStatusColumn = true;
                break;
            }
        }

        if ($hasStatusColumn) {
            $pendingReviews = $this->db->query("
                SELECT COUNT(*) as pending_count
                FROM product_reviews
                WHERE status = 'pending'
                    AND created_at <= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            ")->getRowArray();
        } else {
            // Si no hay columna status, contar todas las reseñas recientes
            $pendingReviews = $this->db->query("
                SELECT COUNT(*) as pending_count
                FROM product_reviews
                WHERE created_at <= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            ")->getRowArray();
        }

        $pendingCount = $pendingReviews['pending_count'] ?? 0;

        if ($pendingCount == 0) {
            return [];
        }

        $message = "⭐ *RESEÑAS PENDIENTES DE REVISIÓN*\n\n";
        $message .= "📝 *Reseñas pendientes:* {$pendingCount}\n";
        $message .= "Reseñas que llevan más de 48 horas sin revisar\n\n";

        return [
            'type' => 'reviews',
            'title' => 'Reseñas Pendientes',
            'message' => $message,
            'count' => $pendingCount
        ];
    }

    /**
     * Enviar notificaciones usando el sistema WhatsApp existente
     */
    private function sendNotifications(array $alerts): void
    {
        try {
            // Obtener número de grupo configurado
            $groupNumber = $this->getWhatsAppGroupNumber();

            if (empty($groupNumber)) {
                log_message('warning', 'No hay número de grupo de WhatsApp configurado para alertas');
                return;
            }

            // Crear mensaje consolidado
            $fullMessage = "🚨 *ALERTAS DEL SISTEMA MRCELL*\n";
            $fullMessage .= "📅 " . date('d/m/Y H:i:s') . "\n\n";

            foreach ($alerts as $alert) {
                $fullMessage .= $alert['message'] . "\n";
                $fullMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
            }

            $fullMessage .= "💻 *Panel de Administración:*\n";
            $fullMessage .= base_url('admin/dashboard') . "\n\n";
            $fullMessage .= "🤖 _Mensaje automático del sistema_";

            // Enviar al grupo usando el servicio existente
            try {
                $result = $this->whatsappService->sendMessage($groupNumber, $fullMessage);

                if ($result['success']) {
                    log_message('info', 'Alerta enviada exitosamente al grupo: ' . $groupNumber);
                } else {
                    log_message('error', 'Error enviando alerta al grupo ' . $groupNumber . ': ' . ($result['error'] ?? 'Error desconocido'));
                }
            } catch (\Exception $e) {
                log_message('error', 'Excepción enviando alerta al grupo ' . $groupNumber . ': ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            log_message('error', 'Error enviando notificaciones: ' . $e->getMessage());
        }
    }

    /**
     * Obtener número de grupo de WhatsApp configurado
     */
    private function getWhatsAppGroupNumber(): ?string
    {
        $setting = $this->db->query("
            SELECT value
            FROM system_settings
            WHERE setting_key = 'whatsapp_alerts_group'
            LIMIT 1
        ")->getRowArray();

        $groupNumber = $setting['value'] ?? '120363416393766854';

        // Validar que el número solo contenga dígitos
        if (!empty($groupNumber) && preg_match('/^[0-9]+$/', $groupNumber)) {
            return $groupNumber;
        }

        // Si no hay configuración válida, usar el número por defecto
        return '120363416393766854';
    }

    /**
     * Registrar ejecución del cron
     */
    private function recordCronExecution(): void
    {
        // Crear tabla si no existe
        $this->db->query("
            CREATE TABLE IF NOT EXISTS cron_executions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                task_name VARCHAR(100) NOT NULL,
                status ENUM('success', 'error') DEFAULT 'success',
                message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        $this->db->table('cron_executions')->insert([
            'task_name' => 'scheduled_alerts',
            'status' => 'success',
            'message' => 'Tareas programadas ejecutadas exitosamente',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
