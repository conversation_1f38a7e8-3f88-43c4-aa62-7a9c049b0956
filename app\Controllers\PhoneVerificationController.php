<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PhoneVerificationModel;
use App\Services\WhatsAppService;

class PhoneVerificationController extends BaseController
{
    protected $phoneVerificationModel;
    protected $whatsappService;

    public function __construct()
    {
        $this->phoneVerificationModel = new PhoneVerificationModel();
        $this->whatsappService = new WhatsAppService();
    }

    /**
     * Mostrar modal de verificación
     */
    public function showModal()
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no autenticado']);
        }

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();
        
        $user = $db->table('users')->where('id', $userId)->get()->getRowArray();
        
        if (!$user) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no encontrado']);
        }

        // Verificar si ya está verificado
        if ($user['phone_verified']) {
            return $this->response->setJSON([
                'success' => false, 
                'error' => 'El teléfono ya está verificado',
                'already_verified' => true
            ]);
        }

        // Verificar si hay una verificación pendiente
        $pendingVerification = $this->phoneVerificationModel->getPendingVerification($userId);

        $data = [
            'user' => $user,
            'pending_verification' => $pendingVerification,
            'can_request_new' => $this->phoneVerificationModel->canRequestNewCode($userId)
        ];

        return view('frontend/modals/phone_verification', $data);
    }

    /**
     * Enviar código de verificación
     */
    public function sendCode()
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no autenticado']);
        }

        $userId = $this->session->get('user_id');
        $phone = $this->request->getPost('phone');

        // Validaciones
        if (empty($phone)) {
            return $this->response->setJSON(['success' => false, 'error' => 'Número de teléfono requerido']);
        }

        if (!preg_match('/^\+502\d{8}$/', $phone)) {
            return $this->response->setJSON(['success' => false, 'error' => 'Formato de teléfono inválido. Use +502XXXXXXXX']);
        }

        // Verificar si puede solicitar un nuevo código
        if (!$this->phoneVerificationModel->canRequestNewCode($userId)) {
            return $this->response->setJSON(['success' => false, 'error' => 'Debe esperar 1 minuto antes de solicitar un nuevo código']);
        }

        try {
            // Generar código
            $result = $this->phoneVerificationModel->generateVerificationCode($userId, $phone);
            
            if (!$result['success']) {
                return $this->response->setJSON($result);
            }

            // Enviar código por WhatsApp
            $message = "🔐 *Código de Verificación MrCell*\n\n" .
                      "Tu código de verificación es: *{$result['code']}*\n\n" .
                      "⏰ Este código expira en 10 minutos.\n" .
                      "🔒 No compartas este código con nadie.\n\n" .
                      "Si no solicitaste este código, ignora este mensaje.";

            $whatsappResult = $this->whatsappService->sendMessage($phone, $message);

            if ($whatsappResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Código enviado exitosamente',
                    'expires_at' => $result['expires_at'],
                    'phone' => $phone
                ]);
            } else {
                // Si falla el envío, marcar la verificación como fallida
                $this->phoneVerificationModel->update($result['verification_id'], ['is_verified' => -1]);
                
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error enviando código por WhatsApp: ' . $whatsappResult['error']
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error enviando código de verificación: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'error' => 'Error interno del servidor']);
        }
    }

    /**
     * Verificar código
     */
    public function verifyCode()
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no autenticado']);
        }

        $userId = $this->session->get('user_id');
        $code = $this->request->getPost('code');

        // Validaciones
        if (empty($code)) {
            return $this->response->setJSON(['success' => false, 'error' => 'Código de verificación requerido']);
        }

        if (!preg_match('/^\d{6}$/', $code)) {
            return $this->response->setJSON(['success' => false, 'error' => 'El código debe tener 6 dígitos']);
        }

        try {
            $result = $this->phoneVerificationModel->verifyCode($userId, $code);
            return $this->response->setJSON($result);

        } catch (\Exception $e) {
            log_message('error', 'Error verificando código: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'error' => 'Error interno del servidor']);
        }
    }

    /**
     * Verificar estado de verificación del usuario
     */
    public function checkStatus()
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no autenticado']);
        }

        $userId = $this->session->get('user_id');
        $db = \Config\Database::connect();
        
        $user = $db->table('users')
                  ->select('phone, phone_verified, whatsapp_notifications_enabled')
                  ->where('id', $userId)
                  ->get()
                  ->getRowArray();

        if (!$user) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no encontrado']);
        }

        $pendingVerification = $this->phoneVerificationModel->getPendingVerification($userId);

        return $this->response->setJSON([
            'success' => true,
            'phone_verified' => (bool)$user['phone_verified'],
            'phone' => $user['phone'],
            'whatsapp_notifications_enabled' => (bool)$user['whatsapp_notifications_enabled'],
            'has_pending_verification' => !empty($pendingVerification),
            'can_request_new' => $this->phoneVerificationModel->canRequestNewCode($userId)
        ]);
    }

    /**
     * Alternar notificaciones de WhatsApp
     */
    public function toggleNotifications()
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'error' => 'Usuario no autenticado']);
        }

        $userId = $this->session->get('user_id');
        $enabled = $this->request->getPost('enabled') === 'true';

        try {
            $db = \Config\Database::connect();
            $result = $db->table('users')
                        ->where('id', $userId)
                        ->update(['whatsapp_notifications_enabled' => $enabled ? 1 : 0]);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => $enabled ? 'Notificaciones habilitadas' : 'Notificaciones deshabilitadas',
                    'enabled' => $enabled
                ]);
            } else {
                return $this->response->setJSON(['success' => false, 'error' => 'Error actualizando configuración']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error actualizando notificaciones: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'error' => 'Error interno del servidor']);
        }
    }
}
