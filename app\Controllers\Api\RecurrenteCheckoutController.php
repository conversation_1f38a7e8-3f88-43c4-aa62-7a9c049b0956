<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class RecurrenteCheckoutController extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $productModel;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->productModel = new \App\Models\ProductModel();
    }

    /**
     * Crear checkout en Recurrente
     */
    public function createCheckout()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'cart_items' => 'required|is_array',
                'cart_items.*.product_id' => 'required|integer',
                'cart_items.*.quantity' => 'permit_empty|integer|greater_than[0]',
                'metadata' => 'permit_empty|is_array'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $cartItems = $json['cart_items'];
            $metadata = $json['metadata'] ?? [];

            // Verificar que todos los productos tengan recurrente_product_id
            $recurrenteItems = [];
            $totalAmount = 0;
            $invalidProducts = [];

            foreach ($cartItems as $item) {
                $product = $this->productModel->find($item['product_id']);
                
                if (!$product) {
                    $invalidProducts[] = "Producto {$item['product_id']} no encontrado";
                    continue;
                }

                if (empty($product['recurrente_product_id'])) {
                    $invalidProducts[] = "Producto '{$product['name']}' no está sincronizado con Recurrente";
                    continue;
                }

                if ($product['recurrente_sync_status'] === 'disabled') {
                    $invalidProducts[] = "Producto '{$product['name']}' no es elegible para Recurrente (precio alto)";
                    continue;
                }

                if ($product['recurrente_sync_status'] !== 'synced') {
                    $invalidProducts[] = "Producto '{$product['name']}' no está correctamente sincronizado con Recurrente";
                    continue;
                }

                // Agregar item para Recurrente
                $quantity = $item['quantity'] ?? 1;
                for ($i = 0; $i < $quantity; $i++) {
                    $recurrenteItems[] = [
                        'product_id' => $product['recurrente_product_id']
                    ];
                }

                // Calcular total para validación
                $price = !empty($product['price_sale']) && $product['price_sale'] > 0
                    ? $product['price_sale']
                    : $product['price_regular'];
                $totalAmount += $price * $quantity;
            }

            if (!empty($invalidProducts)) {
                return $this->fail([
                    'message' => 'Algunos productos no son válidos para Recurrente',
                    'invalid_products' => $invalidProducts
                ], 400);
            }

            if (empty($recurrenteItems)) {
                return $this->fail('No hay productos válidos para el checkout', 400);
            }

            // Verificar límite de precio total
            if (!$this->isWithinRecurrentePriceLimit($totalAmount, 'GTQ')) {
                return $this->fail([
                    'message' => 'El total del carrito excede el límite para pagos con Recurrente',
                    'total_amount' => $totalAmount,
                    'limit_gtq' => 117000,
                    'limit_usd' => 15000
                ], 400);
            }

            // Obtener configuración de Recurrente
            $recurrenteConfig = $this->getRecurrenteConfig();
            if (!$recurrenteConfig) {
                return $this->fail('Recurrente no está configurado correctamente', 500);
            }

            // Preparar datos para Recurrente
            $checkoutData = [
                'items' => $recurrenteItems,
                'metadata' => array_merge($metadata, [
                    'platform' => 'MrCell_CI4',
                    'total_items' => count($cartItems),
                    'total_products' => count($recurrenteItems),
                    'created_at' => date('Y-m-d H:i:s')
                ])
            ];

            // Crear checkout en Recurrente
            $response = $this->createRecurrenteCheckout($checkoutData, $recurrenteConfig);

            if (isset($response['id'])) {
                // Guardar información del checkout en la base de datos local
                $this->saveCheckoutRecord($response, $cartItems, $totalAmount);

                return $this->respond([
                    'status' => 'success',
                    'message' => 'Checkout creado exitosamente',
                    'data' => [
                        'checkout_id' => $response['id'],
                        'checkout_url' => $response['checkout_url'] ?? null,
                        'total_items' => count($recurrenteItems),
                        'expires_at' => $response['expires_at'] ?? null
                    ]
                ]);
            } else {
                return $this->fail('Error creando checkout en Recurrente: ' . json_encode($response), 500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en RecurrenteCheckoutController::createCheckout: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener estado de un checkout
     */
    public function getCheckoutStatus($checkoutId)
    {
        try {
            // Obtener configuración de Recurrente
            $recurrenteConfig = $this->getRecurrenteConfig();
            if (!$recurrenteConfig) {
                return $this->fail('Recurrente no está configurado correctamente', 500);
            }

            // Consultar estado en Recurrente
            $response = $this->getRecurrenteCheckoutStatus($checkoutId, $recurrenteConfig);

            return $this->respond([
                'status' => 'success',
                'data' => $response
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en RecurrenteCheckoutController::getCheckoutStatus: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Verificar si el precio está dentro del límite de Recurrente
     */
    private function isWithinRecurrentePriceLimit($amount, $currency = 'GTQ')
    {
        $maxPriceUSD = 15000; // Límite de Recurrente
        $amountInUSD = $amount;
        
        // Convertir a USD si está en GTQ (aproximadamente 1 USD = 7.8 GTQ)
        if ($currency === 'GTQ') {
            $amountInUSD = $amount / 7.8;
        }
        
        return $amountInUSD <= $maxPriceUSD;
    }

    /**
     * Obtener configuración de Recurrente
     */
    private function getRecurrenteConfig()
    {
        try {
            $stmt = $this->db->prepare("
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('recurrente_public_key', 'recurrente_secret_key', 'recurrente_enabled')
                AND is_active = 1
            ");
            $stmt->execute();
            
            $config = [];
            while ($row = $stmt->fetch()) {
                $config[$row['setting_key']] = $row['setting_value'];
            }

            if (empty($config['recurrente_public_key']) || empty($config['recurrente_secret_key'])) {
                return null;
            }

            if ($config['recurrente_enabled'] !== '1') {
                return null;
            }

            return $config;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo configuración de Recurrente: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Crear checkout en Recurrente via API
     */
    private function createRecurrenteCheckout($checkoutData, $config)
    {
        $url = 'https://app.recurrente.com/api/checkouts';
        
        $headers = [
            'X-PUBLIC-KEY: ' . $config['recurrente_public_key'],
            'X-SECRET-KEY: ' . $config['recurrente_secret_key'],
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($checkoutData),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('Error de conexión con Recurrente: ' . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = $decodedResponse['message'] ?? $decodedResponse['error'] ?? 'Error desconocido';
            throw new \Exception("Error HTTP {$httpCode} de Recurrente: {$errorMessage}");
        }
        
        return $decodedResponse;
    }

    /**
     * Obtener estado de checkout desde Recurrente
     */
    private function getRecurrenteCheckoutStatus($checkoutId, $config)
    {
        $url = "https://app.recurrente.com/api/checkouts/{$checkoutId}";
        
        $headers = [
            'X-PUBLIC-KEY: ' . $config['recurrente_public_key'],
            'X-SECRET-KEY: ' . $config['recurrente_secret_key'],
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('Error de conexión con Recurrente: ' . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = $decodedResponse['message'] ?? $decodedResponse['error'] ?? 'Error desconocido';
            throw new \Exception("Error HTTP {$httpCode} de Recurrente: {$errorMessage}");
        }
        
        return $decodedResponse;
    }

    /**
     * Guardar registro del checkout en la base de datos local
     */
    private function saveCheckoutRecord($recurrenteResponse, $cartItems, $totalAmount)
    {
        try {
            $checkoutData = [
                'recurrente_checkout_id' => $recurrenteResponse['id'],
                'cart_items' => json_encode($cartItems),
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'checkout_url' => $recurrenteResponse['checkout_url'] ?? null,
                'expires_at' => $recurrenteResponse['expires_at'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Aquí podrías guardar en una tabla de checkouts si existe
            // Por ahora solo loggeamos
            log_message('info', 'Checkout creado: ' . json_encode($checkoutData));

        } catch (\Exception $e) {
            log_message('error', 'Error guardando registro de checkout: ' . $e->getMessage());
        }
    }
}
