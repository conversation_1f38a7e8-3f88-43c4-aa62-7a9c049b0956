<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class FixRecurrenteUrls extends BaseCommand
{
    protected $group       = 'Setup';
    protected $name        = 'fix:recurrente-urls';
    protected $description = 'Corregir URLs de Recurrente';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== CORRIGIENDO URLS DE RECURRENTE ===', 'yellow');
            CLI::newLine();

            // Corregir tipo del método de pago
            $db->query('UPDATE payment_methods SET type = "gateway" WHERE slug = "recurrente"');
            CLI::write('✅ Tipo de método de pago corregido', 'green');

            // Corregir URLs
            $baseUrl = 'http://localhost:8081';
            
            $urls = [
                'recurrente_cancel_url' => $baseUrl . '/checkout/cancel',
                'recurrente_error_url' => $baseUrl . '/checkout/error', 
                'recurrente_success_url' => $baseUrl . '/checkout/success',
                'recurrente_webhook_url' => $baseUrl . '/webhooks/recurrente'
            ];

            foreach ($urls as $key => $url) {
                $db->query('UPDATE system_settings SET setting_value = ? WHERE setting_key = ?', [$url, $key]);
                CLI::write("✅ {$key} actualizada: {$url}", 'green');
            }

            CLI::newLine();
            CLI::write('=== CORRECCIÓN COMPLETADA ===', 'green');
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
