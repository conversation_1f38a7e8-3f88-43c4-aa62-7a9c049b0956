<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;

class WishlistController extends BaseController
{
    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Toggle product in wishlist
     */
    public function toggle()
    {
        try {
            // Check if user is logged in
            $userId = session()->get('user_id');
            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Debes iniciar sesión'
                ]);
            }

            $input = $this->request->getJSON(true);
            $productId = $input['product_id'] ?? null;

            if (!$productId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de producto requerido'
                ]);
            }

            // Check if product exists
            $product = $this->db->table('products')
                ->where('id', $productId)
                ->where('is_active', 1)
                ->get()
                ->getRowArray();

            if (!$product) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Producto no encontrado'
                ]);
            }

            // Check if product is already in wishlist
            $existing = $this->db->table('wishlist')
                ->where('user_id', $userId)
                ->where('product_id', $productId)
                ->get()
                ->getRowArray();

            if ($existing) {
                // Remove from wishlist
                $this->db->table('wishlist')
                    ->where('user_id', $userId)
                    ->where('product_id', $productId)
                    ->delete();

                return $this->response->setJSON([
                    'success' => true,
                    'action' => 'removed',
                    'message' => 'Producto eliminado de la lista de deseos'
                ]);
            } else {
                // Add to wishlist
                $this->db->table('wishlist')->insert([
                    'user_id' => $userId,
                    'product_id' => $productId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'action' => 'added',
                    'message' => 'Producto agregado a la lista de deseos'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistController::toggle: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Get user's wishlist
     */
    public function index()
    {
        try {
            $userId = session()->get('user_id');
            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Debes iniciar sesión'
                ]);
            }

            $wishlist = $this->db->query("
                SELECT w.*, p.name, p.slug, p.price_regular, p.price_sale, 
                       p.featured_image, c.name as category_name
                FROM wishlist w
                INNER JOIN products p ON w.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE w.user_id = ? AND p.is_active = 1
                ORDER BY w.created_at DESC
            ", [$userId])->getResultArray();

            return $this->response->setJSON([
                'success' => true,
                'data' => $wishlist
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistController::index: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Check if product is in wishlist
     */
    public function check($productId)
    {
        try {
            $userId = session()->get('user_id');
            if (!$userId) {
                return $this->response->setJSON([
                    'success' => true,
                    'in_wishlist' => false
                ]);
            }

            $exists = $this->db->table('wishlist')
                ->where('user_id', $userId)
                ->where('product_id', $productId)
                ->countAllResults() > 0;

            return $this->response->setJSON([
                'success' => true,
                'in_wishlist' => $exists
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistController::check: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Remove product from wishlist
     */
    public function remove($productId)
    {
        try {
            $userId = session()->get('user_id');
            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Debes iniciar sesión'
                ]);
            }

            $deleted = $this->db->table('wishlist')
                ->where('user_id', $userId)
                ->where('product_id', $productId)
                ->delete();

            if ($deleted) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Producto eliminado de la lista de deseos'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Producto no encontrado en la lista de deseos'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WishlistController::remove: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }
}
