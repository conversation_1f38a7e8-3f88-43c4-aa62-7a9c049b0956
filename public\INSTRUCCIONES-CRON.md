# 🚀 INSTRUCCIONES DIRECTAS PARA CONFIGURAR EL CRON

## **PASO 1: CONFIGURAR LA BASE DE DATOS**

Visita: **https://mrcell.com.gt/setup-db.php**

Esto creará las tablas necesarias y configurará el número de grupo.

## **PASO 2: PROBAR EL CRON**

Visita: **https://mrcell.com.gt/cron-simple.php**

Esto ejecutará el cron manualmente y te mostrará:
- Qué alertas encuentra
- Si envía el mensaje a WhatsApp
- Cualquier error que ocurra

## **PASO 3: CONFIGURAR EN CPANEL**

1. **Entra a tu cPanel**
2. **Ve a "Cron Jobs"**
3. **Agrega este comando:**

```
/opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-simple.php
```

4. **Programación (cada 12 horas):**
   - Minuto: `0`
   - Hora: `8,20`
   - <PERSON>ía: `*`
   - Mes: `*`
   - Día de la semana: `*`

## **QUÉ HACE EL CRON:**

✅ **Verifica productos con bajo stock**
✅ **Verifica productos próximos a caducar**
✅ **Verifica pedidos pendientes**
✅ **Envía alertas al grupo WhatsApp: 120363416393766854**
✅ **Solo se ejecuta cada 12 horas**
✅ **Registra todo en la base de datos**

## **CONFIGURACIÓN ACTUAL:**

- **Base de datos:** mayansourcecom_mrcell
- **Servidor:** 167.114.111.50
- **API WhatsApp:** http://167.114.111.52/api/sendMessage
- **Grupo WhatsApp:** 120363416393766854

## **VERIFICAR QUE FUNCIONA:**

1. **Ejecuta manualmente:** https://mrcell.com.gt/cron-simple.php
2. **Revisa la tabla:** `cron_executions` en tu base de datos
3. **Verifica mensajes** en el grupo de WhatsApp

---

**¡SIMPLE Y DIRECTO!** 🎯
