<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AddExpirationFieldsSeeder extends Seeder
{
    public function run()
    {
        try {
            // Verificar si los campos ya existen
            $fields = $this->db->getFieldNames('products');
            
            if (!in_array('has_expiration', $fields)) {
                $this->db->query("
                    ALTER TABLE products 
                    ADD COLUMN has_expiration TINYINT(1) DEFAULT 0 COMMENT 'Indica si el producto tiene fecha de caducidad'
                ");
                echo "✅ Campo 'has_expiration' agregado\n";
            } else {
                echo "⚠️ Campo 'has_expiration' ya existe\n";
            }
            
            if (!in_array('expiration_date', $fields)) {
                $this->db->query("
                    ALTER TABLE products 
                    ADD COLUMN expiration_date DATE NULL COMMENT 'Fecha de caducidad del producto'
                ");
                echo "✅ Campo 'expiration_date' agregado\n";
            } else {
                echo "⚠️ Campo 'expiration_date' ya existe\n";
            }
            
            if (!in_array('expiration_alert_days', $fields)) {
                $this->db->query("
                    ALTER TABLE products 
                    ADD COLUMN expiration_alert_days INT(11) DEFAULT 30 COMMENT 'Días antes de la caducidad para mostrar alerta'
                ");
                echo "✅ Campo 'expiration_alert_days' agregado\n";
            } else {
                echo "⚠️ Campo 'expiration_alert_days' ya existe\n";
            }
            
            // Agregar índice si no existe
            try {
                $this->db->query("
                    ALTER TABLE products 
                    ADD INDEX idx_product_expiration (has_expiration, expiration_date)
                ");
                echo "✅ Índice 'idx_product_expiration' agregado\n";
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    echo "⚠️ Índice 'idx_product_expiration' ya existe\n";
                } else {
                    echo "❌ Error agregando índice: " . $e->getMessage() . "\n";
                }
            }
            
            echo "\n✅ Campos de fecha de caducidad agregados exitosamente\n";
            
        } catch (\Exception $e) {
            echo "❌ Error agregando campos: " . $e->getMessage() . "\n";
        }
    }
}
