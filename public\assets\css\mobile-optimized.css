/**
 * CSS Optimizado para Móviles - MrCell Guatemala
 * Estilos específicos para dispositivos móviles, PWA y funcionalidades táctiles
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

/* ========================================
   VARIABLES CSS PARA MÓVILES
   ======================================== */
:root {
    --mobile-header-height: 60px;
    --mobile-bottom-nav-height: 60px;
    --mobile-touch-target: 44px;
    --mobile-padding: 16px;
    --mobile-border-radius: 12px;
    --mobile-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --mobile-transition: all 0.3s ease;
    
    /* Colores específicos para móvil */
    --mobile-primary: #007bff;
    --mobile-secondary: #6c757d;
    --mobile-success: #28a745;
    --mobile-warning: #ffc107;
    --mobile-danger: #dc3545;
    --mobile-background: #f8f9fa;
    --mobile-surface: #ffffff;
    --mobile-text: #212529;
    --mobile-text-muted: #6c757d;
}

/* ========================================
   ESTILOS BASE PARA MÓVILES
   ======================================== */
@media (max-width: 768px) {
    /* Optimizaciones generales */
    * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
    
    input, textarea, select {
        -webkit-user-select: text;
        user-select: text;
    }
    
    body {
        font-size: 16px; /* Evitar zoom en iOS */
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        touch-action: manipulation;
    }
    
    /* Contenedor principal */
    .container {
        padding-left: var(--mobile-padding);
        padding-right: var(--mobile-padding);
        max-width: 100%;
    }
    
    /* Botones optimizados para touch */
    .btn {
        min-height: var(--mobile-touch-target);
        padding: 12px 20px;
        font-size: 16px;
        border-radius: var(--mobile-border-radius);
        transition: var(--mobile-transition);
        position: relative;
        overflow: hidden;
    }
    
    .btn:active {
        transform: scale(0.98);
    }
    
    /* Efecto ripple para botones */
    .btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }
    
    .btn:active::after {
        width: 200px;
        height: 200px;
    }
    
    /* Formularios optimizados */
    .form-control {
        min-height: var(--mobile-touch-target);
        font-size: 16px; /* Evitar zoom en iOS */
        padding: 12px 16px;
        border-radius: var(--mobile-border-radius);
        border: 2px solid #e9ecef;
        transition: var(--mobile-transition);
    }
    
    .form-control:focus {
        border-color: var(--mobile-primary);
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        outline: none;
    }
}

/* ========================================
   PWA ESPECÍFICO
   ======================================== */
.pwa-mode {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
}

.pwa-mode .navbar {
    padding-top: env(safe-area-inset-top);
}

/* Banner de instalación PWA */
.pwa-install-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, var(--mobile-primary), #0056b3);
    color: white;
    padding: var(--mobile-padding);
    z-index: 9999;
    transform: translateY(100%);
    animation: slideUp 0.3s ease forwards;
    box-shadow: var(--mobile-shadow);
}

.pwa-install-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pwa-install-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.pwa-install-text {
    flex: 1;
}

.pwa-install-text strong {
    display: block;
    font-size: 16px;
    margin-bottom: 4px;
}

.pwa-install-text p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.pwa-install-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--mobile-transition);
}

.pwa-install-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.pwa-install-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 4px;
    margin-left: 8px;
}

/* ========================================
   NOTIFICACIONES PUSH
   ======================================== */
.push-permission-prompt {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--mobile-padding);
}

.push-prompt-content {
    background: var(--mobile-surface);
    border-radius: var(--mobile-border-radius);
    padding: 24px;
    max-width: 320px;
    width: 100%;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.push-prompt-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.push-prompt-text strong {
    display: block;
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--mobile-text);
}

.push-prompt-text p {
    margin: 0 0 24px 0;
    color: var(--mobile-text-muted);
    font-size: 14px;
    line-height: 1.4;
}

.push-prompt-allow,
.push-prompt-deny {
    padding: 12px 24px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 600;
    margin: 0 4px;
    cursor: pointer;
    transition: var(--mobile-transition);
}

.push-prompt-allow {
    background: var(--mobile-primary);
    color: white;
    border: none;
}

.push-prompt-allow:hover {
    background: #0056b3;
}

.push-prompt-deny {
    background: transparent;
    color: var(--mobile-text-muted);
    border: 1px solid #e9ecef;
}

.push-prompt-deny:hover {
    background: #f8f9fa;
}

/* ========================================
   PULL TO REFRESH
   ======================================== */
#pull-refresh-indicator {
    position: fixed;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--mobile-primary);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    z-index: 9999;
    transition: top 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: var(--mobile-shadow);
}

/* ========================================
   CONECTIVIDAD
   ======================================== */
.connectivity-status {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    z-index: 9999;
    animation: slideDown 0.3s ease;
    box-shadow: var(--mobile-shadow);
}

.connectivity-status.online {
    background: var(--mobile-success);
    color: white;
}

.connectivity-status.offline {
    background: var(--mobile-warning);
    color: #212529;
}

/* ========================================
   ACTUALIZACIÓN DE APP
   ======================================== */
.update-notification {
    position: fixed;
    top: 20px;
    left: var(--mobile-padding);
    right: var(--mobile-padding);
    background: var(--mobile-primary);
    color: white;
    border-radius: var(--mobile-border-radius);
    padding: 16px;
    z-index: 9999;
    animation: slideDown 0.3s ease;
    box-shadow: var(--mobile-shadow);
}

.update-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

#update-reload {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--mobile-transition);
}

#update-reload:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ========================================
   IMÁGENES OPTIMIZADAS
   ======================================== */
.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy.loaded {
    opacity: 1;
}

.responsive-image {
    width: 100%;
    height: auto;
    border-radius: var(--mobile-border-radius);
}

/* Placeholder para imágenes cargando */
.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--mobile-border-radius);
}

/* ========================================
   GRID DE PRODUCTOS MÓVIL
   ======================================== */
@media (max-width: 768px) {
    .product-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: var(--mobile-padding);
    }
    
    .product-card {
        background: var(--mobile-surface);
        border-radius: var(--mobile-border-radius);
        padding: 12px;
        box-shadow: var(--mobile-shadow);
        transition: var(--mobile-transition);
    }
    
    .product-card:active {
        transform: scale(0.98);
    }
    
    .product-image {
        width: 100%;
        aspect-ratio: 1;
        object-fit: cover;
        border-radius: 8px;
        margin-bottom: 8px;
    }
    
    .product-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .product-price {
        font-size: 16px;
        font-weight: 700;
        color: var(--mobile-primary);
    }
}

/* ========================================
   NAVEGACIÓN INFERIOR MÓVIL
   ======================================== */
@media (max-width: 768px) {
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: var(--mobile-bottom-nav-height);
        background: var(--mobile-surface);
        border-top: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-around;
        z-index: 1000;
        padding-bottom: env(safe-area-inset-bottom);
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px;
        color: var(--mobile-text-muted);
        text-decoration: none;
        font-size: 12px;
        transition: var(--mobile-transition);
        min-width: var(--mobile-touch-target);
        min-height: var(--mobile-touch-target);
    }
    
    .mobile-nav-item.active {
        color: var(--mobile-primary);
    }
    
    .mobile-nav-item:hover {
        color: var(--mobile-primary);
    }
    
    .mobile-nav-icon {
        font-size: 20px;
        margin-bottom: 2px;
    }
    
    /* Agregar padding bottom al body para la navegación */
    body {
        padding-bottom: var(--mobile-bottom-nav-height);
    }
}

/* ========================================
   ANIMACIONES
   ======================================== */
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
    }
    to {
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ========================================
   UTILIDADES MÓVILES
   ======================================== */
@media (max-width: 768px) {
    .mobile-only {
        display: block !important;
    }
    
    .desktop-only {
        display: none !important;
    }
    
    .mobile-text-center {
        text-align: center;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-no-padding {
        padding: 0 !important;
    }
    
    .mobile-padding {
        padding: var(--mobile-padding) !important;
    }
    
    .mobile-margin-bottom {
        margin-bottom: var(--mobile-padding) !important;
    }
    
    .mobile-sticky-top {
        position: sticky;
        top: 0;
        z-index: 100;
    }
}

/* ========================================
   MODO OSCURO PARA MÓVILES
   ======================================== */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    :root {
        --mobile-background: #121212;
        --mobile-surface: #1e1e1e;
        --mobile-text: #ffffff;
        --mobile-text-muted: #a0a0a0;
    }
    
    .pwa-install-banner {
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    }
    
    .push-prompt-content {
        background: var(--mobile-surface);
        color: var(--mobile-text);
    }
    
    .connectivity-status.offline {
        background: #ff9800;
        color: #000;
    }
}

/* ========================================
   OPTIMIZACIONES DE RENDIMIENTO
   ======================================== */
@media (max-width: 768px) {
    /* Reducir animaciones en dispositivos lentos */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* Optimizar scroll */
    .scroll-container {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
    
    /* Optimizar transformaciones */
    .transform-gpu {
        transform: translateZ(0);
        will-change: transform;
    }
}
