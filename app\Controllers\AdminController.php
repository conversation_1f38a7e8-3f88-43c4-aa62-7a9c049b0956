<?php

namespace App\Controllers;

/**
 * Controlador para la gestión de administradores
 */
class AdminController extends BaseController
{
    public function __construct()
    {
        // Helpers will be loaded in initController
    }

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        // Call parent initController
        parent::initController($request, $response, $logger);

        // Load helpers
        helper('security');
    }

    /**
     * Dashboard principal del admin
     */
    public function index()
    {
        // Check if admin is logged in
        if (!session()->get('admin_logged_in')) {
            return redirect()->to(base_url('admin/login'));
        }

        $data = [
            'title' => 'Dashboard Administrativo - MrCell',
            'admin_user' => session()->get('admin_user'),
            'stats' => $this->getDashboardStats()
        ];

        return view('admin/dashboard', $data);
    }

    /**
     * Página de login del admin
     */
    public function login()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('admin_logged_in')) {
            return redirect()->to(base_url('admin'));
        }

        $data = [
            'title' => 'Login Administrativo - MrCell'
        ];

        return view('admin/login', $data);
    }

    /**
     * Procesar autenticación del admin
     */
    public function authenticate()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Validación básica
        if (empty($email) || empty($password)) {
            return redirect()->to(base_url('admin/login'))
                           ->with('error', 'Email y contraseña son requeridos');
        }

        // Verificar credenciales (simplificado)
        if ($this->verifyAdminCredentials($email, $password)) {
            // Crear sesión de admin
            session()->set([
                'admin_logged_in' => true,
                'admin_user' => [
                    'id' => 1,
                    'nombre' => 'Super Administrador',
                    'email' => $email,
                    'rol_admin' => 'super_admin'
                ],
                'admin_login_time' => time()
            ]);

            log_message('info', "Admin login exitoso: {$email}");
            
            return redirect()->to(base_url('admin'))
                           ->with('success', 'Bienvenido al panel administrativo');
        } else {
            log_message('warning', "Admin login fallido: {$email}");
            
            return redirect()->to(base_url('admin/login'))
                           ->with('error', 'Credenciales inválidas');
        }
    }

    /**
     * Logout del admin
     */
    public function logout()
    {
        $adminEmail = session()->get('admin_user')['email'] ?? 'unknown';
        
        session()->destroy();
        
        log_message('info', "Admin logout: {$adminEmail}");
        
        return redirect()->to(base_url('admin/login'))
                       ->with('success', 'Has cerrado sesión exitosamente');
    }

    /**
     * Perfil del administrador
     */
    public function profile()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to(base_url('admin/login'));
        }

        $data = [
            'title' => 'Mi Perfil - Admin MrCell',
            'admin_user' => session()->get('admin_user')
        ];

        return view('admin/profile', $data);
    }

    /**
     * Actualizar perfil del administrador
     */
    public function updateProfile()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to(base_url('admin/login'));
        }

        $nombre = $this->request->getPost('nombre');
        $email = $this->request->getPost('email');

        // Validación básica
        if (empty($nombre) || empty($email)) {
            return redirect()->to(base_url('admin/profile'))
                           ->with('error', 'Nombre y email son requeridos');
        }

        // Actualizar datos en sesión
        $adminUser = session()->get('admin_user');
        $adminUser['nombre'] = $nombre;
        $adminUser['email'] = $email;
        
        session()->set('admin_user', $adminUser);

        return redirect()->to(base_url('admin/profile'))
                       ->with('success', 'Perfil actualizado exitosamente');
    }

    /**
     * Verificar credenciales del administrador
     */
    private function verifyAdminCredentials($email, $password)
    {
        try {
            $db = \Config\Database::connect();

            // Autenticar usando stored procedure
            $db->query("CALL sp_admin_authenticate(?, ?, @admin_id, @admin_data, @result)", [
                $email, $password
            ]);

            $authResult = $db->query("SELECT @admin_id as admin_id, @admin_data as admin_data, @result as result")->getRow();

            if ($authResult->result === 'VERIFY_PASSWORD') {
                // Verificar contraseña
                $adminQuery = $db->query("SELECT password FROM administradores WHERE id = ?", [$authResult->admin_id]);
                $adminPassword = $adminQuery->getRow();

                if ($adminPassword && password_verify($password, $adminPassword->password)) {
                    // Actualizar login exitoso
                    $db->query("CALL sp_admin_login_success(?, @result)", [$authResult->admin_id]);
                    return true;
                } else {
                    // Registrar intento fallido
                    $db->query("CALL sp_admin_login_failed(?, @result)", [$email]);
                    return false;
                }
            }

            return false;
        } catch (\Exception $e) {
            log_message('error', 'Error verificando credenciales admin: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtener estadísticas del dashboard
     */
    private function getDashboardStats()
    {
        $stats = [
            'total_users' => 0,
            'total_orders' => 0,
            'total_products' => 0,
            'total_revenue' => 0,
            'pending_orders' => 0,
            'recent_orders' => []
        ];

        try {
            $db = \Config\Database::connect();

            // Usar stored procedure para obtener estadísticas
            $query = $db->query("CALL sp_get_admin_dashboard_stats()");
            $result = $query->getResultArray();

            if (!empty($result)) {
                $row = $result[0];
                $stats['total_users'] = $row['total_users'] ?? 0;
                $stats['total_orders'] = $row['total_orders'] ?? 0;
                $stats['total_products'] = $row['total_products'] ?? 0;
                $stats['total_revenue'] = $row['total_revenue'] ?? 0;
                $stats['pending_orders'] = $row['pending_orders'] ?? 0;
            }

            // Obtener pedidos recientes con SP
            $query2 = $db->query("CALL sp_get_recent_orders(5)");
            $stats['recent_orders'] = $query2->getResultArray();

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo estadísticas: ' . $e->getMessage());

            // Fallback a consultas directas si SP no existe
            try {
                if ($db->tableExists('users')) {
                    $stats['total_users'] = $db->table('users')->countAllResults();
                }

                if ($db->tableExists('orders')) {
                    $stats['total_orders'] = $db->table('orders')->countAllResults();
                    $stats['pending_orders'] = $db->table('orders')->where('status', 'pending')->countAllResults();

                    // Revenue total con manejo seguro
                    $result = $db->table('orders')
                                ->selectSum('total', 'total_revenue')
                                ->where('status !=', 'cancelled')
                                ->get()
                                ->getRow();
                    $stats['total_revenue'] = $result ? ($result->total_revenue ?? 0) : 0;

                    // Pedidos recientes
                    $stats['recent_orders'] = $db->table('orders')
                                                ->select('orders.*, COALESCE(users.name, orders.customer_name) as user_name')
                                                ->join('users', 'users.id = orders.customer_id', 'left')
                                                ->orderBy('orders.created_at', 'DESC')
                                                ->limit(5)
                                                ->get()
                                                ->getResultArray();
                }

                if ($db->tableExists('products')) {
                    $stats['total_products'] = $db->table('products')->countAllResults();
                }

            } catch (\Exception $e2) {
                log_message('error', 'Error en fallback de estadísticas: ' . $e2->getMessage());
            }
        }

        return $stats;
    }
}
