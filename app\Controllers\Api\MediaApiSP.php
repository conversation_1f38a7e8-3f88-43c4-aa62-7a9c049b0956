<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class MediaApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Subir archivo de media
     * POST /api/media/upload
     */
    public function upload()
    {
        try {
            $file = $this->request->getFile('file');
            
            if (!$file || !$file->isValid()) {
                return $this->failValidationError('No se ha subido ningún archivo válido');
            }

            // Validar tipo de archivo
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file->getMimeType(), $allowedTypes)) {
                return $this->failValidationError('Tipo de archivo no permitido');
            }

            // <PERSON>idar tamaño (máximo 5MB)
            if ($file->getSize() > 5 * 1024 * 1024) {
                return $this->failValidationError('El archivo es demasiado grande (máximo 5MB)');
            }

            // Crear directorio si no existe
            $uploadPath = WRITEPATH . 'uploads/media/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generar nombre único
            $filename = uniqid() . '_' . time() . '.' . $file->getExtension();
            $file->move($uploadPath, $filename);

            // Obtener dimensiones si es imagen
            $width = null;
            $height = null;
            $fullPath = $uploadPath . $filename;
            
            if (function_exists('getimagesize')) {
                $imageInfo = getimagesize($fullPath);
                if ($imageInfo) {
                    $width = $imageInfo[0];
                    $height = $imageInfo[1];
                }
            }

            // URL pública del archivo
            $fileUrl = base_url('writable/uploads/media/' . $filename);

            // Datos adicionales del formulario
            $altText = $this->request->getPost('alt_text');
            $title = $this->request->getPost('title');
            $description = $this->request->getPost('description');

            // Llamar SP para guardar en BD
            $query = $this->db->query("CALL sp_upload_media_file(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @media_id, @result)", [
                $filename,
                $file->getName(),
                $fullPath,
                $fileUrl,
                $file->getSize(),
                $file->getMimeType(),
                'image',
                $width,
                $height,
                $altText,
                $title,
                $description
            ]);

            // Obtener resultados
            $resultQuery = $this->db->query("SELECT @media_id as media_id, @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'data' => [
                        'media_id' => $result['media_id'],
                        'filename' => $filename,
                        'file_url' => $fileUrl,
                        'width' => $width,
                        'height' => $height
                    ],
                    'message' => 'Archivo subido correctamente'
                ]);
            } else {
                // Eliminar archivo si falló la BD
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
                return $this->failServerError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::upload: ' . $e->getMessage());
            return $this->failServerError('Error al subir archivo');
        }
    }

    /**
     * Asociar media con entidad
     * POST /api/media/associate
     */
    public function associate()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'media_id' => 'required|integer',
                'entity_type' => 'required|in_list[product,category,user,order,other]',
                'entity_id' => 'required|integer',
                'relation_type' => 'required|in_list[featured,gallery,thumbnail,icon,other]',
                'sort_order' => 'permit_empty|integer'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para asociar
            $query = $this->db->query("CALL sp_associate_media(?, ?, ?, ?, ?, @result)", [
                $data['media_id'],
                $data['entity_type'],
                $data['entity_id'],
                $data['relation_type'],
                $data['sort_order'] ?? 0
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Media asociada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::associate: ' . $e->getMessage());
            return $this->failServerError('Error al asociar media');
        }
    }

    /**
     * Obtener media de una entidad
     * GET /api/media/entity/{type}/{id}
     */
    public function getEntityMedia($entityType = null, $entityId = null)
    {
        try {
            if (!$entityType || !$entityId) {
                return $this->failValidationError('Tipo de entidad e ID requeridos');
            }

            $relationType = $this->request->getGet('relation_type');

            $query = $this->db->query("CALL sp_get_entity_media(?, ?, ?)", [
                $entityType, $entityId, $relationType
            ]);

            $media = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $media,
                'message' => 'Media obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::getEntityMedia: ' . $e->getMessage());
            return $this->failServerError('Error al obtener media');
        }
    }

    /**
     * Obtener galería de producto
     * GET /api/media/product/{id}/gallery
     */
    public function getProductGallery($productId = null)
    {
        try {
            if (!$productId) {
                return $this->failValidationError('ID de producto requerido');
            }

            $query = $this->db->query("CALL sp_get_product_gallery(?)", [$productId]);
            $gallery = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $gallery,
                'message' => 'Galería obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::getProductGallery: ' . $e->getMessage());
            return $this->failServerError('Error al obtener galería');
        }
    }

    /**
     * Actualizar orden de imágenes
     * PUT /api/media/order
     */
    public function updateOrder()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'media_ids' => 'required|string',
                'entity_type' => 'required|in_list[product,category,user,order,other]',
                'entity_id' => 'required|integer'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para actualizar orden
            $query = $this->db->query("CALL sp_update_media_order(?, ?, ?, @result)", [
                $data['media_ids'],
                $data['entity_type'],
                $data['entity_id']
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Orden actualizado correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::updateOrder: ' . $e->getMessage());
            return $this->failServerError('Error al actualizar orden');
        }
    }

    /**
     * Eliminar archivo de media
     * DELETE /api/media/{id}
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de media requerido');
            }

            // Obtener información del archivo antes de eliminarlo
            $fileQuery = $this->db->query("
                SELECT file_path FROM media_files 
                WHERE id = ? AND deleted_at IS NULL
            ", [$id]);
            $fileInfo = $fileQuery->getRowArray();

            // Llamar SP para eliminar
            $query = $this->db->query("CALL sp_delete_media(?, @result)", [$id]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                // Eliminar archivo físico
                if ($fileInfo && file_exists($fileInfo['file_path'])) {
                    unlink($fileInfo['file_path']);
                }

                return $this->respondDeleted([
                    'status' => 'success',
                    'message' => 'Archivo eliminado correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::delete: ' . $e->getMessage());
            return $this->failServerError('Error al eliminar archivo');
        }
    }

    /**
     * Listar todos los archivos de media
     * GET /api/media
     */
    public function index()
    {
        try {
            $page = (int) ($this->request->getGet('page') ?? 1);
            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $offset = ($page - 1) * $limit;
            $fileType = $this->request->getGet('file_type');

            $whereClause = "WHERE mf.deleted_at IS NULL AND mf.is_active = 1";
            $params = [];

            if ($fileType) {
                $whereClause .= " AND mf.file_type = ?";
                $params[] = $fileType;
            }

            $query = $this->db->query("
                SELECT mf.*, COUNT(mr.id) as usage_count
                FROM media_files mf
                LEFT JOIN media_relations mr ON mr.media_id = mf.id
                {$whereClause}
                GROUP BY mf.id
                ORDER BY mf.created_at DESC
                LIMIT {$limit} OFFSET {$offset}
            ", $params);

            $media = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $media,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => count($media)
                ],
                'message' => 'Media obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en MediaApiSP::index: ' . $e->getMessage());
            return $this->failServerError('Error al obtener media');
        }
    }
}
