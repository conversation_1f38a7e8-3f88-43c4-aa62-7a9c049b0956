<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestCheckout extends BaseCommand
{
    protected $group       = 'Testing';
    protected $name        = 'test:checkout';
    protected $description = 'Probar el checkout con productos simulados';

    public function run(array $params)
    {
        CLI::write('=== PROBANDO CHECKOUT ===', 'yellow');
        CLI::newLine();

        try {
            // Simular sesión con productos en el carrito
            session_start();
            
            // Agregar productos al carrito en la sesión
            $_SESSION['cart'] = [
                2 => [
                    'product_id' => 2,
                    'quantity' => 1,
                    'price' => 15999.00
                ],
                3 => [
                    'product_id' => 3,
                    'quantity' => 2,
                    'price' => 8999.00
                ]
            ];
            
            CLI::write('✅ Productos agregados al carrito de sesión', 'green');
            CLI::write('Productos en carrito:', 'white');
            foreach ($_SESSION['cart'] as $item) {
                CLI::write("- Producto ID: {$item['product_id']}, Cantidad: {$item['quantity']}, Precio: Q{$item['price']}", 'white');
            }
            CLI::newLine();
            
            // Hacer petición al checkout
            $sessionId = session_id();
            CLI::write("Session ID: $sessionId", 'white');
            
            // Usar curl para probar el checkout con la sesión
            $cookieFile = WRITEPATH . 'test_cookies.txt';
            file_put_contents($cookieFile, "localhost:8081\tFALSE\t/\tFALSE\t0\tci_session\t$sessionId");
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost:8081/checkout');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            curl_setopt($ch, CURLOPT_HEADER, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            CLI::write("Código de respuesta HTTP: $httpCode", 'white');
            
            if ($httpCode === 200) {
                CLI::write('✅ Checkout cargó correctamente', 'green');
                
                // Buscar errores en la respuesta
                if (strpos($response, 'Undefined array key') !== false) {
                    CLI::error('❌ Aún hay errores de "Undefined array key"');
                    preg_match_all('/Undefined array key "([^"]+)"/', $response, $matches);
                    if (!empty($matches[1])) {
                        CLI::write('Campos faltantes:', 'red');
                        foreach (array_unique($matches[1]) as $field) {
                            CLI::write("- $field", 'red');
                        }
                    }
                } else {
                    CLI::write('✅ No se encontraron errores de campos faltantes', 'green');
                }
                
                if (strpos($response, 'Error al calcular envío') !== false) {
                    CLI::error('❌ Hay errores en el cálculo de envío');
                } else {
                    CLI::write('✅ No hay errores de cálculo de envío', 'green');
                }
                
            } elseif ($httpCode === 302) {
                CLI::write('⚠️  Checkout redirige (carrito vacío o sin sesión)', 'yellow');
            } else {
                CLI::error("❌ Error HTTP: $httpCode");
            }
            
            // Limpiar
            unlink($cookieFile);
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
