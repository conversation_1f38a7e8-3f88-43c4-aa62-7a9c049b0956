<?php

namespace App\Models;

use CodeIgniter\Model;

class CurrencyRateModel extends Model
{
    protected $table = 'currency_rates';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'from_currency',
        'to_currency', 
        'rate',
        'source',
        'is_active'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'from_currency' => 'required|max_length[3]',
        'to_currency' => 'required|max_length[3]',
        'rate' => 'required|decimal',
        'source' => 'permit_empty|max_length[50]',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Obtener tasa de cambio entre dos monedas
     */
    public function getRate($fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $rate = $this->where([
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'is_active' => true
        ])->first();

        if ($rate) {
            return (float) $rate['rate'];
        }

        // Si no existe la tasa directa, buscar la inversa
        $inverseRate = $this->where([
            'from_currency' => $toCurrency,
            'to_currency' => $fromCurrency,
            'is_active' => true
        ])->first();

        if ($inverseRate) {
            return 1.0 / (float) $inverseRate['rate'];
        }

        // Si no se encuentra ninguna tasa, usar tasa por defecto
        return $this->getDefaultRate($fromCurrency, $toCurrency);
    }

    /**
     * Obtener tasa por defecto (fallback)
     */
    private function getDefaultRate($fromCurrency, $toCurrency)
    {
        $defaultRates = [
            'USD_GTQ' => 7.80,
            'GTQ_USD' => 0.128205,
            'EUR_GTQ' => 8.50,
            'GTQ_EUR' => 0.117647,
        ];

        $key = $fromCurrency . '_' . $toCurrency;
        return $defaultRates[$key] ?? 1.0;
    }

    /**
     * Actualizar tasa de cambio
     */
    public function updateRate($fromCurrency, $toCurrency, $rate, $source = 'manual')
    {
        $existing = $this->where([
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency
        ])->first();

        $data = [
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'rate' => $rate,
            'source' => $source,
            'is_active' => true
        ];

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Obtener todas las tasas activas
     */
    public function getActiveRates()
    {
        return $this->where('is_active', true)
                   ->orderBy('from_currency', 'ASC')
                   ->orderBy('to_currency', 'ASC')
                   ->findAll();
    }

    /**
     * Obtener monedas disponibles
     */
    public function getAvailableCurrencies()
    {
        $fromCurrencies = $this->select('from_currency')
                              ->where('is_active', true)
                              ->groupBy('from_currency')
                              ->findColumn('from_currency');

        $toCurrencies = $this->select('to_currency')
                            ->where('is_active', true)
                            ->groupBy('to_currency')
                            ->findColumn('to_currency');

        return array_unique(array_merge($fromCurrencies, $toCurrencies));
    }
}
