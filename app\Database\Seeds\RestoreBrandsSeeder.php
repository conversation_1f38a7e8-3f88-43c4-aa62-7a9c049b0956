<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class RestoreBrandsSeeder extends Seeder
{
    public function run()
    {
        try {
            // Restaurar marcas eliminadas (poner deleted_at = NULL)
            $sql = "UPDATE brands SET deleted_at = NULL WHERE deleted_at IS NOT NULL";
            $this->db->query($sql);
            
            $affectedRows = $this->db->affectedRows();
            echo "✅ {$affectedRows} marcas restauradas exitosamente\n";
            
            // Mostrar marcas activas
            $brands = $this->db->query("
                SELECT id, name, slug, is_active, deleted_at 
                FROM brands 
                WHERE deleted_at IS NULL 
                ORDER BY name
            ")->getResultArray();
            
            echo "📋 Marcas activas:\n";
            foreach ($brands as $brand) {
                $status = $brand['is_active'] ? '✅' : '❌';
                echo "  {$status} {$brand['name']} (ID: {$brand['id']})\n";
            }
            
        } catch (\Exception $e) {
            echo "❌ Error restaurando marcas: " . $e->getMessage() . "\n";
        }
    }
}
