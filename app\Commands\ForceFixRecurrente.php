<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ForceFixRecurrente extends BaseCommand
{
    protected $group       = 'Fix';
    protected $name        = 'force:fix-recurrente';
    protected $description = 'Forzar corrección de Recurrente';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== FORZANDO CORRECCIÓN DE RECURRENTE ===', 'yellow');
            CLI::newLine();

            // Verificar estado actual
            $current = $db->query("SELECT id, name, slug, type, is_active FROM payment_methods WHERE slug = 'recurrente'")->getRowArray();
            CLI::write('Estado actual:', 'cyan');
            CLI::write("ID: {$current['id']}, Nombre: {$current['name']}, Tipo: '{$current['type']}', Activo: {$current['is_active']}", 'white');
            
            // Forzar actualización
            $result = $db->query("UPDATE payment_methods SET type = 'gateway' WHERE id = ?", [$current['id']]);
            CLI::write("Resultado de UPDATE: " . ($result ? 'SUCCESS' : 'FAILED'), $result ? 'green' : 'red');
            
            // Verificar después de la actualización
            $after = $db->query("SELECT id, name, slug, type, is_active FROM payment_methods WHERE slug = 'recurrente'")->getRowArray();
            CLI::write('Estado después:', 'cyan');
            CLI::write("ID: {$after['id']}, Nombre: {$after['name']}, Tipo: '{$after['type']}', Activo: {$after['is_active']}", 'white');
            
            // Verificar solo métodos activos
            CLI::newLine();
            CLI::write('=== MÉTODOS ACTIVOS PARA CHECKOUT ===', 'yellow');
            $activeMethods = $db->query("
                SELECT id, name, slug, description, type, icon, instructions
                FROM payment_methods 
                WHERE is_active = 1 
                ORDER BY sort_order, name
            ")->getResultArray();
            
            CLI::write('Total métodos activos: ' . count($activeMethods), 'cyan');
            foreach ($activeMethods as $method) {
                CLI::write("✅ {$method['name']} (tipo: '{$method['type']}')", 'green');
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
