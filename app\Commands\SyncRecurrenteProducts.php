<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\ProductSyncService;
use App\Models\ProductModel;

class SyncRecurrenteProducts extends BaseCommand
{
    protected $group       = 'Recurrente';
    protected $name        = 'recurrente:sync-products';
    protected $description = 'Sincronizar productos con Recurrente';
    protected $usage       = 'recurrente:sync-products [options]';
    protected $arguments   = [];
    protected $options     = [
        '--all'     => 'Sincronizar todos los productos activos',
        '--pending' => 'Sincronizar solo productos pendientes',
        '--force'   => 'Forzar sincronización incluso si ya están sincronizados',
        '--update-all' => 'Actualizar TODOS los productos (incluso los ya sincronizados) - útil para corregir URLs',
        '--limit'   => 'Limitar número de productos a sincronizar (default: 50)',
        '--dry-run' => 'Mostrar qué se haría sin ejecutar cambios'
    ];

    public function run(array $params)
    {
        CLI::write('=== Sincronización de Productos con Recurrente ===', 'yellow');
        CLI::newLine();

        $productSyncService = new ProductSyncService();
        $productModel = new ProductModel();

        // Verificar si Recurrente está habilitado
        $recurrenteService = new \App\Services\RecurrenteService();
        if (!$recurrenteService->isEnabled()) {
            CLI::error('Recurrente no está habilitado. Verifica la configuración.');
            return;
        }

        CLI::write('✓ Recurrente está habilitado', 'green');
        CLI::newLine();

        // Obtener opciones
        $syncAll = CLI::getOption('all');
        $syncPending = CLI::getOption('pending');
        $updateAll = CLI::getOption('update-all');
        $force = CLI::getOption('force');
        $limit = (int) (CLI::getOption('limit') ?? 0); // 0 = sin límite
        $dryRun = CLI::getOption('dry-run');

        // Determinar qué productos sincronizar
        if ($updateAll) {
            $query = $productModel
                ->where('is_active', 1)
                ->where('deleted_at IS NULL');
            if ($limit > 0) {
                $query->limit($limit);
            }
            $products = $query->findAll();
            $limitText = $limit > 0 ? "límite: {$limit}" : "sin límite";
            CLI::write("ACTUALIZANDO TODOS los productos activos ({$limitText})", 'cyan');
            CLI::write("Esto corregirá las URLs de imágenes y otros datos", 'yellow');
        } elseif ($syncAll) {
            $query = $productModel
                ->where('is_active', 1)
                ->where('deleted_at IS NULL');
            if ($limit > 0) {
                $query->limit($limit);
            }
            $products = $query->findAll();
            $limitText = $limit > 0 ? "límite: {$limit}" : "sin límite";
            CLI::write("Sincronizando todos los productos activos ({$limitText})", 'cyan');
        } elseif ($syncPending) {
            $products = $productSyncService->getProductsNeedingSync();
            if ($limit > 0 && count($products) > $limit) {
                $products = array_slice($products, 0, $limit);
            }
            $limitText = $limit > 0 ? "límite: {$limit}" : "sin límite";
            CLI::write("Sincronizando productos pendientes ({$limitText})", 'cyan');
        } else {
            // Por defecto, sincronizar productos pendientes
            $products = $productSyncService->getProductsNeedingSync();
            if ($limit > 0 && count($products) > $limit) {
                $products = array_slice($products, 0, $limit);
            }
            $limitText = $limit > 0 ? "límite: {$limit}" : "sin límite";
            CLI::write("Sincronizando productos pendientes ({$limitText})", 'cyan');
        }

        if (empty($products)) {
            CLI::write('No hay productos para sincronizar.', 'green');
            return;
        }

        CLI::write("Productos encontrados: " . count($products), 'yellow');
        CLI::newLine();

        if ($dryRun) {
            CLI::write('=== MODO DRY-RUN (No se realizarán cambios) ===', 'yellow');
            CLI::newLine();
        }

        // Mostrar progreso
        $total = count($products);
        $synced = 0;
        $errors = 0;
        $skipped = 0;

        foreach ($products as $index => $product) {
            $current = $index + 1;
            CLI::write("[{$current}/{$total}] Procesando: {$product['name']} (ID: {$product['id']})", 'white');

            if ($dryRun) {
                CLI::write("  → Se sincronizaría con Recurrente", 'cyan');
                continue;
            }

            try {
                // Verificar si ya está sincronizado y no es forzado (excepto si es --update-all)
                if (!$force && !$updateAll && !empty($product['recurrente_product_id']) && $product['recurrente_sync_status'] === 'synced') {
                    CLI::write("  → Ya sincronizado, omitiendo", 'yellow');
                    $skipped++;
                    continue;
                }

                // Sincronizar producto
                if (empty($product['recurrente_product_id'])) {
                    $result = $productSyncService->syncProductCreate($product['id']);
                    $action = 'creado';
                } else {
                    $result = $productSyncService->syncProductUpdate($product['id']);
                    $action = $updateAll ? 'actualizado (URLs corregidas)' : 'actualizado';
                }

                if ($result) {
                    CLI::write("  ✓ Producto {$action} en Recurrente", 'green');
                    $synced++;
                } else {
                    CLI::write("  ✗ Error sincronizando producto", 'red');
                    $errors++;
                }

            } catch (\Exception $e) {
                CLI::write("  ✗ Error: " . $e->getMessage(), 'red');
                $errors++;
            }

            // Pausa pequeña para no sobrecargar la API
            usleep(500000); // 0.5 segundos
        }

        CLI::newLine();
        CLI::write('=== Resumen de Sincronización ===', 'yellow');
        CLI::write("Total procesados: {$total}", 'white');
        CLI::write("Sincronizados: {$synced}", 'green');
        CLI::write("Errores: {$errors}", 'red');
        CLI::write("Omitidos: {$skipped}", 'yellow');

        if ($dryRun) {
            CLI::newLine();
            CLI::write('Para ejecutar los cambios, ejecuta el comando sin --dry-run', 'cyan');
        }

        CLI::newLine();
        CLI::write('Sincronización completada.', 'green');
    }
}
