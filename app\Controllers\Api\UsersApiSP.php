<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class UsersApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Asignar rol a usuario
     * POST /api/users/{id}/roles
     */
    public function assignRole($userId = null)
    {
        try {
            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'role_id' => 'required|integer',
                'assigned_by' => 'required|integer',
                'expires_at' => 'permit_empty|valid_date'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para asignar rol
            $query = $this->db->query("CALL sp_assign_role_to_user(?, ?, ?, ?, @result)", [
                $userId,
                $data['role_id'],
                $data['assigned_by'],
                $data['expires_at'] ?? null
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => 'Rol asignado correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::assignRole: ' . $e->getMessage());
            return $this->failServerError('Error al asignar rol');
        }
    }

    /**
     * Remover rol de usuario
     * DELETE /api/users/{id}/roles/{roleId}
     */
    public function removeRole($userId = null, $roleId = null)
    {
        try {
            if (!$userId || !$roleId) {
                return $this->failValidationError('ID de usuario y rol requeridos');
            }

            $removedBy = $this->request->getJSON(true)['removed_by'] ?? session()->get('user_id');

            if (!$removedBy) {
                return $this->failValidationError('ID del usuario que remueve el rol requerido');
            }

            // Llamar SP para remover rol
            $query = $this->db->query("CALL sp_remove_role_from_user(?, ?, ?, @result)", [
                $userId, $roleId, $removedBy
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondDeleted([
                    'status' => 'success',
                    'message' => 'Rol removido correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::removeRole: ' . $e->getMessage());
            return $this->failServerError('Error al remover rol');
        }
    }

    /**
     * Obtener roles de usuario
     * GET /api/users/{id}/roles
     */
    public function getUserRoles($userId = null)
    {
        try {
            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            $query = $this->db->query("CALL sp_get_user_roles(?)", [$userId]);
            $roles = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $roles,
                'message' => 'Roles obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::getUserRoles: ' . $e->getMessage());
            return $this->failServerError('Error al obtener roles');
        }
    }

    /**
     * Verificar permisos de usuario
     * GET /api/users/{id}/permissions/check
     */
    public function checkPermission($userId = null)
    {
        try {
            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            $module = $this->request->getGet('module');
            $action = $this->request->getGet('action');
            $resource = $this->request->getGet('resource');

            if (!$module || !$action) {
                return $this->failValidationError('Módulo y acción requeridos');
            }

            // Llamar SP para verificar permisos
            $query = $this->db->query("CALL sp_check_user_permission(?, ?, ?, ?, @has_permission)", [
                $userId, $module, $action, $resource
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @has_permission as has_permission");
            $result = $resultQuery->getRowArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'user_id' => $userId,
                    'module' => $module,
                    'action' => $action,
                    'resource' => $resource,
                    'has_permission' => (bool) $result['has_permission']
                ],
                'message' => 'Permisos verificados correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::checkPermission: ' . $e->getMessage());
            return $this->failServerError('Error al verificar permisos');
        }
    }

    /**
     * Registrar actividad de usuario
     * POST /api/users/{id}/activity
     */
    public function logActivity($userId = null)
    {
        try {
            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'action' => 'required|max_length[100]',
                'module' => 'required|max_length[50]',
                'resource_type' => 'permit_empty|max_length[50]',
                'resource_id' => 'permit_empty|integer',
                'description' => 'permit_empty|string',
                'data' => 'permit_empty'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Obtener información de la request
            $ipAddress = $this->request->getIPAddress();
            $userAgent = $this->request->getUserAgent()->getAgentString();
            $sessionId = session()->session_id;

            // Convertir data a JSON si es necesario
            $jsonData = null;
            if (!empty($data['data'])) {
                $jsonData = is_string($data['data']) ? $data['data'] : json_encode($data['data']);
            }

            // Llamar SP para registrar actividad
            $query = $this->db->query("CALL sp_log_user_activity(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $userId,
                $data['action'],
                $data['module'],
                $data['resource_type'] ?? null,
                $data['resource_id'] ?? null,
                $data['description'] ?? null,
                $ipAddress,
                $userAgent,
                $sessionId,
                $jsonData
            ]);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Actividad registrada correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::logActivity: ' . $e->getMessage());
            return $this->failServerError('Error al registrar actividad');
        }
    }

    /**
     * Obtener actividad de usuario
     * GET /api/users/{id}/activity
     */
    public function getUserActivity($userId = null)
    {
        try {
            $limit = (int) ($this->request->getGet('limit') ?? 50);
            $offset = (int) ($this->request->getGet('offset') ?? 0);

            $query = $this->db->query("CALL sp_get_user_activity(?, ?, ?)", [
                $userId, $limit, $offset
            ]);

            $activities = $query->getResultArray();

            // Procesar datos JSON
            foreach ($activities as &$activity) {
                if (!empty($activity['data'])) {
                    $activity['data'] = json_decode($activity['data'], true);
                }
            }

            return $this->respond([
                'status' => 'success',
                'data' => $activities,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'total' => count($activities)
                ],
                'message' => 'Actividad obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::getUserActivity: ' . $e->getMessage());
            return $this->failServerError('Error al obtener actividad');
        }
    }

    /**
     * Obtener todos los roles disponibles
     * GET /api/roles
     */
    public function getRoles()
    {
        try {
            $query = $this->db->query("
                SELECT 
                    r.*,
                    COUNT(rp.permission_id) as permissions_count,
                    COUNT(ur.user_id) as users_count
                FROM roles r
                LEFT JOIN role_permissions rp ON rp.role_id = r.id AND rp.is_active = 1
                LEFT JOIN user_roles ur ON ur.role_id = r.id AND ur.is_active = 1
                WHERE r.deleted_at IS NULL
                GROUP BY r.id
                ORDER BY r.level DESC
            ");

            $roles = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $roles,
                'message' => 'Roles obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::getRoles: ' . $e->getMessage());
            return $this->failServerError('Error al obtener roles');
        }
    }

    /**
     * Obtener todos los permisos disponibles
     * GET /api/permissions
     */
    public function getPermissions()
    {
        try {
            $module = $this->request->getGet('module');

            $sql = "
                SELECT 
                    p.*,
                    COUNT(rp.role_id) as roles_count
                FROM permissions p
                LEFT JOIN role_permissions rp ON rp.permission_id = p.id AND rp.is_active = 1
                WHERE p.deleted_at IS NULL
            ";

            $params = [];
            if ($module) {
                $sql .= " AND p.module = ?";
                $params[] = $module;
            }

            $sql .= " GROUP BY p.id ORDER BY p.module, p.action";

            $query = $this->db->query($sql, $params);
            $permissions = $query->getResultArray();

            // Agrupar por módulo
            $groupedPermissions = [];
            foreach ($permissions as $permission) {
                $groupedPermissions[$permission['module']][] = $permission;
            }

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'permissions' => $permissions,
                    'grouped' => $groupedPermissions
                ],
                'message' => 'Permisos obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en UsersApiSP::getPermissions: ' . $e->getMessage());
            return $this->failServerError('Error al obtener permisos');
        }
    }
}
