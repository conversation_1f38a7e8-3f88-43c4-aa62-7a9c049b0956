/**
 * 🎨 MrCell Modern CSS Variables
 * Modern CSS custom properties for consistent theming
 */

:root {
  /* 🎨 Brand Colors */
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --primary-light: #66b3ff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;

  /* 🌈 Extended Palette */
  --accent-color: #ff6b35;
  --accent-light: #ff8c69;
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-light));

  /* 📝 Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  --font-family-secondary: 'Poppins', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* 📏 Font Sizes (Fluid Typography) */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --font-size-3xl: clamp(2rem, 1.7rem + 1.5vw, 3rem);

  /* 📐 Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* 🎯 Layout */
  --container-max-width: 1200px;
  --container-padding: var(--space-md);
  --header-height: 80px;
  --footer-height: auto;

  /* 🎭 Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 🎪 Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* ⚡ Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* 📱 Breakpoints (for JS usage) */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;

  /* 🎨 Component Specific */
  --navbar-bg: rgba(255, 255, 255, 0.95);
  --navbar-backdrop: blur(10px);
  --card-bg: #ffffff;
  --card-border: rgba(0, 0, 0, 0.125);
  
  /* 🛒 E-commerce Specific */
  --product-card-bg: var(--card-bg);
  --product-card-hover: var(--light-color);
  --price-color: var(--success-color);
  --price-old-color: var(--secondary-color);
  --cart-badge-bg: var(--danger-color);
  --rating-color: #ffc107;

  /* 🌙 Dark Mode Support */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
}

/* 🌙 Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border-color: #404040;
    --card-bg: #2d2d2d;
    --navbar-bg: rgba(26, 26, 26, 0.95);
  }
}

/* 🎯 Dark Mode Class Override */
.dark-mode {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --border-color: #404040;
  --card-bg: #2d2d2d;
  --navbar-bg: rgba(26, 26, 26, 0.95);
}

/* 🎨 Utility Classes for Variables */
.text-primary-var { color: var(--text-primary); }
.text-secondary-var { color: var(--text-secondary); }
.bg-primary-var { background-color: var(--bg-primary); }
.bg-secondary-var { background-color: var(--bg-secondary); }
.border-var { border-color: var(--border-color); }

/* 📱 Responsive Typography */
.text-responsive {
  font-size: var(--font-size-base);
  line-height: 1.6;
}

.heading-responsive {
  font-size: var(--font-size-2xl);
  line-height: 1.2;
  font-weight: 600;
}

/* 🎭 Modern Shadows */
.shadow-modern { box-shadow: var(--shadow-md); }
.shadow-modern-lg { box-shadow: var(--shadow-lg); }
.shadow-modern-xl { box-shadow: var(--shadow-xl); }

/* ⚡ Smooth Transitions */
.transition-all { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* 🎪 Modern Border Radius */
.rounded-modern { border-radius: var(--radius-md); }
.rounded-modern-lg { border-radius: var(--radius-lg); }
.rounded-modern-xl { border-radius: var(--radius-xl); }
