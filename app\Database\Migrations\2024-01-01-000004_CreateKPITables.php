<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKPITables extends Migration
{
    public function up()
    {
        // Tabla de métricas de KPI
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'metric_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'metric_type' => [
                'type' => 'ENUM',
                'constraint' => ['sales', 'inventory', 'customer', 'shipping', 'marketing', 'financial'],
                'null' => false,
            ],
            'period_type' => [
                'type' => 'ENUM',
                'constraint' => ['hourly', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
                'null' => false,
            ],
            'period_start' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'period_end' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => false,
            ],
            'previous_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => true,
            ],
            'target_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => true,
            ],
            'unit' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'calculated_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['metric_name', 'period_type', 'period_start']);
        $this->forge->addKey('metric_type');
        $this->forge->addKey('period_start');
        $this->forge->addKey('calculated_at');
        
        $this->forge->createTable('kpi_metrics');

        // Tabla de alertas de KPI
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'metric_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'alert_type' => [
                'type' => 'ENUM',
                'constraint' => ['threshold_exceeded', 'threshold_below', 'trend_negative', 'trend_positive', 'anomaly'],
                'null' => false,
            ],
            'severity' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'critical'],
                'null' => false,
                'default' => 'medium',
            ],
            'current_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => false,
            ],
            'threshold_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => true,
            ],
            'message' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'recommendations' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'is_resolved' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'resolved_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'resolved_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['metric_name', 'is_resolved']);
        $this->forge->addKey(['severity', 'is_resolved']);
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('kpi_alerts');

        // Tabla de configuración de dashboards
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'dashboard_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'dashboard_type' => [
                'type' => 'ENUM',
                'constraint' => ['executive', 'sales', 'inventory', 'marketing', 'custom'],
                'null' => false,
                'default' => 'custom',
            ],
            'widgets_config' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'layout_config' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'refresh_interval' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 300,
            ],
            'is_default' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'is_shared' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'dashboard_type']);
        $this->forge->addKey('is_default');
        $this->forge->addKey('is_shared');
        
        $this->forge->createTable('dashboard_configs');

        // Tabla de reportes programados
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'report_type' => [
                'type' => 'ENUM',
                'constraint' => ['sales', 'inventory', 'customers', 'kpi_summary', 'custom'],
                'null' => false,
            ],
            'frequency' => [
                'type' => 'ENUM',
                'constraint' => ['daily', 'weekly', 'monthly', 'quarterly'],
                'null' => false,
            ],
            'schedule_time' => [
                'type' => 'TIME',
                'null' => false,
            ],
            'schedule_day' => [
                'type' => 'INT',
                'constraint' => 2,
                'null' => true,
            ],
            'recipients' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'parameters' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'format' => [
                'type' => 'ENUM',
                'constraint' => ['pdf', 'excel', 'csv', 'html'],
                'null' => false,
                'default' => 'pdf',
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 1,
            ],
            'last_sent' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'next_send' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['is_active', 'next_send']);
        $this->forge->addKey('report_type');
        $this->forge->addKey('frequency');
        
        $this->forge->createTable('scheduled_reports');

        // Tabla de historial de reportes enviados
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'scheduled_report_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'period_start' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'period_end' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'file_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'file_size' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'recipients_sent' => [
                'type' => 'JSON',
                'null' => false,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['generating', 'sent', 'failed'],
                'null' => false,
                'default' => 'generating',
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'generated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'sent_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('scheduled_report_id');
        $this->forge->addKey(['status', 'created_at']);
        $this->forge->addKey('sent_at');
        
        $this->forge->createTable('report_history');

        // Tabla de análisis de tendencias
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'metric_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'analysis_type' => [
                'type' => 'ENUM',
                'constraint' => ['trend', 'seasonality', 'forecast', 'correlation'],
                'null' => false,
            ],
            'period_analyzed' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'data_points' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'trend_direction' => [
                'type' => 'ENUM',
                'constraint' => ['up', 'down', 'stable', 'volatile'],
                'null' => true,
            ],
            'trend_strength' => [
                'type' => 'DECIMAL',
                'constraint' => '5,4',
                'null' => true,
            ],
            'confidence_level' => [
                'type' => 'DECIMAL',
                'constraint' => '5,4',
                'null' => true,
            ],
            'forecast_values' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'insights' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'recommendations' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['metric_name', 'analysis_type']);
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('trend_analysis');
    }

    public function down()
    {
        $this->forge->dropTable('trend_analysis');
        $this->forge->dropTable('report_history');
        $this->forge->dropTable('scheduled_reports');
        $this->forge->dropTable('dashboard_configs');
        $this->forge->dropTable('kpi_alerts');
        $this->forge->dropTable('kpi_metrics');
    }
}
