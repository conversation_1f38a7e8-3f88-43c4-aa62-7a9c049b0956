<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddCurrencyToProducts extends Migration
{
    public function up()
    {
        // Agregar campo currency a la tabla products
        $fields = [
            'currency' => [
                'type' => 'ENUM',
                'constraint' => ['GTQ', 'USD'],
                'default' => 'GTQ',
                'null' => false,
                'after' => 'cost_price'
            ]
        ];
        
        $this->forge->addColumn('products', $fields);
        
        // Agregar configuraciones de tipo de cambio en system_settings
        $data = [
            [
                'setting_key' => 'exchange_rate_usd_to_gtq',
                'setting_value' => '7.75',
                'setting_group' => 'currency',
                'display_name' => 'Tipo de Cambio USD a GTQ',
                'description' => 'Tipo de cambio para convertir dólares americanos a quetzales guatemaltecos',
                'setting_type' => 'number',
                'options' => json_encode(['min' => 1, 'max' => 20, 'step' => 0.01]),
                'is_active' => true
            ],
            [
                'setting_key' => 'default_currency',
                'setting_value' => 'GTQ',
                'setting_group' => 'currency',
                'display_name' => 'Moneda por Defecto',
                'description' => 'Moneda por defecto del sistema',
                'setting_type' => 'select',
                'options' => json_encode(['GTQ' => 'Quetzales (GTQ)', 'USD' => 'Dólares (USD)']),
                'is_active' => true
            ],
            [
                'setting_key' => 'show_currency_conversion',
                'setting_value' => '1',
                'setting_group' => 'currency',
                'display_name' => 'Mostrar Conversión de Moneda',
                'description' => 'Mostrar automáticamente la conversión de precios en ambas monedas',
                'setting_type' => 'checkbox',
                'options' => null,
                'is_active' => true
            ],
            [
                'setting_key' => 'currency_symbol_gtq',
                'setting_value' => 'Q',
                'setting_group' => 'currency',
                'display_name' => 'Símbolo Quetzales',
                'description' => 'Símbolo para mostrar precios en quetzales',
                'setting_type' => 'text',
                'options' => null,
                'is_active' => true
            ],
            [
                'setting_key' => 'currency_symbol_usd',
                'setting_value' => '$',
                'setting_group' => 'currency',
                'display_name' => 'Símbolo Dólares',
                'description' => 'Símbolo para mostrar precios en dólares',
                'setting_type' => 'text',
                'options' => null,
                'is_active' => true
            ]
        ];
        
        $this->db->table('system_settings')->insertBatch($data);
        
        // Crear índice para el campo currency
        $this->forge->addKey('currency', false, false, 'products');
    }

    public function down()
    {
        // Eliminar configuraciones de moneda
        $this->db->table('system_settings')->where('setting_group', 'currency')->delete();
        
        // Eliminar campo currency de products
        $this->forge->dropColumn('products', 'currency');
    }
}
