<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Mensajes de Contacto</h1>
            <p class="text-muted">Gestiona las solicitudes de contacto de los clientes</p>
        </div>
        <div>
            <a href="<?= base_url('admin/contact/export') ?>" class="btn btn-outline-success">
                <i class="fas fa-download me-2"></i>Exportar CSV
            </a>
            <a href="<?= base_url('admin/contact/dashboard') ?>" class="btn btn-primary">
                <i class="fas fa-chart-bar me-2"></i>Dashboard
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total</h6>
                            <h3 class="mb-0"><?= $stats['total'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Pendientes</h6>
                            <h3 class="mb-0"><?= $stats['pending'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Hoy</h6>
                            <h3 class="mb-0"><?= $stats['today'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Esta Semana</h6>
                            <h3 class="mb-0"><?= $stats['this_week'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?= base_url('admin/contact') ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Estado</label>
                        <select name="status" class="form-select">
                            <option value="">Todos</option>
                            <option value="pending" <?= ($filters['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Pendiente</option>
                            <option value="read" <?= ($filters['status'] ?? '') === 'read' ? 'selected' : '' ?>>Leído</option>
                            <option value="replied" <?= ($filters['status'] ?? '') === 'replied' ? 'selected' : '' ?>>Respondido</option>
                            <option value="closed" <?= ($filters['status'] ?? '') === 'closed' ? 'selected' : '' ?>>Cerrado</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Prioridad</label>
                        <select name="priority" class="form-select">
                            <option value="">Todas</option>
                            <option value="low" <?= ($filters['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Baja</option>
                            <option value="normal" <?= ($filters['priority'] ?? '') === 'normal' ? 'selected' : '' ?>>Normal</option>
                            <option value="high" <?= ($filters['priority'] ?? '') === 'high' ? 'selected' : '' ?>>Alta</option>
                            <option value="urgent" <?= ($filters['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>Urgente</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="search" class="form-control" placeholder="Nombre, email, asunto..." value="<?= esc($filters['search'] ?? '') ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filtrar
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($messages)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5>No hay mensajes</h5>
                    <p class="text-muted">No se encontraron mensajes con los filtros aplicados.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Fecha</th>
                                <th>Nombre</th>
                                <th>Email</th>
                                <th>Asunto</th>
                                <th>Estado</th>
                                <th>Prioridad</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($messages as $message): ?>
                                <tr>
                                    <td><?= $message['id'] ?></td>
                                    <td>
                                        <small><?= date('d/m/Y H:i', strtotime($message['created_at'])) ?></small>
                                    </td>
                                    <td><?= esc($message['name']) ?></td>
                                    <td><?= esc($message['email']) ?></td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?= esc($message['subject']) ?>">
                                            <?= esc($message['subject']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'read' => 'info',
                                            'replied' => 'success',
                                            'closed' => 'secondary'
                                        ];
                                        $statusLabels = [
                                            'pending' => 'Pendiente',
                                            'read' => 'Leído',
                                            'replied' => 'Respondido',
                                            'closed' => 'Cerrado'
                                        ];
                                        ?>
                                        <span class="badge bg-<?= $statusColors[$message['status']] ?? 'secondary' ?>">
                                            <?= $statusLabels[$message['status']] ?? $message['status'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $priorityColors = [
                                            'low' => 'success',
                                            'normal' => 'primary',
                                            'high' => 'warning',
                                            'urgent' => 'danger'
                                        ];
                                        $priorityLabels = [
                                            'low' => 'Baja',
                                            'normal' => 'Normal',
                                            'high' => 'Alta',
                                            'urgent' => 'Urgente'
                                        ];
                                        ?>
                                        <span class="badge bg-<?= $priorityColors[$message['priority']] ?? 'primary' ?>">
                                            <?= $priorityLabels[$message['priority']] ?? $message['priority'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('admin/contact/view/' . $message['id']) ?>" class="btn btn-outline-primary" title="Ver">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteMessage(<?= $message['id'] ?>)" title="Eliminar">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Paginación">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                    <a class="page-link" href="<?= base_url('admin/contact?page=' . $i) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteMessage(id) {
    if (confirm('¿Estás seguro de que quieres eliminar este mensaje?')) {
        fetch(`/admin/contact/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error al eliminar el mensaje');
        });
    }
}
</script>
<?= $this->endSection() ?>
