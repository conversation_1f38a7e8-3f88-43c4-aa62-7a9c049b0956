<?php

if (!function_exists('product_image_url')) {
    /**
     * Genera la URL completa para una imagen de producto
     * 
     * @param string|null $imageName Nombre del archivo de imagen
     * @param string $defaultImage Imagen por defecto si no se proporciona una
     * @return string URL completa de la imagen
     */
    function product_image_url($imageName = null, $defaultImage = 'no-image.jpg')
    {
        if (empty($imageName)) {
            return base_url('assets/img/' . $defaultImage);
        }

        // Si ya es una URL completa, devolverla tal como está
        if (strpos($imageName, 'http') === 0) {
            return $imageName;
        }

        // Si contiene una ruta relativa completa, usar base_url directamente
        if (strpos($imageName, 'assets/') === 0) {
            return base_url($imageName);
        }

        // Si es solo el nombre del archivo, construir la ruta completa
        return base_url('assets/img/products/' . $imageName);
    }
}

if (!function_exists('category_image_url')) {
    /**
     * Genera la URL completa para una imagen de categoría
     * 
     * @param string|null $imageName Nombre del archivo de imagen
     * @param string $defaultImage Imagen por defecto si no se proporciona una
     * @return string URL completa de la imagen
     */
    function category_image_url($imageName = null, $defaultImage = 'default.svg')
    {
        if (empty($imageName)) {
            return base_url('assets/img/categories/' . $defaultImage);
        }

        // Si ya es una URL completa, devolverla tal como está
        if (strpos($imageName, 'http') === 0) {
            return $imageName;
        }

        // Si contiene una ruta relativa completa, usar base_url directamente
        if (strpos($imageName, 'assets/') === 0) {
            return base_url($imageName);
        }

        // Si es solo el nombre del archivo, construir la ruta completa
        return base_url('assets/img/categories/' . $imageName);
    }
}

if (!function_exists('brand_image_url')) {
    /**
     * Genera la URL completa para una imagen de marca
     * 
     * @param string|null $imageName Nombre del archivo de imagen
     * @param string $defaultImage Imagen por defecto si no se proporciona una
     * @return string URL completa de la imagen
     */
    function brand_image_url($imageName = null, $defaultImage = 'default.png')
    {
        if (empty($imageName)) {
            return base_url('assets/img/brands/' . $defaultImage);
        }

        // Si ya es una URL completa, devolverla tal como está
        if (strpos($imageName, 'http') === 0) {
            return $imageName;
        }

        // Si contiene una ruta relativa completa, usar base_url directamente
        if (strpos($imageName, 'assets/') === 0) {
            return base_url($imageName);
        }

        // Si es solo el nombre del archivo, construir la ruta completa
        return base_url('assets/img/brands/' . $imageName);
    }
}

if (!function_exists('placeholder_image_url')) {
    /**
     * Genera la URL para una imagen placeholder
     *
     * @param int $width Ancho de la imagen
     * @param int $height Alto de la imagen
     * @param string $text Texto a mostrar en el placeholder
     * @param string $bgColor Color de fondo (sin #)
     * @param string $textColor Color del texto (sin #)
     * @return string URL del placeholder
     */
    function placeholder_image_url($width = 300, $height = 300, $text = 'Producto', $bgColor = 'dc2626', $textColor = 'ffffff')
    {
        return "https://via.placeholder.com/{$width}x{$height}/{$bgColor}/{$textColor}?text=" . urlencode($text);
    }
}

if (!function_exists('sanitize_filename')) {
    /**
     * Sanitiza un nombre de archivo removiendo caracteres problemáticos
     *
     * @param string $filename Nombre del archivo original
     * @param bool $preserveExtension Si mantener la extensión original
     * @return string Nombre de archivo sanitizado
     */
    function sanitize_filename($filename, $preserveExtension = true)
    {
        // Obtener la extensión si se debe preservar
        $extension = '';
        if ($preserveExtension) {
            $pathInfo = pathinfo($filename);
            $extension = isset($pathInfo['extension']) ? '.' . strtolower($pathInfo['extension']) : '';
            $filename = $pathInfo['filename'];
        }

        // Convertir a minúsculas
        $filename = strtolower($filename);

        // Reemplazar caracteres problemáticos
        $filename = preg_replace('/[^a-z0-9\-_]/', '-', $filename);

        // Reemplazar múltiples guiones con uno solo
        $filename = preg_replace('/-+/', '-', $filename);

        // Remover guiones al inicio y final
        $filename = trim($filename, '-');

        // Si el nombre queda vacío, usar un nombre por defecto
        if (empty($filename)) {
            $filename = 'archivo-' . time();
        }

        return $filename . $extension;
    }
}

if (!function_exists('generate_unique_filename')) {
    /**
     * Genera un nombre de archivo único basado en el nombre original
     *
     * @param string $originalName Nombre original del archivo
     * @param string $directory Directorio donde se guardará el archivo
     * @return string Nombre de archivo único
     */
    function generate_unique_filename($originalName, $directory = '')
    {
        // Sanitizar el nombre original
        $sanitized = sanitize_filename($originalName);

        // Obtener la extensión
        $pathInfo = pathinfo($sanitized);
        $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
        $basename = $pathInfo['filename'];

        // Agregar timestamp para unicidad
        $timestamp = time();
        $uniqueName = $basename . '-' . $timestamp . $extension;

        // Si se proporciona un directorio, verificar que el archivo no exista
        if (!empty($directory)) {
            $counter = 1;
            while (file_exists($directory . '/' . $uniqueName)) {
                $uniqueName = $basename . '-' . $timestamp . '-' . $counter . $extension;
                $counter++;
            }
        }

        return $uniqueName;
    }
}
