<?php

namespace App\Libraries;

/**
 * Gestor de Redes Sociales
 * Integración con Facebook, Instagram, Twitter y otras plataformas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SocialMediaManager
{
    private $config;
    private $db;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->config = [
            'facebook' => [
                'enabled' => env('FACEBOOK_ENABLED', false),
                'app_id' => env('FACEBOOK_APP_ID', ''),
                'app_secret' => env('FACEBOOK_APP_SECRET', ''),
                'page_id' => env('FACEBOOK_PAGE_ID', ''),
                'access_token' => env('FACEBOOK_ACCESS_TOKEN', '')
            ],
            'instagram' => [
                'enabled' => env('INSTAGRAM_ENABLED', false),
                'business_account_id' => env('INSTAGRAM_BUSINESS_ID', ''),
                'access_token' => env('INSTAGRAM_ACCESS_TOKEN', '')
            ],
            'twitter' => [
                'enabled' => env('TWITTER_ENABLED', false),
                'api_key' => env('TWITTER_API_KEY', ''),
                'api_secret' => env('TWITTER_API_SECRET', ''),
                'access_token' => env('TWITTER_ACCESS_TOKEN', ''),
                'access_token_secret' => env('TWITTER_ACCESS_TOKEN_SECRET', '')
            ],
            'tiktok' => [
                'enabled' => env('TIKTOK_ENABLED', false),
                'app_id' => env('TIKTOK_APP_ID', ''),
                'app_secret' => env('TIKTOK_APP_SECRET', ''),
                'access_token' => env('TIKTOK_ACCESS_TOKEN', '')
            ]
        ];
    }
    
    /**
     * Publicar producto en todas las redes sociales
     */
    public function publishProduct(array $product, array $options = []): array
    {
        $results = [
            'product_id' => $product['id'],
            'published_at' => date('Y-m-d H:i:s'),
            'platforms' => [],
            'total_success' => 0,
            'total_failed' => 0
        ];
        
        try {
            // Facebook
            if ($this->config['facebook']['enabled']) {
                $facebookResult = $this->publishToFacebook($product, $options);
                $results['platforms']['facebook'] = $facebookResult;
                
                if ($facebookResult['success']) {
                    $results['total_success']++;
                } else {
                    $results['total_failed']++;
                }
            }
            
            // Instagram
            if ($this->config['instagram']['enabled']) {
                $instagramResult = $this->publishToInstagram($product, $options);
                $results['platforms']['instagram'] = $instagramResult;
                
                if ($instagramResult['success']) {
                    $results['total_success']++;
                } else {
                    $results['total_failed']++;
                }
            }
            
            // Twitter
            if ($this->config['twitter']['enabled']) {
                $twitterResult = $this->publishToTwitter($product, $options);
                $results['platforms']['twitter'] = $twitterResult;
                
                if ($twitterResult['success']) {
                    $results['total_success']++;
                } else {
                    $results['total_failed']++;
                }
            }
            
            // TikTok
            if ($this->config['tiktok']['enabled']) {
                $tiktokResult = $this->publishToTikTok($product, $options);
                $results['platforms']['tiktok'] = $tiktokResult;
                
                if ($tiktokResult['success']) {
                    $results['total_success']++;
                } else {
                    $results['total_failed']++;
                }
            }
            
            // Log publicación
            $this->logSocialMediaPost($product['id'], $results);
            
            return $results;
            
        } catch (\Exception $e) {
            log_message('error', 'Social media publish error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'product_id' => $product['id']
            ];
        }
    }
    
    /**
     * Publicar en Facebook
     */
    private function publishToFacebook(array $product, array $options): array
    {
        try {
            if (!$this->config['facebook']['enabled']) {
                return ['success' => false, 'error' => 'Facebook disabled'];
            }
            
            $message = $this->generateFacebookPost($product, $options);
            $imageUrl = $this->getProductImageUrl($product);
            
            $postData = [
                'message' => $message,
                'access_token' => $this->config['facebook']['access_token']
            ];
            
            if ($imageUrl) {
                $postData['link'] = base_url('producto/' . $product['slug']);
                $postData['picture'] = $imageUrl;
            }
            
            $url = "https://graph.facebook.com/v18.0/{$this->config['facebook']['page_id']}/feed";
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($postData),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if ($httpCode === 200 && isset($responseData['id'])) {
                return [
                    'success' => true,
                    'post_id' => $responseData['id'],
                    'platform' => 'facebook',
                    'url' => "https://facebook.com/{$responseData['id']}"
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $responseData['error']['message'] ?? 'Unknown Facebook error',
                    'platform' => 'facebook'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => 'facebook'
            ];
        }
    }
    
    /**
     * Publicar en Instagram
     */
    private function publishToInstagram(array $product, array $options): array
    {
        try {
            if (!$this->config['instagram']['enabled']) {
                return ['success' => false, 'error' => 'Instagram disabled'];
            }
            
            $caption = $this->generateInstagramPost($product, $options);
            $imageUrl = $this->getProductImageUrl($product);
            
            if (!$imageUrl) {
                return ['success' => false, 'error' => 'Image required for Instagram'];
            }
            
            // Paso 1: Crear container de media
            $containerData = [
                'image_url' => $imageUrl,
                'caption' => $caption,
                'access_token' => $this->config['instagram']['access_token']
            ];
            
            $containerUrl = "https://graph.facebook.com/v18.0/{$this->config['instagram']['business_account_id']}/media";
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $containerUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($containerData),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $containerResponse = curl_exec($ch);
            $containerHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $containerData = json_decode($containerResponse, true);
            
            if ($containerHttpCode !== 200 || !isset($containerData['id'])) {
                return [
                    'success' => false,
                    'error' => $containerData['error']['message'] ?? 'Container creation failed',
                    'platform' => 'instagram'
                ];
            }
            
            // Paso 2: Publicar media
            $publishData = [
                'creation_id' => $containerData['id'],
                'access_token' => $this->config['instagram']['access_token']
            ];
            
            $publishUrl = "https://graph.facebook.com/v18.0/{$this->config['instagram']['business_account_id']}/media_publish";
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $publishUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($publishData),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $publishResponse = curl_exec($ch);
            $publishHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $publishData = json_decode($publishResponse, true);
            
            if ($publishHttpCode === 200 && isset($publishData['id'])) {
                return [
                    'success' => true,
                    'post_id' => $publishData['id'],
                    'platform' => 'instagram',
                    'url' => "https://instagram.com/p/{$publishData['id']}"
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $publishData['error']['message'] ?? 'Publish failed',
                    'platform' => 'instagram'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => 'instagram'
            ];
        }
    }
    
    /**
     * Publicar en Twitter
     */
    private function publishToTwitter(array $product, array $options): array
    {
        try {
            if (!$this->config['twitter']['enabled']) {
                return ['success' => false, 'error' => 'Twitter disabled'];
            }
            
            $tweet = $this->generateTwitterPost($product, $options);
            
            // Usar Twitter API v2
            $tweetData = [
                'text' => $tweet
            ];
            
            $headers = [
                'Authorization: Bearer ' . $this->config['twitter']['access_token'],
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://api.twitter.com/2/tweets',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($tweetData),
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if ($httpCode === 201 && isset($responseData['data']['id'])) {
                return [
                    'success' => true,
                    'post_id' => $responseData['data']['id'],
                    'platform' => 'twitter',
                    'url' => "https://twitter.com/i/status/{$responseData['data']['id']}"
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $responseData['errors'][0]['message'] ?? 'Twitter API error',
                    'platform' => 'twitter'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => 'twitter'
            ];
        }
    }
    
    /**
     * Publicar en TikTok
     */
    private function publishToTikTok(array $product, array $options): array
    {
        // TikTok requiere video, por ahora retornamos como no implementado
        return [
            'success' => false,
            'error' => 'TikTok publishing not implemented (requires video content)',
            'platform' => 'tiktok'
        ];
    }
    
    /**
     * Generar contenido para Facebook
     */
    private function generateFacebookPost(array $product, array $options): string
    {
        $template = $options['facebook_template'] ?? 'default';
        
        $templates = [
            'default' => "🔥 ¡Nuevo producto disponible!\n\n📱 {product_name}\n💰 Precio: Q{price}\n\n{description}\n\n¡Visita nuestra tienda para más detalles!\n\n#MrCellGuatemala #Tecnología #Ofertas",
            'offer' => "🎉 ¡OFERTA ESPECIAL!\n\n📱 {product_name}\n💸 Precio especial: Q{price}\n⏰ ¡Por tiempo limitado!\n\n{description}\n\n¡No te lo pierdas!\n\n#Oferta #MrCellGuatemala #Tecnología",
            'new_arrival' => "✨ ¡RECIÉN LLEGADO!\n\n📱 {product_name}\n💰 Q{price}\n\n{description}\n\n¡Sé el primero en tenerlo!\n\n#NuevoProducto #MrCellGuatemala #Tecnología"
        ];
        
        $message = $templates[$template] ?? $templates['default'];
        
        return $this->replacePlaceholders($message, $product);
    }
    
    /**
     * Generar contenido para Instagram
     */
    private function generateInstagramPost(array $product, array $options): string
    {
        $template = $options['instagram_template'] ?? 'default';
        
        $templates = [
            'default' => "📱 {product_name}\n💰 Q{price}\n\n{description}\n\n#MrCellGuatemala #Tecnología #Guatemala #Smartphone #Tech #Gadgets #Shopping #Ofertas",
            'lifestyle' => "✨ Eleva tu estilo de vida con {product_name}\n\n💫 Características increíbles\n💰 Precio accesible: Q{price}\n🚀 Tecnología de vanguardia\n\n{description}\n\n#LifeStyle #Tech #MrCellGuatemala #Guatemala #Innovation",
            'minimalist' => "{product_name}\nQ{price}\n\n{description}\n\n#MrCellGuatemala #Tech #Guatemala"
        ];
        
        $caption = $templates[$template] ?? $templates['default'];
        
        return $this->replacePlaceholders($caption, $product);
    }
    
    /**
     * Generar contenido para Twitter
     */
    private function generateTwitterPost(array $product, array $options): string
    {
        $template = $options['twitter_template'] ?? 'default';
        
        $templates = [
            'default' => "🔥 {product_name} - Q{price}\n\n{short_description}\n\n🛒 {product_url}\n\n#MrCellGuatemala #Tech #Guatemala",
            'deal' => "💥 OFERTA: {product_name}\n💰 Solo Q{price}\n⏰ ¡Por tiempo limitado!\n\n🛒 {product_url}\n\n#Oferta #MrCellGuatemala #Tech",
            'announcement' => "📢 ¡Ya disponible!\n\n📱 {product_name}\n💰 Q{price}\n\n🛒 {product_url}\n\n#NuevoProducto #MrCellGuatemala"
        ];
        
        $tweet = $templates[$template] ?? $templates['default'];
        
        return $this->replacePlaceholders($tweet, $product);
    }
    
    /**
     * Reemplazar placeholders en el texto
     */
    private function replacePlaceholders(string $text, array $product): string
    {
        $placeholders = [
            '{product_name}' => $product['name'],
            '{price}' => number_format($product['price'], 2),
            '{description}' => $this->truncateText($product['description'] ?? '', 200),
            '{short_description}' => $this->truncateText($product['description'] ?? '', 100),
            '{product_url}' => base_url('producto/' . $product['slug']),
            '{category}' => $product['category_name'] ?? '',
            '{brand}' => $product['brand_name'] ?? ''
        ];
        
        return str_replace(array_keys($placeholders), array_values($placeholders), $text);
    }
    
    /**
     * Obtener URL de imagen del producto
     */
    private function getProductImageUrl(array $product): ?string
    {
        if (!empty($product['featured_image'])) {
            return base_url('uploads/' . $product['featured_image']);
        }
        
        return null;
    }
    
    /**
     * Truncar texto
     */
    private function truncateText(string $text, int $length): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length - 3) . '...';
    }
    
    /**
     * Log de publicación en redes sociales
     */
    private function logSocialMediaPost(int $productId, array $results): void
    {
        try {
            $this->db->table('social_media_posts')->insert([
                'product_id' => $productId,
                'platforms' => json_encode($results['platforms']),
                'total_success' => $results['total_success'],
                'total_failed' => $results['total_failed'],
                'published_at' => $results['published_at'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging social media post: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas de redes sociales
     */
    public function getSocialMediaStats(int $days = 30): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $stats = $this->db->table('social_media_posts')
                            ->select('
                                COUNT(*) as total_posts,
                                SUM(total_success) as successful_posts,
                                SUM(total_failed) as failed_posts,
                                AVG(total_success) as avg_success_rate
                            ')
                            ->where('created_at >=', $startDate)
                            ->get()
                            ->getRowArray();
            
            // Estadísticas por plataforma
            $platformStats = [];
            $posts = $this->db->table('social_media_posts')
                            ->select('platforms')
                            ->where('created_at >=', $startDate)
                            ->get()
                            ->getResultArray();
            
            foreach ($posts as $post) {
                $platforms = json_decode($post['platforms'], true);
                foreach ($platforms as $platform => $result) {
                    if (!isset($platformStats[$platform])) {
                        $platformStats[$platform] = ['success' => 0, 'failed' => 0];
                    }
                    
                    if ($result['success']) {
                        $platformStats[$platform]['success']++;
                    } else {
                        $platformStats[$platform]['failed']++;
                    }
                }
            }
            
            return [
                'period_days' => $days,
                'total_stats' => $stats,
                'platform_stats' => $platformStats,
                'enabled_platforms' => $this->getEnabledPlatforms()
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Obtener plataformas habilitadas
     */
    public function getEnabledPlatforms(): array
    {
        $enabled = [];
        
        foreach ($this->config as $platform => $config) {
            if ($config['enabled']) {
                $enabled[] = $platform;
            }
        }
        
        return $enabled;
    }
    
    /**
     * Verificar configuración de plataformas
     */
    public function validateConfiguration(): array
    {
        $validation = [];
        
        foreach ($this->config as $platform => $config) {
            $validation[$platform] = [
                'enabled' => $config['enabled'],
                'configured' => false,
                'missing_fields' => []
            ];
            
            if ($config['enabled']) {
                switch ($platform) {
                    case 'facebook':
                        $required = ['app_id', 'app_secret', 'page_id', 'access_token'];
                        break;
                    case 'instagram':
                        $required = ['business_account_id', 'access_token'];
                        break;
                    case 'twitter':
                        $required = ['api_key', 'api_secret', 'access_token', 'access_token_secret'];
                        break;
                    case 'tiktok':
                        $required = ['app_id', 'app_secret', 'access_token'];
                        break;
                    default:
                        $required = [];
                }
                
                $missing = [];
                foreach ($required as $field) {
                    if (empty($config[$field])) {
                        $missing[] = $field;
                    }
                }
                
                $validation[$platform]['configured'] = empty($missing);
                $validation[$platform]['missing_fields'] = $missing;
            }
        }
        
        return $validation;
    }
    
    /**
     * Programar publicación automática
     */
    public function schedulePost(int $productId, array $platforms, \DateTime $scheduledAt, array $options = []): array
    {
        try {
            $scheduleData = [
                'product_id' => $productId,
                'platforms' => json_encode($platforms),
                'scheduled_at' => $scheduledAt->format('Y-m-d H:i:s'),
                'options' => json_encode($options),
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $scheduleId = $this->db->table('scheduled_social_posts')->insert($scheduleData, true);
            
            return [
                'success' => true,
                'schedule_id' => $scheduleId,
                'scheduled_at' => $scheduledAt->format('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Procesar publicaciones programadas
     */
    public function processScheduledPosts(): array
    {
        try {
            $now = date('Y-m-d H:i:s');

            $scheduledPosts = $this->db->table('scheduled_social_posts ssp')
                                     ->select('ssp.*, p.name, p.slug, p.price, p.description, p.featured_image')
                                     ->join('products p', 'ssp.product_id = p.id')
                                     ->where('ssp.scheduled_at <=', $now)
                                     ->where('ssp.status', 'pending')
                                     ->limit(10)
                                     ->get()
                                     ->getResultArray();

            $processed = 0;
            $errors = [];

            foreach ($scheduledPosts as $scheduledPost) {
                try {
                    $product = [
                        'id' => $scheduledPost['product_id'],
                        'name' => $scheduledPost['name'],
                        'slug' => $scheduledPost['slug'],
                        'price' => $scheduledPost['price'],
                        'description' => $scheduledPost['description'],
                        'featured_image' => $scheduledPost['featured_image']
                    ];

                    $platforms = json_decode($scheduledPost['platforms'], true);
                    $options = json_decode($scheduledPost['options'], true) ?? [];

                    $result = $this->publishProduct($product, $options);

                    // Actualizar estado
                    $this->db->table('scheduled_social_posts')
                            ->where('id', $scheduledPost['id'])
                            ->update([
                                'status' => $result['total_success'] > 0 ? 'completed' : 'failed',
                                'result' => json_encode($result),
                                'processed_at' => date('Y-m-d H:i:s')
                            ]);

                    $processed++;

                } catch (\Exception $e) {
                    $errors[] = "Post {$scheduledPost['id']}: " . $e->getMessage();

                    // Marcar como fallido
                    $this->db->table('scheduled_social_posts')
                            ->where('id', $scheduledPost['id'])
                            ->update([
                                'status' => 'failed',
                                'result' => json_encode(['error' => $e->getMessage()]),
                                'processed_at' => date('Y-m-d H:i:s')
                            ]);
                }
            }

            return [
                'success' => true,
                'processed' => $processed,
                'total_scheduled' => count($scheduledPosts),
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
