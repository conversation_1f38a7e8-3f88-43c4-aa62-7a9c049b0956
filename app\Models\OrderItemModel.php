<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderItemModel extends Model
{
    protected $table            = 'order_items';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'quantity',
        'price',
        'subtotal'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'order_id' => 'required|integer',
        'product_id' => 'required|integer',
        'product_name' => 'required|max_length[255]',
        'quantity' => 'required|integer|greater_than[0]',
        'price' => 'required|decimal|greater_than[0]',
        'subtotal' => 'required|decimal|greater_than[0]'
    ];

    protected $validationMessages = [
        'order_id' => [
            'required' => 'El ID del pedido es requerido',
            'integer' => 'El ID del pedido debe ser un número entero'
        ],
        'product_id' => [
            'required' => 'El ID del producto es requerido',
            'integer' => 'El ID del producto debe ser un número entero'
        ],
        'product_name' => [
            'required' => 'El nombre del producto es requerido',
            'max_length' => 'El nombre del producto no puede exceder 255 caracteres'
        ],
        'quantity' => [
            'required' => 'La cantidad es requerida',
            'integer' => 'La cantidad debe ser un número entero',
            'greater_than' => 'La cantidad debe ser mayor a 0'
        ],
        'price' => [
            'required' => 'El precio es requerido',
            'decimal' => 'El precio debe ser un número decimal válido',
            'greater_than' => 'El precio debe ser mayor a 0'
        ]
    ];

    protected $skipValidation = false;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['calculateTotal'];
    protected $beforeUpdate   = ['calculateTotal'];

    /**
     * Calculate total before saving
     */
    protected function calculateTotal(array $data)
    {
        if (isset($data['data']['quantity']) && isset($data['data']['price'])) {
            $data['data']['total'] = $data['data']['quantity'] * $data['data']['price'];
        }
        return $data;
    }

    /**
     * Get order items with product details
     */
    public function getOrderItems($orderId)
    {
        return $this->select('order_items.*, products.image as product_image, products.slug as product_slug')
                   ->join('products', 'products.id = order_items.product_id', 'left')
                   ->where('order_items.order_id', $orderId)
                   ->orderBy('order_items.id', 'ASC')
                   ->findAll();
    }

    /**
     * Add item to order
     */
    public function addOrderItem($orderData)
    {
        // Calculate total
        $orderData['total'] = $orderData['quantity'] * $orderData['price'];
        
        return $this->insert($orderData);
    }

    /**
     * Update item quantity
     */
    public function updateItemQuantity($itemId, $quantity)
    {
        $item = $this->find($itemId);
        if ($item) {
            $total = $quantity * $item['price'];
            return $this->update($itemId, [
                'quantity' => $quantity,
                'total' => $total
            ]);
        }
        return false;
    }

    /**
     * Remove item from order
     */
    public function removeOrderItem($itemId)
    {
        return $this->delete($itemId);
    }

    /**
     * Get order total
     */
    public function getOrderTotal($orderId)
    {
        $result = $this->selectSum('total')
                      ->where('order_id', $orderId)
                      ->get()
                      ->getRow();
        
        return $result->total ?? 0;
    }

    /**
     * Get order item count
     */
    public function getOrderItemCount($orderId)
    {
        return $this->where('order_id', $orderId)->countAllResults();
    }

    /**
     * Get best selling products
     */
    public function getBestSellingProducts($limit = 10, $startDate = null, $endDate = null)
    {
        $builder = $this->builder();
        
        if ($startDate) {
            $builder->where('order_items.created_at >=', $startDate);
        }
        if ($endDate) {
            $builder->where('order_items.created_at <=', $endDate);
        }

        return $builder->select('order_items.product_id, order_items.product_name, 
                                SUM(order_items.quantity) as total_sold,
                                SUM(order_items.total) as total_revenue,
                                products.image as product_image')
                      ->join('products', 'products.id = order_items.product_id', 'left')
                      ->groupBy('order_items.product_id')
                      ->orderBy('total_sold', 'DESC')
                      ->limit($limit)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get product sales statistics
     */
    public function getProductSalesStats($productId, $startDate = null, $endDate = null)
    {
        $builder = $this->builder();
        
        if ($startDate) {
            $builder->where('created_at >=', $startDate);
        }
        if ($endDate) {
            $builder->where('created_at <=', $endDate);
        }

        $result = $builder->select('SUM(quantity) as total_quantity, 
                                   SUM(total) as total_revenue,
                                   COUNT(*) as total_orders')
                         ->where('product_id', $productId)
                         ->get()
                         ->getRow();

        return [
            'total_quantity' => $result->total_quantity ?? 0,
            'total_revenue' => $result->total_revenue ?? 0,
            'total_orders' => $result->total_orders ?? 0
        ];
    }

    /**
     * Clear order items
     */
    public function clearOrderItems($orderId)
    {
        return $this->where('order_id', $orderId)->delete();
    }

    /**
     * Bulk add order items
     */
    public function bulkAddOrderItems($orderItems)
    {
        return $this->insertBatch($orderItems);
    }
}
