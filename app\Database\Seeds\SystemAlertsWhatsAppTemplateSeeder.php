<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class SystemAlertsWhatsAppTemplateSeeder extends Seeder
{
    public function run()
    {
        try {
            // Verificar si la tabla existe
            $tableExists = $this->db->query("
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                    AND table_name = 'whatsapp_templates'
            ")->getRowArray();

            if (!$tableExists || $tableExists['count'] == 0) {
                echo "⚠️ Tabla whatsapp_templates no existe, creándola...\n";
                
                // Crear tabla básica si no existe
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS whatsapp_templates (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        template_key VARCHAR(100) NOT NULL UNIQUE,
                        template_name VARCHAR(200) NOT NULL,
                        message_template TEXT NOT NULL,
                        description TEXT,
                        variables JSON,
                        is_mandatory TINYINT(1) DEFAULT 0,
                        is_active TINYINT(1) DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
            }

            // Plantilla para alertas del sistema
            $systemAlertTemplate = [
                'template_key' => 'system_alerts',
                'template_name' => 'Alertas del Sistema',
                'message_template' => '🚨 *ALERTAS DEL SISTEMA MRCELL*
📅 {date_time}

{alerts_content}

💻 *Panel de Administración:*
{admin_url}

🤖 _Mensaje automático del sistema_',
                'description' => 'Plantilla para enviar alertas automáticas del sistema a administradores',
                'variables' => json_encode(['date_time', 'alerts_content', 'admin_url']),
                'is_mandatory' => 1,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Verificar si ya existe
            $existing = $this->db->query("
                SELECT id FROM whatsapp_templates 
                WHERE template_key = 'system_alerts' 
                LIMIT 1
            ")->getRowArray();

            if (!$existing) {
                $this->db->table('whatsapp_templates')->insert($systemAlertTemplate);
                echo "✅ Plantilla de alertas del sistema creada\n";
            } else {
                // Actualizar plantilla existente
                $this->db->table('whatsapp_templates')
                    ->where('template_key', 'system_alerts')
                    ->update([
                        'template_name' => $systemAlertTemplate['template_name'],
                        'message_template' => $systemAlertTemplate['message_template'],
                        'description' => $systemAlertTemplate['description'],
                        'variables' => $systemAlertTemplate['variables'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                echo "✅ Plantilla de alertas del sistema actualizada\n";
            }

            // Plantillas adicionales para diferentes tipos de alertas
            $additionalTemplates = [
                [
                    'template_key' => 'low_stock_alert',
                    'template_name' => 'Alerta de Bajo Stock',
                    'message_template' => '🔴 *ALERTA: PRODUCTOS CON BAJO STOCK*

{products_list}

💻 Ver inventario: {inventory_url}

🤖 _Alerta automática del sistema_',
                    'description' => 'Alerta específica para productos con bajo stock',
                    'variables' => json_encode(['products_list', 'inventory_url']),
                    'is_mandatory' => 0,
                    'is_active' => 1
                ],
                [
                    'template_key' => 'expiration_alert',
                    'template_name' => 'Alerta de Productos por Caducar',
                    'message_template' => '⏰ *ALERTA: PRODUCTOS POR CADUCAR*

{expiration_summary}

💻 Ver productos: {products_url}

🤖 _Alerta automática del sistema_',
                    'description' => 'Alerta para productos próximos a caducar',
                    'variables' => json_encode(['expiration_summary', 'products_url']),
                    'is_mandatory' => 0,
                    'is_active' => 1
                ],
                [
                    'template_key' => 'pending_orders_alert',
                    'template_name' => 'Alerta de Pedidos Pendientes',
                    'message_template' => '📦 *ALERTA: PEDIDOS PENDIENTES*

{orders_summary}

💻 Ver pedidos: {orders_url}

🤖 _Alerta automática del sistema_',
                    'description' => 'Alerta para pedidos pendientes de procesar',
                    'variables' => json_encode(['orders_summary', 'orders_url']),
                    'is_mandatory' => 0,
                    'is_active' => 1
                ]
            ];

            foreach ($additionalTemplates as $template) {
                $template['created_at'] = date('Y-m-d H:i:s');
                $template['updated_at'] = date('Y-m-d H:i:s');

                $existing = $this->db->query("
                    SELECT id FROM whatsapp_templates 
                    WHERE template_key = ? 
                    LIMIT 1
                ", [$template['template_key']])->getRowArray();

                if (!$existing) {
                    $this->db->table('whatsapp_templates')->insert($template);
                    echo "✅ Plantilla '{$template['template_name']}' creada\n";
                } else {
                    echo "⚠️ Plantilla '{$template['template_name']}' ya existe\n";
                }
            }

            echo "\n✅ Plantillas de WhatsApp para alertas del sistema configuradas\n";

        } catch (\Exception $e) {
            echo "❌ Error configurando plantillas: " . $e->getMessage() . "\n";
        }
    }
}
