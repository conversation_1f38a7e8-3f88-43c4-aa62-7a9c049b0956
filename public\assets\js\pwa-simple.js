/**
 * Simple PWA Manager
 * Simplified version to avoid conflicts and errors
 */

class SimplePWAManager {
    constructor() {
        this.swRegistration = null;
        this.deferredPrompt = null;
        this.isInitialized = false;
        
        // Prevent multiple initializations
        if (window.simplePWAManager) {
            return window.simplePWAManager;
        }
        
        window.simplePWAManager = this;
        this.init();
    }

    async init() {
        if (this.isInitialized) {
            console.log('Simple PWA Manager: Already initialized');
            return;
        }

        console.log('Simple PWA Manager: Initializing...');
        
        try {
            // Register service worker
            await this.registerServiceWorker();
            
            // Setup install prompt (without preventing default)
            this.setupInstallPrompt();
            
            // Setup network status
            this.setupNetworkStatus();
            
            this.isInitialized = true;
            console.log('Simple PWA Manager: Initialized successfully');
            
        } catch (error) {
            console.error('Simple PWA Manager: Initialization failed:', error);
        }
    }

    async registerServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.log('Simple PWA Manager: Service Worker not supported');
            return;
        }

        try {
            console.log('Simple PWA Manager: Registering Service Worker...');
            
            // Clear any existing registrations first
            const existingRegistrations = await navigator.serviceWorker.getRegistrations();
            for (const registration of existingRegistrations) {
                console.log('Simple PWA Manager: Unregistering existing SW');
                await registration.unregister();
            }

            // Register new service worker
            const swPath = '/sw.js';
            const scope = '/';
            
            this.swRegistration = await navigator.serviceWorker.register(swPath, {
                scope: scope,
                updateViaCache: 'none' // Always check for updates
            });

            console.log('Simple PWA Manager: Service Worker registered successfully');
            console.log('Simple PWA Manager: Scope:', this.swRegistration.scope);

            // Handle updates
            this.swRegistration.addEventListener('updatefound', () => {
                console.log('Simple PWA Manager: Service Worker update found');
                const newWorker = this.swRegistration.installing;
                
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        console.log('Simple PWA Manager: New Service Worker available');
                        this.showUpdateNotification();
                    }
                });
            });

        } catch (error) {
            console.error('Simple PWA Manager: Service Worker registration failed:', error);
        }
    }

    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Simple PWA Manager: Install prompt available');
            this.deferredPrompt = e;
            
            // Don't prevent default - let browser show native prompt
            // Only prevent if you want custom UI
            // e.preventDefault();
            
            this.showInstallButton();
        });

        window.addEventListener('appinstalled', () => {
            console.log('Simple PWA Manager: App installed');
            this.deferredPrompt = null;
            this.hideInstallButton();
        });
    }

    setupNetworkStatus() {
        const updateOnlineStatus = () => {
            const status = navigator.onLine ? 'online' : 'offline';
            console.log('Simple PWA Manager: Network status:', status);
            document.body.classList.toggle('offline', !navigator.onLine);
        };

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus();
    }

    showInstallButton() {
        // Create install button if it doesn't exist
        if (!document.getElementById('pwa-install-btn')) {
            const button = document.createElement('button');
            button.id = 'pwa-install-btn';
            button.innerHTML = '<i class="fas fa-download"></i> Instalar App';
            button.className = 'btn btn-primary position-fixed';
            button.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; border-radius: 25px;';
            
            button.addEventListener('click', () => this.installApp());
            document.body.appendChild(button);
        }
    }

    hideInstallButton() {
        const button = document.getElementById('pwa-install-btn');
        if (button) {
            button.remove();
        }
    }

    async installApp() {
        if (!this.deferredPrompt) {
            console.log('Simple PWA Manager: No install prompt available');
            return;
        }

        try {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log('Simple PWA Manager: Install outcome:', outcome);
            
            this.deferredPrompt = null;
            this.hideInstallButton();
            
        } catch (error) {
            console.error('Simple PWA Manager: Install failed:', error);
        }
    }

    showUpdateNotification() {
        // Simple update notification
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div class="alert alert-info alert-dismissible position-fixed" style="top: 20px; right: 20px; z-index: 1001;">
                <strong>Actualización disponible</strong>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="window.location.reload()">
                    Actualizar
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.appendChild(notification);
    }

    // Public methods for external use
    async requestNotificationPermission() {
        if (!('Notification' in window)) {
            console.log('Simple PWA Manager: Notifications not supported');
            return false;
        }

        if (Notification.permission === 'granted') {
            return true;
        }

        if (Notification.permission !== 'denied') {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }

        return false;
    }

    showNotification(title, options = {}) {
        if (Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/MrCellCI4/assets/img/icons/icon-192x192.png',
                badge: '/MrCellCI4/assets/img/icons/icon-72x72.png',
                ...options
            });
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new SimplePWAManager();
    });
} else {
    new SimplePWAManager();
}

// Export for external use
window.SimplePWAManager = SimplePWAManager;
