<?php

// Script para probar la creación de productos y identificar el error 500
require_once 'vendor/autoload.php';

// Configurar CodeIgniter
$paths = new Config\Paths();
$bootstrap = \CodeIgniter\Boot::bootWeb($paths);
$app = $bootstrap->getApp();

echo "🧪 PROBANDO CREACIÓN DE PRODUCTO...\n\n";

try {
    // Obtener instancia de la base de datos
    $db = \Config\Database::connect();
    
    // Verificar conexión
    echo "🔗 Verificando conexión a la base de datos...\n";
    $dbTest = $db->query("SELECT 1 as test")->getRowArray();
    if (!$dbTest) {
        throw new Exception('No se puede conectar a la base de datos');
    }
    echo "✅ Conexión a la base de datos OK\n";
    
    // Obtener categorías
    echo "📂 Obteniendo categorías...\n";
    $categories = $db->query("SELECT id, name FROM categories WHERE deleted_at IS NULL LIMIT 3")->getResultArray();
    if (empty($categories)) {
        throw new Exception('No hay categorías disponibles');
    }
    echo "✅ Categorías encontradas: " . count($categories) . "\n";
    foreach ($categories as $cat) {
        echo "   - ID: {$cat['id']}, Nombre: {$cat['name']}\n";
    }
    
    // Obtener marcas
    echo "🏷️ Obteniendo marcas...\n";
    $brands = $db->query("SELECT id, name FROM brands WHERE deleted_at IS NULL LIMIT 3")->getResultArray();
    if (empty($brands)) {
        throw new Exception('No hay marcas disponibles');
    }
    echo "✅ Marcas encontradas: " . count($brands) . "\n";
    foreach ($brands as $brand) {
        echo "   - ID: {$brand['id']}, Nombre: {$brand['name']}\n";
    }
    
    // Generar UUID
    function generateUUID() {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    // Preparar datos del producto
    $testSku = 'TEST-DEBUG-' . date('YmdHis');
    $productData = [
        'uuid' => generateUUID(),
        'sku' => $testSku,
        'name' => 'Producto Debug - ' . date('Y-m-d H:i:s'),
        'slug' => 'producto-debug-' . date('ymdHis'),
        'description' => 'Producto creado para debug del error 500',
        'short_description' => 'Descripción corta debug',
        'category_id' => $categories[0]['id'],
        'brand_id' => $brands[0]['id'],
        'price_regular' => 99.99,
        'price_sale' => 79.99,
        'price_wholesale' => null,
        'cost_price' => null,
        'currency' => 'GTQ',
        'stock_quantity' => 25,
        'stock_min' => 3,
        'weight' => null,
        'dimensions' => null,
        'dimension_length' => null,
        'dimension_width' => null,
        'dimension_height' => null,
        'dimension_unit' => 'cm',
        'featured_image' => null,
        'gallery_images' => null,
        'is_active' => 1,
        'is_featured' => 0,
        'has_expiration' => 0,
        'expiration_date' => null,
        'expiration_alert_days' => null,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    echo "\n📝 Datos del producto:\n";
    echo "   - SKU: {$productData['sku']}\n";
    echo "   - Nombre: {$productData['name']}\n";
    echo "   - Categoría ID: {$productData['category_id']}\n";
    echo "   - Marca ID: {$productData['brand_id']}\n";
    echo "   - Precio: Q{$productData['price_regular']}\n";
    
    // Verificar SKU único
    echo "\n🔍 Verificando SKU único...\n";
    $skuExists = $db->query("SELECT id FROM products WHERE sku = ? AND deleted_at IS NULL", [$testSku])->getRowArray();
    if ($skuExists) {
        throw new Exception('El SKU ya existe: ' . $testSku);
    }
    echo "✅ SKU disponible\n";
    
    // Verificar estructura de la tabla
    echo "\n🔍 Verificando estructura de la tabla products...\n";
    $columns = $db->query("SHOW COLUMNS FROM products")->getResultArray();
    $requiredColumns = ['uuid', 'sku', 'name', 'category_id', 'brand_id', 'price_regular'];
    $missingColumns = [];
    
    $existingColumns = array_column($columns, 'Field');
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $existingColumns)) {
            $missingColumns[] = $col;
        }
    }
    
    if (!empty($missingColumns)) {
        throw new Exception('Columnas faltantes en la tabla products: ' . implode(', ', $missingColumns));
    }
    echo "✅ Estructura de tabla OK\n";
    
    // Intentar inserción
    echo "\n💾 Insertando producto...\n";
    
    $insertResult = $db->table('products')->insert($productData);
    
    if (!$insertResult) {
        $error = $db->error();
        echo "❌ Error al insertar:\n";
        echo "   Código: {$error['code']}\n";
        echo "   Mensaje: {$error['message']}\n";
        throw new Exception('Error de inserción: ' . $error['message']);
    }
    
    $productId = $db->insertID();
    if (!$productId) {
        throw new Exception('No se pudo obtener el ID del producto');
    }
    
    echo "✅ Producto insertado exitosamente!\n";
    echo "   ID: $productId\n";
    
    // Verificar inserción
    echo "\n🔍 Verificando producto insertado...\n";
    $product = $db->query("SELECT id, sku, name FROM products WHERE id = ?", [$productId])->getRowArray();
    
    if ($product) {
        echo "✅ Producto verificado:\n";
        echo "   ID: {$product['id']}\n";
        echo "   SKU: {$product['sku']}\n";
        echo "   Nombre: {$product['name']}\n";
    } else {
        echo "❌ No se pudo verificar el producto\n";
    }
    
    echo "\n🎉 PRUEBA COMPLETADA EXITOSAMENTE\n";
    echo "La inserción básica de productos funciona correctamente.\n";
    echo "El error 500 debe estar en otra parte del proceso.\n";
    
} catch (Exception $e) {
    echo "\n❌ ERROR: {$e->getMessage()}\n";
    echo "Archivo: {$e->getFile()}:{$e->getLine()}\n";
    
    // Información adicional de la BD
    try {
        if (isset($db)) {
            $dbError = $db->error();
            if (!empty($dbError['message'])) {
                echo "Error de BD: {$dbError['message']}\n";
            }
        }
    } catch (Exception $dbE) {
        echo "No se pudo obtener info adicional de BD\n";
    }
}

echo "\n";
