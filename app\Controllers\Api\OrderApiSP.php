<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class OrderApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener pedidos con filtros usando SP
     */
    public function index()
    {
        try {
            $status = $this->request->getGet('status');
            $paymentMethod = $this->request->getGet('payment_method');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');
            $customerSearch = $this->request->getGet('customer');
            $minAmount = $this->request->getGet('min_amount');
            $maxAmount = $this->request->getGet('max_amount');
            $sortBy = $this->request->getGet('sort_by') ?? 'created';
            $sortOrder = $this->request->getGet('sort_order') ?? 'DESC';
            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);
            $offset = ($page - 1) * $limit;

            $query = $this->db->query("CALL sp_get_orders(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $status, $paymentMethod, $dateFrom, $dateTo, $customerSearch,
                $minAmount, $maxAmount, $sortBy, $sortOrder, $limit, $offset
            ]);

            $orders = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'orders' => $orders,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($orders)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::index: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener detalles de pedido usando SP
     */
    public function show($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('ID de pedido requerido', 400);
            }

            // Obtener información del pedido
            $orderQuery = $this->db->query("
                SELECT
                    o.*,
                    o.customer_name,
                    o.shipping_address
                FROM orders o
                WHERE o.id = ?
            ", [(int) $id]);

            $order = $orderQuery->getRowArray();

            // Obtener items del pedido
            $itemsQuery = $this->db->query("
                SELECT
                    oi.*,
                    p.name as product_name,
                    p.sku as product_sku,
                    p.featured_image as product_image,
                    (oi.quantity * oi.price) as line_total
                FROM order_items oi
                INNER JOIN products p ON p.id = oi.product_id
                WHERE oi.order_id = ?
                ORDER BY oi.id
            ", [(int) $id]);

            $items = $itemsQuery->getResultArray();

            if (!$order) {
                return $this->failNotFound('Pedido no encontrado');
            }

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'order' => $order,
                    'items' => $items
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::show: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Actualizar estado de pedido usando SP
     */
    public function updateStatus($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('ID de pedido requerido', 400);
            }

            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['status'])) {
                return $this->fail('Estado requerido', 400);
            }

            $validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($json['status'], $validStatuses)) {
                return $this->fail('Estado inválido', 400);
            }

            $query = $this->db->query("CALL sp_update_order_status(?, ?, ?, @result)", [
                (int) $id,
                $json['status'],
                $json['notes'] ?? ''
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result)
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::updateStatus: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Estadísticas de pedidos usando consultas directas
     */
    public function stats()
    {
        try {
            $dateFrom = $this->request->getGet('date_from') ?? date('Y-m-d', strtotime('-30 days'));
            $dateTo = $this->request->getGet('date_to') ?? date('Y-m-d');

            // Estadísticas generales
            $generalQuery = $this->db->query("
                SELECT
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
                    COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
                    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
                    COALESCE(SUM(total), 0) as total_revenue,
                    COALESCE(AVG(total), 0) as average_order_value,
                    COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments
                FROM orders
                WHERE DATE(created_at) BETWEEN ? AND ?
            ", [$dateFrom, $dateTo]);

            $generalStats = $generalQuery->getRowArray();

            // Estadísticas por método de pago
            $paymentQuery = $this->db->query("
                SELECT
                    payment_method,
                    COUNT(*) as order_count,
                    COALESCE(SUM(total), 0) as total_amount
                FROM orders
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY payment_method
                ORDER BY order_count DESC
            ", [$dateFrom, $dateTo]);

            $paymentStats = $paymentQuery->getResultArray();

            // Pedidos por día
            $dailyQuery = $this->db->query("
                SELECT
                    DATE(created_at) as order_date,
                    COUNT(*) as order_count,
                    COALESCE(SUM(total), 0) as daily_revenue
                FROM orders
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY order_date DESC
                LIMIT 30
            ", [$dateFrom, $dateTo]);

            $dailyStats = $dailyQuery->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'general' => $generalStats,
                    'by_payment_method' => $paymentStats,
                    'daily' => $dailyStats,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::stats: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Buscar pedidos por número o cliente
     */
    public function search()
    {
        try {
            $query = $this->request->getGet('q');
            $limit = (int) ($this->request->getGet('limit') ?? 10);
            
            if (!$query) {
                return $this->fail('Parámetro de búsqueda requerido', 400);
            }

            $dbQuery = $this->db->query("CALL sp_get_orders(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                null, null, null, null, $query, null, null, 'created', 'DESC', $limit, 0
            ]);

            $orders = $dbQuery->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::search: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Pedidos por estado
     */
    public function byStatus($status = null)
    {
        try {
            if (!$status) {
                return $this->fail('Estado requerido', 400);
            }

            $limit = (int) ($this->request->getGet('limit') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);
            $offset = ($page - 1) * $limit;

            $query = $this->db->query("CALL sp_get_orders(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $status, null, null, null, null, null, null, 'created', 'DESC', $limit, $offset
            ]);

            $orders = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'orders' => $orders,
                    'status' => $status,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($orders)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::byStatus: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Pedidos recientes
     */
    public function recent()
    {
        try {
            $limit = (int) ($this->request->getGet('limit') ?? 10);

            $query = $this->db->query("CALL sp_get_orders(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                null, null, null, null, null, null, null, 'created', 'DESC', $limit, 0
            ]);

            $orders = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::recent: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Exportar pedidos (básico)
     */
    public function export()
    {
        try {
            $format = $this->request->getGet('format') ?? 'json';
            $status = $this->request->getGet('status');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $query = $this->db->query("CALL sp_get_orders(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $status, null, $dateFrom, $dateTo, null, null, null, 'created', 'DESC', null, null
            ]);

            $orders = $query->getResultArray();

            if ($format === 'csv') {
                $this->response->setHeader('Content-Type', 'text/csv');
                $this->response->setHeader('Content-Disposition', 'attachment; filename="orders_export.csv"');
                
                $csv = "ID,Número,Cliente,Email,Total,Estado,Método Pago,Fecha\n";
                foreach ($orders as $order) {
                    $csv .= sprintf(
                        "%s,%s,%s,%s,%s,%s,%s,%s\n",
                        $order['id'],
                        $order['order_number'],
                        $order['customer_name'],
                        $order['customer_email'],
                        $order['total_amount'],
                        $order['status'],
                        $order['payment_method'],
                        $order['created_at']
                    );
                }
                
                return $this->response->setBody($csv);
            }

            return $this->respond([
                'status' => 'success',
                'data' => $orders,
                'export_info' => [
                    'total_records' => count($orders),
                    'format' => $format,
                    'generated_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en OrderApiSP::export: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }
}
