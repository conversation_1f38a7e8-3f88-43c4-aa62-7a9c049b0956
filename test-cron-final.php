<?php

// Script final para probar el sistema de cron con la nueva configuración
echo "🚀 PRUEBA FINAL DEL SISTEMA DE CRON\n";
echo "=====================================\n\n";

try {
    // Configuración de la base de datos
    $host = 'localhost';
    $dbname = 'mrcell_db';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Conectado a la base de datos\n\n";
    
    // 1. Configurar el número de grupo
    echo "⚙️ PASO 1: Configurando número de grupo...\n";
    
    $stmt = $pdo->prepare("
        INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
        VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
            setting_value = '120363416393766854',
            updated_at = NOW()
    ");
    
    $stmt->execute();
    echo "✅ Número de grupo configurado: 120363416393766854\n\n";
    
    // 2. Verificar productos con alertas
    echo "🔍 PASO 2: Verificando productos que generarán alertas...\n";
    
    // Productos con bajo stock
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND stock_quantity > 0
            AND is_active = 1 
            AND deleted_at IS NULL
    ");
    $lowStock = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📦 Productos con bajo stock: {$lowStock['count']}\n";
    
    // Productos próximos a caducar
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND (
                expiration_date <= CURDATE() 
                OR DATEDIFF(expiration_date, CURDATE()) <= expiration_alert_days
            )
    ");
    $expiring = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "⏰ Productos próximos a caducar: {$expiring['count']}\n";
    
    // Pedidos pendientes
    $stmt = $pdo->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending,
            COUNT(CASE WHEN status = 'shipped' AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 END) as shipped
        FROM orders
    ");
    $orders = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📋 Pedidos pendientes (>24h): {$orders['pending']}\n";
    echo "🚚 Pedidos enviados (>3 días): {$orders['shipped']}\n\n";
    
    // 3. Simular mensaje de alerta
    echo "📝 PASO 3: Simulando mensaje de alerta...\n";
    
    $alertMessage = "🚨 *ALERTAS DEL SISTEMA MRCELL*\n";
    $alertMessage .= "📅 " . date('d/m/Y H:i:s') . "\n\n";
    
    if ($lowStock['count'] > 0) {
        $alertMessage .= "🔴 *ALERTA: PRODUCTOS CON BAJO STOCK*\n\n";
        $alertMessage .= "Se encontraron {$lowStock['count']} productos con stock bajo o agotado.\n\n";
        $alertMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
    }
    
    if ($expiring['count'] > 0) {
        $alertMessage .= "⏰ *ALERTA: PRODUCTOS POR CADUCAR*\n\n";
        $alertMessage .= "Se encontraron {$expiring['count']} productos próximos a caducar o caducados.\n\n";
        $alertMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
    }
    
    if ($orders['pending'] > 0 || $orders['shipped'] > 0) {
        $alertMessage .= "📦 *ALERTA: PEDIDOS PENDIENTES*\n\n";
        if ($orders['pending'] > 0) {
            $alertMessage .= "🔴 *PEDIDOS PENDIENTES (>24h):* {$orders['pending']}\n";
        }
        if ($orders['shipped'] > 0) {
            $alertMessage .= "🚚 *PEDIDOS ENVIADOS (>3 días):* {$orders['shipped']}\n";
        }
        $alertMessage .= "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
    }
    
    $alertMessage .= "💻 *Panel de Administración:*\n";
    $alertMessage .= "http://localhost:8080/admin/dashboard\n\n";
    $alertMessage .= "🤖 _Mensaje automático del sistema_";
    
    echo "📱 Destinatario: 120363416393766854\n";
    echo "📏 Longitud del mensaje: " . strlen($alertMessage) . " caracteres\n";
    echo "📄 Vista previa del mensaje:\n";
    echo "─────────────────────────────────────\n";
    echo $alertMessage;
    echo "\n─────────────────────────────────────\n\n";
    
    // 4. Verificar configuración final
    echo "✅ PASO 4: Verificación final...\n";
    
    $stmt = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'whatsapp_alerts_group'");
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config && $config['setting_value'] === '120363416393766854') {
        echo "✅ Configuración correcta en la base de datos\n";
        echo "✅ Número de grupo: {$config['setting_value']}\n";
        echo "✅ Sistema listo para enviar alertas\n\n";
        
        echo "🎉 CONFIGURACIÓN COMPLETADA EXITOSAMENTE\n";
        echo "═══════════════════════════════════════════\n\n";
        echo "📋 RESUMEN:\n";
        echo "• Número de grupo configurado: 120363416393766854\n";
        echo "• Editable desde: /admin/settings?tab=notifications\n";
        echo "• Alertas programadas cada 12 horas\n";
        echo "• Sistema integrado con WhatsApp existente\n\n";
        
        echo "🔄 PRÓXIMOS PASOS:\n";
        echo "1. Visita /admin/settings?tab=notifications para verificar la configuración\n";
        echo "2. Ejecuta: php spark cron:alerts --test --force (para probar)\n";
        echo "3. Ejecuta: php spark cron:alerts --force (para enviar alertas reales)\n";
        echo "4. Las tareas programadas se ejecutarán automáticamente cada 12 horas\n";
        
    } else {
        echo "❌ Error en la configuración\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "\n";
    echo "\n💡 Ajusta la configuración de la base de datos en este script\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";
