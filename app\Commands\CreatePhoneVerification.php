<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CreatePhoneVerification extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:create-phone-verification';
    protected $description = 'Crear tabla de verificación de teléfono y columnas relacionadas';

    public function run(array $params)
    {
        CLI::write('=== CREANDO SISTEMA DE VERIFICACIÓN DE TELÉFONO ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();

        try {
            // 1. Crear tabla phone_verifications
            CLI::write('🔧 Creando tabla phone_verifications...', 'white');
            
            $createTableSQL = "
            CREATE TABLE IF NOT EXISTS phone_verifications (
                id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                user_id INT(11) UNSIGNED NOT NULL,
                phone VARCHAR(20) NOT NULL COMMENT 'Número de teléfono a verificar',
                verification_code VARCHAR(6) NOT NULL COMMENT 'Código de verificación de 6 dígitos',
                is_verified TINYINT(1) DEFAULT 0 COMMENT '1 = verificado, 0 = pendiente',
                attempts INT(3) DEFAULT 0 COMMENT 'Número de intentos de verificación',
                expires_at DATETIME NOT NULL COMMENT 'Fecha de expiración del código',
                verified_at DATETIME NULL COMMENT 'Fecha de verificación exitosa',
                created_at DATETIME NULL,
                updated_at DATETIME NULL,
                PRIMARY KEY (id),
                KEY idx_user_id (user_id),
                KEY idx_phone (phone),
                KEY idx_verification_code (verification_code),
                KEY idx_expires_at (expires_at),
                KEY idx_created_at (created_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";

            $db->query($createTableSQL);
            CLI::write('   ✅ Tabla phone_verifications creada', 'green');

            // 2. Agregar columnas a la tabla users
            CLI::write('🔧 Agregando columnas a tabla users...', 'white');
            
            // Verificar si las columnas ya existen
            $columns = $db->query("SHOW COLUMNS FROM users LIKE 'phone_verified'")->getResultArray();
            if (empty($columns)) {
                $db->query("ALTER TABLE users ADD COLUMN phone_verified TINYINT(1) DEFAULT 0 COMMENT '1 = teléfono verificado, 0 = no verificado' AFTER phone");
                CLI::write('   ✅ Columna phone_verified agregada', 'green');
            } else {
                CLI::write('   ⚠️ Columna phone_verified ya existe', 'yellow');
            }

            $columns = $db->query("SHOW COLUMNS FROM users LIKE 'whatsapp_notifications_enabled'")->getResultArray();
            if (empty($columns)) {
                $db->query("ALTER TABLE users ADD COLUMN whatsapp_notifications_enabled TINYINT(1) DEFAULT 1 COMMENT '1 = notificaciones habilitadas, 0 = deshabilitadas' AFTER phone_verified");
                CLI::write('   ✅ Columna whatsapp_notifications_enabled agregada', 'green');
            } else {
                CLI::write('   ⚠️ Columna whatsapp_notifications_enabled ya existe', 'yellow');
            }

            // 3. Verificar estructura final
            CLI::newLine();
            CLI::write('📋 Verificando estructura de phone_verifications:', 'cyan');
            $phoneVerificationColumns = $db->query("SHOW COLUMNS FROM phone_verifications")->getResultArray();
            foreach ($phoneVerificationColumns as $column) {
                CLI::write("   - {$column['Field']} ({$column['Type']})", 'white');
            }

            CLI::newLine();
            CLI::write('📋 Verificando nuevas columnas en users:', 'cyan');
            $userColumns = $db->query("SHOW COLUMNS FROM users WHERE Field IN ('phone_verified', 'whatsapp_notifications_enabled')")->getResultArray();
            foreach ($userColumns as $column) {
                CLI::write("   - {$column['Field']} ({$column['Type']}) - Default: {$column['Default']}", 'white');
            }

            CLI::newLine();
            CLI::write('✅ Sistema de verificación de teléfono creado exitosamente', 'green');

        } catch (\Exception $e) {
            CLI::error('❌ Error creando sistema de verificación: ' . $e->getMessage());
            return;
        }

        CLI::newLine();
        CLI::write('=== CREACIÓN COMPLETADA ===', 'yellow');
    }
}
