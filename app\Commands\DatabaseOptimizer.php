<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\Database;

class DatabaseOptimizer extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:optimize';
    protected $description = 'Optimiza la base de datos, índices y consultas';
    protected $usage       = 'db:optimize [options]';
    protected $arguments   = [];
    protected $options     = [
        '--analyze'  => 'Analizar consultas lentas',
        '--indexes'  => 'Optimizar índices',
        '--tables'   => 'Optimizar tablas',
        '--cache'    => 'Limpiar cache de consultas',
        '--all'      => 'Ejecutar todas las optimizaciones'
    ];

    public function run(array $params)
    {
        $db = Database::connect();
        
        CLI::write('=== Optimizador de Base de Datos ===', 'yellow');
        CLI::newLine();

        $runAll = isset($params['all']);

        if ($runAll || isset($params['analyze'])) {
            $this->analyzeSlowQueries($db);
        }

        if ($runAll || isset($params['indexes'])) {
            $this->optimizeIndexes($db);
        }

        if ($runAll || isset($params['tables'])) {
            $this->optimizeTables($db);
        }

        if ($runAll || isset($params['cache'])) {
            $this->clearQueryCache($db);
        }

        if (!$runAll && !isset($params['analyze']) && !isset($params['indexes']) && 
            !isset($params['tables']) && !isset($params['cache'])) {
            $this->showUsage();
        }

        CLI::newLine();
        CLI::write('=== Optimización Completada ===', 'green');
    }

    private function analyzeSlowQueries($db): void
    {
        CLI::write('Analizando consultas lentas...', 'cyan');

        try {
            // Habilitar log de consultas lentas si no está habilitado
            $db->query("SET GLOBAL slow_query_log = 'ON'");
            $db->query("SET GLOBAL long_query_time = 1"); // 1 segundo

            // Obtener estadísticas de consultas
            $stats = $db->query("SHOW GLOBAL STATUS LIKE 'Slow_queries'")->getRowArray();
            CLI::write("  - Consultas lentas registradas: " . ($stats['Value'] ?? 'N/A'));

            // Analizar tablas más consultadas
            $this->analyzeTableUsage($db);

            // Sugerir índices faltantes
            $this->suggestMissingIndexes($db);

        } catch (\Exception $e) {
            CLI::write("  - Error analizando consultas: " . $e->getMessage(), 'red');
        }
    }

    private function analyzeTableUsage($db): void
    {
        CLI::write('  Analizando uso de tablas...', 'yellow');

        try {
            $query = "
                SELECT 
                    table_name,
                    table_rows,
                    data_length,
                    index_length,
                    (data_length + index_length) as total_size
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY total_size DESC
                LIMIT 10
            ";

            $tables = $db->query($query)->getResultArray();

            CLI::write('    Top 10 tablas por tamaño:');
            foreach ($tables as $table) {
                $size = $this->formatBytes($table['total_size']);
                CLI::write("      - {$table['table_name']}: {$table['table_rows']} filas, {$size}");
            }

        } catch (\Exception $e) {
            CLI::write("    Error: " . $e->getMessage(), 'red');
        }
    }

    private function suggestMissingIndexes($db): void
    {
        CLI::write('  Sugiriendo índices faltantes...', 'yellow');

        $suggestions = [
            'coupons' => [
                'idx_coupons_active_dates' => ['is_active', 'valid_from', 'valid_until'],
                'idx_coupons_code_active' => ['code', 'is_active']
            ],
            'coupon_usage' => [
                'idx_coupon_usage_user_date' => ['user_id', 'used_at'],
                'idx_coupon_usage_coupon_date' => ['coupon_id', 'used_at']
            ],
            'shipments' => [
                'idx_shipments_tracking' => ['tracking_number', 'company_id'],
                'idx_shipments_status_date' => ['status', 'created_at']
            ],
            'products' => [
                'idx_products_active_category' => ['is_active', 'category_id'],
                'idx_products_price_active' => ['price', 'is_active']
            ],
            'inventory' => [
                'idx_inventory_product_warehouse' => ['product_id', 'warehouse_id'],
                'idx_inventory_quantity' => ['quantity']
            ]
        ];

        foreach ($suggestions as $table => $indexes) {
            if ($this->tableExists($db, $table)) {
                CLI::write("    Tabla: {$table}");
                foreach ($indexes as $indexName => $columns) {
                    if (!$this->indexExists($db, $table, $indexName)) {
                        $columnList = implode(', ', $columns);
                        CLI::write("      - Sugerido: CREATE INDEX {$indexName} ON {$table} ({$columnList})", 'yellow');
                        
                        // Crear el índice automáticamente
                        try {
                            $db->query("CREATE INDEX {$indexName} ON {$table} ({$columnList})");
                            CLI::write("        ✓ Índice creado exitosamente", 'green');
                        } catch (\Exception $e) {
                            CLI::write("        ✗ Error creando índice: " . $e->getMessage(), 'red');
                        }
                    }
                }
            }
        }
    }

    private function optimizeIndexes($db): void
    {
        CLI::write('Optimizando índices...', 'cyan');

        try {
            // Obtener todas las tablas
            $tables = $db->query("SHOW TABLES")->getResultArray();
            $tableKey = array_keys($tables[0])[0];

            foreach ($tables as $table) {
                $tableName = $table[$tableKey];
                
                try {
                    // Analizar tabla
                    $db->query("ANALYZE TABLE {$tableName}");
                    
                    // Verificar índices duplicados o innecesarios
                    $this->checkDuplicateIndexes($db, $tableName);
                    
                    CLI::write("  - Tabla {$tableName} analizada", 'green');
                    
                } catch (\Exception $e) {
                    CLI::write("  - Error en tabla {$tableName}: " . $e->getMessage(), 'red');
                }
            }

        } catch (\Exception $e) {
            CLI::write("  - Error optimizando índices: " . $e->getMessage(), 'red');
        }
    }

    private function checkDuplicateIndexes($db, string $tableName): void
    {
        try {
            $indexes = $db->query("SHOW INDEX FROM {$tableName}")->getResultArray();
            
            $indexGroups = [];
            foreach ($indexes as $index) {
                $key = $index['Key_name'];
                if (!isset($indexGroups[$key])) {
                    $indexGroups[$key] = [];
                }
                $indexGroups[$key][] = $index['Column_name'];
            }

            // Buscar índices duplicados (mismas columnas)
            $columnSets = [];
            foreach ($indexGroups as $indexName => $columns) {
                if ($indexName === 'PRIMARY') continue;
                
                $columnSet = implode(',', $columns);
                if (isset($columnSets[$columnSet])) {
                    CLI::write("    ⚠ Posible índice duplicado en {$tableName}: {$indexName} y {$columnSets[$columnSet]}", 'yellow');
                } else {
                    $columnSets[$columnSet] = $indexName;
                }
            }

        } catch (\Exception $e) {
            // Ignorar errores de índices
        }
    }

    private function optimizeTables($db): void
    {
        CLI::write('Optimizando tablas...', 'cyan');

        try {
            $tables = $db->query("SHOW TABLES")->getResultArray();
            $tableKey = array_keys($tables[0])[0];

            foreach ($tables as $table) {
                $tableName = $table[$tableKey];
                
                try {
                    // Optimizar tabla
                    $result = $db->query("OPTIMIZE TABLE {$tableName}")->getRowArray();
                    
                    if ($result && isset($result['Msg_text'])) {
                        $status = $result['Msg_text'] === 'OK' ? 'green' : 'yellow';
                        CLI::write("  - {$tableName}: {$result['Msg_text']}", $status);
                    } else {
                        CLI::write("  - {$tableName}: Optimizada", 'green');
                    }
                    
                } catch (\Exception $e) {
                    CLI::write("  - Error en {$tableName}: " . $e->getMessage(), 'red');
                }
            }

        } catch (\Exception $e) {
            CLI::write("  - Error optimizando tablas: " . $e->getMessage(), 'red');
        }
    }

    private function clearQueryCache($db): void
    {
        CLI::write('Limpiando cache de consultas...', 'cyan');

        try {
            // Limpiar cache de consultas de MySQL
            $db->query("RESET QUERY CACHE");
            CLI::write("  - Cache de consultas MySQL limpiado", 'green');

            // Limpiar cache de CodeIgniter
            $cache = \Config\Services::cache();
            $cache->clean();
            CLI::write("  - Cache de CodeIgniter limpiado", 'green');

            // Limpiar cache específico de optimizaciones
            $cacheKeys = [
                'available_coupons_*',
                'coupon_stats_*',
                'tracking_*',
                'shipping_costs_*',
                'kpi_metrics_*',
                'inventory_status_*'
            ];

            foreach ($cacheKeys as $pattern) {
                $cache->deleteMatching($pattern);
            }
            CLI::write("  - Cache de consultas optimizadas limpiado", 'green');

        } catch (\Exception $e) {
            CLI::write("  - Error limpiando cache: " . $e->getMessage(), 'red');
        }
    }

    private function tableExists($db, string $tableName): bool
    {
        try {
            $result = $db->query("SHOW TABLES LIKE '{$tableName}'")->getRowArray();
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    private function indexExists($db, string $tableName, string $indexName): bool
    {
        try {
            $result = $db->query("SHOW INDEX FROM {$tableName} WHERE Key_name = '{$indexName}'")->getRowArray();
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    private function showUsage(): void
    {
        CLI::write('Uso: php spark db:optimize [opciones]', 'yellow');
        CLI::write('');
        CLI::write('Opciones disponibles:');
        CLI::write('  --analyze   Analizar consultas lentas y uso de tablas');
        CLI::write('  --indexes   Optimizar y sugerir índices');
        CLI::write('  --tables    Optimizar todas las tablas');
        CLI::write('  --cache     Limpiar cache de consultas');
        CLI::write('  --all       Ejecutar todas las optimizaciones');
        CLI::write('');
        CLI::write('Ejemplos:');
        CLI::write('  php spark db:optimize --all');
        CLI::write('  php spark db:optimize --indexes --tables');
        CLI::write('  php spark db:optimize --analyze');
    }
}
