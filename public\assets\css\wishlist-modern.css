/**
 * Estilos para Wishlist Moderno
 * MrCell Guatemala - Compatible con todos los navegadores
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

/* ===== WISHLIST CARD ===== */
.wishlist-card {
    position: relative;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.wishlist-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

/* ===== PRIORITY BADGE ===== */
.wishlist-priority {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 2;
    color: white;
}

.priority-high {
    background: #dc3545;
}

.priority-medium {
    background: #ffc107;
    color: #333;
}

.priority-low {
    background: #28a745;
}

/* ===== PRODUCT IMAGE ===== */
.wishlist-card .product-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.wishlist-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wishlist-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wishlist-card:hover .product-overlay {
    opacity: 1;
}

/* ===== DISCOUNT BADGE ===== */
.wishlist-card .discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

/* ===== PRODUCT INFO ===== */
.wishlist-card .product-info {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.wishlist-card .product-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.3;
    height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.wishlist-card .product-name a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.wishlist-card .product-name a:hover {
    color: #667eea;
}

.product-meta {
    margin-bottom: 8px;
}

.product-price {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.price {
    font-size: 18px;
    font-weight: 700;
    color: #667eea;
}

.price-sale {
    font-size: 18px;
    font-weight: 700;
    color: #dc3545;
}

.price-regular {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    font-size: 14px;
    color: #ffc107;
}

.rating-count {
    font-size: 12px;
    color: #666;
}

.product-stock {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 10px;
    width: fit-content;
}

.product-stock.in-stock {
    background: #d4edda;
    color: #155724;
}

.product-stock.out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* ===== WISHLIST NOTES ===== */
.wishlist-notes {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 6px;
    margin-bottom: 10px;
    border-left: 3px solid #667eea;
}

.wishlist-notes small {
    color: #666;
    line-height: 1.4;
}

/* ===== WISHLIST ACTIONS ===== */
.wishlist-actions {
    display: flex;
    gap: 8px;
    margin-top: auto;
    margin-bottom: 10px;
}

.wishlist-actions .btn {
    border-radius: 6px;
    font-size: 13px;
    padding: 8px 12px;
}

/* ===== WISHLIST DATE ===== */
.wishlist-date {
    margin-top: auto;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .wishlist-card .product-image {
        height: 150px;
    }
    
    .wishlist-card .product-info {
        padding: 12px;
    }
    
    .wishlist-card .product-name {
        font-size: 14px;
    }
    
    .price,
    .price-sale {
        font-size: 16px;
    }
    
    .wishlist-actions {
        flex-direction: column;
    }
    
    .wishlist-actions .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .wishlist-card .product-image {
        height: 120px;
    }
    
    .wishlist-card .product-info {
        padding: 10px;
    }
    
    .wishlist-card .product-name {
        font-size: 13px;
        height: auto;
        -webkit-line-clamp: 3;
    }
    
    .price,
    .price-sale {
        font-size: 14px;
    }
}

/* ===== ANIMACIONES ===== */
.wishlist-card {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== BOTONES DE WISHLIST GLOBALES ===== */
.wishlist-btn {
    position: relative;
    background: transparent;
    border: 2px solid #e0e0e0;
    color: #666;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.wishlist-btn:hover {
    border-color: #667eea;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.wishlist-btn.active {
    border-color: #dc3545;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.wishlist-btn.active:hover {
    background: rgba(220, 53, 69, 0.2);
}

.wishlist-btn i {
    transition: transform 0.3s ease;
}

.wishlist-btn:hover i {
    transform: scale(1.1);
}

/* ===== CONTADOR DE WISHLIST ===== */
.wishlist-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* ===== UTILIDADES ===== */
.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

/* ===== ACCESIBILIDAD ===== */
.wishlist-btn:focus,
.wishlist-card:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
