/**
 * Sistema de Wishlist Moderno para MrCell Guatemala
 * Compatible con cPanel hosting - Sin dependencias externas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class WishlistManager {
    constructor() {
        this.apiUrl = window.MrCellWishlist?.apiUrl || '/api/wishlist';
        this.baseUrl = window.MrCellWishlist?.baseUrl || '';
        this.userId = window.MrCellWishlist?.userId || null;
        
        this.currentPage = 1;
        this.currentFilters = {
            priority: '',
            sort_by: 'created_at',
            sort_order: 'DESC'
        };
        
        this.isLoading = false;
        this.cache = new Map();
        this.cacheTTL = 5 * 60 * 1000; // 5 minutos
        
        this.init();
    }
    
    /**
     * Inicializar el sistema
     */
    init() {
        this.bindEvents();
        this.loadWishlist();
        this.updateGlobalWishlistCount();
    }
    
    /**
     * Vincular eventos
     */
    bindEvents() {
        // Filtros
        const priorityFilter = document.getElementById('priority-filter');
        const sortFilter = document.getElementById('sort-filter');
        const orderFilter = document.getElementById('order-filter');
        
        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                this.currentFilters.priority = priorityFilter.value;
                this.currentPage = 1;
                this.loadWishlist();
            });
        }
        
        if (sortFilter) {
            sortFilter.addEventListener('change', () => {
                this.currentFilters.sort_by = sortFilter.value;
                this.currentPage = 1;
                this.loadWishlist();
            });
        }
        
        if (orderFilter) {
            orderFilter.addEventListener('change', () => {
                this.currentFilters.sort_order = orderFilter.value;
                this.currentPage = 1;
                this.loadWishlist();
            });
        }
        
        // Eventos globales para botones de wishlist
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('wishlist-btn') || 
                e.target.closest('.wishlist-btn')) {
                e.preventDefault();
                const btn = e.target.classList.contains('wishlist-btn') ? 
                           e.target : e.target.closest('.wishlist-btn');
                const productId = btn.dataset.productId;
                
                if (productId) {
                    this.toggleWishlist(parseInt(productId), btn);
                }
            }
        });
    }
    
    /**
     * Cargar wishlist
     */
    async loadWishlist() {
        if (this.isLoading) return;
        
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 12,
                ...this.currentFilters
            });
            
            // Verificar cache
            const cacheKey = `wishlist_${params.toString()}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                this.displayWishlist(cached);
                this.hideLoading();
                return;
            }
            
            const response = await fetch(`${this.apiUrl}?${params}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.setCache(cacheKey, data.data);
                this.displayWishlist(data.data);
                this.updateStats(data.data.stats);
            } else {
                this.showError('Error al cargar la lista de deseos');
            }
            
        } catch (error) {
            console.error('Error loading wishlist:', error);
            this.showError('Error de conexión');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Mostrar wishlist
     */
    displayWishlist(data) {
        const container = document.getElementById('wishlist-items');
        if (!container) return;
        
        if (data.items.length === 0) {
            container.innerHTML = this.renderEmptyWishlist();
            return;
        }
        
        let html = '<div class="row">';
        data.items.forEach(item => {
            html += this.renderWishlistItem(item);
        });
        html += '</div>';
        
        container.innerHTML = html;
        
        // Renderizar paginación
        this.renderPagination(data.pagination);
        
        // Actualizar contador de productos en stock
        this.updateInStockCount(data.items);
    }
    
    /**
     * Renderizar item de wishlist
     */
    renderWishlistItem(item) {
        const product = item.product;
        const price = item.price;
        const stock = item.stock;
        const wishlistData = item.wishlist_data;
        
        const discountBadge = price.has_discount ? 
            `<span class="discount-badge">-${price.discount_percentage}%</span>` : '';
        
        const priceHtml = price.has_discount ? 
            `<span class="price-sale">Q${price.final.toFixed(2)}</span>
             <span class="price-regular">Q${price.regular.toFixed(2)}</span>` :
            `<span class="price">Q${price.final.toFixed(2)}</span>`;
        
        const stockClass = stock.in_stock ? 'in-stock' : 'out-of-stock';
        const stockText = stock.in_stock ? 'En stock' : 'Agotado';
        
        const priorityClass = `priority-${wishlistData.priority}`;
        const priorityText = {
            'high': 'Alta',
            'medium': 'Media',
            'low': 'Baja'
        }[wishlistData.priority] || 'Media';
        
        const alertIcon = wishlistData.price_alert_triggered ? 
            '<i class="fas fa-bell text-warning" title="¡Precio alcanzado!"></i>' : '';
        
        return `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="wishlist-card">
                    ${discountBadge}
                    <div class="wishlist-priority ${priorityClass}">
                        ${priorityText}
                    </div>
                    
                    <div class="product-image">
                        <img src="${product.image || this.baseUrl + 'assets/images/no-image.jpg'}" 
                             alt="${product.name}" loading="lazy">
                        <div class="product-overlay">
                            <a href="${product.url}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i> Ver
                            </a>
                            <button class="btn btn-outline-light btn-sm" 
                                    onclick="window.wishlistManager.editWishlistItem(${product.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="product-info">
                        <h5 class="product-name">
                            <a href="${product.url}">${product.name}</a>
                        </h5>
                        
                        <div class="product-meta">
                            <small class="text-muted">
                                ${product.category} • ${product.brand}
                            </small>
                        </div>
                        
                        <div class="product-price">
                            ${priceHtml}
                            ${alertIcon}
                        </div>
                        
                        <div class="product-rating">
                            ${this.renderStars(item.rating.average)}
                            <span class="rating-count">(${item.rating.count})</span>
                        </div>
                        
                        <div class="product-stock ${stockClass}">
                            ${stockText}
                        </div>
                        
                        ${wishlistData.notes ? `
                            <div class="wishlist-notes">
                                <small><i class="fas fa-sticky-note"></i> ${wishlistData.notes}</small>
                            </div>
                        ` : ''}
                        
                        <div class="wishlist-actions">
                            <button class="btn btn-primary btn-sm flex-fill me-2" 
                                    onclick="window.addToCart(${product.id})"
                                    ${!stock.in_stock ? 'disabled' : ''}>
                                <i class="fas fa-shopping-cart"></i>
                                ${stock.in_stock ? 'Agregar al Carrito' : 'Agotado'}
                            </button>
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="window.wishlistManager.removeFromWishlist(${product.id})"
                                    title="Eliminar de lista de deseos">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        
                        <div class="wishlist-date">
                            <small class="text-muted">
                                Agregado: ${this.formatDate(wishlistData.added_at)}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Renderizar wishlist vacía
     */
    renderEmptyWishlist() {
        return `
            <div class="empty-wishlist">
                <i class="fas fa-heart-broken"></i>
                <h3>Tu lista de deseos está vacía</h3>
                <p>Explora nuestros productos y agrega tus favoritos para encontrarlos fácilmente después.</p>
                <a href="${this.baseUrl}tienda" class="btn-continue-shopping">
                    <i class="fas fa-shopping-bag me-2"></i>
                    Explorar Productos
                </a>
            </div>
        `;
    }
    
    /**
     * Renderizar estrellas de rating
     */
    renderStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            } else {
                stars += '<i class="far fa-star"></i>';
            }
        }
        return `<div class="stars">${stars}</div>`;
    }
    
    /**
     * Renderizar paginación
     */
    renderPagination(pagination) {
        const container = document.getElementById('wishlist-pagination');
        if (!container || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '<nav><ul class="pagination justify-content-center">';
        
        // Botón anterior
        if (pagination.has_previous) {
            html += `
                <li class="page-item">
                    <button class="page-link" onclick="window.wishlistManager.goToPage(${pagination.previous_page})">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </li>
            `;
        }
        
        // Números de página
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            html += `
                <li class="page-item ${activeClass}">
                    <button class="page-link" onclick="window.wishlistManager.goToPage(${i})">${i}</button>
                </li>
            `;
        }
        
        // Botón siguiente
        if (pagination.has_next) {
            html += `
                <li class="page-item">
                    <button class="page-link" onclick="window.wishlistManager.goToPage(${pagination.next_page})">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </li>
            `;
        }
        
        html += '</ul></nav>';
        container.innerHTML = html;
    }
    
    /**
     * Ir a página específica
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadWishlist();
        
        // Scroll suave hacia arriba
        document.querySelector('.wishlist-content').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
    
    /**
     * Toggle wishlist (agregar/quitar)
     */
    async toggleWishlist(productId, button = null) {
        try {
            if (button) {
                button.disabled = true;
            }
            
            // Verificar si está en wishlist
            const checkResponse = await fetch(`${this.apiUrl}/check/${productId}`);
            const checkData = await checkResponse.json();
            
            if (checkData.data.requires_login) {
                window.location.href = this.baseUrl + 'login';
                return;
            }
            
            let response, data;
            
            if (checkData.data.in_wishlist) {
                // Remover de wishlist
                response = await fetch(`${this.apiUrl}/${productId}`, {
                    method: 'DELETE'
                });
                data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification('Producto eliminado de tu lista de deseos', 'info');
                    this.updateWishlistButton(button, false);
                    this.updateGlobalWishlistCount();
                    this.clearCache();
                    
                    // Si estamos en la página de wishlist, recargar
                    if (window.location.pathname.includes('wishlist')) {
                        this.loadWishlist();
                    }
                }
            } else {
                // Agregar a wishlist
                response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        product_id: productId
                    })
                });
                data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification('Producto agregado a tu lista de deseos', 'success');
                    this.updateWishlistButton(button, true);
                    this.updateGlobalWishlistCount();
                    this.clearCache();
                }
            }
            
            if (data.status !== 'success') {
                this.showNotification(data.message || 'Error al procesar solicitud', 'error');
            }
            
        } catch (error) {
            console.error('Error toggling wishlist:', error);
            this.showNotification('Error de conexión', 'error');
        } finally {
            if (button) {
                button.disabled = false;
            }
        }
    }
    
    /**
     * Remover de wishlist
     */
    async removeFromWishlist(productId) {
        if (!confirm('¿Estás seguro de que quieres eliminar este producto de tu lista de deseos?')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiUrl}/${productId}`, {
                method: 'DELETE'
            });
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showNotification('Producto eliminado de tu lista de deseos', 'info');
                this.updateGlobalWishlistCount();
                this.clearCache();
                this.loadWishlist();
            } else {
                this.showNotification(data.message || 'Error al eliminar producto', 'error');
            }
            
        } catch (error) {
            console.error('Error removing from wishlist:', error);
            this.showNotification('Error de conexión', 'error');
        }
    }
    
    /**
     * Editar item de wishlist
     */
    async editWishlistItem(productId) {
        try {
            // Obtener datos actuales del item
            const response = await fetch(`${this.apiUrl}?limit=1000`);
            const data = await response.json();
            
            if (data.status === 'success') {
                const item = data.data.items.find(i => i.product.id === productId);
                if (item) {
                    this.showEditModal(item);
                }
            }
            
        } catch (error) {
            console.error('Error loading item for edit:', error);
            this.showNotification('Error al cargar datos del producto', 'error');
        }
    }
    
    /**
     * Mostrar modal de edición
     */
    showEditModal(item) {
        const modal = document.getElementById('editWishlistModal');
        if (!modal) return;
        
        // Llenar formulario
        document.getElementById('edit-product-id').value = item.product.id;
        document.getElementById('edit-priority').value = item.wishlist_data.priority;
        document.getElementById('edit-notes').value = item.wishlist_data.notes || '';
        document.getElementById('edit-notifications').checked = item.wishlist_data.notification_enabled;
        document.getElementById('edit-price-alert').value = item.wishlist_data.price_alert_threshold || '';
        
        // Mostrar modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
    
    /**
     * Guardar cambios de wishlist
     */
    async saveWishlistChanges() {
        try {
            const productId = document.getElementById('edit-product-id').value;
            const priority = document.getElementById('edit-priority').value;
            const notes = document.getElementById('edit-notes').value;
            const notifications = document.getElementById('edit-notifications').checked;
            const priceAlert = document.getElementById('edit-price-alert').value;
            
            const updateData = {
                priority: priority,
                notes: notes || null,
                notification_enabled: notifications ? 1 : 0,
                price_alert_threshold: priceAlert ? parseFloat(priceAlert) : null
            };
            
            const response = await fetch(`${this.apiUrl}/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showNotification('Cambios guardados correctamente', 'success');
                
                // Cerrar modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editWishlistModal'));
                modal.hide();
                
                // Recargar wishlist
                this.clearCache();
                this.loadWishlist();
            } else {
                this.showNotification(data.message || 'Error al guardar cambios', 'error');
            }
            
        } catch (error) {
            console.error('Error saving wishlist changes:', error);
            this.showNotification('Error de conexión', 'error');
        }
    }
    
    /**
     * Actualizar contador global de wishlist
     */
    async updateGlobalWishlistCount() {
        try {
            const response = await fetch(`${this.apiUrl}/count`);
            const data = await response.json();
            
            if (data.status === 'success' && !data.data.requires_login) {
                this.updateWishlistCountDisplay(data.data.count);
            }
            
        } catch (error) {
            console.error('Error updating wishlist count:', error);
        }
    }
    
    /**
     * Actualizar display del contador
     */
    updateWishlistCountDisplay(count) {
        const counters = document.querySelectorAll('.wishlist-count');
        counters.forEach(counter => {
            counter.textContent = count;
            if (count > 0) {
                counter.classList.remove('d-none');
            } else {
                counter.classList.add('d-none');
            }
        });
    }
    
    /**
     * Actualizar botón de wishlist
     */
    updateWishlistButton(button, inWishlist) {
        if (!button) return;
        
        const icon = button.querySelector('i');
        if (icon) {
            if (inWishlist) {
                icon.className = 'fas fa-heart';
                button.classList.add('active');
            } else {
                icon.className = 'far fa-heart';
                button.classList.remove('active');
            }
        }
    }
    
    /**
     * Actualizar estadísticas
     */
    updateStats(stats) {
        if (stats) {
            const totalElement = document.getElementById('total-items');
            if (totalElement) {
                totalElement.textContent = stats.total_items || 0;
            }
            
            const alertsElement = document.getElementById('with-alerts');
            if (alertsElement) {
                alertsElement.textContent = stats.with_price_alerts || 0;
            }
        }
    }
    
    /**
     * Actualizar contador de productos en stock
     */
    updateInStockCount(items) {
        const inStockCount = items.filter(item => item.stock.in_stock).length;
        const element = document.getElementById('in-stock');
        if (element) {
            element.textContent = inStockCount;
        }
    }
    
    /**
     * Limpiar filtros
     */
    clearFilters() {
        document.getElementById('priority-filter').value = '';
        document.getElementById('sort-filter').value = 'created_at';
        document.getElementById('order-filter').value = 'DESC';
        
        this.currentFilters = {
            priority: '',
            sort_by: 'created_at',
            sort_order: 'DESC'
        };
        this.currentPage = 1;
        this.loadWishlist();
    }
    
    /**
     * Mostrar loading
     */
    showLoading() {
        this.isLoading = true;
        const loading = document.getElementById('wishlist-loading');
        if (loading) {
            loading.style.display = 'block';
        }
    }
    
    /**
     * Ocultar loading
     */
    hideLoading() {
        this.isLoading = false;
        const loading = document.getElementById('wishlist-loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }
    
    /**
     * Mostrar error
     */
    showError(message) {
        const container = document.getElementById('wishlist-items');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
    }
    
    /**
     * Mostrar notificación
     */
    showNotification(message, type = 'info') {
        // Usar el sistema de notificaciones global si existe
        if (window.MrCellUtils && window.MrCellUtils.showNotification) {
            window.MrCellUtils.showNotification(message, type);
        } else {
            // Fallback a alert
            alert(message);
        }
    }
    
    /**
     * Formatear fecha
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('es-GT', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    /**
     * Cache simple
     */
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this.cacheTTL) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }
    
    clearCache() {
        this.cache.clear();
    }
}

// Funciones globales para compatibilidad
window.saveWishlistChanges = function() {
    if (window.wishlistManager) {
        window.wishlistManager.saveWishlistChanges();
    }
};

window.clearFilters = function() {
    if (window.wishlistManager) {
        window.wishlistManager.clearFilters();
    }
};

// Actualizar funciones globales existentes
if (!window.addToWishlist) {
    window.addToWishlist = async function(productId) {
        if (window.wishlistManager) {
            await window.wishlistManager.toggleWishlist(productId);
        }
    };
}

if (!window.removeFromWishlist) {
    window.removeFromWishlist = async function(productId) {
        if (window.wishlistManager) {
            await window.wishlistManager.removeFromWishlist(productId);
        }
    };
}
