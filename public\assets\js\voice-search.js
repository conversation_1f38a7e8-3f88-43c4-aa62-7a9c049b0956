/**
 * Sistema de Búsqueda por Voz para MrCell Guatemala
 * Compatible con todos los navegadores modernos
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class VoiceSearchManager {
    constructor(options = {}) {
        this.options = {
            language: 'es-GT', // Español Guatemala
            continuous: false,
            interimResults: true,
            maxAlternatives: 3,
            timeout: 10000, // 10 segundos
            ...options
        };
        
        this.recognition = null;
        this.isListening = false;
        this.isSupported = false;
        this.callbacks = {
            onStart: null,
            onResult: null,
            onEnd: null,
            onError: null,
            onNoMatch: null
        };
        
        this.init();
    }
    
    /**
     * Inicializar el sistema de reconocimiento de voz
     */
    init() {
        // Verificar soporte del navegador
        this.isSupported = this.checkBrowserSupport();
        
        if (!this.isSupported) {
            console.warn('Voice search not supported in this browser');
            return;
        }
        
        this.setupRecognition();
        this.setupUI();
    }
    
    /**
     * Verificar soporte del navegador
     */
    checkBrowserSupport() {
        return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    }
    
    /**
     * Configurar el reconocimiento de voz
     */
    setupRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        
        if (!SpeechRecognition) {
            return;
        }
        
        this.recognition = new SpeechRecognition();
        
        // Configuración
        this.recognition.lang = this.options.language;
        this.recognition.continuous = this.options.continuous;
        this.recognition.interimResults = this.options.interimResults;
        this.recognition.maxAlternatives = this.options.maxAlternatives;
        
        // Eventos
        this.recognition.onstart = () => {
            this.isListening = true;
            this.updateUI('listening');
            if (this.callbacks.onStart) {
                this.callbacks.onStart();
            }
        };
        
        this.recognition.onresult = (event) => {
            this.handleResults(event);
        };
        
        this.recognition.onerror = (event) => {
            this.handleError(event);
        };
        
        this.recognition.onend = () => {
            this.isListening = false;
            this.updateUI('idle');
            if (this.callbacks.onEnd) {
                this.callbacks.onEnd();
            }
        };
        
        this.recognition.onnomatch = () => {
            if (this.callbacks.onNoMatch) {
                this.callbacks.onNoMatch();
            }
        };
    }
    
    /**
     * Configurar interfaz de usuario
     */
    setupUI() {
        // Crear botón de voz si no existe
        if (!document.getElementById('voice-search-btn')) {
            this.createVoiceButton();
        }
        
        // Crear modal de voz si no existe
        if (!document.getElementById('voice-search-modal')) {
            this.createVoiceModal();
        }
        
        this.bindEvents();
    }
    
    /**
     * Crear botón de búsqueda por voz
     */
    createVoiceButton() {
        const searchContainer = document.querySelector('.search-container') || 
                               document.querySelector('#search-input')?.parentNode;
        
        if (!searchContainer) {
            return;
        }
        
        const voiceButton = document.createElement('button');
        voiceButton.id = 'voice-search-btn';
        voiceButton.type = 'button';
        voiceButton.className = 'voice-search-btn';
        voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceButton.title = 'Buscar por voz';
        
        // Estilos inline para compatibilidad
        voiceButton.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        `;
        
        searchContainer.style.position = 'relative';
        searchContainer.appendChild(voiceButton);
    }
    
    /**
     * Crear modal de búsqueda por voz
     */
    createVoiceModal() {
        const modal = document.createElement('div');
        modal.id = 'voice-search-modal';
        modal.className = 'voice-search-modal';
        modal.style.cssText = `
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            justify-content: center;
            align-items: center;
        `;
        
        modal.innerHTML = `
            <div class="voice-search-content" style="
                background: white;
                border-radius: 20px;
                padding: 40px;
                text-align: center;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <div class="voice-animation" style="
                    width: 100px;
                    height: 100px;
                    border-radius: 50%;
                    background: #667eea;
                    margin: 0 auto 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                ">
                    <i class="fas fa-microphone" style="font-size: 40px; color: white;"></i>
                    <div class="pulse-ring" style="
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        border: 3px solid #667eea;
                        border-radius: 50%;
                        animation: pulse 2s infinite;
                    "></div>
                </div>
                
                <h3 id="voice-status" style="margin-bottom: 10px; color: #333;">
                    Presiona el micrófono y habla
                </h3>
                
                <p id="voice-transcript" style="
                    font-size: 18px;
                    color: #667eea;
                    min-height: 25px;
                    margin-bottom: 20px;
                "></p>
                
                <div class="voice-controls">
                    <button id="voice-start-btn" class="btn btn-primary" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 25px;
                        margin-right: 10px;
                        cursor: pointer;
                    ">
                        <i class="fas fa-microphone"></i> Escuchar
                    </button>
                    
                    <button id="voice-close-btn" class="btn btn-secondary" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 25px;
                        cursor: pointer;
                    ">
                        <i class="fas fa-times"></i> Cerrar
                    </button>
                </div>
                
                <div class="voice-tips" style="
                    margin-top: 20px;
                    font-size: 12px;
                    color: #666;
                ">
                    <p>💡 Ejemplos: "iPhone 13", "Samsung Galaxy", "Audífonos bluetooth"</p>
                </div>
            </div>
        `;
        
        // Agregar estilos de animación
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% {
                    transform: scale(1);
                    opacity: 1;
                }
                100% {
                    transform: scale(1.3);
                    opacity: 0;
                }
            }
            
            .voice-search-modal.show {
                display: flex !important;
            }
            
            .voice-listening .voice-animation {
                background: #dc3545 !important;
            }
            
            .voice-listening .pulse-ring {
                border-color: #dc3545 !important;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(modal);
    }
    
    /**
     * Vincular eventos
     */
    bindEvents() {
        const voiceBtn = document.getElementById('voice-search-btn');
        const modal = document.getElementById('voice-search-modal');
        const startBtn = document.getElementById('voice-start-btn');
        const closeBtn = document.getElementById('voice-close-btn');
        
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => {
                if (this.isSupported) {
                    this.showModal();
                } else {
                    this.showUnsupportedMessage();
                }
            });
        }
        
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.toggleListening();
            });
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideModal();
            });
        }
        
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal();
                }
            });
        }
        
        // Atajos de teclado
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + V para activar búsqueda por voz
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'V') {
                e.preventDefault();
                if (this.isSupported) {
                    this.showModal();
                }
            }
            
            // Escape para cerrar modal
            if (e.key === 'Escape') {
                this.hideModal();
            }
        });
    }
    
    /**
     * Mostrar modal de voz
     */
    showModal() {
        const modal = document.getElementById('voice-search-modal');
        if (modal) {
            modal.classList.add('show');
            this.updateUI('idle');
        }
    }
    
    /**
     * Ocultar modal de voz
     */
    hideModal() {
        const modal = document.getElementById('voice-search-modal');
        if (modal) {
            modal.classList.remove('show');
            if (this.isListening) {
                this.stopListening();
            }
        }
    }
    
    /**
     * Alternar escucha
     */
    toggleListening() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }
    
    /**
     * Iniciar escucha
     */
    startListening() {
        if (!this.recognition || this.isListening) {
            return;
        }
        
        try {
            this.recognition.start();
            
            // Timeout de seguridad
            setTimeout(() => {
                if (this.isListening) {
                    this.stopListening();
                }
            }, this.options.timeout);
            
        } catch (error) {
            console.error('Error starting voice recognition:', error);
            this.handleError({ error: 'not-allowed' });
        }
    }
    
    /**
     * Detener escucha
     */
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }
    
    /**
     * Manejar resultados de voz
     */
    handleResults(event) {
        let finalTranscript = '';
        let interimTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }
        
        // Mostrar transcripción en tiempo real
        const transcriptElement = document.getElementById('voice-transcript');
        if (transcriptElement) {
            transcriptElement.textContent = finalTranscript || interimTranscript;
        }
        
        // Si hay resultado final, procesar búsqueda
        if (finalTranscript) {
            this.processVoiceSearch(finalTranscript.trim());
        }
        
        if (this.callbacks.onResult) {
            this.callbacks.onResult({
                final: finalTranscript,
                interim: interimTranscript,
                isFinal: finalTranscript.length > 0
            });
        }
    }
    
    /**
     * Procesar búsqueda por voz
     */
    processVoiceSearch(transcript) {
        if (!transcript) {
            return;
        }
        
        // Limpiar y procesar el texto
        const cleanTranscript = this.cleanVoiceInput(transcript);
        
        // Llenar el campo de búsqueda
        const searchInput = document.getElementById('search-input') || 
                           document.querySelector('input[type="search"]') ||
                           document.querySelector('.search-input');
        
        if (searchInput) {
            searchInput.value = cleanTranscript;
            
            // Disparar evento de búsqueda
            const event = new Event('input', { bubbles: true });
            searchInput.dispatchEvent(event);
            
            // Si hay función de búsqueda global, ejecutarla
            if (window.performSearch) {
                window.performSearch(cleanTranscript);
            } else if (window.mrCellSearch && window.mrCellSearch.performSearch) {
                window.mrCellSearch.performSearch();
            }
        }
        
        // Cerrar modal después de un breve delay
        setTimeout(() => {
            this.hideModal();
        }, 1500);
        
        // Mostrar mensaje de confirmación
        this.showSearchConfirmation(cleanTranscript);
    }
    
    /**
     * Limpiar entrada de voz
     */
    cleanVoiceInput(transcript) {
        // Convertir números hablados a dígitos
        const numberMap = {
            'cero': '0', 'uno': '1', 'dos': '2', 'tres': '3', 'cuatro': '4',
            'cinco': '5', 'seis': '6', 'siete': '7', 'ocho': '8', 'nueve': '9',
            'diez': '10', 'once': '11', 'doce': '12', 'trece': '13', 'catorce': '14',
            'quince': '15', 'dieciséis': '16', 'diecisiete': '17', 'dieciocho': '18',
            'diecinueve': '19', 'veinte': '20'
        };
        
        let cleaned = transcript.toLowerCase();
        
        // Reemplazar números hablados
        Object.keys(numberMap).forEach(word => {
            const regex = new RegExp(`\\b${word}\\b`, 'g');
            cleaned = cleaned.replace(regex, numberMap[word]);
        });
        
        // Correcciones comunes de reconocimiento de voz
        const corrections = {
            'iphone': 'iPhone',
            'samsung': 'Samsung',
            'huawei': 'Huawei',
            'xiaomi': 'Xiaomi',
            'bluetooth': 'bluetooth',
            'wifi': 'WiFi',
            'usb': 'USB'
        };
        
        Object.keys(corrections).forEach(word => {
            const regex = new RegExp(`\\b${word}\\b`, 'gi');
            cleaned = cleaned.replace(regex, corrections[word]);
        });
        
        return cleaned.trim();
    }
    
    /**
     * Manejar errores
     */
    handleError(event) {
        let errorMessage = 'Error en el reconocimiento de voz';
        
        switch (event.error) {
            case 'no-speech':
                errorMessage = 'No se detectó voz. Intenta de nuevo.';
                break;
            case 'audio-capture':
                errorMessage = 'No se pudo acceder al micrófono.';
                break;
            case 'not-allowed':
                errorMessage = 'Permiso de micrófono denegado.';
                break;
            case 'network':
                errorMessage = 'Error de conexión. Verifica tu internet.';
                break;
            case 'service-not-allowed':
                errorMessage = 'Servicio de voz no disponible.';
                break;
        }
        
        this.updateUI('error', errorMessage);
        
        if (this.callbacks.onError) {
            this.callbacks.onError(event);
        }
        
        // Auto-cerrar después de mostrar error
        setTimeout(() => {
            this.updateUI('idle');
        }, 3000);
    }
    
    /**
     * Actualizar interfaz de usuario
     */
    updateUI(state, message = '') {
        const modal = document.getElementById('voice-search-modal');
        const status = document.getElementById('voice-status');
        const startBtn = document.getElementById('voice-start-btn');
        const transcript = document.getElementById('voice-transcript');
        
        if (!modal || !status || !startBtn) {
            return;
        }
        
        // Remover clases anteriores
        modal.classList.remove('voice-listening', 'voice-error');
        
        switch (state) {
            case 'idle':
                status.textContent = 'Presiona el micrófono y habla';
                startBtn.innerHTML = '<i class="fas fa-microphone"></i> Escuchar';
                startBtn.disabled = false;
                if (transcript) transcript.textContent = '';
                break;
                
            case 'listening':
                status.textContent = 'Escuchando... Habla ahora';
                startBtn.innerHTML = '<i class="fas fa-stop"></i> Detener';
                startBtn.disabled = false;
                modal.classList.add('voice-listening');
                break;
                
            case 'processing':
                status.textContent = 'Procesando...';
                startBtn.disabled = true;
                break;
                
            case 'error':
                status.textContent = message || 'Error en el reconocimiento';
                startBtn.innerHTML = '<i class="fas fa-microphone"></i> Reintentar';
                startBtn.disabled = false;
                modal.classList.add('voice-error');
                break;
        }
    }
    
    /**
     * Mostrar mensaje de no soportado
     */
    showUnsupportedMessage() {
        if (window.MrCellUtils && window.MrCellUtils.showNotification) {
            window.MrCellUtils.showNotification(
                'Tu navegador no soporta búsqueda por voz. Prueba con Chrome o Firefox.',
                'warning'
            );
        } else {
            alert('Tu navegador no soporta búsqueda por voz. Prueba con Chrome o Firefox.');
        }
    }
    
    /**
     * Mostrar confirmación de búsqueda
     */
    showSearchConfirmation(query) {
        if (window.MrCellUtils && window.MrCellUtils.showNotification) {
            window.MrCellUtils.showNotification(
                `Buscando: "${query}"`,
                'success'
            );
        }
    }
    
    /**
     * Configurar callbacks
     */
    on(event, callback) {
        if (this.callbacks.hasOwnProperty('on' + event.charAt(0).toUpperCase() + event.slice(1))) {
            this.callbacks['on' + event.charAt(0).toUpperCase() + event.slice(1)] = callback;
        }
    }
    
    /**
     * Verificar si está soportado
     */
    isVoiceSearchSupported() {
        return this.isSupported;
    }
    
    /**
     * Obtener estado actual
     */
    getState() {
        return {
            isSupported: this.isSupported,
            isListening: this.isListening,
            language: this.options.language
        };
    }
}

// Inicializar automáticamente cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Solo inicializar si no existe ya
    if (!window.voiceSearchManager) {
        window.voiceSearchManager = new VoiceSearchManager({
            language: 'es-GT' // Español Guatemala
        });
        
        // Configurar callbacks si es necesario
        window.voiceSearchManager.on('result', function(result) {
            console.log('Voice search result:', result);
        });
        
        window.voiceSearchManager.on('error', function(error) {
            console.error('Voice search error:', error);
        });
    }
});

// Exportar para uso global
window.VoiceSearchManager = VoiceSearchManager;
