<?php

namespace App\Models;

use CodeIgniter\Model;

class UserNotificationPreferencesModel extends Model
{
    protected $table = 'user_notification_preferences';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'template_key',
        'is_enabled'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'template_key' => 'required|max_length[100]',
        'is_enabled' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'El ID de usuario es requerido',
            'integer' => 'El ID de usuario debe ser un número'
        ],
        'template_key' => [
            'required' => 'La clave de plantilla es requerida'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Obtener preferencias de un usuario
     */
    public function getUserPreferences($userId)
    {
        return $this->where('user_id', $userId)->findAll();
    }

    /**
     * Verificar si un usuario tiene habilitada una notificación
     */
    public function isNotificationEnabled($userId, $templateKey)
    {
        $preference = $this->where('user_id', $userId)
                          ->where('template_key', $templateKey)
                          ->first();
        
        // Si no existe preferencia, verificar si la plantilla es obligatoria
        if (!$preference) {
            $templateModel = new \App\Models\WhatsAppTemplateModel();
            $template = $templateModel->getTemplate($templateKey);
            
            // Si es obligatoria o no existe la plantilla, retornar true
            return $template ? ($template['is_mandatory'] == 1) : true;
        }
        
        return $preference['is_enabled'] == 1;
    }

    /**
     * Actualizar preferencia de notificación
     */
    public function updatePreference($userId, $templateKey, $isEnabled)
    {
        $existing = $this->where('user_id', $userId)
                        ->where('template_key', $templateKey)
                        ->first();
        
        if ($existing) {
            return $this->update($existing['id'], [
                'is_enabled' => $isEnabled ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            return $this->insert([
                'user_id' => $userId,
                'template_key' => $templateKey,
                'is_enabled' => $isEnabled ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Inicializar preferencias para un nuevo usuario
     */
    public function initializeUserPreferences($userId)
    {
        $templateModel = new \App\Models\WhatsAppTemplateModel();
        $optionalTemplates = $templateModel->getOptionalTemplates();
        
        $preferences = [];
        foreach ($optionalTemplates as $template) {
            $preferences[] = [
                'user_id' => $userId,
                'template_key' => $template['template_key'],
                'is_enabled' => 1, // Por defecto habilitadas
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        
        if (!empty($preferences)) {
            return $this->insertBatch($preferences);
        }
        
        return true;
    }

    /**
     * Obtener preferencias con información de plantillas
     */
    public function getUserPreferencesWithTemplates($userId)
    {
        return $this->select('user_notification_preferences.*, whatsapp_templates.template_name, whatsapp_templates.description, whatsapp_templates.is_mandatory')
                   ->join('whatsapp_templates', 'whatsapp_templates.template_key = user_notification_preferences.template_key')
                   ->where('user_notification_preferences.user_id', $userId)
                   ->where('whatsapp_templates.is_active', 1)
                   ->findAll();
    }

    /**
     * Obtener todas las plantillas con preferencias del usuario
     */
    public function getAllTemplatesWithUserPreferences($userId)
    {
        $templateModel = new \App\Models\WhatsAppTemplateModel();
        $templates = $templateModel->getActiveTemplates();
        
        $userPreferences = $this->getUserPreferences($userId);
        $preferencesMap = [];
        
        foreach ($userPreferences as $pref) {
            $preferencesMap[$pref['template_key']] = $pref['is_enabled'];
        }
        
        foreach ($templates as &$template) {
            if (isset($preferencesMap[$template['template_key']])) {
                $template['user_enabled'] = $preferencesMap[$template['template_key']];
            } else {
                // Si no hay preferencia, usar el valor por defecto basado en si es obligatoria
                $template['user_enabled'] = $template['is_mandatory'];
            }
            
            // Las plantillas obligatorias no se pueden desactivar
            $template['can_disable'] = !$template['is_mandatory'];
        }
        
        return $templates;
    }

    /**
     * Actualizar múltiples preferencias de un usuario
     */
    public function updateUserPreferences($userId, $preferences)
    {
        $this->db->transStart();
        
        foreach ($preferences as $templateKey => $isEnabled) {
            $this->updatePreference($userId, $templateKey, $isEnabled);
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }
}
