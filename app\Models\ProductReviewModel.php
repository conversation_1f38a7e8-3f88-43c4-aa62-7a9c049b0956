<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductReviewModel extends BaseModel
{
    protected $table = 'product_reviews';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'uuid',
        'product_id',
        'user_id',
        'customer_name',
        'customer_email',
        'rating',
        'title',
        'comment',
        'is_verified_purchase',
        'is_approved',
        'is_featured',
        'helpful_count',
        'admin_response',
        'admin_response_date'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'product_id' => 'required|integer',
        'customer_name' => 'required|max_length[255]',
        'customer_email' => 'required|valid_email|max_length[255]',
        'rating' => 'required|integer|greater_than[0]|less_than[6]',
        'title' => 'required|max_length[255]',
        'comment' => 'required|min_length[10]'
    ];

    protected $validationMessages = [
        'product_id' => [
            'required' => 'El producto es requerido',
            'integer' => 'ID de producto inválido'
        ],
        'customer_name' => [
            'required' => 'El nombre es requerido',
            'max_length' => 'El nombre no puede exceder 255 caracteres'
        ],
        'customer_email' => [
            'required' => 'El email es requerido',
            'valid_email' => 'Debe ser un email válido',
            'max_length' => 'El email no puede exceder 255 caracteres'
        ],
        'rating' => [
            'required' => 'La calificación es requerida',
            'integer' => 'La calificación debe ser un número',
            'greater_than' => 'La calificación debe ser entre 1 y 5',
            'less_than' => 'La calificación debe ser entre 1 y 5'
        ],
        'title' => [
            'required' => 'El título es requerido',
            'max_length' => 'El título no puede exceder 255 caracteres'
        ],
        'comment' => [
            'required' => 'El comentario es requerido',
            'min_length' => 'El comentario debe tener al menos 10 caracteres'
        ]
    ];

    // Callbacks
    protected $beforeInsert = ['generateUuid'];
    protected $beforeUpdate = [];

    /**
     * Generate UUID before insert
     */
    protected function generateUuid(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->generateUuidV4();
        }
        return $data;
    }

    /**
     * Generate UUID v4
     */
    private function generateUuidV4()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Get reviews for a product
     */
    public function getProductReviews($productId, $approved = true, $limit = null, $offset = 0)
    {
        $builder = $this->select('product_reviews.*, CONCAT(users.first_name, " ", users.last_name) as user_name')
                        ->join('users', 'users.id = product_reviews.user_id', 'left')
                        ->where('product_reviews.product_id', $productId);

        if ($approved) {
            $builder->where('product_reviews.is_approved', 1);
        }

        $builder->orderBy('product_reviews.is_featured', 'DESC')
                ->orderBy('product_reviews.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit, $offset);
        }

        return $builder->findAll();
    }

    /**
     * Get product rating statistics
     */
    public function getProductRatingStats($productId)
    {
        $stats = $this->select('
                AVG(rating) as average_rating,
                COUNT(*) as total_reviews,
                SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as rating_5,
                SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as rating_4,
                SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as rating_3,
                SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as rating_2,
                SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as rating_1
            ')
            ->where('product_id', $productId)
            ->where('is_approved', 1)
            ->first();

        if ($stats) {
            $stats['average_rating'] = round($stats['average_rating'], 1);
            
            // Calculate percentages
            if ($stats['total_reviews'] > 0) {
                for ($i = 1; $i <= 5; $i++) {
                    $stats["rating_{$i}_percent"] = round(($stats["rating_{$i}"] / $stats['total_reviews']) * 100, 1);
                }
            } else {
                for ($i = 1; $i <= 5; $i++) {
                    $stats["rating_{$i}_percent"] = 0;
                }
            }
        }

        return $stats;
    }

    /**
     * Check if user can review product
     */
    public function canUserReview($productId, $userId = null, $email = null)
    {
        $builder = $this->where('product_id', $productId);
        
        if ($userId) {
            $builder->where('user_id', $userId);
        } else {
            $builder->where('customer_email', $email);
        }

        return $builder->countAllResults() === 0;
    }

    /**
     * Get pending reviews for admin
     */
    public function getPendingReviews($limit = null, $offset = 0)
    {
        $builder = $this->select('product_reviews.*, products.name as product_name, CONCAT(users.first_name, " ", users.last_name) as user_name')
                        ->join('products', 'products.id = product_reviews.product_id', 'left')
                        ->join('users', 'users.id = product_reviews.user_id', 'left')
                        ->where('product_reviews.is_approved', 0)
                        ->orderBy('product_reviews.created_at', 'ASC');

        if ($limit) {
            $builder->limit($limit, $offset);
        }

        return $builder->findAll();
    }

    /**
     * Approve review
     */
    public function approveReview($reviewId)
    {
        return $this->update($reviewId, ['is_approved' => 1]);
    }

    /**
     * Reject review
     */
    public function rejectReview($reviewId)
    {
        return $this->delete($reviewId);
    }

    /**
     * Add admin response
     */
    public function addAdminResponse($reviewId, $response)
    {
        return $this->update($reviewId, [
            'admin_response' => $response,
            'admin_response_date' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured($reviewId)
    {
        $review = $this->find($reviewId);
        if ($review) {
            return $this->update($reviewId, ['is_featured' => !$review['is_featured']]);
        }
        return false;
    }
}
