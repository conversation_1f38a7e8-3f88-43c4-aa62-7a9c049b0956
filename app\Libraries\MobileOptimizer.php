<?php

namespace App\Libraries;

/**
 * Sistema de Optimización Móvil
 * Detecta dispositivos móviles y optimiza la experiencia
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class MobileOptimizer
{
    private $userAgent;
    private $isMobile;
    private $isTablet;
    private $deviceType;
    private $browserInfo;
    private $config;
    
    public function __construct()
    {
        $this->userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $this->config = [
            'enable_mobile_detection' => env('MOBILE_DETECTION_ENABLED', true),
            'enable_mobile_cache' => env('MOBILE_CACHE_ENABLED', true),
            'enable_image_optimization' => env('MOBILE_IMAGE_OPTIMIZATION', true),
            'enable_lazy_loading' => env('MOBILE_LAZY_LOADING', true),
            'enable_touch_gestures' => env('MOBILE_TOUCH_GESTURES', true),
            'mobile_breakpoint' => env('MOBILE_BREAKPOINT', 768),
            'tablet_breakpoint' => env('TABLET_BREAKPOINT', 1024)
        ];
        
        $this->detectDevice();
        $this->detectBrowser();
    }
    
    /**
     * Detectar tipo de dispositivo
     */
    private function detectDevice(): void
    {
        $mobileKeywords = [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 
            'Windows Phone', 'Opera Mini', 'IEMobile', 'Mobile Safari'
        ];
        
        $tabletKeywords = [
            'iPad', 'Android.*Tablet', 'Tablet', 'Kindle', 'Silk', 
            'PlayBook', 'Nexus 7', 'Nexus 10'
        ];
        
        // Detectar tablet primero
        foreach ($tabletKeywords as $keyword) {
            if (preg_match('/' . $keyword . '/i', $this->userAgent)) {
                $this->isTablet = true;
                $this->isMobile = false;
                $this->deviceType = 'tablet';
                return;
            }
        }
        
        // Detectar móvil
        foreach ($mobileKeywords as $keyword) {
            if (preg_match('/' . $keyword . '/i', $this->userAgent)) {
                $this->isMobile = true;
                $this->isTablet = false;
                $this->deviceType = 'mobile';
                return;
            }
        }
        
        // Desktop por defecto
        $this->isMobile = false;
        $this->isTablet = false;
        $this->deviceType = 'desktop';
    }
    
    /**
     * Detectar información del navegador
     */
    private function detectBrowser(): void
    {
        $this->browserInfo = [
            'name' => 'unknown',
            'version' => 'unknown',
            'supports_webp' => false,
            'supports_avif' => false,
            'supports_service_worker' => false,
            'supports_push_notifications' => false
        ];
        
        // Detectar navegador
        if (preg_match('/Chrome\/([0-9.]+)/', $this->userAgent, $matches)) {
            $this->browserInfo['name'] = 'Chrome';
            $this->browserInfo['version'] = $matches[1];
            $this->browserInfo['supports_webp'] = version_compare($matches[1], '23', '>=');
            $this->browserInfo['supports_avif'] = version_compare($matches[1], '85', '>=');
            $this->browserInfo['supports_service_worker'] = version_compare($matches[1], '40', '>=');
            $this->browserInfo['supports_push_notifications'] = version_compare($matches[1], '42', '>=');
        } elseif (preg_match('/Firefox\/([0-9.]+)/', $this->userAgent, $matches)) {
            $this->browserInfo['name'] = 'Firefox';
            $this->browserInfo['version'] = $matches[1];
            $this->browserInfo['supports_webp'] = version_compare($matches[1], '65', '>=');
            $this->browserInfo['supports_avif'] = version_compare($matches[1], '93', '>=');
            $this->browserInfo['supports_service_worker'] = version_compare($matches[1], '44', '>=');
            $this->browserInfo['supports_push_notifications'] = version_compare($matches[1], '44', '>=');
        } elseif (preg_match('/Safari\/([0-9.]+)/', $this->userAgent, $matches)) {
            $this->browserInfo['name'] = 'Safari';
            $this->browserInfo['version'] = $matches[1];
            $this->browserInfo['supports_webp'] = strpos($this->userAgent, 'Version/14') !== false;
            $this->browserInfo['supports_service_worker'] = strpos($this->userAgent, 'Version/11') !== false;
        }
    }
    
    /**
     * Verificar si es dispositivo móvil
     */
    public function isMobile(): bool
    {
        return $this->isMobile;
    }
    
    /**
     * Verificar si es tablet
     */
    public function isTablet(): bool
    {
        return $this->isTablet;
    }
    
    /**
     * Obtener tipo de dispositivo
     */
    public function getDeviceType(): string
    {
        return $this->deviceType;
    }
    
    /**
     * Obtener información del navegador
     */
    public function getBrowserInfo(): array
    {
        return $this->browserInfo;
    }
    
    /**
     * Optimizar imágenes para móvil
     */
    public function optimizeImageForMobile(string $imagePath, array $options = []): string
    {
        if (!$this->config['enable_image_optimization']) {
            return $imagePath;
        }
        
        $options = array_merge([
            'width' => $this->isMobile ? 400 : ($this->isTablet ? 600 : 800),
            'quality' => $this->isMobile ? 75 : 85,
            'format' => 'auto' // auto, webp, avif, jpg
        ], $options);
        
        // Determinar mejor formato
        $format = $options['format'];
        if ($format === 'auto') {
            if ($this->browserInfo['supports_avif']) {
                $format = 'avif';
            } elseif ($this->browserInfo['supports_webp']) {
                $format = 'webp';
            } else {
                $format = 'jpg';
            }
        }
        
        // Generar URL optimizada
        $optimizedPath = $this->generateOptimizedImagePath($imagePath, $options['width'], $options['quality'], $format);
        
        return $optimizedPath;
    }
    
    /**
     * Generar HTML de imagen responsiva
     */
    public function generateResponsiveImage(string $imagePath, string $alt = '', array $options = []): string
    {
        $options = array_merge([
            'lazy' => $this->config['enable_lazy_loading'],
            'sizes' => '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
            'class' => 'responsive-image'
        ], $options);
        
        $srcset = [];
        $widths = [400, 600, 800, 1200];
        
        foreach ($widths as $width) {
            $optimizedPath = $this->optimizeImageForMobile($imagePath, ['width' => $width]);
            $srcset[] = "{$optimizedPath} {$width}w";
        }
        
        $srcsetAttr = implode(', ', $srcset);
        $lazyClass = $options['lazy'] ? 'lazy' : '';
        $srcAttr = $options['lazy'] ? 'data-src' : 'src';
        
        return "<img {$srcAttr}=\"{$imagePath}\" 
                     srcset=\"{$srcsetAttr}\" 
                     sizes=\"{$options['sizes']}\" 
                     alt=\"{$alt}\" 
                     class=\"{$options['class']} {$lazyClass}\" 
                     loading=\"" . ($options['lazy'] ? 'lazy' : 'eager') . "\">";
    }
    
    /**
     * Generar CSS crítico para móvil
     */
    public function generateCriticalCSS(): string
    {
        $criticalCSS = '';
        
        if ($this->isMobile) {
            $criticalCSS = "
                /* Critical Mobile CSS */
                * { box-sizing: border-box; }
                body { margin: 0; padding: 0; font-size: 16px; line-height: 1.4; }
                .container { max-width: 100%; padding: 0 15px; }
                .btn { min-height: 44px; padding: 12px 20px; font-size: 16px; }
                .form-control { min-height: 44px; font-size: 16px; }
                .navbar { position: sticky; top: 0; z-index: 1000; }
                .product-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; }
                .lazy { opacity: 0; transition: opacity 0.3s; }
                .lazy.loaded { opacity: 1; }
            ";
        } elseif ($this->isTablet) {
            $criticalCSS = "
                /* Critical Tablet CSS */
                .container { max-width: 100%; padding: 0 20px; }
                .product-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
                .btn { min-height: 40px; padding: 10px 18px; }
            ";
        }
        
        return $criticalCSS;
    }
    
    /**
     * Generar JavaScript para gestos táctiles
     */
    public function generateTouchGesturesJS(): string
    {
        if (!$this->config['enable_touch_gestures'] || !$this->isMobile) {
            return '';
        }
        
        return "
            // Touch Gestures for Mobile
            class TouchGestureManager {
                constructor() {
                    this.startX = 0;
                    this.startY = 0;
                    this.endX = 0;
                    this.endY = 0;
                    this.minSwipeDistance = 50;
                    this.init();
                }
                
                init() {
                    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
                    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
                    
                    // Pull to refresh
                    this.initPullToRefresh();
                    
                    // Swipe navigation
                    this.initSwipeNavigation();
                }
                
                handleTouchStart(e) {
                    this.startX = e.touches[0].clientX;
                    this.startY = e.touches[0].clientY;
                }
                
                handleTouchEnd(e) {
                    this.endX = e.changedTouches[0].clientX;
                    this.endY = e.changedTouches[0].clientY;
                    this.handleSwipe();
                }
                
                handleSwipe() {
                    const deltaX = this.endX - this.startX;
                    const deltaY = this.endY - this.startY;
                    
                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        // Horizontal swipe
                        if (Math.abs(deltaX) > this.minSwipeDistance) {
                            if (deltaX > 0) {
                                this.onSwipeRight();
                            } else {
                                this.onSwipeLeft();
                            }
                        }
                    } else {
                        // Vertical swipe
                        if (Math.abs(deltaY) > this.minSwipeDistance) {
                            if (deltaY > 0) {
                                this.onSwipeDown();
                            } else {
                                this.onSwipeUp();
                            }
                        }
                    }
                }
                
                onSwipeLeft() {
                    // Next product in gallery
                    const nextBtn = document.querySelector('.product-gallery .next');
                    if (nextBtn) nextBtn.click();
                }
                
                onSwipeRight() {
                    // Previous product in gallery
                    const prevBtn = document.querySelector('.product-gallery .prev');
                    if (prevBtn) prevBtn.click();
                    
                    // Or go back in navigation
                    if (window.history.length > 1) {
                        window.history.back();
                    }
                }
                
                onSwipeUp() {
                    // Show more content or scroll to top
                    const showMoreBtn = document.querySelector('.show-more');
                    if (showMoreBtn) showMoreBtn.click();
                }
                
                onSwipeDown() {
                    // Pull to refresh functionality
                    if (window.scrollY === 0) {
                        this.triggerRefresh();
                    }
                }
                
                initPullToRefresh() {
                    let startY = 0;
                    let pullDistance = 0;
                    const threshold = 100;
                    
                    document.addEventListener('touchstart', (e) => {
                        if (window.scrollY === 0) {
                            startY = e.touches[0].clientY;
                        }
                    });
                    
                    document.addEventListener('touchmove', (e) => {
                        if (window.scrollY === 0 && startY > 0) {
                            pullDistance = e.touches[0].clientY - startY;
                            if (pullDistance > 0) {
                                this.showPullToRefreshIndicator(pullDistance);
                            }
                        }
                    });
                    
                    document.addEventListener('touchend', () => {
                        if (pullDistance > threshold) {
                            this.triggerRefresh();
                        }
                        this.hidePullToRefreshIndicator();
                        startY = 0;
                        pullDistance = 0;
                    });
                }
                
                showPullToRefreshIndicator(distance) {
                    let indicator = document.getElementById('pull-to-refresh');
                    if (!indicator) {
                        indicator = document.createElement('div');
                        indicator.id = 'pull-to-refresh';
                        indicator.innerHTML = '↓ Desliza para actualizar';
                        indicator.style.cssText = `
                            position: fixed;
                            top: -50px;
                            left: 50%;
                            transform: translateX(-50%);
                            background: #007bff;
                            color: white;
                            padding: 10px 20px;
                            border-radius: 20px;
                            z-index: 9999;
                            transition: top 0.3s ease;
                        `;
                        document.body.appendChild(indicator);
                    }
                    
                    const progress = Math.min(distance / 100, 1);
                    indicator.style.top = (progress * 50 - 50) + 'px';
                    
                    if (progress >= 1) {
                        indicator.innerHTML = '↑ Suelta para actualizar';
                        indicator.style.background = '#28a745';
                    }
                }
                
                hidePullToRefreshIndicator() {
                    const indicator = document.getElementById('pull-to-refresh');
                    if (indicator) {
                        indicator.style.top = '-50px';
                        setTimeout(() => {
                            if (indicator.parentNode) {
                                indicator.parentNode.removeChild(indicator);
                            }
                        }, 300);
                    }
                }
                
                triggerRefresh() {
                    // Show loading
                    this.showRefreshLoading();
                    
                    // Reload page or refresh content
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
                
                showRefreshLoading() {
                    const indicator = document.getElementById('pull-to-refresh');
                    if (indicator) {
                        indicator.innerHTML = '⟳ Actualizando...';
                        indicator.style.background = '#ffc107';
                        indicator.style.top = '10px';
                    }
                }
                
                initSwipeNavigation() {
                    // Swipe navigation for product galleries
                    const galleries = document.querySelectorAll('.product-gallery');
                    galleries.forEach(gallery => {
                        let startX = 0;
                        
                        gallery.addEventListener('touchstart', (e) => {
                            startX = e.touches[0].clientX;
                        });
                        
                        gallery.addEventListener('touchend', (e) => {
                            const endX = e.changedTouches[0].clientX;
                            const deltaX = endX - startX;
                            
                            if (Math.abs(deltaX) > 50) {
                                const event = new CustomEvent('gallerySwipe', {
                                    detail: { direction: deltaX > 0 ? 'right' : 'left' }
                                });
                                gallery.dispatchEvent(event);
                            }
                        });
                    });
                }
            }
            
            // Initialize touch gestures
            if ('ontouchstart' in window) {
                new TouchGestureManager();
            }
        ";
    }
    
    /**
     * Generar configuración PWA
     */
    public function generatePWAConfig(): array
    {
        return [
            'name' => 'MrCell Guatemala',
            'short_name' => 'MrCell',
            'description' => 'Tu tienda de tecnología de confianza en Guatemala',
            'start_url' => '/',
            'display' => 'standalone',
            'background_color' => '#ffffff',
            'theme_color' => '#007bff',
            'orientation' => 'portrait-primary',
            'icons' => [
                [
                    'src' => '/assets/images/icons/icon-72x72.png',
                    'sizes' => '72x72',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-96x96.png',
                    'sizes' => '96x96',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-128x128.png',
                    'sizes' => '128x128',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-144x144.png',
                    'sizes' => '144x144',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-152x152.png',
                    'sizes' => '152x152',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-192x192.png',
                    'sizes' => '192x192',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-384x384.png',
                    'sizes' => '384x384',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/icons/icon-512x512.png',
                    'sizes' => '512x512',
                    'type' => 'image/png'
                ]
            ]
        ];
    }
    
    /**
     * Métodos auxiliares privados
     */
    private function generateOptimizedImagePath(string $imagePath, int $width, int $quality, string $format): string
    {
        $pathInfo = pathinfo($imagePath);
        $filename = $pathInfo['filename'];
        $directory = $pathInfo['dirname'];
        
        return "{$directory}/optimized/{$filename}_{$width}_{$quality}.{$format}";
    }
    
    /**
     * Obtener configuración de dispositivo
     */
    public function getDeviceConfig(): array
    {
        return [
            'is_mobile' => $this->isMobile,
            'is_tablet' => $this->isTablet,
            'device_type' => $this->deviceType,
            'browser_info' => $this->browserInfo,
            'supports_webp' => $this->browserInfo['supports_webp'],
            'supports_service_worker' => $this->browserInfo['supports_service_worker'],
            'supports_push_notifications' => $this->browserInfo['supports_push_notifications']
        ];
    }
    
    /**
     * Verificar si soporta características modernas
     */
    public function supportsModernFeatures(): bool
    {
        return $this->browserInfo['supports_service_worker'] && 
               $this->browserInfo['supports_push_notifications'];
    }
}
