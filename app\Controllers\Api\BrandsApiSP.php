<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;

class BrandsApiSP extends ResourceController
{
    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener todas las marcas activas
     * GET /api/brands
     */
    public function index()
    {
        try {
            $query = $this->db->query("
                SELECT b.*, 
                       COUNT(p.id) as products_count
                FROM brands b
                LEFT JOIN products p ON b.id = p.brand_id 
                    AND p.deleted_at IS NULL 
                    AND p.is_active = 1
                WHERE b.deleted_at IS NULL 
                    AND b.is_active = 1
                GROUP BY b.id
                HAVING products_count > 0
                ORDER BY b.sort_order ASC, b.name ASC
            ");

            $brands = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $brands,
                'message' => 'Marcas obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en BrandsApiSP::index: ' . $e->getMessage());
            return $this->failServerError('Error al obtener marcas');
        }
    }

    /**
     * Obtener marcas que tienen productos en las categorías especificadas
     * GET /api/brands/by-categories?category_ids=1,2,3
     */
    public function byCategories()
    {
        try {
            $categoryIds = $this->request->getGet('category_ids');
            
            if (empty($categoryIds)) {
                // Si no hay categorías, devolver todas las marcas
                return $this->index();
            }

            // Convertir string a array y limpiar
            $categoryIdsArray = array_map('intval', explode(',', $categoryIds));
            $categoryIdsArray = array_filter($categoryIdsArray, function($id) {
                return $id > 0;
            });

            if (empty($categoryIdsArray)) {
                return $this->index();
            }

            $placeholders = str_repeat('?,', count($categoryIdsArray) - 1) . '?';

            $query = $this->db->query("
                SELECT DISTINCT b.*, 
                       COUNT(p.id) as products_count
                FROM brands b
                INNER JOIN products p ON b.id = p.brand_id 
                    AND p.deleted_at IS NULL 
                    AND p.is_active = 1
                    AND p.category_id IN ($placeholders)
                WHERE b.deleted_at IS NULL 
                    AND b.is_active = 1
                GROUP BY b.id
                ORDER BY b.sort_order ASC, b.name ASC
            ", $categoryIdsArray);

            $brands = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $brands,
                'category_ids' => $categoryIdsArray,
                'message' => 'Marcas por categorías obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en BrandsApiSP::byCategories: ' . $e->getMessage());
            return $this->failServerError('Error al obtener marcas por categorías');
        }
    }
}
