<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\ProductModel;
use App\Services\RecurrenteService;

class RecurrenteCleanup extends BaseCommand
{
    protected $group       = 'Recurrente';
    protected $name        = 'recurrente:cleanup';
    protected $description = 'Limpieza completa de productos en Recurrente';
    protected $usage       = 'recurrente:cleanup [action]';
    protected $arguments   = [
        'action' => 'Acción a realizar: list, delete-all, clean-db, resync-all'
    ];
    protected $options     = [
        '--confirm' => 'Confirmar acciones destructivas',
        '--dry-run' => 'Mostrar qué se haría sin ejecutar cambios'
    ];

    public function run(array $params)
    {
        $action = $params[0] ?? 'list';
        $confirm = CLI::getOption('confirm');
        $dryRun = CLI::getOption('dry-run');

        $recurrenteService = new RecurrenteService();
        $productModel = new ProductModel();

        if (!$recurrenteService->isEnabled()) {
            CLI::error('Recurrente no está habilitado');
            return;
        }

        switch ($action) {
            case 'list':
                $this->listRecurrenteProducts($recurrenteService);
                break;
            
            case 'delete-all':
                $this->deleteAllRecurrenteProducts($recurrenteService, $confirm, $dryRun);
                break;
            
            case 'clean-db':
                $this->cleanLocalDatabase($productModel, $confirm, $dryRun);
                break;
            
            case 'resync-all':
                $this->resyncAllProducts($recurrenteService, $productModel, $dryRun);
                break;
            
            default:
                CLI::error('Acción no válida. Use: list, delete-all, clean-db, resync-all');
                break;
        }
    }

    private function listRecurrenteProducts($recurrenteService)
    {
        CLI::write('=== PASO 1: Listando productos en Recurrente ===', 'yellow');
        CLI::newLine();

        try {
            $products = $recurrenteService->getProducts(1, 1000);
            
            if (is_array($products) && isset($products[0])) {
                $productsList = $products;
            } elseif (isset($products['data'])) {
                $productsList = $products['data'];
            } else {
                CLI::error('Error obteniendo productos de Recurrente');
                return;
            }

            CLI::write('Total de productos en Recurrente: ' . count($productsList), 'cyan');
            CLI::newLine();

            foreach ($productsList as $index => $product) {
                $num = $index + 1;
                CLI::write("[{$num}] ID: {$product['id']}", 'white');
                CLI::write("    Nombre: {$product['name']}", 'white');
                CLI::write("    Estado: {$product['status']}", 'white');
                
                if (isset($product['image_url']) && !empty($product['image_url'])) {
                    CLI::write("    Imagen: {$product['image_url']}", 'white');
                } else {
                    CLI::write("    Imagen: Sin imagen", 'yellow');
                }
                
                if (isset($product['prices'][0])) {
                    $price = $product['prices'][0]['amount_in_cents'] / 100;
                    CLI::write("    Precio: Q{$price}", 'white');
                }
                CLI::newLine();
            }

            CLI::write('=== Resumen ===', 'yellow');
            CLI::write('Total productos: ' . count($productsList), 'white');
            
            $withImages = array_filter($productsList, function($p) {
                return !empty($p['image_url'] ?? '');
            });
            CLI::write('Con imagen: ' . count($withImages), 'green');
            CLI::write('Sin imagen: ' . (count($productsList) - count($withImages)), 'red');

        } catch (\Exception $e) {
            CLI::error('Error listando productos: ' . $e->getMessage());
        }
    }

    private function deleteAllRecurrenteProducts($recurrenteService, $confirm, $dryRun)
    {
        CLI::write('=== PASO 2: Eliminando productos de Recurrente ===', 'yellow');
        CLI::newLine();

        if (!$confirm && !$dryRun) {
            CLI::error('Esta acción eliminará TODOS los productos de Recurrente.');
            CLI::error('Use --confirm para confirmar o --dry-run para simular');
            return;
        }

        try {
            $products = $recurrenteService->getProducts(1, 1000);
            
            if (is_array($products) && isset($products[0])) {
                $productsList = $products;
            } elseif (isset($products['data'])) {
                $productsList = $products['data'];
            } else {
                CLI::error('Error obteniendo productos de Recurrente');
                return;
            }

            CLI::write('Productos a eliminar: ' . count($productsList), 'cyan');
            CLI::newLine();

            if ($dryRun) {
                CLI::write('=== MODO DRY-RUN ===', 'yellow');
                foreach ($productsList as $product) {
                    CLI::write("Se eliminaría: {$product['name']} (ID: {$product['id']})", 'cyan');
                }
                return;
            }

            $deleted = 0;
            $errors = 0;

            foreach ($productsList as $index => $product) {
                $num = $index + 1;
                $total = count($productsList);
                
                CLI::write("[{$num}/{$total}] Eliminando: {$product['name']}", 'cyan');
                
                try {
                    $recurrenteService->deleteProduct($product['id']);
                    CLI::write("  ✓ Eliminado correctamente", 'green');
                    $deleted++;
                    
                    // Pausa para no sobrecargar la API
                    usleep(500000); // 0.5 segundos
                    
                } catch (\Exception $e) {
                    CLI::write("  ✗ Error: " . $e->getMessage(), 'red');
                    $errors++;
                }
            }

            CLI::newLine();
            CLI::write('=== Resumen de Eliminación ===', 'yellow');
            CLI::write("Eliminados: {$deleted}", 'green');
            CLI::write("Errores: {$errors}", 'red');

        } catch (\Exception $e) {
            CLI::error('Error eliminando productos: ' . $e->getMessage());
        }
    }

    private function cleanLocalDatabase($productModel, $confirm, $dryRun)
    {
        CLI::write('=== PASO 3: Limpiando base de datos local ===', 'yellow');
        CLI::newLine();

        if (!$confirm && !$dryRun) {
            CLI::error('Esta acción limpiará todos los IDs de Recurrente en la base de datos local.');
            CLI::error('Use --confirm para confirmar o --dry-run para simular');
            return;
        }

        try {
            $products = $productModel
                ->where('recurrente_product_id IS NOT NULL')
                ->findAll();

            CLI::write('Productos con ID de Recurrente: ' . count($products), 'cyan');

            if ($dryRun) {
                CLI::write('=== MODO DRY-RUN ===', 'yellow');
                foreach ($products as $product) {
                    CLI::write("Se limpiaría: {$product['name']} (ID local: {$product['id']}, Recurrente: {$product['recurrente_product_id']})", 'cyan');
                }
                return;
            }

            // Limpiar todos los IDs de Recurrente
            $result = $productModel->set([
                'recurrente_product_id' => null,
                'recurrente_synced_at' => null,
                'recurrente_sync_status' => 'pending'
            ])->where('recurrente_product_id IS NOT NULL')->update();

            CLI::write("✓ Base de datos limpiada. Productos actualizados: " . count($products), 'green');

        } catch (\Exception $e) {
            CLI::error('Error limpiando base de datos: ' . $e->getMessage());
        }
    }

    private function resyncAllProducts($recurrenteService, $productModel, $dryRun)
    {
        CLI::write('=== PASO 4: Re-sincronizando todos los productos ===', 'yellow');
        CLI::newLine();

        if ($dryRun) {
            CLI::write('=== MODO DRY-RUN ===', 'yellow');
        }

        try {
            $products = $productModel
                ->where('is_active', 1)
                ->where('deleted_at IS NULL')
                ->findAll();

            CLI::write('Productos a sincronizar: ' . count($products), 'cyan');
            CLI::newLine();

            $synced = 0;
            $errors = 0;

            foreach ($products as $index => $product) {
                $num = $index + 1;
                $total = count($products);
                
                CLI::write("[{$num}/{$total}] Sincronizando: {$product['name']}", 'cyan');

                if ($dryRun) {
                    CLI::write("  → Se sincronizaría con Recurrente", 'yellow');
                    continue;
                }

                try {
                    $productSyncService = new \App\Services\ProductSyncService();
                    $result = $productSyncService->syncProductCreate($product['id']);
                    
                    if ($result) {
                        CLI::write("  ✓ Sincronizado correctamente", 'green');
                        $synced++;
                    } else {
                        CLI::write("  ✗ Error en sincronización", 'red');
                        $errors++;
                    }
                    
                    // Pausa para no sobrecargar la API
                    usleep(1000000); // 1 segundo
                    
                } catch (\Exception $e) {
                    CLI::write("  ✗ Error: " . $e->getMessage(), 'red');
                    $errors++;
                }
            }

            if (!$dryRun) {
                CLI::newLine();
                CLI::write('=== Resumen de Sincronización ===', 'yellow');
                CLI::write("Sincronizados: {$synced}", 'green');
                CLI::write("Errores: {$errors}", 'red');
            }

        } catch (\Exception $e) {
            CLI::error('Error re-sincronizando productos: ' . $e->getMessage());
        }
    }
}
