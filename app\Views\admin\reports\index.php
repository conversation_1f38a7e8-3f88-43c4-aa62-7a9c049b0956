<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<!-- Filtros Globales -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label class="form-label"><PERSON><PERSON> de Fechas</label>
                <input type="text" class="form-control" id="dateRange" placeholder="Seleccionar fechas">
            </div>
            <div class="col-md-3">
                <label class="form-label">Categoría</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">Todas las categorías</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Período</label>
                <select class="form-select" id="periodFilter">
                    <option value="daily">Diario</option>
                    <option value="weekly">Semanal</option>
                    <option value="monthly" selected>Mensual</option>
                    <option value="yearly">Anual</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter me-2"></i>Aplicar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Métricas Principales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-primary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">1</h3>
                        <small class="text-muted">Total Pedidos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-success text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">Q1,170</h3>
                        <small class="text-muted">Ingresos Totales</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">Q1,170</h3>
                        <small class="text-muted">Valor Promedio</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">1</h3>
                        <small class="text-muted">Clientes Únicos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos y Reportes -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Tendencia de Ventas</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportChart('sales')">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div style="height: 300px; display: flex; align-items: center; justify-content: center;">
                    <div class="text-center">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Gráfico de ventas se mostraría aquí</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Productos</h5>
                    <button class="btn btn-sm btn-outline-success" onclick="exportReport('products')">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                        <div class="fw-bold">1. iPhone 15 Pro</div>
                        <small class="text-muted">IPH15PRO128</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">1 vendido</div>
                        <small class="text-muted">Q1,170</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-chart-bar me-2"></i>Reportes y Análisis</h1>
        <div>
            <button class="btn btn-success me-2" onclick="exportReport('sales')">
                <i class="fas fa-download me-2"></i>Exportar Ventas
            </button>
            <button class="btn btn-primary" onclick="refreshAllReports()">
                <i class="fas fa-sync-alt me-2"></i>Actualizar
            </button>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para reportes
    function exportReport(type) {
        console.log('Exportar reporte:', type);
        alert('Funcionalidad de exportación en desarrollo');
    }

    function refreshAllReports() {
        console.log('Refrescar reportes');
        location.reload();
    }

    function applyFilters() {
        console.log('Aplicar filtros');
        // Aquí iría la lógica para aplicar los filtros
    }

    function exportChart(type) {
        console.log('Exportar gráfico:', type);
        alert('Funcionalidad de exportación de gráficos en desarrollo');
    }
</script>
<?= $this->endSection() ?>
