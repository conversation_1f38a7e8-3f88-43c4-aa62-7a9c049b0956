<?php

namespace App\Libraries;

use App\Services\WhatsAppService;
use App\Models\UserModel;
use App\Models\OrderModel;

class WhatsAppEventHandler
{
    protected $whatsappService;
    protected $userModel;
    protected $orderModel;

    public function __construct()
    {
        $this->whatsappService = new WhatsAppService();
        $this->userModel = new UserModel();
        $this->orderModel = new OrderModel();
    }

    /**
     * Manejar registro de nuevo usuario
     */
    public function handleUserRegistration($userData)
    {
        try {
            // Verificar que el usuario tenga teléfono
            if (empty($userData['phone'])) {
                log_message('info', 'Usuario registrado sin teléfono, no se enviará WhatsApp: ' . ($userData['email'] ?? 'sin email'));
                return;
            }

            // Enviar notificación de bienvenida
            $result = $this->whatsappService->sendCustomerRegistrationNotification($userData);
            
            if ($result['success']) {
                log_message('info', 'WhatsApp de bienvenida enviado a: ' . $userData['phone']);
            } else {
                log_message('error', 'Error enviando WhatsApp de bienvenida: ' . $result['error']);
            }

            // Inicializar preferencias de notificación para el usuario
            if (isset($userData['id'])) {
                $preferencesModel = new \App\Models\UserNotificationPreferencesModel();
                $preferencesModel->initializeUserPreferences($userData['id']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp handleUserRegistration: ' . $e->getMessage());
        }
    }

    /**
     * Manejar creación de nueva orden
     */
    public function handleOrderCreated($orderData)
    {
        try {
            // Obtener datos del cliente
            $customerData = $this->getCustomerData($orderData);
            
            if (!$customerData || empty($customerData['phone'])) {
                log_message('info', 'Orden creada sin teléfono de cliente, no se enviará WhatsApp: ' . ($orderData['order_number'] ?? 'sin número'));
                return;
            }

            // Enviar notificación de orden creada al cliente
            $customerResult = $this->whatsappService->sendOrderCreatedNotification($orderData, $customerData);

            if ($customerResult['success']) {
                log_message('info', 'WhatsApp de orden creada enviado al cliente: ' . $orderData['order_number']);
            } else {
                log_message('error', 'Error enviando WhatsApp al cliente: ' . $customerResult['error']);
            }

            // Enviar notificación a administradores
            $adminResult = $this->whatsappService->sendAdminOrderNotification($orderData, $customerData);

            if ($adminResult['success']) {
                log_message('info', 'WhatsApp de orden enviado a ' . $adminResult['sent_to'] . ' administradores: ' . $orderData['order_number']);
            } else {
                log_message('error', 'Error enviando WhatsApp a administradores: ' . $adminResult['error']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp handleOrderCreated: ' . $e->getMessage());
        }
    }

    /**
     * Manejar cambio de estado de orden
     */
    public function handleOrderStatusChange($orderData, $oldStatus, $newStatus)
    {
        try {
            // No enviar notificación si el estado no cambió realmente
            if ($oldStatus === $newStatus) {
                return;
            }

            // Obtener datos del cliente
            $customerData = $this->getCustomerData($orderData);
            
            if (!$customerData || empty($customerData['phone'])) {
                log_message('info', 'Cambio de estado sin teléfono de cliente, no se enviará WhatsApp: ' . ($orderData['order_number'] ?? 'sin número'));
                return;
            }

            // Enviar notificación de cambio de estado
            $result = $this->whatsappService->sendOrderStatusChangeNotification(
                $orderData, 
                $customerData, 
                $oldStatus, 
                $newStatus
            );
            
            if ($result['success']) {
                log_message('info', "WhatsApp de cambio de estado enviado: {$orderData['order_number']} ({$oldStatus} -> {$newStatus})");
            } else {
                log_message('error', 'Error enviando WhatsApp de cambio de estado: ' . $result['error']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp handleOrderStatusChange: ' . $e->getMessage());
        }
    }

    /**
     * Manejar producto agregado
     */
    public function handleProductAdded($productData)
    {
        try {
            // Enviar notificación a usuarios que tienen habilitada esta notificación
            $results = $this->whatsappService->sendProductAddedNotification($productData);
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }
            
            log_message('info', "WhatsApp producto agregado: {$successCount} enviados, {$errorCount} errores - Producto: " . ($productData['name'] ?? 'sin nombre'));

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp handleProductAdded: ' . $e->getMessage());
        }
    }

    /**
     * Manejar descuento en producto
     */
    public function handleProductDiscount($productData, $oldPrice, $newPrice)
    {
        try {
            // Solo enviar si realmente hay descuento
            if ($newPrice >= $oldPrice) {
                return;
            }

            // Enviar notificación a usuarios que tienen habilitada esta notificación
            $results = $this->whatsappService->sendProductDiscountNotification($productData, $oldPrice, $newPrice);
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }
            
            $discountAmount = $oldPrice - $newPrice;
            log_message('info', "WhatsApp descuento producto: {$successCount} enviados, {$errorCount} errores - Producto: " . ($productData['name'] ?? 'sin nombre') . " Descuento: Q{$discountAmount}");

        } catch (\Exception $e) {
            log_message('error', 'Error en WhatsApp handleProductDiscount: ' . $e->getMessage());
        }
    }

    /**
     * Obtener datos del cliente de una orden
     */
    protected function getCustomerData($orderData)
    {
        // Si ya tenemos los datos del cliente en la orden
        if (isset($orderData['customer_name']) && isset($orderData['customer_phone'])) {
            return [
                'id' => $orderData['customer_id'] ?? null,
                'name' => $orderData['customer_name'],
                'email' => $orderData['customer_email'] ?? '',
                'phone' => $orderData['customer_phone']
            ];
        }

        // Si tenemos customer_id, buscar en la base de datos
        if (isset($orderData['customer_id']) && $orderData['customer_id']) {
            $user = $this->userModel->find($orderData['customer_id']);
            if ($user) {
                return [
                    'id' => $user['id'],
                    'name' => $user['name'] ?? $user['first_name'] . ' ' . $user['last_name'],
                    'email' => $user['email'],
                    'phone' => $user['phone']
                ];
            }
        }

        return null;
    }

    /**
     * Método estático para registrar eventos
     */
    public static function registerEvents()
    {
        $handler = new self();

        // Registrar eventos usando CodeIgniter Events
        \CodeIgniter\Events\Events::on('user_registered', [$handler, 'handleUserRegistration']);
        \CodeIgniter\Events\Events::on('order_created', [$handler, 'handleOrderCreated']);
        \CodeIgniter\Events\Events::on('order_status_changed', [$handler, 'handleOrderStatusChange']);
        \CodeIgniter\Events\Events::on('product_added', [$handler, 'handleProductAdded']);
        \CodeIgniter\Events\Events::on('product_discount', [$handler, 'handleProductDiscount']);
    }

    /**
     * Método para disparar evento de registro de usuario
     */
    public static function triggerUserRegistered($userData)
    {
        \CodeIgniter\Events\Events::trigger('user_registered', $userData);
    }

    /**
     * Método para disparar evento de orden creada
     */
    public static function triggerOrderCreated($orderData)
    {
        \CodeIgniter\Events\Events::trigger('order_created', $orderData);
    }

    /**
     * Método para disparar evento de cambio de estado de orden
     */
    public static function triggerOrderStatusChanged($orderData, $oldStatus, $newStatus)
    {
        \CodeIgniter\Events\Events::trigger('order_status_changed', $orderData, $oldStatus, $newStatus);
    }

    /**
     * Método para disparar evento de producto agregado
     */
    public static function triggerProductAdded($productData)
    {
        \CodeIgniter\Events\Events::trigger('product_added', $productData);
    }

    /**
     * Método para disparar evento de descuento en producto
     */
    public static function triggerProductDiscount($productData, $oldPrice, $newPrice)
    {
        \CodeIgniter\Events\Events::trigger('product_discount', $productData, $oldPrice, $newPrice);
    }

    /**
     * Disparar evento de verificación de teléfono
     */
    public static function triggerPhoneVerification($userData)
    {
        try {
            // Verificar si el usuario tiene teléfono
            if (empty($userData['phone'])) {
                log_message('error', 'Usuario sin teléfono para verificación: ' . $userData['email']);
                return;
            }

            // Obtener plantilla de verificación
            $templateModel = new \App\Models\WhatsAppTemplateModel();
            $template = $templateModel->where('template_key', 'phone_verification')->where('is_active', 1)->first();

            if (!$template) {
                log_message('error', 'Plantilla de verificación no encontrada');
                return;
            }

            // Reemplazar variables en el mensaje
            $message = str_replace(
                ['{customer_name}', '{verification_code}'],
                [$userData['name'], $userData['verification_code']],
                $template['message_template']
            );

            // Enviar mensaje
            $whatsappService = new WhatsAppService();
            $result = $whatsappService->sendMessage($userData['phone'], $message, 'phone_verification', $userData['id']);

            if ($result) {
                log_message('info', 'Código de verificación enviado por WhatsApp a: ' . $userData['phone'] . ' - Código: ' . $userData['verification_code']);
            } else {
                log_message('error', 'Error al enviar código de verificación por WhatsApp a: ' . $userData['phone']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error enviando código de verificación: ' . $e->getMessage());
        }
    }
}
