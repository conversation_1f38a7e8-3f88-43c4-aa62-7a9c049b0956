<?php

namespace App\Controllers\Payment;

use App\Controllers\BaseController;

class RecurrenteController extends BaseController
{
    protected $orderModel;
    protected $paymentModel;

    public function __construct()
    {
        $this->orderModel = new \App\Models\OrderModel();
        $this->paymentModel = new \App\Models\PaymentModel();
    }

    /**
     * Página de éxito después del pago
     */
    public function success($orderId = null)
    {
        try {
            if (!$orderId) {
                return redirect()->to('/')->with('error', 'ID de pedido requerido');
            }

            // Obtener información del pedido
            $order = $this->orderModel->find($orderId);
            
            if (!$order) {
                return redirect()->to('/')->with('error', 'Pedido no encontrado');
            }

            // Obtener información del pago
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->orderBy('created_at', 'DESC')
                                         ->first();

            // Verificar estado del pago
            $paymentStatus = $payment['status'] ?? 'pending';
            
            // Datos para la vista
            $data = [
                'title' => 'Pago Exitoso - MrCell Guatemala',
                'order' => $order,
                'payment' => $payment,
                'payment_status' => $paymentStatus,
                'success_message' => $this->getSuccessMessage($paymentStatus),
                'next_steps' => $this->getNextSteps($paymentStatus)
            ];

            // Log del acceso a la página de éxito
            log_message('info', "Página de éxito de Recurrente accedida para pedido {$orderId} - Estado: {$paymentStatus}");

            return view('payment/recurrente/success', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en página de éxito de Recurrente: ' . $e->getMessage());
            return redirect()->to('/')->with('error', 'Error procesando la respuesta del pago');
        }
    }

    /**
     * Página de cancelación del pago
     */
    public function cancel($orderId = null)
    {
        try {
            if (!$orderId) {
                return redirect()->to('/')->with('error', 'ID de pedido requerido');
            }

            // Obtener información del pedido
            $order = $this->orderModel->find($orderId);
            
            if (!$order) {
                return redirect()->to('/')->with('error', 'Pedido no encontrado');
            }

            // Obtener información del pago
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->orderBy('created_at', 'DESC')
                                         ->first();

            // Marcar pago como cancelado si está pendiente
            if ($payment && $payment['status'] === 'pending') {
                $this->paymentModel->update($payment['id'], [
                    'status' => 'cancelled',
                    'notes' => 'Pago cancelado por el usuario'
                ]);

                // Actualizar pedido
                $this->orderModel->update($orderId, [
                    'payment_status' => 'cancelled',
                    'status' => 'cancelled',
                    'notes' => 'Pago cancelado por el usuario en Recurrente'
                ]);
            }

            // Datos para la vista
            $data = [
                'title' => 'Pago Cancelado - MrCell Guatemala',
                'order' => $order,
                'payment' => $payment,
                'cancel_message' => 'Su pago ha sido cancelado. No se ha realizado ningún cargo.',
                'retry_options' => $this->getRetryOptions($orderId)
            ];

            // Log de la cancelación
            log_message('info', "Pago de Recurrente cancelado para pedido {$orderId}");

            return view('payment/recurrente/cancel', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en página de cancelación de Recurrente: ' . $e->getMessage());
            return redirect()->to('/')->with('error', 'Error procesando la cancelación del pago');
        }
    }

    /**
     * Obtener mensaje de éxito según el estado del pago
     */
    private function getSuccessMessage($paymentStatus)
    {
        switch ($paymentStatus) {
            case 'completed':
                return '¡Pago completado exitosamente! Su pedido ha sido confirmado y será procesado pronto.';
            case 'pending':
                return 'Su pago está siendo procesado. Recibirá una confirmación por email una vez que se complete.';
            case 'processing':
                return 'Su pago está en proceso de verificación. Le notificaremos cuando esté confirmado.';
            default:
                return 'Gracias por su compra. Estamos verificando su pago y le notificaremos el estado.';
        }
    }

    /**
     * Obtener próximos pasos según el estado del pago
     */
    private function getNextSteps($paymentStatus)
    {
        $steps = [];
        
        switch ($paymentStatus) {
            case 'completed':
                $steps = [
                    'Recibirá un email de confirmación con los detalles de su pedido',
                    'Su pedido será preparado y enviado según el método de envío seleccionado',
                    'Podrá rastrear su pedido desde su cuenta o con el número de seguimiento'
                ];
                break;
                
            case 'pending':
            case 'processing':
                $steps = [
                    'Estamos verificando su pago con el banco',
                    'Le enviaremos un email de confirmación una vez completado',
                    'Si tiene dudas, puede contactarnos con el número de pedido'
                ];
                break;
                
            default:
                $steps = [
                    'Verifique su email para actualizaciones del estado',
                    'Contacte nuestro servicio al cliente si tiene dudas',
                    'Guarde el número de pedido para futuras referencias'
                ];
        }
        
        return $steps;
    }

    /**
     * Obtener opciones para reintentar el pago
     */
    private function getRetryOptions($orderId)
    {
        return [
            [
                'title' => 'Intentar nuevamente con Recurrente',
                'description' => 'Volver a procesar el pago con tarjeta de crédito/débito',
                'url' => site_url("checkout/payment/{$orderId}?method=recurrente"),
                'icon' => 'credit-card'
            ],
            [
                'title' => 'Elegir otro método de pago',
                'description' => 'Transferencia bancaria, PayPal u otros métodos disponibles',
                'url' => site_url("checkout/payment/{$orderId}"),
                'icon' => 'payment'
            ],
            [
                'title' => 'Contactar soporte',
                'description' => 'Obtener ayuda de nuestro equipo de atención al cliente',
                'url' => site_url('contact'),
                'icon' => 'support'
            ]
        ];
    }

    /**
     * Webhook de estado (endpoint adicional para verificaciones)
     */
    public function status($orderId = null)
    {
        try {
            if (!$orderId) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'ID de pedido requerido'
                ]);
            }

            $order = $this->orderModel->find($orderId);
            $payment = $this->paymentModel->where('order_id', $orderId)
                                         ->where('payment_method', 'recurrente')
                                         ->orderBy('created_at', 'DESC')
                                         ->first();

            return $this->response->setJSON([
                'status' => 'success',
                'data' => [
                    'order_id' => $orderId,
                    'order_status' => $order['status'] ?? 'not_found',
                    'payment_status' => $payment['status'] ?? 'not_found',
                    'payment_method' => 'recurrente'
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error consultando estado del pago'
            ]);
        }
    }
}
