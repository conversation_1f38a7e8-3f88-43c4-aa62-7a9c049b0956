<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: var(--white-color);
        padding: 100px 0;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-content h1 {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .hero-content p {
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.95;
    }

    /* Categories Section */
    .category-card {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        text-decoration: none;
        color: var(--gray-800);
        background: var(--white-color);
        padding: 2rem;
        text-align: center;
        height: 100%;
        display: block;
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        text-decoration: none;
        color: var(--gray-800);
        border-color: var(--primary-light);
    }

    .category-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .category-card:hover .category-icon {
        color: var(--primary-dark);
        transform: scale(1.1);
    }
        
    /* Product Cards */
    .product-card {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        background: var(--white-color);
        height: 100%;
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        border-color: var(--primary-light);
    }

    .product-image {
        height: 200px;
        object-fit: cover;
        width: 100%;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image {
        transform: scale(1.05);
    }

    .price {
        font-size: 1.25rem;
        font-weight: bold;
        color: var(--primary-color);
    }

    .old-price {
        text-decoration: line-through;
        color: var(--gray-500);
        font-size: 0.9rem;
    }

    /* Features Section */
    .features-section {
        background: linear-gradient(135deg, var(--gray-100) 0%, var(--white-color) 100%);
        padding: 4rem 0;
        margin-top: 4rem;
        border-top: 1px solid var(--gray-200);
    }

    .feature-icon {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .features-section .col-md-3:hover .feature-icon {
        color: var(--primary-dark);
        transform: scale(1.1);
    }

    /* Buttons */
    .btn-hero {
        background: var(--white-color);
        color: var(--primary-color);
        border: 2px solid var(--white-color);
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-hero:hover {
        background: transparent;
        color: var(--white-color);
        border-color: var(--white-color);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255,255,255,0.3);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1>Los mejores celulares en Guatemala</h1>
                    <p>Descubre la última tecnología en smartphones, accesorios y más. Envío gratis, garantía oficial y los mejores precios.</p>
                    <a href="<?= base_url('tienda') ?>" class="btn-hero">
                        <i class="fas fa-shopping-bag me-2"></i>Ver Productos
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="/logo.jpg" alt="MrCell Guatemala" class="img-fluid" style="max-height: 400px; filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));">
            </div>
        </div>
    </div>
</section>

    <!-- Categories Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Categorías Populares</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <a href="<?= base_url('tienda') ?>" class="category-card card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-mobile-alt category-icon"></i>
                            <h5 class="card-title">Smartphones</h5>
                            <p class="card-text">Los últimos modelos de iPhone, Samsung, Xiaomi y más</p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="<?= base_url('tienda') ?>" class="category-card card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-headphones category-icon"></i>
                            <h5 class="card-title">Accesorios</h5>
                            <p class="card-text">Fundas, cargadores, audífonos y más accesorios</p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="<?= base_url('tienda') ?>" class="category-card card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-tablet-alt category-icon"></i>
                            <h5 class="card-title">Tablets</h5>
                            <p class="card-text">iPad, Samsung Galaxy Tab y más tablets</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-3 text-center">
                    <i class="fas fa-shipping-fast feature-icon"></i>
                    <h5>Envío Gratis</h5>
                    <p>En compras mayores a Q500</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-shield-alt feature-icon"></i>
                    <h5>Garantía Oficial</h5>
                    <p>Todos nuestros productos tienen garantía</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-credit-card feature-icon"></i>
                    <h5>Pago Seguro</h5>
                    <p>Múltiples métodos de pago disponibles</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-headset feature-icon"></i>
                    <h5>Soporte 24/7</h5>
                    <p>Atención al cliente siempre disponible</p>
                </div>
            </div>
        </div>
    </section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Homepage specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add to cart functionality for featured products
        document.querySelectorAll('.btn-primary').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.querySelector('.fa-cart-plus')) {
                    e.preventDefault();
                    // Add to cart logic here
                    if (window.showCartModal) {
                        window.showCartModal('Producto', 1, 0);
                    } else {
                        alert('Producto agregado al carrito');
                    }

                    // Update cart count
                    let currentCount = parseInt(localStorage.getItem('cart_count') || 0);
                    localStorage.setItem('cart_count', currentCount + 1);
                    updateCartCount();
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>
