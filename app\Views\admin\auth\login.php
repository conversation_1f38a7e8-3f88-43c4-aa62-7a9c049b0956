<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Login Admin - MrCell' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Pa<PERSON>a <PERSON> - <PERSON>, Negro, Blanco, Gris */
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --success-color: #059669;        /* Verde para éxito */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris medio claro */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Negro gris */
            --gray-900: #111827;             /* Negro */
        }
        
        body {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--gray-800) 50%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .admin-login-card {
            background: var(--white-color);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 20px;
            position: relative;
            z-index: 1;
            border: 1px solid var(--gray-200);
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white-color);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        }
        
        .admin-logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            position: relative;
            z-index: 1;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background-color: var(--white-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
            background-color: var(--white-color);
        }

        .form-floating > label {
            color: var(--gray-500);
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color);
        }
        
        .btn-admin-login {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-admin-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-admin-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            color: var(--white-color);
        }

        .btn-admin-login:hover::before {
            left: 100%;
        }
        
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
        }
        
        .input-group-password {
            position: relative;
        }
        
        .security-notice {
            background: var(--light-color);
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            border: 1px solid var(--gray-200);
            color: var(--gray-700);
        }

        .back-to-site {
            text-align: center;
            padding: 20px;
            background: var(--light-color);
            color: var(--gray-600);
        }

        .back-to-site a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .back-to-site a:hover {
            color: var(--primary-dark);
        }

        /* Estilos para alertas */
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
            padding: 15px 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--primary-dark));
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color), #0ea5e9);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
        }
    </style>
</head>
<body>

    <div class="admin-login-card">
        <div class="admin-header">
            <div class="admin-logo">
                <img src="/logo.jpg" alt="MrCell Logo" style="width: 50px; height: 50px; object-fit: contain;">
            </div>
            <h3>Panel Administrativo</h3>
            <p class="mb-0">MrCell Guatemala</p>
        </div>

        <div class="p-4">
            <!-- Mensajes de error/éxito -->
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Aviso de Seguridad -->
            <div class="security-notice">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle text-primary me-2"></i>
                    <small><strong>Acceso Restringido:</strong> Solo personal autorizado</small>
                </div>
            </div>

            <!-- ULTRA SIMPLE FORM - GUARANTEED TO WORK -->
            <form method="POST" action="<?= base_url('admin/login') ?>">
                <?= csrf_field() ?>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                        <i class="fas fa-user-shield"></i> Email Administrativo
                    </label>
                    <input type="email" name="email" required
                           style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;"
                           placeholder="<EMAIL>" value="<?= old('email') ?>">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                        <i class="fas fa-lock"></i> Contraseña
                    </label>
                    <input type="password" name="password" required
                           style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;"
                           placeholder="Contraseña administrativa">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" name="remember" style="margin-right: 8px;">
                        Mantener sesión activa
                    </label>
                </div>

                <button type="submit"
                        style="width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; font-weight: bold; cursor: pointer;">
                    <i class="fas fa-sign-in-alt"></i> Acceder al Panel
                </button>
            </form>


            <!-- Información Adicional -->
            <div class="mt-4 text-center">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    Sesión válida por 8 horas
                </small>
            </div>
        </div>

        <div class="back-to-site">
            <a href="<?= base_url() ?>" class="text-decoration-none text-muted">
                <i class="fas fa-arrow-left me-1"></i>Volver al sitio web
            </a>
        </div>
    </div>

    <script>
        // MINIMAL JAVASCRIPT - NO BOOTSTRAP INTERFERENCE
        console.log('Admin login page loaded - pure HTML form submission');
    </script>
</body>
</html>
