<?php

namespace App\Libraries;

/**
 * Gestor de Logística y Envíos Guatemala
 * Sistema completo para empresas de envío guatemaltecas: CargoExpreso, GuatEx, Forza
 */
class ShippingManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('SHIPPING_ENABLED', true),
            'default_company' => env('DEFAULT_SHIPPING_COMPANY', 'cargoexpreso'),
            'free_shipping_threshold' => env('FREE_SHIPPING_THRESHOLD', 200), // Q200
            'insurance_enabled' => env('SHIPPING_INSURANCE_ENABLED', true),
            'insurance_rate' => env('SHIPPING_INSURANCE_RATE', 0.02), // 2%
            'cash_on_delivery' => env('CASH_ON_DELIVERY_ENABLED', true),
            'cod_fee' => env('COD_FEE', 10), // Q10
            'tracking_enabled' => env('SHIPPING_TRACKING_ENABLED', true),
            'auto_assign_company' => env('AUTO_ASSIGN_SHIPPING_COMPANY', true),
            'cache_ttl' => env('SHIPPING_CACHE_TTL', 3600)
        ];
        
        $this->createShippingTables();
        $this->initializeGuatemalaData();
    }
    
    /**
     * Calcular costo de envío
     */
    public function calculateShippingCost(array $orderData): array
    {
        try {
            if (!$this->config['enabled']) {
                return ['success' => false, 'error' => 'Shipping disabled'];
            }
            
            $destination = $orderData['destination'] ?? [];
            $weight = $orderData['weight'] ?? 1;
            $value = $orderData['value'] ?? 0;
            $items = $orderData['items'] ?? [];
            
            // Verificar envío gratis
            if ($value >= $this->config['free_shipping_threshold']) {
                return [
                    'success' => true,
                    'free_shipping' => true,
                    'options' => [[
                        'company' => 'free',
                        'name' => 'Envío Gratis',
                        'cost' => 0,
                        'estimated_days' => '3-5 días hábiles',
                        'description' => 'Envío gratis por compra mayor a Q' . $this->config['free_shipping_threshold']
                    ]]
                ];
            }
            
            // Obtener opciones de envío disponibles
            $shippingOptions = $this->getShippingOptions($destination, $weight, $value);
            
            if (empty($shippingOptions)) {
                return [
                    'success' => false,
                    'error' => 'No shipping options available for this destination'
                ];
            }
            
            return [
                'success' => true,
                'free_shipping' => false,
                'options' => $shippingOptions,
                'insurance_available' => $this->config['insurance_enabled'],
                'cod_available' => $this->config['cash_on_delivery']
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Shipping cost calculation error: " . $e->getMessage(), $orderData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Crear envío
     */
    public function createShipment(array $shipmentData): array
    {
        try {
            $data = [
                'order_id' => $shipmentData['order_id'],
                'shipping_company' => $shipmentData['shipping_company'],
                'service_type' => $shipmentData['service_type'] ?? 'standard',
                'recipient_name' => $shipmentData['recipient_name'],
                'recipient_phone' => $shipmentData['recipient_phone'],
                'recipient_email' => $shipmentData['recipient_email'] ?? '',
                'destination_department' => $shipmentData['destination_department'],
                'destination_municipality' => $shipmentData['destination_municipality'],
                'destination_address' => $shipmentData['destination_address'],
                'destination_reference' => $shipmentData['destination_reference'] ?? '',
                'weight' => $shipmentData['weight'],
                'declared_value' => $shipmentData['declared_value'],
                'shipping_cost' => $shipmentData['shipping_cost'],
                'insurance_cost' => $shipmentData['insurance_cost'] ?? 0,
                'cod_fee' => $shipmentData['cod_fee'] ?? 0,
                'total_cost' => $shipmentData['shipping_cost'] + ($shipmentData['insurance_cost'] ?? 0) + ($shipmentData['cod_fee'] ?? 0),
                'cash_on_delivery' => $shipmentData['cash_on_delivery'] ?? 0,
                'cod_amount' => $shipmentData['cod_amount'] ?? 0,
                'special_instructions' => $shipmentData['special_instructions'] ?? '',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            // Generar número de guía único
            $data['tracking_number'] = $this->generateTrackingNumber($data['shipping_company']);
            
            $shipmentId = $this->db->table('shipments')->insert($data);
            
            if ($shipmentId) {
                // Actualizar orden con información de envío
                $this->db->table('orders')
                        ->where('id', $data['order_id'])
                        ->update([
                            'shipping_status' => 'pending',
                            'tracking_number' => $data['tracking_number'],
                            'shipping_company' => $data['shipping_company'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                
                // Registrar evento de envío
                $this->recordShippingEvent($shipmentId, 'created', 'Envío creado y asignado a ' . $this->getCompanyName($data['shipping_company']));
                
                $this->logger->info("Shipment created", [
                    'shipment_id' => $shipmentId,
                    'order_id' => $data['order_id'],
                    'tracking_number' => $data['tracking_number'],
                    'company' => $data['shipping_company']
                ]);
                
                return [
                    'success' => true,
                    'shipment_id' => $shipmentId,
                    'tracking_number' => $data['tracking_number'],
                    'estimated_delivery' => $this->calculateEstimatedDelivery($data['shipping_company'], $data['destination_department'])
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create shipment'];
            
        } catch (\Exception $e) {
            $this->logger->error("Shipment creation error: " . $e->getMessage(), $shipmentData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Actualizar estado de envío
     */
    public function updateShipmentStatus(int $shipmentId, string $status, string $notes = ''): array
    {
        try {
            $validStatuses = ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed', 'returned'];
            
            if (!in_array($status, $validStatuses)) {
                throw new \Exception("Invalid status: $status");
            }
            
            $shipment = $this->getShipment($shipmentId);
            if (!$shipment) {
                throw new \Exception("Shipment not found: $shipmentId");
            }
            
            // Actualizar estado
            $updateData = [
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if ($status === 'delivered') {
                $updateData['delivered_at'] = date('Y-m-d H:i:s');
            }
            
            $this->db->table('shipments')
                    ->where('id', $shipmentId)
                    ->update($updateData);
            
            // Actualizar orden
            $orderStatus = $this->mapShipmentStatusToOrder($status);
            $this->db->table('orders')
                    ->where('id', $shipment['order_id'])
                    ->update([
                        'shipping_status' => $orderStatus,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            
            // Registrar evento
            $this->recordShippingEvent($shipmentId, $status, $notes ?: $this->getStatusDescription($status));
            
            // Enviar notificación al cliente si está entregado
            if ($status === 'delivered') {
                $this->sendDeliveryNotification($shipment);
            }
            
            $this->logger->info("Shipment status updated", [
                'shipment_id' => $shipmentId,
                'old_status' => $shipment['status'],
                'new_status' => $status,
                'notes' => $notes
            ]);
            
            return [
                'success' => true,
                'old_status' => $shipment['status'],
                'new_status' => $status
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener información de seguimiento
     */
    public function trackShipment(string $trackingNumber): array
    {
        try {
            $shipment = $this->getShipmentByTracking($trackingNumber);
            
            if (!$shipment) {
                return [
                    'success' => false,
                    'error' => 'Tracking number not found'
                ];
            }
            
            // Obtener eventos de seguimiento
            $events = $this->getShippingEvents($shipment['id']);
            
            // Obtener información de la orden
            $order = $this->db->table('orders')
                             ->where('id', $shipment['order_id'])
                             ->get()
                             ->getRowArray();
            
            return [
                'success' => true,
                'tracking_number' => $trackingNumber,
                'shipment' => $shipment,
                'order' => $order,
                'events' => $events,
                'estimated_delivery' => $this->calculateEstimatedDelivery($shipment['shipping_company'], $shipment['destination_department']),
                'company_info' => $this->getCompanyInfo($shipment['shipping_company'])
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener tarifas de envío por empresa y departamento
     */
    public function getShippingRates(): array
    {
        try {
            $rates = $this->db->table('shipping_rates sr')
                             ->select('sr.*, sc.name as company_name, d.name as department_name')
                             ->join('shipping_companies sc', 'sc.id = sr.company_id')
                             ->join('guatemala_departments d', 'd.id = sr.department_id')
                             ->where('sr.is_active', 1)
                             ->orderBy('sc.name, d.name')
                             ->get()
                             ->getResultArray();
            
            // Agrupar por empresa
            $groupedRates = [];
            foreach ($rates as $rate) {
                $companyName = $rate['company_name'];
                if (!isset($groupedRates[$companyName])) {
                    $groupedRates[$companyName] = [];
                }
                $groupedRates[$companyName][] = $rate;
            }
            
            return [
                'success' => true,
                'rates' => $groupedRates,
                'total_rates' => count($rates)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Actualizar tarifa de envío
     */
    public function updateShippingRate(int $rateId, array $rateData): array
    {
        try {
            $updateData = [
                'base_cost' => $rateData['base_cost'],
                'cost_per_kg' => $rateData['cost_per_kg'] ?? 0,
                'max_weight' => $rateData['max_weight'] ?? 50,
                'estimated_days_min' => $rateData['estimated_days_min'] ?? 1,
                'estimated_days_max' => $rateData['estimated_days_max'] ?? 5,
                'is_active' => $rateData['is_active'] ?? 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $result = $this->db->table('shipping_rates')
                              ->where('id', $rateId)
                              ->update($updateData);
            
            if ($result) {
                // Limpiar cache
                $this->cache->delete('shipping_rates');
                
                return ['success' => true];
            }
            
            return ['success' => false, 'error' => 'Failed to update rate'];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de envíos
     */
    public function getShippingStats(int $days = 30): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Total de envíos
            $totalShipments = $this->db->table('shipments')
                                      ->where('created_at >=', $dateFrom)
                                      ->countAllResults();
            
            // Envíos por estado
            $shipmentsByStatus = $this->db->query("
                SELECT status, COUNT(*) as count
                FROM shipments
                WHERE created_at >= ?
                GROUP BY status
                ORDER BY count DESC
            ", [$dateFrom])->getResultArray();
            
            // Envíos por empresa
            $shipmentsByCompany = $this->db->query("
                SELECT sc.name, COUNT(s.id) as count, AVG(s.shipping_cost) as avg_cost
                FROM shipments s
                JOIN shipping_companies sc ON sc.code = s.shipping_company
                WHERE s.created_at >= ?
                GROUP BY s.shipping_company, sc.name
                ORDER BY count DESC
            ", [$dateFrom])->getResultArray();
            
            // Envíos por departamento
            $shipmentsByDepartment = $this->db->query("
                SELECT destination_department, COUNT(*) as count
                FROM shipments
                WHERE created_at >= ?
                GROUP BY destination_department
                ORDER BY count DESC
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Tiempo promedio de entrega
            $avgDeliveryTime = $this->db->query("
                SELECT AVG(DATEDIFF(delivered_at, created_at)) as avg_days
                FROM shipments
                WHERE status = 'delivered' 
                AND created_at >= ?
                AND delivered_at IS NOT NULL
            ", [$dateFrom])->getRowArray()['avg_days'] ?? 0;
            
            // Ingresos por envíos
            $shippingRevenue = $this->db->table('shipments')
                                       ->where('created_at >=', $dateFrom)
                                       ->selectSum('total_cost')
                                       ->get()
                                       ->getRowArray()['total_cost'] ?? 0;
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => [
                    'total_shipments' => $totalShipments,
                    'by_status' => $shipmentsByStatus,
                    'by_company' => $shipmentsByCompany,
                    'by_department' => $shipmentsByDepartment,
                    'avg_delivery_time' => round($avgDeliveryTime, 1),
                    'shipping_revenue' => $shippingRevenue,
                    'delivery_success_rate' => $this->calculateDeliverySuccessRate($dateFrom)
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getShippingOptions(array $destination, float $weight, float $value): array
    {
        $departmentId = $this->getDepartmentId($destination['department'] ?? '');
        
        if (!$departmentId) {
            return [];
        }
        
        $rates = $this->db->table('shipping_rates sr')
                         ->select('sr.*, sc.name, sc.code, sc.description')
                         ->join('shipping_companies sc', 'sc.id = sr.company_id')
                         ->where('sr.department_id', $departmentId)
                         ->where('sr.is_active', 1)
                         ->where('sc.is_active', 1)
                         ->where('sr.max_weight >=', $weight)
                         ->get()
                         ->getResultArray();
        
        $options = [];
        foreach ($rates as $rate) {
            $cost = $rate['base_cost'] + ($weight * $rate['cost_per_kg']);
            
            $options[] = [
                'company' => $rate['code'],
                'name' => $rate['name'],
                'description' => $rate['description'],
                'cost' => $cost,
                'estimated_days' => $rate['estimated_days_min'] . '-' . $rate['estimated_days_max'] . ' días hábiles',
                'max_weight' => $rate['max_weight'],
                'insurance_available' => true,
                'cod_available' => true
            ];
        }
        
        return $options;
    }
    
    private function generateTrackingNumber(string $company): string
    {
        $prefix = strtoupper(substr($company, 0, 3));
        $timestamp = date('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
    
    private function getCompanyName(string $code): string
    {
        $companies = [
            'cargoexpreso' => 'Cargo Expreso',
            'guatex' => 'GuatEx',
            'forza' => 'Forza'
        ];
        
        return $companies[$code] ?? $code;
    }
    
    private function calculateEstimatedDelivery(string $company, string $department): string
    {
        $rate = $this->db->table('shipping_rates sr')
                        ->join('shipping_companies sc', 'sc.id = sr.company_id')
                        ->join('guatemala_departments d', 'd.id = sr.department_id')
                        ->where('sc.code', $company)
                        ->where('d.name', $department)
                        ->get()
                        ->getRowArray();
        
        if ($rate) {
            $minDays = $rate['estimated_days_min'];
            $maxDays = $rate['estimated_days_max'];
            $estimatedDate = date('d/m/Y', strtotime("+$maxDays days"));
            
            return "$minDays-$maxDays días hábiles (aprox. $estimatedDate)";
        }
        
        return '3-5 días hábiles';
    }
    
    private function getShipment(int $shipmentId): ?array
    {
        return $this->db->table('shipments')
                       ->where('id', $shipmentId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getShipmentByTracking(string $trackingNumber): ?array
    {
        return $this->db->table('shipments')
                       ->where('tracking_number', $trackingNumber)
                       ->get()
                       ->getRowArray();
    }
    
    private function recordShippingEvent(int $shipmentId, string $event, string $description): void
    {
        $this->db->table('shipping_events')->insert([
            'shipment_id' => $shipmentId,
            'event' => $event,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    private function getShippingEvents(int $shipmentId): array
    {
        return $this->db->table('shipping_events')
                       ->where('shipment_id', $shipmentId)
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function mapShipmentStatusToOrder(string $shipmentStatus): string
    {
        $mapping = [
            'pending' => 'pending',
            'picked_up' => 'shipped',
            'in_transit' => 'shipped',
            'out_for_delivery' => 'shipped',
            'delivered' => 'delivered',
            'failed' => 'failed',
            'returned' => 'returned'
        ];
        
        return $mapping[$shipmentStatus] ?? 'pending';
    }
    
    private function getStatusDescription(string $status): string
    {
        $descriptions = [
            'pending' => 'Envío pendiente de recolección',
            'picked_up' => 'Paquete recolectado por la empresa de envío',
            'in_transit' => 'Paquete en tránsito',
            'out_for_delivery' => 'Paquete en reparto',
            'delivered' => 'Paquete entregado exitosamente',
            'failed' => 'Intento de entrega fallido',
            'returned' => 'Paquete devuelto al remitente'
        ];
        
        return $descriptions[$status] ?? $status;
    }
    
    private function sendDeliveryNotification(array $shipment): void
    {
        // Implementar notificación de entrega
        $this->logger->info("Delivery notification sent", [
            'shipment_id' => $shipment['id'],
            'tracking_number' => $shipment['tracking_number']
        ]);
    }
    
    private function getCompanyInfo(string $code): array
    {
        return $this->db->table('shipping_companies')
                       ->where('code', $code)
                       ->get()
                       ->getRowArray() ?? [];
    }
    
    private function getDepartmentId(string $departmentName): ?int
    {
        $result = $this->db->table('guatemala_departments')
                          ->where('name', $departmentName)
                          ->get()
                          ->getRowArray();
        
        return $result['id'] ?? null;
    }
    
    private function calculateDeliverySuccessRate(string $dateFrom): float
    {
        $totalDeliveries = $this->db->table('shipments')
                                   ->where('created_at >=', $dateFrom)
                                   ->whereIn('status', ['delivered', 'failed', 'returned'])
                                   ->countAllResults();
        
        $successfulDeliveries = $this->db->table('shipments')
                                        ->where('created_at >=', $dateFrom)
                                        ->where('status', 'delivered')
                                        ->countAllResults();
        
        return $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;
    }
    
    /**
     * Crear tablas de envíos
     */
    private function createShippingTables(): void
    {
        try {
            // Tabla de empresas de envío
            $this->db->query("
                CREATE TABLE IF NOT EXISTS shipping_companies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    contact_phone VARCHAR(20),
                    contact_email VARCHAR(255),
                    website VARCHAR(255),
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            
            // Tabla de departamentos de Guatemala
            $this->db->query("
                CREATE TABLE IF NOT EXISTS guatemala_departments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(10) NOT NULL,
                    is_active TINYINT(1) DEFAULT 1
                )
            ");
            
            // Tabla de tarifas de envío
            $this->db->query("
                CREATE TABLE IF NOT EXISTS shipping_rates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    company_id INT NOT NULL,
                    department_id INT NOT NULL,
                    service_type VARCHAR(50) DEFAULT 'standard',
                    base_cost DECIMAL(10,2) NOT NULL,
                    cost_per_kg DECIMAL(10,2) DEFAULT 0,
                    max_weight DECIMAL(10,2) DEFAULT 50,
                    estimated_days_min INT DEFAULT 1,
                    estimated_days_max INT DEFAULT 5,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (company_id) REFERENCES shipping_companies(id),
                    FOREIGN KEY (department_id) REFERENCES guatemala_departments(id),
                    UNIQUE KEY unique_rate (company_id, department_id, service_type)
                )
            ");
            
            // Tabla de envíos
            $this->db->query("
                CREATE TABLE IF NOT EXISTS shipments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    tracking_number VARCHAR(50) NOT NULL UNIQUE,
                    shipping_company VARCHAR(50) NOT NULL,
                    service_type VARCHAR(50) DEFAULT 'standard',
                    recipient_name VARCHAR(255) NOT NULL,
                    recipient_phone VARCHAR(20) NOT NULL,
                    recipient_email VARCHAR(255),
                    destination_department VARCHAR(100) NOT NULL,
                    destination_municipality VARCHAR(100),
                    destination_address TEXT NOT NULL,
                    destination_reference TEXT,
                    weight DECIMAL(10,2) NOT NULL,
                    declared_value DECIMAL(10,2) NOT NULL,
                    shipping_cost DECIMAL(10,2) NOT NULL,
                    insurance_cost DECIMAL(10,2) DEFAULT 0,
                    cod_fee DECIMAL(10,2) DEFAULT 0,
                    total_cost DECIMAL(10,2) NOT NULL,
                    cash_on_delivery TINYINT(1) DEFAULT 0,
                    cod_amount DECIMAL(10,2) DEFAULT 0,
                    special_instructions TEXT,
                    status ENUM('pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed', 'returned') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    delivered_at TIMESTAMP NULL,
                    INDEX idx_order_id (order_id),
                    INDEX idx_tracking_number (tracking_number),
                    INDEX idx_status (status),
                    INDEX idx_shipping_company (shipping_company),
                    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
                )
            ");
            
            // Tabla de eventos de envío
            $this->db->query("
                CREATE TABLE IF NOT EXISTS shipping_events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    shipment_id INT NOT NULL,
                    event VARCHAR(100) NOT NULL,
                    description TEXT,
                    location VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_shipment_id (shipment_id),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Shipping tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Inicializar datos de Guatemala
     */
    private function initializeGuatemalaData(): void
    {
        try {
            // Insertar empresas de envío guatemaltecas
            $companies = [
                ['code' => 'cargoexpreso', 'name' => 'Cargo Expreso', 'description' => 'Empresa líder en envíos a nivel nacional'],
                ['code' => 'guatex', 'name' => 'GuatEx', 'description' => 'Servicio de paquetería y encomiendas'],
                ['code' => 'forza', 'name' => 'Forza', 'description' => 'Logística y distribución nacional']
            ];
            
            foreach ($companies as $company) {
                $this->db->query("
                    INSERT IGNORE INTO shipping_companies (code, name, description) 
                    VALUES (?, ?, ?)
                ", [$company['code'], $company['name'], $company['description']]);
            }
            
            // Insertar departamentos de Guatemala
            $departments = [
                ['name' => 'Guatemala', 'code' => 'GT'],
                ['name' => 'Sacatepéquez', 'code' => 'SA'],
                ['name' => 'Chimaltenango', 'code' => 'CH'],
                ['name' => 'Escuintla', 'code' => 'ES'],
                ['name' => 'Santa Rosa', 'code' => 'SR'],
                ['name' => 'Sololá', 'code' => 'SO'],
                ['name' => 'Totonicapán', 'code' => 'TO'],
                ['name' => 'Quetzaltenango', 'code' => 'QZ'],
                ['name' => 'Suchitepéquez', 'code' => 'SU'],
                ['name' => 'Retalhuleu', 'code' => 'RE'],
                ['name' => 'San Marcos', 'code' => 'SM'],
                ['name' => 'Huehuetenango', 'code' => 'HU'],
                ['name' => 'Quiché', 'code' => 'QI'],
                ['name' => 'Baja Verapaz', 'code' => 'BV'],
                ['name' => 'Alta Verapaz', 'code' => 'AV'],
                ['name' => 'Petén', 'code' => 'PE'],
                ['name' => 'Izabal', 'code' => 'IZ'],
                ['name' => 'Zacapa', 'code' => 'ZA'],
                ['name' => 'Chiquimula', 'code' => 'CQ'],
                ['name' => 'Jalapa', 'code' => 'JA'],
                ['name' => 'Jutiapa', 'code' => 'JU'],
                ['name' => 'El Progreso', 'code' => 'EP']
            ];
            
            foreach ($departments as $dept) {
                $this->db->query("
                    INSERT IGNORE INTO guatemala_departments (name, code) 
                    VALUES (?, ?)
                ", [$dept['name'], $dept['code']]);
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Guatemala data initialization failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'default_company' => $this->config['default_company'],
            'free_shipping_threshold' => $this->config['free_shipping_threshold'],
            'insurance_enabled' => $this->config['insurance_enabled'],
            'cash_on_delivery' => $this->config['cash_on_delivery'],
            'tracking_enabled' => $this->config['tracking_enabled']
        ];
    }
}
