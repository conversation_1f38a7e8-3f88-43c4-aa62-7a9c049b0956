<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AutomationManager;
use App\Libraries\SystemMonitor;
use App\Libraries\WhatsAppManager;
use App\Libraries\WishlistManager;
use App\Libraries\CartManager;
use App\Libraries\MobileManager;

/**
 * Controlador de Pruebas del Sistema
 * Verifica que todas las funcionalidades estén operativas
 */
class TestController extends BaseController
{
    /**
     * Dashboard de pruebas del sistema
     */
    public function index()
    {
        $data = [
            'title' => 'Pruebas del Sistema - MrCell Guatemala',
            'system_status' => $this->getSystemStatus(),
            'test_categories' => $this->getTestCategories()
        ];

        return view('test/dashboard', $data);
    }

    /**
     * Prueba simple de métodos de pago
     */
    public function paymentMethods()
    {
        try {
            $db = \Config\Database::connect();
            $paymentMethods = $db->query("
                SELECT id, name, slug, description, type, icon, instructions, is_active
                FROM payment_methods
                ORDER BY sort_order, name
            ")->getResultArray();

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Payment methods retrieved',
                'data' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error retrieving payment methods',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prueba de checkout simple
     */
    public function checkoutTest()
    {
        try {
            $db = \Config\Database::connect();

            // Obtener métodos de pago activos (igual que CheckoutController)
            $paymentMethods = $db->query("
                SELECT id, name, slug, description, type, icon, instructions
                FROM payment_methods
                WHERE is_active = 1
                ORDER BY sort_order, name
            ")->getResultArray();

            $html = '<!DOCTYPE html>
<html>
<head>
    <title>Test Checkout</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .payment-method { border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Checkout - Métodos de Pago</h1>

    <div class="debug">
        <h3>DEBUG INFO:</h3>
        <p>Total métodos de pago activos: ' . count($paymentMethods) . '</p>
        <p>Variable payment_methods: ' . (empty($paymentMethods) ? 'EMPTY' : 'NOT EMPTY') . '</p>
    </div>';

            if (!empty($paymentMethods)) {
                $html .= '<h2>Métodos de Pago Disponibles:</h2>';
                foreach ($paymentMethods as $method) {
                    $html .= '<div class="payment-method">
                        <h3>' . htmlspecialchars($method['name']) . '</h3>
                        <p><strong>Slug:</strong> ' . htmlspecialchars($method['slug']) . '</p>
                        <p><strong>Tipo:</strong> ' . htmlspecialchars($method['type']) . '</p>
                        <p><strong>Descripción:</strong> ' . htmlspecialchars($method['description']) . '</p>
                        <p><strong>Icono:</strong> ' . htmlspecialchars($method['icon']) . '</p>
                    </div>';
                }
            } else {
                $html .= '<div class="payment-method">
                    <h3>❌ No hay métodos de pago disponibles</h3>
                    <p>Por favor contacta al administrador.</p>
                </div>';
            }

            $html .= '</body></html>';

            return $this->response->setBody($html);

        } catch (\Exception $e) {
            return $this->response->setBody('Error: ' . $e->getMessage());
        }
    }

    /**
     * Ejecutar todas las pruebas del sistema
     */
    public function runAllTests()
    {
        try {
            set_time_limit(300); // 5 minutos

            $results = [
                'database' => $this->testDatabase(),
                'whatsapp' => $this->testWhatsApp(),
                'automation' => $this->testAutomation(),
                'monitoring' => $this->testMonitoring(),
                'api' => $this->testAPI()
            ];

            // Calcular estadísticas generales
            $totalTests = 0;
            $passedTests = 0;

            foreach ($results as $category => $tests) {
                foreach ($tests['tests'] as $test) {
                    $totalTests++;
                    if ($test['status'] === 'pass') {
                        $passedTests++;
                    }
                }
            }

            $results['summary'] = [
                'total_tests' => $totalTests,
                'passed_tests' => $passedTests,
                'failed_tests' => $totalTests - $passedTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 2)
            ];

            return $this->response->setJSON([
                'success' => true,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Probar conexión y estructura de base de datos
     */
    private function testDatabase(): array
    {
        $tests = [];

        try {
            $db = \Config\Database::connect();

            // Test 1: Conexión a base de datos
            try {
                $db->query('SELECT 1');
                $tests[] = [
                    'name' => 'Conexión a Base de Datos',
                    'status' => 'pass',
                    'message' => 'Conexión exitosa'
                ];
            } catch (\Exception $e) {
                $tests[] = [
                    'name' => 'Conexión a Base de Datos',
                    'status' => 'fail',
                    'message' => 'Error de conexión: ' . $e->getMessage()
                ];
            }

            // Test 2: Verificar tablas del sistema
            $requiredTables = [
                'whatsapp_templates', 'whatsapp_message_log', 'price_history',
                'user_preferences', 'push_subscriptions', 'automation_log',
                'system_settings', 'notification_log', 'system_alerts'
            ];

            $missingTables = [];
            foreach ($requiredTables as $table) {
                try {
                    $db->query("SELECT 1 FROM `$table` LIMIT 1");
                } catch (\Exception $e) {
                    $missingTables[] = $table;
                }
            }

            if (empty($missingTables)) {
                $tests[] = [
                    'name' => 'Tablas del Sistema',
                    'status' => 'pass',
                    'message' => 'Todas las tablas requeridas existen'
                ];
            } else {
                $tests[] = [
                    'name' => 'Tablas del Sistema',
                    'status' => 'fail',
                    'message' => 'Tablas faltantes: ' . implode(', ', $missingTables)
                ];
            }

            // Test 3: Configuraciones del sistema
            $settingsCount = $db->table('system_settings')->countAllResults();
            if ($settingsCount > 0) {
                $tests[] = [
                    'name' => 'Configuraciones del Sistema',
                    'status' => 'pass',
                    'message' => "$settingsCount configuraciones encontradas"
                ];
            } else {
                $tests[] = [
                    'name' => 'Configuraciones del Sistema',
                    'status' => 'fail',
                    'message' => 'No se encontraron configuraciones'
                ];
            }

        } catch (\Exception $e) {
            $tests[] = [
                'name' => 'Error General de Base de Datos',
                'status' => 'fail',
                'message' => $e->getMessage()
            ];
        }

        return [
            'category' => 'Base de Datos',
            'tests' => $tests
        ];
    }

    /**
     * Probar sistema de WhatsApp
     */
    private function testWhatsApp(): array
    {
        $tests = [];

        try {
            // Test 1: Templates de WhatsApp
            $db = \Config\Database::connect();
            $templatesCount = $db->table('whatsapp_templates')->countAllResults();

            if ($templatesCount > 0) {
                $tests[] = [
                    'name' => 'Templates de WhatsApp',
                    'status' => 'pass',
                    'message' => "$templatesCount templates disponibles"
                ];
            } else {
                $tests[] = [
                    'name' => 'Templates de WhatsApp',
                    'status' => 'fail',
                    'message' => 'No se encontraron templates'
                ];
            }

            // Test 2: Configuración de WhatsApp
            try {
                $whatsappManager = new WhatsAppManager();
                $tests[] = [
                    'name' => 'Clase WhatsAppManager',
                    'status' => 'pass',
                    'message' => 'Clase cargada correctamente'
                ];
            } catch (\Exception $e) {
                $tests[] = [
                    'name' => 'Clase WhatsAppManager',
                    'status' => 'warning',
                    'message' => 'Clase no disponible (normal si no está implementada)'
                ];
            }

        } catch (\Exception $e) {
            $tests[] = [
                'name' => 'Error en Sistema WhatsApp',
                'status' => 'fail',
                'message' => $e->getMessage()
            ];
        }

        return [
            'category' => 'Sistema WhatsApp',
            'tests' => $tests
        ];
    }

    /**
     * Probar sistema de automatizaciones
     */
    private function testAutomation(): array
    {
        $tests = [];

        try {
            // Test 1: Clase AutomationManager
            try {
                $automationManager = new AutomationManager();
                $tests[] = [
                    'name' => 'Clase AutomationManager',
                    'status' => 'pass',
                    'message' => 'Clase cargada correctamente'
                ];
            } catch (\Exception $e) {
                $tests[] = [
                    'name' => 'Clase AutomationManager',
                    'status' => 'fail',
                    'message' => 'Error cargando clase: ' . $e->getMessage()
                ];
            }

            // Test 2: Tabla de logs de automatización
            $db = \Config\Database::connect();
            try {
                $db->query("SELECT 1 FROM automation_log LIMIT 1");
                $tests[] = [
                    'name' => 'Tabla de Logs de Automatización',
                    'status' => 'pass',
                    'message' => 'Tabla automation_log disponible'
                ];
            } catch (\Exception $e) {
                $tests[] = [
                    'name' => 'Tabla de Logs de Automatización',
                    'status' => 'fail',
                    'message' => 'Tabla automation_log no encontrada'
                ];
            }

        } catch (\Exception $e) {
            $tests[] = [
                'name' => 'Error en Sistema Automatización',
                'status' => 'fail',
                'message' => $e->getMessage()
            ];
        }

        return [
            'category' => 'Sistema Automatización',
            'tests' => $tests
        ];
    }

    /**
     * Probar sistema de monitoreo
     */
    private function testMonitoring(): array
    {
        $tests = [];

        try {
            // Test 1: Clase SystemMonitor
            try {
                $systemMonitor = new SystemMonitor();
                $tests[] = [
                    'name' => 'Clase SystemMonitor',
                    'status' => 'pass',
                    'message' => 'Clase cargada correctamente'
                ];

                // Test 2: Verificación básica del sistema
                $healthCheck = $systemMonitor->runSystemCheck();
                if ($healthCheck['success']) {
                    $tests[] = [
                        'name' => 'Verificación del Sistema',
                        'status' => 'pass',
                        'message' => 'Sistema funcionando correctamente'
                    ];
                } else {
                    $tests[] = [
                        'name' => 'Verificación del Sistema',
                        'status' => 'warning',
                        'message' => 'Sistema con advertencias'
                    ];
                }
            } catch (\Exception $e) {
                $tests[] = [
                    'name' => 'Clase SystemMonitor',
                    'status' => 'fail',
                    'message' => 'Error: ' . $e->getMessage()
                ];
            }

        } catch (\Exception $e) {
            $tests[] = [
                'name' => 'Error en Sistema Monitoreo',
                'status' => 'fail',
                'message' => $e->getMessage()
            ];
        }

        return [
            'category' => 'Sistema Monitoreo',
            'tests' => $tests
        ];
    }

    /**
     * Probar APIs del sistema
     */
    private function testAPI(): array
    {
        $tests = [];

        try {
            // Test: URLs de cron jobs
            $cronUrls = [
                'status' => base_url('cron/status'),
                'help' => base_url('cron/help')
            ];

            foreach ($cronUrls as $name => $url) {
                $tests[] = [
                    'name' => "URL Cron: $name",
                    'status' => 'pass',
                    'message' => "URL configurada: $url"
                ];
            }

        } catch (\Exception $e) {
            $tests[] = [
                'name' => 'Error en Pruebas de API',
                'status' => 'fail',
                'message' => $e->getMessage()
            ];
        }

        return [
            'category' => 'APIs del Sistema',
            'tests' => $tests
        ];
    }

    /**
     * Obtener estado general del sistema
     */
    private function getSystemStatus(): array
    {
        try {
            $db = \Config\Database::connect();

            return [
                'database' => true,
                'tables_count' => count($db->listTables()),
                'settings_count' => $db->table('system_settings')->countAllResults(),
                'templates_count' => $db->table('whatsapp_templates')->countAllResults()
            ];
        } catch (\Exception $e) {
            return [
                'database' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtener categorías de pruebas
     */
    private function getTestCategories(): array
    {
        return [
            'database' => 'Base de Datos',
            'whatsapp' => 'Sistema WhatsApp',
            'automation' => 'Automatizaciones',
            'monitoring' => 'Monitoreo',
            'api' => 'APIs del Sistema'
        ];
    }
}
