<?php
/**
 * Script de prueba para verificar el entorno de cron en cPanel
 * Optimizado para estructura /public
 * 
 * Ejecutar desde cPanel con:
 * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/test-cron-environment.php
 */

echo "🧪 PRUEBA DEL ENTORNO DE CRON PARA MRCELL\n";
echo "==========================================\n\n";

// Detectar rutas del proyecto
$projectRoot = dirname(__DIR__); // Subir un nivel desde /public

// Información básica del sistema
echo "📋 INFORMACIÓN DEL SISTEMA:\n";
echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- Sistema Operativo: " . PHP_OS . "\n";
echo "- Directorio actual: " . __DIR__ . "\n";
echo "- Ra<PERSON>z del proyecto: $projectRoot\n";
echo "- Usuario: " . get_current_user() . "\n";
echo "- <PERSON><PERSON> horaria: " . date_default_timezone_get() . "\n";
echo "- Fecha/Hora actual: " . date('Y-m-d H:i:s') . "\n\n";

// Verificar estructura de directorios
echo "📁 VERIFICACIÓN DE DIRECTORIOS:\n";

$requiredDirs = [
    'app',
    'app/Config',
    'app/Controllers',
    'app/Libraries',
    'app/Services',
    'vendor',
    'writable',
    'writable/logs'
];

foreach ($requiredDirs as $dir) {
    $path = $projectRoot . '/' . $dir;
    $exists = is_dir($path);
    $writable = $exists ? is_writable($path) : false;
    
    echo "  " . ($exists ? "✅" : "❌") . " $dir";
    if ($exists && in_array($dir, ['writable', 'writable/logs'])) {
        echo " " . ($writable ? "(escribible)" : "(NO escribible)");
    }
    echo "\n";
}

echo "\n";

// Verificar archivos críticos
echo "📄 VERIFICACIÓN DE ARCHIVOS CRÍTICOS:\n";

$requiredFiles = [
    'vendor/autoload.php',
    'app/Config/App.php',
    'app/Config/Database.php',
    'app/Libraries/CronService.php',
    'app/Services/WhatsAppService.php',
    'public/cron-alerts.php'
];

foreach ($requiredFiles as $file) {
    $path = $projectRoot . '/' . $file;
    $exists = file_exists($path);
    echo "  " . ($exists ? "✅" : "❌") . " $file\n";
}

echo "\n";

// Probar carga de CodeIgniter
echo "🔧 PROBANDO CARGA DE CODEIGNITER:\n";

try {
    $autoloadPath = $projectRoot . '/vendor/autoload.php';
    
    if (!file_exists($autoloadPath)) {
        throw new Exception('Autoloader de Composer no encontrado en: ' . $autoloadPath);
    }
    
    require_once $autoloadPath;
    echo "  ✅ Autoloader cargado\n";
    
    if (!class_exists('Config\\Paths')) {
        throw new Exception('Clase Config\\Paths no encontrada');
    }
    
    $paths = new Config\Paths();
    $paths->systemDirectory = $projectRoot . '/system';
    $paths->appDirectory = $projectRoot . '/app';
    $paths->writableDirectory = $projectRoot . '/writable';
    $paths->testsDirectory = $projectRoot . '/tests';
    $paths->viewDirectory = $projectRoot . '/app/Views';
    
    echo "  ✅ Paths configurado\n";
    
    $bootstrap = \CodeIgniter\Boot::bootWeb($paths);
    $app = $bootstrap->getApp();
    echo "  ✅ CodeIgniter inicializado\n";
    
} catch (Exception $e) {
    echo "  ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Probar conexión a la base de datos
echo "🗄️ PROBANDO CONEXIÓN A LA BASE DE DATOS:\n";

try {
    $db = \Config\Database::connect();
    $testQuery = $db->query("SELECT 1 as test, NOW() as current_time")->getRowArray();
    
    if ($testQuery) {
        echo "  ✅ Conexión exitosa\n";
        echo "  📅 Hora del servidor BD: " . $testQuery['current_time'] . "\n";
        
        // Probar consultas específicas
        $tablesTest = [
            'products' => 'SELECT COUNT(*) as count FROM products',
            'orders' => 'SELECT COUNT(*) as count FROM orders',
            'system_settings' => 'SELECT COUNT(*) as count FROM system_settings'
        ];
        
        foreach ($tablesTest as $table => $query) {
            try {
                $result = $db->query($query)->getRowArray();
                echo "  ✅ Tabla $table: {$result['count']} registros\n";
            } catch (Exception $e) {
                echo "  ⚠️ Tabla $table: Error - " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "  ❌ Consulta de prueba falló\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error de conexión: " . $e->getMessage() . "\n";
}

echo "\n";

// Probar configuración de WhatsApp
echo "📱 PROBANDO CONFIGURACIÓN DE WHATSAPP:\n";

try {
    $groupSetting = $db->query("
        SELECT setting_value FROM system_settings 
        WHERE setting_key = 'whatsapp_alerts_group' 
        LIMIT 1
    ")->getRowArray();
    
    $groupNumber = $groupSetting['setting_value'] ?? 'NO CONFIGURADO';
    echo "  📞 Número de grupo: $groupNumber\n";
    
    if (class_exists('\\App\\Services\\WhatsAppService')) {
        echo "  ✅ Servicio WhatsApp disponible\n";
        
        // Probar instanciación del servicio
        $whatsappService = new \App\Services\WhatsAppService();
        echo "  ✅ Servicio WhatsApp instanciado\n";
    } else {
        echo "  ❌ Servicio WhatsApp no encontrado\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Probar creación de logs
echo "📝 PROBANDO CREACIÓN DE LOGS:\n";

try {
    $logFile = $projectRoot . '/writable/logs/cron-test.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
        echo "  ✅ Directorio de logs creado\n";
    }
    
    $testMessage = "[" . date('Y-m-d H:i:s') . "] Prueba de log desde test-cron-environment.php\n";
    $result = file_put_contents($logFile, $testMessage, FILE_APPEND | LOCK_EX);
    
    if ($result !== false) {
        echo "  ✅ Log de prueba creado: $logFile\n";
        echo "  📏 Bytes escritos: $result\n";
    } else {
        echo "  ❌ No se pudo crear el log\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Simular verificación de alertas
echo "🔍 SIMULANDO VERIFICACIÓN DE ALERTAS:\n";

try {
    // Productos con bajo stock
    $lowStock = $db->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND stock_quantity >= 0
            AND is_active = 1 
            AND deleted_at IS NULL
    ")->getRowArray();
    
    echo "  📦 Productos con bajo stock: {$lowStock['count']}\n";
    
    // Productos próximos a caducar
    $expiring = $db->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND expiration_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ")->getRowArray();
    
    echo "  ⏰ Productos próximos a caducar: {$expiring['count']}\n";
    
    // Pedidos pendientes
    $orders = $db->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
            COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped
        FROM orders
    ")->getRowArray();
    
    echo "  📋 Pedidos pendientes: {$orders['pending']}\n";
    echo "  🚚 Pedidos enviados: {$orders['shipped']}\n";
    
} catch (Exception $e) {
    echo "  ❌ Error en verificación: " . $e->getMessage() . "\n";
}

echo "\n";

// Resumen final
echo "📊 RESUMEN DE LA PRUEBA:\n";
echo "========================\n";

$checks = [
    'Directorios requeridos' => is_dir($projectRoot . '/app') && is_dir($projectRoot . '/vendor'),
    'CodeIgniter funcional' => class_exists('Config\\Paths'),
    'Base de datos conectada' => isset($testQuery) && $testQuery,
    'Logs escribibles' => is_writable($projectRoot . '/writable/logs'),
    'Servicio WhatsApp' => class_exists('\\App\\Services\\WhatsAppService')
];

$passed = 0;
$total = count($checks);

foreach ($checks as $check => $status) {
    echo ($status ? "✅" : "❌") . " $check\n";
    if ($status) $passed++;
}

echo "\n";
echo "🎯 RESULTADO: $passed/$total verificaciones pasaron\n";

if ($passed === $total) {
    echo "🎉 ¡ENTORNO LISTO PARA CRON!\n";
    echo "\nPuedes configurar el cron en cPanel con:\n";
    echo "/opt/cpanel/ea-php82/root/usr/bin/php " . __DIR__ . "/cron-alerts.php\n";
} else {
    echo "⚠️ Hay problemas que resolver antes de configurar el cron\n";
}

echo "\n📅 Prueba completada: " . date('Y-m-d H:i:s') . "\n";
?>
