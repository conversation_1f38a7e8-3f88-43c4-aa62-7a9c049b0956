<?php

namespace App\Libraries;

/**
 * Minificador de Assets (CSS/JS)
 * Compatible con cPanel hosting - Sin dependencias externas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class AssetMinifier
{
    private $config;
    private $cache;
    
    public function __construct()
    {
        $this->config = [
            'css_path' => FCPATH . 'assets/css/',
            'js_path' => FCPATH . 'assets/js/',
            'minified_suffix' => '.min',
            'enable_cache' => true,
            'cache_ttl' => 86400, // 24 horas
            'combine_files' => true,
            'preserve_comments' => false,
            'enable_gzip' => true
        ];
        
        $this->cache = new SimpleCache();
    }
    
    /**
     * Minificar y combinar archivos CSS
     * 
     * @param array $files Lista de archivos CSS
     * @param string $outputName Nombre del archivo combinado
     * @return array Resultado de la minificación
     */
    public function minifyCSS(array $files, string $outputName = 'combined'): array
    {
        try {
            $combinedCSS = '';
            $totalOriginalSize = 0;
            $processedFiles = [];
            
            foreach ($files as $file) {
                $filePath = $this->config['css_path'] . $file;
                
                if (!file_exists($filePath)) {
                    continue;
                }
                
                $css = file_get_contents($filePath);
                $originalSize = strlen($css);
                $totalOriginalSize += $originalSize;
                
                // Minificar CSS individual
                $minifiedCSS = $this->minifyCSSContent($css);
                
                // Agregar comentario de origen si está habilitado
                if ($this->config['preserve_comments']) {
                    $combinedCSS .= "/* Source: {$file} */\n";
                }
                
                $combinedCSS .= $minifiedCSS . "\n";
                
                $processedFiles[] = [
                    'file' => $file,
                    'original_size' => $originalSize,
                    'minified_size' => strlen($minifiedCSS),
                    'savings' => $originalSize - strlen($minifiedCSS)
                ];
            }
            
            if (empty($combinedCSS)) {
                return [
                    'success' => false,
                    'error' => 'No se encontraron archivos CSS válidos'
                ];
            }
            
            // Guardar archivo combinado
            $outputFile = $outputName . $this->config['minified_suffix'] . '.css';
            $outputPath = $this->config['css_path'] . $outputFile;
            
            file_put_contents($outputPath, $combinedCSS);
            
            // Crear versión gzipped si está habilitado
            $gzipPath = null;
            if ($this->config['enable_gzip'] && function_exists('gzencode')) {
                $gzipPath = $outputPath . '.gz';
                file_put_contents($gzipPath, gzencode($combinedCSS, 9));
            }
            
            $finalSize = strlen($combinedCSS);
            $totalSavings = $totalOriginalSize - $finalSize;
            $savingsPercentage = $totalOriginalSize > 0 ? round(($totalSavings / $totalOriginalSize) * 100, 2) : 0;
            
            return [
                'success' => true,
                'output_file' => $outputFile,
                'output_path' => $outputPath,
                'gzip_path' => $gzipPath,
                'processed_files' => $processedFiles,
                'total_original_size' => $totalOriginalSize,
                'final_size' => $finalSize,
                'total_savings' => $totalSavings,
                'savings_percentage' => $savingsPercentage,
                'files_count' => count($processedFiles)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Minificar contenido CSS
     */
    private function minifyCSSContent(string $css): string
    {
        // Remover comentarios (excepto los importantes /*! */)
        if (!$this->config['preserve_comments']) {
            $css = preg_replace('/\/\*(?!\!).*?\*\//s', '', $css);
        }
        
        // Remover espacios en blanco innecesarios
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remover espacios alrededor de caracteres especiales
        $css = str_replace([
            '; ', ' {', '{ ', ' }', '} ', ': ', ', ', ' > ', ' + ', ' ~ ', ' !',
            '( ', ' )', ' [', '] ', ' = ', '= ', ' *', '* '
        ], [
            ';', '{', '{', '}', '}', ':', ',', '>', '+', '~', '!',
            '(', ')', '[', ']', '=', '=', '*', '*'
        ], $css);
        
        // Remover punto y coma antes de }
        $css = str_replace(';}', '}', $css);
        
        // Remover espacios al inicio y final
        $css = trim($css);
        
        // Optimizaciones adicionales
        $css = $this->optimizeCSSValues($css);
        
        return $css;
    }
    
    /**
     * Optimizar valores CSS
     */
    private function optimizeCSSValues(string $css): string
    {
        // Convertir colores hex largos a cortos
        $css = preg_replace('/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/i', '#$1$2$3', $css);
        
        // Remover unidades innecesarias en valores 0
        $css = preg_replace('/\b0+(px|em|rem|%|pt|pc|in|mm|cm|ex|ch|vw|vh|vmin|vmax)\b/', '0', $css);
        
        // Simplificar valores decimales
        $css = preg_replace('/\b0+\.(\d+)/', '.$1', $css);
        
        // Remover espacios en valores múltiples
        $css = preg_replace('/:\s*([^;}]+)\s*([;}])/', ':$1$2', $css);
        
        return $css;
    }
    
    /**
     * Minificar y combinar archivos JavaScript
     * 
     * @param array $files Lista de archivos JS
     * @param string $outputName Nombre del archivo combinado
     * @return array Resultado de la minificación
     */
    public function minifyJS(array $files, string $outputName = 'combined'): array
    {
        try {
            $combinedJS = '';
            $totalOriginalSize = 0;
            $processedFiles = [];
            
            foreach ($files as $file) {
                $filePath = $this->config['js_path'] . $file;
                
                if (!file_exists($filePath)) {
                    continue;
                }
                
                $js = file_get_contents($filePath);
                $originalSize = strlen($js);
                $totalOriginalSize += $originalSize;
                
                // Minificar JS individual
                $minifiedJS = $this->minifyJSContent($js);
                
                // Agregar comentario de origen si está habilitado
                if ($this->config['preserve_comments']) {
                    $combinedJS .= "/* Source: {$file} */\n";
                }
                
                $combinedJS .= $minifiedJS . "\n";
                
                $processedFiles[] = [
                    'file' => $file,
                    'original_size' => $originalSize,
                    'minified_size' => strlen($minifiedJS),
                    'savings' => $originalSize - strlen($minifiedJS)
                ];
            }
            
            if (empty($combinedJS)) {
                return [
                    'success' => false,
                    'error' => 'No se encontraron archivos JS válidos'
                ];
            }
            
            // Guardar archivo combinado
            $outputFile = $outputName . $this->config['minified_suffix'] . '.js';
            $outputPath = $this->config['js_path'] . $outputFile;
            
            file_put_contents($outputPath, $combinedJS);
            
            // Crear versión gzipped si está habilitado
            $gzipPath = null;
            if ($this->config['enable_gzip'] && function_exists('gzencode')) {
                $gzipPath = $outputPath . '.gz';
                file_put_contents($gzipPath, gzencode($combinedJS, 9));
            }
            
            $finalSize = strlen($combinedJS);
            $totalSavings = $totalOriginalSize - $finalSize;
            $savingsPercentage = $totalOriginalSize > 0 ? round(($totalSavings / $totalOriginalSize) * 100, 2) : 0;
            
            return [
                'success' => true,
                'output_file' => $outputFile,
                'output_path' => $outputPath,
                'gzip_path' => $gzipPath,
                'processed_files' => $processedFiles,
                'total_original_size' => $totalOriginalSize,
                'final_size' => $finalSize,
                'total_savings' => $totalSavings,
                'savings_percentage' => $savingsPercentage,
                'files_count' => count($processedFiles)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Minificar contenido JavaScript
     */
    private function minifyJSContent(string $js): string
    {
        // Remover comentarios de línea y multilinea (básico)
        if (!$this->config['preserve_comments']) {
            // Remover comentarios multilinea
            $js = preg_replace('/\/\*(?!\!).*?\*\//s', '', $js);
            
            // Remover comentarios de línea (cuidado con URLs)
            $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        }
        
        // Remover espacios en blanco excesivos
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remover espacios alrededor de operadores y caracteres especiales
        $js = str_replace([
            '; ', ' {', '{ ', ' }', '} ', ' (', '( ', ' )', ') ', ' [', '[ ', ' ]', '] ',
            ' =', '= ', ' +', '+ ', ' -', '- ', ' *', '* ', ' /', '/ ', ' %', '% ',
            ' <', '< ', ' >', '> ', ' !', '! ', ' &', '& ', ' |', '| ', ' ?', '? ',
            ' :', ': ', ' ,', ', '
        ], [
            ';', '{', '{', '}', '}', '(', '(', ')', ')', '[', '[', ']', ']',
            '=', '=', '+', '+', '-', '-', '*', '*', '/', '/', '%', '%',
            '<', '<', '>', '>', '!', '!', '&', '&', '|', '|', '?', '?',
            ':', ':', ',', ','
        ], $js);
        
        // Remover punto y coma antes de }
        $js = str_replace(';}', '}', $js);
        
        // Remover espacios al inicio y final
        $js = trim($js);
        
        return $js;
    }
    
    /**
     * Generar HTML para cargar assets minificados
     * 
     * @param array $cssFiles Archivos CSS
     * @param array $jsFiles Archivos JS
     * @param bool $autoMinify Minificar automáticamente si no existe
     * @return array HTML generado
     */
    public function generateAssetHTML(array $cssFiles = [], array $jsFiles = [], bool $autoMinify = true): array
    {
        $html = ['css' => '', 'js' => ''];
        
        // Procesar CSS
        if (!empty($cssFiles)) {
            if ($this->config['combine_files']) {
                $cssResult = $this->minifyCSS($cssFiles, 'app');
                if ($cssResult['success']) {
                    $cssUrl = base_url('assets/css/' . $cssResult['output_file']);
                    $html['css'] = "<link rel=\"stylesheet\" href=\"{$cssUrl}\">";
                }
            } else {
                foreach ($cssFiles as $file) {
                    $minFile = str_replace('.css', $this->config['minified_suffix'] . '.css', $file);
                    $minPath = $this->config['css_path'] . $minFile;
                    
                    if (!file_exists($minPath) && $autoMinify) {
                        $this->minifyCSS([$file], pathinfo($file, PATHINFO_FILENAME));
                    }
                    
                    $cssUrl = file_exists($minPath) ? 
                             base_url('assets/css/' . $minFile) : 
                             base_url('assets/css/' . $file);
                    
                    $html['css'] .= "<link rel=\"stylesheet\" href=\"{$cssUrl}\">\n";
                }
            }
        }
        
        // Procesar JS
        if (!empty($jsFiles)) {
            if ($this->config['combine_files']) {
                $jsResult = $this->minifyJS($jsFiles, 'app');
                if ($jsResult['success']) {
                    $jsUrl = base_url('assets/js/' . $jsResult['output_file']);
                    $html['js'] = "<script src=\"{$jsUrl}\"></script>";
                }
            } else {
                foreach ($jsFiles as $file) {
                    $minFile = str_replace('.js', $this->config['minified_suffix'] . '.js', $file);
                    $minPath = $this->config['js_path'] . $minFile;
                    
                    if (!file_exists($minPath) && $autoMinify) {
                        $this->minifyJS([$file], pathinfo($file, PATHINFO_FILENAME));
                    }
                    
                    $jsUrl = file_exists($minPath) ? 
                            base_url('assets/js/' . $minFile) : 
                            base_url('assets/js/' . $file);
                    
                    $html['js'] .= "<script src=\"{$jsUrl}\"></script>\n";
                }
            }
        }
        
        return $html;
    }
    
    /**
     * Limpiar archivos minificados antiguos
     * 
     * @param int $days Días de antigüedad
     * @return array Resultado de la limpieza
     */
    public function cleanupOldMinified(int $days = 7): array
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $deleted = [];
        
        // Limpiar CSS minificados
        $cssFiles = glob($this->config['css_path'] . '*' . $this->config['minified_suffix'] . '.css*');
        foreach ($cssFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deleted[] = basename($file);
                }
            }
        }
        
        // Limpiar JS minificados
        $jsFiles = glob($this->config['js_path'] . '*' . $this->config['minified_suffix'] . '.js*');
        foreach ($jsFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deleted[] = basename($file);
                }
            }
        }
        
        return [
            'deleted' => $deleted,
            'count' => count($deleted)
        ];
    }
    
    /**
     * Obtener estadísticas de minificación
     * 
     * @return array Estadísticas
     */
    public function getStats(): array
    {
        $cssFiles = glob($this->config['css_path'] . '*.css');
        $jsFiles = glob($this->config['js_path'] . '*.js');
        
        $cssMinified = glob($this->config['css_path'] . '*' . $this->config['minified_suffix'] . '.css');
        $jsMinified = glob($this->config['js_path'] . '*' . $this->config['minified_suffix'] . '.js');
        
        $cssOriginalSize = 0;
        $cssMinifiedSize = 0;
        
        foreach ($cssFiles as $file) {
            if (strpos($file, $this->config['minified_suffix']) === false) {
                $cssOriginalSize += filesize($file);
            }
        }
        
        foreach ($cssMinified as $file) {
            $cssMinifiedSize += filesize($file);
        }
        
        $jsOriginalSize = 0;
        $jsMinifiedSize = 0;
        
        foreach ($jsFiles as $file) {
            if (strpos($file, $this->config['minified_suffix']) === false) {
                $jsOriginalSize += filesize($file);
            }
        }
        
        foreach ($jsMinified as $file) {
            $jsMinifiedSize += filesize($file);
        }
        
        return [
            'css' => [
                'original_files' => count($cssFiles) - count($cssMinified),
                'minified_files' => count($cssMinified),
                'original_size' => $cssOriginalSize,
                'minified_size' => $cssMinifiedSize,
                'savings' => $cssOriginalSize - $cssMinifiedSize,
                'savings_percentage' => $cssOriginalSize > 0 ? round((($cssOriginalSize - $cssMinifiedSize) / $cssOriginalSize) * 100, 2) : 0
            ],
            'js' => [
                'original_files' => count($jsFiles) - count($jsMinified),
                'minified_files' => count($jsMinified),
                'original_size' => $jsOriginalSize,
                'minified_size' => $jsMinifiedSize,
                'savings' => $jsOriginalSize - $jsMinifiedSize,
                'savings_percentage' => $jsOriginalSize > 0 ? round((($jsOriginalSize - $jsMinifiedSize) / $jsOriginalSize) * 100, 2) : 0
            ]
        ];
    }
    
    /**
     * Configurar opciones
     */
    public function setConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
