<?php

namespace App\Libraries;

/**
 * Gestor de Alertas Avanzado
 * Sistema completo de alertas por email, SMS y notificaciones
 */
class AlertManager
{
    private $db;
    private $config;
    private $emailService;
    private $smsService;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        
        $this->config = [
            'enabled' => env('ALERTS_ENABLED', true),
            'email_enabled' => env('ALERT_EMAIL_ENABLED', true),
            'sms_enabled' => env('ALERT_SMS_ENABLED', false),
            'whatsapp_enabled' => env('ALERT_WHATSAPP_ENABLED', true),
            'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),
            'admin_phone' => env('ADMIN_PHONE', '+50212345678'),
            'smtp_host' => env('SMTP_HOST', 'smtp.gmail.com'),
            'smtp_port' => env('SMTP_PORT', 587),
            'smtp_user' => env('SMTP_USER', ''),
            'smtp_pass' => env('SMTP_PASS', ''),
            'smtp_encryption' => env('SMTP_ENCRYPTION', 'tls'),
            'alert_cooldown' => 300, // 5 minutos entre alertas del mismo tipo
            'max_alerts_per_hour' => 20
        ];
        
        $this->initializeServices();
    }
    
    /**
     * Enviar alerta crítica
     */
    public function sendCriticalAlert(string $title, string $message, array $data = []): array
    {
        return $this->sendAlert('critical', $title, $message, $data);
    }
    
    /**
     * Enviar alerta de advertencia
     */
    public function sendWarningAlert(string $title, string $message, array $data = []): array
    {
        return $this->sendAlert('warning', $title, $message, $data);
    }
    
    /**
     * Enviar alerta informativa
     */
    public function sendInfoAlert(string $title, string $message, array $data = []): array
    {
        return $this->sendAlert('info', $title, $message, $data);
    }
    
    /**
     * Enviar alerta general
     */
    public function sendAlert(string $level, string $title, string $message, array $data = []): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'Alerts disabled'];
        }
        
        try {
            // Verificar cooldown
            if (!$this->checkCooldown($level, $title)) {
                return ['success' => false, 'error' => 'Alert in cooldown period'];
            }
            
            // Verificar límite por hora
            if (!$this->checkHourlyLimit()) {
                return ['success' => false, 'error' => 'Hourly alert limit reached'];
            }
            
            $alertId = $this->createAlert($level, $title, $message, $data);
            
            $results = [];
            
            // Enviar por email
            if ($this->config['email_enabled']) {
                $results['email'] = $this->sendEmailAlert($level, $title, $message, $data);
            }
            
            // Enviar por SMS
            if ($this->config['sms_enabled']) {
                $results['sms'] = $this->sendSMSAlert($level, $title, $message);
            }
            
            // Enviar por WhatsApp
            if ($this->config['whatsapp_enabled']) {
                $results['whatsapp'] = $this->sendWhatsAppAlert($level, $title, $message, $data);
            }
            
            // Actualizar estado de la alerta
            $this->updateAlertStatus($alertId, $results);
            
            return [
                'success' => true,
                'alert_id' => $alertId,
                'level' => $level,
                'channels' => $results
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'level' => $level,
                'title' => $title
            ];
        }
    }
    
    /**
     * Alerta de sistema crítico
     */
    public function systemCritical(string $component, string $issue, array $metrics = []): array
    {
        $title = "🚨 SISTEMA CRÍTICO: $component";
        $message = "Se ha detectado un problema crítico en $component:\n\n$issue";
        
        if (!empty($metrics)) {
            $message .= "\n\nMétricas del sistema:\n";
            foreach ($metrics as $key => $value) {
                $message .= "- $key: $value\n";
            }
        }
        
        $message .= "\n⚠️ ACCIÓN REQUERIDA INMEDIATAMENTE";
        
        return $this->sendCriticalAlert($title, $message, [
            'component' => $component,
            'issue' => $issue,
            'metrics' => $metrics,
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => gethostname()
        ]);
    }
    
    /**
     * Alerta de automatización fallida
     */
    public function automationFailed(string $automationType, string $error, int $attempts = 1): array
    {
        $title = "⚠️ AUTOMATIZACIÓN FALLIDA: $automationType";
        $message = "La automatización '$automationType' ha fallado:\n\n$error";
        
        if ($attempts > 1) {
            $message .= "\n\nIntentos realizados: $attempts";
        }
        
        $message .= "\n\n🔧 Revisar logs para más detalles";
        
        return $this->sendWarningAlert($title, $message, [
            'automation_type' => $automationType,
            'error' => $error,
            'attempts' => $attempts,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de backup fallido
     */
    public function backupFailed(string $backupType, string $error): array
    {
        $title = "💾 BACKUP FALLIDO: $backupType";
        $message = "El backup '$backupType' ha fallado:\n\n$error";
        $message .= "\n\n⚠️ Los datos pueden estar en riesgo";
        
        return $this->sendCriticalAlert($title, $message, [
            'backup_type' => $backupType,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de espacio en disco bajo
     */
    public function diskSpaceLow(float $usagePercent, string $availableSpace): array
    {
        $title = "💽 ESPACIO EN DISCO BAJO";
        $message = "El espacio en disco está llegando al límite:\n\n";
        $message .= "- Uso actual: {$usagePercent}%\n";
        $message .= "- Espacio disponible: $availableSpace\n\n";
        $message .= "🧹 Se recomienda ejecutar limpieza del sistema";
        
        $level = $usagePercent > 90 ? 'critical' : 'warning';
        
        return $this->sendAlert($level, $title, $message, [
            'usage_percent' => $usagePercent,
            'available_space' => $availableSpace,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de memoria alta
     */
    public function highMemoryUsage(float $usagePercent, string $usedMemory, string $totalMemory): array
    {
        $title = "🧠 USO DE MEMORIA ALTO";
        $message = "El uso de memoria está por encima del límite normal:\n\n";
        $message .= "- Uso actual: {$usagePercent}%\n";
        $message .= "- Memoria usada: $usedMemory\n";
        $message .= "- Memoria total: $totalMemory\n\n";
        $message .= "⚡ Se recomienda optimizar el sistema";
        
        $level = $usagePercent > 90 ? 'critical' : 'warning';
        
        return $this->sendAlert($level, $title, $message, [
            'usage_percent' => $usagePercent,
            'used_memory' => $usedMemory,
            'total_memory' => $totalMemory,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de base de datos desconectada
     */
    public function databaseDisconnected(string $error): array
    {
        $title = "🗄️ BASE DE DATOS DESCONECTADA";
        $message = "Se ha perdido la conexión con la base de datos:\n\n$error";
        $message .= "\n\n🚨 EL SITIO WEB PUEDE NO FUNCIONAR CORRECTAMENTE";
        
        return $this->sendCriticalAlert($title, $message, [
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de múltiples errores 500
     */
    public function multipleServerErrors(int $errorCount, string $timeframe): array
    {
        $title = "🔥 MÚLTIPLES ERRORES 500";
        $message = "Se han detectado múltiples errores del servidor:\n\n";
        $message .= "- Errores: $errorCount\n";
        $message .= "- Período: $timeframe\n\n";
        $message .= "🔍 Revisar logs del servidor inmediatamente";
        
        return $this->sendCriticalAlert($title, $message, [
            'error_count' => $errorCount,
            'timeframe' => $timeframe,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Alerta de notificaciones push fallando
     */
    public function pushNotificationsFailing(int $failedCount, float $failureRate): array
    {
        $title = "📱 NOTIFICACIONES PUSH FALLANDO";
        $message = "Las notificaciones push están fallando:\n\n";
        $message .= "- Notificaciones fallidas: $failedCount\n";
        $message .= "- Tasa de fallo: {$failureRate}%\n\n";
        $message .= "🔧 Verificar configuración VAPID";
        
        return $this->sendWarningAlert($title, $message, [
            'failed_count' => $failedCount,
            'failure_rate' => $failureRate,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Resumen diario del sistema
     */
    public function dailySystemSummary(array $metrics): array
    {
        $title = "📊 RESUMEN DIARIO DEL SISTEMA";
        $message = "Resumen del sistema para " . date('Y-m-d') . ":\n\n";
        
        foreach ($metrics as $category => $data) {
            $message .= "📈 " . strtoupper($category) . ":\n";
            foreach ($data as $key => $value) {
                $message .= "  - $key: $value\n";
            }
            $message .= "\n";
        }
        
        return $this->sendInfoAlert($title, $message, [
            'metrics' => $metrics,
            'date' => date('Y-m-d'),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Obtener historial de alertas
     */
    public function getAlertHistory(int $limit = 50, ?string $level = null): array
    {
        try {
            $builder = $this->db->table('system_alerts')
                              ->orderBy('created_at', 'DESC')
                              ->limit($limit);
            
            if ($level) {
                $builder->where('level', $level);
            }
            
            return [
                'success' => true,
                'alerts' => $builder->get()->getResultArray()
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Marcar alerta como resuelta
     */
    public function resolveAlert(int $alertId, string $resolution = ''): array
    {
        try {
            $this->db->table('system_alerts')
                    ->where('id', $alertId)
                    ->update([
                        'is_resolved' => 1,
                        'resolution' => $resolution,
                        'resolved_at' => date('Y-m-d H:i:s')
                    ]);
            
            return [
                'success' => true,
                'alert_id' => $alertId,
                'resolution' => $resolution
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verificar cooldown entre alertas
     */
    private function checkCooldown(string $level, string $title): bool
    {
        try {
            $recentAlert = $this->db->table('system_alerts')
                                   ->where('level', $level)
                                   ->where('title', $title)
                                   ->where('created_at >', date('Y-m-d H:i:s', time() - $this->config['alert_cooldown']))
                                   ->get()
                                   ->getRowArray();
            
            return $recentAlert === null;
            
        } catch (\Exception $e) {
            return true; // En caso de error, permitir alerta
        }
    }
    
    /**
     * Verificar límite de alertas por hora
     */
    private function checkHourlyLimit(): bool
    {
        try {
            $hourlyCount = $this->db->table('system_alerts')
                                   ->where('created_at >', date('Y-m-d H:i:s', time() - 3600))
                                   ->countAllResults();
            
            return $hourlyCount < $this->config['max_alerts_per_hour'];
            
        } catch (\Exception $e) {
            return true; // En caso de error, permitir alerta
        }
    }
    
    /**
     * Crear registro de alerta en base de datos
     */
    private function createAlert(string $level, string $title, string $message, array $data): int
    {
        return $this->db->table('system_alerts')->insert([
            'level' => $level,
            'title' => $title,
            'message' => $message,
            'data' => json_encode($data),
            'is_resolved' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Actualizar estado de alerta con resultados de envío
     */
    private function updateAlertStatus(int $alertId, array $results): void
    {
        try {
            $this->db->table('system_alerts')
                    ->where('id', $alertId)
                    ->update([
                        'delivery_status' => json_encode($results),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
        } catch (\Exception $e) {
            // Log error but don't fail
        }
    }
    
    /**
     * Enviar alerta por email
     */
    private function sendEmailAlert(string $level, string $title, string $message, array $data): array
    {
        try {
            if (!$this->emailService) {
                return ['success' => false, 'error' => 'Email service not configured'];
            }
            
            $subject = "[MrCell Guatemala] $title";
            $body = $this->formatEmailBody($level, $title, $message, $data);
            
            $result = $this->emailService->send($this->config['admin_email'], $subject, $body);
            
            return [
                'success' => $result,
                'to' => $this->config['admin_email']
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enviar alerta por WhatsApp
     */
    private function sendWhatsAppAlert(string $level, string $title, string $message, array $data): array
    {
        try {
            $whatsappManager = new \App\Libraries\WhatsAppBusinessManager();
            
            if (!$whatsappManager->isEnabled()) {
                return ['success' => false, 'error' => 'WhatsApp not configured'];
            }
            
            $formattedMessage = $this->formatWhatsAppMessage($level, $title, $message);
            
            return $whatsappManager->sendTextMessage($this->config['admin_phone'], $formattedMessage);
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Formatear mensaje para email
     */
    private function formatEmailBody(string $level, string $title, string $message, array $data): string
    {
        $levelEmoji = [
            'critical' => '🚨',
            'warning' => '⚠️',
            'info' => 'ℹ️'
        ];
        
        $body = "<html><body>";
        $body .= "<h2>" . ($levelEmoji[$level] ?? '') . " $title</h2>";
        $body .= "<p><strong>Nivel:</strong> " . strtoupper($level) . "</p>";
        $body .= "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>";
        $body .= "<hr>";
        $body .= "<p>" . nl2br(htmlspecialchars($message)) . "</p>";
        
        if (!empty($data)) {
            $body .= "<hr>";
            $body .= "<h3>Datos adicionales:</h3>";
            $body .= "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
        }
        
        $body .= "<hr>";
        $body .= "<p><small>Sistema de Alertas - MrCell Guatemala</small></p>";
        $body .= "</body></html>";
        
        return $body;
    }
    
    /**
     * Formatear mensaje para WhatsApp
     */
    private function formatWhatsAppMessage(string $level, string $title, string $message): string
    {
        $levelEmoji = [
            'critical' => '🚨',
            'warning' => '⚠️',
            'info' => 'ℹ️'
        ];
        
        $formatted = ($levelEmoji[$level] ?? '') . " *$title*\n\n";
        $formatted .= $message . "\n\n";
        $formatted .= "📅 " . date('Y-m-d H:i:s') . "\n";
        $formatted .= "🏪 MrCell Guatemala";
        
        return $formatted;
    }
    
    /**
     * Inicializar servicios
     */
    private function initializeServices(): void
    {
        // Inicializar servicio de email si está configurado
        if ($this->config['email_enabled'] && !empty($this->config['smtp_user'])) {
            $this->emailService = new \App\Libraries\EmailService([
                'host' => $this->config['smtp_host'],
                'port' => $this->config['smtp_port'],
                'username' => $this->config['smtp_user'],
                'password' => $this->config['smtp_pass'],
                'encryption' => $this->config['smtp_encryption']
            ]);
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'email_enabled' => $this->config['email_enabled'],
            'sms_enabled' => $this->config['sms_enabled'],
            'whatsapp_enabled' => $this->config['whatsapp_enabled'],
            'admin_email' => $this->config['admin_email'],
            'admin_phone' => $this->config['admin_phone'],
            'alert_cooldown' => $this->config['alert_cooldown'],
            'max_alerts_per_hour' => $this->config['max_alerts_per_hour']
        ];
    }
}
