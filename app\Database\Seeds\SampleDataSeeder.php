<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class SampleDataSeeder extends Seeder
{
    public function run()
    {
        // Categories
        $categories = [
            [
                'name' => 'Smartphones',
                'slug' => 'smartphones',
                'description' => 'Los mejores smartphones del mercado',
                'icon' => 'fas fa-mobile-alt',
                'sort_order' => 1,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Accesorios',
                'slug' => 'accesorios',
                'description' => 'Accesorios para tu dispositivo móvil',
                'icon' => 'fas fa-headphones',
                'sort_order' => 2,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Tablets',
                'slug' => 'tablets',
                'description' => 'Tablets para trabajo y entretenimiento',
                'icon' => 'fas fa-tablet-alt',
                'sort_order' => 3,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Audio',
                'slug' => 'audio',
                'description' => 'Dispositivos de audio de alta calidad',
                'icon' => 'fas fa-volume-up',
                'sort_order' => 4,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('categories')->insertBatch($categories);

        // Products
        $products = [
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'IPH15PRO-128',
                'name' => 'iPhone 15 Pro',
                'slug' => 'iphone-15-pro',
                'description' => 'iPhone 15 Pro con pantalla Super Retina XDR de 6.1 pulgadas, chip A17 Pro, sistema de cámaras Pro y Action Button.',
                'short_description' => 'El iPhone más avanzado con chip A17 Pro',
                'category_id' => 1,
                'price_regular' => 8999.00,
                'price_sale' => 8499.00,
                'stock_quantity' => 25,
                'featured_image' => 'https://via.placeholder.com/400x400/007bff/ffffff?text=iPhone+15+Pro',
                'is_active' => 1,
                'is_featured' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'SAM-S24U-256',
                'name' => 'Samsung Galaxy S24 Ultra',
                'slug' => 'samsung-galaxy-s24-ultra',
                'description' => 'Samsung Galaxy S24 Ultra con pantalla Dynamic AMOLED 2X de 6.8 pulgadas, procesador Snapdragon 8 Gen 3 y S Pen integrado.',
                'short_description' => 'El Galaxy más potente con S Pen integrado',
                'category_id' => 1,
                'price_regular' => 7999.00,
                'price_sale' => 7499.00,
                'stock_quantity' => 18,
                'featured_image' => 'https://via.placeholder.com/400x400/28a745/ffffff?text=Galaxy+S24+Ultra',
                'is_active' => 1,
                'is_featured' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'XIA-14PRO-512',
                'name' => 'Xiaomi 14 Pro',
                'slug' => 'xiaomi-14-pro',
                'description' => 'Xiaomi 14 Pro con pantalla AMOLED de 6.73 pulgadas, procesador Snapdragon 8 Gen 3 y cámara Leica.',
                'short_description' => 'Potencia y elegancia en un solo dispositivo',
                'category_id' => 1,
                'price_regular' => 4999.00,
                'price_sale' => null,
                'stock_quantity' => 32,
                'featured_image' => 'https://via.placeholder.com/400x400/ffc107/000000?text=Xiaomi+14+Pro',
                'is_active' => 1,
                'is_featured' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'AIRPODS-PRO2',
                'name' => 'AirPods Pro (2da generación)',
                'slug' => 'airpods-pro-2da-generacion',
                'description' => 'AirPods Pro con cancelación activa de ruido, audio espacial personalizado y hasta 6 horas de reproducción.',
                'short_description' => 'Audio premium con cancelación de ruido',
                'category_id' => 2,
                'price_regular' => 1899.00,
                'price_sale' => 1699.00,
                'stock_quantity' => 45,
                'featured_image' => 'https://via.placeholder.com/400x400/6c757d/ffffff?text=AirPods+Pro',
                'is_active' => 1,
                'is_featured' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'IPAD-AIR-256',
                'name' => 'iPad Air (5ta generación)',
                'slug' => 'ipad-air-5ta-generacion',
                'description' => 'iPad Air con chip M1, pantalla Liquid Retina de 10.9 pulgadas y compatibilidad con Apple Pencil.',
                'short_description' => 'Potencia de M1 en un diseño ultradelgado',
                'category_id' => 3,
                'price_regular' => 5999.00,
                'price_sale' => 5499.00,
                'stock_quantity' => 15,
                'featured_image' => 'https://via.placeholder.com/400x400/17a2b8/ffffff?text=iPad+Air',
                'is_active' => 1,
                'is_featured' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'SONY-WH1000XM5',
                'name' => 'Sony WH-1000XM5',
                'slug' => 'sony-wh-1000xm5',
                'description' => 'Audífonos inalámbricos con la mejor cancelación de ruido del mercado y hasta 30 horas de batería.',
                'short_description' => 'La mejor cancelación de ruido',
                'category_id' => 4,
                'price_regular' => 2499.00,
                'price_sale' => null,
                'stock_quantity' => 22,
                'featured_image' => 'https://via.placeholder.com/400x400/343a40/ffffff?text=Sony+WH1000XM5',
                'is_active' => 1,
                'is_featured' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'MAGSAFE-CHARGER',
                'name' => 'Cargador MagSafe',
                'slug' => 'cargador-magsafe',
                'description' => 'Cargador inalámbrico MagSafe para iPhone con alineación perfecta y carga rápida de 15W.',
                'short_description' => 'Carga inalámbrica perfecta para iPhone',
                'category_id' => 2,
                'price_regular' => 399.00,
                'price_sale' => 349.00,
                'stock_quantity' => 60,
                'featured_image' => 'https://via.placeholder.com/400x400/fd7e14/ffffff?text=MagSafe',
                'is_active' => 1,
                'is_featured' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'uuid' => $this->generateUUID(),
                'sku' => 'PIXEL-8-PRO',
                'name' => 'Google Pixel 8 Pro',
                'slug' => 'google-pixel-8-pro',
                'description' => 'Google Pixel 8 Pro con chip Tensor G3, cámaras con IA avanzada y pantalla LTPO OLED de 6.7 pulgadas.',
                'short_description' => 'IA de Google en tu bolsillo',
                'category_id' => 1,
                'price_regular' => 6999.00,
                'price_sale' => 6499.00,
                'stock_quantity' => 12,
                'featured_image' => 'https://via.placeholder.com/400x400/20c997/ffffff?text=Pixel+8+Pro',
                'is_active' => 1,
                'is_featured' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('products')->insertBatch($products);

        // Users
        $users = [
            [
                'uuid' => $this->generateUUID(),
                'name' => 'Usuario Demo',
                'email' => '<EMAIL>',
                'password' => password_hash('demo123', PASSWORD_DEFAULT),
                'phone' => '+502 1234-5678',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('users')->insertBatch($users);

        echo "Sample data seeded successfully!\n";
    }

    private function generateUUID()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
