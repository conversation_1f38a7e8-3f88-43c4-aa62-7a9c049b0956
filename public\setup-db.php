<?php
/**
 * CONFIGUR<PERSON>IÓN RÁPIDA DE BASE DE DATOS PARA CRON
 */

echo "🔧 CONFIGURACIÓN DE BASE DE DATOS PARA CRON\n";
echo "===========================================\n\n";

try {
    // CONFIGURACIÓN REAL DE LA BASE DE DATOS
    $host = '**************';
    $dbname = 'mayansourcecom_mrcell';
    $username = 'mayansourcecom_mrcell';
    $password = 'Clairo!23';
    
    echo "🔗 Conectando a la base de datos...\n";
    echo "Host: $host\n";
    echo "Base de datos: $dbname\n";
    echo "Usuario: $username\n\n";
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Conectado exitosamente\n\n";
    
    // 1. Crear tabla de ejecuciones de cron
    echo "📋 Creando tabla cron_executions...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cron_executions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_name VARCHAR(100) NOT NULL,
            status ENUM('success', 'error') DEFAULT 'success',
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_task_name (task_name),
            INDEX idx_created_at (created_at)
        )
    ");
    echo "✅ Tabla cron_executions creada\n";
    
    // 2. Configurar número de grupo de WhatsApp
    echo "📱 Configurando número de grupo de WhatsApp...\n";

    // Primero verificar la estructura de la tabla system_settings
    $stmt = $pdo->query("SHOW COLUMNS FROM system_settings");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Columnas en system_settings: " . implode(', ', $columns) . "\n";

    // Verificar si existe la configuración
    $stmt = $pdo->query("
        SELECT setting_value FROM system_settings
        WHERE setting_key = 'whatsapp_alerts_group'
        LIMIT 1
    ");
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existing) {
        // Insertar configuración usando solo las columnas que existen
        if (in_array('setting_description', $columns)) {
            // Tabla completa
            $pdo->exec("
                INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
                VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
            ");
        } else {
            // Tabla básica
            $pdo->exec("
                INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at)
                VALUES ('whatsapp_alerts_group', '120363416393766854', NOW(), NOW())
            ");
        }
        echo "✅ Número de grupo configurado: 120363416393766854\n";
    } else {
        echo "✅ Número de grupo ya configurado: {$existing['setting_value']}\n";
    }
    
    // 3. Verificar datos de prueba
    echo "\n🔍 Verificando datos para alertas...\n";
    
    // Productos con bajo stock
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND is_active = 1 
            AND deleted_at IS NULL
    ");
    $lowStock = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📦 Productos con bajo stock: {$lowStock['count']}\n";
    
    // Productos próximos a caducar
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND expiration_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ");
    $expiring = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "⏰ Productos próximos a caducar: {$expiring['count']}\n";
    
    // Pedidos pendientes
    $stmt = $pdo->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
            COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped
        FROM orders
    ");
    $orders = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📋 Pedidos pendientes: {$orders['pending']}\n";
    echo "🚚 Pedidos enviados: {$orders['shipped']}\n";
    
    echo "\n🎯 CONFIGURACIÓN COMPLETADA\n";
    echo "============================\n\n";
    
    echo "📋 PRÓXIMOS PASOS:\n\n";
    
    echo "1. EDITAR EL ARCHIVO cron-simple.php:\n";
    echo "   - Cambiar la configuración de la base de datos (líneas 15-18)\n";
    echo "   - Ajustar la URL de WhatsApp API (línea 142)\n\n";
    
    echo "2. CONFIGURAR EN CPANEL:\n";
    echo "   Comando: /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-simple.php\n";
    echo "   Programación: 0 8,20 * * * (cada 12 horas)\n\n";
    
    echo "3. PROBAR MANUALMENTE:\n";
    echo "   Visita: https://mrcell.com.gt/cron-simple.php\n\n";
    
    echo "📱 CONFIGURACIÓN DE WHATSAPP:\n";
    echo "- Número de grupo: 120363416393766854\n";
    echo "- Editable desde: /admin/settings?tab=notifications\n\n";
    
    echo "🎉 ¡LISTO PARA USAR!\n";
    
} catch (PDOException $e) {
    echo "❌ ERROR DE BASE DE DATOS:\n";
    echo "Mensaje: " . $e->getMessage() . "\n\n";
    
    echo "💡 VERIFICA LA CONFIGURACIÓN:\n";
    echo "1. Host: $host\n";
    echo "2. Base de datos: $dbname\n";
    echo "3. Usuario: $username\n";
    echo "4. Contraseña: [verifica que sea correcta]\n\n";
    
    echo "📞 CONTACTA AL SOPORTE DE TU HOSTING SI PERSISTE EL PROBLEMA\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n📅 Configuración finalizada: " . date('Y-m-d H:i:s') . "\n";
?>
