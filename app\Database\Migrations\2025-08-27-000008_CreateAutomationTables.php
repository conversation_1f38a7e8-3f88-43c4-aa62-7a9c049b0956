<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tablas del sistema de automatizaciones
 * Incluye logs, analytics y configuraciones
 */
class CreateAutomationTables extends Migration
{
    public function up()
    {
        // Tabla de log de automatizaciones
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'task_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Nombre de la tarea automatizada',
            ],
            'result' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Resultado completo de la ejecución',
            ],
            'success' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si la tarea fue exitosa',
            ],
            'duration' => [
                'type' => 'DECIMAL',
                'constraint' => '10,3',
                'default' => 0.000,
                'comment' => 'Duración en segundos',
            ],
            'memory_usage' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Uso de memoria en bytes',
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Mensaje de error si falló',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('task_name');
        $this->forge->addKey('success');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('automation_log');

        // Tabla de log de notificaciones
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'notification_type' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'wishlist_reminder', 'order_update', 'promotion', 'general'],
                'null' => false,
            ],
            'channel' => [
                'type' => 'ENUM',
                'constraint' => ['whatsapp', 'push', 'email', 'sms'],
                'null' => false,
            ],
            'data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Datos adicionales de la notificación',
            ],
            'success' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'sent_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'created_at']);
        $this->forge->addKey('product_id');
        $this->forge->addKey('notification_type');
        $this->forge->addKey('channel');
        $this->forge->addKey('success');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('notification_log');
        
        // Foreign keys
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de eventos de analytics pendientes
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'event_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'event_data' => [
                'type' => 'JSON',
                'null' => false,
                'comment' => 'Datos del evento para GA4',
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => true,
            ],
            'processed' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'processed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'retry_count' => [
                'type' => 'INT',
                'constraint' => 3,
                'unsigned' => true,
                'default' => 0,
            ],
            'last_error' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('processed');
        $this->forge->addKey('event_name');
        $this->forge->addKey('user_id');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('pending_analytics_events');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de configuraciones del sistema
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => 'Categoría de configuración',
            ],
            'key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Clave de configuración',
            ],
            'value' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Valor de configuración',
            ],
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['string', 'integer', 'float', 'boolean', 'json', 'array'],
                'default' => 'string',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Descripción de la configuración',
            ],
            'is_public' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si es accesible desde frontend',
            ],
            'is_editable' => [
                'type' => 'BOOLEAN',
                'default' => true,
                'comment' => 'Si se puede editar desde admin',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['category', 'key'], false, true); // Unique
        $this->forge->addKey('category');
        $this->forge->addKey('is_public');
        
        $this->forge->createTable('system_settings');

        // Tabla de métricas del sistema
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'date' => [
                'type' => 'DATE',
                'null' => false,
            ],
            'metric_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'metric_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
                'null' => false,
            ],
            'metric_unit' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
                'comment' => 'Unidad de medida (ms, %, count, etc.)',
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => 'Categoría de la métrica',
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Metadatos adicionales',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['date', 'metric_name'], false, true); // Unique
        $this->forge->addKey('date');
        $this->forge->addKey('metric_name');
        $this->forge->addKey('category');
        
        $this->forge->createTable('system_metrics');

        // Tabla de alertas del sistema
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'alert_type' => [
                'type' => 'ENUM',
                'constraint' => ['performance', 'error', 'security', 'business', 'system'],
                'null' => false,
            ],
            'severity' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'critical'],
                'default' => 'medium',
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'message' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Datos adicionales de la alerta',
            ],
            'is_resolved' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'resolved_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'resolved_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'notification_sent' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('alert_type');
        $this->forge->addKey('severity');
        $this->forge->addKey('is_resolved');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('system_alerts');
        
        // Foreign key para resolved_by
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('resolved_by', 'users', 'id', 'SET NULL', 'CASCADE');
        }

        // Tabla de publicaciones en redes sociales
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'platforms' => [
                'type' => 'JSON',
                'null' => false,
                'comment' => 'Resultados de publicación por plataforma',
            ],
            'total_success' => [
                'type' => 'INT',
                'constraint' => 3,
                'unsigned' => true,
                'default' => 0,
            ],
            'total_failed' => [
                'type' => 'INT',
                'constraint' => 3,
                'unsigned' => true,
                'default' => 0,
            ],
            'published_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('product_id');
        $this->forge->addKey('published_at');
        $this->forge->addKey('created_at');

        $this->forge->createTable('social_media_posts');

        // Foreign key
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de publicaciones programadas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'platforms' => [
                'type' => 'JSON',
                'null' => false,
                'comment' => 'Plataformas donde publicar',
            ],
            'options' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Opciones de publicación',
            ],
            'scheduled_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'processing', 'completed', 'failed', 'cancelled'],
                'default' => 'pending',
            ],
            'result' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Resultado de la publicación',
            ],
            'processed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('product_id');
        $this->forge->addKey('scheduled_at');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');

        $this->forge->createTable('scheduled_social_posts');

        // Foreign key
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Insertar configuraciones por defecto
        $this->insertDefaultSettings();
    }
    
    /**
     * Insertar configuraciones por defecto
     */
    private function insertDefaultSettings()
    {
        $settings = [
            // Configuraciones de automatización
            [
                'category' => 'automation',
                'key' => 'enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Habilitar sistema de automatizaciones',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'automation',
                'key' => 'max_execution_time',
                'value' => '300',
                'type' => 'integer',
                'description' => 'Tiempo máximo de ejecución en segundos',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'automation',
                'key' => 'batch_size',
                'value' => '100',
                'type' => 'integer',
                'description' => 'Tamaño de lote para procesamiento',
                'is_public' => false,
                'is_editable' => true
            ],
            
            // Configuraciones de notificaciones
            [
                'category' => 'notifications',
                'key' => 'max_daily_notifications',
                'value' => '5',
                'type' => 'integer',
                'description' => 'Máximo de notificaciones por usuario por día',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'notifications',
                'key' => 'cooldown_hours',
                'value' => '24',
                'type' => 'integer',
                'description' => 'Horas de espera entre notificaciones del mismo producto',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'notifications',
                'key' => 'quiet_hours_start',
                'value' => '22:00',
                'type' => 'string',
                'description' => 'Hora de inicio del período de silencio',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'notifications',
                'key' => 'quiet_hours_end',
                'value' => '08:00',
                'type' => 'string',
                'description' => 'Hora de fin del período de silencio',
                'is_public' => false,
                'is_editable' => true
            ],
            
            // Configuraciones de monitoreo
            [
                'category' => 'monitoring',
                'key' => 'price_change_threshold',
                'value' => '5.0',
                'type' => 'float',
                'description' => 'Umbral mínimo de cambio de precio para alertas (%)',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'monitoring',
                'key' => 'price_change_amount',
                'value' => '5.00',
                'type' => 'float',
                'description' => 'Cambio mínimo en quetzales para alertas',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'monitoring',
                'key' => 'performance_alert_threshold',
                'value' => '5000',
                'type' => 'integer',
                'description' => 'Umbral de tiempo de respuesta para alertas (ms)',
                'is_public' => false,
                'is_editable' => true
            ],
            
            // Configuraciones de backup
            [
                'category' => 'backup',
                'key' => 'enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Habilitar backups automáticos',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'backup',
                'key' => 'retention_days',
                'value' => '7',
                'type' => 'integer',
                'description' => 'Días de retención de backups',
                'is_public' => false,
                'is_editable' => true
            ],
            [
                'category' => 'backup',
                'key' => 'schedule_hour',
                'value' => '2',
                'type' => 'integer',
                'description' => 'Hora del día para ejecutar backup (0-23)',
                'is_public' => false,
                'is_editable' => true
            ],
            
            // Configuraciones de analytics
            [
                'category' => 'analytics',
                'key' => 'ga4_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Habilitar Google Analytics 4',
                'is_public' => true,
                'is_editable' => true
            ],
            [
                'category' => 'analytics',
                'key' => 'enhanced_ecommerce',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Habilitar Enhanced Ecommerce en GA4',
                'is_public' => true,
                'is_editable' => true
            ],
            [
                'category' => 'analytics',
                'key' => 'sync_batch_size',
                'value' => '50',
                'type' => 'integer',
                'description' => 'Tamaño de lote para sincronización de eventos',
                'is_public' => false,
                'is_editable' => true
            ]
        ];
        
        foreach ($settings as $setting) {
            $setting['created_at'] = date('Y-m-d H:i:s');
            $setting['updated_at'] = date('Y-m-d H:i:s');
            
            $this->db->table('system_settings')->insert($setting);
        }
    }

    public function down()
    {
        $this->forge->dropTable('scheduled_social_posts', true);
        $this->forge->dropTable('social_media_posts', true);
        $this->forge->dropTable('system_alerts', true);
        $this->forge->dropTable('system_metrics', true);
        $this->forge->dropTable('system_settings', true);
        $this->forge->dropTable('pending_analytics_events', true);
        $this->forge->dropTable('notification_log', true);
        $this->forge->dropTable('automation_log', true);
    }
}
