-- Tabla para mensajes de contacto
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid VARCHAR(36) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    phone VARCHAR(20) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    status ENUM('pending', 'read', 'replied', 'closed') DEFAULT 'pending',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    assigned_to INT(11) UNSIGNED NULL,
    admin_notes TEXT NULL,
    replied_at DATETIME NULL,
    replied_by INT(11) UNSIGNED NULL,
    created_at DATETIME NULL,
    updated_at DATETIME NULL,
    deleted_at DATETIME NULL,
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> idx_uuid (uuid),
    KEY idx_status_created (status, created_at),
    KEY idx_email (email),
    KEY idx_assigned_to (assigned_to),
    KEY idx_created_at (created_at),
    KEY idx_search (name, email, subject)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para respuestas a mensajes de contacto (opcional)
CREATE TABLE IF NOT EXISTS contact_message_replies (
    id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    contact_message_id INT(11) UNSIGNED NOT NULL,
    admin_id INT(11) UNSIGNED NOT NULL,
    reply_message TEXT NOT NULL,
    is_internal_note BOOLEAN DEFAULT FALSE,
    created_at DATETIME NULL,
    updated_at DATETIME NULL,
    PRIMARY KEY (id),
    KEY idx_contact_message_id (contact_message_id),
    KEY idx_admin_id (admin_id),
    KEY idx_created_at (created_at),
    FOREIGN KEY (contact_message_id) REFERENCES contact_messages(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar algunos datos de prueba (opcional)
INSERT INTO contact_messages (uuid, name, email, subject, message, status, priority, created_at, updated_at) VALUES
(UUID(), 'Juan Pérez', '<EMAIL>', 'Consulta sobre producto', 'Hola, me gustaría saber más información sobre el iPhone 13.', 'pending', 'normal', NOW(), NOW()),
(UUID(), 'María García', '<EMAIL>', 'Problema con mi pedido', 'Mi pedido no ha llegado y ya pasaron 5 días.', 'pending', 'high', NOW(), NOW()),
(UUID(), 'Carlos López', '<EMAIL>', 'Solicitud de devolución', 'Quiero devolver un producto que compré la semana pasada.', 'read', 'normal', NOW(), NOW());
