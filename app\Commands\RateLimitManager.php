<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class RateLimitManager extends BaseCommand
{
    protected $group       = 'Security';
    protected $name        = 'ratelimit:manage';
    protected $description = 'Gestionar rate limiting y revisar logs de seguridad';

    protected $usage = 'ratelimit:manage [action]';
    protected $arguments = [
        'action' => 'Acción a realizar: stats, clear, block, unblock, logs'
    ];

    protected $options = [
        '--ip'     => 'IP address para bloquear/desbloquear',
        '--hours'  => 'Horas para mostrar estadísticas (default: 24)',
        '--limit'  => 'Límite de registros a mostrar (default: 50)'
    ];

    protected $db;
    protected $cache;

    public function run(array $params)
    {
        $this->db = \Config\Database::connect();
        $this->cache = \Config\Services::cache();

        $action = $params[0] ?? 'stats';

        switch ($action) {
            case 'stats':
                $this->showStats();
                break;
            case 'clear':
                $this->clearCache();
                break;
            case 'block':
                $this->blockIp();
                break;
            case 'unblock':
                $this->unblockIp();
                break;
            case 'logs':
                $this->showLogs();
                break;
            case 'cleanup':
                $this->cleanupOldLogs();
                break;
            default:
                $this->showHelp();
        }
    }

    /**
     * Mostrar estadísticas de rate limiting
     */
    private function showStats()
    {
        $hours = CLI::getOption('hours') ?? 24;
        
        CLI::write("📊 Estadísticas de Rate Limiting (últimas {$hours} horas)", 'yellow');
        CLI::newLine();

        try {
            // Verificar si la tabla existe
            if (!$this->db->tableExists('rate_limit_logs')) {
                CLI::write("⚠️  Tabla rate_limit_logs no existe. Ejecuta algunas requests primero.", 'red');
                return;
            }

            $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));

            // Violaciones por IP
            $ipStats = $this->db->query("
                SELECT ip_address, COUNT(*) as violations, MAX(created_at) as last_violation
                FROM rate_limit_logs 
                WHERE created_at >= ? 
                GROUP BY ip_address 
                ORDER BY violations DESC 
                LIMIT 10
            ", [$since])->getResultArray();

            if (!empty($ipStats)) {
                CLI::write("🚫 Top IPs con violaciones:", 'red');
                $table = [];
                foreach ($ipStats as $stat) {
                    $table[] = [
                        $stat['ip_address'],
                        $stat['violations'],
                        $stat['last_violation']
                    ];
                }
                CLI::table($table, ['IP Address', 'Violaciones', 'Última Violación']);
                CLI::newLine();
            }

            // Violaciones por URI
            $uriStats = $this->db->query("
                SELECT uri, COUNT(*) as violations
                FROM rate_limit_logs 
                WHERE created_at >= ? 
                GROUP BY uri 
                ORDER BY violations DESC 
                LIMIT 10
            ", [$since])->getResultArray();

            if (!empty($uriStats)) {
                CLI::write("📍 URIs más atacadas:", 'yellow');
                $table = [];
                foreach ($uriStats as $stat) {
                    $table[] = [$stat['uri'], $stat['violations']];
                }
                CLI::table($table, ['URI', 'Violaciones']);
                CLI::newLine();
            }

            // Estadísticas generales
            $totalViolations = $this->db->query("
                SELECT COUNT(*) as total FROM rate_limit_logs WHERE created_at >= ?
            ", [$since])->getRow()->total;

            $uniqueIps = $this->db->query("
                SELECT COUNT(DISTINCT ip_address) as unique_ips FROM rate_limit_logs WHERE created_at >= ?
            ", [$since])->getRow()->unique_ips;

            CLI::write("📈 Resumen:", 'green');
            CLI::write("   Total de violaciones: {$totalViolations}");
            CLI::write("   IPs únicas: {$uniqueIps}");
            CLI::write("   Promedio por IP: " . ($uniqueIps > 0 ? round($totalViolations / $uniqueIps, 2) : 0));

        } catch (\Exception $e) {
            CLI::write("❌ Error al obtener estadísticas: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Limpiar cache de rate limiting
     */
    private function clearCache()
    {
        CLI::write("🧹 Limpiando cache de rate limiting...", 'yellow');
        
        try {
            // Limpiar todas las claves de rate limit del cache
            $this->cache->clean();
            CLI::write("✅ Cache limpiado exitosamente", 'green');
        } catch (\Exception $e) {
            CLI::write("❌ Error al limpiar cache: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Bloquear una IP específica
     */
    private function blockIp()
    {
        $ip = CLI::getOption('ip');
        
        if (!$ip) {
            CLI::write("❌ Debes especificar una IP con --ip", 'red');
            return;
        }

        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            CLI::write("❌ IP inválida: {$ip}", 'red');
            return;
        }

        try {
            // Bloquear por 24 horas
            $blockKey = "rate_limit_block_manual_{$ip}";
            $this->cache->save($blockKey, true, 86400);
            
            CLI::write("🚫 IP {$ip} bloqueada por 24 horas", 'red');
            
            // Log del bloqueo manual
            log_message('warning', "IP {$ip} bloqueada manualmente via CLI");
            
        } catch (\Exception $e) {
            CLI::write("❌ Error al bloquear IP: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Desbloquear una IP específica
     */
    private function unblockIp()
    {
        $ip = CLI::getOption('ip');
        
        if (!$ip) {
            CLI::write("❌ Debes especificar una IP con --ip", 'red');
            return;
        }

        try {
            // Buscar y eliminar todas las claves de bloqueo para esta IP
            $patterns = [
                "rate_limit_block_manual_{$ip}",
                "rate_limit_block_{$ip}_*"
            ];

            $removed = 0;
            foreach ($patterns as $pattern) {
                if ($this->cache->delete($pattern)) {
                    $removed++;
                }
            }
            
            CLI::write("✅ IP {$ip} desbloqueada ({$removed} bloqueos removidos)", 'green');
            
            // Log del desbloqueo
            log_message('info', "IP {$ip} desbloqueada manualmente via CLI");
            
        } catch (\Exception $e) {
            CLI::write("❌ Error al desbloquear IP: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Mostrar logs recientes
     */
    private function showLogs()
    {
        $limit = CLI::getOption('limit') ?? 50;
        
        CLI::write("📋 Últimos {$limit} logs de rate limiting:", 'yellow');
        CLI::newLine();

        try {
            if (!$this->db->tableExists('rate_limit_logs')) {
                CLI::write("⚠️  Tabla rate_limit_logs no existe.", 'red');
                return;
            }

            $logs = $this->db->query("
                SELECT ip_address, uri, method, user_id, created_at
                FROM rate_limit_logs 
                ORDER BY created_at DESC 
                LIMIT ?
            ", [$limit])->getResultArray();

            if (empty($logs)) {
                CLI::write("ℹ️  No hay logs de rate limiting", 'blue');
                return;
            }

            $table = [];
            foreach ($logs as $log) {
                $table[] = [
                    $log['created_at'],
                    $log['ip_address'],
                    $log['method'] . ' ' . $log['uri'],
                    $log['user_id'] ?? 'Anónimo'
                ];
            }

            CLI::table($table, ['Fecha', 'IP', 'Request', 'Usuario']);

        } catch (\Exception $e) {
            CLI::write("❌ Error al obtener logs: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Limpiar logs antiguos
     */
    private function cleanupOldLogs()
    {
        $days = CLI::getOption('days') ?? 30;
        
        CLI::write("🗑️  Limpiando logs de más de {$days} días...", 'yellow');

        try {
            if (!$this->db->tableExists('rate_limit_logs')) {
                CLI::write("⚠️  Tabla rate_limit_logs no existe.", 'red');
                return;
            }

            $cutoff = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $result = $this->db->query("DELETE FROM rate_limit_logs WHERE created_at < ?", [$cutoff]);
            $affected = $this->db->affectedRows();
            
            CLI::write("✅ {$affected} logs antiguos eliminados", 'green');
            
        } catch (\Exception $e) {
            CLI::write("❌ Error al limpiar logs: " . $e->getMessage(), 'red');
        }
    }

    /**
     * Mostrar ayuda
     */
    public function showHelp()
    {
        CLI::write("🛡️  Rate Limit Manager", 'yellow');
        CLI::newLine();
        CLI::write("Acciones disponibles:");
        CLI::write("  stats    - Mostrar estadísticas de rate limiting");
        CLI::write("  clear    - Limpiar cache de rate limiting");
        CLI::write("  block    - Bloquear una IP específica (requiere --ip)");
        CLI::write("  unblock  - Desbloquear una IP específica (requiere --ip)");
        CLI::write("  logs     - Mostrar logs recientes");
        CLI::write("  cleanup  - Limpiar logs antiguos");
        CLI::newLine();
        CLI::write("Ejemplos:");
        CLI::write("  php spark ratelimit:manage stats --hours=12");
        CLI::write("  php spark ratelimit:manage block --ip=*************");
        CLI::write("  php spark ratelimit:manage logs --limit=100");
    }
}
