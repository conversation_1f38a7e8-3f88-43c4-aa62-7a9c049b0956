<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ExecuteSQL extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:execute-sql';
    protected $description = 'Ejecutar archivo SQL';
    protected $usage       = 'db:execute-sql [file]';
    protected $arguments   = [
        'file' => 'Archivo SQL a ejecutar'
    ];

    public function run(array $params)
    {
        $file = $params[0] ?? null;
        
        if (!$file) {
            CLI::error('Debe especificar un archivo SQL');
            return;
        }

        if (!file_exists($file)) {
            CLI::error("Archivo no encontrado: {$file}");
            return;
        }

        $sql = file_get_contents($file);
        $db = \Config\Database::connect();

        try {
            CLI::write("Ejecutando SQL desde: {$file}", 'yellow');
            CLI::newLine();

            // Dividir por punto y coma para ejecutar múltiples consultas
            $queries = array_filter(array_map('trim', explode(';', $sql)));

            foreach ($queries as $query) {
                if (empty($query) || strpos($query, '--') === 0) {
                    continue;
                }

                CLI::write("Ejecutando: " . substr($query, 0, 50) . "...", 'cyan');
                $result = $db->query($query);
                
                if ($result) {
                    CLI::write("✓ Ejecutado correctamente", 'green');
                } else {
                    CLI::write("✗ Error ejecutando consulta", 'red');
                }
            }

            CLI::newLine();
            CLI::write("SQL ejecutado correctamente", 'green');

        } catch (\Exception $e) {
            CLI::error("Error ejecutando SQL: " . $e->getMessage());
        }
    }
}
