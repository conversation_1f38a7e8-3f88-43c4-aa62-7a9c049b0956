<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ContactMessageModel;
use App\Models\UserModel;

class ContactController extends BaseController
{
    protected $contactModel;
    protected $userModel;
    protected $db;

    public function __construct()
    {
        $this->contactModel = new ContactMessageModel();
        $this->userModel = new UserModel();
        $this->db = \Config\Database::connect();
    }

    /**
     * Lista de mensajes de contacto
     */
    public function index()
    {
        // Verificar autenticación
        if (!session()->get('admin_id')) {
            return redirect()->to('/admin/login');
        }

        $perPage = 20;
        $page = $this->request->getGet('page') ?? 1;
        $offset = ($page - 1) * $perPage;

        // Obtener filtros
        $filters = [
            'status' => $this->request->getGet('status'),
            'priority' => $this->request->getGet('priority'),
            'search' => $this->request->getGet('search'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        // Limpiar filtros vacíos
        $filters = array_filter($filters);

        try {
            // Obtener mensajes con filtros
            $messages = $this->contactModel->getMessagesWithFilters($filters, $perPage, $offset);
            $totalMessages = $this->contactModel->countMessagesWithFilters($filters);
            $totalPages = ceil($totalMessages / $perPage);

            // Obtener estadísticas
            $stats = $this->contactModel->getMessageStats();

            $data = [
                'title' => 'Mensajes de Contacto - Admin',
                'messages' => $messages,
                'stats' => $stats,
                'filters' => $filters,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $totalMessages,
                    'per_page' => $perPage
                ]
            ];

            return view('admin/contact/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar mensajes de contacto: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Error al cargar los mensajes de contacto');
        }
    }

    /**
     * Ver detalle de un mensaje
     */
    public function view($id = null)
    {
        if (!session()->get('admin_id')) {
            return redirect()->to('/admin/login');
        }

        if (!$id) {
            return redirect()->to('/admin/contact')->with('error', 'ID de mensaje requerido');
        }

        try {
            $message = $this->contactModel->find($id);

            if (!$message) {
                return redirect()->to('/admin/contact')->with('error', 'Mensaje no encontrado');
            }

            // Marcar como leído si está pendiente
            if ($message['status'] === 'pending') {
                $this->contactModel->markAsRead($id, session()->get('admin_id'));
                $message['status'] = 'read';
            }

            // Obtener otros mensajes del mismo email
            $relatedMessages = $this->contactModel->getMessagesByEmail($message['email'], 5);

            // Obtener respuestas (si implementas el sistema de respuestas)
            $replies = $this->getMessageReplies($id);

            $data = [
                'title' => 'Ver Mensaje - Admin',
                'message' => $message,
                'related_messages' => $relatedMessages,
                'replies' => $replies
            ];

            return view('admin/contact/view', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al ver mensaje de contacto: ' . $e->getMessage());
            return redirect()->to('/admin/contact')->with('error', 'Error al cargar el mensaje');
        }
    }

    /**
     * Actualizar estado de un mensaje
     */
    public function updateStatus($id = null)
    {
        if (!session()->get('admin_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'No autorizado']);
        }

        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'ID requerido']);
        }

        $newStatus = $this->request->getPost('status');
        $priority = $this->request->getPost('priority');
        $adminNotes = $this->request->getPost('admin_notes');

        if (!in_array($newStatus, ['pending', 'read', 'replied', 'closed'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Estado inválido']);
        }

        try {
            $updateData = ['status' => $newStatus];

            if ($priority && in_array($priority, ['low', 'normal', 'high', 'urgent'])) {
                $updateData['priority'] = $priority;
            }

            if ($adminNotes) {
                $updateData['admin_notes'] = trim($adminNotes);
            }

            if ($newStatus === 'replied') {
                $updateData['replied_at'] = date('Y-m-d H:i:s');
                $updateData['replied_by'] = session()->get('admin_id');
            }

            $result = $this->contactModel->update($id, $updateData);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Estado actualizado correctamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error al actualizar el estado'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al actualizar estado de mensaje: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Eliminar mensaje (soft delete)
     */
    public function delete($id = null)
    {
        if (!session()->get('admin_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'No autorizado']);
        }

        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'ID requerido']);
        }

        try {
            $result = $this->contactModel->delete($id);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Mensaje eliminado correctamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error al eliminar el mensaje'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al eliminar mensaje: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Exportar mensajes a CSV
     */
    public function export()
    {
        if (!session()->get('admin_id')) {
            return redirect()->to('/admin/login');
        }

        try {
            // Obtener filtros
            $filters = [
                'status' => $this->request->getGet('status'),
                'priority' => $this->request->getGet('priority'),
                'date_from' => $this->request->getGet('date_from'),
                'date_to' => $this->request->getGet('date_to')
            ];

            $filters = array_filter($filters);

            // Obtener todos los mensajes con filtros
            $messages = $this->contactModel->getMessagesWithFilters($filters, 10000, 0);

            // Generar CSV
            $filename = 'mensajes_contacto_' . date('Y-m-d_H-i-s') . '.csv';
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $output = fopen('php://output', 'w');
            
            // Encabezados
            fputcsv($output, [
                'ID', 'Fecha', 'Nombre', 'Email', 'Teléfono', 'Asunto', 
                'Mensaje', 'Estado', 'Prioridad', 'IP'
            ]);
            
            // Datos
            foreach ($messages as $message) {
                fputcsv($output, [
                    $message['id'],
                    $message['created_at'],
                    $message['name'],
                    $message['email'],
                    $message['phone'] ?? '',
                    $message['subject'],
                    $message['message'],
                    $message['status'],
                    $message['priority'],
                    $message['ip_address'] ?? ''
                ]);
            }
            
            fclose($output);
            exit;

        } catch (\Exception $e) {
            log_message('error', 'Error al exportar mensajes: ' . $e->getMessage());
            return redirect()->to('/admin/contact')->with('error', 'Error al exportar los mensajes');
        }
    }

    /**
     * Obtener respuestas de un mensaje (placeholder para futura implementación)
     */
    private function getMessageReplies($messageId)
    {
        // Aquí se implementaría la lógica para obtener respuestas
        // de la tabla contact_message_replies
        return [];
    }

    /**
     * Dashboard de estadísticas de contacto
     */
    public function dashboard()
    {
        if (!session()->get('admin_id')) {
            return redirect()->to('/admin/login');
        }

        try {
            $stats = $this->contactModel->getMessageStats();
            $recentMessages = $this->contactModel->getRecentMessages(10);

            $data = [
                'title' => 'Dashboard de Contacto - Admin',
                'stats' => $stats,
                'recent_messages' => $recentMessages
            ];

            return view('admin/contact/dashboard', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error al cargar dashboard de contacto: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Error al cargar el dashboard');
        }
    }
}
