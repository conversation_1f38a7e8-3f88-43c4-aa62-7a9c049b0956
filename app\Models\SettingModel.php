<?php

namespace App\Models;

use CodeIgniter\Model;

class SettingModel extends Model
{
    protected $table            = 'system_settings';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'setting_key',
        'setting_value',
        'setting_group',
        'display_name',
        'description',
        'setting_type',
        'options',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'setting_key' => 'required|max_length[100]|is_unique[system_settings.setting_key,id,{id}]',
        'setting_value' => 'permit_empty',
        'setting_type' => 'required|in_list[text,textarea,select,checkbox,number,email,url]',
        'setting_group' => 'required|max_length[50]'
    ];

    protected $validationMessages = [
        'setting_key' => [
            'required' => 'La clave de configuración es requerida',
            'max_length' => 'La clave no puede exceder 100 caracteres',
            'is_unique' => 'Esta clave de configuración ya existe'
        ],
        'setting_type' => [
            'required' => 'El tipo de configuración es requerido',
            'in_list' => 'Tipo de configuración no válido'
        ],
        'setting_group' => [
            'required' => 'El grupo es requerido',
            'max_length' => 'El grupo no puede exceder 50 caracteres'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Obtener configuración por clave
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)->first();
        return $setting ? $setting['setting_value'] : $default;
    }

    /**
     * Actualizar configuración
     */
    public function updateSetting($key, $value)
    {
        $existing = $this->where('setting_key', $key)->first();
        
        if ($existing) {
            return $this->update($existing['id'], ['setting_value' => $value]);
        } else {
            return $this->insert([
                'setting_key' => $key,
                'setting_value' => $value,
                'type' => 'text',
                'category' => 'general'
            ]);
        }
    }

    /**
     * Obtener configuraciones por grupo
     */
    public function getSettingsByGroup($group)
    {
        return $this->where('setting_group', $group)
                   ->where('is_active', 1)
                   ->orderBy('setting_key', 'ASC')
                   ->findAll();
    }

    /**
     * Obtener configuraciones de pago
     */
    public function getPaymentSettings()
    {
        return $this->where('setting_group', 'payment')
                   ->where('is_active', 1)
                   ->orderBy('setting_key', 'ASC')
                   ->findAll();
    }

    /**
     * Obtener configuraciones de Recurrente
     */
    public function getRecurrenteSettings()
    {
        $settings = $this->where('setting_key LIKE', 'recurrente_%')
                        ->findAll();
        
        $config = [];
        foreach ($settings as $setting) {
            $key = str_replace('recurrente_', '', $setting['setting_key']);
            $config[$key] = $setting['setting_value'];
        }
        
        return $config;
    }

    /**
     * Verificar si Recurrente está habilitado
     */
    public function isRecurrenteEnabled()
    {
        return $this->getSetting('recurrente_enabled', '0') === '1';
    }

    /**
     * Obtener configuración completa de Recurrente
     */
    public function getRecurrenteConfig()
    {
        $settings = $this->getRecurrenteSettings();
        $mode = $settings['mode'] ?? 'test';

        // Seleccionar las claves según el modo
        $publicKey = $mode === 'live'
            ? ($settings['public_key_live'] ?? '')
            : ($settings['public_key_test'] ?? '');

        $secretKey = $mode === 'live'
            ? ($settings['secret_key_live'] ?? '')
            : ($settings['secret_key_test'] ?? '');

        return [
            'enabled' => ($settings['enabled'] ?? '0') === '1',
            'mode' => $mode,
            'public_key' => $publicKey,
            'secret_key' => $secretKey,
            'webhook_secret' => $settings['webhook_secret'] ?? '',
            'currency' => $settings['currency'] ?? 'GTQ',
            'fee_percentage' => floatval($settings['fee_percentage'] ?? 3.9),
            'base_url' => 'https://app.recurrente.com/api', // URL base de la API
            // Mantener acceso a todas las claves para configuración
            'test_keys' => [
                'public_key' => $settings['public_key_test'] ?? '',
                'secret_key' => $settings['secret_key_test'] ?? ''
            ],
            'live_keys' => [
                'public_key' => $settings['public_key_live'] ?? '',
                'secret_key' => $settings['secret_key_live'] ?? ''
            ]
        ];
    }

    /**
     * Actualizar múltiples configuraciones
     */
    public function updateMultipleSettings($settings)
    {
        $this->db->transStart();

        foreach ($settings as $key => $value) {
            $this->updateSetting($key, $value);
        }

        $this->db->transComplete();

        return $this->db->transStatus();
    }

    /**
     * Obtener configuraciones de moneda
     */
    public function getCurrencySettings()
    {
        $settings = $this->where('setting_group', 'currency')
                        ->where('is_active', 1)
                        ->findAll();

        $config = [];
        foreach ($settings as $setting) {
            $config[$setting['setting_key']] = $setting['setting_value'];
        }

        return $config;
    }

    /**
     * Obtener tipo de cambio actual
     */
    public function getExchangeRate()
    {
        return floatval($this->getSetting('exchange_rate_usd_to_gtq', 7.75));
    }

    /**
     * Actualizar tipo de cambio
     */
    public function updateExchangeRate($rate)
    {
        return $this->updateSetting('exchange_rate_usd_to_gtq', $rate);
    }

    /**
     * Obtener moneda por defecto
     */
    public function getDefaultCurrency()
    {
        return $this->getSetting('default_currency', 'GTQ');
    }

    /**
     * Verificar si mostrar conversión de moneda
     */
    public function shouldShowCurrencyConversion()
    {
        return $this->getSetting('show_currency_conversion', '1') === '1';
    }

    /**
     * Obtener símbolos de moneda
     */
    public function getCurrencySymbols()
    {
        return [
            'GTQ' => $this->getSetting('currency_symbol_gtq', 'Q'),
            'USD' => $this->getSetting('currency_symbol_usd', '$')
        ];
    }

    /**
     * Obtener configuración completa de monedas
     */
    public function getCurrencyConfig()
    {
        $settings = $this->getCurrencySettings();

        return [
            'default_currency' => $settings['default_currency'] ?? 'GTQ',
            'exchange_rate' => floatval($settings['exchange_rate_usd_to_gtq'] ?? 7.75),
            'show_conversion' => ($settings['show_currency_conversion'] ?? '1') === '1',
            'symbols' => [
                'GTQ' => $settings['currency_symbol_gtq'] ?? 'Q',
                'USD' => $settings['currency_symbol_usd'] ?? '$'
            ]
        ];
    }
}
