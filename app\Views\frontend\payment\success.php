<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Pago Exitoso - MrCell Guatemala' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --success-color: #28a745;
            --success-light: #d4edda;
            --primary-color: #007bff;
        }
        
        .success-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .success-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .success-header {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
        }
        
        .order-details {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .payment-details {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .status-paid {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: #ffc107;
            color: #856404;
        }
        
        .status-pending-verification {
            background: #17a2b8;
            color: white;
        }
        
        .next-steps {
            background: #e7f3ff;
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            border-radius: 0 10px 10px 0;
            margin-bottom: 25px;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        
        .btn-outline-custom {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-outline-custom:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--success-color);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
        }
        
        .confetti {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
</head>
<body>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-mobile-alt me-2"></i>MrCell
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?= base_url() ?>">
                    <i class="fas fa-home me-1"></i>Inicio
                </a>
            </div>
        </div>
    </nav>

    <div class="success-container">
        <div class="container">
            <div class="success-card">
                <div class="success-header">
                    <div class="success-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <h2>¡Pago Exitoso!</h2>
                    <p class="mb-0">Tu pedido ha sido procesado correctamente</p>
                </div>

                <div class="p-4">
                    <!-- Detalles del Pedido -->
                    <div class="order-details">
                        <h5><i class="fas fa-receipt me-2"></i>Detalles del Pedido</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Número de Pedido:</strong> <?= $order['order_number'] ?></p>
                                <p><strong>Cliente:</strong> <?= $order['customer_name'] ?></p>
                                <p><strong>Email:</strong> <?= $order['customer_email'] ?></p>
                                <p><strong>Teléfono:</strong> <?= $order['customer_phone'] ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Total Pagado:</strong> Q<?= number_format($order['total'], 2) ?></p>
                                <p><strong>Estado del Pedido:</strong> 
                                    <span class="status-badge status-<?= $order['status'] ?>">
                                        <?= ucfirst($order['status']) ?>
                                    </span>
                                </p>
                                <p><strong>Fecha:</strong> <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Detalles del Pago -->
                    <?php if ($payment): ?>
                    <div class="payment-details">
                        <h5><i class="fas fa-credit-card me-2"></i>Información del Pago</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Método de Pago:</strong> 
                                    <?php
                                    $methods = [
                                        'transfer' => 'Transferencia Bancaria',
                                        'paypal' => 'PayPal',
                                        'card' => 'Tarjeta de Crédito/Débito',
                                        'cash' => 'Pago Contra Entrega'
                                    ];
                                    echo $methods[$payment['payment_method']] ?? $payment['payment_method'];
                                    ?>
                                </p>
                                <p><strong>Estado del Pago:</strong> 
                                    <span class="status-badge status-<?= $payment['status'] ?>">
                                        <?php
                                        $statuses = [
                                            'completed' => 'Completado',
                                            'pending' => 'Pendiente',
                                            'pending_verification' => 'Pendiente de Verificación',
                                            'failed' => 'Fallido'
                                        ];
                                        echo $statuses[$payment['status']] ?? $payment['status'];
                                        ?>
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Referencia:</strong> <?= $payment['reference_number'] ?></p>
                                <p><strong>Monto:</strong> Q<?= number_format($payment['amount'], 2) ?></p>
                                <?php if ($payment['payment_date']): ?>
                                    <p><strong>Fecha de Pago:</strong> <?= date('d/m/Y H:i', strtotime($payment['payment_date'])) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($payment['notes']): ?>
                            <div class="mt-3">
                                <p><strong>Notas:</strong> <?= $payment['notes'] ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Próximos Pasos -->
                    <div class="next-steps">
                        <h6><i class="fas fa-list-check me-2"></i>Próximos Pasos</h6>
                        <div class="timeline">
                            <?php if ($payment && $payment['status'] === 'pending_verification'): ?>
                                <div class="timeline-item">
                                    <strong>Verificación de Pago</strong><br>
                                    <small class="text-muted">Verificaremos tu transferencia en las próximas 24 horas</small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="timeline-item">
                                <strong>Confirmación por Email</strong><br>
                                <small class="text-muted">Recibirás un email de confirmación en <?= $order['customer_email'] ?></small>
                            </div>
                            
                            <div class="timeline-item">
                                <strong>Preparación del Pedido</strong><br>
                                <small class="text-muted">Comenzaremos a preparar tu pedido para envío</small>
                            </div>
                            
                            <div class="timeline-item">
                                <strong>Envío</strong><br>
                                <small class="text-muted">Te notificaremos cuando tu pedido sea enviado con el número de tracking</small>
                            </div>
                            
                            <div class="timeline-item">
                                <strong>Entrega</strong><br>
                                <small class="text-muted">Recibirás tu pedido en la dirección proporcionada</small>
                            </div>
                        </div>
                    </div>

                    <!-- Botones de Acción -->
                    <div class="action-buttons">
                        <a href="<?= base_url() ?>" class="btn btn-primary btn-primary-custom">
                            <i class="fas fa-home me-2"></i>Volver al Inicio
                        </a>
                        
                        <a href="<?= base_url('tienda') ?>" class="btn btn-outline-secondary btn-outline-custom">
                            <i class="fas fa-shopping-bag me-2"></i>Seguir Comprando
                        </a>
                        
                        <button onclick="window.print()" class="btn btn-outline-secondary btn-outline-custom">
                            <i class="fas fa-print me-2"></i>Imprimir Recibo
                        </button>
                    </div>

                    <!-- Información de Contacto -->
                    <div class="text-center mt-4 pt-4 border-top">
                        <h6>¿Necesitas Ayuda?</h6>
                        <p class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:+50299998888">+502 9999-8888</a>
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Lunes a Viernes: 8:00 AM - 6:00 PM
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animación de confetti al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            // Crear efecto de confetti simple
            createConfetti();
            
            // Auto-scroll suave al contenido
            setTimeout(function() {
                document.querySelector('.success-card').scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }, 500);
        });

        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
            const confettiContainer = document.createElement('div');
            confettiContainer.className = 'confetti';
            document.body.appendChild(confettiContainer);

            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'absolute';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                confetti.style.animationName = 'fall';
                confetti.style.animationTimingFunction = 'linear';
                confetti.style.animationFillMode = 'forwards';
                confettiContainer.appendChild(confetti);
            }

            // Remover confetti después de 5 segundos
            setTimeout(function() {
                confettiContainer.remove();
            }, 5000);
        }

        // CSS para animación de confetti
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                0% {
                    transform: translateY(-100vh) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
