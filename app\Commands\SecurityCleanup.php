<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\Database;

class SecurityCleanup extends BaseCommand
{
    protected $group       = 'Security';
    protected $name        = 'security:cleanup';
    protected $description = 'Limpia logs de seguridad antiguos y optimiza tablas de seguridad';
    protected $usage       = 'security:cleanup [options]';
    protected $arguments   = [];
    protected $options     = [
        '--days'    => 'Días de retención para logs (default: 30)',
        '--dry-run' => 'Mostrar qué se eliminaría sin hacer cambios',
        '--force'   => 'Forzar limpieza sin confirmación'
    ];

    public function run(array $params)
    {
        $db = Database::connect();
        $days = (int)($params['days'] ?? 30);
        $dryRun = isset($params['dry-run']);
        $force = isset($params['force']);

        CLI::write('=== Limpieza de Seguridad ===', 'yellow');
        CLI::write("Días de retención: {$days}");
        CLI::write("Modo: " . ($dryRun ? 'DRY RUN' : 'EJECUCIÓN'));
        CLI::newLine();

        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        CLI::write("Eliminando registros anteriores a: {$cutoffDate}");
        CLI::newLine();

        // 1. Limpiar eventos de seguridad
        $this->cleanSecurityEvents($db, $cutoffDate, $dryRun);

        // 2. Limpiar violaciones de rate limit
        $this->cleanRateLimitViolations($db, $cutoffDate, $dryRun);

        // 3. Limpiar intentos de login fallidos
        $this->cleanFailedLoginAttempts($db, $cutoffDate, $dryRun);

        // 4. Limpiar logs de auditoría antiguos
        $this->cleanAuditLogs($db, $cutoffDate, $dryRun);

        // 5. Limpiar IPs bloqueadas expiradas
        $this->cleanExpiredBlockedIPs($db, $dryRun);

        // 6. Optimizar tablas
        if (!$dryRun) {
            $this->optimizeTables($db);
        }

        // 7. Generar reporte de limpieza
        $this->generateCleanupReport($db, $cutoffDate);

        CLI::newLine();
        CLI::write('=== Limpieza Completada ===', 'green');
    }

    private function cleanSecurityEvents($db, string $cutoffDate, bool $dryRun): void
    {
        CLI::write('Limpiando eventos de seguridad...', 'cyan');

        try {
            // Contar registros a eliminar
            $count = $db->table('security_events')
                       ->where('created_at <', $cutoffDate)
                       ->where('is_resolved', 1) // Solo eliminar eventos resueltos
                       ->countAllResults();

            CLI::write("  - Eventos de seguridad a eliminar: {$count}");

            if (!$dryRun && $count > 0) {
                $deleted = $db->table('security_events')
                             ->where('created_at <', $cutoffDate)
                             ->where('is_resolved', 1)
                             ->delete();
                
                CLI::write("  - Eliminados: {$deleted}", 'green');
            }

        } catch (\Exception $e) {
            CLI::write("  - Error: " . $e->getMessage(), 'red');
        }
    }

    private function cleanRateLimitViolations($db, string $cutoffDate, bool $dryRun): void
    {
        CLI::write('Limpiando violaciones de rate limit...', 'cyan');

        try {
            $count = $db->table('rate_limit_violations')
                       ->where('created_at <', $cutoffDate)
                       ->countAllResults();

            CLI::write("  - Violaciones de rate limit a eliminar: {$count}");

            if (!$dryRun && $count > 0) {
                $deleted = $db->table('rate_limit_violations')
                             ->where('created_at <', $cutoffDate)
                             ->delete();
                
                CLI::write("  - Eliminadas: {$deleted}", 'green');
            }

        } catch (\Exception $e) {
            CLI::write("  - Error: " . $e->getMessage(), 'red');
        }
    }

    private function cleanFailedLoginAttempts($db, string $cutoffDate, bool $dryRun): void
    {
        CLI::write('Limpiando intentos de login fallidos...', 'cyan');

        try {
            $count = $db->table('failed_login_attempts')
                       ->where('created_at <', $cutoffDate)
                       ->countAllResults();

            CLI::write("  - Intentos fallidos a eliminar: {$count}");

            if (!$dryRun && $count > 0) {
                $deleted = $db->table('failed_login_attempts')
                             ->where('created_at <', $cutoffDate)
                             ->delete();
                
                CLI::write("  - Eliminados: {$deleted}", 'green');
            }

        } catch (\Exception $e) {
            CLI::write("  - Error: " . $e->getMessage(), 'red');
        }
    }

    private function cleanAuditLogs($db, string $cutoffDate, bool $dryRun): void
    {
        CLI::write('Limpiando logs de auditoría...', 'cyan');

        try {
            // Mantener logs críticos por más tiempo
            $criticalActions = ['user_deleted', 'admin_created', 'permissions_changed'];
            
            $count = $db->table('audit_logs')
                       ->where('created_at <', $cutoffDate)
                       ->whereNotIn('action', $criticalActions)
                       ->countAllResults();

            CLI::write("  - Logs de auditoría a eliminar: {$count}");

            if (!$dryRun && $count > 0) {
                $deleted = $db->table('audit_logs')
                             ->where('created_at <', $cutoffDate)
                             ->whereNotIn('action', $criticalActions)
                             ->delete();
                
                CLI::write("  - Eliminados: {$deleted}", 'green');
            }

        } catch (\Exception $e) {
            CLI::write("  - Error: " . $e->getMessage(), 'red');
        }
    }

    private function cleanExpiredBlockedIPs($db, bool $dryRun): void
    {
        CLI::write('Limpiando IPs bloqueadas expiradas...', 'cyan');

        try {
            $now = date('Y-m-d H:i:s');
            
            $count = $db->table('blocked_ips')
                       ->where('expires_at <', $now)
                       ->where('expires_at IS NOT NULL')
                       ->where('is_active', 1)
                       ->countAllResults();

            CLI::write("  - IPs bloqueadas expiradas: {$count}");

            if (!$dryRun && $count > 0) {
                // Desactivar en lugar de eliminar para mantener historial
                $updated = $db->table('blocked_ips')
                             ->where('expires_at <', $now)
                             ->where('expires_at IS NOT NULL')
                             ->where('is_active', 1)
                             ->update(['is_active' => 0]);
                
                CLI::write("  - Desactivadas: {$updated}", 'green');
            }

        } catch (\Exception $e) {
            CLI::write("  - Error: " . $e->getMessage(), 'red');
        }
    }

    private function optimizeTables($db): void
    {
        CLI::write('Optimizando tablas de seguridad...', 'cyan');

        $tables = [
            'security_events',
            'rate_limit_violations',
            'failed_login_attempts',
            'audit_logs',
            'blocked_ips'
        ];

        foreach ($tables as $table) {
            try {
                $db->query("OPTIMIZE TABLE {$table}");
                CLI::write("  - Tabla {$table} optimizada", 'green');
            } catch (\Exception $e) {
                CLI::write("  - Error optimizando {$table}: " . $e->getMessage(), 'red');
            }
        }
    }

    private function generateCleanupReport($db, string $cutoffDate): void
    {
        CLI::write('Generando reporte de limpieza...', 'cyan');

        try {
            // Estadísticas actuales
            $stats = [
                'security_events' => $db->table('security_events')->countAllResults(),
                'rate_limit_violations' => $db->table('rate_limit_violations')->countAllResults(),
                'failed_login_attempts' => $db->table('failed_login_attempts')->countAllResults(),
                'audit_logs' => $db->table('audit_logs')->countAllResults(),
                'blocked_ips_active' => $db->table('blocked_ips')->where('is_active', 1)->countAllResults()
            ];

            CLI::newLine();
            CLI::write('=== Estadísticas Actuales ===', 'yellow');
            foreach ($stats as $table => $count) {
                CLI::write("  - {$table}: {$count} registros");
            }

            // Eventos de seguridad por tipo (últimos 7 días)
            $recentEvents = $db->table('security_events')
                              ->select('event_type, COUNT(*) as count')
                              ->where('created_at >', date('Y-m-d H:i:s', strtotime('-7 days')))
                              ->groupBy('event_type')
                              ->orderBy('count', 'DESC')
                              ->get()
                              ->getResultArray();

            if (!empty($recentEvents)) {
                CLI::newLine();
                CLI::write('=== Eventos de Seguridad (Últimos 7 días) ===', 'yellow');
                foreach ($recentEvents as $event) {
                    CLI::write("  - {$event['event_type']}: {$event['count']} eventos");
                }
            }

            // IPs más bloqueadas
            $topBlockedIPs = $db->table('blocked_ips')
                               ->select('ip_address, violation_count, reason')
                               ->where('is_active', 1)
                               ->orderBy('violation_count', 'DESC')
                               ->limit(5)
                               ->get()
                               ->getResultArray();

            if (!empty($topBlockedIPs)) {
                CLI::newLine();
                CLI::write('=== Top IPs Bloqueadas ===', 'yellow');
                foreach ($topBlockedIPs as $ip) {
                    CLI::write("  - {$ip['ip_address']}: {$ip['violation_count']} violaciones ({$ip['reason']})");
                }
            }

        } catch (\Exception $e) {
            CLI::write("  - Error generando reporte: " . $e->getMessage(), 'red');
        }
    }
}
