<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\CurrencyRateModel;

class CurrencyRates extends BaseController
{
    protected $currencyRateModel;

    public function __construct()
    {
        $this->currencyRateModel = new CurrencyRateModel();
        helper('currency');
    }

    /**
     * Mostrar lista de tasas de cambio
     */
    public function index()
    {
        $data = [
            'title' => 'Tasas de Cambio',
            'rates' => $this->currencyRateModel->getActiveRates(),
            'currencies' => $this->currencyRateModel->getAvailableCurrencies(),
            'config' => config('Currency')
        ];

        return view('admin/currency_rates/index', $data);
    }

    /**
     * Crear nueva tasa de cambio
     */
    public function create()
    {
        $config = config('Currency');
        
        $data = [
            'title' => 'Nueva Tasa de Cambio',
            'currencies' => array_keys($config->supported_currencies)
        ];

        return view('admin/currency_rates/create', $data);
    }

    /**
     * Guardar nueva tasa de cambio
     */
    public function store()
    {
        $rules = [
            'from_currency' => 'required|max_length[3]',
            'to_currency' => 'required|max_length[3]',
            'rate' => 'required|decimal|greater_than[0]',
            'source' => 'permit_empty|max_length[50]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'from_currency' => $this->request->getPost('from_currency'),
            'to_currency' => $this->request->getPost('to_currency'),
            'rate' => $this->request->getPost('rate'),
            'source' => $this->request->getPost('source') ?: 'manual',
            'is_active' => true
        ];

        // Verificar si ya existe la tasa
        $existing = $this->currencyRateModel->where([
            'from_currency' => $data['from_currency'],
            'to_currency' => $data['to_currency']
        ])->first();

        if ($existing) {
            // Actualizar tasa existente
            $this->currencyRateModel->update($existing['id'], $data);
            $message = 'Tasa de cambio actualizada exitosamente';
        } else {
            // Crear nueva tasa
            $this->currencyRateModel->insert($data);
            $message = 'Tasa de cambio creada exitosamente';
        }

        // Limpiar caché
        clear_currency_cache($data['from_currency'], $data['to_currency']);

        return redirect()->to('/admin/currency-rates')->with('success', $message);
    }

    /**
     * Editar tasa de cambio
     */
    public function edit($id)
    {
        $rate = $this->currencyRateModel->find($id);
        
        if (!$rate) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tasa de cambio no encontrada');
        }

        $config = config('Currency');
        
        $data = [
            'title' => 'Editar Tasa de Cambio',
            'rate' => $rate,
            'currencies' => array_keys($config->supported_currencies)
        ];

        return view('admin/currency_rates/edit', $data);
    }

    /**
     * Actualizar tasa de cambio
     */
    public function update($id)
    {
        $rate = $this->currencyRateModel->find($id);
        
        if (!$rate) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tasa de cambio no encontrada');
        }

        $rules = [
            'rate' => 'required|decimal|greater_than[0]',
            'source' => 'permit_empty|max_length[50]',
            'is_active' => 'permit_empty|in_list[0,1]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'rate' => $this->request->getPost('rate'),
            'source' => $this->request->getPost('source') ?: 'manual',
            'is_active' => $this->request->getPost('is_active') ? true : false
        ];

        $this->currencyRateModel->update($id, $data);

        // Limpiar caché
        clear_currency_cache($rate['from_currency'], $rate['to_currency']);

        return redirect()->to('/admin/currency-rates')->with('success', 'Tasa de cambio actualizada exitosamente');
    }

    /**
     * Eliminar tasa de cambio
     */
    public function delete($id)
    {
        $rate = $this->currencyRateModel->find($id);
        
        if (!$rate) {
            return redirect()->to('/admin/currency-rates')->with('error', 'Tasa de cambio no encontrada');
        }

        // Limpiar caché antes de eliminar
        clear_currency_cache($rate['from_currency'], $rate['to_currency']);

        $this->currencyRateModel->delete($id);

        return redirect()->to('/admin/currency-rates')->with('success', 'Tasa de cambio eliminada exitosamente');
    }

    /**
     * Limpiar todo el caché de tasas de cambio
     */
    public function clearCache()
    {
        clear_currency_cache();
        
        return redirect()->to('/admin/currency-rates')->with('success', 'Caché de tasas de cambio limpiado exitosamente');
    }

    /**
     * API: Obtener tasa de cambio específica
     */
    public function getRate()
    {
        $fromCurrency = $this->request->getGet('from');
        $toCurrency = $this->request->getGet('to');

        if (!$fromCurrency || !$toCurrency) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Parámetros from y to son requeridos'
            ]);
        }

        $rate = get_cached_exchange_rate($fromCurrency, $toCurrency);

        return $this->response->setJSON([
            'success' => true,
            'rate' => $rate,
            'from' => $fromCurrency,
            'to' => $toCurrency
        ]);
    }
}
