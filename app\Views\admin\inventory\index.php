<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-warehouse me-2"></i>Gestión de Inventario</h1>
        <div>
            <button class="btn btn-success me-2" onclick="showAddStockModal()">
                <i class="fas fa-plus me-2"></i>Agregar Stock
            </button>
            <button class="btn btn-warning me-2" onclick="showAdjustmentModal()">
                <i class="fas fa-edit me-2"></i>Ajustar Stock
            </button>
            <button class="btn btn-primary" onclick="refreshInventory()">
                <i class="fas fa-sync-alt me-2"></i>Actualizar
            </button>
        </div>
    </div>
</div>

<!-- Estadísticas de Inventario -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-primary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-database"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['total_products']) ? $stats['total_products'] : 0 ?></h3>
                        <small class="text-muted">Total Histórico</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-success text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['active_products']) ? $stats['active_products'] : 0 ?></h3>
                        <small class="text-muted">Activos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-secondary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-trash"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['deleted_products']) ? $stats['deleted_products'] : 0 ?></h3>
                        <small class="text-muted">Eliminados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-box"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['normal_stock']) ? $stats['normal_stock'] : 0 ?></h3>
                        <small class="text-muted">Stock Normal</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['low_stock']) ? $stats['low_stock'] : 0 ?></h3>
                        <small class="text-muted">Stock Bajo</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-danger text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= isset($stats['no_stock']) ? $stats['no_stock'] : 0 ?></h3>
                        <small class="text-muted">Sin Stock</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="/admin/inventory" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">Buscar Producto</label>
                <input type="text" class="form-control" name="search" value="<?= esc($search ?? '') ?>" placeholder="Nombre, SKU, descripción...">
            </div>
            <div class="col-md-4">
                <label class="form-label">Estado</label>
                <select class="form-select" name="status">
                    <option value="all" <?= ($current_status ?? 'active') === 'all' ? 'selected' : '' ?>>Todos</option>
                    <option value="active" <?= ($current_status ?? 'active') === 'active' ? 'selected' : '' ?>>Activos</option>
                    <option value="inactive" <?= ($current_status ?? 'active') === 'inactive' ? 'selected' : '' ?>>Inactivos</option>
                    <option value="deleted" <?= ($current_status ?? 'active') === 'deleted' ? 'selected' : '' ?>>Eliminados</option>
                    <option value="out_of_stock" <?= ($current_status ?? 'active') === 'out_of_stock' ? 'selected' : '' ?>>Sin Stock</option>
                    <option value="low_stock" <?= ($current_status ?? 'active') === 'low_stock' ? 'selected' : '' ?>>Stock Bajo</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Buscar
                </button>
                <a href="/admin/inventory" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Limpiar
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Inventario -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Inventario de Productos</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Producto</th>
                        <th>SKU</th>
                        <th>Estado Producto</th>
                        <th>Stock Actual</th>
                        <th>Stock Mínimo</th>
                        <th>Estado Stock</th>
                        <th>Precio</th>
                        <th>Caducidad</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (isset($products) && !empty($products)): ?>
                        <?php foreach ($products as $product): ?>
                        <tr class="<?= isset($product['deleted_at']) && $product['deleted_at'] !== null ? 'table-secondary' : (isset($product['is_active']) && $product['is_active'] == 0 ? 'table-warning' : '') ?>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong><?= esc($product['name']) ?></strong>
                                        <?php if (!empty($product['category_name'])): ?>
                                            <br><small class="text-muted"><?= esc($product['category_name']) ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($product['deleted_at']) && $product['deleted_at'] !== null): ?>
                                            <br><small class="text-danger"><i class="fas fa-trash"></i> Eliminado: <?= date('d/m/Y', strtotime($product['deleted_at'])) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td><?= esc($product['sku']) ?></td>
                            <td>
                                <?php if (isset($product['product_status'])): ?>
                                <span class="badge bg-<?= $product['product_status_class'] ?>">
                                    <?= esc($product['product_status']) ?>
                                </span>
                                <?php else: ?>
                                <span class="badge bg-success">Activo</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= $product['stock_status_class'] ?>">
                                    <?= $product['stock_quantity'] ?>
                                </span>
                            </td>
                            <td><?= $product['stock_min'] ?? 0 ?></td>
                            <td>
                                <span class="badge bg-<?= $product['stock_status_class'] ?>">
                                    <?= esc($product['stock_status']) ?>
                                </span>
                            </td>
                            <td>Q<?= number_format($product['price_regular'], 2) ?></td>
                            <td>
                                <?php
                                helper('expiration');
                                if (!empty($product['has_expiration']) && !empty($product['expiration_date'])):
                                    echo getExpirationStatusBadge($product);
                                    echo '<br><small class="text-muted">' . formatExpirationDate($product['expiration_date']) . '</small>';
                                else:
                                    echo '<span class="text-muted">N/A</span>';
                                endif;
                                ?>
                            </td>
                            <td>
                                <?php if (!isset($product['deleted_at']) || $product['deleted_at'] === null): ?>
                                    <?php if (!isset($product['is_active']) || $product['is_active'] == 1): ?>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-success" onclick="addStock('<?= esc($product['sku']) ?>')" title="Agregar Stock">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="adjustStock('<?= esc($product['sku']) ?>')" title="Ajustar Stock">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="viewMovements('<?= esc($product['sku']) ?>')" title="Ver Movimientos">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                    <?php else: ?>
                                    <small class="text-muted">Producto inactivo</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                <small class="text-muted">Producto eliminado</small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-box-open fa-2x mb-2"></i>
                                <br>No hay productos en el inventario
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para gestión de inventario
    function showAddStockModal() {
        console.log('Agregar stock');
        alert('Funcionalidad de agregar stock en desarrollo');
    }

    function showAdjustmentModal() {
        console.log('Ajustar stock');
        alert('Funcionalidad de ajustar stock en desarrollo');
    }

    function refreshInventory() {
        console.log('Refrescar inventario');
        location.reload();
    }

    function addStock(sku) {
        console.log('Agregar stock para:', sku);
        alert('Funcionalidad de agregar stock en desarrollo');
    }

    function adjustStock(sku) {
        console.log('Ajustar stock para:', sku);
        alert('Funcionalidad de ajustar stock en desarrollo');
    }

    function viewMovements(sku) {
        console.log('Ver movimientos para:', sku);
        alert('Funcionalidad de ver movimientos en desarrollo');
    }

    function applyFilters() {
        console.log('Aplicar filtros');
        // Aquí iría la lógica para aplicar los filtros
    }
</script>
<?= $this->endSection() ?>
