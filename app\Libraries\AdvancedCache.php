<?php

namespace App\Libraries;

/**
 * Sistema de Cache Avanzado
 * Cache inteligente con múltiples drivers y estrategias
 */
class AdvancedCache
{
    private $config;
    private $drivers;
    private $currentDriver;
    private $logger;
    
    public function __construct()
    {
        $this->config = [
            'enabled' => env('ADVANCED_CACHE_ENABLED', true),
            'default_driver' => env('CACHE_DRIVER', 'file'),
            'ttl_default' => env('CACHE_TTL_DEFAULT', 3600), // 1 hora
            'ttl_long' => env('CACHE_TTL_LONG', 86400), // 24 horas
            'ttl_short' => env('CACHE_TTL_SHORT', 300), // 5 minutos
            'prefix' => env('CACHE_PREFIX', 'mrcell_'),
            'compression' => env('CACHE_COMPRESSION', true),
            'serialization' => env('CACHE_SERIALIZATION', 'json'),
            'auto_cleanup' => env('CACHE_AUTO_CLEANUP', true),
            'max_size' => env('CACHE_MAX_SIZE', 104857600), // 100MB
            'stats_enabled' => env('CACHE_STATS_ENABLED', true)
        ];
        
        $this->logger = new AdvancedLogger();
        $this->initializeDrivers();
    }
    
    /**
     * Obtener valor del cache
     */
    public function get(string $key, $default = null)
    {
        if (!$this->config['enabled']) {
            return $default;
        }
        
        try {
            $fullKey = $this->buildKey($key);
            $startTime = microtime(true);
            
            $value = $this->currentDriver->get($fullKey);
            
            $duration = microtime(true) - $startTime;
            
            if ($value !== null) {
                $this->recordStats('hit', $key, $duration);
                $this->logger->debug("Cache hit: $key", ['duration_ms' => $duration * 1000]);
                return $this->deserialize($value);
            } else {
                $this->recordStats('miss', $key, $duration);
                $this->logger->debug("Cache miss: $key", ['duration_ms' => $duration * 1000]);
                return $default;
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Cache get error: " . $e->getMessage(), ['key' => $key]);
            return $default;
        }
    }
    
    /**
     * Establecer valor en cache
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->config['enabled']) {
            return false;
        }
        
        try {
            $fullKey = $this->buildKey($key);
            $ttl = $ttl ?? $this->config['ttl_default'];
            $startTime = microtime(true);
            
            $serializedValue = $this->serialize($value);
            $result = $this->currentDriver->set($fullKey, $serializedValue, $ttl);
            
            $duration = microtime(true) - $startTime;
            
            if ($result) {
                $this->recordStats('set', $key, $duration, strlen($serializedValue));
                $this->logger->debug("Cache set: $key", [
                    'ttl' => $ttl,
                    'size' => strlen($serializedValue),
                    'duration_ms' => $duration * 1000
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("Cache set error: " . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }
    
    /**
     * Eliminar valor del cache
     */
    public function delete(string $key): bool
    {
        if (!$this->config['enabled']) {
            return false;
        }
        
        try {
            $fullKey = $this->buildKey($key);
            $result = $this->currentDriver->delete($fullKey);
            
            if ($result) {
                $this->recordStats('delete', $key);
                $this->logger->debug("Cache delete: $key");
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("Cache delete error: " . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }
    
    /**
     * Verificar si existe una clave
     */
    public function has(string $key): bool
    {
        if (!$this->config['enabled']) {
            return false;
        }
        
        try {
            $fullKey = $this->buildKey($key);
            return $this->currentDriver->has($fullKey);
            
        } catch (\Exception $e) {
            $this->logger->error("Cache has error: " . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }
    
    /**
     * Obtener o establecer (cache-aside pattern)
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }
    
    /**
     * Obtener múltiples valores
     */
    public function getMultiple(array $keys, $default = null): array
    {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->get($key, $default);
        }
        
        return $results;
    }
    
    /**
     * Establecer múltiples valores
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        $success = true;
        
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value, $ttl)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Eliminar múltiples valores
     */
    public function deleteMultiple(array $keys): bool
    {
        $success = true;
        
        foreach ($keys as $key) {
            if (!$this->delete($key)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Limpiar todo el cache
     */
    public function clear(): bool
    {
        try {
            $result = $this->currentDriver->clear();
            
            if ($result) {
                $this->recordStats('clear');
                $this->logger->info("Cache cleared completely");
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("Cache clear error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Limpiar cache por patrón
     */
    public function clearByPattern(string $pattern): int
    {
        try {
            $keys = $this->getKeysByPattern($pattern);
            $deleted = 0;
            
            foreach ($keys as $key) {
                if ($this->delete($key)) {
                    $deleted++;
                }
            }
            
            $this->logger->info("Cache cleared by pattern: $pattern", ['deleted_keys' => $deleted]);
            
            return $deleted;
            
        } catch (\Exception $e) {
            $this->logger->error("Cache clear by pattern error: " . $e->getMessage(), ['pattern' => $pattern]);
            return 0;
        }
    }
    
    /**
     * Cache de productos
     */
    public function cacheProduct(int $productId, array $productData, int $ttl = null): bool
    {
        $key = "product:$productId";
        return $this->set($key, $productData, $ttl ?? $this->config['ttl_long']);
    }
    
    /**
     * Obtener producto del cache
     */
    public function getProduct(int $productId): ?array
    {
        $key = "product:$productId";
        return $this->get($key);
    }
    
    /**
     * Cache de categorías
     */
    public function cacheCategory(int $categoryId, array $categoryData, int $ttl = null): bool
    {
        $key = "category:$categoryId";
        return $this->set($key, $categoryData, $ttl ?? $this->config['ttl_long']);
    }
    
    /**
     * Cache de consultas de base de datos
     */
    public function cacheQuery(string $sql, array $params, array $result, int $ttl = null): bool
    {
        $key = "query:" . md5($sql . serialize($params));
        return $this->set($key, $result, $ttl ?? $this->config['ttl_short']);
    }
    
    /**
     * Obtener consulta del cache
     */
    public function getQuery(string $sql, array $params): ?array
    {
        $key = "query:" . md5($sql . serialize($params));
        return $this->get($key);
    }
    
    /**
     * Cache de sesiones de usuario
     */
    public function cacheUserSession(int $userId, array $sessionData, int $ttl = null): bool
    {
        $key = "user_session:$userId";
        return $this->set($key, $sessionData, $ttl ?? $this->config['ttl_default']);
    }
    
    /**
     * Cache de configuraciones
     */
    public function cacheConfig(string $configKey, $configValue, int $ttl = null): bool
    {
        $key = "config:$configKey";
        return $this->set($key, $configValue, $ttl ?? $this->config['ttl_long']);
    }
    
    /**
     * Cache de métricas
     */
    public function cacheMetrics(string $metricType, array $metricsData, int $ttl = null): bool
    {
        $key = "metrics:$metricType:" . date('Y-m-d-H');
        return $this->set($key, $metricsData, $ttl ?? $this->config['ttl_short']);
    }
    
    /**
     * Invalidar cache relacionado
     */
    public function invalidateRelated(string $type, int $id): int
    {
        $patterns = [
            'product' => ["product:$id", "category:*", "search:*"],
            'category' => ["category:$id", "product:*", "menu:*"],
            'user' => ["user_session:$id", "user_data:$id"],
            'order' => ["user_session:*", "metrics:*"]
        ];
        
        $deleted = 0;
        
        if (isset($patterns[$type])) {
            foreach ($patterns[$type] as $pattern) {
                $deleted += $this->clearByPattern($pattern);
            }
        }
        
        return $deleted;
    }
    
    /**
     * Precalentar cache
     */
    public function warmup(array $keys = []): array
    {
        $results = [
            'warmed' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        $defaultKeys = [
            'config:site_settings',
            'config:payment_methods',
            'menu:main',
            'categories:active'
        ];
        
        $keysToWarm = empty($keys) ? $defaultKeys : $keys;
        
        foreach ($keysToWarm as $key) {
            try {
                // Simular carga de datos y cache
                $data = $this->loadDataForKey($key);
                if ($this->set($key, $data)) {
                    $results['warmed']++;
                } else {
                    $results['failed']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Error warming $key: " . $e->getMessage();
            }
        }
        
        $this->logger->info("Cache warmup completed", $results);
        
        return $results;
    }
    
    /**
     * Obtener estadísticas del cache
     */
    public function getStats(): array
    {
        if (!$this->config['stats_enabled']) {
            return ['stats_disabled' => true];
        }
        
        try {
            $stats = $this->currentDriver->getStats();
            
            // Agregar estadísticas personalizadas
            $customStats = $this->getCustomStats();
            
            return array_merge($stats, $customStats);
            
        } catch (\Exception $e) {
            $this->logger->error("Cache stats error: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Optimizar cache
     */
    public function optimize(): array
    {
        $results = [
            'cleaned_expired' => 0,
            'compressed_keys' => 0,
            'freed_space' => 0
        ];
        
        try {
            // Limpiar entradas expiradas
            $results['cleaned_expired'] = $this->cleanExpired();
            
            // Comprimir entradas grandes
            $results['compressed_keys'] = $this->compressLargeEntries();
            
            // Calcular espacio liberado
            $results['freed_space'] = $this->calculateFreedSpace();
            
            $this->logger->info("Cache optimization completed", $results);
            
            return $results;
            
        } catch (\Exception $e) {
            $this->logger->error("Cache optimization error: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Inicializar drivers de cache
     */
    private function initializeDrivers(): void
    {
        $this->drivers = [];
        
        // Driver de archivos
        $this->drivers['file'] = new FileCache([
            'path' => WRITEPATH . 'cache/',
            'prefix' => $this->config['prefix']
        ]);
        
        // Driver Redis (si está disponible)
        if (extension_loaded('redis')) {
            $this->drivers['redis'] = new RedisCache([
                'host' => env('REDIS_HOST', '127.0.0.1'),
                'port' => env('REDIS_PORT', 6379),
                'prefix' => $this->config['prefix']
            ]);
        }
        
        // Driver Memcached (si está disponible)
        if (extension_loaded('memcached')) {
            $this->drivers['memcached'] = new MemcachedCache([
                'host' => env('MEMCACHED_HOST', '127.0.0.1'),
                'port' => env('MEMCACHED_PORT', 11211),
                'prefix' => $this->config['prefix']
            ]);
        }
        
        // Establecer driver actual
        $driverName = $this->config['default_driver'];
        if (isset($this->drivers[$driverName])) {
            $this->currentDriver = $this->drivers[$driverName];
        } else {
            $this->currentDriver = $this->drivers['file'];
        }
    }
    
    /**
     * Construir clave completa
     */
    private function buildKey(string $key): string
    {
        return $this->config['prefix'] . $key;
    }
    
    /**
     * Serializar valor
     */
    private function serialize($value): string
    {
        switch ($this->config['serialization']) {
            case 'json':
                $serialized = json_encode($value);
                break;
            case 'serialize':
                $serialized = serialize($value);
                break;
            default:
                $serialized = (string)$value;
        }
        
        if ($this->config['compression'] && strlen($serialized) > 1024) {
            $serialized = gzcompress($serialized);
        }
        
        return $serialized;
    }
    
    /**
     * Deserializar valor
     */
    private function deserialize(string $value)
    {
        // Verificar si está comprimido
        if ($this->config['compression'] && $this->isCompressed($value)) {
            $value = gzuncompress($value);
        }
        
        switch ($this->config['serialization']) {
            case 'json':
                return json_decode($value, true);
            case 'serialize':
                return unserialize($value);
            default:
                return $value;
        }
    }
    
    /**
     * Verificar si un valor está comprimido
     */
    private function isCompressed(string $value): bool
    {
        return substr($value, 0, 2) === "\x1f\x8b" || substr($value, 0, 3) === "\x78\x9c" || substr($value, 0, 3) === "\x78\x01";
    }
    
    /**
     * Registrar estadísticas
     */
    private function recordStats(string $operation, string $key = '', float $duration = 0, int $size = 0): void
    {
        if (!$this->config['stats_enabled']) {
            return;
        }
        
        // Implementar registro de estadísticas
        // Por ahora, solo log
        $this->logger->debug("Cache stats", [
            'operation' => $operation,
            'key' => $key,
            'duration_ms' => $duration * 1000,
            'size' => $size
        ]);
    }
    
    /**
     * Obtener claves por patrón
     */
    private function getKeysByPattern(string $pattern): array
    {
        // Implementación básica para driver de archivos
        if (method_exists($this->currentDriver, 'getKeysByPattern')) {
            return $this->currentDriver->getKeysByPattern($pattern);
        }
        
        return [];
    }
    
    /**
     * Cargar datos para una clave específica
     */
    private function loadDataForKey(string $key)
    {
        // Implementar carga de datos según el tipo de clave
        switch (true) {
            case strpos($key, 'config:') === 0:
                return $this->loadConfigData($key);
            case strpos($key, 'menu:') === 0:
                return $this->loadMenuData($key);
            case strpos($key, 'categories:') === 0:
                return $this->loadCategoriesData($key);
            default:
                return null;
        }
    }
    
    /**
     * Cargar datos de configuración
     */
    private function loadConfigData(string $key): array
    {
        // Simular carga de configuración
        return ['loaded' => true, 'key' => $key, 'timestamp' => time()];
    }
    
    /**
     * Cargar datos de menú
     */
    private function loadMenuData(string $key): array
    {
        // Simular carga de menú
        return ['menu_items' => [], 'loaded' => true, 'timestamp' => time()];
    }
    
    /**
     * Cargar datos de categorías
     */
    private function loadCategoriesData(string $key): array
    {
        // Simular carga de categorías
        return ['categories' => [], 'loaded' => true, 'timestamp' => time()];
    }
    
    /**
     * Obtener estadísticas personalizadas
     */
    private function getCustomStats(): array
    {
        return [
            'driver' => $this->config['default_driver'],
            'prefix' => $this->config['prefix'],
            'compression_enabled' => $this->config['compression'],
            'auto_cleanup_enabled' => $this->config['auto_cleanup']
        ];
    }
    
    /**
     * Limpiar entradas expiradas
     */
    private function cleanExpired(): int
    {
        if (method_exists($this->currentDriver, 'cleanExpired')) {
            return $this->currentDriver->cleanExpired();
        }
        
        return 0;
    }
    
    /**
     * Comprimir entradas grandes
     */
    private function compressLargeEntries(): int
    {
        // Implementar compresión de entradas grandes
        return 0;
    }
    
    /**
     * Calcular espacio liberado
     */
    private function calculateFreedSpace(): int
    {
        // Implementar cálculo de espacio liberado
        return 0;
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'driver' => $this->config['default_driver'],
            'available_drivers' => array_keys($this->drivers),
            'ttl_default' => $this->config['ttl_default'],
            'compression' => $this->config['compression'],
            'stats_enabled' => $this->config['stats_enabled']
        ];
    }
}

/**
 * Driver de cache de archivos básico
 */
class FileCache
{
    private $path;
    private $prefix;
    
    public function __construct(array $config)
    {
        $this->path = $config['path'];
        $this->prefix = $config['prefix'];
        
        if (!is_dir($this->path)) {
            mkdir($this->path, 0755, true);
        }
    }
    
    public function get(string $key)
    {
        $file = $this->getFilePath($key);
        
        if (!file_exists($file)) {
            return null;
        }
        
        $content = file_get_contents($file);
        $data = unserialize($content);
        
        if ($data['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $data['value'];
    }
    
    public function set(string $key, $value, int $ttl): bool
    {
        $file = $this->getFilePath($key);
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        return file_put_contents($file, serialize($data), LOCK_EX) !== false;
    }
    
    public function delete(string $key): bool
    {
        $file = $this->getFilePath($key);
        
        if (file_exists($file)) {
            return unlink($file);
        }
        
        return true;
    }
    
    public function has(string $key): bool
    {
        return $this->get($key) !== null;
    }
    
    public function clear(): bool
    {
        $files = glob($this->path . $this->prefix . '*');
        
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    public function getStats(): array
    {
        $files = glob($this->path . $this->prefix . '*');
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        
        return [
            'keys' => count($files),
            'size' => $totalSize
        ];
    }
    
    private function getFilePath(string $key): string
    {
        return $this->path . $key . '.cache';
    }
}
