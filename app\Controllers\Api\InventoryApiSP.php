<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class InventoryApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Registrar movimiento de inventario
     * POST /api/inventory/movement
     */
    public function registerMovement()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'product_id' => 'required|integer',
                'movement_type' => 'required|in_list[in,out,adjustment,transfer]',
                'quantity' => 'required|integer|greater_than[0]',
                'reason' => 'required|max_length[255]',
                'reference_type' => 'permit_empty|in_list[order,purchase,adjustment,return,damage,transfer]',
                'reference_id' => 'permit_empty|integer',
                'user_id' => 'permit_empty|integer',
                'notes' => 'permit_empty|string'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para registrar movimiento
            $query = $this->db->query("CALL sp_register_inventory_movement(?, ?, ?, ?, ?, ?, ?, ?, @movement_id, @result)", [
                $data['product_id'],
                $data['movement_type'],
                $data['quantity'],
                $data['reason'],
                $data['reference_type'] ?? null,
                $data['reference_id'] ?? null,
                $data['user_id'] ?? null,
                $data['notes'] ?? null
            ]);

            // Obtener resultados
            $resultQuery = $this->db->query("SELECT @movement_id as movement_id, @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'data' => ['movement_id' => $result['movement_id']],
                    'message' => 'Movimiento de inventario registrado correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::registerMovement: ' . $e->getMessage());
            return $this->failServerError('Error al registrar movimiento de inventario');
        }
    }

    /**
     * Obtener movimientos de inventario
     * GET /api/inventory/movements
     */
    public function getMovements()
    {
        try {
            $productId = $this->request->getGet('product_id');
            $movementType = $this->request->getGet('movement_type');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');
            $limit = (int) ($this->request->getGet('limit') ?? 50);
            $offset = (int) ($this->request->getGet('offset') ?? 0);

            $query = $this->db->query("CALL sp_get_inventory_movements(?, ?, ?, ?, ?, ?)", [
                $productId, $movementType, $dateFrom, $dateTo, $limit, $offset
            ]);

            $movements = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $movements,
                'message' => 'Movimientos de inventario obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::getMovements: ' . $e->getMessage());
            return $this->failServerError('Error al obtener movimientos de inventario');
        }
    }

    /**
     * Obtener alertas de inventario
     * GET /api/inventory/alerts
     */
    public function getAlerts()
    {
        try {
            $alertType = $this->request->getGet('alert_type');
            $isResolved = $this->request->getGet('is_resolved');
            
            // Convertir string a boolean
            if ($isResolved !== null) {
                $isResolved = $isResolved === 'true' ? 1 : 0;
            }

            $query = $this->db->query("CALL sp_get_inventory_alerts(?, ?)", [
                $alertType, $isResolved
            ]);

            $alerts = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $alerts,
                'message' => 'Alertas de inventario obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::getAlerts: ' . $e->getMessage());
            return $this->failServerError('Error al obtener alertas de inventario');
        }
    }

    /**
     * Obtener estadísticas de inventario
     * GET /api/inventory/stats
     */
    public function getStats()
    {
        try {
            // Estadísticas generales
            $statsQuery = $this->db->query("
                SELECT 
                    COUNT(DISTINCT p.id) as total_products,
                    SUM(p.stock_quantity) as total_stock,
                    COUNT(CASE WHEN p.stock_quantity <= 5 THEN 1 END) as low_stock_count,
                    COUNT(CASE WHEN p.stock_quantity = 0 THEN 1 END) as out_of_stock_count,
                    AVG(p.stock_quantity) as avg_stock,
                    SUM(p.stock_quantity * p.price_regular) as inventory_value
                FROM products p
                WHERE p.deleted_at IS NULL AND p.is_active = 1
            ");
            $stats = $statsQuery->getRowArray();

            // Movimientos recientes (últimos 7 días)
            $movementsQuery = $this->db->query("
                SELECT 
                    movement_type,
                    COUNT(*) as count,
                    SUM(quantity) as total_quantity
                FROM inventory_movements 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY movement_type
            ");
            $recentMovements = $movementsQuery->getResultArray();

            // Alertas activas
            $alertsQuery = $this->db->query("
                SELECT 
                    alert_type,
                    COUNT(*) as count
                FROM inventory_alerts 
                WHERE is_active = 1 AND is_resolved = 0
                GROUP BY alert_type
            ");
            $activeAlerts = $alertsQuery->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'general_stats' => $stats,
                    'recent_movements' => $recentMovements,
                    'active_alerts' => $activeAlerts
                ],
                'message' => 'Estadísticas de inventario obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::getStats: ' . $e->getMessage());
            return $this->failServerError('Error al obtener estadísticas de inventario');
        }
    }

    /**
     * Ajustar stock de producto
     * POST /api/inventory/adjust
     */
    public function adjustStock()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'product_id' => 'required|integer',
                'new_stock' => 'required|integer|greater_than_equal_to[0]',
                'reason' => 'required|max_length[255]',
                'user_id' => 'permit_empty|integer',
                'notes' => 'permit_empty|string'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para ajustar stock
            $query = $this->db->query("CALL sp_register_inventory_movement(?, 'adjustment', ?, ?, 'adjustment', NULL, ?, ?, @movement_id, @result)", [
                $data['product_id'],
                $data['new_stock'],
                $data['reason'],
                $data['user_id'] ?? null,
                $data['notes'] ?? null
            ]);

            // Obtener resultados
            $resultQuery = $this->db->query("SELECT @movement_id as movement_id, @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'data' => ['movement_id' => $result['movement_id']],
                    'message' => 'Stock ajustado correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::adjustStock: ' . $e->getMessage());
            return $this->failServerError('Error al ajustar stock');
        }
    }

    /**
     * Resolver alerta de inventario
     * PUT /api/inventory/alerts/{id}/resolve
     */
    public function resolveAlert($alertId = null)
    {
        try {
            if (!$alertId) {
                return $this->failValidationError('ID de alerta requerido');
            }

            $query = $this->db->query("
                UPDATE inventory_alerts 
                SET is_resolved = 1, resolved_at = NOW()
                WHERE id = ? AND is_active = 1
            ", [$alertId]);

            if ($this->db->affectedRows() > 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Alerta resuelta correctamente'
                ]);
            } else {
                return $this->failNotFound('Alerta no encontrada o ya resuelta');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::resolveAlert: ' . $e->getMessage());
            return $this->failServerError('Error al resolver alerta');
        }
    }

    /**
     * Obtener productos con stock bajo
     * GET /api/inventory/low-stock
     */
    public function getLowStock()
    {
        try {
            $threshold = (int) ($this->request->getGet('threshold') ?? 5);
            $limit = (int) ($this->request->getGet('limit') ?? 20);

            $query = $this->db->query("CALL sp_get_low_stock_products(?, ?)", [
                $threshold, $limit
            ]);

            $products = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $products,
                'message' => 'Productos con stock bajo obtenidos correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en InventoryApiSP::getLowStock: ' . $e->getMessage());
            return $this->failServerError('Error al obtener productos con stock bajo');
        }
    }
}
