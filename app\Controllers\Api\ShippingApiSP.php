<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class ShippingApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener métodos de envío usando SP
     */
    public function methods()
    {
        try {
            $includeRates = $this->request->getGet('include_rates') === 'true';

            $query = $this->db->query("CALL sp_get_shipping_methods(?)", [$includeRates]);
            
            // Obtener métodos
            $methods = $query->getResultArray();
            
            $data = ['methods' => $methods];

            // Si se solicitaron las tarifas, obtenerlas con consulta separada
            if ($includeRates) {
                $ratesQuery = $this->db->query("
                    SELECT
                        sr.*,
                        sm.name as method_name
                    FROM shipping_rates sr
                    INNER JOIN shipping_methods sm ON sm.id = sr.shipping_method_id
                    WHERE sr.is_active = 1 AND sm.is_active = 1
                    ORDER BY sm.sort_order, sr.min_weight, sr.min_volume
                ");
                $data['rates'] = $ratesQuery->getResultArray();
            }

            return $this->respond([
                'status' => 'success',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::methods: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Crear método de envío usando SP
     */
    public function createMethod()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|max_length[100]',
                'description' => 'permit_empty',
                'sort_order' => 'permit_empty|integer'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_create_shipping_method(?, ?, ?, @method_id, @result)", [
                $json['name'],
                $json['description'] ?? '',
                $json['sort_order'] ?? 0
            ]);

            $result = $this->db->query("SELECT @method_id as method_id, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => ['method_id' => $result->method_id]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::createMethod: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Crear tarifa de envío usando SP
     */
    public function createRate()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'shipping_method_id' => 'required|integer',
                'min_weight' => 'required|decimal|greater_than_equal_to[0]',
                'max_weight' => 'permit_empty|decimal',
                'min_volume' => 'required|decimal|greater_than_equal_to[0]',
                'max_volume' => 'permit_empty|decimal',
                'rate' => 'required|decimal|greater_than_equal_to[0]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $query = $this->db->query("CALL sp_create_shipping_rate(?, ?, ?, ?, ?, ?, @rate_id, @result)", [
                $json['shipping_method_id'],
                $json['min_weight'],
                $json['max_weight'] ?? null,
                $json['min_volume'],
                $json['max_volume'] ?? null,
                $json['rate']
            ]);

            $result = $this->db->query("SELECT @rate_id as rate_id, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => ['rate_id' => $result->rate_id]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::createRate: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Calcular costo de envío para carrito usando SP
     */
    public function calculateCost()
    {
        try {
            $sessionId = $this->getSessionId();

            $query = $this->db->query("CALL sp_calculate_shipping_cost(?, @shipping_cost, @shipping_details, @result)", [$sessionId]);
            $result = $this->db->query("SELECT @shipping_cost as shipping_cost, @shipping_details as shipping_details, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                $shippingDetails = json_decode($result->shipping_details, true) ?? [];
                
                return $this->respond([
                    'status' => 'success',
                    'data' => [
                        'shipping_cost' => (float) $result->shipping_cost,
                        'details' => $shippingDetails,
                        'message' => str_replace('SUCCESS: ', '', $result->result)
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::calculateCost: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener tarifas por método
     */
    public function getRatesByMethod($methodId = null)
    {
        try {
            if (!$methodId || !is_numeric($methodId)) {
                return $this->fail('ID de método requerido', 400);
            }

            $query = $this->db->query("
                SELECT 
                    sr.*,
                    sm.name as method_name
                FROM shipping_rates sr
                INNER JOIN shipping_methods sm ON sm.id = sr.shipping_method_id
                WHERE sr.shipping_method_id = ? AND sr.is_active = 1
                ORDER BY sr.min_weight, sr.min_volume
            ", [(int) $methodId]);

            $rates = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $rates
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::getRatesByMethod: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Actualizar método de envío
     */
    public function updateMethod($methodId = null)
    {
        try {
            if (!$methodId || !is_numeric($methodId)) {
                return $this->fail('ID de método requerido', 400);
            }

            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->fail('Datos JSON requeridos', 400);
            }

            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|max_length[100]',
                'description' => 'permit_empty',
                'sort_order' => 'permit_empty|integer',
                'is_active' => 'permit_empty|in_list[0,1]'
            ]);

            if (!$validation->run($json)) {
                return $this->fail($validation->getErrors(), 400);
            }

            $this->db->query("
                UPDATE shipping_methods 
                SET name = ?, 
                    description = ?, 
                    sort_order = ?, 
                    is_active = ?,
                    updated_at = NOW()
                WHERE id = ?
            ", [
                $json['name'],
                $json['description'] ?? '',
                $json['sort_order'] ?? 0,
                $json['is_active'] ?? 1,
                (int) $methodId
            ]);

            return $this->respond([
                'status' => 'success',
                'message' => 'Método de envío actualizado correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ShippingApiSP::updateMethod: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener session ID para el carrito
     */
    private function getSessionId()
    {
        $session = session();
        $sessionId = $session->get('cart_session_id');
        if (!$sessionId) {
            $sessionId = session_id() ?: uniqid('cart_', true);
            $session->set('cart_session_id', $sessionId);
        }
        return $sessionId;
    }
}
