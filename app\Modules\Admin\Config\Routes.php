<?php

/**
 * Rutas del Módulo Admin
 * Configuración de rutas específicas para el panel administrativo
 */

// Rutas del módulo Admin
$routes->group('admin', ['namespace' => 'App\Modules\Admin\Controllers'], function ($routes) {
    
    // Rutas públicas (sin autenticación)
    $routes->get('login', 'AdminController::login');
    $routes->post('login', 'AdminController::authenticate');
    $routes->post('authenticate', 'AdminController::authenticate');
    $routes->get('logout', 'AdminController::logout');
    
    // Rutas protegidas (requieren autenticación)
    $routes->get('/', 'AdminController::index');
    $routes->get('dashboard', 'AdminController::dashboard');
    
    // Gestión de perfil
    $routes->get('profile', 'AdminController::profile');
    $routes->post('profile/update', 'AdminController::updateProfile');
    
    // Gestión de productos
    $routes->get('products', 'AdminController::products');
    $routes->post('products/create', 'AdminController::createProduct');
    $routes->get('products/(:num)', 'AdminController::viewProduct/$1');
    $routes->post('products/(:num)/update', 'AdminController::updateProduct/$1');
    $routes->delete('products/(:num)', 'AdminController::deleteProduct/$1');
    
    // Gestión de pedidos
    $routes->get('orders', 'AdminController::orders');
    $routes->get('orders/(:num)', 'AdminController::viewOrder/$1');
    $routes->post('orders/(:num)/update-status', 'AdminController::updateOrderStatus/$1');
    
    // Gestión de usuarios
    $routes->get('users', 'AdminController::users');
    $routes->post('users/create', 'AdminController::createUser');
    $routes->get('users/(:num)', 'AdminController::viewUser/$1');
    $routes->post('users/(:num)/update', 'AdminController::updateUser/$1');
    $routes->delete('users/(:num)', 'AdminController::deleteUser/$1');
    
    // Gestión de categorías
    $routes->get('categories', 'AdminController::categories');
    $routes->post('categories/create', 'AdminController::createCategory');
    $routes->get('categories/(:num)', 'AdminController::viewCategory/$1');
    $routes->post('categories/(:num)/update', 'AdminController::updateCategory/$1');
    $routes->delete('categories/(:num)', 'AdminController::deleteCategory/$1');
    
    // Gestión de inventario
    $routes->get('inventory', 'AdminController::inventory');
    $routes->post('inventory/update-stock', 'AdminController::updateStock');
    $routes->get('inventory/low-stock', 'AdminController::lowStock');
    
    // Gestión de envíos
    $routes->get('shipping', 'AdminController::shipping');
    $routes->post('shipping/methods/create', 'AdminController::createShippingMethod');
    $routes->post('shipping/methods/(:num)/update', 'AdminController::updateShippingMethod/$1');
    $routes->delete('shipping/methods/(:num)', 'AdminController::deleteShippingMethod/$1');
    
    // Reportes
    $routes->get('reports', 'AdminController::reports');
    $routes->get('reports/sales', 'AdminController::salesReport');
    $routes->get('reports/products', 'AdminController::productsReport');
    $routes->get('reports/users', 'AdminController::usersReport');
    
    // Notificaciones
    $routes->get('notifications', 'AdminController::notifications');
    $routes->post('notifications/mark-read/(:num)', 'AdminController::markNotificationRead/$1');
    $routes->post('notifications/mark-all-read', 'AdminController::markAllNotificationsRead');
    
    // Configuración
    $routes->get('settings', 'AdminController::settings');
    $routes->post('settings/update', 'AdminController::updateSettings');
    $routes->post('settings/update-payment-methods', 'AdminController::updatePaymentMethods');
    $routes->post('settings/update-notifications', 'AdminController::updateNotificationSettings');
    
    // API endpoints para AJAX
    $routes->group('api', function ($routes) {
        $routes->get('dashboard/stats', 'AdminController::getDashboardStats');
        $routes->get('products/search', 'AdminController::searchProducts');
        $routes->get('orders/recent', 'AdminController::getRecentOrders');
        $routes->get('users/search', 'AdminController::searchUsers');
        $routes->post('products/bulk-action', 'AdminController::bulkProductAction');
        $routes->post('orders/bulk-action', 'AdminController::bulkOrderAction');
    });
});
