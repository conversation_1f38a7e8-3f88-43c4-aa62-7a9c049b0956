<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class CartApiSP extends ResourceController
{
    use ResponseTrait;

    protected $session;
    protected $db;

    public function __construct()
    {
        $this->session = session();
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener session ID para el carrito
     */
    private function getSessionId()
    {
        $sessionId = $this->session->get('cart_session_id');
        if (!$sessionId) {
            $sessionId = session_id() ?: uniqid('cart_', true);
            $this->session->set('cart_session_id', $sessionId);
        }
        return $sessionId;
    }

    /**
     * Debug endpoint para verificar sesión
     */
    public function debug()
    {
        $sessionId = $this->getSessionId();

        // Obtener items directamente de la BD
        $query = $this->db->query("SELECT * FROM cart_items WHERE session_id = ?", [$sessionId]);
        $items = $query->getResultArray();

        return $this->respond([
            'session_id' => $sessionId,
            'php_session_id' => session_id(),
            'cart_session_id' => $this->session->get('cart_session_id'),
            'items_in_db' => $items,
            'all_sessions' => $this->db->query("SELECT DISTINCT session_id FROM cart_items")->getResultArray()
        ]);
    }

    /**
     * Agregar producto al carrito usando SP
     */
    public function add()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['product_id']) || !isset($json['quantity'])) {
                return $this->fail('Datos requeridos: product_id, quantity', 400);
            }

            $productId = (int) $json['product_id'];
            $quantity = (int) $json['quantity'];
            $sessionId = $this->getSessionId();

            if ($productId <= 0 || $quantity <= 0) {
                return $this->fail('product_id y quantity deben ser números positivos', 400);
            }

            // Usar stored procedure para agregar al carrito
            $query = $this->db->query("CALL sp_cart_add_item(?, ?, ?, @result)", [
                $sessionId, $productId, $quantity
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                // Obtener items actualizados del carrito
                $items = $this->getCartItems($sessionId);
                $totals = $this->getCartTotals($sessionId);

                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'items' => $items,
                        'totals' => $totals
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::add: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Actualizar cantidad de producto en carrito usando SP
     */
    public function updateItem()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['product_id']) || !isset($json['quantity'])) {
                return $this->fail('Datos requeridos: product_id, quantity', 400);
            }

            $productId = (int) $json['product_id'];
            $quantity = (int) $json['quantity'];
            $sessionId = $this->getSessionId();

            if ($productId <= 0) {
                return $this->fail('product_id debe ser un número positivo', 400);
            }

            // Usar stored procedure para actualizar carrito
            $query = $this->db->query("CALL sp_cart_update_item(?, ?, ?, @result)", [
                $sessionId, $productId, $quantity
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                // Obtener items actualizados del carrito
                $items = $this->getCartItems($sessionId);
                $totals = $this->getCartTotals($sessionId);

                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'items' => $items,
                        'totals' => $totals
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::update: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Eliminar producto del carrito usando SP
     */
    public function remove($productId = null)
    {
        try {
            if (!$productId) {
                return $this->fail('product_id es requerido', 400);
            }

            $productId = (int) $productId;
            $sessionId = $this->getSessionId();

            if ($productId <= 0) {
                return $this->fail('product_id debe ser un número positivo', 400);
            }

            // Usar stored procedure para eliminar del carrito
            $query = $this->db->query("CALL sp_cart_remove_item(?, ?, @result)", [
                $sessionId, $productId
            ]);

            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                // Obtener items actualizados del carrito
                $items = $this->getCartItems($sessionId);
                $totals = $this->getCartTotals($sessionId);

                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'items' => $items,
                        'totals' => $totals
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::remove: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener items del carrito usando SP
     */
    public function items()
    {
        try {
            $sessionId = $this->getSessionId();
            
            $items = $this->getCartItems($sessionId);
            $totals = $this->getCartTotals($sessionId);

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'items' => $items,
                    'totals' => $totals
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::items: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Calcular totales del carrito usando SP
     */
    public function totals()
    {
        try {
            $sessionId = $this->getSessionId();
            $shippingMethod = $this->request->getGet('shipping_method') ?? 'standard';
            $couponCode = $this->request->getGet('coupon_code') ?? '';

            $totals = $this->getCartTotals($sessionId, $shippingMethod, $couponCode);

            return $this->respond([
                'status' => 'success',
                'data' => $totals
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::totals: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Limpiar carrito usando SP
     */
    public function clear()
    {
        try {
            $sessionId = $this->getSessionId();

            // Usar stored procedure para limpiar carrito
            $query = $this->db->query("CALL sp_cart_clear(?, @result)", [$sessionId]);
            $result = $this->db->query("SELECT @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => str_replace('SUCCESS: ', '', $result->result),
                    'data' => [
                        'items' => [],
                        'totals' => [
                            'item_count' => 0,
                            'subtotal' => 0,
                            'tax_amount' => 0,
                            'shipping_cost' => 0,
                            'discount_amount' => 0,
                            'total' => 0
                        ]
                    ]
                ]);
            } else {
                return $this->fail(str_replace('ERROR: ', '', $result->result), 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::clear: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Validar carrito usando SP
     */
    public function validateCart()
    {
        try {
            $sessionId = $this->getSessionId();

            // Usar stored procedure para validar carrito
            $query = $this->db->query("CALL sp_cart_validate(?, @is_valid, @errors, @total)", [$sessionId]);
            $result = $this->db->query("SELECT @is_valid as is_valid, @errors as errors, @total as total")->getRow();

            $isValid = (bool) $result->is_valid;
            $errors = json_decode($result->errors, true) ?? [];
            $total = (float) $result->total;

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'is_valid' => $isValid,
                    'errors' => $errors,
                    'total' => $total
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartApiSP::validate: ' . $e->getMessage());
            return $this->fail('Error interno del servidor', 500);
        }
    }

    /**
     * Obtener items del carrito (método auxiliar)
     */
    private function getCartItems($sessionId)
    {
        try {
            // Usar consulta directa temporalmente hasta resolver problema con SP
            $query = $this->db->query("
                SELECT
                    ci.product_id,
                    ci.quantity,
                    ci.price,
                    (ci.quantity * ci.price) as subtotal,
                    p.name,
                    p.slug,
                    p.sku,
                    p.featured_image as image,
                    p.stock_quantity,
                    p.is_active,
                    ci.created_at,
                    ci.updated_at
                FROM cart_items ci
                INNER JOIN products p ON p.id = ci.product_id
                WHERE ci.session_id = ?
                  AND p.is_active = 1
                  AND p.deleted_at IS NULL
                ORDER BY ci.created_at ASC
            ", [$sessionId]);

            return $query->getResultArray();
        } catch (\Exception $e) {
            log_message('error', 'Error en getCartItems: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener totales del carrito (método auxiliar)
     */
    private function getCartTotals($sessionId, $shippingMethod = 'standard', $couponCode = '')
    {
        try {
            // Usar consulta directa ya que CodeIgniter no soporta nextRowset()
            $directQuery = $this->db->query("
                SELECT
                    COUNT(*) as item_count,
                    COALESCE(SUM(ci.quantity * ci.price), 0) as subtotal,
                    COALESCE(SUM(ci.quantity * ci.price), 0) * 0.12 as tax_amount,
                    25.00 as shipping_cost,
                    0 as discount_amount,
                    (COALESCE(SUM(ci.quantity * ci.price), 0) * 1.12 + 25.00) as total,
                    0.12 as tax_rate
                FROM cart_items ci
                INNER JOIN products p ON p.id = ci.product_id
                WHERE ci.session_id = ?
                  AND p.is_active = 1
                  AND p.deleted_at IS NULL
            ", [$sessionId]);

            $result = $directQuery->getRowArray();

            // Aplicar envío gratis si subtotal > 200
            if ($result && $result['subtotal'] >= 200) {
                $result['shipping_cost'] = 0;
                $result['total'] = $result['subtotal'] * 1.12;
            }

            return [
                'item_count' => (int) ($result['item_count'] ?? 0),
                'subtotal' => (float) ($result['subtotal'] ?? 0),
                'tax_amount' => (float) ($result['tax_amount'] ?? 0),
                'shipping_cost' => (float) ($result['shipping_cost'] ?? 0),
                'discount_amount' => (float) ($result['discount_amount'] ?? 0),
                'total' => (float) ($result['total'] ?? 0),
                'tax_rate' => (float) ($result['tax_rate'] ?? 0.12)
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error en getCartTotals: ' . $e->getMessage());
            return [
                'item_count' => 0,
                'subtotal' => 0,
                'tax_amount' => 0,
                'shipping_cost' => 0,
                'discount_amount' => 0,
                'total' => 0,
                'tax_rate' => 0.12
            ];
        }
    }
}
