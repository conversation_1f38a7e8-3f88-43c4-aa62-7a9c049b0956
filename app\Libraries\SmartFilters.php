<?php

namespace App\Libraries;

use App\Libraries\SimpleCache;

/**
 * Sistema de Filtros Inteligentes
 * Filtros dinámicos y contextuales para búsqueda
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SmartFilters
{
    private $db;
    private $cache;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new SimpleCache();
        $this->config = [
            'max_filter_options' => 50,
            'min_product_count' => 1,
            'enable_smart_suggestions' => true,
            'enable_price_ranges' => true,
            'enable_dynamic_filters' => true,
            'cache_ttl' => 1800 // 30 minutos
        ];
    }
    
    /**
     * Obtener filtros inteligentes basados en contexto
     * 
     * @param string $query Término de búsqueda
     * @param array $currentFilters Filtros ya aplicados
     * @param array $options Opciones adicionales
     * @return array Filtros disponibles
     */
    public function getSmartFilters(string $query = '', array $currentFilters = [], array $options = []): array
    {
        $cacheKey = 'smart_filters_' . md5(serialize([$query, $currentFilters, $options]));
        
        // Intentar obtener del cache
        $cached = $this->cache->get($cacheKey);
        if ($cached) {
            return $cached;
        }
        
        $filters = [
            'categories' => $this->getCategoryFilters($query, $currentFilters),
            'brands' => $this->getBrandFilters($query, $currentFilters),
            'price_ranges' => $this->getPriceRangeFilters($query, $currentFilters),
            'ratings' => $this->getRatingFilters($query, $currentFilters),
            'features' => $this->getFeatureFilters($query, $currentFilters),
            'availability' => $this->getAvailabilityFilters($query, $currentFilters),
            'discounts' => $this->getDiscountFilters($query, $currentFilters),
            'smart_suggestions' => $this->getSmartSuggestions($query, $currentFilters)
        ];
        
        // Aplicar lógica de filtros inteligentes
        $filters = $this->applySmartLogic($filters, $query, $currentFilters);
        
        // Guardar en cache
        $this->cache->set($cacheKey, $filters, $this->config['cache_ttl']);
        
        return $filters;
    }
    
    /**
     * Obtener filtros de categorías
     */
    private function getCategoryFilters(string $query, array $currentFilters): array
    {
        $builder = $this->db->table('categories c');
        $builder->select('c.id, c.name, c.slug, COUNT(p.id) as product_count');
        $builder->join('products p', 'c.id = p.category_id', 'left');
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        
        // Aplicar búsqueda si existe
        if (!empty($query)) {
            $builder->where("MATCH(p.name, p.description, p.sku) AGAINST(? IN BOOLEAN MODE)", $this->prepareSearchTerm($query));
        }
        
        // Aplicar filtros existentes (excepto categoría)
        $this->applyExistingFilters($builder, $currentFilters, ['category_id']);
        
        $builder->groupBy('c.id, c.name, c.slug');
        $builder->having('product_count >=', $this->config['min_product_count']);
        $builder->orderBy('product_count', 'DESC');
        $builder->limit($this->config['max_filter_options']);
        
        $query = $builder->get();
        $categories = $query->getResultArray();
        
        return [
            'type' => 'category',
            'label' => 'Categorías',
            'options' => $categories,
            'display_type' => 'list',
            'allow_multiple' => false
        ];
    }
    
    /**
     * Obtener filtros de marcas
     */
    private function getBrandFilters(string $query, array $currentFilters): array
    {
        $builder = $this->db->table('brands b');
        $builder->select('b.id, b.name, b.slug, b.logo, COUNT(p.id) as product_count');
        $builder->join('products p', 'b.id = p.brand_id', 'left');
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        
        // Aplicar búsqueda si existe
        if (!empty($query)) {
            $builder->where("MATCH(p.name, p.description, p.sku) AGAINST(? IN BOOLEAN MODE)", $this->prepareSearchTerm($query));
        }
        
        // Aplicar filtros existentes (excepto marca)
        $this->applyExistingFilters($builder, $currentFilters, ['brand_id']);
        
        $builder->groupBy('b.id, b.name, b.slug, b.logo');
        $builder->having('product_count >=', $this->config['min_product_count']);
        $builder->orderBy('product_count', 'DESC');
        $builder->limit($this->config['max_filter_options']);
        
        $query = $builder->get();
        $brands = $query->getResultArray();
        
        return [
            'type' => 'brand',
            'label' => 'Marcas',
            'options' => $brands,
            'display_type' => 'grid',
            'allow_multiple' => true,
            'show_logos' => true
        ];
    }
    
    /**
     * Obtener filtros de rangos de precio
     */
    private function getPriceRangeFilters(string $query, array $currentFilters): array
    {
        // Obtener estadísticas de precios
        $builder = $this->db->table('products p');
        $builder->select('MIN(COALESCE(p.price_sale, p.price_regular)) as min_price, 
                         MAX(COALESCE(p.price_sale, p.price_regular)) as max_price,
                         AVG(COALESCE(p.price_sale, p.price_regular)) as avg_price');
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        
        // Aplicar búsqueda si existe
        if (!empty($query)) {
            $builder->where("MATCH(p.name, p.description, p.sku) AGAINST(? IN BOOLEAN MODE)", $this->prepareSearchTerm($query));
        }
        
        // Aplicar filtros existentes (excepto precio)
        $this->applyExistingFilters($builder, $currentFilters, ['min_price', 'max_price']);
        
        $stats = $builder->get()->getRowArray();
        
        if (!$stats || $stats['min_price'] === null) {
            return [
                'type' => 'price_range',
                'label' => 'Precio',
                'options' => [],
                'display_type' => 'range'
            ];
        }
        
        // Generar rangos inteligentes
        $ranges = $this->generatePriceRanges($stats['min_price'], $stats['max_price'], $stats['avg_price']);
        
        // Contar productos en cada rango
        foreach ($ranges as &$range) {
            $count = $this->countProductsInPriceRange($range['min'], $range['max'], $query, $currentFilters);
            $range['product_count'] = $count;
        }
        
        // Filtrar rangos sin productos
        $ranges = array_filter($ranges, function($range) {
            return $range['product_count'] > 0;
        });
        
        return [
            'type' => 'price_range',
            'label' => 'Precio',
            'options' => array_values($ranges),
            'display_type' => 'range',
            'min_value' => $stats['min_price'],
            'max_value' => $stats['max_price'],
            'currency' => 'Q'
        ];
    }
    
    /**
     * Obtener filtros de rating
     */
    private function getRatingFilters(string $query, array $currentFilters): array
    {
        $ratings = [
            ['value' => 4, 'label' => '4 estrellas y más', 'min_rating' => 4.0],
            ['value' => 3, 'label' => '3 estrellas y más', 'min_rating' => 3.0],
            ['value' => 2, 'label' => '2 estrellas y más', 'min_rating' => 2.0],
            ['value' => 1, 'label' => '1 estrella y más', 'min_rating' => 1.0]
        ];
        
        // Contar productos para cada rating
        foreach ($ratings as &$rating) {
            $count = $this->countProductsByRating($rating['min_rating'], $query, $currentFilters);
            $rating['product_count'] = $count;
        }
        
        // Filtrar ratings sin productos
        $ratings = array_filter($ratings, function($rating) {
            return $rating['product_count'] > 0;
        });
        
        return [
            'type' => 'rating',
            'label' => 'Calificación',
            'options' => array_values($ratings),
            'display_type' => 'stars',
            'allow_multiple' => false
        ];
    }
    
    /**
     * Obtener filtros de características
     */
    private function getFeatureFilters(string $query, array $currentFilters): array
    {
        // Características comunes basadas en la búsqueda
        $features = [];
        
        // Detectar tipo de producto y sugerir características relevantes
        $productType = $this->detectProductType($query);
        
        switch ($productType) {
            case 'smartphone':
                $features = [
                    ['key' => 'storage', 'label' => 'Almacenamiento', 'values' => ['32GB', '64GB', '128GB', '256GB', '512GB']],
                    ['key' => 'ram', 'label' => 'RAM', 'values' => ['2GB', '3GB', '4GB', '6GB', '8GB', '12GB']],
                    ['key' => 'screen_size', 'label' => 'Pantalla', 'values' => ['5"', '5.5"', '6"', '6.5"', '7"']],
                    ['key' => 'camera', 'label' => 'Cámara', 'values' => ['12MP', '16MP', '20MP', '48MP', '64MP', '108MP']]
                ];
                break;
                
            case 'laptop':
                $features = [
                    ['key' => 'processor', 'label' => 'Procesador', 'values' => ['Intel i3', 'Intel i5', 'Intel i7', 'AMD Ryzen']],
                    ['key' => 'ram', 'label' => 'RAM', 'values' => ['4GB', '8GB', '16GB', '32GB']],
                    ['key' => 'storage', 'label' => 'Almacenamiento', 'values' => ['256GB SSD', '512GB SSD', '1TB SSD', '1TB HDD']],
                    ['key' => 'screen_size', 'label' => 'Pantalla', 'values' => ['13"', '14"', '15"', '17"']]
                ];
                break;
                
            case 'headphones':
                $features = [
                    ['key' => 'type', 'label' => 'Tipo', 'values' => ['In-ear', 'Over-ear', 'On-ear']],
                    ['key' => 'connectivity', 'label' => 'Conectividad', 'values' => ['Bluetooth', 'Inalámbrico', 'Con cable']],
                    ['key' => 'noise_cancelling', 'label' => 'Cancelación de ruido', 'values' => ['Sí', 'No']],
                    ['key' => 'battery', 'label' => 'Batería', 'values' => ['10h', '20h', '30h', '40h+']]
                ];
                break;
        }
        
        // Contar productos para cada característica
        foreach ($features as &$feature) {
            foreach ($feature['values'] as &$value) {
                // Esto sería más complejo en implementación real
                // Por ahora, asignamos conteos simulados
                $value = [
                    'value' => $value,
                    'product_count' => rand(1, 50)
                ];
            }
        }
        
        return [
            'type' => 'features',
            'label' => 'Características',
            'options' => $features,
            'display_type' => 'accordion',
            'allow_multiple' => true
        ];
    }
    
    /**
     * Obtener filtros de disponibilidad
     */
    private function getAvailabilityFilters(string $query, array $currentFilters): array
    {
        $availability = [
            [
                'key' => 'in_stock',
                'label' => 'En stock',
                'value' => 1,
                'count' => $this->countProductsByAvailability(true, $query, $currentFilters)
            ],
            [
                'key' => 'featured',
                'label' => 'Productos destacados',
                'value' => 1,
                'count' => $this->countProductsByFeatured(true, $query, $currentFilters)
            ],
            [
                'key' => 'new_arrivals',
                'label' => 'Nuevos productos',
                'value' => 1,
                'count' => $this->countNewProducts($query, $currentFilters)
            ]
        ];
        
        // Filtrar opciones sin productos
        $availability = array_filter($availability, function($option) {
            return $option['count'] > 0;
        });
        
        return [
            'type' => 'availability',
            'label' => 'Disponibilidad',
            'options' => array_values($availability),
            'display_type' => 'checkbox',
            'allow_multiple' => true
        ];
    }
    
    /**
     * Obtener filtros de descuentos
     */
    private function getDiscountFilters(string $query, array $currentFilters): array
    {
        $discounts = [
            ['key' => 'on_sale', 'label' => 'En oferta', 'min_discount' => 0],
            ['key' => 'discount_10', 'label' => '10% o más descuento', 'min_discount' => 10],
            ['key' => 'discount_25', 'label' => '25% o más descuento', 'min_discount' => 25],
            ['key' => 'discount_50', 'label' => '50% o más descuento', 'min_discount' => 50]
        ];
        
        // Contar productos para cada nivel de descuento
        foreach ($discounts as &$discount) {
            $count = $this->countProductsByDiscount($discount['min_discount'], $query, $currentFilters);
            $discount['product_count'] = $count;
        }
        
        // Filtrar descuentos sin productos
        $discounts = array_filter($discounts, function($discount) {
            return $discount['product_count'] > 0;
        });
        
        return [
            'type' => 'discounts',
            'label' => 'Ofertas',
            'options' => array_values($discounts),
            'display_type' => 'checkbox',
            'allow_multiple' => false
        ];
    }
    
    /**
     * Obtener sugerencias inteligentes
     */
    private function getSmartSuggestions(string $query, array $currentFilters): array
    {
        if (!$this->config['enable_smart_suggestions']) {
            return [];
        }
        
        $suggestions = [];
        
        // Sugerencias basadas en búsquedas populares
        $popularSearches = $this->getPopularSearches($query);
        if (!empty($popularSearches)) {
            $suggestions[] = [
                'type' => 'popular_searches',
                'label' => 'Búsquedas populares',
                'items' => $popularSearches
            ];
        }
        
        // Sugerencias basadas en productos relacionados
        $relatedProducts = $this->getRelatedProductSuggestions($query, $currentFilters);
        if (!empty($relatedProducts)) {
            $suggestions[] = [
                'type' => 'related_products',
                'label' => 'También te puede interesar',
                'items' => $relatedProducts
            ];
        }
        
        // Sugerencias de filtros recomendados
        $recommendedFilters = $this->getRecommendedFilters($query, $currentFilters);
        if (!empty($recommendedFilters)) {
            $suggestions[] = [
                'type' => 'recommended_filters',
                'label' => 'Filtros recomendados',
                'items' => $recommendedFilters
            ];
        }
        
        return $suggestions;
    }
    
    /**
     * Aplicar lógica inteligente a los filtros
     */
    private function applySmartLogic(array $filters, string $query, array $currentFilters): array
    {
        // Reordenar filtros por relevancia
        $filterOrder = $this->calculateFilterRelevance($filters, $query);
        
        // Aplicar orden inteligente
        $orderedFilters = [];
        foreach ($filterOrder as $filterType) {
            if (isset($filters[$filterType])) {
                $orderedFilters[$filterType] = $filters[$filterType];
            }
        }
        
        // Agregar filtros restantes
        foreach ($filters as $type => $filter) {
            if (!isset($orderedFilters[$type])) {
                $orderedFilters[$type] = $filter;
            }
        }
        
        return $orderedFilters;
    }
    
    /**
     * Calcular relevancia de filtros
     */
    private function calculateFilterRelevance(array $filters, string $query): array
    {
        $relevance = [];
        
        // Lógica básica de relevancia
        if (!empty($query)) {
            // Si hay búsqueda, priorizar categorías y marcas
            $relevance = ['categories', 'brands', 'price_ranges', 'ratings', 'availability', 'discounts', 'features'];
        } else {
            // Sin búsqueda, priorizar disponibilidad y ofertas
            $relevance = ['availability', 'discounts', 'categories', 'brands', 'price_ranges', 'ratings', 'features'];
        }
        
        return $relevance;
    }
    
    // Métodos auxiliares
    private function prepareSearchTerm(string $query): string
    {
        $words = explode(' ', trim($query));
        $searchTerms = [];
        
        foreach ($words as $word) {
            if (strlen($word) >= 2) {
                $searchTerms[] = "+{$word}*";
            }
        }
        
        return implode(' ', $searchTerms);
    }
    
    private function applyExistingFilters($builder, array $filters, array $exclude = []): void
    {
        foreach ($filters as $key => $value) {
            if (in_array($key, $exclude) || empty($value)) {
                continue;
            }
            
            switch ($key) {
                case 'category_id':
                    $builder->where('p.category_id', $value);
                    break;
                case 'brand_id':
                    if (is_array($value)) {
                        $builder->whereIn('p.brand_id', $value);
                    } else {
                        $builder->where('p.brand_id', $value);
                    }
                    break;
                case 'min_price':
                    $builder->where('COALESCE(p.price_sale, p.price_regular) >=', $value);
                    break;
                case 'max_price':
                    $builder->where('COALESCE(p.price_sale, p.price_regular) <=', $value);
                    break;
                case 'min_rating':
                    $builder->where('p.rating_average >=', $value);
                    break;
                case 'in_stock':
                    if ($value) {
                        $builder->where('p.stock_quantity >', 0);
                        $builder->where('p.stock_status', 'in_stock');
                    }
                    break;
                case 'featured':
                    if ($value) {
                        $builder->where('p.is_featured', 1);
                    }
                    break;
                case 'on_sale':
                    if ($value) {
                        $builder->where('p.price_sale IS NOT NULL');
                        $builder->where('p.price_sale <', 'p.price_regular', false);
                    }
                    break;
            }
        }
    }
    
    private function generatePriceRanges(float $min, float $max, float $avg): array
    {
        $ranges = [];
        $step = ($max - $min) / 5; // 5 rangos
        
        for ($i = 0; $i < 5; $i++) {
            $rangeMin = $min + ($step * $i);
            $rangeMax = $i === 4 ? $max : $min + ($step * ($i + 1));
            
            $ranges[] = [
                'min' => round($rangeMin, 2),
                'max' => round($rangeMax, 2),
                'label' => 'Q' . number_format($rangeMin, 0) . ' - Q' . number_format($rangeMax, 0)
            ];
        }
        
        return $ranges;
    }
    
    private function detectProductType(string $query): string
    {
        $query = strtolower($query);
        
        if (preg_match('/\b(iphone|samsung|huawei|xiaomi|celular|telefono|smartphone)\b/', $query)) {
            return 'smartphone';
        }
        
        if (preg_match('/\b(laptop|computadora|pc|macbook)\b/', $query)) {
            return 'laptop';
        }
        
        if (preg_match('/\b(audifonos|headphones|auriculares)\b/', $query)) {
            return 'headphones';
        }
        
        return 'general';
    }
    
    // Métodos de conteo (implementación básica)
    private function countProductsInPriceRange(float $min, float $max, string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(1, 20);
    }
    
    private function countProductsByRating(float $minRating, string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(1, 30);
    }
    
    private function countProductsByAvailability(bool $inStock, string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(10, 50);
    }
    
    private function countProductsByFeatured(bool $featured, string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(5, 25);
    }
    
    private function countNewProducts(string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(3, 15);
    }
    
    private function countProductsByDiscount(int $minDiscount, string $query, array $filters): int
    {
        // Implementación simplificada
        return rand(2, 20);
    }
    
    private function getPopularSearches(string $query): array
    {
        // Implementación simplificada
        return [];
    }
    
    private function getRelatedProductSuggestions(string $query, array $filters): array
    {
        // Implementación simplificada
        return [];
    }
    
    private function getRecommendedFilters(string $query, array $filters): array
    {
        // Implementación simplificada
        return [];
    }
}
