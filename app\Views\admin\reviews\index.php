<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>Gestión de Reseñas<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .review-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .review-card:hover {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .review-header {
        background: #f8f9fc;
        padding: 1rem;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .review-body {
        padding: 1rem;
    }
    
    .review-actions {
        padding: 1rem;
        background: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }
    
    .rating-stars {
        color: #f6c23e;
        font-size: 1.2rem;
    }
    
    .review-status {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
    
    .status-pending {
        background: #ffeaa7;
        color: #d63031;
    }
    
    .status-approved {
        background: #00b894;
        color: white;
    }
    
    .status-featured {
        background: #6c5ce7;
        color: white;
    }
    
    .product-info {
        background: #e3f2fd;
        padding: 0.5rem;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
    }
    
    .customer-info {
        background: #f3e5f5;
        padding: 0.5rem;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
    }
    
    .admin-response {
        background: #e8f5e8;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-top: 1rem;
        border-left: 4px solid #28a745;
    }
    
    .response-form {
        margin-top: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.25rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-star text-warning me-2"></i>Gestión de Reseñas
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary active" data-filter="pending">
                <i class="fas fa-clock me-1"></i>Pendientes
            </button>
            <button type="button" class="btn btn-outline-success" data-filter="approved">
                <i class="fas fa-check me-1"></i>Aprobadas
            </button>
            <button type="button" class="btn btn-outline-info" data-filter="all">
                <i class="fas fa-list me-1"></i>Todas
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pendientes de Aprobación
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Reseñas Aprobadas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Calificación Promedio
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="average-rating">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Reseñas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>Lista de Reseñas
            </h6>
        </div>
        <div class="card-body">
            <div id="reviews-container">
                <div class="text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">Cargando reseñas...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="responseModalLabel">
                    <i class="fas fa-reply me-2"></i>Responder a Reseña
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="responseForm">
                    <input type="hidden" id="responseReviewId">
                    <div class="mb-3">
                        <label for="adminResponse" class="form-label">Respuesta del Administrador</label>
                        <textarea class="form-control" id="adminResponse" rows="4" 
                                  placeholder="Escribe tu respuesta aquí..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="submitResponse()">
                    <i class="fas fa-paper-plane me-1"></i>Enviar Respuesta
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentFilter = 'pending';
    let allReviews = [];

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadStats();
        loadReviews();
        setupEventListeners();
    });

    // Setup event listeners
    function setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('[data-filter]').forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Update filter and reload
                currentFilter = this.getAttribute('data-filter');
                loadReviews();
            });
        });
    }

    // Load statistics
    function loadStats() {
        fetch('/admin/reviews/stats')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const stats = data.data;
                    document.getElementById('pending-count').textContent = stats.pending_count;
                    document.getElementById('approved-count').textContent = stats.approved_count;
                    document.getElementById('total-count').textContent = stats.total_count;

                    // Format average rating with stars
                    const avgRating = parseFloat(stats.average_rating);
                    const stars = '★'.repeat(Math.floor(avgRating)) + '☆'.repeat(5 - Math.floor(avgRating));
                    document.getElementById('average-rating').innerHTML = `${avgRating} <span class="text-warning">${stars}</span>`;
                } else {
                    console.error('Error loading stats:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching stats:', error);
            });
    }

    // Load reviews based on current filter
    function loadReviews() {
        const container = document.getElementById('reviews-container');
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x text-gray-300 mb-3"></i>
                <p class="text-muted">Cargando reseñas...</p>
            </div>
        `;

        let url = '/api/reviews/pending';
        if (currentFilter === 'all') {
            url = '/api/reviews/all';
        } else if (currentFilter === 'approved') {
            url = '/api/reviews/approved';
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    allReviews = data.data;
                    displayReviews(allReviews);
                } else {
                    showError('Error al cargar las reseñas');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Error de conexión');
            });
    }

    // Display reviews
    function displayReviews(reviews) {
        const container = document.getElementById('reviews-container');
        
        if (reviews.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-muted">No hay reseñas para mostrar</h5>
                    <p class="text-muted">No se encontraron reseñas con el filtro seleccionado.</p>
                </div>
            `;
            return;
        }

        let html = '';
        reviews.forEach(review => {
            html += createReviewCard(review);
        });
        
        container.innerHTML = html;
    }

    // Create review card HTML
    function createReviewCard(review) {
        const statusClass = review.is_approved == '1' ? 'status-approved' : 'status-pending';
        const statusText = review.is_approved == '1' ? 'Aprobada' : 'Pendiente';
        const featuredBadge = review.is_featured == '1' ? '<span class="status-featured ms-2">Destacada</span>' : '';
        
        return `
            <div class="review-card" data-review-id="${review.id}">
                <div class="review-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="product-info">
                                <strong>Producto:</strong> ${review.product_name || 'Producto #' + review.product_id}
                            </div>
                            <div class="customer-info">
                                <strong>Cliente:</strong> ${review.display_name} (${review.customer_email})
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="rating-stars">${review.rating_stars}</div>
                            <div class="mt-1">
                                <span class="review-status ${statusClass}">${statusText}</span>
                                ${featuredBadge}
                            </div>
                            <small class="text-muted">${review.created_at_formatted}</small>
                        </div>
                    </div>
                </div>
                
                <div class="review-body">
                    <h6 class="mb-2">${review.title}</h6>
                    <p class="mb-0">${review.comment}</p>
                    
                    ${review.admin_response ? `
                        <div class="admin-response">
                            <strong><i class="fas fa-reply me-1"></i>Respuesta del Administrador:</strong>
                            <p class="mb-0 mt-1">${review.admin_response}</p>
                            <small class="text-muted">Respondido el: ${review.admin_response_date}</small>
                        </div>
                    ` : ''}
                </div>
                
                <div class="review-actions">
                    <div class="btn-group" role="group">
                        ${review.is_approved == '0' ? `
                            <button class="btn btn-success btn-sm" onclick="approveReview(${review.id})">
                                <i class="fas fa-check me-1"></i>Aprobar
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="rejectReview(${review.id})">
                                <i class="fas fa-times me-1"></i>Rechazar
                            </button>
                        ` : ''}
                        
                        <button class="btn btn-info btn-sm" onclick="openResponseModal(${review.id})">
                            <i class="fas fa-reply me-1"></i>Responder
                        </button>
                        
                        <button class="btn btn-warning btn-sm" onclick="toggleFeatured(${review.id})">
                            <i class="fas fa-star me-1"></i>${review.is_featured == '1' ? 'Quitar Destacado' : 'Destacar'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Approve review
    function approveReview(reviewId) {
        if (!confirm('¿Estás seguro de que quieres aprobar esta reseña?')) return;
        
        fetch(`/api/reviews/${reviewId}/approve`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showSuccess('Reseña aprobada exitosamente');
                loadReviews();
                loadStats();
            } else {
                showError('Error al aprobar la reseña');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Error de conexión');
        });
    }

    // Reject review
    function rejectReview(reviewId) {
        if (!confirm('¿Estás seguro de que quieres rechazar esta reseña? Esta acción no se puede deshacer.')) return;
        
        fetch(`/api/reviews/${reviewId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showSuccess('Reseña rechazada exitosamente');
                loadReviews();
                loadStats();
            } else {
                showError('Error al rechazar la reseña');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Error de conexión');
        });
    }

    // Open response modal
    function openResponseModal(reviewId) {
        document.getElementById('responseReviewId').value = reviewId;
        document.getElementById('adminResponse').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('responseModal'));
        modal.show();
    }

    // Submit response
    function submitResponse() {
        const reviewId = document.getElementById('responseReviewId').value;
        const response = document.getElementById('adminResponse').value.trim();
        
        if (!response) {
            showError('Por favor escribe una respuesta');
            return;
        }
        
        fetch(`/api/reviews/${reviewId}/response`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ response: response })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showSuccess('Respuesta agregada exitosamente');
                bootstrap.Modal.getInstance(document.getElementById('responseModal')).hide();
                loadReviews();
            } else {
                showError('Error al agregar la respuesta');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Error de conexión');
        });
    }

    // Toggle featured status
    function toggleFeatured(reviewId) {
        fetch(`/api/reviews/${reviewId}/featured`, {
            method: 'PUT'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showSuccess('Estado de destacado actualizado');
                loadReviews();
            } else {
                showError('Error al actualizar el estado');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Error de conexión');
        });
    }

    // Show success message
    function showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    // Show error message
    function showError(message) {
        // You can implement a toast notification system here
        alert(message);
    }
</script>
<?= $this->endSection() ?>
