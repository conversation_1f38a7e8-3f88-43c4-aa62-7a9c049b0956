<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class PermissionFilter implements FilterInterface
{
    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Verificar permisos antes de ejecutar el controlador
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Obtener usuario de la sesión
        $userId = session()->get('user_id');
        
        if (!$userId) {
            // Si no hay usuario logueado, redirigir al login
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'status' => 'error',
                    'message' => 'Acceso no autorizado'
                ])->setStatusCode(401);
            }
            return redirect()->to('/admin/login');
        }

        // Si no se especifican argumentos, permitir acceso
        if (empty($arguments)) {
            return;
        }

        // Parsear argumentos: module.action.resource
        $permission = explode('.', $arguments[0]);
        $module = $permission[0] ?? null;
        $action = $permission[1] ?? 'view';
        $resource = $permission[2] ?? null;

        if (!$module) {
            return;
        }

        // Verificar permisos usando SP
        try {
            $query = $this->db->query("CALL sp_check_user_permission(?, ?, ?, ?, @has_permission)", [
                $userId, $module, $action, $resource
            ]);

            $resultQuery = $this->db->query("SELECT @has_permission as has_permission");
            $result = $resultQuery->getRowArray();

            if (!$result['has_permission']) {
                // Registrar intento de acceso no autorizado
                $this->logUnauthorizedAccess($userId, $module, $action, $resource, $request);

                // Responder según el tipo de request
                if ($request->isAJAX()) {
                    return service('response')->setJSON([
                        'status' => 'error',
                        'message' => 'No tienes permisos para realizar esta acción'
                    ])->setStatusCode(403);
                }

                // Redirigir con mensaje de error
                session()->setFlashdata('error', 'No tienes permisos para acceder a esta sección');
                return redirect()->to('/admin/dashboard');
            }

            // Registrar acceso autorizado
            $this->logAuthorizedAccess($userId, $module, $action, $resource, $request);

        } catch (\Exception $e) {
            log_message('error', 'Error en PermissionFilter: ' . $e->getMessage());
            
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'status' => 'error',
                    'message' => 'Error interno del servidor'
                ])->setStatusCode(500);
            }
            
            return redirect()->to('/admin/dashboard');
        }
    }

    /**
     * Después de ejecutar el controlador
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No hacer nada después
    }

    /**
     * Registrar acceso autorizado
     */
    private function logAuthorizedAccess($userId, $module, $action, $resource, $request)
    {
        try {
            $this->db->query("CALL sp_log_user_activity(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $userId,
                'access_granted',
                $module,
                $resource,
                null,
                "Acceso autorizado a {$module}.{$action}" . ($resource ? ".{$resource}" : ''),
                $request->getIPAddress(),
                $request->getUserAgent()->getAgentString(),
                session()->session_id,
                json_encode([
                    'module' => $module,
                    'action' => $action,
                    'resource' => $resource,
                    'url' => $request->getUri()->getPath(),
                    'method' => $request->getMethod()
                ])
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging authorized access: ' . $e->getMessage());
        }
    }

    /**
     * Registrar intento de acceso no autorizado
     */
    private function logUnauthorizedAccess($userId, $module, $action, $resource, $request)
    {
        try {
            $this->db->query("CALL sp_log_user_activity(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $userId,
                'access_denied',
                $module,
                $resource,
                null,
                "Acceso denegado a {$module}.{$action}" . ($resource ? ".{$resource}" : ''),
                $request->getIPAddress(),
                $request->getUserAgent()->getAgentString(),
                session()->session_id,
                json_encode([
                    'module' => $module,
                    'action' => $action,
                    'resource' => $resource,
                    'url' => $request->getUri()->getPath(),
                    'method' => $request->getMethod(),
                    'reason' => 'insufficient_permissions'
                ])
            ]);

            // Crear notificación de seguridad para administradores
            $this->createSecurityNotification($userId, $module, $action, $request);

        } catch (\Exception $e) {
            log_message('error', 'Error logging unauthorized access: ' . $e->getMessage());
        }
    }

    /**
     * Crear notificación de seguridad
     */
    private function createSecurityNotification($userId, $module, $action, $request)
    {
        try {
            // Obtener información del usuario
            $userQuery = $this->db->query("SELECT name, email FROM users WHERE id = ?", [$userId]);
            $user = $userQuery->getRowArray();

            if ($user) {
                $this->db->query("CALL sp_create_notification(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @notification_id, @result)", [
                    null, // user_id (null para global)
                    'warning',
                    'Intento de Acceso No Autorizado',
                    "El usuario {$user['name']} ({$user['email']}) intentó acceder a {$module}.{$action} sin permisos",
                    json_encode([
                        'user_id' => $userId,
                        'user_name' => $user['name'],
                        'user_email' => $user['email'],
                        'module' => $module,
                        'action' => $action,
                        'ip_address' => $request->getIPAddress(),
                        'url' => $request->getUri()->getPath()
                    ]),
                    '/admin/users/' . $userId,
                    'Ver Usuario',
                    1, // is_global
                    'high',
                    null // expires_at
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating security notification: ' . $e->getMessage());
        }
    }
}
