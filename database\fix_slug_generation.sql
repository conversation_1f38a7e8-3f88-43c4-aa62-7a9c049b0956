-- =====================================================
-- Script para corregir la generación de slugs
-- =====================================================

DELIMITER $$

-- Función para generar slugs consistentes
DROP FUNCTION IF EXISTS generate_consistent_slug$$
CREATE FUNCTION generate_consistent_slug(input_name VARCHAR(255))
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result_slug VARCHAR(255);
    
    -- Convertir a minúsculas
    SET result_slug = LOWER(input_name);
    
    -- Reemplazar caracteres especiales con equivalentes ASCII
    SET result_slug = REPLACE(result_slug, 'á', 'a');
    SET result_slug = REPLACE(result_slug, 'à', 'a');
    SET result_slug = REPLACE(result_slug, 'ä', 'a');
    SET result_slug = REPLACE(result_slug, 'â', 'a');
    SET result_slug = REPLACE(result_slug, 'ā', 'a');
    SET result_slug = REPLACE(result_slug, 'ã', 'a');
    
    SET result_slug = REPLACE(result_slug, 'é', 'e');
    SET result_slug = REPLACE(result_slug, 'è', 'e');
    SET result_slug = REPLACE(result_slug, 'ë', 'e');
    SET result_slug = REPLACE(result_slug, 'ê', 'e');
    SET result_slug = REPLACE(result_slug, 'ē', 'e');
    
    SET result_slug = REPLACE(result_slug, 'í', 'i');
    SET result_slug = REPLACE(result_slug, 'ì', 'i');
    SET result_slug = REPLACE(result_slug, 'ï', 'i');
    SET result_slug = REPLACE(result_slug, 'î', 'i');
    SET result_slug = REPLACE(result_slug, 'ī', 'i');
    
    SET result_slug = REPLACE(result_slug, 'ó', 'o');
    SET result_slug = REPLACE(result_slug, 'ò', 'o');
    SET result_slug = REPLACE(result_slug, 'ö', 'o');
    SET result_slug = REPLACE(result_slug, 'ô', 'o');
    SET result_slug = REPLACE(result_slug, 'ō', 'o');
    SET result_slug = REPLACE(result_slug, 'õ', 'o');
    
    SET result_slug = REPLACE(result_slug, 'ú', 'u');
    SET result_slug = REPLACE(result_slug, 'ù', 'u');
    SET result_slug = REPLACE(result_slug, 'ü', 'u');
    SET result_slug = REPLACE(result_slug, 'û', 'u');
    SET result_slug = REPLACE(result_slug, 'ū', 'u');
    
    SET result_slug = REPLACE(result_slug, 'ñ', 'n');
    SET result_slug = REPLACE(result_slug, 'ç', 'c');
    
    -- Reemplazar caracteres especiales adicionales
    SET result_slug = REPLACE(result_slug, '&', 'and');
    SET result_slug = REPLACE(result_slug, '@', 'at');
    SET result_slug = REPLACE(result_slug, '+', 'plus');
    SET result_slug = REPLACE(result_slug, '%', 'percent');
    SET result_slug = REPLACE(result_slug, '$', 'dollar');
    SET result_slug = REPLACE(result_slug, '€', 'euro');
    SET result_slug = REPLACE(result_slug, '£', 'pound');
    SET result_slug = REPLACE(result_slug, '¥', 'yen');
    
    -- Remover caracteres que no sean letras, números, espacios o guiones
    SET result_slug = REGEXP_REPLACE(result_slug, '[^a-z0-9\\s\\-]', '');
    
    -- Reemplazar espacios múltiples con uno solo
    SET result_slug = REGEXP_REPLACE(result_slug, '\\s+', ' ');
    
    -- Reemplazar espacios con guiones
    SET result_slug = REPLACE(result_slug, ' ', '-');
    
    -- Remover guiones múltiples
    SET result_slug = REGEXP_REPLACE(result_slug, '-+', '-');
    
    -- Remover guiones al inicio y final
    SET result_slug = TRIM(BOTH '-' FROM result_slug);
    
    RETURN result_slug;
END$$

-- Función para generar slug único
DROP FUNCTION IF EXISTS generate_unique_product_slug$$
CREATE FUNCTION generate_unique_product_slug(input_name VARCHAR(255), exclude_id INT)
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE base_slug VARCHAR(255);
    DECLARE final_slug VARCHAR(255);
    DECLARE counter INT DEFAULT 1;
    DECLARE slug_exists INT DEFAULT 0;
    
    -- Generar slug base
    SET base_slug = generate_consistent_slug(input_name);
    SET final_slug = base_slug;
    
    -- Verificar si el slug existe
    slug_loop: LOOP
        IF exclude_id IS NOT NULL THEN
            SELECT COUNT(*) INTO slug_exists 
            FROM products 
            WHERE slug = final_slug 
              AND deleted_at IS NULL 
              AND id != exclude_id;
        ELSE
            SELECT COUNT(*) INTO slug_exists 
            FROM products 
            WHERE slug = final_slug 
              AND deleted_at IS NULL;
        END IF;
        
        IF slug_exists = 0 THEN
            LEAVE slug_loop;
        END IF;
        
        SET final_slug = CONCAT(base_slug, '-', counter);
        SET counter = counter + 1;
    END LOOP;
    
    RETURN final_slug;
END$$

-- Actualizar stored procedure para crear productos
DROP PROCEDURE IF EXISTS sp_admin_create_product_fixed$$
CREATE PROCEDURE sp_admin_create_product_fixed(
    IN p_name VARCHAR(255),
    IN p_sku VARCHAR(100),
    IN p_description TEXT,
    IN p_short_description TEXT,
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_price_wholesale DECIMAL(10,2),
    IN p_cost_price DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_weight DECIMAL(8,2),
    IN p_dimensions VARCHAR(100),
    IN p_featured_image VARCHAR(500),
    IN p_gallery_images JSON,
    IN p_is_active BOOLEAN,
    IN p_is_featured BOOLEAN,
    OUT p_result VARCHAR(50),
    OUT p_product_id INT
)
BEGIN
    DECLARE v_uuid VARCHAR(36);
    DECLARE v_slug VARCHAR(255);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_product_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el SKU no exista
    IF EXISTS (SELECT 1 FROM products WHERE sku = p_sku AND deleted_at IS NULL) THEN
        SET p_result = 'SKU_EXISTS';
        SET p_product_id = 0;
        ROLLBACK;
    ELSE
        -- Generar UUID
        SET v_uuid = UUID();

        -- Generar slug único usando la nueva función
        SET v_slug = generate_unique_product_slug(p_name, NULL);

        -- Insertar producto
        INSERT INTO products (
            uuid, sku, name, slug, description, short_description, category_id, brand_id,
            price_regular, price_sale, price_wholesale, cost_price, stock_quantity, stock_min,
            weight, dimensions, featured_image, gallery_images, is_active, is_featured,
            created_at, updated_at
        ) VALUES (
            v_uuid, p_sku, p_name, v_slug, p_description, p_short_description, p_category_id, p_brand_id,
            p_price_regular, p_price_sale, p_price_wholesale, p_cost_price, p_stock_quantity, p_stock_min,
            p_weight, p_dimensions, p_featured_image, p_gallery_images, p_is_active, p_is_featured,
            NOW(), NOW()
        );

        SET p_product_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

DELIMITER ;

-- Script para actualizar slugs existentes que puedan tener problemas
UPDATE products 
SET slug = (
    SELECT generate_unique_product_slug(name, id)
    FROM (SELECT name, id FROM products WHERE id = products.id) AS temp
)
WHERE deleted_at IS NULL 
  AND (slug IS NULL OR slug = '' OR slug REGEXP '[áéíóúñü]');

-- Mostrar productos que fueron actualizados
SELECT id, name, slug, 'Slug actualizado' as status
FROM products 
WHERE deleted_at IS NULL 
  AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);
