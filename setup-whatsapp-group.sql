-- Script para configurar el número de grupo de WhatsApp para alertas del sistema

-- Insertar o actualizar en system_settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    setting_value = '120363416393766854',
    updated_at = NOW();

-- También insertar en la tabla settings si existe (por compatibilidad)
INSERT IGNORE INTO settings (`key`, `value`, description, created_at, updated_at)
VALUES ('whatsapp_alerts_group', '120363416393766854', 'Número del grupo de WhatsApp para alertas automáticas', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `value` = '120363416393766854',
    updated_at = NOW();

-- Verificar que se insertó correctamente
SELECT 'system_settings' as tabla, setting_key as clave, setting_value as valor 
FROM system_settings 
WHERE setting_key = 'whatsapp_alerts_group'
UNION ALL
SELECT 'settings' as tabla, `key` as clave, `value` as valor 
FROM settings 
WHERE `key` = 'whatsapp_alerts_group';
