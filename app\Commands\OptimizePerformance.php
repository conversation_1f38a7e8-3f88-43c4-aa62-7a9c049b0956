<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\PerformanceOptimizer;

/**
 * Comando para optimización completa de rendimiento
 * Compatible con cPanel hosting
 * 
 * Uso: php spark optimize:performance [options]
 */
class OptimizePerformance extends BaseCommand
{
    protected $group       = 'MrCell';
    protected $name        = 'optimize:performance';
    protected $description = 'Optimización completa de rendimiento del sistema';
    protected $usage       = 'optimize:performance [options]';
    protected $arguments   = [];
    protected $options     = [
        '--database' => 'Solo optimizar base de datos',
        '--images' => 'Solo optimizar imágenes',
        '--assets' => 'Solo minificar CSS/JS',
        '--all' => 'Ejecutar todas las optimizaciones (por defecto)',
        '--report' => 'Generar reporte completo',
        '--dry-run' => 'Mostrar qué se haría sin ejecutar'
    ];

    public function run(array $params)
    {
        CLI::write('🚀 OPTIMIZACIÓN COMPLETA DE RENDIMIENTO - MrCell Guatemala', 'green');
        CLI::newLine();
        
        $optimizer = new PerformanceOptimizer();
        
        $database = CLI::getOption('database');
        $images = CLI::getOption('images');
        $assets = CLI::getOption('assets');
        $all = CLI::getOption('all') || (!$database && !$images && !$assets);
        $report = CLI::getOption('report');
        $dryRun = CLI::getOption('dry-run');
        
        if ($dryRun) {
            $this->showDryRun($all, $database, $images, $assets);
            return;
        }
        
        $startTime = microtime(true);
        $results = [];
        
        // Optimización de base de datos
        if ($all || $database) {
            CLI::write('🗄️  Optimizando Base de Datos...', 'yellow');
            $results['database'] = $this->optimizeDatabase($optimizer);
            CLI::newLine();
        }
        
        // Optimización de imágenes
        if ($all || $images) {
            CLI::write('🖼️  Optimizando Imágenes...', 'yellow');
            $results['images'] = $this->optimizeImages($optimizer);
            CLI::newLine();
        }
        
        // Minificación de assets
        if ($all || $assets) {
            CLI::write('📦 Minificando CSS y JavaScript...', 'yellow');
            $results['assets'] = $this->optimizeAssets($optimizer);
            CLI::newLine();
        }
        
        // Optimizaciones adicionales
        if ($all) {
            CLI::write('⚙️  Aplicando Optimizaciones Adicionales...', 'yellow');
            $results['additional'] = $this->additionalOptimizations();
            CLI::newLine();
        }
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        // Mostrar resumen
        $this->showSummary($results, $executionTime);
        
        // Generar reporte si se solicita
        if ($report) {
            $this->generateReport($optimizer, $results);
        }
        
        CLI::newLine();
        CLI::write('✅ Optimización completada exitosamente!', 'green');
        CLI::write("⏱️  Tiempo total: {$executionTime} segundos", 'blue');
    }
    
    /**
     * Mostrar qué se haría en dry-run
     */
    private function showDryRun(bool $all, bool $database, bool $images, bool $assets)
    {
        CLI::write('🔍 MODO DRY-RUN - Mostrando qué se ejecutaría:', 'yellow');
        CLI::newLine();
        
        if ($all || $database) {
            CLI::write('📋 Base de Datos:', 'blue');
            CLI::write('  • Crear índices optimizados en tablas principales');
            CLI::write('  • Optimizar tablas (OPTIMIZE TABLE)');
            CLI::write('  • Analizar consultas lentas');
            CLI::newLine();
        }
        
        if ($all || $images) {
            CLI::write('📋 Imágenes:', 'blue');
            CLI::write('  • Comprimir imágenes JPEG/PNG');
            CLI::write('  • Generar versiones WebP');
            CLI::write('  • Reducir tamaño de archivos');
            CLI::newLine();
        }
        
        if ($all || $assets) {
            CLI::write('📋 Assets:', 'blue');
            CLI::write('  • Minificar archivos CSS');
            CLI::write('  • Minificar archivos JavaScript');
            CLI::write('  • Crear versiones .min optimizadas');
            CLI::newLine();
        }
        
        if ($all) {
            CLI::write('📋 Adicionales:', 'blue');
            CLI::write('  • Optimizar .htaccess');
            CLI::write('  • Limpiar cache expirado');
            CLI::write('  • Configurar headers de cache');
            CLI::newLine();
        }
        
        CLI::write('💡 Para ejecutar realmente, quita la opción --dry-run', 'yellow');
    }
    
    /**
     * Optimizar base de datos
     */
    private function optimizeDatabase(PerformanceOptimizer $optimizer): array
    {
        $results = [];
        
        // Optimizar consultas lentas
        CLI::write('  🔧 Creando índices optimizados...', 'white');
        $queryResults = $optimizer->optimizeSlowQueries();
        
        $created = 0;
        $existing = 0;
        $errors = 0;
        
        foreach ($queryResults as $result) {
            if (isset($result['status'])) {
                switch ($result['status']) {
                    case 'created':
                        $created++;
                        CLI::write("    ✅ {$result['table']}: {$result['index']}", 'green');
                        break;
                    case 'already_exists':
                        $existing++;
                        CLI::write("    ℹ️  {$result['table']}: {$result['index']} (ya existe)", 'blue');
                        break;
                    case 'error':
                        $errors++;
                        CLI::write("    ❌ {$result['table']}: {$result['error']}", 'red');
                        break;
                }
            }
        }
        
        $results['indexes'] = [
            'created' => $created,
            'existing' => $existing,
            'errors' => $errors
        ];
        
        // Optimizar tablas
        CLI::write('  🔧 Optimizando tablas...', 'white');
        $tableResults = $optimizer->optimizeTables();
        
        $optimized = 0;
        $tableErrors = 0;
        
        foreach ($tableResults as $result) {
            if ($result['status'] === 'optimized') {
                $optimized++;
                CLI::write("    ✅ {$result['table']}", 'green');
            } else {
                $tableErrors++;
                CLI::write("    ❌ {$result['table']}: {$result['error']}", 'red');
            }
        }
        
        $results['tables'] = [
            'optimized' => $optimized,
            'errors' => $tableErrors
        ];
        
        return $results;
    }
    
    /**
     * Optimizar imágenes
     */
    private function optimizeImages(PerformanceOptimizer $optimizer): array
    {
        CLI::write('  🔧 Comprimiendo imágenes...', 'white');
        $imageResults = $optimizer->optimizeImages();
        
        if (isset($imageResults['error'])) {
            CLI::write("    ❌ {$imageResults['error']}", 'red');
            return ['error' => $imageResults['error']];
        }
        
        $compressed = 0;
        $skipped = 0;
        $errors = 0;
        $totalSavings = 0;
        $webpCreated = 0;
        
        foreach ($imageResults as $result) {
            switch ($result['status']) {
                case 'compressed':
                    $compressed++;
                    $totalSavings += $result['savings'];
                    if ($result['webp_created']) {
                        $webpCreated++;
                    }
                    CLI::write("    ✅ {$result['file']}: -{$result['percentage']}% ({$this->formatBytes($result['savings'])} ahorrados)", 'green');
                    break;
                case 'skipped':
                    $skipped++;
                    CLI::write("    ⏭️  {$result['file']}: {$result['reason']}", 'yellow');
                    break;
                case 'error':
                    $errors++;
                    CLI::write("    ❌ {$result['file']}: {$result['error']}", 'red');
                    break;
            }
        }
        
        if ($webpCreated > 0) {
            CLI::write("    🎉 {$webpCreated} imágenes WebP creadas", 'blue');
        }
        
        return [
            'compressed' => $compressed,
            'skipped' => $skipped,
            'errors' => $errors,
            'total_savings' => $totalSavings,
            'webp_created' => $webpCreated
        ];
    }
    
    /**
     * Optimizar assets
     */
    private function optimizeAssets(PerformanceOptimizer $optimizer): array
    {
        CLI::write('  🔧 Minificando archivos...', 'white');
        $assetResults = $optimizer->minifyAssets();
        
        $results = ['css' => [], 'js' => []];
        
        // Procesar CSS
        if (isset($assetResults['css'])) {
            $cssMinified = 0;
            $cssSavings = 0;
            
            foreach ($assetResults['css'] as $result) {
                if ($result['status'] === 'minified') {
                    $cssMinified++;
                    $cssSavings += $result['savings'];
                    CLI::write("    ✅ CSS: {$result['file']} → {$result['minified_file']} (-{$result['percentage']}%)", 'green');
                } else {
                    CLI::write("    ❌ CSS: {$result['file']}: {$result['error']}", 'red');
                }
            }
            
            $results['css'] = [
                'minified' => $cssMinified,
                'savings' => $cssSavings
            ];
        }
        
        // Procesar JS
        if (isset($assetResults['js'])) {
            $jsMinified = 0;
            $jsSavings = 0;
            
            foreach ($assetResults['js'] as $result) {
                if ($result['status'] === 'minified') {
                    $jsMinified++;
                    $jsSavings += $result['savings'];
                    CLI::write("    ✅ JS: {$result['file']} → {$result['minified_file']} (-{$result['percentage']}%)", 'green');
                } else {
                    CLI::write("    ❌ JS: {$result['file']}: {$result['error']}", 'red');
                }
            }
            
            $results['js'] = [
                'minified' => $jsMinified,
                'savings' => $jsSavings
            ];
        }
        
        return $results;
    }
    
    /**
     * Optimizaciones adicionales
     */
    private function additionalOptimizations(): array
    {
        $results = [];
        
        // Limpiar cache expirado
        CLI::write('  🔧 Limpiando cache expirado...', 'white');
        try {
            $deleted = \App\Libraries\SimpleCache::clearExpired();
            CLI::write("    ✅ {$deleted} archivos de cache eliminados", 'green');
            $results['cache_cleanup'] = $deleted;
        } catch (\Exception $e) {
            CLI::write("    ❌ Error limpiando cache: {$e->getMessage()}", 'red');
            $results['cache_cleanup'] = 0;
        }
        
        // Optimizar .htaccess
        CLI::write('  🔧 Verificando optimizaciones de .htaccess...', 'white');
        $htaccessOptimized = $this->optimizeHtaccess();
        if ($htaccessOptimized) {
            CLI::write('    ✅ .htaccess optimizado', 'green');
            $results['htaccess'] = 'optimized';
        } else {
            CLI::write('    ℹ️  .htaccess ya está optimizado', 'blue');
            $results['htaccess'] = 'already_optimized';
        }
        
        return $results;
    }
    
    /**
     * Optimizar .htaccess
     */
    private function optimizeHtaccess(): bool
    {
        $htaccessFile = FCPATH . '.htaccess';
        
        if (!file_exists($htaccessFile)) {
            return false;
        }
        
        $content = file_get_contents($htaccessFile);
        
        // Verificar si ya tiene optimizaciones
        if (strpos($content, '# MrCell Performance Optimizations') !== false) {
            return false;
        }
        
        $optimizations = "\n# MrCell Performance Optimizations\n";
        $optimizations .= "# Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Compresión GZIP mejorada
        $optimizations .= "# Enhanced GZIP Compression\n";
        $optimizations .= "<IfModule mod_deflate.c>\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/plain\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/html\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/css\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/javascript\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/xhtml+xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/rss+xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/javascript\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/x-javascript\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/json\n";
        $optimizations .= "</IfModule>\n\n";
        
        // Cache headers mejorados
        $optimizations .= "# Enhanced Browser Caching\n";
        $optimizations .= "<IfModule mod_expires.c>\n";
        $optimizations .= "    ExpiresActive On\n";
        $optimizations .= "    ExpiresByType image/jpg \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType image/jpeg \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType image/gif \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType image/png \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType image/webp \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType text/css \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/pdf \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/javascript \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/x-javascript \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/x-shockwave-flash \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType image/x-icon \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresByType text/html \"access plus 1 hour\"\n";
        $optimizations .= "    ExpiresDefault \"access plus 2 days\"\n";
        $optimizations .= "</IfModule>\n\n";
        
        // Headers adicionales
        $optimizations .= "# Additional Performance Headers\n";
        $optimizations .= "<IfModule mod_headers.c>\n";
        $optimizations .= "    <FilesMatch \"\\.(ico|pdf|flv|jpg|jpeg|png|gif|webp|js|css|swf)$\">\n";
        $optimizations .= "        Header set Cache-Control \"max-age=31536000, public, immutable\"\n";
        $optimizations .= "    </FilesMatch>\n";
        $optimizations .= "    <FilesMatch \"\\.(html|htm)$\">\n";
        $optimizations .= "        Header set Cache-Control \"max-age=3600, public\"\n";
        $optimizations .= "    </FilesMatch>\n";
        $optimizations .= "</IfModule>\n\n";
        
        file_put_contents($htaccessFile, $content . $optimizations);
        
        return true;
    }
    
    /**
     * Mostrar resumen de resultados
     */
    private function showSummary(array $results, float $executionTime)
    {
        CLI::newLine();
        CLI::write('📊 RESUMEN DE OPTIMIZACIÓN:', 'yellow');
        CLI::newLine();
        
        if (isset($results['database'])) {
            CLI::write('🗄️  Base de Datos:', 'blue');
            $db = $results['database'];
            if (isset($db['indexes'])) {
                CLI::write("    • Índices creados: {$db['indexes']['created']}", 'white');
                CLI::write("    • Índices existentes: {$db['indexes']['existing']}", 'white');
            }
            if (isset($db['tables'])) {
                CLI::write("    • Tablas optimizadas: {$db['tables']['optimized']}", 'white');
            }
            CLI::newLine();
        }
        
        if (isset($results['images'])) {
            CLI::write('🖼️  Imágenes:', 'blue');
            $img = $results['images'];
            if (!isset($img['error'])) {
                CLI::write("    • Imágenes comprimidas: {$img['compressed']}", 'white');
                CLI::write("    • Espacio ahorrado: {$this->formatBytes($img['total_savings'])}", 'white');
                CLI::write("    • Imágenes WebP creadas: {$img['webp_created']}", 'white');
            }
            CLI::newLine();
        }
        
        if (isset($results['assets'])) {
            CLI::write('📦 Assets:', 'blue');
            $assets = $results['assets'];
            if (isset($assets['css'])) {
                CLI::write("    • Archivos CSS minificados: {$assets['css']['minified']}", 'white');
                CLI::write("    • CSS ahorrado: {$this->formatBytes($assets['css']['savings'])}", 'white');
            }
            if (isset($assets['js'])) {
                CLI::write("    • Archivos JS minificados: {$assets['js']['minified']}", 'white');
                CLI::write("    • JS ahorrado: {$this->formatBytes($assets['js']['savings'])}", 'white');
            }
            CLI::newLine();
        }
        
        if (isset($results['additional'])) {
            CLI::write('⚙️  Adicionales:', 'blue');
            $add = $results['additional'];
            CLI::write("    • Cache limpiado: {$add['cache_cleanup']} archivos", 'white');
            CLI::write("    • .htaccess: {$add['htaccess']}", 'white');
            CLI::newLine();
        }
    }
    
    /**
     * Generar reporte completo
     */
    private function generateReport(PerformanceOptimizer $optimizer, array $results)
    {
        CLI::write('📋 Generando reporte completo...', 'yellow');
        
        $report = $optimizer->generatePerformanceReport();
        $reportFile = WRITEPATH . 'logs/performance_report_' . date('Y-m-d_H-i-s') . '.json';
        
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT));
        
        CLI::write("    ✅ Reporte guardado en: {$reportFile}", 'green');
    }
    
    /**
     * Formatear bytes
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
