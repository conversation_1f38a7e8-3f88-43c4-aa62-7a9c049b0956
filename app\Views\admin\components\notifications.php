<!-- Componente de Notificaciones para Admin -->
<style>
.notifications-dropdown {
    position: relative;
}

.notification-bell {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-bell:hover {
    background: #f8f9fa;
    color: #007bff;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notifications-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1050;
    display: none;
}

.notifications-dropdown-menu.show {
    display: block;
}

.notifications-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.notifications-header h6 {
    margin: 0;
    font-weight: 600;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background 0.2s ease;
    position: relative;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
}

.notification-content {
    margin-left: 15px;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
    color: #333;
}

.notification-message {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 6px;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.notification-action {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.notification-action.primary {
    background: #007bff;
    color: white;
}

.notification-action.secondary {
    background: #6c757d;
    color: white;
}

.notifications-footer {
    padding: 10px 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.notifications-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 0.85rem;
}

.notifications-empty {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.notification-type-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    flex-shrink: 0;
}

.notification-type-icon.info { background: #17a2b8; }
.notification-type-icon.success { background: #28a745; }
.notification-type-icon.warning { background: #ffc107; color: #333; }
.notification-type-icon.error { background: #dc3545; }
.notification-type-icon.stock_alert { background: #fd7e14; }
.notification-type-icon.order_alert { background: #6f42c1; }
.notification-type-icon.system { background: #6c757d; }

.loading-notifications {
    padding: 20px;
    text-align: center;
    color: #6c757d;
}
</style>

<div class="notifications-dropdown">
    <button class="notification-bell" id="notificationBell" onclick="toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
    </button>

    <div class="notifications-dropdown-menu" id="notificationsDropdown">
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6>Notificaciones</h6>
                <div>
                    <button class="btn btn-sm btn-link p-0 me-2" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn btn-sm btn-link p-0" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <div id="notificationsList">
            <div class="loading-notifications">
                <i class="fas fa-spinner fa-spin"></i>
                <div>Cargando notificaciones...</div>
            </div>
        </div>

        <div class="notifications-footer">
            <a href="/admin/notifications">Ver todas las notificaciones</a>
        </div>
    </div>
</div>

<script>
let notificationsVisible = false;
let currentUserId = <?= session()->get('user_id') ?? 3 ?>;

// Inicializar notificaciones al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    
    // Actualizar cada 30 segundos
    setInterval(loadNotifications, 30000);
    
    // Cerrar dropdown al hacer clic fuera
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.notifications-dropdown')) {
            hideNotifications();
        }
    });
});

// Alternar visibilidad del dropdown
function toggleNotifications() {
    if (notificationsVisible) {
        hideNotifications();
    } else {
        showNotifications();
    }
}

function showNotifications() {
    document.getElementById('notificationsDropdown').classList.add('show');
    notificationsVisible = true;
    loadNotifications();
}

function hideNotifications() {
    document.getElementById('notificationsDropdown').classList.remove('show');
    notificationsVisible = false;
}

// Cargar notificaciones
async function loadNotifications() {
    try {
        const response = await fetch(`/api/notifications?user_id=${currentUserId}&limit=10`);
        const data = await response.json();
        
        if (data.status === 'success') {
            updateNotificationsList(data.data);
            updateNotificationBadge();
        }
    } catch (error) {
        console.error('Error loading notifications:', error);
    }
}

// Actualizar lista de notificaciones
function updateNotificationsList(notifications) {
    const container = document.getElementById('notificationsList');
    
    if (notifications.length === 0) {
        container.innerHTML = `
            <div class="notifications-empty">
                <i class="fas fa-bell-slash fa-2x mb-2"></i>
                <div>No hay notificaciones</div>
            </div>
        `;
        return;
    }

    container.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.is_read == 0 ? 'unread' : ''}" 
             onclick="handleNotificationClick(${notification.id}, '${notification.action_url || ''}')">
            <div class="d-flex">
                <div class="notification-type-icon ${notification.type}">
                    <i class="fas ${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${formatNotificationTime(notification.created_at)}</div>
                    ${notification.action_url ? `
                        <div class="notification-actions">
                            <a href="${notification.action_url}" class="notification-action primary">
                                ${notification.action_text || 'Ver más'}
                            </a>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

// Actualizar badge de notificaciones
async function updateNotificationBadge() {
    try {
        const response = await fetch(`/api/notifications/stats?user_id=${currentUserId}`);
        const data = await response.json();
        
        if (data.status === 'success') {
            const unreadCount = data.data.unread_count;
            const badge = document.getElementById('notificationBadge');
            
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating notification badge:', error);
    }
}

// Manejar clic en notificación
async function handleNotificationClick(notificationId, actionUrl) {
    // Marcar como leída
    await markNotificationAsRead(notificationId);
    
    // Navegar si hay URL
    if (actionUrl) {
        window.location.href = actionUrl;
    }
}

// Marcar notificación como leída
async function markNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user_id: currentUserId })
        });

        if (response.ok) {
            loadNotifications();
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
}

// Marcar todas como leídas
async function markAllAsRead() {
    try {
        const response = await fetch('/api/notifications/read-all', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user_id: currentUserId })
        });

        if (response.ok) {
            loadNotifications();
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
    }
}

// Refrescar notificaciones
function refreshNotifications() {
    loadNotifications();
}

// Obtener icono según tipo de notificación
function getNotificationIcon(type) {
    const icons = {
        'info': 'fa-info',
        'success': 'fa-check',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times',
        'stock_alert': 'fa-boxes',
        'order_alert': 'fa-shopping-cart',
        'system': 'fa-cog'
    };
    return icons[type] || 'fa-bell';
}

// Formatear tiempo de notificación
function formatNotificationTime(timestamp) {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Ahora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
}

// Función global para crear notificaciones desde otros scripts
window.createNotification = async function(type, title, message, data = null, actionUrl = null, actionText = null) {
    try {
        const response = await fetch('/api/notifications', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUserId,
                type: type,
                title: title,
                message: message,
                data: data,
                action_url: actionUrl,
                action_text: actionText,
                is_global: false,
                priority: 'normal'
            })
        });

        if (response.ok) {
            loadNotifications();
        }
    } catch (error) {
        console.error('Error creating notification:', error);
    }
};
</script>
