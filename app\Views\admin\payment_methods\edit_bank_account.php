<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1><i class="fas fa-edit me-2"></i>Editar Cuenta Bancaria</h1>
        <p class="text-muted mb-0">Para: <?= esc($paymentMethod['name']) ?></p>
    </div>
    <div>
        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Editar Cuenta Bancaria</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bankName" class="form-label">Nombre del Banco <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="bankName" name="bank_name" required
                                       value="<?= esc($bankAccount['bank_name']) ?>"
                                       placeholder="Ej: Banco Industrial">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountNumber" class="form-label">Número de Cuenta <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="accountNumber" name="account_number" required
                                       value="<?= esc($bankAccount['account_number']) ?>"
                                       placeholder="Ej: ********90">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountType" class="form-label">Tipo de Cuenta</label>
                                <select class="form-select" id="accountType" name="account_type">
                                    <option value="monetaria" <?= $bankAccount['account_type'] === 'monetaria' ? 'selected' : '' ?>>Monetaria</option>
                                    <option value="ahorro" <?= $bankAccount['account_type'] === 'ahorro' ? 'selected' : '' ?>>Ahorro</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountHolderId" class="form-label">NIT/DPI del Titular</label>
                                <input type="text" class="form-control" id="accountHolderId" name="account_holder_id"
                                       value="<?= esc($bankAccount['account_holder_id']) ?>"
                                       placeholder="Ej: ********-9">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="accountHolder" class="form-label">Titular de la Cuenta <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="accountHolder" name="account_holder" required
                               value="<?= esc($bankAccount['account_holder']) ?>"
                               placeholder="Ej: MrCell Guatemala S.A.">
                    </div>

                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instrucciones Especiales</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="3"
                                  placeholder="Ej: Enviar comprobante por WhatsApp al +502 2345-6789"><?= esc($bankAccount['instructions']) ?></textarea>
                        <small class="form-text text-muted">
                            Instrucciones adicionales para el cliente al usar esta cuenta
                        </small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Cuenta
                        </button>
                        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
                            Cancelar
                        </a>
                        <button type="button" class="btn btn-danger ms-auto" 
                                onclick="confirmDelete(<?= $bankAccount['id'] ?>)">
                            <i class="fas fa-trash me-2"></i>Eliminar Cuenta
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Información de ayuda -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información Importante</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Asegúrate de que la información de la cuenta sea correcta</li>
                    <li>El titular de la cuenta debe coincidir con el nombre registrado en el banco</li>
                    <li>Las instrucciones aparecerán al cliente cuando seleccione este método de pago</li>
                    <li>Los cambios se aplicarán inmediatamente</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('¿Estás seguro de eliminar esta cuenta bancaria? Esta acción no se puede deshacer.')) {
        window.location.href = '/admin/payment-methods/delete-bank-account/' + id;
    }
}
</script>
<?= $this->endSection() ?>
