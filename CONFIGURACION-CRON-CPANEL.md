# 🔧 CONFIGURACIÓN DE CRON EN CPANEL PARA ALERTAS DEL SISTEMA

## 📋 INFORMACIÓN DEL HOSTING

- **Hosting:** cPanel (Linux)
- **PHP:** 8.2.29 (EA-PHP82)
- **Ruta del proyecto:** `/home/<USER>/public_html/accounts/mrcell.com.gt/`
- **Ruta de /public:** `/home/<USER>/public_html/accounts/mrcell.com.gt/public/`
- **PHP CLI:** `/opt/cpanel/ea-php82/root/usr/bin/php`

## 🚀 PASOS PARA CONFIGURAR EL CRON

### **PASO 1: Acceder a cPanel**
1. Inicia sesión en tu cPanel
2. Busca la sección **"Avanzado"** o **"Advanced"**
3. Haz clic en **"Cron Jobs"** o **"Trabajos Cron"**

### **PASO 2: Configurar el Cron Job**

#### **Configuración Recomendada:**

**Comando a ejecutar:**
```bash
/opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-alerts.php
```

#### **Programación (cada 12 horas):**

**Opción 1: Usando el selector de cPanel**
- **Configuración común:** Selecciona "Twice Daily" (Dos veces al día)
- **Hora:** 8:00 AM y 8:00 PM

**Opción 2: Configuración manual**
```
Minuto: 0
Hora: */12
Día: *
Mes: *
Día de la semana: *
```

**Opción 3: Configuración específica (recomendada)**
```
Minuto: 0
Hora: 8,20
Día: *
Mes: *
Día de la semana: *
```
*Esto ejecutará el cron a las 8:00 AM y 8:00 PM todos los días*

### **PASO 3: Configurar Email de Notificaciones (Opcional)**

En la sección **"Cron Email"**, puedes configurar:
- **Email:** `<EMAIL>` (para recibir logs por email)
- **O dejar vacío** si no quieres emails de cron

### **PASO 4: Verificar la Configuración**

Una vez guardado, deberías ver algo como:
```
0 8,20 * * * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/cron-alerts.php
```

## 📝 CONFIGURACIONES ALTERNATIVAS

### **Cada 6 horas:**
```
0 */6 * * * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/cron-alerts.php
```

### **Una vez al día (8:00 AM):**
```
0 8 * * * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/cron-alerts.php
```

### **Cada hora (para pruebas):**
```
0 * * * * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/cron-alerts.php
```

## 🧪 PROBAR EL CRON

### **Método 1: Ejecutar manualmente desde cPanel**
1. Ve a **"Cron Jobs"** en cPanel
2. Busca tu cron job configurado
3. Haz clic en **"Run Now"** o **"Ejecutar Ahora"** (si está disponible)

### **Método 2: Ejecutar desde SSH (si tienes acceso)**
```bash
cd /home/<USER>/public_html/accounts/mrcell.com.gt/
/opt/cpanel/ea-php82/root/usr/bin/php cron-alerts.php
```

### **Método 3: Crear un cron temporal para pruebas**
Configura un cron que se ejecute cada 5 minutos para probar:
```
*/5 * * * * /opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/cron-alerts.php
```
**¡Recuerda cambiarlo después a cada 12 horas!**

## 📊 VERIFICAR QUE FUNCIONA

### **1. Revisar Logs**
El script crea logs en: `/writable/logs/cron-alerts.log`

Puedes verificar el contenido desde:
- **File Manager** en cPanel
- **SSH** (si tienes acceso)
- **FTP**

### **2. Verificar en la Base de Datos**
El script registra cada ejecución en la tabla `cron_executions`:
```sql
SELECT * FROM cron_executions ORDER BY created_at DESC LIMIT 10;
```

### **3. Verificar WhatsApp**
Si hay alertas, deberías recibir mensajes en el grupo configurado: `120363416393766854`

## ⚠️ SOLUCIÓN DE PROBLEMAS

### **Error: "Command not found"**
- Verifica que la ruta de PHP sea correcta: `/opt/cpanel/ea-php82/root/usr/bin/php`
- Algunos hostings usan: `/usr/local/bin/php` o `/usr/bin/php`

### **Error: "Permission denied"**
- Asegúrate de que el archivo `cron-alerts.php` tenga permisos de ejecución (755)
- Verifica que la carpeta `writable/logs/` exista y tenga permisos de escritura

### **Error: "No such file or directory"**
- Verifica que la ruta del proyecto sea correcta
- Asegúrate de que el archivo `cron-alerts.php` esté en la raíz del proyecto

### **No se ejecuta el cron**
1. Verifica que el cron esté activo en cPanel
2. Revisa los logs de cron del hosting (si están disponibles)
3. Configura un email para recibir notificaciones de errores

## 📱 CONFIGURAR NÚMERO DE GRUPO

El número de grupo de WhatsApp se puede configurar desde:
- **Panel de Admin:** `/admin/settings?tab=notifications`
- **Número actual:** `120363416393766854`

## 🔄 FRECUENCIA RECOMENDADA

- **Producción:** Cada 12 horas (8:00 AM y 8:00 PM)
- **Desarrollo:** Cada hora o cada 6 horas
- **Pruebas:** Cada 5 minutos (temporal)

## 📞 SOPORTE

Si tienes problemas:
1. Revisa los logs en `/writable/logs/cron-alerts.log`
2. Verifica la configuración en cPanel
3. Contacta al soporte de tu hosting si es necesario

---

**¡El sistema de alertas automáticas está listo para funcionar en tu hosting cPanel!** 🎉
