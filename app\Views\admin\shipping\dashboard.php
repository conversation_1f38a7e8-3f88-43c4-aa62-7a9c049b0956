<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-truck me-2"></i>Dashboard de Envíos</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShippingModal">
            <i class="fas fa-plus me-2"></i>Nuevo Envío
        </button>
    </div>
</div>

<!-- Estadísticas de Envíos -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= count($pendingShipments ?? []) ?></h4>
                        <p class="mb-0">Envíos Pendientes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= count($recentDeliveries ?? []) ?></h4>
                        <p class="mb-0">Entregas Recientes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= count($companiesPerformance ?? []) ?></h4>
                        <p class="mb-0">Empresas Activas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>Q0.00</h4>
                        <p class="mb-0">Costo Promedio</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Envíos Pendientes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Envíos Pendientes</h5>
            </div>
            <div class="card-body">
                <?php if (empty($pendingShipments ?? [])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay envíos pendientes</p>
                        <small class="text-muted">Los envíos aparecerán aquí cuando se configuren las tablas de la base de datos</small>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Pedido</th>
                                    <th>Destino</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingShipments as $shipment): ?>
                                <tr>
                                    <td>#<?= $shipment['order_id'] ?></td>
                                    <td><?= $shipment['destination'] ?? 'N/A' ?></td>
                                    <td><span class="badge bg-warning"><?= ucfirst($shipment['status']) ?></span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" title="Ver detalles">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Entregas Recientes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Entregas Recientes</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentDeliveries ?? [])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay entregas recientes</p>
                        <small class="text-muted">Las entregas aparecerán aquí cuando se configuren las tablas de la base de datos</small>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Pedido</th>
                                    <th>Entregado</th>
                                    <th>Tiempo</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentDeliveries as $delivery): ?>
                                <tr>
                                    <td>#<?= $delivery['order_id'] ?></td>
                                    <td><?= date('d/m/Y', strtotime($delivery['delivered_at'])) ?></td>
                                    <td><?= $delivery['delivery_time'] ?? 'N/A' ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-success" title="Ver detalles">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Rendimiento de Empresas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Rendimiento de Empresas de Envío</h5>
            </div>
            <div class="card-body">
                <?php if (empty($companiesPerformance ?? [])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay datos de rendimiento disponibles</p>
                        <small class="text-muted">Los datos aparecerán aquí cuando se configuren las tablas de la base de datos</small>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Empresa</th>
                                    <th>Total Envíos</th>
                                    <th>Entregados</th>
                                    <th>Tiempo Promedio</th>
                                    <th>Costo Promedio</th>
                                    <th>Eficiencia</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($companiesPerformance as $company): ?>
                                <tr>
                                    <td><strong><?= $company['name'] ?></strong></td>
                                    <td><?= $company['total_shipments'] ?></td>
                                    <td><?= $company['delivered'] ?></td>
                                    <td><?= round($company['avg_delivery_days'], 1) ?> días</td>
                                    <td>Q<?= number_format($company['avg_cost'], 2) ?></td>
                                    <td>
                                        <?php 
                                        $efficiency = ($company['delivered'] / $company['total_shipments']) * 100;
                                        $badgeClass = $efficiency >= 90 ? 'bg-success' : ($efficiency >= 70 ? 'bg-warning' : 'bg-danger');
                                        ?>
                                        <span class="badge <?= $badgeClass ?>"><?= round($efficiency, 1) ?>%</span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
console.log('Dashboard de Envíos cargado correctamente');

// Actualizar estadísticas cada 30 segundos
setInterval(function() {
    // Aquí se puede agregar lógica para actualizar las estadísticas via AJAX
    console.log('Actualizando estadísticas de envíos...');
}, 30000);
</script>
<?= $this->endSection() ?>
