<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

/**
 * API Controller para el Carrito Frontend
 * 
 * Maneja las operaciones del carrito de compras
 */
class CartController extends ResourceController
{
    use ResponseTrait;

    protected $format = 'json';

    /**
     * GET /api/cart/count
     * Obtener cantidad de items en el carrito
     */
    public function count()
    {
        try {
            $sessionId = $this->getSessionId();
            $cart = session()->get('cart') ?? [];
            
            $count = 0;
            foreach ($cart as $item) {
                $count += $item['quantity'] ?? 0;
            }

            return $this->respond([
                'status' => 'success',
                'count' => $count
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::count: ' . $e->getMessage());
            return $this->respond([
                'status' => 'success',
                'count' => 0
            ]);
        }
    }

    /**
     * GET /api/cart/items
     * Obtener items del carrito
     */
    public function items()
    {
        try {
            $sessionId = $this->getSessionId();
            $cart = session()->get('cart') ?? [];
            
            $items = [];
            $subtotal = 0;
            $totalQuantity = 0;

            foreach ($cart as $productId => $item) {
                $itemTotal = $item['price'] * $item['quantity'];
                $subtotal += $itemTotal;
                $totalQuantity += $item['quantity'];
                
                $items[] = [
                    'product_id' => $productId,
                    'name' => $item['name'],
                    'price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'total' => $itemTotal,
                    'image' => $item['image'] ?? null,
                    'sku' => $item['sku'] ?? null
                ];
            }

            $tax = $subtotal * 0.12; // 12% IVA
            $shipping = 0; // El envío se calcula cuando se selecciona el método
            $total = $subtotal + $tax + $shipping;

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'session_id' => $sessionId,
                    'items' => $items,
                    'totals' => [
                        'subtotal' => $subtotal,
                        'tax' => $tax,
                        'shipping' => $shipping,
                        'total' => $total,
                        'quantity' => $totalQuantity
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::items: ' . $e->getMessage());
            return $this->respond([
                'status' => 'error',
                'message' => 'Error al obtener items del carrito'
            ]);
        }
    }

    /**
     * POST /api/cart/add
     * Agregar producto al carrito
     */
    public function add()
    {
        try {
            $data = $this->request->getJSON(true) ?? $this->request->getPost();

            if (!isset($data['product_id']) || !isset($data['quantity'])) {
                return $this->failValidationError('product_id y quantity son requeridos');
            }

            $productId = (string)$data['product_id'];
            $quantity = (int)$data['quantity'];
            $variantId = isset($data['variant_id']) ? (string)$data['variant_id'] : null;

            if ($quantity <= 0) {
                return $this->failValidationError('La cantidad debe ser mayor a 0');
            }

            // Obtener información del producto y variante
            $product = $this->getProductInfo($productId);
            if (!$product) {
                return $this->failNotFound('Producto no encontrado');
            }

            $variant = null;
            if ($variantId) {
                $variant = $this->getVariantInfo($variantId);
                if (!$variant) {
                    return $this->failNotFound('Variante no encontrada');
                }

                // Verificar que la variante pertenece al producto
                if ($variant['product_id'] != $productId) {
                    return $this->failValidationError('La variante no pertenece al producto especificado');
                }
            }

            // Obtener carrito actual
            $cart = session()->get('cart') ?? [];

            // Crear clave única para el item (producto + variante)
            $cartKey = $variantId ? $productId . '_variant_' . $variantId : $productId;

            // Determinar datos a usar (variante o producto)
            $itemData = [
                'product_id' => $productId,
                'variant_id' => $variantId,
                'name' => $variant ? $variant['name'] : $product['name'],
                'price' => $variant ? ($variant['price_sale'] ?: $variant['price_regular']) : $product['price'],
                'quantity' => $quantity,
                'image' => $variant ? $variant['image'] : $product['image'],
                'sku' => $variant ? $variant['sku'] : $product['sku'],
                'stock' => $variant ? $variant['stock_quantity'] : $product['stock']
            ];

            // Verificar stock disponible
            if ($itemData['stock'] < $quantity) {
                return $this->failValidationError('Stock insuficiente. Solo hay ' . $itemData['stock'] . ' unidades disponibles');
            }

            // Si el item ya existe en el carrito, sumar cantidad
            if (isset($cart[$cartKey])) {
                $newQuantity = $cart[$cartKey]['quantity'] + $quantity;
                if ($itemData['stock'] < $newQuantity) {
                    return $this->failValidationError('Stock insuficiente. Solo hay ' . $itemData['stock'] . ' unidades disponibles');
                }
                $cart[$cartKey]['quantity'] = $newQuantity;
            } else {
                $cart[$cartKey] = $itemData;
            }

            // Guardar carrito en sesión
            session()->set('cart', $cart);

            return $this->respond([
                'status' => 'success',
                'message' => 'Producto agregado al carrito',
                'product_name' => $itemData['name'],
                'cart_count' => $this->getCartCount(),
                'cart_total' => $this->getCartTotal()
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::add: ' . $e->getMessage());
            return $this->failServerError('Error al agregar producto al carrito');
        }
    }

    /**
     * PUT /api/cart/update
     * Actualizar cantidad de producto en carrito
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getJSON(true) ?? $this->request->getPost();
            
            if (!isset($data['product_id']) || !isset($data['quantity'])) {
                return $this->failValidationError('product_id y quantity son requeridos');
            }

            $productId = (string)$data['product_id'];
            $quantity = (int)$data['quantity'];
            
            $cart = session()->get('cart') ?? [];
            
            if (!isset($cart[$productId])) {
                return $this->failNotFound('Producto no encontrado en el carrito');
            }

            if ($quantity <= 0) {
                // Remover producto si cantidad es 0 o menor
                unset($cart[$productId]);
            } else {
                $cart[$productId]['quantity'] = $quantity;
            }

            session()->set('cart', $cart);

            return $this->respond([
                'status' => 'success',
                'message' => 'Carrito actualizado',
                'cart_count' => $this->getCartCount()
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::update: ' . $e->getMessage());
            return $this->failServerError('Error al actualizar carrito');
        }
    }

    /**
     * DELETE /api/cart/remove/{product_id}
     * Remover producto del carrito
     */
    public function remove($productId = null)
    {
        try {
            if (!$productId) {
                return $this->failValidationError('ID de producto requerido');
            }

            // Convertir a string para consistencia con las claves del carrito
            $productId = (string)$productId;

            $cart = session()->get('cart') ?? [];

            if (!isset($cart[$productId])) {
                return $this->failNotFound('Producto no encontrado en el carrito');
            }

            unset($cart[$productId]);
            session()->set('cart', $cart);

            return $this->respond([
                'status' => 'success',
                'message' => 'Producto removido del carrito',
                'cart_count' => $this->getCartCount()
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::remove: ' . $e->getMessage());
            return $this->failServerError('Error al remover producto del carrito');
        }
    }

    /**
     * DELETE /api/cart/clear
     * Limpiar carrito completo
     */
    public function clear()
    {
        try {
            session()->remove('cart');

            return $this->respond([
                'status' => 'success',
                'message' => 'Carrito limpiado',
                'cart_count' => 0
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CartController::clear: ' . $e->getMessage());
            return $this->failServerError('Error al limpiar carrito');
        }
    }

    /**
     * Obtener ID de sesión
     */
    private function getSessionId()
    {
        return session_id() ?: $this->request->getHeaderLine('X-Session-ID') ?: uniqid();
    }

    /**
     * Obtener información del producto desde la base de datos
     */
    private function getProductInfo($productId)
    {
        try {
            $db = \Config\Database::connect();

            $product = $db->table('products')
                         ->select('id, name, sku, price_regular, price_sale, featured_image, stock_quantity')
                         ->where('id', $productId)
                         ->where('is_active', 1)
                         ->where('deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if (!$product) {
                return null;
            }

            // Formatear producto para el carrito
            return [
                'name' => $product['name'],
                'price' => $product['price_sale'] ?: $product['price_regular'],
                'image' => $product['featured_image'] ? base_url($product['featured_image']) : null,
                'sku' => $product['sku'],
                'stock' => $product['stock_quantity']
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo producto: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener información de una variante desde la base de datos
     */
    private function getVariantInfo($variantId)
    {
        try {
            $db = \Config\Database::connect();

            $variant = $db->table('product_variants')
                         ->select('id, product_id, name, sku, price_regular, price_sale, featured_image, stock_quantity')
                         ->where('id', $variantId)
                         ->where('is_active', 1)
                         ->where('deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if (!$variant) {
                return null;
            }

            // Formatear variante para el carrito
            return [
                'product_id' => $variant['product_id'],
                'name' => $variant['name'],
                'price_regular' => $variant['price_regular'],
                'price_sale' => $variant['price_sale'],
                'image' => $variant['featured_image'] ? base_url($variant['featured_image']) : null,
                'sku' => $variant['sku'],
                'stock_quantity' => $variant['stock_quantity']
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo variante: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener cantidad total de items en carrito
     */
    private function getCartCount()
    {
        $cart = session()->get('cart') ?? [];
        $count = 0;

        foreach ($cart as $item) {
            $count += $item['quantity'] ?? 0;
        }

        return $count;
    }

    /**
     * Obtener total del carrito
     */
    private function getCartTotal()
    {
        $cart = session()->get('cart') ?? [];
        $total = 0;

        foreach ($cart as $item) {
            $total += ($item['price'] ?? 0) * ($item['quantity'] ?? 0);
        }

        return $total;
    }
}
