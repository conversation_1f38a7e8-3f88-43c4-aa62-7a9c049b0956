<?php

namespace App\Models;

use CodeIgniter\Model;

class WhatsAppSettingsModel extends Model
{
    protected $table = 'whatsapp_settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'setting_key',
        'setting_value',
        'description',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'setting_key' => 'required|max_length[100]|is_unique[whatsapp_settings.setting_key,id,{id}]',
        'setting_value' => 'permit_empty',
        'description' => 'permit_empty|max_length[255]',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'setting_key' => [
            'required' => 'La clave de configuración es requerida',
            'is_unique' => 'Esta clave de configuración ya existe'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Obtener configuración por clave
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)
                       ->where('is_active', 1)
                       ->first();
        
        return $setting ? $setting['setting_value'] : $default;
    }

    /**
     * Actualizar configuración
     */
    public function updateSetting($key, $value)
    {
        $existing = $this->where('setting_key', $key)->first();
        
        if ($existing) {
            return $this->update($existing['id'], [
                'setting_value' => $value,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            return $this->insert([
                'setting_key' => $key,
                'setting_value' => $value,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Obtener todas las configuraciones como array asociativo
     */
    public function getAllSettings()
    {
        $settings = $this->where('is_active', 1)->findAll();
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    }

    /**
     * Verificar si WhatsApp está habilitado
     */
    public function isEnabled()
    {
        return $this->getSetting('enabled', '0') === '1';
    }

    /**
     * Obtener configuración completa para envío
     */
    public function getApiConfig()
    {
        return [
            'api_url' => $this->getSetting('api_url'),
            'api_key' => $this->getSetting('api_key'),
            'device_token' => $this->getSetting('device_token'),
            'enabled' => $this->isEnabled()
        ];
    }

    /**
     * Validar configuración de API
     */
    public function validateApiConfig()
    {
        $config = $this->getApiConfig();
        
        $errors = [];
        
        if (empty($config['api_url'])) {
            $errors[] = 'URL de API no configurada';
        }
        
        if (empty($config['api_key'])) {
            $errors[] = 'API Key no configurada';
        }
        
        if (empty($config['device_token'])) {
            $errors[] = 'Device Token no configurado';
        }
        
        return empty($errors) ? true : $errors;
    }
}
