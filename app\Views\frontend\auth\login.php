<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Iniciar <PERSON><PERSON><PERSON> - Mr<PERSON>ell <PERSON>' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* <PERSON><PERSON>a <PERSON> - <PERSON>, Negro, Blanco, Gris */
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --success-color: #059669;        /* Verde para éxito */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris medio claro */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Negro gris */
            --gray-900: #111827;             /* Negro */
        }
        
        .auth-container {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--gray-800) 50%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .auth-card {
            background: var(--white-color);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            position: relative;
            z-index: 1;
            border: 1px solid var(--gray-200);
        }
        
        .auth-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white-color);
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .auth-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        }
        
        .auth-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.8rem;
            position: relative;
            z-index: 1;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background-color: var(--white-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
            background-color: var(--white-color);
        }

        .form-floating > label {
            color: var(--gray-500);
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            color: var(--white-color);
        }

        .btn-login:hover::before {
            left: 100%;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn-social {
            flex: 1;
            padding: 10px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-social:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .auth-footer {
            text-align: center;
            padding: 20px;
            background: var(--light-color);
            color: var(--gray-600);
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--primary-dark);
        }

        /* Estilos para alertas */
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
            padding: 15px 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--primary-dark));
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color), #0ea5e9);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
        }
        
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
        }
        
        .input-group-password {
            position: relative;
        }
    </style>
</head>
<body>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="/logo.jpg" alt="MrCell Logo" style="width: 40px; height: 40px; object-fit: contain;">
                </div>
                <h3>Bienvenido a MrCell</h3>
                <p class="mb-0">Inicia sesión en tu cuenta</p>
            </div>

            <div class="p-4">
                <!-- Mensajes de error/éxito -->
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Formulario de Login -->
                <form action="<?= base_url('login') ?>" method="POST" id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= old('email') ?>" required
                               placeholder="<EMAIL>">
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Contraseña
                        </label>
                        <div class="input-group-password">
                            <input type="password" class="form-control" id="password" name="password" 
                                   required placeholder="Tu contraseña">
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Recordarme
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                    </button>
                </form>



                <!-- Enlaces adicionales -->
                <div class="text-center">
                    <a href="#" class="text-decoration-none" onclick="alert('Próximamente')">
                        ¿Olvidaste tu contraseña?
                    </a>
                </div>
            </div>

            <div class="auth-footer">
                <p class="mb-0">
                    ¿No tienes cuenta? 
                    <a href="<?= base_url('register') ?>" class="text-decoration-none fw-bold">
                        Regístrate aquí
                    </a>
                </p>
                <div class="mt-2">
                    <a href="<?= base_url() ?>" class="text-decoration-none text-muted">
                        <i class="fas fa-arrow-left me-1"></i>Volver al inicio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
            }
        }

        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            console.log('Form submit event triggered');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            console.log('Email:', email);
            console.log('Password length:', password.length);

            if (!email || !password) {
                console.log('Validation failed: empty fields');
                e.preventDefault();
                alert('Por favor completa todos los campos');
                return false;
            }

            if (password.length < 6) {
                console.log('Validation failed: password too short');
                e.preventDefault();
                alert('La contraseña debe tener al menos 6 caracteres');
                return false;
            }

            console.log('Validation passed, submitting form');
            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Iniciando sesión...';
            submitBtn.disabled = true;
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
