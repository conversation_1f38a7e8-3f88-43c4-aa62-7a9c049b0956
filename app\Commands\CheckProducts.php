<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CheckProducts extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:check-products';
    protected $description = 'Verificar productos en la base de datos';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== PRODUCTOS EN LA BASE DE DATOS ===', 'yellow');
            CLI::newLine();
            
            $products = $db->query("SELECT id, name, price_regular, price_sale FROM products LIMIT 10")->getResultArray();
            
            if (empty($products)) {
                CLI::error('❌ No hay productos en la base de datos');
            } else {
                CLI::write('✅ Productos encontrados:', 'green');
                CLI::newLine();
                
                foreach ($products as $product) {
                    CLI::write("ID: {$product['id']}", 'white');
                    CLI::write("Nombre: {$product['name']}", 'white');
                    CLI::write("Precio regular: Q{$product['price_regular']}", 'white');
                    CLI::write("Precio oferta: " . ($product['price_sale'] ? "Q{$product['price_sale']}" : "N/A"), 'white');
                    CLI::write('---', 'dark_gray');
                }
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
