<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\CouponManager;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Cupones y Descuentos
 * Panel de administración para gestión de cupones, promociones y campañas
 */
class CouponsController extends BaseController
{
    private $couponManager;
    protected $logger;
    
    public function __construct()
    {
        $this->couponManager = new CouponManager();
        $this->logger = new AdvancedLogger();
    }
    
    /**
     * Dashboard de cupones
     */
    public function index()
    {
        $data = [
            'title' => 'Gestión de Cupones - MrCell Guatemala',
            'coupon_stats' => $this->couponManager->getCouponStats(),
            'active_coupons' => $this->getActiveCoupons(),
            'recent_usage' => $this->getRecentCouponUsage(),
            'top_performing' => $this->getTopPerformingCoupons()
        ];
        
        return view('admin/coupons/dashboard', $data);
    }
    
    /**
     * Lista de cupones
     */
    public function coupons()
    {
        $data = [
            'title' => 'Lista de Cupones - MrCell Guatemala',
            'coupons' => $this->getAllCoupons(),
            'coupon_types' => ['percentage', 'fixed', 'free_shipping', 'buy_x_get_y']
        ];
        
        return view('admin/coupons/list', $data);
    }
    
    /**
     * Crear cupón
     */
    public function create()
    {
        $data = [
            'title' => 'Crear Cupón - MrCell Guatemala',
            'products' => $this->getProducts(),
            'categories' => $this->getCategories()
        ];
        
        return view('admin/coupons/create', $data);
    }
    
    /**
     * Guardar cupón
     */
    public function store()
    {
        try {
            $couponData = [
                'code' => $this->request->getPost('code'),
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'type' => $this->request->getPost('type'),
                'value' => (float)$this->request->getPost('value'),
                'min_order_amount' => (float)$this->request->getPost('min_order_amount'),
                'max_discount_amount' => $this->request->getPost('max_discount_amount') ? (float)$this->request->getPost('max_discount_amount') : null,
                'usage_limit' => $this->request->getPost('usage_limit') ? (int)$this->request->getPost('usage_limit') : null,
                'usage_limit_per_user' => (int)$this->request->getPost('usage_limit_per_user'),
                'valid_from' => $this->request->getPost('valid_from'),
                'valid_until' => $this->request->getPost('valid_until'),
                'applicable_products' => $this->request->getPost('applicable_products') ?? [],
                'applicable_categories' => $this->request->getPost('applicable_categories') ?? [],
                'excluded_products' => $this->request->getPost('excluded_products') ?? [],
                'excluded_categories' => $this->request->getPost('excluded_categories') ?? [],
                'first_order_only' => $this->request->getPost('first_order_only') ? 1 : 0,
                'stackable' => $this->request->getPost('stackable') ? 1 : 0,
                'auto_apply' => $this->request->getPost('auto_apply') ? 1 : 0,
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'created_by' => session('user_id') ?? 0
            ];
            
            $result = $this->couponManager->createCoupon($couponData);
            
            if ($result['success']) {
                session()->setFlashdata('success', 'Cupón creado exitosamente');
                return redirect()->to('/admin/coupons');
            } else {
                session()->setFlashdata('error', $result['error'] ?? 'Error al crear cupón');
                return redirect()->back()->withInput();
            }
            
        } catch (\Exception $e) {
            session()->setFlashdata('error', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }
    
    /**
     * Editar cupón
     */
    public function edit($couponId)
    {
        $coupon = $this->getCouponById($couponId);
        
        if (!$coupon) {
            session()->setFlashdata('error', 'Cupón no encontrado');
            return redirect()->to('/admin/coupons');
        }
        
        $data = [
            'title' => 'Editar Cupón - MrCell Guatemala',
            'coupon' => $coupon,
            'products' => $this->getProducts(),
            'categories' => $this->getCategories()
        ];
        
        return view('admin/coupons/edit', $data);
    }
    
    /**
     * Actualizar cupón
     */
    public function update($couponId)
    {
        try {
            // Validar que el cupón existe
            $existingCoupon = $this->getCouponById($couponId);
            if (!$existingCoupon) {
                session()->setFlashdata('error', 'Cupón no encontrado');
                return redirect()->to('/admin/coupons');
            }

            // Validar datos de entrada
            $validation = \Config\Services::validation();
            $validation->setRules([
                'code' => 'required|max_length[50]',
                'name' => 'required|max_length[255]',
                'type' => 'required|in_list[percentage,fixed,free_shipping,buy_x_get_y]',
                'value' => 'permit_empty|decimal',
                'min_order_amount' => 'permit_empty|decimal',
                'max_discount_amount' => 'permit_empty|decimal',
                'usage_limit' => 'permit_empty|integer',
                'usage_limit_per_user' => 'permit_empty|integer',
                'valid_from' => 'permit_empty|valid_date',
                'valid_until' => 'permit_empty|valid_date'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                session()->setFlashdata('error', 'Datos inválidos: ' . implode(', ', $validation->getErrors()));
                return redirect()->back()->withInput();
            }

            // Verificar que el código no esté duplicado (excepto el actual)
            $codeExists = $this->db->table('coupons')
                                  ->where('code', $this->request->getPost('code'))
                                  ->where('id !=', $couponId)
                                  ->countAllResults();

            if ($codeExists > 0) {
                session()->setFlashdata('error', 'El código del cupón ya existe');
                return redirect()->back()->withInput();
            }

            // Preparar datos para actualización
            $updateData = [
                'code' => strtoupper($this->request->getPost('code')),
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'type' => $this->request->getPost('type'),
                'min_order_amount' => $this->request->getPost('min_order_amount') ?: 0,
                'max_discount_amount' => $this->request->getPost('max_discount_amount') ?: null,
                'usage_limit' => $this->request->getPost('usage_limit') ?: null,
                'usage_limit_per_user' => $this->request->getPost('usage_limit_per_user') ?: 1,
                'valid_from' => $this->request->getPost('valid_from') ?: date('Y-m-d H:i:s'),
                'valid_until' => $this->request->getPost('valid_until') ?: null,
                'first_order_only' => $this->request->getPost('first_order_only') ? 1 : 0,
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Solo agregar valor si no es envío gratis
            if ($this->request->getPost('type') !== 'free_shipping') {
                $updateData['value'] = $this->request->getPost('value') ?: 0;
            }

            // Actualizar productos y categorías aplicables
            $applicableProducts = $this->request->getPost('applicable_products') ?: [];
            $applicableCategories = $this->request->getPost('applicable_categories') ?: [];

            $updateData['applicable_products'] = !empty($applicableProducts) ? json_encode($applicableProducts) : null;
            $updateData['applicable_categories'] = !empty($applicableCategories) ? json_encode($applicableCategories) : null;

            // Actualizar cupón
            $result = $this->db->table('coupons')
                              ->where('id', $couponId)
                              ->update($updateData);

            if ($result) {
                // Log de la acción
                $this->logger->userAction('coupon_updated', session('admin_id'), [
                    'coupon_id' => $couponId,
                    'coupon_code' => $updateData['code']
                ]);

                session()->setFlashdata('success', 'Cupón actualizado exitosamente');
                return redirect()->to('/admin/coupons');
            } else {
                session()->setFlashdata('error', 'Error al actualizar el cupón');
                return redirect()->back()->withInput();
            }

        } catch (\Exception $e) {
            $this->logger->error('Error updating coupon: ' . $e->getMessage(), [
                'coupon_id' => $couponId,
                'admin_id' => session('admin_id')
            ]);

            session()->setFlashdata('error', 'Error interno: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }
    
    /**
     * Eliminar cupón
     */
    public function delete($couponId)
    {
        try {
            $result = $this->db->table('coupons')
                              ->where('id', $couponId)
                              ->update(['is_active' => 0]);
            
            if ($result) {
                session()->setFlashdata('success', 'Cupón desactivado exitosamente');
            } else {
                session()->setFlashdata('error', 'Error al desactivar cupón');
            }
            
            return redirect()->to('/admin/coupons');
            
        } catch (\Exception $e) {
            session()->setFlashdata('error', $e->getMessage());
            return redirect()->to('/admin/coupons');
        }
    }
    
    /**
     * Descuentos por volumen
     */
    public function bulkDiscounts()
    {
        $data = [
            'title' => 'Descuentos por Volumen - MrCell Guatemala',
            'bulk_discounts' => $this->getBulkDiscounts(),
            'products' => $this->getProducts(),
            'categories' => $this->getCategories()
        ];
        
        return view('admin/coupons/bulk_discounts', $data);
    }
    
    /**
     * Crear descuento por volumen
     */
    public function createBulkDiscount()
    {
        try {
            $discountData = [
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'min_quantity' => (int)$this->request->getPost('min_quantity'),
                'discount_type' => $this->request->getPost('discount_type'),
                'discount_value' => (float)$this->request->getPost('discount_value'),
                'applicable_products' => $this->request->getPost('applicable_products') ?? [],
                'applicable_categories' => $this->request->getPost('applicable_categories') ?? [],
                'valid_from' => $this->request->getPost('valid_from'),
                'valid_until' => $this->request->getPost('valid_until'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];
            
            $result = $this->couponManager->createBulkDiscount($discountData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Campañas promocionales
     */
    public function campaigns()
    {
        $data = [
            'title' => 'Campañas Promocionales - MrCell Guatemala',
            'campaigns' => $this->getPromotionalCampaigns(),
            'campaign_types' => ['seasonal', 'flash_sale', 'clearance', 'new_customer']
        ];
        
        return view('admin/coupons/campaigns', $data);
    }
    
    /**
     * Crear campaña promocional
     */
    public function createCampaign()
    {
        try {
            $campaignData = [
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'type' => $this->request->getPost('type'),
                'discount_type' => $this->request->getPost('discount_type'),
                'discount_value' => (float)$this->request->getPost('discount_value'),
                'target_audience' => $this->request->getPost('target_audience'),
                'min_order_amount' => (float)$this->request->getPost('min_order_amount'),
                'max_uses' => $this->request->getPost('max_uses') ? (int)$this->request->getPost('max_uses') : null,
                'start_date' => $this->request->getPost('start_date'),
                'end_date' => $this->request->getPost('end_date'),
                'banner_text' => $this->request->getPost('banner_text'),
                'banner_color' => $this->request->getPost('banner_color'),
                'email_template' => $this->request->getPost('email_template'),
                'auto_generate_coupons' => $this->request->getPost('auto_generate_coupons') ? 1 : 0,
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];
            
            $result = $this->couponManager->createPromotionalCampaign($campaignData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Estadísticas de cupones
     */
    public function stats()
    {
        try {
            $days = (int)($this->request->getGet('days') ?? 30);
            $stats = $this->couponManager->getCouponStats($days);
            
            return $this->response->setJSON($stats);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Validar cupón
     */
    public function validateCoupon()
    {
        try {
            $couponCode = $this->request->getPost('code');
            $orderTotal = (float)$this->request->getPost('order_total');
            $userId = $this->request->getPost('user_id');
            
            $orderData = [
                'total' => $orderTotal,
                'items' => [] // Simplificado
            ];
            
            $result = $this->couponManager->applyCoupon($couponCode, $orderData, $userId);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Generar código de cupón
     */
    public function generateCode()
    {
        try {
            $code = 'SAVE' . strtoupper(bin2hex(random_bytes(4)));
            
            return $this->response->setJSON([
                'success' => true,
                'code' => $code
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Exportar datos de cupones
     */
    public function export()
    {
        try {
            $type = $this->request->getPost('type') ?? 'coupons';
            $format = $this->request->getPost('format') ?? 'csv';
            
            switch ($type) {
                case 'coupons':
                    $data = $this->getAllCoupons();
                    break;
                case 'usage':
                    $data = $this->getCouponUsageData();
                    break;
                case 'campaigns':
                    $data = $this->getPromotionalCampaigns();
                    break;
                default:
                    throw new \Exception("Unknown export type: $type");
            }
            
            $filename = $this->exportData($data, $type, $format);
            
            return $this->response->setJSON([
                'success' => true,
                'download_url' => base_url('exports/' . $filename),
                'filename' => $filename
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cambiar estado del cupón (AJAX)
     */
    public function toggleStatus()
    {
        try {
            $couponId = $this->request->getPost('coupon_id');
            $status = $this->request->getPost('status');

            if (!$couponId || !isset($status)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Datos requeridos faltantes'
                ]);
            }

            $result = $this->db->table('coupons')
                              ->where('id', $couponId)
                              ->update(['is_active' => $status ? 1 : 0]);

            if ($result) {
                $this->logger->userAction('coupon_status_changed', session('admin_id'), [
                    'coupon_id' => $couponId,
                    'new_status' => $status ? 'active' : 'inactive'
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Estado del cupón actualizado'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al actualizar el estado'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }

    /**
     * Eliminar cupón (AJAX)
     */
    public function deleteCoupon()
    {
        try {
            $couponId = $this->request->getPost('coupon_id');

            if (!$couponId) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID del cupón requerido'
                ]);
            }

            // Verificar si el cupón tiene usos
            $usageCount = $this->db->table('coupon_usage')
                                  ->where('coupon_id', $couponId)
                                  ->countAllResults();

            if ($usageCount > 0) {
                // Si tiene usos, solo desactivar
                $result = $this->db->table('coupons')
                                  ->where('id', $couponId)
                                  ->update(['is_active' => 0, 'deleted_at' => date('Y-m-d H:i:s')]);
            } else {
                // Si no tiene usos, eliminar completamente
                $result = $this->db->table('coupons')
                                  ->where('id', $couponId)
                                  ->delete();
            }

            if ($result) {
                $this->logger->userAction('coupon_deleted', session('admin_id'), [
                    'coupon_id' => $couponId,
                    'had_usage' => $usageCount > 0
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Cupón eliminado exitosamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al eliminar el cupón'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error interno del servidor'
            ]);
        }
    }



    /**
     * Métodos privados auxiliares
     */
    private function getActiveCoupons(): array
    {
        return $this->db->table('coupons')
                       ->where('is_active', 1)
                       ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                       ->orderBy('created_at', 'DESC')
                       ->limit(10)
                       ->get()
                       ->getResultArray();
    }
    
    private function getAllCoupons(): array
    {
        return $this->db->table('coupons')
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getCouponById(int $couponId): ?array
    {
        return $this->db->table('coupons')
                       ->where('id', $couponId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getRecentCouponUsage(): array
    {
        return $this->db->table('coupon_usage cu')
                       ->select('cu.*, c.code, c.name, u.name as user_name')
                       ->join('coupons c', 'c.id = cu.coupon_id', 'left')
                       ->join('users u', 'u.id = cu.user_id', 'left')
                       ->orderBy('cu.created_at', 'DESC')
                       ->limit(20)
                       ->get()
                       ->getResultArray();
    }
    
    private function getTopPerformingCoupons(): array
    {
        return $this->db->query("
            SELECT c.code, c.name, COUNT(cu.id) as uses, SUM(cu.discount_amount) as total_discount
            FROM coupons c
            LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
            WHERE cu.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY c.id, c.code, c.name
            ORDER BY uses DESC
            LIMIT 10
        ")->getResultArray();
    }
    
    private function getBulkDiscounts(): array
    {
        return $this->db->table('bulk_discounts')
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getPromotionalCampaigns(): array
    {
        return $this->db->table('promotional_campaigns')
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getProducts(): array
    {
        return $this->db->table('products')
                       ->select('id, name, price')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getCategories(): array
    {
        return $this->db->table('categories')
                       ->select('id, name')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getCouponUsageData(): array
    {
        return $this->db->table('coupon_usage cu')
                       ->select('cu.*, c.code, c.name, u.name as user_name, u.email as user_email')
                       ->join('coupons c', 'c.id = cu.coupon_id', 'left')
                       ->join('users u', 'u.id = cu.user_id', 'left')
                       ->orderBy('cu.created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function exportData(array $data, string $type, string $format): string
    {
        $filename = "coupons_{$type}_" . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if (!is_dir(WRITEPATH . 'exports/')) {
            mkdir(WRITEPATH . 'exports/', 0755, true);
        }
        
        if ($format === 'csv') {
            $this->exportToCSV($data, $filepath);
        } else {
            file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
        }
        
        return $filename;
    }
    
    private function exportToCSV(array $data, string $filepath): void
    {
        if (empty($data)) return;
        
        $handle = fopen($filepath, 'w');
        
        // Headers
        fputcsv($handle, array_keys($data[0]));
        
        // Data
        foreach ($data as $row) {
            fputcsv($handle, array_values($row));
        }
        
        fclose($handle);
    }
}
