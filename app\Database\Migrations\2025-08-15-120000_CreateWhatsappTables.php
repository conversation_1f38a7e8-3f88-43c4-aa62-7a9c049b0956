<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateWhatsappTables extends Migration
{
    public function up()
    {
        // Tabla de configuración de WhatsApp
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'setting_key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'description' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('whatsapp_settings');

        // Insertar configuraciones por defecto
        $data = [
            [
                'setting_key' => 'api_url',
                'setting_value' => 'http://**************/api/sendMessage',
                'description' => 'URL de la API de WhatsApp',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'api_key',
                'setting_value' => 'msa_662507a99485d3fb883fca2e283bd598127c525ba62f4a9d763c8e85de7f4819',
                'description' => 'API Key para autenticación',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'device_token',
                'setting_value' => '9LIXJVGW',
                'description' => 'Token del dispositivo WhatsApp',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'enabled',
                'setting_value' => '1',
                'description' => 'Habilitar/deshabilitar notificaciones WhatsApp',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];
        $this->db->table('whatsapp_settings')->insertBatch($data);

        // Tabla de plantillas de mensajes
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'template_key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
            ],
            'template_name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
            ],
            'message_template' => [
                'type' => 'TEXT',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'variables' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'is_mandatory' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si es true, el usuario no puede desactivar esta notificación',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('whatsapp_templates');

        // Insertar plantillas por defecto
        $templates = [
            [
                'template_key' => 'customer_registration',
                'template_name' => 'Registro de Cliente',
                'message_template' => '¡Bienvenido a MrCell, {customer_name}! 🎉\n\nTu cuenta ha sido creada exitosamente.\nEmail: {customer_email}\n\n¡Gracias por unirte a nosotros!',
                'description' => 'Mensaje enviado cuando un cliente se registra',
                'variables' => json_encode(['customer_name', 'customer_email']),
                'is_mandatory' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_key' => 'order_created',
                'template_name' => 'Orden Creada',
                'message_template' => '¡Hola {customer_name}! 📦\n\nTu orden #{order_number} ha sido creada exitosamente.\nTotal: Q{order_total}\n\n¡Pronto recibirás más información!',
                'description' => 'Mensaje enviado cuando se crea una orden',
                'variables' => json_encode(['customer_name', 'order_number', 'order_total']),
                'is_mandatory' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_key' => 'order_status_change',
                'template_name' => 'Cambio de Estado de Orden',
                'message_template' => 'Hola {customer_name} 📋\n\nTu orden #{order_number} ha cambiado de estado:\n🔄 {old_status} → {new_status}\n\n¡Mantente atento a las actualizaciones!',
                'description' => 'Mensaje enviado cuando cambia el estado de una orden',
                'variables' => json_encode(['customer_name', 'order_number', 'old_status', 'new_status']),
                'is_mandatory' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_key' => 'product_added',
                'template_name' => 'Producto Agregado',
                'message_template' => '¡Nuevo producto disponible! 🆕\n\n📱 {product_name}\n💰 Precio: Q{product_price}\n\n¡No te lo pierdas!',
                'description' => 'Mensaje enviado cuando se agrega un nuevo producto',
                'variables' => json_encode(['product_name', 'product_price']),
                'is_mandatory' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_key' => 'product_discount',
                'template_name' => 'Descuento en Producto',
                'message_template' => '¡OFERTA ESPECIAL! 🔥\n\n📱 {product_name}\n💰 Precio anterior: Q{old_price}\n🎯 Precio con descuento: Q{new_price}\n💸 ¡Ahorra Q{discount_amount}!\n\n¡Aprovecha esta oportunidad!',
                'description' => 'Mensaje enviado cuando un producto tiene descuento',
                'variables' => json_encode(['product_name', 'old_price', 'new_price', 'discount_amount']),
                'is_mandatory' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];
        $this->db->table('whatsapp_templates')->insertBatch($templates);

        // Tabla de preferencias de notificaciones de usuarios
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'template_key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'is_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'template_key'], false, true); // Índice único compuesto
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('user_notification_preferences');

        // Tabla de log de mensajes enviados
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'phone_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
            ],
            'template_key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'message_content' => [
                'type' => 'TEXT',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['sent', 'failed', 'pending'],
                'default' => 'pending',
            ],
            'api_response' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'sent_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('whatsapp_message_log');
    }

    public function down()
    {
        $this->forge->dropTable('whatsapp_message_log');
        $this->forge->dropTable('user_notification_preferences');
        $this->forge->dropTable('whatsapp_templates');
        $this->forge->dropTable('whatsapp_settings');
    }
}
