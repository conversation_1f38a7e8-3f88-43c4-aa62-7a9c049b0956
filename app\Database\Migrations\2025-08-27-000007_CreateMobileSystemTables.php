<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tablas del sistema móvil
 * Incluye notificaciones push, PWA y optimizaciones móviles
 */
class CreateMobileSystemTables extends Migration
{
    public function up()
    {
        // Tabla de suscripciones push
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'endpoint' => [
                'type' => 'TEXT',
                'null' => false,
                'comment' => 'Push service endpoint URL',
            ],
            'p256dh_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
                'comment' => 'Public key for encryption',
            ],
            'auth_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
                'comment' => 'Authentication secret',
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Browser/device information',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'last_used' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Last successful notification',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('is_active');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('push_subscriptions');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de log de notificaciones push
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'subscription_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'notification_type' => [
                'type' => 'ENUM',
                'constraint' => ['price_alert', 'stock_alert', 'order_update', 'welcome', 'promotion', 'general'],
                'null' => false,
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'body' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Additional notification data',
            ],
            'success' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'error_message' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'http_code' => [
                'type' => 'INT',
                'constraint' => 3,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'created_at']);
        $this->forge->addKey('subscription_id');
        $this->forge->addKey('notification_type');
        $this->forge->addKey('success');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('push_notification_log');
        
        // Foreign keys
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }
        $this->forge->addForeignKey('subscription_id', 'push_subscriptions', 'id', 'CASCADE', 'CASCADE');

        // Tabla de configuraciones móviles por usuario
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'push_notifications_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'price_alerts_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'stock_alerts_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'order_updates_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'promotion_alerts_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'quiet_hours_enabled' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'quiet_hours_start' => [
                'type' => 'TIME',
                'null' => true,
                'default' => '22:00:00',
            ],
            'quiet_hours_end' => [
                'type' => 'TIME',
                'null' => true,
                'default' => '08:00:00',
            ],
            'preferred_language' => [
                'type' => 'VARCHAR',
                'constraint' => 5,
                'default' => 'es',
            ],
            'timezone' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => 'America/Guatemala',
            ],
            'mobile_theme' => [
                'type' => 'ENUM',
                'constraint' => ['light', 'dark', 'auto'],
                'default' => 'auto',
            ],
            'image_quality' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'auto'],
                'default' => 'auto',
                'comment' => 'Preferred image quality for mobile',
            ],
            'data_saver_mode' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Enable data saving features',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id', false, true); // Unique key
        
        $this->forge->createTable('mobile_user_preferences');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de sesiones móviles y PWA
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => false,
            ],
            'device_type' => [
                'type' => 'ENUM',
                'constraint' => ['mobile', 'tablet', 'desktop'],
                'null' => false,
            ],
            'browser_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'browser_version' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'os_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'os_version' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'screen_width' => [
                'type' => 'INT',
                'constraint' => 5,
                'null' => true,
            ],
            'screen_height' => [
                'type' => 'INT',
                'constraint' => 5,
                'null' => true,
            ],
            'is_pwa' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Accessed as PWA',
            ],
            'supports_webp' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'supports_avif' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'supports_service_worker' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'connection_type' => [
                'type' => 'ENUM',
                'constraint' => ['slow-2g', '2g', '3g', '4g', '5g', 'wifi', 'unknown'],
                'default' => 'unknown',
            ],
            'last_activity' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('session_id', false, true); // Unique
        $this->forge->addKey('user_id');
        $this->forge->addKey('device_type');
        $this->forge->addKey('is_pwa');
        $this->forge->addKey('last_activity');
        
        $this->forge->createTable('mobile_sessions');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de analytics móviles
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'date' => [
                'type' => 'DATE',
                'null' => false,
            ],
            'device_type' => [
                'type' => 'ENUM',
                'constraint' => ['mobile', 'tablet', 'desktop'],
                'null' => false,
            ],
            'total_sessions' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'unique_users' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'pwa_sessions' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'avg_session_duration' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Average session duration in seconds',
            ],
            'bounce_rate' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'default' => 0.00,
                'comment' => 'Bounce rate percentage',
            ],
            'conversion_rate' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'default' => 0.00,
                'comment' => 'Conversion rate percentage',
            ],
            'push_subscriptions' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'push_notifications_sent' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'push_click_rate' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'default' => 0.00,
                'comment' => 'Push notification click rate',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['date', 'device_type'], false, true); // Unique
        $this->forge->addKey('date');
        $this->forge->addKey('device_type');
        
        $this->forge->createTable('mobile_analytics');

        // Tabla de cache de imágenes optimizadas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'original_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
            ],
            'optimized_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
            ],
            'width' => [
                'type' => 'INT',
                'constraint' => 5,
                'null' => false,
            ],
            'height' => [
                'type' => 'INT',
                'constraint' => 5,
                'null' => false,
            ],
            'quality' => [
                'type' => 'INT',
                'constraint' => 3,
                'null' => false,
            ],
            'format' => [
                'type' => 'ENUM',
                'constraint' => ['jpg', 'jpeg', 'png', 'webp', 'avif'],
                'null' => false,
            ],
            'file_size' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'File size in bytes',
            ],
            'compression_ratio' => [
                'type' => 'DECIMAL',
                'constraint' => '5,2',
                'null' => false,
                'comment' => 'Compression ratio percentage',
            ],
            'hit_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Number of times served',
            ],
            'last_accessed' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['original_path', 'width', 'quality', 'format'], false, true); // Unique
        $this->forge->addKey('format');
        $this->forge->addKey('hit_count');
        $this->forge->addKey('last_accessed');
        
        $this->forge->createTable('optimized_images_cache');
    }

    public function down()
    {
        $this->forge->dropTable('optimized_images_cache', true);
        $this->forge->dropTable('mobile_analytics', true);
        $this->forge->dropTable('mobile_sessions', true);
        $this->forge->dropTable('mobile_user_preferences', true);
        $this->forge->dropTable('push_notification_log', true);
        $this->forge->dropTable('push_subscriptions', true);
    }
}
