<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
        }
        
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
        }
        
        .login-left {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 60px 40px;
            text-align: center;
        }
        
        .login-left h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .login-left p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .social-login {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #495057;
        }
        
        .social-login:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 20px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="login-left">
                        <i class="fas fa-mobile-alt fa-4x mb-4"></i>
                        <h2>MrCell</h2>
                        <p>Tu tienda de confianza para celulares y tecnología en Guatemala</p>
                        <div class="mt-5">
                            <h5>¿Por qué crear una cuenta?</h5>
                            <ul class="list-unstyled mt-3">
                                <li><i class="fas fa-check me-2"></i>Compras más rápidas</li>
                                <li><i class="fas fa-check me-2"></i>Historial de pedidos</li>
                                <li><i class="fas fa-check me-2"></i>Ofertas exclusivas</li>
                                <li><i class="fas fa-check me-2"></i>Lista de deseos</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="login-right">
                        <h3 class="mb-4">Iniciar Sesión</h3>
                        
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (session()->getFlashdata('success')): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= session()->getFlashdata('success') ?>
                            </div>
                        <?php endif; ?>
                        
                        <form action="<?= base_url('login') ?>" method="POST" id="login-form">
                            <?= csrf_field() ?>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required value="<?= old('email') ?>">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Contraseña</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Recordar sesión
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mb-3">
                            <a href="<?= base_url('forgot-password') ?>" class="text-decoration-none">
                                ¿Olvidaste tu contraseña?
                            </a>
                        </div>
                        
                        <div class="divider">
                            <span>o continúa con</span>
                        </div>
                        
                        <div class="row g-2 mb-4">
                            <div class="col-6">
                                <a href="#" class="btn social-login w-100">
                                    <i class="fab fa-google me-2"></i>Google
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="#" class="btn social-login w-100">
                                    <i class="fab fa-facebook me-2"></i>Facebook
                                </a>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <p class="mb-0">¿No tienes cuenta? 
                                <a href="<?= base_url('register') ?>" class="text-decoration-none fw-bold">
                                    Regístrate aquí
                                </a>
                            </p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="<?= base_url() ?>" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-left me-2"></i>Volver a la tienda
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Formulario ahora se envía normalmente al servidor
        // Sin interceptar con JavaScript
    </script>
</body>
</html>
