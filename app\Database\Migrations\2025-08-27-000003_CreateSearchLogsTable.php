<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tabla de logs de búsqueda
 * Almacena estadísticas y analytics de búsquedas
 */
class CreateSearchLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'query' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
            ],
            'result_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'filters' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'execution_time' => [
                'type' => 'DECIMAL',
                'constraint' => '8,3',
                'null' => true,
                'comment' => 'Tiempo de ejecución en segundos',
            ],
            'search_type' => [
                'type' => 'ENUM',
                'constraint' => ['text', 'voice', 'visual', 'filter'],
                'default' => 'text',
            ],
            'device_type' => [
                'type' => 'ENUM',
                'constraint' => ['desktop', 'mobile', 'tablet'],
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['query', 'created_at']);
        $this->forge->addKey('user_id');
        $this->forge->addKey('created_at');
        $this->forge->addKey('result_count');
        $this->forge->addKey('search_type');
        
        // Índice FULLTEXT para búsquedas en queries
        $this->forge->addKey('query', false, false, 'FULLTEXT');
        
        $this->forge->createTable('search_logs');
        
        // Agregar foreign key si existe tabla users
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        }
    }

    public function down()
    {
        $this->forge->dropTable('search_logs');
    }
}
