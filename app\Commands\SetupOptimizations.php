<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\SearchOptimizer;
use App\Libraries\SimpleCache;

/**
 * Comando para configurar optimizaciones iniciales
 * Compatible con cPanel hosting
 * 
 * Uso: php spark setup:optimizations
 */
class SetupOptimizations extends BaseCommand
{
    protected $group       = 'MrCell';
    protected $name        = 'setup:optimizations';
    protected $description = 'Configura optimizaciones iniciales para el proyecto MrCell';
    protected $usage       = 'setup:optimizations [options]';
    protected $arguments   = [];
    protected $options     = [
        '--force' => 'Forzar recreación de índices existentes',
        '--skip-indexes' => 'Saltar creación de índices de base de datos',
        '--skip-cache' => 'Saltar configuración de cache'
    ];

    public function run(array $params)
    {
        CLI::write('🚀 Configurando optimizaciones para MrCell Guatemala', 'green');
        CLI::newLine();
        
        $force = CLI::getOption('force');
        $skipIndexes = CLI::getOption('skip-indexes');
        $skipCache = CLI::getOption('skip-cache');
        
        // 1. Configurar directorios de cache
        if (!$skipCache) {
            $this->setupCacheDirectories();
        }
        
        // 2. Configurar índices de base de datos
        if (!$skipIndexes) {
            $this->setupDatabaseIndexes($force);
        }
        
        // 3. Crear archivos de configuración
        $this->createConfigFiles();
        
        // 4. Optimizar .htaccess
        $this->optimizeHtaccess();
        
        // 5. Crear cron jobs recomendados
        $this->showCronJobsRecommendations();
        
        CLI::newLine();
        CLI::write('✅ Configuración completada exitosamente!', 'green');
        CLI::write('📋 Revisa las recomendaciones mostradas arriba', 'yellow');
    }
    
    /**
     * Configurar directorios de cache
     */
    private function setupCacheDirectories()
    {
        CLI::write('📁 Configurando directorios de cache...', 'yellow');
        
        $directories = [
            WRITEPATH . 'cache/simple/',
            WRITEPATH . 'cache/products/',
            WRITEPATH . 'cache/searches/',
            WRITEPATH . 'cache/images/',
            WRITEPATH . 'cache/minified/'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    CLI::write("  ✅ Creado: {$dir}", 'green');
                } else {
                    CLI::write("  ❌ Error creando: {$dir}", 'red');
                }
            } else {
                CLI::write("  ℹ️  Ya existe: {$dir}", 'blue');
            }
            
            // Crear archivo .htaccess para proteger cache
            $htaccessFile = $dir . '.htaccess';
            if (!file_exists($htaccessFile)) {
                file_put_contents($htaccessFile, "Deny from all\n");
                CLI::write("  🔒 Protección agregada: {$htaccessFile}", 'green');
            }
        }
        
        CLI::newLine();
    }
    
    /**
     * Configurar índices de base de datos
     */
    private function setupDatabaseIndexes($force = false)
    {
        CLI::write('🗄️  Configurando índices de base de datos...', 'yellow');
        
        try {
            $searchOptimizer = new SearchOptimizer();
            $results = $searchOptimizer->setupFullTextIndexes();
            
            foreach ($results as $index => $status) {
                if ($status === 'Creado exitosamente') {
                    CLI::write("  ✅ {$index}: {$status}", 'green');
                } elseif ($status === 'Ya existe') {
                    CLI::write("  ℹ️  {$index}: {$status}", 'blue');
                } else {
                    CLI::write("  ❌ {$index}: {$status}", 'red');
                }
            }
            
            // Optimizar tablas
            CLI::write('  🔧 Optimizando tablas...', 'yellow');
            $db = \Config\Database::connect();
            
            $tables = ['products', 'categories', 'brands', 'users', 'orders'];
            foreach ($tables as $table) {
                $db->query("OPTIMIZE TABLE {$table}");
                CLI::write("    ✅ Optimizada: {$table}", 'green');
            }
            
        } catch (\Exception $e) {
            CLI::write("  ❌ Error: " . $e->getMessage(), 'red');
        }
        
        CLI::newLine();
    }
    
    /**
     * Crear archivos de configuración
     */
    private function createConfigFiles()
    {
        CLI::write('⚙️  Creando archivos de configuración...', 'yellow');
        
        // Crear configuración de cache
        $cacheConfig = "<?php\n\n";
        $cacheConfig .= "// Configuración de Cache Simple para MrCell\n";
        $cacheConfig .= "// Generado automáticamente el " . date('Y-m-d H:i:s') . "\n\n";
        $cacheConfig .= "return [\n";
        $cacheConfig .= "    'default_ttl' => 3600, // 1 hora\n";
        $cacheConfig .= "    'products_ttl' => 1800, // 30 minutos\n";
        $cacheConfig .= "    'searches_ttl' => 900, // 15 minutos\n";
        $cacheConfig .= "    'categories_ttl' => 7200, // 2 horas\n";
        $cacheConfig .= "    'auto_cleanup' => true,\n";
        $cacheConfig .= "    'cleanup_probability' => 100, // 1% de probabilidad por request\n";
        $cacheConfig .= "];\n";
        
        $cacheConfigFile = APPPATH . 'Config/SimpleCache.php';
        if (!file_exists($cacheConfigFile)) {
            file_put_contents($cacheConfigFile, $cacheConfig);
            CLI::write("  ✅ Creado: Config/SimpleCache.php", 'green');
        } else {
            CLI::write("  ℹ️  Ya existe: Config/SimpleCache.php", 'blue');
        }
        
        // Crear configuración de búsqueda
        $searchConfig = "<?php\n\n";
        $searchConfig .= "// Configuración de Búsqueda Optimizada para MrCell\n";
        $searchConfig .= "// Generado automáticamente el " . date('Y-m-d H:i:s') . "\n\n";
        $searchConfig .= "return [\n";
        $searchConfig .= "    'min_search_length' => 2,\n";
        $searchConfig .= "    'max_suggestions' => 10,\n";
        $searchConfig .= "    'default_limit' => 20,\n";
        $searchConfig .= "    'max_limit' => 50,\n";
        $searchConfig .= "    'cache_ttl' => 900, // 15 minutos\n";
        $searchConfig .= "    'enable_fuzzy_search' => true,\n";
        $searchConfig .= "    'boost_featured' => true,\n";
        $searchConfig .= "];\n";
        
        $searchConfigFile = APPPATH . 'Config/SearchOptimized.php';
        if (!file_exists($searchConfigFile)) {
            file_put_contents($searchConfigFile, $searchConfig);
            CLI::write("  ✅ Creado: Config/SearchOptimized.php", 'green');
        } else {
            CLI::write("  ℹ️  Ya existe: Config/SearchOptimized.php", 'blue');
        }
        
        CLI::newLine();
    }
    
    /**
     * Optimizar archivo .htaccess
     */
    private function optimizeHtaccess()
    {
        CLI::write('🔧 Optimizando .htaccess...', 'yellow');
        
        $htaccessFile = FCPATH . '.htaccess';
        $optimizations = "\n# Optimizaciones MrCell - Generado automáticamente\n";
        $optimizations .= "# Fecha: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Compresión GZIP
        $optimizations .= "# Compresión GZIP\n";
        $optimizations .= "<IfModule mod_deflate.c>\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/plain\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/html\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE text/css\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/xhtml+xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/rss+xml\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/javascript\n";
        $optimizations .= "    AddOutputFilterByType DEFLATE application/x-javascript\n";
        $optimizations .= "</IfModule>\n\n";
        
        // Cache de archivos estáticos
        $optimizations .= "# Cache de archivos estáticos\n";
        $optimizations .= "<IfModule mod_expires.c>\n";
        $optimizations .= "    ExpiresActive On\n";
        $optimizations .= "    ExpiresByType image/jpg \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType image/jpeg \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType image/gif \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType image/png \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType text/css \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/pdf \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/javascript \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/x-javascript \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType application/x-shockwave-flash \"access plus 1 month\"\n";
        $optimizations .= "    ExpiresByType image/x-icon \"access plus 1 year\"\n";
        $optimizations .= "    ExpiresDefault \"access plus 2 days\"\n";
        $optimizations .= "</IfModule>\n\n";
        
        // Headers de cache
        $optimizations .= "# Headers de cache\n";
        $optimizations .= "<IfModule mod_headers.c>\n";
        $optimizations .= "    <FilesMatch \"\\.(ico|pdf|flv|jpg|jpeg|png|gif|js|css|swf)$\">\n";
        $optimizations .= "        Header set Cache-Control \"max-age=2592000, public\"\n";
        $optimizations .= "    </FilesMatch>\n";
        $optimizations .= "</IfModule>\n\n";
        
        if (file_exists($htaccessFile)) {
            $currentContent = file_get_contents($htaccessFile);
            
            // Verificar si ya tiene optimizaciones
            if (strpos($currentContent, '# Optimizaciones MrCell') === false) {
                file_put_contents($htaccessFile, $currentContent . $optimizations);
                CLI::write("  ✅ Optimizaciones agregadas a .htaccess", 'green');
            } else {
                CLI::write("  ℹ️  .htaccess ya tiene optimizaciones", 'blue');
            }
        } else {
            CLI::write("  ❌ Archivo .htaccess no encontrado", 'red');
        }
        
        CLI::newLine();
    }
    
    /**
     * Mostrar recomendaciones de cron jobs
     */
    private function showCronJobsRecommendations()
    {
        CLI::write('⏰ Cron Jobs recomendados para cPanel:', 'yellow');
        CLI::newLine();
        
        $cronJobs = [
            [
                'frequency' => 'Cada hora',
                'command' => 'php /home/<USER>/public_html/spark cache:cleanup',
                'description' => 'Limpiar cache expirado'
            ],
            [
                'frequency' => 'Diario a las 2:00 AM',
                'command' => 'php /home/<USER>/public_html/spark db:optimize',
                'description' => 'Optimizar tablas de base de datos'
            ],
            [
                'frequency' => 'Cada 6 horas',
                'command' => 'php /home/<USER>/public_html/spark search:reindex',
                'description' => 'Reindexar búsquedas populares'
            ]
        ];
        
        foreach ($cronJobs as $job) {
            CLI::write("📅 {$job['frequency']}", 'blue');
            CLI::write("   Comando: {$job['command']}", 'white');
            CLI::write("   Descripción: {$job['description']}", 'light_gray');
            CLI::newLine();
        }
        
        CLI::write('💡 Para configurar estos cron jobs:', 'yellow');
        CLI::write('   1. Accede a tu cPanel', 'white');
        CLI::write('   2. Busca "Cron Jobs" o "Tareas Cron"', 'white');
        CLI::write('   3. Agrega los comandos con la frecuencia indicada', 'white');
        CLI::write('   4. Ajusta la ruta según tu configuración', 'white');
        CLI::newLine();
    }
}
