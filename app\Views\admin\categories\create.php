<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-plus me-2"></i>Nueva Categoría</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/admin/categories">Categorías</a></li>
                <li class="breadcrumb-item active">Nueva Categoría</li>
            </ol>
        </nav>
    </div>
    <div class="mt-3">
        <a href="/admin/categories" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Crear Nueva Categoría</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/categories/create" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre de la Categoría *</label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       value="<?= old('name') ?>" placeholder="Ej: Smartphones">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Categoría Padre</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">Sin categoría padre</option>
                                    <?php if (!empty($parentCategories)): ?>
                                        <?php foreach ($parentCategories as $parent): ?>
                                            <option value="<?= $parent['id'] ?>" <?= old('parent_id') == $parent['id'] ? 'selected' : '' ?>>
                                                <?= esc($parent['display_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Descripción de la categoría"><?= old('description') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">Imagen de la Categoría</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Formatos permitidos: JPG, PNG, GIF. Máximo 2MB.</div>

                        <!-- Vista previa de imagen -->
                        <div id="image_preview" class="mt-3" style="display: none;">
                            <label class="form-label">Vista Previa</label>
                            <div class="position-relative d-inline-block">
                                <img id="preview_img" src="" alt="Vista previa"
                                     class="img-fluid rounded shadow-sm"
                                     style="max-height: 200px; cursor: pointer;">
                                <button type="button" class="btn btn-danger btn-sm position-absolute"
                                        onclick="removeImagePreview()"
                                        style="top: 5px; right: 5px; border-radius: 50%; width: 30px; height: 30px; padding: 0;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Orden de Clasificación</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?= old('sort_order', 0) ?>" min="0" placeholder="0">
                                <div class="form-text">Número para ordenar las categorías (menor número = mayor prioridad)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" <?= old('is_active', '1') ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        Categoría activa
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="window.location.href='/admin/categories'">
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Categoría
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h5>
            </div>
            <div class="card-body">
                <h6>Consejos para crear categorías:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Usa nombres descriptivos y claros</li>
                    <li><i class="fas fa-check text-success me-2"></i>Evita crear demasiadas subcategorías</li>
                    <li><i class="fas fa-check text-success me-2"></i>El orden de clasificación determina la posición en listados</li>
                    <li><i class="fas fa-check text-success me-2"></i>Las categorías inactivas no aparecen en la tienda</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Vista previa de imagen
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validar tamaño (2MB máximo)
        if (file.size > 2 * 1024 * 1024) {
            alert('La imagen es demasiado grande. Máximo 2MB.');
            this.value = '';
            return;
        }

        // Validar tipo
        if (!file.type.match('image.*')) {
            alert('Por favor selecciona un archivo de imagen válido.');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview_img').src = e.target.result;
            document.getElementById('image_preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

function removeImagePreview() {
    document.getElementById('image').value = '';
    document.getElementById('image_preview').style.display = 'none';
}
</script>
<?= $this->endSection() ?>
