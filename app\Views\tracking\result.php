<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <!-- Header -->
    <div class="text-center mb-4">
        <h1 class="h2 text-primary mb-3">
            <i class="fas fa-map-marker-alt me-2"></i>Seguimiento de Envío
        </h1>
        <p class="lead text-muted">Número de seguimiento: <strong><?= esc($tracking_number) ?></strong></p>
    </div>

    <!-- Información del Envío -->
    <?php if ($tracking_info['success']): ?>
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-0">
                                    <i class="fas fa-package me-2"></i>
                                    <?= esc($tracking_info['shipment']['description'] ?? 'Paquete MrCell') ?>
                                </h5>
                                <small class="opacity-75">
                                    Empresa: <?= esc($tracking_info['shipment']['company_name'] ?? 'N/A') ?>
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php
                                $status = $tracking_info['shipment']['status'] ?? 'unknown';
                                $statusClass = 'bg-secondary';
                                $statusIcon = 'fa-question';
                                $statusText = 'Desconocido';
                                
                                switch(strtolower($status)) {
                                    case 'pending':
                                        $statusClass = 'bg-warning';
                                        $statusIcon = 'fa-clock';
                                        $statusText = 'Pendiente';
                                        break;
                                    case 'processing':
                                        $statusClass = 'bg-info';
                                        $statusIcon = 'fa-cog';
                                        $statusText = 'Procesando';
                                        break;
                                    case 'shipped':
                                    case 'in_transit':
                                        $statusClass = 'bg-primary';
                                        $statusIcon = 'fa-truck';
                                        $statusText = 'En Tránsito';
                                        break;
                                    case 'out_for_delivery':
                                        $statusClass = 'bg-warning';
                                        $statusIcon = 'fa-shipping-fast';
                                        $statusText = 'En Reparto';
                                        break;
                                    case 'delivered':
                                        $statusClass = 'bg-success';
                                        $statusIcon = 'fa-check-circle';
                                        $statusText = 'Entregado';
                                        break;
                                    case 'failed':
                                    case 'returned':
                                        $statusClass = 'bg-danger';
                                        $statusIcon = 'fa-exclamation-triangle';
                                        $statusText = 'Problema';
                                        break;
                                }
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6 px-3 py-2">
                                    <i class="fas <?= $statusIcon ?> me-2"></i><?= $statusText ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Información General -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Información del Envío
                                </h6>
                                <div class="info-item mb-2">
                                    <strong>Origen:</strong> 
                                    <?= esc($tracking_info['shipment']['origin'] ?? 'Guatemala, Guatemala') ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>Destino:</strong> 
                                    <?= esc($tracking_info['shipment']['destination'] ?? 'N/A') ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>Peso:</strong> 
                                    <?= esc($tracking_info['shipment']['weight'] ?? 'N/A') ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>Servicio:</strong> 
                                    <?= esc($tracking_info['shipment']['service_type'] ?? 'Estándar') ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-calendar me-2"></i>Fechas Importantes
                                </h6>
                                <div class="info-item mb-2">
                                    <strong>Fecha de Envío:</strong> 
                                    <?= $tracking_info['shipment']['shipped_at'] ? date('d/m/Y H:i', strtotime($tracking_info['shipment']['shipped_at'])) : 'N/A' ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>Entrega Estimada:</strong> 
                                    <?= $tracking_info['shipment']['estimated_delivery'] ? date('d/m/Y', strtotime($tracking_info['shipment']['estimated_delivery'])) : 'N/A' ?>
                                </div>
                                <?php if ($tracking_info['shipment']['delivered_at']): ?>
                                    <div class="info-item mb-2">
                                        <strong class="text-success">Fecha de Entrega:</strong> 
                                        <span class="text-success">
                                            <?= date('d/m/Y H:i', strtotime($tracking_info['shipment']['delivered_at'])) ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Progreso Visual -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-route me-2"></i>Progreso del Envío
                            </h6>
                            <div class="progress-container">
                                <?php
                                $steps = [
                                    'pending' => 'Pendiente',
                                    'processing' => 'Procesando',
                                    'shipped' => 'Enviado',
                                    'out_for_delivery' => 'En Reparto',
                                    'delivered' => 'Entregado'
                                ];
                                
                                $currentStatus = strtolower($tracking_info['shipment']['status'] ?? 'pending');
                                $statusKeys = array_keys($steps);
                                $currentIndex = array_search($currentStatus, $statusKeys);
                                if ($currentIndex === false) $currentIndex = 0;
                                ?>
                                
                                <div class="progress-steps">
                                    <?php foreach ($steps as $key => $label): ?>
                                        <?php
                                        $stepIndex = array_search($key, $statusKeys);
                                        $isActive = $stepIndex <= $currentIndex;
                                        $isCurrent = $stepIndex === $currentIndex;
                                        ?>
                                        <div class="progress-step <?= $isActive ? 'active' : '' ?> <?= $isCurrent ? 'current' : '' ?>">
                                            <div class="step-circle">
                                                <?php if ($isActive): ?>
                                                    <i class="fas fa-check"></i>
                                                <?php else: ?>
                                                    <span><?= $stepIndex + 1 ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="step-label"><?= $label ?></div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Historial de Eventos -->
                        <?php if (!empty($tracking_info['events'])): ?>
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-history me-2"></i>Historial de Eventos
                                </h6>
                                <div class="timeline">
                                    <?php foreach ($tracking_info['events'] as $event): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-marker">
                                                <i class="fas fa-circle"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <div class="timeline-header">
                                                    <h6 class="mb-1"><?= esc($event['description']) ?></h6>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y H:i', strtotime($event['created_at'])) ?>
                                                        <?php if (!empty($event['location'])): ?>
                                                            - <?= esc($event['location']) ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <?php if (!empty($event['notes'])): ?>
                                                    <p class="text-muted small mb-0"><?= esc($event['notes']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Mapa (si está disponible) -->
                        <?php if (!empty($tracking_info['shipment']['current_location'])): ?>
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-map me-2"></i>Ubicación Actual
                                </h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    <strong>Última ubicación conocida:</strong> 
                                    <?= esc($tracking_info['shipment']['current_location']) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones -->
        <div class="row mb-4">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center py-4">
                        <h6 class="mb-3">¿Necesitas ayuda con tu envío?</h6>
                        <div class="row justify-content-center">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-primary w-100" onclick="refreshTracking()">
                                    <i class="fas fa-sync me-2"></i>Actualizar
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-info w-100" onclick="subscribeNotifications()">
                                    <i class="fas fa-bell me-2"></i>Notificaciones
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-warning w-100" onclick="reportIssue()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Reportar Problema
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-success w-100" onclick="shareTracking()">
                                    <i class="fas fa-share me-2"></i>Compartir
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <!-- Error en el seguimiento -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-danger">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-4x text-danger mb-4"></i>
                        <h3 class="text-danger mb-3">No se pudo encontrar el envío</h3>
                        <p class="text-muted mb-4">
                            <?= esc($tracking_info['error'] ?? 'El número de seguimiento no existe o aún no está disponible en el sistema.') ?>
                        </p>
                        
                        <div class="mb-4">
                            <h6>Posibles causas:</h6>
                            <ul class="list-unstyled text-muted">
                                <li><i class="fas fa-circle fa-xs me-2"></i>El número de seguimiento es incorrecto</li>
                                <li><i class="fas fa-circle fa-xs me-2"></i>El paquete aún no ha sido procesado</li>
                                <li><i class="fas fa-circle fa-xs me-2"></i>Puede tomar hasta 24 horas en aparecer</li>
                            </ul>
                        </div>
                        
                        <div class="row justify-content-center">
                            <div class="col-md-4 mb-2">
                                <a href="<?= base_url('tracking') ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Buscar Nuevamente
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="<?= base_url('contact') ?>" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-headset me-2"></i>Contactar Soporte
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Rastrear Otro Paquete -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-4">
                    <h5 class="mb-3">¿Quieres rastrear otro paquete?</h5>
                    <a href="<?= base_url('tracking') ?>" class="btn btn-light">
                        <i class="fas fa-search me-2"></i>Nueva Búsqueda
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.info-item {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin: 2rem 0;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    border: 3px solid white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-step.active .step-circle {
    background: #28a745;
    color: white;
}

.progress-step.current .step-circle {
    background: #007bff;
    color: white;
    animation: pulse 2s infinite;
}

.step-label {
    font-size: 0.875rem;
    text-align: center;
    color: #6c757d;
    font-weight: 500;
}

.progress-step.active .step-label {
    color: #28a745;
    font-weight: 600;
}

.progress-step.current .step-label {
    color: #007bff;
    font-weight: 600;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.5rem;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #007bff;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

@media (max-width: 768px) {
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .progress-steps::before {
        display: none;
    }
    
    .step-circle {
        margin-bottom: 0.25rem;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Actualizar seguimiento
function refreshTracking() {
    location.reload();
}

// Suscribirse a notificaciones
function subscribeNotifications() {
    const trackingNumber = '<?= esc($tracking_number) ?>';
    
    // Mostrar modal o formulario para suscripción
    const email = prompt('Ingresa tu email para recibir notificaciones de este envío:');
    
    if (email) {
        fetch('<?= base_url('tracking/subscribe-notifications') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                tracking_number: trackingNumber,
                email: email
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('¡Te notificaremos sobre cualquier actualización de tu envío!');
            } else {
                alert('Error: ' + (data.error || 'No se pudo suscribir a las notificaciones'));
            }
        })
        .catch(error => {
            alert('Error de conexión');
        });
    }
}

// Reportar problema
function reportIssue() {
    const trackingNumber = '<?= esc($tracking_number) ?>';
    
    const issue = prompt('Describe el problema con tu envío:');
    
    if (issue) {
        fetch('<?= base_url('tracking/report-issue') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                tracking_number: trackingNumber,
                issue: issue
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Tu reporte ha sido enviado. Nos pondremos en contacto contigo pronto.');
            } else {
                alert('Error: ' + (data.error || 'No se pudo enviar el reporte'));
            }
        })
        .catch(error => {
            alert('Error de conexión');
        });
    }
}

// Compartir seguimiento
function shareTracking() {
    const trackingNumber = '<?= esc($tracking_number) ?>';
    const url = '<?= base_url('tracking/result/' . $tracking_number) ?>';
    
    if (navigator.share) {
        navigator.share({
            title: 'Seguimiento de Envío - MrCell',
            text: `Seguimiento del paquete: ${trackingNumber}`,
            url: url
        });
    } else {
        // Fallback: copiar al portapapeles
        navigator.clipboard.writeText(url).then(function() {
            alert('Enlace de seguimiento copiado al portapapeles');
        });
    }
}

// Auto-actualizar cada 5 minutos si el envío está en tránsito
<?php if ($tracking_info['success'] && in_array(strtolower($tracking_info['shipment']['status'] ?? ''), ['processing', 'shipped', 'in_transit', 'out_for_delivery'])): ?>
setInterval(function() {
    // Actualizar silenciosamente en segundo plano
    fetch(window.location.href)
        .then(response => response.text())
        .then(html => {
            // Aquí podrías actualizar solo ciertas partes de la página
            console.log('Seguimiento actualizado automáticamente');
        })
        .catch(error => {
            console.log('Error al actualizar automáticamente');
        });
}, 300000); // 5 minutos
<?php endif; ?>
</script>
<?= $this->endSection() ?>
