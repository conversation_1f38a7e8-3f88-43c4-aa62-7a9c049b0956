-- =====================================================
-- STORED PROCEDURES ACTUALIZADOS PARA MÚLTIPLES MONEDAS
-- =====================================================

DELIMITER $$

-- =====================================================
-- SP: Crear producto con soporte para múltiples monedas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_create_product$$
CREATE PROCEDURE sp_admin_create_product(
    IN p_name VARCHAR(200),
    IN p_sku VARCHAR(50),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_weight DECIMAL(8,2),
    IN p_dimensions VARCHAR(100),
    IN p_featured_image VARCHAR(255),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_featured BOOLEAN,
    IN p_currency ENUM('GTQ', 'USD'),
    OUT p_product_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_uuid VARCHAR(36);
    DECLARE v_slug VARCHAR(220);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(220);
    DECLARE v_currency ENUM('GTQ', 'USD') DEFAULT COALESCE(p_currency, 'GTQ');
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
        SET p_product_id = 0;
    END;

    START TRANSACTION;

    -- Verificar que el SKU no exista
    IF EXISTS (SELECT 1 FROM products WHERE sku = p_sku AND deleted_at IS NULL) THEN
        SET p_result = 'SKU_EXISTS';
        SET p_product_id = 0;
        ROLLBACK;
    ELSE
        -- Generar UUID
        SET v_uuid = UUID();

        -- Generar slug único basado en el nombre
        SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
        SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
        SET v_slug = v_base_slug;

        -- Verificar que el slug sea único
        WHILE EXISTS (SELECT 1 FROM products WHERE slug = v_slug AND deleted_at IS NULL) DO
            SET v_counter = v_counter + 1;
            SET v_slug = CONCAT(v_base_slug, '-', v_counter);
        END WHILE;

        -- Crear el producto
        INSERT INTO products (
            uuid, sku, name, slug, description, short_description, category_id, brand_id,
            price_regular, price_sale, stock_quantity, stock_min, weight, dimensions,
            featured_image, gallery_images, is_active, is_featured, currency, created_at, updated_at
        ) VALUES (
            v_uuid, p_sku, p_name, v_slug, p_description, p_short_description, p_category_id, p_brand_id,
            p_price_regular, p_price_sale, p_stock_quantity, p_stock_min, p_weight, p_dimensions,
            p_featured_image, p_gallery_images, p_is_active, p_is_featured, v_currency, NOW(), NOW()
        );

        SET p_product_id = LAST_INSERT_ID();
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Actualizar producto con soporte para múltiples monedas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_admin_update_product$$
CREATE PROCEDURE sp_admin_update_product(
    IN p_product_id INT,
    IN p_name VARCHAR(200),
    IN p_sku VARCHAR(50),
    IN p_description TEXT,
    IN p_short_description VARCHAR(500),
    IN p_category_id INT,
    IN p_brand_id INT,
    IN p_price_regular DECIMAL(10,2),
    IN p_price_sale DECIMAL(10,2),
    IN p_stock_quantity INT,
    IN p_stock_min INT,
    IN p_weight DECIMAL(8,2),
    IN p_dimensions VARCHAR(100),
    IN p_dimension_length DECIMAL(8,2),
    IN p_dimension_width DECIMAL(8,2),
    IN p_dimension_height DECIMAL(8,2),
    IN p_dimension_unit VARCHAR(10),
    IN p_featured_image VARCHAR(255),
    IN p_gallery_images LONGTEXT,
    IN p_is_active BOOLEAN,
    IN p_is_featured BOOLEAN,
    IN p_currency ENUM('GTQ', 'USD'),
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_slug VARCHAR(220);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_base_slug VARCHAR(220);
    DECLARE v_current_slug VARCHAR(220);
    DECLARE v_currency ENUM('GTQ', 'USD') DEFAULT COALESCE(p_currency, 'GTQ');
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;

    -- Verificar que el producto existe
    IF NOT EXISTS (SELECT 1 FROM products WHERE id = p_product_id AND deleted_at IS NULL) THEN
        SET p_result = 'PRODUCT_NOT_FOUND';
        ROLLBACK;
    -- Verificar que el SKU no exista en otro producto
    ELSEIF EXISTS (SELECT 1 FROM products WHERE sku = p_sku AND id != p_product_id AND deleted_at IS NULL) THEN
        SET p_result = 'SKU_EXISTS';
        ROLLBACK;
    ELSE
        -- Obtener slug actual
        SELECT slug INTO v_current_slug FROM products WHERE id = p_product_id;

        -- Generar nuevo slug si el nombre cambió
        SET v_base_slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p_name, ' ', '-'), 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'));
        SET v_base_slug = REPLACE(REPLACE(REPLACE(v_base_slug, 'ú', 'u'), 'ñ', 'n'), 'ü', 'u');
        SET v_slug = v_base_slug;

        -- Verificar que el slug sea único (excepto para el producto actual)
        WHILE EXISTS (SELECT 1 FROM products WHERE slug = v_slug AND id != p_product_id AND deleted_at IS NULL) DO
            SET v_counter = v_counter + 1;
            SET v_slug = CONCAT(v_base_slug, '-', v_counter);
        END WHILE;

        -- Actualizar el producto
        UPDATE products SET
            name = p_name,
            sku = p_sku,
            slug = v_slug,
            description = p_description,
            short_description = p_short_description,
            category_id = p_category_id,
            brand_id = p_brand_id,
            price_regular = p_price_regular,
            price_sale = p_price_sale,
            stock_quantity = p_stock_quantity,
            stock_min = p_stock_min,
            weight = p_weight,
            dimensions = p_dimensions,
            dimension_length = p_dimension_length,
            dimension_width = p_dimension_width,
            dimension_height = p_dimension_height,
            dimension_unit = p_dimension_unit,
            featured_image = p_featured_image,
            gallery_images = p_gallery_images,
            is_active = p_is_active,
            is_featured = p_is_featured,
            currency = v_currency,
            updated_at = NOW()
        WHERE id = p_product_id;

        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END$$

-- =====================================================
-- SP: Función para convertir precios entre monedas
-- =====================================================
DROP FUNCTION IF EXISTS fn_convert_currency$$
CREATE FUNCTION fn_convert_currency(
    p_amount DECIMAL(10,2),
    p_from_currency ENUM('GTQ', 'USD'),
    p_to_currency ENUM('GTQ', 'USD')
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_exchange_rate DECIMAL(10,4) DEFAULT 7.75;
    DECLARE v_result DECIMAL(10,2);
    
    -- Obtener tipo de cambio actual
    SELECT CAST(setting_value AS DECIMAL(10,4)) INTO v_exchange_rate
    FROM system_settings 
    WHERE setting_key = 'exchange_rate_usd_to_gtq' 
    AND is_active = 1
    LIMIT 1;
    
    -- Si no hay tipo de cambio configurado, usar valor por defecto
    IF v_exchange_rate IS NULL OR v_exchange_rate <= 0 THEN
        SET v_exchange_rate = 7.75;
    END IF;
    
    -- Realizar conversión
    IF p_from_currency = p_to_currency THEN
        SET v_result = p_amount;
    ELSEIF p_from_currency = 'USD' AND p_to_currency = 'GTQ' THEN
        SET v_result = p_amount * v_exchange_rate;
    ELSEIF p_from_currency = 'GTQ' AND p_to_currency = 'USD' THEN
        SET v_result = p_amount / v_exchange_rate;
    ELSE
        SET v_result = p_amount;
    END IF;
    
    RETURN ROUND(v_result, 2);
END$$

-- =====================================================
-- SP: Obtener catálogo público con conversión de monedas
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_public_product_catalog$$
CREATE PROCEDURE sp_get_public_product_catalog(
    IN p_category_id INT,
    IN p_search_term VARCHAR(255),
    IN p_min_price DECIMAL(10,2),
    IN p_max_price DECIMAL(10,2),
    IN p_sort_by ENUM('name', 'price_asc', 'price_desc', 'newest', 'popular'),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    DECLARE v_sort_clause VARCHAR(100) DEFAULT 'p.created_at DESC';
    DECLARE v_limit INT DEFAULT COALESCE(p_limit, 12);
    DECLARE v_offset INT DEFAULT COALESCE(p_offset, 0);

    -- Configurar ordenamiento
    CASE p_sort_by
        WHEN 'name' THEN SET v_sort_clause = 'p.name ASC';
        WHEN 'price_asc' THEN SET v_sort_clause = 'final_price ASC';
        WHEN 'price_desc' THEN SET v_sort_clause = 'final_price DESC';
        WHEN 'newest' THEN SET v_sort_clause = 'p.created_at DESC';
        WHEN 'popular' THEN SET v_sort_clause = 'p.is_featured DESC, p.created_at DESC';
        ELSE SET v_sort_clause = 'p.created_at DESC';
    END CASE;

    SELECT
        p.id,
        p.uuid,
        p.name,
        p.slug,
        p.description,
        p.short_description,
        p.sku,
        p.price_regular,
        p.price_sale,
        p.currency,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale
            ELSE p.price_regular
        END as final_price,
        -- Conversión de precios
        CASE
            WHEN p.currency = 'USD' THEN fn_convert_currency(p.price_regular, 'USD', 'GTQ')
            ELSE p.price_regular
        END as price_regular_gtq,
        CASE
            WHEN p.currency = 'USD' AND p.price_sale IS NOT NULL AND p.price_sale > 0 THEN fn_convert_currency(p.price_sale, 'USD', 'GTQ')
            WHEN p.currency = 'GTQ' THEN p.price_sale
            ELSE NULL
        END as price_sale_gtq,
        CASE
            WHEN p.currency = 'GTQ' THEN fn_convert_currency(p.price_regular, 'GTQ', 'USD')
            ELSE p.price_regular
        END as price_regular_usd,
        CASE
            WHEN p.currency = 'GTQ' AND p.price_sale IS NOT NULL AND p.price_sale > 0 THEN fn_convert_currency(p.price_sale, 'GTQ', 'USD')
            WHEN p.currency = 'USD' THEN p.price_sale
            ELSE NULL
        END as price_sale_usd,
        CASE
            WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN
                ROUND(((p.price_regular - p.price_sale) / p.price_regular) * 100, 0)
            ELSE 0
        END as discount_percentage,
        p.stock_quantity,
        p.weight,
        p.dimensions,
        p.is_featured,
        0 as views_count,
        0.0 as rating_average,
        0 as rating_count,
        p.created_at,
        c.name as category_name,
        c.slug as category_slug,
        p.featured_image,
        1 as images_count,
        CASE
            WHEN p.stock_quantity > 0 THEN 'in_stock'
            ELSE 'out_of_stock'
        END as stock_status
    FROM products p
    LEFT JOIN categories c ON c.id = p.category_id AND c.deleted_at IS NULL
    WHERE p.is_active = 1
    AND p.deleted_at IS NULL
    AND (p_category_id IS NULL OR p.category_id = p_category_id)
    AND (p_search_term IS NULL OR p_search_term = '' OR
         p.name LIKE CONCAT('%', p_search_term, '%') OR
         p.description LIKE CONCAT('%', p_search_term, '%') OR
         p.sku LIKE CONCAT('%', p_search_term, '%'))
    AND (p_min_price IS NULL OR
         (p.currency = 'GTQ' AND CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END >= p_min_price) OR
         (p.currency = 'USD' AND fn_convert_currency(CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END, 'USD', 'GTQ') >= p_min_price))
    AND (p_max_price IS NULL OR
         (p.currency = 'GTQ' AND CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END <= p_max_price) OR
         (p.currency = 'USD' AND fn_convert_currency(CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END, 'USD', 'GTQ') <= p_max_price))
    ORDER BY
        CASE p_sort_by
            WHEN 'name' THEN p.name
            WHEN 'newest' THEN p.created_at
            ELSE NULL
        END ASC,
        CASE p_sort_by
            WHEN 'price_asc' THEN
                CASE
                    WHEN p.currency = 'GTQ' THEN CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END
                    ELSE fn_convert_currency(CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END, 'USD', 'GTQ')
                END
            ELSE NULL
        END ASC,
        CASE p_sort_by
            WHEN 'price_desc' THEN
                CASE
                    WHEN p.currency = 'GTQ' THEN CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END
                    ELSE fn_convert_currency(CASE WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale ELSE p.price_regular END, 'USD', 'GTQ')
                END
            ELSE NULL
        END DESC,
        CASE p_sort_by
            WHEN 'popular' THEN p.is_featured
            ELSE NULL
        END DESC,
        p.created_at DESC
    LIMIT v_limit OFFSET v_offset;
END$$

DELIMITER ;
