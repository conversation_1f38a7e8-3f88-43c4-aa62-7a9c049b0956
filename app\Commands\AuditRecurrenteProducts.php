<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\ProductModel;
use App\Services\RecurrenteService;

class AuditRecurrenteProducts extends BaseCommand
{
    protected $group       = 'Recurrente';
    protected $name        = 'recurrente:audit';
    protected $description = 'Auditar productos en Recurrente vs productos locales';
    protected $usage       = 'recurrente:audit [options]';
    protected $options     = [
        '--fix-urls' => 'Corregir URLs de imágenes automáticamente',
        '--show-details' => 'Mostrar detalles completos de cada producto'
    ];

    public function run(array $params)
    {
        CLI::write('=== Auditoría de Productos Recurrente ===', 'yellow');
        CLI::newLine();

        $fixUrls = CLI::getOption('fix-urls');
        $showDetails = CLI::getOption('show-details');

        $productModel = new ProductModel();
        $recurrenteService = new RecurrenteService();

        // Verificar que Recurrente esté habilitado
        if (!$recurrenteService->isEnabled()) {
            CLI::error('Recurrente no está habilitado');
            return;
        }

        CLI::write('✓ Recurrente está habilitado', 'green');
        CLI::newLine();

        try {
            // Obtener productos de Recurrente
            CLI::write('Obteniendo productos de Recurrente...', 'cyan');
            $recurrenteProducts = $recurrenteService->getProducts(1, 1000); // Obtener hasta 1000 productos

            // La API puede devolver directamente un array o un objeto con 'data'
            if (is_array($recurrenteProducts) && isset($recurrenteProducts[0])) {
                $recurrenteProductsList = $recurrenteProducts;
            } elseif (isset($recurrenteProducts['data'])) {
                $recurrenteProductsList = $recurrenteProducts['data'];
            } else {
                CLI::error('Formato inesperado de respuesta de Recurrente');
                CLI::write('Respuesta recibida: ' . json_encode(array_slice($recurrenteProducts, 0, 2)), 'yellow');
                return;
            }
            CLI::write('✓ Productos en Recurrente: ' . count($recurrenteProductsList), 'green');

            // Obtener productos locales
            CLI::write('Obteniendo productos locales...', 'cyan');
            $localProducts = $productModel
                ->where('is_active', 1)
                ->where('deleted_at IS NULL')
                ->findAll();
            CLI::write('✓ Productos locales activos: ' . count($localProducts), 'green');
            CLI::newLine();

            // Crear mapas para comparación
            $recurrenteByName = [];
            $recurrenteById = [];
            
            foreach ($recurrenteProductsList as $product) {
                $recurrenteByName[strtolower(trim($product['name']))] = $product;
                $recurrenteById[$product['id']] = $product;
            }

            $localByRecurrenteId = [];
            $localByName = [];
            
            foreach ($localProducts as $product) {
                $localByName[strtolower(trim($product['name']))] = $product;
                if (!empty($product['recurrente_product_id'])) {
                    $localByRecurrenteId[$product['recurrente_product_id']] = $product;
                }
            }

            // Análisis de problemas
            $problems = [
                'urls_incorrectas' => [],
                'no_existen_en_recurrente' => [],
                'id_incorrecto' => [],
                'sin_imagen' => [],
                'duplicados' => [],
                'huerfanos_en_recurrente' => []
            ];

            CLI::write('=== ANÁLISIS DE PROBLEMAS ===', 'yellow');
            CLI::newLine();

            // 1. Verificar productos locales
            foreach ($localProducts as $localProduct) {
                $localName = strtolower(trim($localProduct['name']));
                $recurrenteId = $localProduct['recurrente_product_id'] ?? null;

                // Verificar si existe en Recurrente
                if (!empty($recurrenteId)) {
                    if (!isset($recurrenteById[$recurrenteId])) {
                        $problems['no_existen_en_recurrente'][] = [
                            'local' => $localProduct,
                            'recurrente_id' => $recurrenteId,
                            'reason' => 'ID no existe en Recurrente'
                        ];
                        continue;
                    }

                    $recurrenteProduct = $recurrenteById[$recurrenteId];
                    
                    // Verificar si el nombre coincide
                    $recurrenteName = strtolower(trim($recurrenteProduct['name']));
                    if ($localName !== $recurrenteName) {
                        $problems['id_incorrecto'][] = [
                            'local' => $localProduct,
                            'recurrente' => $recurrenteProduct,
                            'reason' => 'Nombre no coincide'
                        ];
                    }

                    // Verificar URL de imagen
                    $imageUrl = $recurrenteProduct['image_url'] ?? '';
                    if (!empty($imageUrl)) {
                        if (strpos($imageUrl, 'localhost') !== false || strpos($imageUrl, '127.0.0.1') !== false) {
                            $problems['urls_incorrectas'][] = [
                                'local' => $localProduct,
                                'recurrente' => $recurrenteProduct,
                                'current_url' => $imageUrl
                            ];
                        }
                    } else {
                        $problems['sin_imagen'][] = [
                            'local' => $localProduct,
                            'recurrente' => $recurrenteProduct
                        ];
                    }

                } else {
                    // Producto local sin ID de Recurrente, buscar por nombre
                    if (isset($recurrenteByName[$localName])) {
                        $problems['id_incorrecto'][] = [
                            'local' => $localProduct,
                            'recurrente' => $recurrenteByName[$localName],
                            'reason' => 'Existe en Recurrente pero sin ID local'
                        ];
                    } else {
                        $problems['no_existen_en_recurrente'][] = [
                            'local' => $localProduct,
                            'recurrente_id' => null,
                            'reason' => 'No existe en Recurrente'
                        ];
                    }
                }
            }

            // 2. Verificar productos huérfanos en Recurrente
            foreach ($recurrenteProductsList as $recurrenteProduct) {
                if (!isset($localByRecurrenteId[$recurrenteProduct['id']])) {
                    // Buscar por nombre
                    $recurrenteName = strtolower(trim($recurrenteProduct['name']));
                    if (!isset($localByName[$recurrenteName])) {
                        $problems['huerfanos_en_recurrente'][] = $recurrenteProduct;
                    }
                }
            }

            // Mostrar resultados
            $this->showResults($problems, $showDetails, $fixUrls, $recurrenteService, $productModel);

        } catch (\Exception $e) {
            CLI::error('Error durante la auditoría: ' . $e->getMessage());
        }
    }

    private function showResults($problems, $showDetails, $fixUrls, $recurrenteService, $productModel)
    {
        // URLs incorrectas
        if (!empty($problems['urls_incorrectas'])) {
            CLI::write('🔴 URLS DE IMÁGENES INCORRECTAS: ' . count($problems['urls_incorrectas']), 'red');
            foreach ($problems['urls_incorrectas'] as $problem) {
                CLI::write("  - {$problem['local']['name']} (ID: {$problem['local']['id']})", 'white');
                CLI::write("    URL actual: {$problem['current_url']}", 'red');
                
                if ($showDetails) {
                    CLI::write("    Recurrente ID: {$problem['recurrente']['id']}", 'white');
                }
            }
            CLI::newLine();
        }

        // No existen en Recurrente
        if (!empty($problems['no_existen_en_recurrente'])) {
            CLI::write('🔴 NO EXISTEN EN RECURRENTE: ' . count($problems['no_existen_en_recurrente']), 'red');
            foreach ($problems['no_existen_en_recurrente'] as $problem) {
                CLI::write("  - {$problem['local']['name']} (ID: {$problem['local']['id']})", 'white');
                CLI::write("    Razón: {$problem['reason']}", 'yellow');
                if ($problem['recurrente_id']) {
                    CLI::write("    Recurrente ID: {$problem['recurrente_id']}", 'white');
                }
            }
            CLI::newLine();
        }

        // IDs incorrectos
        if (!empty($problems['id_incorrecto'])) {
            CLI::write('🔴 IDS INCORRECTOS: ' . count($problems['id_incorrecto']), 'red');
            foreach ($problems['id_incorrecto'] as $problem) {
                CLI::write("  - Local: {$problem['local']['name']} (ID: {$problem['local']['id']})", 'white');
                CLI::write("    Recurrente: {$problem['recurrente']['name']} (ID: {$problem['recurrente']['id']})", 'white');
                CLI::write("    Razón: {$problem['reason']}", 'yellow');
            }
            CLI::newLine();
        }

        // Sin imagen
        if (!empty($problems['sin_imagen'])) {
            CLI::write('🟡 SIN IMAGEN: ' . count($problems['sin_imagen']), 'yellow');
            foreach ($problems['sin_imagen'] as $problem) {
                CLI::write("  - {$problem['local']['name']} (ID: {$problem['local']['id']})", 'white');
            }
            CLI::newLine();
        }

        // Huérfanos en Recurrente
        if (!empty($problems['huerfanos_en_recurrente'])) {
            CLI::write('🟡 HUÉRFANOS EN RECURRENTE: ' . count($problems['huerfanos_en_recurrente']), 'yellow');
            foreach ($problems['huerfanos_en_recurrente'] as $product) {
                CLI::write("  - {$product['name']} (Recurrente ID: {$product['id']})", 'white');
            }
            CLI::newLine();
        }

        // Resumen
        CLI::write('=== RESUMEN ===', 'yellow');
        CLI::write('URLs incorrectas: ' . count($problems['urls_incorrectas']), 'white');
        CLI::write('No existen en Recurrente: ' . count($problems['no_existen_en_recurrente']), 'white');
        CLI::write('IDs incorrectos: ' . count($problems['id_incorrecto']), 'white');
        CLI::write('Sin imagen: ' . count($problems['sin_imagen']), 'white');
        CLI::write('Huérfanos en Recurrente: ' . count($problems['huerfanos_en_recurrente']), 'white');

        if ($fixUrls && !empty($problems['urls_incorrectas'])) {
            CLI::newLine();
            CLI::write('Corrigiendo URLs de imágenes...', 'cyan');
            // Implementar corrección automática aquí si es necesario
        }
    }
}
