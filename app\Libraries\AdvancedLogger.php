<?php

namespace App\Libraries;

/**
 * Sistema de Logs Avanzado
 * Logging completo con múltiples niveles, rotación y análisis
 */
class AdvancedLogger
{
    private $config;
    private $db;
    private $logPath;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->logPath = WRITEPATH . 'logs/';
        
        $this->config = [
            'enabled' => env('ADVANCED_LOGGING_ENABLED', true),
            'log_to_file' => env('LOG_TO_FILE', true),
            'log_to_database' => env('LOG_TO_DATABASE', true),
            'log_level' => env('LOG_LEVEL', 'INFO'),
            'max_file_size' => env('LOG_MAX_FILE_SIZE', 10485760), // 10MB
            'max_files' => env('LOG_MAX_FILES', 10),
            'log_format' => env('LOG_FORMAT', 'detailed'),
            'include_trace' => env('LOG_INCLUDE_TRACE', false),
            'log_user_actions' => env('LOG_USER_ACTIONS', true),
            'log_system_events' => env('LOG_SYSTEM_EVENTS', true),
            'log_security_events' => env('LOG_SECURITY_EVENTS', true),
            'log_performance' => env('LOG_PERFORMANCE', true),
            'retention_days' => env('LOG_RETENTION_DAYS', 30)
        ];
        
        $this->ensureLogDirectory();
        $this->createLogTables();
    }
    
    /**
     * Log de emergencia (sistema no funcional)
     */
    public function emergency(string $message, array $context = []): void
    {
        $this->log('EMERGENCY', $message, $context);
    }
    
    /**
     * Log de alerta (acción requerida inmediatamente)
     */
    public function alert(string $message, array $context = []): void
    {
        $this->log('ALERT', $message, $context);
    }
    
    /**
     * Log crítico (condiciones críticas)
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('CRITICAL', $message, $context);
    }
    
    /**
     * Log de error
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * Log de advertencia
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * Log de aviso
     */
    public function notice(string $message, array $context = []): void
    {
        $this->log('NOTICE', $message, $context);
    }
    
    /**
     * Log informativo
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * Log de debug
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('DEBUG', $message, $context);
    }
    
    /**
     * Log de acción de usuario
     */
    public function userAction(string $action, int $userId, array $data = []): void
    {
        if (!$this->config['log_user_actions']) return;
        
        $context = array_merge($data, [
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]);
        
        $this->log('USER_ACTION', "User action: $action", $context);
    }
    
    /**
     * Log de evento del sistema
     */
    public function systemEvent(string $event, array $data = []): void
    {
        if (!$this->config['log_system_events']) return;
        
        $context = array_merge($data, [
            'server_name' => gethostname(),
            'memory_usage' => memory_get_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
        ]);
        
        $this->log('SYSTEM_EVENT', "System event: $event", $context);
    }
    
    /**
     * Log de evento de seguridad
     */
    public function securityEvent(string $event, array $data = []): void
    {
        if (!$this->config['log_security_events']) return;
        
        $context = array_merge($data, [
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'severity' => 'HIGH'
        ]);
        
        $this->log('SECURITY', "Security event: $event", $context);
        
        // Enviar alerta inmediata para eventos de seguridad
        $this->sendSecurityAlert($event, $context);
    }
    
    /**
     * Log de rendimiento
     */
    public function performance(string $operation, float $duration, array $data = []): void
    {
        if (!$this->config['log_performance']) return;
        
        $context = array_merge($data, [
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ]);
        
        $level = $duration > 5 ? 'WARNING' : 'INFO';
        $this->log($level, "Performance: $operation took {$context['duration_ms']}ms", $context);
    }
    
    /**
     * Log de automatización
     */
    public function automation(string $type, bool $success, array $data = []): void
    {
        $status = $success ? 'SUCCESS' : 'FAILED';
        $level = $success ? 'INFO' : 'ERROR';
        
        $context = array_merge($data, [
            'automation_type' => $type,
            'status' => $status,
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
        ]);
        
        $this->log($level, "Automation $type: $status", $context);
    }
    
    /**
     * Log de backup
     */
    public function backup(string $type, bool $success, array $data = []): void
    {
        $status = $success ? 'SUCCESS' : 'FAILED';
        $level = $success ? 'INFO' : 'CRITICAL';
        
        $context = array_merge($data, [
            'backup_type' => $type,
            'status' => $status
        ]);
        
        $this->log($level, "Backup $type: $status", $context);
    }
    
    /**
     * Log de notificación push
     */
    public function pushNotification(string $type, bool $success, array $data = []): void
    {
        $status = $success ? 'SENT' : 'FAILED';
        $level = $success ? 'INFO' : 'WARNING';
        
        $context = array_merge($data, [
            'notification_type' => $type,
            'status' => $status
        ]);
        
        $this->log($level, "Push notification $type: $status", $context);
    }
    
    /**
     * Método principal de logging
     */
    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config['enabled'] || !$this->shouldLog($level)) {
            return;
        }
        
        $logEntry = $this->formatLogEntry($level, $message, $context);
        
        // Log a archivo
        if ($this->config['log_to_file']) {
            $this->logToFile($level, $logEntry);
        }
        
        // Log a base de datos
        if ($this->config['log_to_database']) {
            $this->logToDatabase($level, $message, $context);
        }
    }
    
    /**
     * Formatear entrada de log
     */
    private function formatLogEntry(string $level, string $message, array $context): string
    {
        $timestamp = date('Y-m-d H:i:s');
        $requestId = $this->getRequestId();
        
        $entry = "[$timestamp] [$level] [$requestId] $message";
        
        if (!empty($context)) {
            if ($this->config['log_format'] === 'detailed') {
                $entry .= " | Context: " . json_encode($context, JSON_UNESCAPED_UNICODE);
            } else {
                $entry .= " | " . $this->formatContextSimple($context);
            }
        }
        
        // Agregar stack trace para errores críticos
        if ($this->config['include_trace'] && in_array($level, ['EMERGENCY', 'ALERT', 'CRITICAL', 'ERROR'])) {
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
            $entry .= " | Trace: " . $this->formatTrace($trace);
        }
        
        return $entry;
    }
    
    /**
     * Log a archivo
     */
    private function logToFile(string $level, string $entry): void
    {
        $filename = $this->getLogFilename($level);
        $filepath = $this->logPath . $filename;
        
        // Verificar rotación de archivo
        if (file_exists($filepath) && filesize($filepath) > $this->config['max_file_size']) {
            $this->rotateLogFile($filepath);
        }
        
        file_put_contents($filepath, $entry . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Log a base de datos
     */
    private function logToDatabase(string $level, string $message, array $context): void
    {
        try {
            $data = [
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'url' => $_SERVER['REQUEST_URI'] ?? null,
                'method' => $_SERVER['REQUEST_METHOD'] ?? null,
                'user_id' => $context['user_id'] ?? null,
                'session_id' => session_id() ?: null,
                'request_id' => $this->getRequestId(),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->table('system_logs')->insert($data);
            
        } catch (\Exception $e) {
            // Fallback a archivo si falla la BD
            error_log("Failed to log to database: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener logs con filtros
     */
    public function getLogs(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        try {
            $builder = $this->db->table('system_logs')
                              ->orderBy('created_at', 'DESC')
                              ->limit($limit, $offset);
            
            // Aplicar filtros
            if (!empty($filters['level'])) {
                $builder->where('level', $filters['level']);
            }
            
            if (!empty($filters['user_id'])) {
                $builder->where('user_id', $filters['user_id']);
            }
            
            if (!empty($filters['date_from'])) {
                $builder->where('created_at >=', $filters['date_from']);
            }
            
            if (!empty($filters['date_to'])) {
                $builder->where('created_at <=', $filters['date_to']);
            }
            
            if (!empty($filters['search'])) {
                $builder->like('message', $filters['search']);
            }
            
            return [
                'success' => true,
                'logs' => $builder->get()->getResultArray()
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de logs
     */
    public function getLogStats(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Conteo por nivel
            $levelStats = $this->db->query("
                SELECT level, COUNT(*) as count 
                FROM system_logs 
                WHERE created_at >= ? 
                GROUP BY level
            ", [$dateFrom])->getResultArray();
            
            // Conteo por día
            $dailyStats = $this->db->query("
                SELECT DATE(created_at) as date, COUNT(*) as count 
                FROM system_logs 
                WHERE created_at >= ? 
                GROUP BY DATE(created_at) 
                ORDER BY date
            ", [$dateFrom])->getResultArray();
            
            // Top usuarios con más logs
            $userStats = $this->db->query("
                SELECT user_id, COUNT(*) as count 
                FROM system_logs 
                WHERE created_at >= ? AND user_id IS NOT NULL 
                GROUP BY user_id 
                ORDER BY count DESC 
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            // Top IPs
            $ipStats = $this->db->query("
                SELECT ip_address, COUNT(*) as count 
                FROM system_logs 
                WHERE created_at >= ? AND ip_address IS NOT NULL 
                GROUP BY ip_address 
                ORDER BY count DESC 
                LIMIT 10
            ", [$dateFrom])->getResultArray();
            
            return [
                'success' => true,
                'stats' => [
                    'by_level' => $levelStats,
                    'by_day' => $dailyStats,
                    'by_user' => $userStats,
                    'by_ip' => $ipStats
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Limpiar logs antiguos
     */
    public function cleanupOldLogs(): array
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$this->config['retention_days']} days"));
            
            // Limpiar base de datos
            $deletedRows = $this->db->table('system_logs')
                                   ->where('created_at <', $cutoffDate)
                                   ->delete();
            
            // Limpiar archivos
            $deletedFiles = $this->cleanupLogFiles();
            
            return [
                'success' => true,
                'deleted_rows' => $deletedRows,
                'deleted_files' => $deletedFiles
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Exportar logs
     */
    public function exportLogs(array $filters = [], string $format = 'csv'): array
    {
        try {
            $logs = $this->getLogs($filters, 10000);
            
            if (!$logs['success']) {
                return $logs;
            }
            
            $filename = 'logs_export_' . date('Y-m-d_H-i-s') . '.' . $format;
            $filepath = WRITEPATH . 'exports/' . $filename;
            
            // Crear directorio si no existe
            if (!is_dir(dirname($filepath))) {
                mkdir(dirname($filepath), 0755, true);
            }
            
            switch ($format) {
                case 'csv':
                    $this->exportToCSV($logs['logs'], $filepath);
                    break;
                case 'json':
                    $this->exportToJSON($logs['logs'], $filepath);
                    break;
                default:
                    throw new \Exception("Unsupported export format: $format");
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verificar si debe logear según el nivel
     */
    private function shouldLog(string $level): bool
    {
        $levels = [
            'DEBUG' => 0,
            'INFO' => 1,
            'NOTICE' => 2,
            'WARNING' => 3,
            'ERROR' => 4,
            'CRITICAL' => 5,
            'ALERT' => 6,
            'EMERGENCY' => 7,
            'USER_ACTION' => 1,
            'SYSTEM_EVENT' => 1,
            'SECURITY' => 6
        ];
        
        $currentLevel = $levels[$this->config['log_level']] ?? 1;
        $messageLevel = $levels[$level] ?? 1;
        
        return $messageLevel >= $currentLevel;
    }
    
    /**
     * Obtener ID único de request
     */
    private function getRequestId(): string
    {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = substr(md5(uniqid()), 0, 8);
        }
        
        return $requestId;
    }
    
    /**
     * Obtener nombre de archivo de log
     */
    private function getLogFilename(string $level): string
    {
        $date = date('Y-m-d');
        $levelLower = strtolower($level);
        
        return "mrcell_{$levelLower}_{$date}.log";
    }
    
    /**
     * Rotar archivo de log
     */
    private function rotateLogFile(string $filepath): void
    {
        $pathInfo = pathinfo($filepath);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $directory = $pathInfo['dirname'];
        
        // Mover archivos existentes
        for ($i = $this->config['max_files'] - 1; $i > 0; $i--) {
            $oldFile = "$directory/$baseName.$i.$extension";
            $newFile = "$directory/$baseName." . ($i + 1) . ".$extension";
            
            if (file_exists($oldFile)) {
                if ($i == $this->config['max_files'] - 1) {
                    unlink($oldFile); // Eliminar el más antiguo
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // Mover archivo actual
        rename($filepath, "$directory/$baseName.1.$extension");
    }
    
    /**
     * Formatear contexto simple
     */
    private function formatContextSimple(array $context): string
    {
        $parts = [];
        foreach ($context as $key => $value) {
            if (is_scalar($value)) {
                $parts[] = "$key=$value";
            }
        }
        return implode(', ', $parts);
    }
    
    /**
     * Formatear stack trace
     */
    private function formatTrace(array $trace): string
    {
        $formatted = [];
        foreach ($trace as $item) {
            $file = $item['file'] ?? 'unknown';
            $line = $item['line'] ?? 0;
            $function = $item['function'] ?? 'unknown';
            $formatted[] = "$file:$line $function()";
        }
        return implode(' -> ', $formatted);
    }
    
    /**
     * Limpiar archivos de log antiguos
     */
    private function cleanupLogFiles(): int
    {
        $files = glob($this->logPath . '*.log*');
        $deletedCount = 0;
        $cutoffTime = time() - ($this->config['retention_days'] * 24 * 3600);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Exportar a CSV
     */
    private function exportToCSV(array $logs, string $filepath): void
    {
        $handle = fopen($filepath, 'w');
        
        // Headers
        fputcsv($handle, ['Timestamp', 'Level', 'Message', 'User ID', 'IP Address', 'URL', 'Context']);
        
        // Data
        foreach ($logs as $log) {
            fputcsv($handle, [
                $log['created_at'],
                $log['level'],
                $log['message'],
                $log['user_id'],
                $log['ip_address'],
                $log['url'],
                $log['context']
            ]);
        }
        
        fclose($handle);
    }
    
    /**
     * Exportar a JSON
     */
    private function exportToJSON(array $logs, string $filepath): void
    {
        file_put_contents($filepath, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * Enviar alerta de seguridad
     */
    private function sendSecurityAlert(string $event, array $context): void
    {
        try {
            $alertManager = new \App\Libraries\AlertManager();
            $alertManager->securityAlert($event, $context);
        } catch (\Exception $e) {
            // Ignorar errores de alertas para no crear loops
        }
    }
    
    /**
     * Asegurar directorio de logs
     */
    private function ensureLogDirectory(): void
    {
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * Crear tablas de logs
     */
    private function createLogTables(): void
    {
        try {
            $this->db->query("
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    level VARCHAR(20) NOT NULL,
                    message TEXT NOT NULL,
                    context JSON,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    url TEXT,
                    method VARCHAR(10),
                    user_id INT,
                    session_id VARCHAR(128),
                    request_id VARCHAR(32),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_level (level),
                    INDEX idx_user_id (user_id),
                    INDEX idx_created_at (created_at),
                    INDEX idx_ip_address (ip_address)
                )
            ");
        } catch (\Exception $e) {
            // Ignorar errores de creación de tabla
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'log_to_file' => $this->config['log_to_file'],
            'log_to_database' => $this->config['log_to_database'],
            'log_level' => $this->config['log_level'],
            'retention_days' => $this->config['retention_days'],
            'log_path' => $this->logPath
        ];
    }
}
