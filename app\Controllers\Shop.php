<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * Controlador de Tienda
 *
 * Maneja la página de catálogo de productos
 */
class Shop extends Controller
{
    public function index()
    {
        try {
            $db = \Config\Database::connect();

            // Parámetros de paginación
            $page = (int) ($this->request->getGet('page') ?? 1);
            $limit = 20; // 20 productos por página
            $offset = ($page - 1) * $limit;

            // Obtener total de productos para paginación
            $totalQuery = $db->query("SELECT COUNT(*) as total FROM products WHERE is_active = 1 AND deleted_at IS NULL");
            $totalResult = $totalQuery->getRowArray();
            $totalProducts = $totalResult['total'];
            $totalPages = ceil($totalProducts / $limit);

            // Usar el SP real para obtener productos de la base de datos
            $query = $db->query("CALL sp_get_public_product_catalog(?, ?, ?, ?, ?, ?, ?)", [
                null, null, null, null, 'newest', $limit, $offset
            ]);

            $products = $query->getResultArray();

            // Obtener ratings actualizados para todos los productos
            $productIds = array_column($products, 'id');
            $ratings = [];
            if (!empty($productIds)) {
                $ratingsQuery = $db->table('products')
                                  ->select('id, rating_average, rating_count')
                                  ->whereIn('id', $productIds)
                                  ->get();
                foreach ($ratingsQuery->getResultArray() as $rating) {
                    $ratings[$rating['id']] = $rating;
                }
            }

            // Obtener category_id, brand_id y currency para cada producto (ya que el SP no los devuelve)
            $productDetailsQuery = $db->query("
                SELECT p.id as product_id, p.category_id, p.brand_id, p.currency, b.name as brand_name
                FROM products p
                LEFT JOIN brands b ON b.id = p.brand_id
                WHERE p.id IN (" . implode(',', array_column($products, 'id')) . ")
            ");
            $productDetails = [];
            foreach ($productDetailsQuery->getResultArray() as $row) {
                $productDetails[$row['product_id']] = [
                    'category_id' => $row['category_id'],
                    'brand_id' => $row['brand_id'],
                    'brand_name' => $row['brand_name'],
                    'currency' => $row['currency']
                ];
            }

            // Procesar datos de productos para compatibilidad con la vista
            foreach ($products as &$product) {
                $product['price_regular'] = (float) $product['price_regular'];
                $product['price_sale'] = $product['price_sale'] ? (float) $product['price_sale'] : null;
                $product['final_price'] = (float) $product['final_price'];
                $product['discount_percentage'] = (int) $product['discount_percentage'];
                $product['stock_quantity'] = (int) $product['stock_quantity'];
                $product['is_featured'] = (bool) $product['is_featured'];

                // Agregar category_id, brand info y currency que faltan del SP
                $details = $productDetails[$product['id']] ?? [];
                $product['category_id'] = $details['category_id'] ?? null;
                $product['brand_id'] = $details['brand_id'] ?? null;
                $product['brand_name'] = $details['brand_name'] ?? null;
                $product['currency'] = $details['currency'] ?? 'GTQ';

                // Actualizar ratings desde la consulta separada
                if (isset($ratings[$product['id']])) {
                    $product['rating_average'] = (float) $ratings[$product['id']]['rating_average'];
                    $product['rating_count'] = (int) $ratings[$product['id']]['rating_count'];
                } else {
                    $product['rating_average'] = 0.0;
                    $product['rating_count'] = 0;
                }

                // Compatibilidad con vista original
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['featured'] = $product['is_featured'];

                // Procesar imagen - construir URL completa si es una ruta relativa
                if (!empty($product['featured_image'])) {
                    if (strpos($product['featured_image'], 'http') === 0) {
                        // Ya es una URL completa
                        $product['image'] = $product['featured_image'];
                    } else {
                        // Es una ruta relativa, construir URL completa
                        $product['image'] = base_url($product['featured_image']);
                    }
                } else {
                    // Imagen por defecto
                    $product['image'] = 'https://via.placeholder.com/250x250/dc2626/ffffff?text=Producto';
                }
            }

            $data = [
                'title' => 'Tienda - MrCell Guatemala',
                'meta_description' => 'Encuentra los mejores celulares y accesorios en MrCell. Precios competitivos y la mejor calidad en Guatemala.',
                'meta_keywords' => 'celulares, smartphones, accesorios, tecnología, Guatemala, MrCell',
                'products' => $products,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_products' => $totalProducts,
                    'per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

            return view('frontend/shop', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en Shop::index: ' . $e->getMessage());

            // En caso de error, mostrar mensaje de error
            $data = [
                'title' => 'Tienda - MrCell Guatemala',
                'meta_description' => 'Encuentra los mejores celulares y accesorios en MrCell.',
                'meta_keywords' => 'celulares, smartphones, accesorios, tecnología, Guatemala, MrCell',
                'products' => [],
                'error_message' => 'Error al cargar productos: ' . $e->getMessage()
            ];

            return view('frontend/shop', $data);
        }
    }



    /**
     * Página de producto individual
     */
    public function product($slugOrId = null)
    {
        if (!$slugOrId) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Producto no encontrado');
        }

        try {
            helper('slug');
            $db = \Config\Database::connect();

            // Decodificar el slug si viene de una URL
            $decodedSlugOrId = url_decode_slug($slugOrId);

            // Usar el nuevo SP para obtener detalles del producto
            $productId = is_numeric($decodedSlugOrId) ? $decodedSlugOrId : null;
            $productSlug = !is_numeric($decodedSlugOrId) ? $decodedSlugOrId : null;

            $query = $db->query("CALL sp_get_public_product_details(?, ?)", [
                $productId, $productSlug
            ]);

            $product = $query->getRowArray();

            // Si no se encuentra con el slug decodificado, intentar con el slug original
            if (!$product && $productSlug && $decodedSlugOrId !== $slugOrId) {
                $query = $db->query("CALL sp_get_public_product_details(?, ?)", [
                    null, $slugOrId
                ]);
                $product = $query->getRowArray();
            }

            if (!$product) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Producto no encontrado');
            }

            // Obtener gallery_images, currency y has_variants directamente de la tabla products
            $additionalQuery = $db->table('products')
                              ->select('gallery_images, currency, has_variants')
                              ->where('id', $product['id'])
                              ->get();
            $additionalData = $additionalQuery->getRowArray();
            if ($additionalData) {
                $product['gallery_images'] = $additionalData['gallery_images'];
                $product['currency'] = $additionalData['currency'];
                $product['has_variants'] = $additionalData['has_variants'];
            }

            // Procesar datos del producto
            $product['price_regular'] = (float) $product['price_regular'];
            $product['price_sale'] = $product['price_sale'] ? (float) $product['price_sale'] : null;
            $product['final_price'] = (float) $product['final_price'];
            $product['price'] = (float) $product['price_regular']; // Agregar campo price para compatibilidad
            $product['sale_price'] = $product['price_sale']; // Agregar campo sale_price para compatibilidad
            $product['stock'] = (int) $product['stock_quantity']; // Agregar campo stock para compatibilidad

            // Procesar imagen - construir URL completa si es una ruta relativa
            if (!empty($product['featured_image'])) {
                if (strpos($product['featured_image'], 'http') === 0) {
                    // Ya es una URL completa
                    $product['image'] = $product['featured_image'];
                } else {
                    // Es una ruta relativa, construir URL completa
                    $product['image'] = base_url($product['featured_image']);
                }
            } else {
                // Imagen por defecto
                $product['image'] = 'https://via.placeholder.com/400x400/dc2626/ffffff?text=Producto';
            }

            $product['discount_percentage'] = (int) $product['discount_percentage'];
            $product['stock_quantity'] = (int) $product['stock_quantity'];
            $product['is_featured'] = (bool) $product['is_featured'];

            // Procesar atributos JSON
            if (!empty($product['attributes'])) {
                $product['attributes'] = json_decode($product['attributes'], true);
            }

            // Procesar galería de imágenes
            $product['gallery'] = [];
            if (!empty($product['gallery_images'])) {
                $galleryImages = json_decode($product['gallery_images'], true);
                if (is_array($galleryImages)) {
                    // Procesar cada imagen de la galería con base_url si es necesario
                    foreach ($galleryImages as $image) {
                        if (strpos($image, 'http') === 0) {
                            // Ya es una URL completa
                            $product['gallery'][] = $image;
                        } else {
                            // Es una ruta relativa, construir URL completa
                            $product['gallery'][] = base_url($image);
                        }
                    }
                } else {
                    // Si no es JSON válido, intentar como string separado por comas
                    $galleryImages = explode(',', $product['gallery_images']);
                    foreach ($galleryImages as $image) {
                        $image = trim($image);
                        if (strpos($image, 'http') === 0) {
                            $product['gallery'][] = $image;
                        } else {
                            $product['gallery'][] = base_url($image);
                        }
                    }
                }
            }

            // Si no hay galería, usar la imagen principal
            if (empty($product['gallery']) && !empty($product['image'])) {
                $product['gallery'] = [$product['image']];
            }

            // Cargar variantes si el producto las tiene
            $variants = [];
            $defaultVariant = null;
            if (isset($product['has_variants']) && $product['has_variants']) {
                $variantModel = new \App\Models\ProductVariantModel();
                $variants = $variantModel->getVariantsByProduct($product['id'], true);

                // Obtener la variante por defecto
                $defaultVariant = $variantModel->getDefaultVariant($product['id']);

                // Si hay variante por defecto, usar sus datos para mostrar inicialmente
                if ($defaultVariant) {
                    $product['variant_price_regular'] = $defaultVariant['price_regular'];
                    $product['variant_price_sale'] = $defaultVariant['price_sale'];
                    $product['variant_stock'] = $defaultVariant['stock_quantity'];
                    $product['variant_description'] = $defaultVariant['description'];
                    $product['variant_name'] = $defaultVariant['name'];
                    $product['selected_variant_id'] = $defaultVariant['id'];

                    // Usar imagen de la variante si existe
                    if (!empty($defaultVariant['featured_image'])) {
                        if (strpos($defaultVariant['featured_image'], 'http') === 0) {
                            $product['variant_image'] = $defaultVariant['featured_image'];
                        } else {
                            $product['variant_image'] = base_url($defaultVariant['featured_image']);
                        }
                    }
                }

                // Procesar imágenes de todas las variantes
                foreach ($variants as &$variant) {
                    if (!empty($variant['featured_image'])) {
                        if (strpos($variant['featured_image'], 'http') === 0) {
                            $variant['image_url'] = $variant['featured_image'];
                        } else {
                            $variant['image_url'] = base_url($variant['featured_image']);
                        }
                    } else {
                        $variant['image_url'] = $product['image']; // Usar imagen del producto padre
                    }
                }
            }

            $data = [
                'title' => $product['name'] . ' - MrCell Guatemala',
                'meta_description' => $product['meta_description'] ?: $product['short_description'] ?: substr(strip_tags($product['description']), 0, 160),
                'meta_keywords' => $product['name'] . ', ' . $product['category_name'] . ', celular, smartphone, MrCell',
                'product' => $product,
                'variants' => $variants,
                'default_variant' => $defaultVariant
            ];

            return view('frontend/product_modern', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en Shop::product: ' . $e->getMessage());
            error_log('DEBUG Shop::product - Error: ' . $e->getMessage() . ' - Slug/ID: ' . $slugOrId);
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Producto no encontrado: ' . $e->getMessage());
        }
    }

    /**
     * Obtener producto de la base de datos real
     */
    private function getProduct($id)
    {
        try {
            $db = \Config\Database::connect();

            // Obtener producto con información de categoría
            $product = $db->table('products p')
                         ->select('p.*, c.name as category_name, c.slug as category_slug')
                         ->join('categories c', 'c.id = p.category_id', 'left')
                         ->where('p.id', $id)
                         ->where('p.is_active', 1)
                         ->where('p.deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if ($product) {
                // Formatear el producto para compatibilidad
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['stock'] = $product['stock_quantity'];

                // Calcular descuento si hay precio de oferta
                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }

                // Agregar especificaciones básicas si no existen
                if (empty($product['specifications'])) {
                    $product['specifications'] = [
                        'SKU' => $product['sku'],
                        'Categoría' => $product['category_name'],
                        'Stock' => $product['stock_quantity'] . ' disponibles'
                    ];
                }
            }

            return $product;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo producto: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener producto por slug
     */
    private function getProductBySlug($slug)
    {
        try {
            $db = \Config\Database::connect();

            // Obtener producto con información de categoría
            $product = $db->table('products p')
                         ->select('p.*, c.name as category_name, c.slug as category_slug')
                         ->join('categories c', 'c.id = p.category_id', 'left')
                         ->where('p.slug', $slug)
                         ->where('p.is_active', 1)
                         ->where('p.deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if ($product) {
                // Formatear el producto para compatibilidad
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['stock'] = $product['stock_quantity'];

                // Calcular descuento si hay precio de oferta
                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }

                // Agregar especificaciones básicas si no existen
                if (empty($product['specifications'])) {
                    $product['specifications'] = [
                        'SKU' => $product['sku'],
                        'Categoría' => $product['category_name'],
                        'Stock' => $product['stock_quantity'] . ' disponibles'
                    ];
                }
            }

            return $product;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo producto por slug: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener productos relacionados
     */
    private function getRelatedProducts($categoryId, $excludeId, $limit = 4)
    {
        try {
            $db = \Config\Database::connect();

            $products = $db->table('products p')
                          ->select('p.*, c.name as category_name')
                          ->join('categories c', 'c.id = p.category_id', 'left')
                          ->where('p.category_id', $categoryId)
                          ->where('p.id !=', $excludeId)
                          ->where('p.is_active', 1)
                          ->where('p.deleted_at IS NULL')
                          ->orderBy('p.is_featured', 'DESC')
                          ->orderBy('p.created_at', 'DESC')
                          ->limit($limit)
                          ->get()
                          ->getResultArray();

            // Formatear productos para compatibilidad
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['stock'] = $product['stock_quantity'];

                // Procesar imagen - construir URL completa si es una ruta relativa
                if (!empty($product['featured_image'])) {
                    if (strpos($product['featured_image'], 'http') === 0) {
                        // Ya es una URL completa
                        $product['image'] = $product['featured_image'];
                    } else {
                        // Es una ruta relativa, construir URL completa
                        $product['image'] = base_url($product['featured_image']);
                    }
                } else {
                    // Imagen por defecto
                    $product['image'] = 'https://via.placeholder.com/200x200/dc2626/ffffff?text=Producto';
                }

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $products;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo productos relacionados: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Página de categorías
     */
    public function categories()
    {
        try {
            $db = \Config\Database::connect();

            // Usar stored procedure para obtener categorías
            $query = $db->query("CALL sp_get_categories_tree(?, ?)", [null, false]);
            $categories = $query->getResultArray();

            // Agregar conteo de productos para cada categoría
            foreach ($categories as &$category) {
                $productCountQuery = $db->query("
                    SELECT COUNT(*) as product_count
                    FROM products p
                    WHERE p.category_id = ? AND p.is_active = 1 AND p.deleted_at IS NULL
                ", [$category['id']]);

                $productCount = $productCountQuery->getRowArray();
                $category['product_count'] = $productCount['product_count'] ?? 0;
            }

            $data = [
                'title' => 'Categorías - MrCell Guatemala',
                'meta_description' => 'Explora todas nuestras categorías de productos tecnológicos en MrCell Guatemala.',
                'meta_keywords' => 'categorías, celulares, smartphones, accesorios, tecnología, Guatemala, MrCell',
                'categories' => $categories
            ];

            return view('frontend/categories', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en Shop::categories: ' . $e->getMessage());

            $data = [
                'title' => 'Categorías - MrCell Guatemala',
                'meta_description' => 'Explora todas nuestras categorías de productos tecnológicos.',
                'meta_keywords' => 'categorías, celulares, smartphones, accesorios, tecnología, Guatemala, MrCell',
                'categories' => [],
                'error_message' => 'Error al cargar categorías: ' . $e->getMessage()
            ];

            return view('frontend/categories', $data);
        }
    }

    /**
     * API endpoint para obtener datos de una variante específica
     */
    public function getVariantData($variantId = null)
    {
        if (!$variantId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'ID de variante requerido'
            ]);
        }

        try {
            $variantModel = new \App\Models\ProductVariantModel();
            $variant = $variantModel->find($variantId);

            if (!$variant || !$variant['is_active']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Variante no encontrada'
                ]);
            }

            // Procesar imagen de la variante
            $variantImage = null;
            if (!empty($variant['featured_image'])) {
                if (strpos($variant['featured_image'], 'http') === 0) {
                    $variantImage = $variant['featured_image'];
                } else {
                    $variantImage = base_url($variant['featured_image']);
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'variant' => [
                    'id' => $variant['id'],
                    'name' => $variant['name'],
                    'sku' => $variant['sku'],
                    'description' => $variant['description'],
                    'price_regular' => (float) $variant['price_regular'],
                    'price_sale' => $variant['price_sale'] ? (float) $variant['price_sale'] : null,
                    'stock_quantity' => (int) $variant['stock_quantity'],
                    'stock_min' => (int) $variant['stock_min'],
                    'featured_image' => $variantImage,
                    'is_default' => (bool) $variant['is_default']
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en Shop::getVariantData: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error al obtener datos de la variante'
            ]);
        }
    }
}
