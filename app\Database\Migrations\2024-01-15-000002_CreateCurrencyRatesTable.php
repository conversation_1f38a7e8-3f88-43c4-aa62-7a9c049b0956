<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCurrencyRatesTable extends Migration
{
    public function up()
    {
        // Tabla para tasas de cambio dinámicas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'from_currency' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'comment' => 'Moneda origen (GTQ, USD, EUR, etc.)',
            ],
            'to_currency' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'comment' => 'Moneda destino (GTQ, USD, EUR, etc.)',
            ],
            'rate' => [
                'type' => 'DECIMAL',
                'constraint' => '10,6',
                'comment' => 'Tasa de cambio',
            ],
            'source' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'Fuente de la tasa (manual, api, banco, etc.)',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['from_currency', 'to_currency']);
        $this->forge->addKey('is_active');
        $this->forge->createTable('currency_rates');
        
        // Insertar tasas iniciales
        $data = [
            [
                'from_currency' => 'USD',
                'to_currency' => 'GTQ',
                'rate' => 7.80,
                'source' => 'manual',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'from_currency' => 'GTQ',
                'to_currency' => 'USD',
                'rate' => 0.128205,
                'source' => 'manual',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'from_currency' => 'EUR',
                'to_currency' => 'GTQ',
                'rate' => 8.50,
                'source' => 'manual',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'from_currency' => 'GTQ',
                'to_currency' => 'EUR',
                'rate' => 0.117647,
                'source' => 'manual',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];
        
        $this->db->table('currency_rates')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('currency_rates');
    }
}
