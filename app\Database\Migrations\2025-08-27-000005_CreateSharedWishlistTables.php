<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migración para tablas del sistema de listas compartidas
 * Incluye listas públicas, colaborativas y sistema de comentarios
 */
class CreateSharedWishlistTables extends Migration
{
    public function up()
    {
        // Tabla principal de listas compartidas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'Propietario de la lista',
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'slug' => [
                'type' => 'VARCHAR',
                'constraint' => 150,
                'null' => false,
            ],
            'share_token' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
                'null' => false,
                'comment' => 'Token único para compartir',
            ],
            'is_public' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si la lista es pública',
            ],
            'is_collaborative' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si permite colaboradores',
            ],
            'allow_comments' => [
                'type' => 'BOOLEAN',
                'default' => true,
                'comment' => 'Si permite comentarios',
            ],
            'allow_voting' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Si permite votar productos',
            ],
            'privacy_level' => [
                'type' => 'ENUM',
                'constraint' => ['private', 'friends', 'public'],
                'default' => 'private',
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => 'general',
                'comment' => 'Categoría de la lista',
            ],
            'tags' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Tags de la lista',
            ],
            'settings' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Configuraciones adicionales',
            ],
            'view_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'like_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'share_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'expires_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Fecha de expiración opcional',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('slug', false, true); // Unique
        $this->forge->addKey('share_token', false, true); // Unique
        $this->forge->addKey(['is_public', 'privacy_level']);
        $this->forge->addKey('category');
        $this->forge->addKey('created_at');
        $this->forge->addKey('view_count');
        $this->forge->addKey('like_count');
        
        $this->forge->createTable('shared_wishlists');
        
        // Foreign key
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de productos en listas compartidas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'added_by_user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'Usuario que agregó el producto',
            ],
            'priority' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'urgent'],
                'default' => 'medium',
            ],
            'notes' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Notas sobre el producto',
            ],
            'price_alert_threshold' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Precio objetivo para alerta',
            ],
            'quantity_desired' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 1,
            ],
            'is_purchased' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'purchased_by_user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'purchased_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'vote_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Votos positivos del producto',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['shared_wishlist_id', 'product_id'], false, true); // Unique
        $this->forge->addKey('added_by_user_id');
        $this->forge->addKey('priority');
        $this->forge->addKey('is_purchased');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('shared_wishlist_items');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_id', 'shared_wishlists', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('added_by_user_id', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('purchased_by_user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        }

        // Tabla de colaboradores
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'invited_by_user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'role' => [
                'type' => 'ENUM',
                'constraint' => ['viewer', 'contributor', 'moderator'],
                'default' => 'contributor',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'accepted', 'declined', 'removed'],
                'default' => 'pending',
            ],
            'permissions' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Permisos específicos del colaborador',
            ],
            'can_add_items' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'can_remove_items' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'can_edit_items' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'can_invite_others' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'invitation_token' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
                'null' => true,
            ],
            'invited_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'responded_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['shared_wishlist_id', 'user_id'], false, true); // Unique
        $this->forge->addKey('invited_by_user_id');
        $this->forge->addKey('status');
        $this->forge->addKey('invitation_token');
        
        $this->forge->createTable('shared_wishlist_collaborators');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_id', 'shared_wishlists', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('invited_by_user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de comentarios
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'parent_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Para respuestas a comentarios',
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Comentario específico de un producto',
            ],
            'comment' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'is_approved' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'like_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('shared_wishlist_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('parent_id');
        $this->forge->addKey('product_id');
        $this->forge->addKey('is_approved');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('shared_wishlist_comments');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_id', 'shared_wishlists', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }
        $this->forge->addForeignKey('parent_id', 'shared_wishlist_comments', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('products')) {
            $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de likes en listas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['shared_wishlist_id', 'user_id'], false, true); // Unique
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('shared_wishlist_likes');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_id', 'shared_wishlists', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de votos en productos
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_item_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'vote_type' => [
                'type' => 'ENUM',
                'constraint' => ['up', 'down'],
                'default' => 'up',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['shared_wishlist_item_id', 'user_id'], false, true); // Unique
        $this->forge->addKey('vote_type');
        
        $this->forge->createTable('shared_wishlist_votes');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_item_id', 'shared_wishlist_items', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }

        // Tabla de actividad en listas compartidas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'shared_wishlist_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'action' => [
                'type' => 'ENUM',
                'constraint' => ['created', 'added_product', 'removed_product', 'commented', 'liked', 'shared', 'invited_user'],
                'null' => false,
            ],
            'target_type' => [
                'type' => 'ENUM',
                'constraint' => ['list', 'product', 'comment', 'user'],
                'null' => true,
            ],
            'target_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Datos adicionales de la actividad',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('shared_wishlist_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('shared_wishlist_activity');
        
        // Foreign keys
        $this->forge->addForeignKey('shared_wishlist_id', 'shared_wishlists', 'id', 'CASCADE', 'CASCADE');
        if ($this->db->tableExists('users')) {
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        }
    }

    public function down()
    {
        $this->forge->dropTable('shared_wishlist_activity', true);
        $this->forge->dropTable('shared_wishlist_votes', true);
        $this->forge->dropTable('shared_wishlist_likes', true);
        $this->forge->dropTable('shared_wishlist_comments', true);
        $this->forge->dropTable('shared_wishlist_collaborators', true);
        $this->forge->dropTable('shared_wishlist_items', true);
        $this->forge->dropTable('shared_wishlists', true);
    }
}
