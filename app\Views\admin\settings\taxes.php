<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Configuración de Impuestos' ?> - <PERSON><PERSON><PERSON> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .settings-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-color: #bbdefb;
            color: #0d47a1;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-percentage me-2"></i>
                        Configuración de Impuestos
                    </h1>
                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver a Configuraciones
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card settings-card">
                            <div class="card-header settings-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    Configuración de Impuestos (IVA)
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="tax-settings-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <label class="form-label fw-bold">Estado del IVA</label>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="tax_enabled" name="tax_enabled" value="1" <?= ($tax_settings['tax_enabled'] ?? false) ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="tax_enabled">
                                                        <span id="tax-status-text">
                                                            <?= ($tax_settings['tax_enabled'] ?? false) ? 'Activado' : 'Desactivado' ?>
                                                        </span>
                                                    </label>
                                                </div>
                                                <small class="text-muted">Activar o desactivar el cálculo de impuestos en todo el sitio</small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <label for="tax_name" class="form-label fw-bold">Nombre del Impuesto</label>
                                                <input type="text" class="form-control" id="tax_name" name="tax_name" value="<?= $tax_settings['tax_name'] ?? 'IVA' ?>" placeholder="IVA, IGV, etc.">
                                                <small class="text-muted">Nombre que aparecerá en facturas y checkout</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <label for="tax_rate" class="form-label fw-bold">Porcentaje de Impuesto (%)</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="<?= $tax_settings['tax_rate'] ?? 12 ?>" min="0" max="100" step="0.01">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                                <small class="text-muted">Porcentaje del impuesto (ej: 12 para 12%)</small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <label class="form-label fw-bold">Impuesto Incluido en Precios</label>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="tax_included_in_price" name="tax_included_in_price" value="1" <?= ($tax_settings['tax_included_in_price'] ?? false) ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="tax_included_in_price">
                                                        <span id="tax-included-text">
                                                            <?= ($tax_settings['tax_included_in_price'] ?? false) ? 'Incluido' : 'Se agrega al final' ?>
                                                        </span>
                                                    </label>
                                                </div>
                                                <small class="text-muted">Si está activado, los precios ya incluyen el impuesto</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Configuración
                                        </button>
                                        
                                        <div class="text-muted">
                                            <small>
                                                <i class="fas fa-info-circle me-1"></i>
                                                Los cambios se aplicarán inmediatamente en todo el sitio
                                            </small>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card settings-card">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    Información
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Importante</h6>
                                    <ul class="mb-0 small">
                                        <li>Al <strong>desactivar</strong> el IVA, no se mostrará ni calculará en ninguna parte del sitio</li>
                                        <li>Al <strong>activar</strong> el IVA, se aplicará a todos los productos y se mostrará en el checkout</li>
                                        <li>Los cambios se aplican inmediatamente</li>
                                        <li>El porcentaje puede tener decimales (ej: 12.5%)</li>
                                    </ul>
                                </div>

                                <div class="mt-3">
                                    <h6>Estado Actual:</h6>
                                    <div class="d-flex align-items-center">
                                        <span class="badge <?= ($tax_settings['tax_enabled'] ?? false) ? 'bg-success' : 'bg-secondary' ?> me-2">
                                            <?= ($tax_settings['tax_enabled'] ?? false) ? 'ACTIVADO' : 'DESACTIVADO' ?>
                                        </span>
                                        <?php if ($tax_settings['tax_enabled'] ?? false): ?>
                                            <small class="text-muted">
                                                <?= $tax_settings['tax_name'] ?? 'IVA' ?> al <?= $tax_settings['tax_rate'] ?? 12 ?>%
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('tax-settings-form');
            const taxEnabledSwitch = document.getElementById('tax_enabled');
            const taxIncludedSwitch = document.getElementById('tax_included_in_price');
            const taxStatusText = document.getElementById('tax-status-text');
            const taxIncludedText = document.getElementById('tax-included-text');

            // Update status text when switches change
            taxEnabledSwitch.addEventListener('change', function() {
                taxStatusText.textContent = this.checked ? 'Activado' : 'Desactivado';
            });

            taxIncludedSwitch.addEventListener('change', function() {
                taxIncludedText.textContent = this.checked ? 'Incluido' : 'Se agrega al final';
            });

            // Handle form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                
                // Ensure unchecked checkboxes are sent as 0
                if (!taxEnabledSwitch.checked) {
                    formData.set('tax_enabled', '0');
                }
                if (!taxIncludedSwitch.checked) {
                    formData.set('tax_included_in_price', '0');
                }

                fetch('<?= base_url('tax-settings/update') ?>', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        location.reload(); // Reload to show updated status
                    } else {
                        alert('❌ ' + data.message);
                        if (data.errors) {
                            console.error('Validation errors:', data.errors);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ Error al guardar la configuración');
                });
            });
        });
    </script>
</body>
</html>
