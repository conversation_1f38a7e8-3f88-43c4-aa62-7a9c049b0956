<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-bell me-2"></i>Centro de Notificaciones</h1>
        <div>
            <button class="btn btn-success me-2" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i><PERSON><PERSON>
            </button>
            <button class="btn btn-primary" onclick="refreshNotifications()">
                <i class="fas fa-sync-alt me-2"></i>Actualizar
            </button>
        </div>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-primary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">5</h3>
                        <small class="text-muted">Total Notificaciones</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">4</h3>
                        <small class="text-muted">Sin Leer</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-danger text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">0</h3>
                        <small class="text-muted">Urgentes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-box"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">2</h3>
                        <small class="text-muted">Alertas de Stock</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Tipo de Notificación</label>
                <select class="form-select" id="typeFilter">
                    <option value="">Todos los tipos</option>
                    <option value="info">Información</option>
                    <option value="success">Éxito</option>
                    <option value="warning">Advertencia</option>
                    <option value="error">Error</option>
                    <option value="stock_alert">Alerta de Stock</option>
                    <option value="order_alert">Alerta de Pedido</option>
                    <option value="system">Sistema</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Estado</label>
                <select class="form-select" id="readFilter">
                    <option value="">Todas</option>
                    <option value="false">Sin leer</option>
                    <option value="true">Leídas</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Prioridad</label>
                <select class="form-select" id="priorityFilter">
                    <option value="">Todas las prioridades</option>
                    <option value="urgent">Urgente</option>
                    <option value="high">Alta</option>
                    <option value="normal">Normal</option>
                    <option value="low">Baja</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter me-2"></i>Aplicar Filtros
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Notificaciones -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Notificaciones</h5>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteAllRead()">
                <i class="fas fa-trash me-2"></i>Eliminar Leídas
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Notificación de Stock Bajo -->
        <div class="notification-item border-start border-warning border-4 p-3 mb-3 bg-light">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex gap-2">
                    <span class="badge bg-warning">STOCK</span>
                    <span class="badge bg-danger">Alta</span>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <small class="text-muted">8h</small>
                    <button class="btn btn-sm btn-outline-danger" title="Eliminar">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <h6 class="mb-2">Stock Bajo: Samsung Galaxy S24 Ultra</h6>
            <p class="mb-3 text-muted">El producto Samsung Galaxy S24 Ultra (SAMS24U256) tiene stock bajo: 5 unidades</p>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-primary">Ver Producto</button>
                <button class="btn btn-sm btn-outline-secondary">Marcar como leída</button>
            </div>
        </div>

        <!-- Notificación de Stock Bajo -->
        <div class="notification-item border-start border-warning border-4 p-3 mb-3 bg-light">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex gap-2">
                    <span class="badge bg-warning">STOCK</span>
                    <span class="badge bg-danger">Alta</span>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <small class="text-muted">8h</small>
                    <button class="btn btn-sm btn-outline-danger" title="Eliminar">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <h6 class="mb-2">Stock Bajo: iPhone 15 Pro</h6>
            <p class="mb-3 text-muted">El producto iPhone 15 Pro (IPH15PRO128) tiene stock bajo: 3 unidades</p>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-primary">Ver Producto</button>
                <button class="btn btn-sm btn-outline-secondary">Marcar como leída</button>
            </div>
        </div>

        <!-- Notificación de Nuevo Pedido -->
        <div class="notification-item border-start border-info border-4 p-3 mb-3">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex gap-2">
                    <span class="badge bg-info">PEDIDO</span>
                    <span class="badge bg-secondary">Normal</span>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <small class="text-muted">8h</small>
                    <button class="btn btn-sm btn-outline-danger" title="Eliminar">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <h6 class="mb-2">Nuevo Pedido: #ORD-2025-001</h6>
            <p class="mb-3 text-muted">Se ha recibido un nuevo pedido por Q1,170.00 de Juan Pérez</p>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-primary">Ver Pedido</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .notification-item {
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<script>
    // Funcionalidad para gestión de notificaciones
    function markAllAsRead() {
        console.log('Marcar todas como leídas');
        // Aquí iría la lógica para marcar todas las notificaciones como leídas
    }

    function refreshNotifications() {
        console.log('Refrescar notificaciones');
        // Aquí iría la lógica para refrescar las notificaciones
        location.reload();
    }

    function applyFilters() {
        console.log('Aplicar filtros');
        // Aquí iría la lógica para aplicar los filtros
    }

    function deleteAllRead() {
        if (confirm('¿Estás seguro de que deseas eliminar todas las notificaciones leídas?')) {
            console.log('Eliminar notificaciones leídas');
            // Aquí iría la lógica para eliminar las notificaciones leídas
        }
    }
</script>
<?= $this->endSection() ?>
