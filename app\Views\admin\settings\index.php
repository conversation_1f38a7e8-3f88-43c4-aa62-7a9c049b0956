<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1><i class="fas fa-cog me-2"></i>Configuración del Sistema</h1>
    <p class="text-muted">Gestiona todas las configuraciones de tu tienda desde un solo lugar</p>
</div>

<!-- Navigation Tabs -->
<div class="card">
    <div class="card-header p-0">
        <ul class="nav nav-tabs nav-tabs-line" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'general' ? 'active' : '' ?>"
                   href="/admin/settings?tab=general" role="tab">
                    <i class="fas fa-store me-2"></i>General
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'payments' ? 'active' : '' ?>"
                   href="/admin/settings?tab=payments" role="tab">
                    <i class="fas fa-credit-card me-2"></i>Pagos
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'notifications' ? 'active' : '' ?>"
                   href="/admin/settings?tab=notifications" role="tab">
                    <i class="fas fa-bell me-2"></i>Notificaciones
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'taxes' ? 'active' : '' ?>"
                   href="/admin/settings?tab=taxes" role="tab">
                    <i class="fas fa-percentage me-2"></i>Impuestos
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'currency' ? 'active' : '' ?>"
                   href="/admin/settings?tab=currency" role="tab">
                    <i class="fas fa-coins me-2"></i>Monedas
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'advanced' ? 'active' : '' ?>"
                   href="/admin/settings?tab=advanced" role="tab">
                    <i class="fas fa-tools me-2"></i>Avanzado
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'integrations' ? 'active' : '' ?>"
                   href="/admin/settings?tab=integrations" role="tab">
                    <i class="fas fa-plug me-2"></i>Integraciones
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'whatsapp' ? 'active' : '' ?>"
                   href="/admin/settings?tab=whatsapp" role="tab">
                    <i class="fab fa-whatsapp me-2"></i>WhatsApp
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link <?= $activeTab === 'shipping' ? 'active' : '' ?>"
                   href="/admin/settings?tab=shipping" role="tab">
                    <i class="fas fa-shipping-fast me-2"></i>Envíos
                </a>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <!-- Tab Content -->
        <div class="tab-content" id="settingsTabContent">

            <!-- General Tab -->
            <?php if ($activeTab === 'general'): ?>
            <div class="tab-pane fade show active" id="general" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-store me-2"></i>Información General</h5>
                                <small class="text-muted">Configuración básica de tu tienda</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="update_general">
                                    <?php
                                    $generalSettings = [];
                                    if (isset($settings['general'])) {
                                        foreach ($settings['general'] as $setting) {
                                            $generalSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    }
                                    ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="siteName" class="form-label">Nombre del Sitio</label>
                                                <input type="text" class="form-control" id="siteName" name="site_name"
                                                       value="<?= esc($generalSettings['site_name'] ?? 'MrCell') ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="contactEmail" class="form-label">Email de Contacto</label>
                                                <input type="email" class="form-control" id="contactEmail" name="contact_email"
                                                       value="<?= esc($generalSettings['contact_email'] ?? '<EMAIL>') ?>" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="siteDescription" class="form-label">Descripción</label>
                                        <textarea class="form-control" id="siteDescription" name="site_description" rows="3"><?= esc($generalSettings['site_description'] ?? 'Tu tienda de tecnología móvil de confianza') ?></textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="siteLogo" class="form-label">Logo del Sitio</label>
                                                <input type="file" class="form-control" id="siteLogo" name="site_logo" accept="image/*">
                                                <div class="form-text">Formatos: JPG, PNG, SVG. Tamaño recomendado: 200x60px</div>
                                                <?php if (!empty($generalSettings['site_logo'])): ?>
                                                    <div class="mt-2">
                                                        <img src="<?= base_url($generalSettings['site_logo']) ?>" alt="Logo actual" style="max-height: 60px;" class="border rounded">
                                                        <small class="text-muted d-block">Logo actual</small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="siteFavicon" class="form-label">Favicon</label>
                                                <input type="file" class="form-control" id="siteFavicon" name="site_favicon" accept="image/x-icon,image/png">
                                                <div class="form-text">Formato: ICO o PNG. Tamaño: 32x32px</div>
                                                <?php if (!empty($generalSettings['site_favicon'])): ?>
                                                    <div class="mt-2">
                                                        <img src="<?= base_url($generalSettings['site_favicon']) ?>" alt="Favicon actual" style="max-height: 32px;" class="border rounded">
                                                        <small class="text-muted d-block">Favicon actual</small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="contactPhone" class="form-label">Teléfono de Contacto</label>
                                                <input type="tel" class="form-control" id="contactPhone" name="contact_phone"
                                                       value="<?= esc($generalSettings['contact_phone'] ?? '+502 2345-6789') ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="address" class="form-label">Dirección</label>
                                                <textarea class="form-control" id="address" name="contact_address" rows="2"><?= esc($generalSettings['contact_address'] ?? 'Ciudad de Guatemala, Guatemala') ?></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Cambios
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    Esta información aparecerá en tu sitio web y será utilizada para contacto con los clientes.
                                </p>
                                <hr>
                                <h6>Estado del Sistema</h6>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small">Versión:</span>
                                    <span class="badge bg-primary">v2.0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="small">Estado:</span>
                                    <span class="badge bg-success">Activo</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Payments Tab -->
            <?php if ($activeTab === 'payments'): ?>
            <div class="tab-pane fade show active" id="payments" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Métodos de Pago</h5>
                                <small class="text-muted">Configura los métodos de pago disponibles</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings">
                                    <input type="hidden" name="action" value="update_payment">
                                    <?php
                                    $paymentSettings = [];
                                    if (isset($settings['payment'])) {
                                        foreach ($settings['payment'] as $setting) {
                                            $paymentSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    }
                                    ?>
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableCash" name="payment_cash"
                                                   <?= ($paymentSettings['payment_cash'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enableCash">
                                                <strong>Pago en Efectivo</strong><br>
                                                <small class="text-muted">Pago contra entrega</small>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableTransfer" name="payment_transfer"
                                                   <?= ($paymentSettings['payment_transfer'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enableTransfer">
                                                <strong>Transferencia Bancaria</strong><br>
                                                <small class="text-muted">Depósito o transferencia</small>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableCard" name="payment_card"
                                                   <?= ($paymentSettings['payment_card'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enableCard">
                                                <strong>Tarjeta de Crédito/Débito</strong><br>
                                                <small class="text-muted">Visa, MasterCard, etc.</small>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enablePaypal" name="payment_paypal"
                                                   <?= ($paymentSettings['payment_paypal'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enablePaypal">
                                                <strong>PayPal</strong><br>
                                                <small class="text-muted">Pagos internacionales</small>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Actualizar Métodos
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Gestión Avanzada</h5>
                            </div>
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                                <h6>Métodos de Pago Avanzados</h6>
                                <p class="text-muted">Configura múltiples cuentas bancarias, puntos de recogida y nuevos métodos de pago</p>
                                <a href="/admin/payment-methods" class="btn btn-outline-primary">
                                    <i class="fas fa-cog me-2"></i>Gestionar Métodos de Pago
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Notifications Tab -->
            <?php if ($activeTab === 'notifications'): ?>
            <div class="tab-pane fade show active" id="notifications" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Configuración de Notificaciones</h5>
                                <small class="text-muted">Gestiona cómo y cuándo recibir notificaciones</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings">
                                    <input type="hidden" name="action" value="update_notifications">
                                    <?php
                                    $notificationSettings = [];
                                    if (isset($settings['notifications'])) {
                                        foreach ($settings['notifications'] as $setting) {
                                            $notificationSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    }
                                    ?>

                                    <h6 class="mb-3">Canales de Notificación</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="emailNotifications" name="notifications_email"
                                                           <?= ($notificationSettings['notifications_email'] ?? '1') == '1' ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="emailNotifications">
                                                        <strong>Notificaciones por Email</strong><br>
                                                        <small class="text-muted">Recibir alertas por correo electrónico</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="smsNotifications" name="notifications_sms"
                                                           <?= ($notificationSettings['notifications_sms'] ?? '0') == '1' ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="smsNotifications">
                                                        <strong>Notificaciones por SMS</strong><br>
                                                        <small class="text-muted">Recibir alertas por mensaje de texto</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>
                                    <h6 class="mb-3">Tipos de Notificaciones</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="orderNotifications" name="notify_new_orders"
                                                           <?= ($notificationSettings['notify_new_orders'] ?? '1') == '1' ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="orderNotifications">
                                                        <strong>Nuevos Pedidos</strong><br>
                                                        <small class="text-muted">Notificar cuando llegue un nuevo pedido</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="stockNotifications" name="notify_low_stock"
                                                           <?= ($notificationSettings['notify_low_stock'] ?? '1') == '1' ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="stockNotifications">
                                                        <strong>Stock Bajo</strong><br>
                                                        <small class="text-muted">Alertas cuando el inventario esté bajo</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>
                                    <h6 class="mb-3">Configuración de Alertas del Sistema</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="whatsapp_alerts_group" class="form-label">
                                                    <i class="fab fa-whatsapp me-2 text-success"></i>
                                                    <strong>Número de Grupo WhatsApp para Alertas</strong>
                                                </label>
                                                <input type="text" class="form-control" id="whatsapp_alerts_group"
                                                       name="whatsapp_alerts_group"
                                                       value="<?= esc($notificationSettings['whatsapp_alerts_group'] ?? '120363416393766854') ?>"
                                                       placeholder="Ej: 120363416393766854">
                                                <div class="form-text">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Número del grupo de WhatsApp donde se enviarán las alertas automáticas del sistema (cron).
                                                    <strong>No incluir el símbolo +</strong>
                                                </div>
                                            </div>

                                            <div class="alert alert-info">
                                                <i class="fas fa-clock me-2"></i>
                                                <strong>Alertas Automáticas:</strong> El sistema enviará notificaciones cada 12 horas sobre:
                                                <ul class="mb-0 mt-2">
                                                    <li>Productos con bajo stock</li>
                                                    <li>Productos sin ventas por más de 1 mes</li>
                                                    <li>Productos próximos a caducar o caducados</li>
                                                    <li>Pedidos pendientes de procesar</li>
                                                    <li>Reseñas pendientes de revisión</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Preferencias
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    Las notificaciones te ayudan a mantenerte informado sobre eventos importantes en tu tienda.
                                </p>
                                <hr>
                                <h6>Estado Actual</h6>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small">Email:</span>
                                    <span class="badge <?= ($notificationSettings['notifications_email'] ?? '1') == '1' ? 'bg-success' : 'bg-secondary' ?>">
                                        <?= ($notificationSettings['notifications_email'] ?? '1') == '1' ? 'Activo' : 'Inactivo' ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="small">SMS:</span>
                                    <span class="badge <?= ($notificationSettings['notifications_sms'] ?? '0') == '1' ? 'bg-success' : 'bg-secondary' ?>">
                                        <?= ($notificationSettings['notifications_sms'] ?? '0') == '1' ? 'Activo' : 'Inactivo' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Taxes Tab -->
            <?php if ($activeTab === 'taxes'): ?>
            <div class="tab-pane fade show active" id="taxes" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-percentage me-2"></i>Configuración de IVA/Impuestos</h5>
                                <small class="text-muted">Configura los impuestos de tu tienda</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings">
                                    <input type="hidden" name="action" value="update_tax">
                                    <?php
                                    // Obtener configuraciones de IVA desde system_settings
                                    $db = \Config\Database::connect();
                                    $taxSettings = [];
                                    try {
                                        $settings = $db->query("
                                            SELECT setting_key, setting_value, setting_type
                                            FROM system_settings
                                            WHERE setting_group = 'taxes' AND is_active = 1
                                        ")->getResultArray();

                                        foreach ($settings as $setting) {
                                            $taxSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    } catch (\Exception $e) {
                                        // Valores por defecto si hay error
                                        $taxSettings = [
                                            'tax_enabled' => '0',
                                            'tax_rate' => '12.00',
                                            'tax_name' => 'IVA',
                                            'tax_included_in_price' => '0'
                                        ];
                                    }
                                    ?>

                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="taxEnabled" name="tax_enabled"
                                                   <?= ($taxSettings['tax_enabled'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="taxEnabled">
                                                <strong>Activar IVA/Impuestos</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Activar o desactivar el cálculo de impuestos en toda la tienda</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="taxRate" class="form-label">Porcentaje de IVA (%)</label>
                                                <input type="number" class="form-control" id="taxRate" name="tax_rate"
                                                       value="<?= esc($taxSettings['tax_rate'] ?? '12.00') ?>"
                                                       step="0.01" min="0" max="100">
                                                <small class="text-muted">Ejemplo: 12 para 12% de IVA</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="taxName" class="form-label">Nombre del Impuesto</label>
                                                <input type="text" class="form-control" id="taxName" name="tax_name"
                                                       value="<?= esc($taxSettings['tax_name'] ?? 'IVA') ?>">
                                                <small class="text-muted">Ejemplo: IVA, IGV, Tax, etc.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="taxIncluded" name="tax_included_in_price"
                                                   <?= ($taxSettings['tax_included_in_price'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="taxIncluded">
                                                <strong>IVA incluido en precios</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Si está activado, los precios ya incluyen IVA. Si no, se agrega al final.</small>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Estado actual:</strong>
                                        <?php if (($taxSettings['tax_enabled'] ?? '0') == '1'): ?>
                                            <span class="text-success">IVA ACTIVADO</span> -
                                            <?= esc($taxSettings['tax_name'] ?? 'IVA') ?> al <?= esc($taxSettings['tax_rate'] ?? '12') ?>%
                                        <?php else: ?>
                                            <span class="text-danger">IVA DESACTIVADO</span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Configuración de IVA
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Simulador de IVA</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="simulatorPrice" class="form-label">Precio del Producto (Q)</label>
                                    <input type="number" class="form-control" id="simulatorPrice" value="100" step="0.01" min="0">
                                </div>

                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Cálculo:</h6>
                                        <div class="d-flex justify-content-between">
                                            <span>Subtotal:</span>
                                            <span id="simSubtotal">Q100.00</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span id="simTaxLabel">IVA (12%):</span>
                                            <span id="simTax">Q12.00</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>Total:</span>
                                            <span id="simTotal">Q112.00</span>
                                        </div>
                                    </div>
                                </div>

                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Este simulador te ayuda a ver cómo se calculará el IVA en tu tienda.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Advanced Tab -->
            <?php if ($activeTab === 'advanced'): ?>
            <div class="tab-pane fade show active" id="advanced" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Configuración Avanzada</h5>
                        <small class="text-muted">Configuraciones técnicas del sistema</small>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/settings">
                            <input type="hidden" name="action" value="update_advanced">
                            <?php
                            $advancedSettings = [];
                            if (isset($settings['advanced'])) {
                                foreach ($settings['advanced'] as $setting) {
                                    $advancedSettings[$setting['setting_key']] = $setting['setting_value'];
                                }
                            }
                            ?>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Zona Horaria</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="America/Guatemala" <?= ($advancedSettings['timezone'] ?? 'America/Guatemala') == 'America/Guatemala' ? 'selected' : '' ?>>Guatemala (GMT-6)</option>
                                            <option value="America/Mexico_City" <?= ($advancedSettings['timezone'] ?? '') == 'America/Mexico_City' ? 'selected' : '' ?>>México (GMT-6)</option>
                                            <option value="America/New_York" <?= ($advancedSettings['timezone'] ?? '') == 'America/New_York' ? 'selected' : '' ?>>Nueva York (GMT-5)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="currency" class="form-label">Moneda</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="GTQ" <?= ($advancedSettings['currency'] ?? 'GTQ') == 'GTQ' ? 'selected' : '' ?>>Quetzal Guatemalteco (Q)</option>
                                            <option value="USD" <?= ($advancedSettings['currency'] ?? '') == 'USD' ? 'selected' : '' ?>>Dólar Americano ($)</option>
                                            <option value="EUR" <?= ($advancedSettings['currency'] ?? '') == 'EUR' ? 'selected' : '' ?>>Euro (€)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Idioma</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="es" <?= ($advancedSettings['language'] ?? 'es') == 'es' ? 'selected' : '' ?>>Español</option>
                                            <option value="en" <?= ($advancedSettings['language'] ?? '') == 'en' ? 'selected' : '' ?>>English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="itemsPerPage" class="form-label">Productos por Página</label>
                                        <select class="form-select" id="itemsPerPage" name="products_per_page">
                                            <option value="12" <?= ($advancedSettings['products_per_page'] ?? '12') == '12' ? 'selected' : '' ?>>12</option>
                                            <option value="24" <?= ($advancedSettings['products_per_page'] ?? '') == '24' ? 'selected' : '' ?>>24</option>
                                            <option value="36" <?= ($advancedSettings['products_per_page'] ?? '') == '36' ? 'selected' : '' ?>>36</option>
                                            <option value="48" <?= ($advancedSettings['products_per_page'] ?? '') == '48' ? 'selected' : '' ?>>48</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maintenanceMode" class="form-label">Modo Mantenimiento</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="maintenanceMode" name="maintenance_mode"
                                                   <?= ($advancedSettings['maintenance_mode'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="maintenanceMode">
                                                Activar modo mantenimiento
                                            </label>
                                        </div>
                                        <small class="text-muted">Desactiva temporalmente el sitio para mantenimiento</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Guardar Configuración Avanzada
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Currency Tab -->
            <?php if ($activeTab === 'currency'): ?>
            <div class="tab-pane fade show active" id="currency" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-coins me-2"></i>Configuración de Monedas</h5>
                        <small class="text-muted">Configurar múltiples monedas y tipos de cambio</small>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/settings">
                            <input type="hidden" name="action" value="update_currency">
                            <?php
                            $currencySettings = [];
                            if (isset($settings['currency'])) {
                                foreach ($settings['currency'] as $setting) {
                                    $currencySettings[$setting['setting_key']] = $setting['setting_value'];
                                }
                            }
                            ?>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="defaultCurrency" class="form-label">Moneda por Defecto</label>
                                        <select class="form-select" id="defaultCurrency" name="default_currency">
                                            <option value="GTQ" <?= ($currencySettings['default_currency'] ?? 'GTQ') == 'GTQ' ? 'selected' : '' ?>>Quetzales (GTQ)</option>
                                            <option value="USD" <?= ($currencySettings['default_currency'] ?? '') == 'USD' ? 'selected' : '' ?>>Dólares (USD)</option>
                                        </select>
                                        <div class="form-text">Moneda principal del sistema</div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="exchangeRate" class="form-label">Tipo de Cambio USD → GTQ</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Q</span>
                                            <input type="number" class="form-control" id="exchangeRate" name="exchange_rate_usd_to_gtq"
                                                   value="<?= esc($currencySettings['exchange_rate_usd_to_gtq'] ?? '7.75') ?>"
                                                   step="0.01" min="1" max="20" required>
                                        </div>
                                        <div class="form-text">1 USD = X GTQ</div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="showConversion" class="form-label">Mostrar Conversión</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="showConversion" name="show_currency_conversion"
                                                   <?= ($currencySettings['show_currency_conversion'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="showConversion">
                                                Mostrar precios en ambas monedas
                                            </label>
                                        </div>
                                        <div class="form-text">Conversión automática en frontend</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="symbolGTQ" class="form-label">Símbolo Quetzales</label>
                                        <input type="text" class="form-control" id="symbolGTQ" name="currency_symbol_gtq"
                                               value="<?= esc($currencySettings['currency_symbol_gtq'] ?? 'Q') ?>"
                                               maxlength="5" required>
                                        <div class="form-text">Símbolo para mostrar precios en quetzales</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="symbolUSD" class="form-label">Símbolo Dólares</label>
                                        <input type="text" class="form-control" id="symbolUSD" name="currency_symbol_usd"
                                               value="<?= esc($currencySettings['currency_symbol_usd'] ?? '$') ?>"
                                               maxlength="5" required>
                                        <div class="form-text">Símbolo para mostrar precios en dólares</div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Información:</strong> Los productos pueden ser creados en cualquiera de las dos monedas.
                                El sistema mostrará automáticamente la conversión basada en el tipo de cambio configurado.
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Guardar Configuración de Monedas
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Integrations Tab -->
            <?php if ($activeTab === 'integrations'): ?>
            <div class="tab-pane fade show active" id="integrations" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plug me-2"></i>Integraciones de Pago</h5>
                        <small class="text-muted">Configurar integraciones con pasarelas de pago</small>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/system-settings/updateRecurrente">
                            <?php
                            // Obtener configuraciones de Recurrente desde system_settings
                            $db = \Config\Database::connect();
                            $recurrenteSettings = [];
                            try {
                                $settings = $db->query("
                                    SELECT setting_key, setting_value
                                    FROM system_settings
                                    WHERE setting_group = 'integrations' AND setting_key LIKE 'recurrente_%' AND is_active = 1
                                ")->getResultArray();

                                foreach ($settings as $setting) {
                                    $key = str_replace('recurrente_', '', $setting['setting_key']);
                                    $recurrenteSettings[$key] = $setting['setting_value'];
                                }
                            } catch (\Exception $e) {
                                // Valores por defecto si hay error
                                $recurrenteSettings = [
                                    'enabled' => '1',
                                    'mode' => 'test',
                                    'currency' => 'GTQ',
                                    'fee_percentage' => '3.9',
                                    'public_key' => 'pk_test_JRZca6LgYaDcTlT1VwQQfoIfubWg6TyzSgFRNZH7bVUGdzRZY4vZ6xe7C',
                                    'secret_key' => 'sk_test_4taMNsXSHb2KkHMAVSvVmr0mQta9tSAs1Vc2CQVqC0tvkjRnkDHqgRh5v',
                                    'webhook_secret' => '',
                                    'allow_cards' => '1',
                                    'allow_transfers' => '1',
                                    'installments_enabled' => '1',
                                    'installments_min_amount' => '500',
                                    'installments_available' => '3,6,12,18,24'
                                ];
                            }
                            ?>

                            <h6 class="mb-3"><i class="fas fa-credit-card me-2"></i>Recurrente</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="recurrenteEnabled" name="recurrente_enabled"
                                                   <?= ($recurrenteSettings['enabled'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="recurrenteEnabled">
                                                <strong>Habilitar Recurrente</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Activar/desactivar pagos con Recurrente</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recurrenteMode" class="form-label">Modo de Operación</label>
                                        <select class="form-select" id="recurrenteMode" name="recurrente_mode">
                                            <option value="test" <?= ($recurrenteSettings['mode'] ?? 'test') == 'test' ? 'selected' : '' ?>>Pruebas (Test)</option>
                                            <option value="live" <?= ($recurrenteSettings['mode'] ?? '') == 'live' ? 'selected' : '' ?>>Producción (Live)</option>
                                        </select>
                                        <small class="text-muted">Usar modo pruebas para desarrollo</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recurrentePublicKey" class="form-label">Clave Pública</label>
                                        <input type="text" class="form-control" id="recurrentePublicKey" name="recurrente_public_key"
                                               value="<?= esc($recurrenteSettings['public_key'] ?? 'pk_test_JRZca6LgYaDcTlT1VwQQfoIfubWg6TyzSgFRNZH7bVUGdzRZY4vZ6xe7C') ?>"
                                               placeholder="pk_test_...">
                                        <small class="text-muted">Clave pública de Recurrente (visible en frontend)</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recurrenteSecretKey" class="form-label">Clave Secreta</label>
                                        <input type="password" class="form-control" id="recurrenteSecretKey" name="recurrente_secret_key"
                                               value="<?= esc($recurrenteSettings['secret_key'] ?? 'sk_test_4taMNsXSHb2KkHMAVSvVmr0mQta9tSAs1Vc2CQVqC0tvkjRnkDHqgRh5v') ?>"
                                               placeholder="sk_test_...">
                                        <small class="text-muted">Clave secreta de Recurrente (mantener privada)</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recurrenteWebhookSecret" class="form-label">Secreto del Webhook</label>
                                        <input type="password" class="form-control" id="recurrenteWebhookSecret" name="recurrente_webhook_secret"
                                               value="<?= esc($recurrenteSettings['webhook_secret'] ?? '') ?>"
                                               placeholder="whsec_...">
                                        <small class="text-muted">Secreto para verificar webhooks de Recurrente</small>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="recurrenteCurrency" class="form-label">Moneda</label>
                                        <select class="form-select" id="recurrenteCurrency" name="recurrente_currency">
                                            <option value="GTQ" <?= ($recurrenteSettings['currency'] ?? 'GTQ') == 'GTQ' ? 'selected' : '' ?>>Quetzal (GTQ)</option>
                                            <option value="USD" <?= ($recurrenteSettings['currency'] ?? '') == 'USD' ? 'selected' : '' ?>>Dólar (USD)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="recurrenteFeePercentage" class="form-label">Comisión (%)</label>
                                        <input type="number" class="form-control" id="recurrenteFeePercentage" name="recurrente_fee_percentage"
                                               value="<?= esc($recurrenteSettings['fee_percentage'] ?? '3.9') ?>"
                                               step="0.1" min="0" max="10">
                                        <small class="text-muted">Porcentaje de comisión de Recurrente</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Métodos de pago disponibles -->
                            <h6 class="mb-3 mt-4"><i class="fas fa-credit-card me-2"></i>Métodos de Pago Disponibles</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="recurrenteAllowCards" name="recurrente_allow_cards"
                                                   <?= ($recurrenteSettings['allow_cards'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="recurrenteAllowCards">
                                                <strong>Permitir Tarjetas de Crédito/Débito</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Habilitar pagos con tarjetas Visa, Mastercard, etc.</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="recurrenteAllowTransfers" name="recurrente_allow_transfers"
                                                   <?= ($recurrenteSettings['allow_transfers'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="recurrenteAllowTransfers">
                                                <strong>Permitir Transferencias Bancarias</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Habilitar pagos por transferencia bancaria</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Configuración de cuotas -->
                            <h6 class="mb-3 mt-4"><i class="fas fa-calendar-alt me-2"></i>Configuración de Cuotas</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="recurrenteInstallmentsEnabled" name="recurrente_installments_enabled"
                                                   <?= ($recurrenteSettings['installments_enabled'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="recurrenteInstallmentsEnabled">
                                                <strong>Habilitar Cuotas</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Permitir pagos en cuotas</small>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="recurrenteInstallmentsMinAmount" class="form-label">Monto Mínimo para Cuotas</label>
                                        <input type="number" class="form-control" id="recurrenteInstallmentsMinAmount" name="recurrente_installments_min_amount"
                                               value="<?= esc($recurrenteSettings['installments_min_amount'] ?? '500') ?>"
                                               min="0" step="50">
                                        <small class="text-muted">Monto mínimo en GTQ para habilitar cuotas</small>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="recurrenteInstallmentsAvailable" class="form-label">Cuotas Disponibles</label>
                                        <input type="text" class="form-control" id="recurrenteInstallmentsAvailable" name="recurrente_installments_available"
                                               value="<?= esc($recurrenteSettings['installments_available'] ?? '3,6,12,18,24') ?>"
                                               placeholder="3,6,12,18,24">
                                        <small class="text-muted">Número de cuotas separadas por coma</small>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Información de Configuración</h6>
                                <ul class="mb-0">
                                    <li><strong>URL del Webhook:</strong> <code><?= base_url('api/webhooks/recurrente') ?></code></li>
                                    <li><strong>URL de Éxito:</strong> <code><?= base_url('payment/recurrente/success/{order_id}') ?></code></li>
                                    <li><strong>URL de Cancelación:</strong> <code><?= base_url('payment/recurrente/cancel/{order_id}') ?></code></li>
                                </ul>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Guardar Configuración de Recurrente
                                </button>

                                <button type="button" class="btn btn-outline-info ms-2" onclick="testRecurrenteConnection()">
                                    <i class="fas fa-plug me-2"></i>Probar Conexión
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- WhatsApp Tab -->
            <?php if ($activeTab === 'whatsapp'): ?>
            <div class="tab-pane fade show active" id="whatsapp" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fab fa-whatsapp me-2"></i>Configuración de WhatsApp</h5>
                                <small class="text-muted">Configura la integración con WhatsApp para notificaciones automáticas</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings">
                                    <input type="hidden" name="action" value="update_whatsapp">
                                    <?php
                                    // Obtener configuraciones de WhatsApp desde system_settings
                                    $db = \Config\Database::connect();
                                    $whatsappSettings = [];
                                    try {
                                        $settings = $db->query("
                                            SELECT setting_key, setting_value
                                            FROM system_settings
                                            WHERE setting_group = 'whatsapp' AND is_active = 1
                                        ")->getResultArray();

                                        foreach ($settings as $setting) {
                                            $whatsappSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    } catch (\Exception $e) {
                                        // Valores por defecto si hay error
                                        $whatsappSettings = [
                                            'whatsapp_enabled' => '0',
                                            'whatsapp_api_url' => 'http://167.114.111.52/api/sendMessage',
                                            'whatsapp_api_key' => '',
                                            'whatsapp_device_token' => '',
                                            'whatsapp_test_phone' => ''
                                        ];
                                    }
                                    ?>

                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="whatsappEnabled" name="whatsapp_enabled"
                                                   <?= ($whatsappSettings['whatsapp_enabled'] ?? '0') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="whatsappEnabled">
                                                <strong>Habilitar notificaciones WhatsApp</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Activar/desactivar el envío de notificaciones por WhatsApp</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="whatsappApiUrl" class="form-label">URL de la API</label>
                                        <input type="url" class="form-control" id="whatsappApiUrl" name="whatsapp_api_url"
                                               value="<?= esc($whatsappSettings['whatsapp_api_url'] ?? 'http://167.114.111.52/api/sendMessage') ?>"
                                               placeholder="http://ejemplo.com/api/sendMessage">
                                        <small class="text-muted">URL completa del endpoint de la API de WhatsApp</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="whatsappApiKey" class="form-label">API Key</label>
                                                <input type="password" class="form-control" id="whatsappApiKey" name="whatsapp_api_key"
                                                       value="<?= esc($whatsappSettings['whatsapp_api_key'] ?? '') ?>"
                                                       placeholder="Tu API Key">
                                                <small class="text-muted">Clave de autenticación de la API</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="whatsappDeviceToken" class="form-label">Device Token</label>
                                                <input type="text" class="form-control" id="whatsappDeviceToken" name="whatsapp_device_token"
                                                       value="<?= esc($whatsappSettings['whatsapp_device_token'] ?? '') ?>"
                                                       placeholder="Token del dispositivo">
                                                <small class="text-muted">Token del dispositivo WhatsApp configurado</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label for="whatsappTestPhone" class="form-label">Teléfono de Prueba</label>
                                        <input type="tel" class="form-control" id="whatsappTestPhone" name="whatsapp_test_phone"
                                               value="<?= esc($whatsappSettings['whatsapp_test_phone'] ?? '') ?>"
                                               placeholder="+502 1234-5678">
                                        <small class="text-muted">Número para pruebas de conexión (incluir código de país)</small>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle me-2"></i>Estado Actual</h6>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>WhatsApp:</span>
                                            <span class="badge <?= ($whatsappSettings['whatsapp_enabled'] ?? '0') == '1' ? 'bg-success' : 'bg-secondary' ?>">
                                                <?= ($whatsappSettings['whatsapp_enabled'] ?? '0') == '1' ? 'ACTIVADO' : 'DESACTIVADO' ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Configuración
                                        </button>
                                        <button type="button" class="btn btn-outline-success" onclick="testWhatsAppConnection()">
                                            <i class="fas fa-plug me-2"></i>Probar Conexión
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    WhatsApp permite enviar notificaciones automáticas a los clientes sobre el estado de sus pedidos.
                                </p>
                                <hr>
                                <h6>Configuración Requerida</h6>
                                <ul class="small text-muted">
                                    <li><strong>URL de API:</strong> Endpoint del servicio WhatsApp</li>
                                    <li><strong>API Key:</strong> Clave de autenticación</li>
                                    <li><strong>Device Token:</strong> Token del dispositivo</li>
                                </ul>
                                <hr>
                                <div class="text-center">
                                    <a href="/admin/whatsapp" class="btn btn-sm btn-outline-primary">
                                        <i class="fab fa-whatsapp me-1"></i>Gestión Completa
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Estadísticas</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $whatsappStats = $db->query("
                                        SELECT
                                            COUNT(*) as total_messages,
                                            SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_messages,
                                            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages
                                        FROM whatsapp_message_log
                                        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                                    ")->getRow();
                                } catch (\Exception $e) {
                                    $whatsappStats = (object)[
                                        'total_messages' => 0,
                                        'sent_messages' => 0,
                                        'failed_messages' => 0
                                    ];
                                }
                                ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small">Mensajes (30 días):</span>
                                    <span class="badge bg-primary"><?= $whatsappStats->total_messages ?? 0 ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small">Enviados:</span>
                                    <span class="badge bg-success"><?= $whatsappStats->sent_messages ?? 0 ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="small">Fallidos:</span>
                                    <span class="badge bg-danger"><?= $whatsappStats->failed_messages ?? 0 ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Shipping Tab -->
            <?php if ($activeTab === 'shipping'): ?>
            <div class="tab-pane fade show active" id="shipping" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>Configuración de Envíos</h5>
                                <small class="text-muted">Configura los costos y tipos de envío disponibles</small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/admin/settings">
                                    <input type="hidden" name="action" value="update_shipping">
                                    <?php
                                    // Obtener configuraciones de envío desde system_settings
                                    $db = \Config\Database::connect();
                                    $shippingSettings = [];
                                    try {
                                        $settings = $db->query("
                                            SELECT setting_key, setting_value
                                            FROM system_settings
                                            WHERE setting_group = 'shipping' AND is_active = 1
                                        ")->getResultArray();

                                        foreach ($settings as $setting) {
                                            $shippingSettings[$setting['setting_key']] = $setting['setting_value'];
                                        }
                                    } catch (\Exception $e) {
                                        // Valores por defecto si hay error
                                        $shippingSettings = [
                                            'shipping_enabled' => '1',
                                            'free_shipping_threshold' => '500',
                                            'default_shipping_cost' => '25'
                                        ];
                                    }
                                    ?>

                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="shippingEnabled" name="shipping_enabled"
                                                   <?= ($shippingSettings['shipping_enabled'] ?? '1') == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="shippingEnabled">
                                                <strong>Habilitar cálculo de envíos</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Activar/desactivar el cálculo automático de costos de envío</small>
                                    </div>

                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="freeShippingEnabled" name="free_shipping_enabled"
                                                   <?= ($shippingSettings['free_shipping_enabled'] ?? '1') == '1' ? 'checked' : '' ?>
                                                   onchange="toggleFreeShipping()">
                                            <label class="form-check-label" for="freeShippingEnabled">
                                                <strong>Habilitar envío gratis</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Activar/desactivar la opción de envío gratuito</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="freeShippingThreshold" class="form-label">Envío Gratis Desde (Q)</label>
                                                <input type="number" class="form-control" id="freeShippingThreshold" name="free_shipping_threshold"
                                                       value="<?= esc($shippingSettings['free_shipping_threshold'] ?? '500') ?>"
                                                       step="0.01" min="0"
                                                       <?= ($shippingSettings['free_shipping_enabled'] ?? '1') == '0' ? 'disabled' : '' ?>>
                                                <small class="text-muted">Monto mínimo para envío gratuito</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="defaultShippingCost" class="form-label">Costo de Envío por Defecto (Q)</label>
                                                <input type="number" class="form-control" id="defaultShippingCost" name="default_shipping_cost"
                                                       value="<?= esc($shippingSettings['default_shipping_cost'] ?? '25') ?>"
                                                       step="0.01" min="0">
                                                <small class="text-muted">Costo cuando no se puede calcular automáticamente</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle me-2"></i>Estado Actual</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>Envíos:</span>
                                            <span class="badge <?= ($shippingSettings['shipping_enabled'] ?? '1') == '1' ? 'bg-success' : 'bg-secondary' ?>">
                                                <?= ($shippingSettings['shipping_enabled'] ?? '1') == '1' ? 'ACTIVADO' : 'DESACTIVADO' ?>
                                            </span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>Envío Gratis:</span>
                                            <span class="badge bg-primary">Desde Q<?= number_format($shippingSettings['free_shipping_threshold'] ?? 500, 2) ?></span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Guardar Configuración
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Gestión de Tipos de Paquetes -->
                        <div class="card mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>Tipos de Paquetes</h5>
                                    <small class="text-muted">Gestiona los diferentes tipos de paquetes y sus costos</small>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="openPackageTypeModal()">
                                    <i class="fas fa-plus me-1"></i>Agregar Tipo
                                </button>
                            </div>
                            <div class="card-body">
                                <?php
                                // Obtener tipos de paquetes
                                try {
                                    $packageTypes = $db->query("
                                        SELECT * FROM shipping_package_types
                                        WHERE is_active = 1
                                        ORDER BY sort_order ASC, name ASC
                                    ")->getResultArray();
                                } catch (\Exception $e) {
                                    $packageTypes = [];
                                }
                                ?>

                                <?php if (empty($packageTypes)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No hay tipos de paquetes configurados</p>
                                        <button type="button" class="btn btn-primary" onclick="openPackageTypeModal()">
                                            <i class="fas fa-plus me-2"></i>Agregar Primer Tipo de Paquete
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Tipo</th>
                                                    <th>Dimensiones Máx.</th>
                                                    <th>Peso Máx.</th>
                                                    <th>Costo Base</th>
                                                    <th>Costo/Km</th>
                                                    <th>Acciones</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($packageTypes as $type): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= esc($type['name']) ?></strong>
                                                        <?php if (!empty($type['description'])): ?>
                                                            <br><small class="text-muted"><?= esc($type['description']) ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?= number_format($type['max_length_cm'], 0) ?>cm ×
                                                        <?= number_format($type['max_width_cm'], 0) ?>cm ×
                                                        <?= number_format($type['max_height_cm'], 0) ?>cm
                                                    </td>
                                                    <td><?= number_format($type['max_weight_lbs'], 1) ?> lbs</td>
                                                    <td>Q<?= number_format($type['base_cost'], 2) ?></td>
                                                    <td>Q<?= number_format($type['cost_per_km'], 2) ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="editPackageType(<?= $type['id'] ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deletePackageType(<?= $type['id'] ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    El sistema de envíos calcula automáticamente el costo basado en las dimensiones y peso de los productos.
                                </p>
                                <hr>
                                <h6>Cómo Funciona</h6>
                                <ul class="small text-muted">
                                    <li><strong>Tipos de Paquetes:</strong> Define diferentes categorías según tamaño y peso</li>
                                    <li><strong>Cálculo Automático:</strong> El sistema selecciona el tipo apropiado</li>
                                    <li><strong>Zonas de Envío:</strong> Costos adicionales por ubicación</li>
                                    <li><strong>Envío Gratis:</strong> Automático al superar el monto mínimo</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>Calculadora de Envío</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Dimensiones (cm)</label>
                                    <div class="row">
                                        <div class="col-4">
                                            <input type="number" class="form-control form-control-sm" id="calcLength" placeholder="Largo" min="0">
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control form-control-sm" id="calcWidth" placeholder="Ancho" min="0">
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control form-control-sm" id="calcHeight" placeholder="Alto" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Peso (lbs)</label>
                                    <input type="number" class="form-control form-control-sm" id="calcWeight" placeholder="0.0" step="0.1" min="0">
                                </div>
                                <button type="button" class="btn btn-sm btn-primary w-100" onclick="calculateShipping()">
                                    <i class="fas fa-calculator me-1"></i>Calcular
                                </button>
                                <div id="shippingResult" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <strong>Tipo de Paquete:</strong> <span id="resultPackageType"></span><br>
                                        <strong>Costo Base:</strong> <span id="resultCost"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div> <!-- End tab-content -->
    </div> <!-- End card-body -->
</div> <!-- End card -->
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .nav-tabs-line {
        border-bottom: 2px solid #e9ecef;
    }

    .nav-tabs-line .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        font-weight: 500;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
    }

    .nav-tabs-line .nav-link:hover {
        border-color: transparent;
        color: #495057;
        background-color: #f8f9fa;
    }

    .nav-tabs-line .nav-link.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background-color: transparent;
    }

    .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para configuración del sistema
    console.log('Configuración del sistema cargada');

    // Handle tab switching with URL update
    document.addEventListener('DOMContentLoaded', function() {
        // Get current tab from URL or default to general
        const urlParams = new URLSearchParams(window.location.search);
        const currentTab = urlParams.get('tab') || 'general';

        // Update active tab styling
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeTabLink = document.querySelector(`[href="/admin/settings?tab=${currentTab}"]`);
        if (activeTabLink) {
            activeTabLink.classList.add('active');
        }
    });

    // Simulador de IVA
    document.addEventListener('DOMContentLoaded', function() {
        const priceInput = document.getElementById('simulatorPrice');
        const taxEnabledCheckbox = document.getElementById('taxEnabled');
        const taxRateInput = document.getElementById('taxRate');
        const taxNameInput = document.getElementById('taxName');

        if (priceInput && taxEnabledCheckbox && taxRateInput && taxNameInput) {
            function updateSimulator() {
                const price = parseFloat(priceInput.value) || 0;
                const taxEnabled = taxEnabledCheckbox.checked;
                const taxRate = parseFloat(taxRateInput.value) || 0;
                const taxName = taxNameInput.value || 'IVA';

                const subtotal = price;
                const tax = taxEnabled ? (subtotal * taxRate / 100) : 0;
                const total = subtotal + tax;

                document.getElementById('simSubtotal').textContent = 'Q' + subtotal.toFixed(2);
                document.getElementById('simTaxLabel').textContent = taxName + ' (' + taxRate + '%):';
                document.getElementById('simTax').textContent = 'Q' + tax.toFixed(2);
                document.getElementById('simTotal').textContent = 'Q' + total.toFixed(2);

                // Mostrar/ocultar línea de impuesto
                const taxRow = document.getElementById('simTax').parentElement;
                taxRow.style.display = taxEnabled ? 'flex' : 'none';
            }

            // Event listeners
            priceInput.addEventListener('input', updateSimulator);
            taxEnabledCheckbox.addEventListener('change', updateSimulator);
            taxRateInput.addEventListener('input', updateSimulator);
            taxNameInput.addEventListener('input', updateSimulator);

            // Inicializar
            updateSimulator();
        }
    });

    // Función para probar conexión con Recurrente
    function testRecurrenteConnection() {
        const btn = event.target;
        const originalText = btn.innerHTML;

        // Mostrar loading
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Probando...';
        btn.disabled = true;

        // Obtener configuraciones del formulario
        const config = {
            public_key: document.getElementById('recurrentePublicKey').value,
            secret_key: document.getElementById('recurrenteSecretKey').value,
            mode: document.getElementById('recurrenteMode').value
        };

        // Hacer petición de prueba
        fetch('/api/payments/recurrente/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('success', 'Conexión exitosa con Recurrente');
            } else {
                showAlert('error', 'Error de conexión: ' + (data.message || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error al probar la conexión');
        })
        .finally(() => {
            // Restaurar botón
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }

    // Función para probar conexión con WhatsApp
    function testWhatsAppConnection() {
        const btn = event.target;
        const originalText = btn.innerHTML;

        // Validar que haya un teléfono de prueba
        const testPhone = document.getElementById('whatsappTestPhone').value;
        if (!testPhone) {
            showAlert('error', 'Por favor ingresa un número de teléfono de prueba');
            return;
        }

        // Mostrar loading
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Probando...';
        btn.disabled = true;

        // Obtener configuraciones del formulario
        const config = {
            api_url: document.getElementById('whatsappApiUrl').value,
            api_key: document.getElementById('whatsappApiKey').value,
            device_token: document.getElementById('whatsappDeviceToken').value,
            test_phone: testPhone
        };

        // Hacer petición de prueba
        fetch('/admin/whatsapp/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Mensaje de prueba enviado correctamente a WhatsApp');
            } else {
                showAlert('error', 'Error al enviar mensaje: ' + (data.error || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error al probar la conexión con WhatsApp');
        })
        .finally(() => {
            // Restaurar botón
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }

    // Función para calcular envío
    function calculateShipping() {
        const length = parseFloat(document.getElementById('calcLength').value) || 0;
        const width = parseFloat(document.getElementById('calcWidth').value) || 0;
        const height = parseFloat(document.getElementById('calcHeight').value) || 0;
        const weight = parseFloat(document.getElementById('calcWeight').value) || 0;

        if (length === 0 || width === 0 || height === 0 || weight === 0) {
            showAlert('error', 'Por favor ingresa todas las dimensiones y el peso');
            return;
        }

        // Hacer petición para calcular el envío
        fetch('/admin/api/calculate-shipping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                length: length,
                width: width,
                height: height,
                weight: weight
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('resultPackageType').textContent = data.package_type;
                document.getElementById('resultCost').textContent = 'Q' + data.cost.toFixed(2);
                document.getElementById('shippingResult').style.display = 'block';
            } else {
                showAlert('error', 'Error al calcular envío: ' + (data.error || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Error al calcular el envío');
        });
    }

    // Función para abrir modal de tipo de paquete
    function openPackageTypeModal(id = null) {
        // Esta función se implementará cuando se cree el modal
        showAlert('info', 'Funcionalidad de gestión de tipos de paquetes en desarrollo');
    }

    // Función para editar tipo de paquete
    function editPackageType(id) {
        openPackageTypeModal(id);
    }

    // Función para eliminar tipo de paquete
    function deletePackageType(id) {
        if (confirm('¿Estás seguro de que deseas eliminar este tipo de paquete?')) {
            fetch(`/admin/api/shipping/package-types/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'Tipo de paquete eliminado correctamente');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('error', 'Error al eliminar: ' + (data.error || 'Error desconocido'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'Error al eliminar el tipo de paquete');
            });
        }
    }

    // Función para toggle de envío gratis
    function toggleFreeShipping() {
        const checkbox = document.getElementById('freeShippingEnabled');
        const thresholdInput = document.getElementById('freeShippingThreshold');

        if (checkbox.checked) {
            thresholdInput.disabled = false;
            thresholdInput.style.opacity = '1';
        } else {
            thresholdInput.disabled = true;
            thresholdInput.style.opacity = '0.5';
        }
    }

    // Inicializar estado al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        toggleFreeShipping();
    });

    // Función para mostrar alertas
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insertar alerta al inicio del contenido
        const content = document.querySelector('.page-header').nextElementSibling;
        content.insertAdjacentHTML('beforebegin', alertHtml);

        // Auto-remover después de 5 segundos
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
</script>
<?= $this->endSection() ?>
