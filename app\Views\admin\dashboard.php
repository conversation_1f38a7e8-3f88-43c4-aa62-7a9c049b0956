<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Dashboard Administrativo - MrCell' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --sidebar-bg: #343a40;
            --sidebar-width: 250px;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 20px;
            background: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li a {
            display: block;
            padding: 15px 20px;
            color: #adb5bd;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-left-color: var(--primary-color);
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 30px;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stats-icon.users { background: linear-gradient(135deg, #667eea, #764ba2); }
        .stats-icon.orders { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .stats-icon.products { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .stats-icon.revenue { background: linear-gradient(135deg, #43e97b, #38f9d7); }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #495057;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .recent-orders {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-mobile-alt me-2"></i>MrCell Admin</h4>
            <small>Panel de Administración</small>
        </div>
        
        <ul class="sidebar-menu">
            <li><a href="<?= base_url('admin') ?>" class="active"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="<?= base_url('admin/products') ?>"><i class="fas fa-box me-2"></i>Productos</a></li>
            <li><a href="<?= base_url('admin/orders') ?>"><i class="fas fa-shopping-cart me-2"></i>Pedidos</a></li>
            <li><a href="<?= base_url('admin/users') ?>"><i class="fas fa-users me-2"></i>Usuarios</a></li>
            <li><a href="<?= base_url('admin/categories') ?>"><i class="fas fa-tags me-2"></i>Categorías</a></li>
            <li><a href="<?= base_url('admin/reports') ?>"><i class="fas fa-chart-bar me-2"></i>Reportes</a></li>
            <li><a href="<?= base_url('admin/settings') ?>"><i class="fas fa-cog me-2"></i>Configuración</a></li>
        </ul>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Dashboard</h2>
                <small class="text-muted">Bienvenido al panel de administración</small>
            </div>
            
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?= $admin_user['nombre'] ?? 'Administrador' ?>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?= base_url('admin/profile') ?>"><i class="fas fa-user me-2"></i>Mi Perfil</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?= base_url('admin/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <div class="container-fluid">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <div class="stats-number"><?= number_format($stats['total_users']) ?></div>
                                <div class="stats-label">Usuarios</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="ms-3">
                                <div class="stats-number"><?= number_format($stats['total_orders']) ?></div>
                                <div class="stats-label">Pedidos</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="ms-3">
                                <div class="stats-number"><?= number_format($stats['total_products']) ?></div>
                                <div class="stats-label">Productos</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon revenue">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="ms-3">
                                <div class="stats-number">Q<?= number_format($stats['total_revenue'], 2) ?></div>
                                <div class="stats-label">Ingresos</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Productos Próximos a Caducar -->
            <?php if (isset($expiring_products) && count($expiring_products) > 0): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="stats-card">
                        <div class="card-header bg-warning bg-opacity-10 border-bottom">
                            <h5 class="mb-0 text-warning">
                                <i class="fas fa-calendar-times me-2"></i>
                                Productos Próximos a Caducar
                                <span class="badge bg-warning text-dark ms-2"><?= count($expiring_products) ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($expiration_summary)): ?>
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-danger mb-0"><?= $expiration_summary['expired'] ?? 0 ?></div>
                                            <small class="text-muted">Caducados</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-warning mb-0"><?= $expiration_summary['expires_today'] ?? 0 ?></div>
                                            <small class="text-muted">Caducan hoy</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-info mb-0"><?= $expiration_summary['expires_this_week'] ?? 0 ?></div>
                                            <small class="text-muted">Esta semana</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-secondary mb-0"><?= $expiration_summary['expires_this_month'] ?? 0 ?></div>
                                            <small class="text-muted">Este mes</small>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                            <?php endif; ?>

                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Producto</th>
                                            <th>SKU</th>
                                            <th>Fecha Caducidad</th>
                                            <th>Días Restantes</th>
                                            <th>Stock</th>
                                            <th>Estado</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($expiring_products, 0, 10) as $product): ?>
                                        <tr class="<?= $product['expiration_status'] == 'expired' ? 'table-danger' : ($product['expiration_status'] == 'expires_today' ? 'table-warning' : '') ?>">
                                            <td>
                                                <strong><?= esc($product['name']) ?></strong>
                                            </td>
                                            <td><?= esc($product['sku']) ?></td>
                                            <td><?= date('d/m/Y', strtotime($product['expiration_date'])) ?></td>
                                            <td>
                                                <?php if ($product['days_until_expiration'] < 0): ?>
                                                    <span class="text-danger">Caducado hace <?= abs($product['days_until_expiration']) ?> días</span>
                                                <?php elseif ($product['days_until_expiration'] == 0): ?>
                                                    <span class="text-warning">Caduca hoy</span>
                                                <?php else: ?>
                                                    <span class="text-info"><?= $product['days_until_expiration'] ?> días</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?= $product['stock_quantity'] > 0 ? 'bg-success' : 'bg-danger' ?>">
                                                    <?= $product['stock_quantity'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($product['expiration_status'] == 'expired'): ?>
                                                    <span class="badge bg-danger">Caducado</span>
                                                <?php elseif ($product['expiration_status'] == 'expires_today'): ?>
                                                    <span class="badge bg-warning">Caduca hoy</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">Próximo</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <?php if (count($expiring_products) > 10): ?>
                                <div class="text-center mt-3">
                                    <a href="/admin/inventory?filter=expiring" class="btn btn-outline-warning">
                                        Ver todos los productos próximos a caducar (<?= count($expiring_products) ?>)
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Orders -->
            <div class="row">
                <div class="col-12">
                    <div class="recent-orders">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Pedidos Recientes</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($stats['recent_orders'])): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Total</th>
                                                <th>Estado</th>
                                                <th>Fecha</th>
                                                <th>Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats['recent_orders'] as $order): ?>
                                                <tr>
                                                    <td>#<?= $order['id'] ?></td>
                                                    <td><?= $order['user_name'] ?? 'Usuario eliminado' ?></td>
                                                    <td>Q<?= number_format($order['total'], 2) ?></td>
                                                    <td>
                                                        <span class="badge bg-<?= $order['status'] === 'pending' ? 'warning' : ($order['status'] === 'completed' ? 'success' : 'secondary') ?>">
                                                            <?= ucfirst($order['status']) ?>
                                                        </span>
                                                    </td>
                                                    <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                    <td>
                                                        <a href="<?= base_url('admin/orders/' . $order['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No hay pedidos recientes</h5>
                                    <p class="text-muted">Los pedidos aparecerán aquí cuando los clientes realicen compras.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            // TODO: Implement AJAX refresh of stats
            console.log('Refreshing stats...');
        }, 30000);
    </script>
</body>
</html>
