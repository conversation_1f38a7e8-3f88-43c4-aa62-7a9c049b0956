<?php

namespace App\Models;

use CodeIgniter\Model;

class CategoryModel extends Model
{
    protected $table            = 'categories';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'slug',
        'description',
        'parent_id',
        'image',
        'icon',
        'sort_order',
        'is_active',
        'meta_title',
        'meta_description',
        'meta_keywords'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'name' => 'required|min_length[2]|max_length[255]',
        'slug' => 'required|min_length[2]|max_length[255]|is_unique[categories.slug,id,{id}]',
        'parent_id' => 'permit_empty|integer',
        'is_active' => 'permit_empty|in_list[0,1]',
        'sort_order' => 'permit_empty|integer'
    ];
    
    protected $validationMessages   = [
        'name' => [
            'required' => 'El nombre de la categoría es requerido',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 255 caracteres'
        ],
        'slug' => [
            'required' => 'El slug es requerido',
            'is_unique' => 'Este slug ya está en uso'
        ],
        'is_active' => [
            'in_list' => 'El estado debe ser 0 (inactivo) o 1 (activo)'
        ]
    ];
    
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateSlug'];
    protected $beforeUpdate   = ['generateSlug'];

    /**
     * Generate slug from name if not provided
     */
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = url_title($data['data']['name'], '-', true);
        }
        return $data;
    }

    /**
     * Get categories with hierarchy
     */
    public function getCategoriesWithHierarchy($parentId = null, $level = 0)
    {
        $categories = $this->where('parent_id', $parentId)
                          ->where('is_active', 1)
                          ->orderBy('sort_order', 'ASC')
                          ->orderBy('name', 'ASC')
                          ->findAll();

        $result = [];
        foreach ($categories as $category) {
            $category['level'] = $level;
            $category['indent'] = str_repeat('— ', $level);
            $result[] = $category;
            
            // Get children recursively
            $children = $this->getCategoriesWithHierarchy($category['id'], $level + 1);
            $result = array_merge($result, $children);
        }

        return $result;
    }

    /**
     * Get category tree for dropdown
     */
    public function getCategoryTree($selectedId = null, $excludeId = null)
    {
        $categories = $this->getCategoriesWithHierarchy();
        $options = ['0' => 'Sin categoría padre'];

        foreach ($categories as $category) {
            // Exclude the category being edited to prevent circular reference
            if ($excludeId && $category['id'] == $excludeId) {
                continue;
            }

            $options[$category['id']] = $category['indent'] . $category['name'];
        }

        return $options;
    }

    /**
     * Get category breadcrumb
     */
    public function getBreadcrumb($categoryId)
    {
        $breadcrumb = [];
        $category = $this->find($categoryId);

        while ($category) {
            array_unshift($breadcrumb, $category);
            $category = $category['parent_id'] ? $this->find($category['parent_id']) : null;
        }

        return $breadcrumb;
    }

    /**
     * Get all child category IDs
     */
    public function getChildCategoryIds($parentId)
    {
        $childIds = [$parentId];
        $children = $this->where('parent_id', $parentId)->findAll();

        foreach ($children as $child) {
            $childIds = array_merge($childIds, $this->getChildCategoryIds($child['id']));
        }

        return $childIds;
    }

    /**
     * Get categories with product count
     */
    public function getCategoriesWithProductCount()
    {
        return $this->select('categories.*, COUNT(products.id) as product_count')
                   ->join('products', 'products.category_id = categories.id', 'left')
                   ->groupBy('categories.id')
                   ->orderBy('categories.sort_order', 'ASC')
                   ->orderBy('categories.name', 'ASC')
                   ->findAll();
    }

    /**
     * Update category sort order
     */
    public function updateSortOrder($categoryId, $sortOrder)
    {
        return $this->update($categoryId, ['sort_order' => $sortOrder]);
    }

    /**
     * Check if category has children
     */
    public function hasChildren($categoryId)
    {
        return $this->where('parent_id', $categoryId)->countAllResults() > 0;
    }

    /**
     * Get popular categories (with most products)
     */
    public function getPopularCategories($limit = 10)
    {
        return $this->select('categories.*, COUNT(products.id) as product_count')
                   ->join('products', 'products.category_id = categories.id', 'left')
                   ->where('categories.is_active', 1)
                   ->groupBy('categories.id')
                   ->having('product_count >', 0)
                   ->orderBy('product_count', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Search categories
     */
    public function searchCategories($term, $limit = 20)
    {
        return $this->like('name', $term)
                   ->orLike('description', $term)
                   ->where('is_active', 1)
                   ->orderBy('name', 'ASC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get category statistics
     */
    public function getCategoryStats()
    {
        $stats = [
            'total_categories' => $this->countAllResults(),
            'active_categories' => $this->where('is_active', 1)->countAllResults(),
            'inactive_categories' => $this->where('is_active', 0)->countAllResults(),
            'parent_categories' => $this->where('parent_id', null)->countAllResults(),
            'child_categories' => $this->where('parent_id !=', null)->countAllResults()
        ];

        return $stats;
    }

    /**
     * Validate category hierarchy (prevent circular references)
     */
    public function validateHierarchy($categoryId, $parentId)
    {
        if (!$parentId) {
            return true;
        }

        // Check if parent is the same as category
        if ($categoryId == $parentId) {
            return false;
        }

        // Check if parent is a child of this category
        $childIds = $this->getChildCategoryIds($categoryId);
        return !in_array($parentId, $childIds);
    }

    /**
     * Bulk update category status
     */
    public function bulkUpdateStatus($categoryIds, $isActive)
    {
        return $this->whereIn('id', $categoryIds)->set(['is_active' => $isActive])->update();
    }

    /**
     * Get categories for sitemap
     */
    public function getCategoriesForSitemap()
    {
        return $this->select('id, slug, updated_at')
                   ->where('is_active', 1)
                   ->orderBy('updated_at', 'DESC')
                   ->findAll();
    }
}
