<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Finalizar Compra - MrCell Guatemala<?= $this->endSection() ?>

<?= $this->section('content') ?>
    
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
        }
        
        .checkout-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 25px;
            margin: 0 10px;
            color: #6c757d;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .checkout-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            position: sticky;
            top: 100px;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 15px;
        }
        
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover,
        .payment-method.selected {
            border-color: var(--primary-color);
            background: rgba(0, 123, 255, 0.05);
        }

        .installment-plan-option {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .installment-plan-option:hover {
            border-color: var(--primary-color);
            background: rgba(0, 123, 255, 0.02);
        }

        .installment-plan-option input[type="radio"]:checked + label {
            color: var(--primary-color);
        }

        .installment-amount {
            font-size: 1.1em;
        }

        #installment-plans {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .btn-place-order {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-place-order:hover {
            background: var(--primary-dark);
            color: white;
        }
    </style>
</head>
<body>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-mobile-alt me-2"></i>MrCell
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-lock me-2"></i>Checkout Seguro
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-4">
        <!-- Checkout Steps -->
        <div class="checkout-steps">
            <div class="step completed">
                <i class="fas fa-shopping-cart me-2"></i>Carrito
            </div>
            <div class="step active">
                <i class="fas fa-credit-card me-2"></i>Checkout
            </div>
            <div class="step">
                <i class="fas fa-check-circle me-2"></i>Confirmación
            </div>
        </div>

        <div class="row">
            <!-- Checkout Form -->
            <div class="col-lg-8">
                <form id="checkout-form">
                    <!-- Customer Information -->
                    <div class="checkout-form">
                        <h4 class="mb-4"><i class="fas fa-user me-2"></i>Información del Cliente</h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_name" class="form-label">Nombre Completo *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_phone" class="form-label">Teléfono *</label>
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_nit" class="form-label">NIT (Opcional)</label>
                                <input type="text" class="form-control" id="customer_nit" name="customer_nit">
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="checkout-form">
                        <h4 class="mb-4"><i class="fas fa-truck me-2"></i>Dirección de Envío</h4>
                        
                        <div class="mb-3">
                            <label for="shipping_address" class="form-label">Dirección Completa *</label>
                            <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3" required placeholder="Incluye zona, colonia, referencias, etc."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">Ciudad *</label>
                                <select class="form-control" id="city" name="city" required>
                                    <option value="">Seleccionar ciudad</option>
                                    <option value="Guatemala">Ciudad de Guatemala</option>
                                    <option value="Mixco">Mixco</option>
                                    <option value="Villa Nueva">Villa Nueva</option>
                                    <option value="Antigua">Antigua Guatemala</option>
                                    <option value="Quetzaltenango">Quetzaltenango</option>
                                    <option value="Escuintla">Escuintla</option>
                                    <option value="Otra">Otra ciudad</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">Departamento *</label>
                                <select class="form-control" id="department" name="department" required>
                                    <option value="">Seleccionar departamento</option>
                                    <option value="Guatemala">Guatemala</option>
                                    <option value="Sacatepéquez">Sacatepéquez</option>
                                    <option value="Quetzaltenango">Quetzaltenango</option>
                                    <option value="Escuintla">Escuintla</option>
                                    <option value="Otro">Otro</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="checkout-form">
                        <h4 class="mb-4"><i class="fas fa-credit-card me-2"></i>Método de Pago</h4>
                        
                        <div class="payment-method" onclick="selectPaymentMethod('cash')">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_cash" value="cash">
                                <label class="form-check-label" for="payment_cash">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-money-bill-wave fa-2x text-success me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Pago Contra Entrega</h6>
                                            <small class="text-muted">Paga en efectivo cuando recibas tu pedido</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="payment-method" onclick="selectPaymentMethod('transfer')">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_transfer" value="transfer">
                                <label class="form-check-label" for="payment_transfer">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-university fa-2x text-primary me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Transferencia Bancaria</h6>
                                            <small class="text-muted">Transfiere a nuestra cuenta bancaria</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="payment-method" onclick="selectPaymentMethod('card')">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_card" value="card">
                                <label class="form-check-label" for="payment_card">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-credit-card fa-2x text-warning me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Tarjeta de Crédito/Débito</h6>
                                            <small class="text-muted">Visa, Mastercard, American Express - Pago único</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Installment Payment Options -->
                        <div class="payment-method" onclick="selectPaymentMethod('installments')">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_installments" value="installments">
                                <label class="form-check-label" for="payment_installments">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar-alt fa-2x text-info me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Pago a Plazos con Tarjeta</h6>
                                            <small class="text-muted">Divide tu pago en cuotas mensuales</small>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <!-- Installment Plans (shown when selected) -->
                            <div id="installment-plans" class="mt-3" style="display: none;">
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="mb-3">Selecciona tu plan de cuotas:</h6>
                                    </div>
                                </div>

                                <div class="installment-plan-option mb-2" data-plan="3" data-interest="0" data-fee="25">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="installment_plan" id="plan_3" value="3">
                                        <label class="form-check-label w-100" for="plan_3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>3 Cuotas sin interés</strong>
                                                    <br><small class="text-muted">Comisión: Q25.00 | Mínimo: Q500</small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="installment-amount text-primary fw-bold">Q0.00</span>
                                                    <br><small class="text-muted">por mes</small>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="installment-plan-option mb-2" data-plan="6" data-interest="0.02" data-fee="50">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="installment_plan" id="plan_6" value="6">
                                        <label class="form-check-label w-100" for="plan_6">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>6 Cuotas - 2% mensual</strong>
                                                    <br><small class="text-muted">Comisión: Q50.00 | Mínimo: Q1,000</small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="installment-amount text-primary fw-bold">Q0.00</span>
                                                    <br><small class="text-muted">por mes</small>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="installment-plan-option mb-2" data-plan="9" data-interest="0.025" data-fee="75">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="installment_plan" id="plan_9" value="9">
                                        <label class="form-check-label w-100" for="plan_9">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>9 Cuotas - 2.5% mensual</strong>
                                                    <br><small class="text-muted">Comisión: Q75.00 | Mínimo: Q2,000</small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="installment-amount text-primary fw-bold">Q0.00</span>
                                                    <br><small class="text-muted">por mes</small>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="installment-plan-option mb-2" data-plan="12" data-interest="0.03" data-fee="100">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="installment_plan" id="plan_12" value="12">
                                        <label class="form-check-label w-100" for="plan_12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>12 Cuotas - 3% mensual</strong>
                                                    <br><small class="text-muted">Comisión: Q100.00 | Mínimo: Q3,000</small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="installment-amount text-primary fw-bold">Q0.00</span>
                                                    <br><small class="text-muted">por mes</small>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        Los pagos se procesarán automáticamente cada mes en la misma fecha.
                                        Puedes cancelar anticipadamente sin penalización.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Notes -->
                    <div class="checkout-form">
                        <h4 class="mb-4"><i class="fas fa-sticky-note me-2"></i>Notas del Pedido (Opcional)</h4>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Instrucciones especiales para la entrega, referencias adicionales, etc."></textarea>
                    </div>
                </form>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="order-summary">
                    <h4 class="mb-4"><i class="fas fa-receipt me-2"></i>Resumen del Pedido</h4>
                    
                    <!-- Order Items -->
                    <div id="order-items">
                        <!-- Items will be loaded here -->
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="text-muted mt-2">Cargando items...</p>
                        </div>
                    </div>
                    
                    <!-- Order Totals -->
                    <div class="border-top pt-3 mt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="subtotal">Q0.00</span>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>Envío:</span>
                            <span id="shipping">Q50.00</span>
                        </div>
                        
                        <!-- IVA will be shown only if enabled in admin settings -->
                        <div class="d-flex justify-content-between mb-2" id="tax-row" style="display: <?= ($tax_settings['tax_enabled'] ?? false) ? 'flex' : 'none' ?>;">
                            <span id="tax-label"><?= ($tax_settings['tax_name'] ?? 'IVA') ?> (<?= ($tax_settings['tax_rate'] ?? 12) ?>%):</span>
                            <span id="tax">Q0.00</span>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong class="text-primary" id="total">Q0.00</strong>
                        </div>
                    </div>
                    
                    <!-- Place Order Button -->
                    <button type="button" class="btn btn-place-order" onclick="placeOrder()">
                        <i class="fas fa-lock me-2"></i>Realizar Pedido
                    </button>
                    
                    <!-- Security Info -->
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Tus datos están protegidos con encriptación SSL
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Select payment method
        function selectPaymentMethod(method) {
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('payment_' + method).checked = true;

            // Show/hide installment plans
            const installmentPlans = document.getElementById('installment-plans');
            if (method === 'installments') {
                installmentPlans.style.display = 'block';
                calculateInstallmentAmounts();
            } else {
                installmentPlans.style.display = 'none';
            }
        }

        // Calculate installment amounts based on order total
        function calculateInstallmentAmounts() {
            const orderTotal = parseFloat(document.getElementById('total')?.textContent?.replace('Q', '').replace(',', '') || 0);

            document.querySelectorAll('.installment-plan-option').forEach(option => {
                const installments = parseInt(option.dataset.plan);
                const interestRate = parseFloat(option.dataset.interest);
                const processingFee = parseFloat(option.dataset.fee);

                // Calculate monthly payment with compound interest
                let monthlyPayment;
                if (interestRate === 0) {
                    // No interest
                    monthlyPayment = (orderTotal + processingFee) / installments;
                } else {
                    // With interest - compound interest formula
                    const principal = orderTotal + processingFee;
                    const monthlyRate = interestRate;
                    monthlyPayment = (principal * monthlyRate * Math.pow(1 + monthlyRate, installments)) /
                                   (Math.pow(1 + monthlyRate, installments) - 1);
                }

                const amountSpan = option.querySelector('.installment-amount');
                amountSpan.textContent = 'Q' + monthlyPayment.toFixed(2);

                // Disable option if order total is below minimum
                const minAmount = getMinAmountForPlan(installments);
                const radioInput = option.querySelector('input[type="radio"]');
                const label = option.querySelector('label');

                if (orderTotal < minAmount) {
                    radioInput.disabled = true;
                    label.classList.add('text-muted');
                    option.style.opacity = '0.5';
                } else {
                    radioInput.disabled = false;
                    label.classList.remove('text-muted');
                    option.style.opacity = '1';
                }
            });
        }

        // Get minimum amount for installment plan
        function getMinAmountForPlan(installments) {
            const minAmounts = {
                3: 500,
                6: 1000,
                9: 2000,
                12: 3000
            };
            return minAmounts[installments] || 0;
        }
        
        // Place order
        function placeOrder() {
            const form = document.getElementById('checkout-form');
            const formData = new FormData(form);

            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Check payment method
            const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
            if (!paymentMethod) {
                alert('Por favor selecciona un método de pago');
                return;
            }

            // Validate installment plan if selected
            if (paymentMethod.value === 'installments') {
                const installmentPlan = document.querySelector('input[name="installment_plan"]:checked');
                if (!installmentPlan) {
                    alert('Por favor selecciona un plan de cuotas');
                    return;
                }

                // Check if order meets minimum amount
                const orderTotal = parseFloat(document.getElementById('order-total')?.textContent?.replace('Q', '').replace(',', '') || 0);
                const minAmount = getMinAmountForPlan(parseInt(installmentPlan.value));

                if (orderTotal < minAmount) {
                    alert(`El monto mínimo para ${installmentPlan.value} cuotas es Q${minAmount.toFixed(2)}`);
                    return;
                }
            }

            const button = document.querySelector('.btn-place-order');
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Procesando...';
            button.disabled = true;

            // TODO: Implement actual order placement
            setTimeout(() => {
                if (paymentMethod.value === 'installments') {
                    alert('¡Pedido realizado exitosamente! Se procesará el primer pago y se configurarán las cuotas automáticas.');
                } else {
                    alert('¡Pedido realizado exitosamente!');
                }
                window.location.href = '<?= base_url() ?>';
            }, 2000);
        }
        
        // Load cart items on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadOrderSummary();
        });
        
        function loadOrderSummary() {
            // TODO: Load actual cart items
            const orderItemsContainer = document.getElementById('order-items');
            orderItemsContainer.innerHTML = `
                <div class="order-item">
                    <img src="https://via.placeholder.com/60x60" alt="Producto" class="item-image">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">iPhone 15 Pro</h6>
                        <small class="text-muted">Cantidad: 1</small>
                    </div>
                    <div class="text-end">
                        <strong>Q8,499.00</strong>
                    </div>
                </div>
            `;
            
            // Update totals
            const subtotal = 8499.00;
            const shipping = 50.00;

            document.getElementById('subtotal').textContent = 'Q' + subtotal.toFixed(2);

            // Calculate tax based on admin settings
            const taxEnabled = <?= json_encode($tax_settings['tax_enabled'] ?? false) ?>;
            const taxRate = <?= json_encode($tax_settings['tax_rate'] ?? 0) ?> / 100;

            let tax = 0;
            let total = subtotal + shipping;

            if (taxEnabled) {
                tax = subtotal * taxRate;
                total += tax;
                document.getElementById('tax').textContent = 'Q' + tax.toFixed(2);
            }

            document.getElementById('total').textContent = 'Q' + total.toFixed(2);

            // Recalculate installment amounts if installments are selected
            const installmentMethod = document.getElementById('payment_installments');
            if (installmentMethod && installmentMethod.checked) {
                calculateInstallmentAmounts();
            }
        }
    </script>
</body>
</html>
