<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 text-primary mb-3">
            <i class="fas fa-history me-3"></i>Mi Historia<PERSON> de Cupones
        </h1>
        <p class="lead text-muted">Revisa todos los cupones que has utilizado y tus ahorros totales</p>
    </div>

    <!-- Resumen de Ahorros -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-gradient-success text-white shadow-lg">
                <div class="card-body text-center py-5">
                    <i class="fas fa-piggy-bank fa-4x mb-3 opacity-75"></i>
                    <h2 class="display-4 fw-bold">Q<?= number_format($total_savings, 2) ?></h2>
                    <h4 class="mb-3">Total Ahorrado</h4>
                    <p class="mb-0 opacity-75">
                        Has ahorrado esta cantidad usando nuestros cupones de descuento
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Historial de Cupones -->
    <?php if (!empty($coupon_history)): ?>
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="card shadow border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Historial de Cupones Utilizados
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Fecha</th>
                                        <th>Cupón</th>
                                        <th>Tipo</th>
                                        <th>Descuento</th>
                                        <th>Pedido</th>
                                        <th>Estado</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($coupon_history as $usage): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold">
                                                    <?= date('d/m/Y', strtotime($usage['created_at'])) ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?= date('H:i', strtotime($usage['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-primary">
                                                    <?= esc($usage['code']) ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?= esc($usage['name']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $typeClass = 'badge-primary';
                                                $typeIcon = 'fa-percentage';
                                                $typeText = 'Porcentaje';
                                                
                                                switch($usage['type']) {
                                                    case 'fixed':
                                                        $typeClass = 'badge-success';
                                                        $typeIcon = 'fa-dollar-sign';
                                                        $typeText = 'Cantidad Fija';
                                                        break;
                                                    case 'free_shipping':
                                                        $typeClass = 'badge-info';
                                                        $typeIcon = 'fa-truck';
                                                        $typeText = 'Envío Gratis';
                                                        break;
                                                    case 'buy_x_get_y':
                                                        $typeClass = 'badge-warning';
                                                        $typeIcon = 'fa-gift';
                                                        $typeText = 'Promoción';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?= $typeClass ?>">
                                                    <i class="fas <?= $typeIcon ?> me-1"></i>
                                                    <?= $typeText ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-success">
                                                    Q<?= number_format($usage['discount_amount'], 2) ?>
                                                </div>
                                                <?php if ($usage['original_amount']): ?>
                                                    <small class="text-muted">
                                                        de Q<?= number_format($usage['original_amount'], 2) ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($usage['order_id']): ?>
                                                    <a href="<?= base_url('user/orders/view/' . $usage['order_id']) ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>
                                                        #<?= str_pad($usage['order_id'], 6, '0', STR_PAD_LEFT) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    Aplicado
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estadísticas Adicionales -->
        <div class="row mt-5">
            <div class="col-lg-10 mx-auto">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card text-center border-0 bg-light">
                            <div class="card-body py-4">
                                <i class="fas fa-ticket-alt fa-2x text-primary mb-3"></i>
                                <h4 class="text-primary"><?= count($coupon_history) ?></h4>
                                <p class="text-muted mb-0">Cupones Utilizados</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card text-center border-0 bg-light">
                            <div class="card-body py-4">
                                <i class="fas fa-percentage fa-2x text-success mb-3"></i>
                                <h4 class="text-success">
                                    <?= count($coupon_history) > 0 ? number_format($total_savings / count($coupon_history), 2) : '0.00' ?>
                                </h4>
                                <p class="text-muted mb-0">Ahorro Promedio</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card text-center border-0 bg-light">
                            <div class="card-body py-4">
                                <i class="fas fa-calendar fa-2x text-info mb-3"></i>
                                <h4 class="text-info">
                                    <?php
                                    $lastUsage = !empty($coupon_history) ? $coupon_history[0]['created_at'] : null;
                                    echo $lastUsage ? date('d/m/Y', strtotime($lastUsage)) : 'N/A';
                                    ?>
                                </h4>
                                <p class="text-muted mb-0">Último Uso</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <!-- Sin historial -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-ticket-alt fa-4x text-muted mb-4"></i>
                        <h3 class="text-muted mb-3">Aún no has usado cupones</h3>
                        <p class="text-muted mb-4">
                            ¡Comienza a ahorrar! Explora nuestros cupones disponibles y obtén descuentos increíbles en tus compras.
                        </p>
                        <div class="row justify-content-center">
                            <div class="col-md-4 mb-2">
                                <a href="<?= base_url('coupons/available') ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Ver Cupones
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="<?= base_url('shop') ?>" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-shopping-bag me-2"></i>Ir de Compras
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Cupones Disponibles -->
    <div class="row mt-5">
        <div class="col-lg-10 mx-auto">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-4">
                    <h4 class="mb-3">¿Buscas más descuentos?</h4>
                    <p class="mb-4">Descubre todos nuestros cupones disponibles y sigue ahorrando en tus compras.</p>
                    <div class="row justify-content-center">
                        <div class="col-md-4 mb-2">
                            <a href="<?= base_url('coupons/available') ?>" class="btn btn-light w-100">
                                <i class="fas fa-ticket-alt me-2"></i>Ver Cupones Disponibles
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="<?= base_url('user/dashboard') ?>" class="btn btn-outline-light w-100">
                                <i class="fas fa-user me-2"></i>Mi Cuenta
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.badge {
    font-size: 0.75rem;
}

.opacity-75 {
    opacity: 0.75;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Inicializar tooltips si están disponibles
document.addEventListener('DOMContentLoaded', function() {
    // Agregar animaciones suaves a las tarjetas
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// Función para copiar código de cupón si se necesita
function copyCouponCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // Mostrar notificación
        const notification = document.createElement('div');
        notification.className = 'alert alert-success position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        notification.innerHTML = `<strong>¡Copiado!</strong> Código ${code} copiado al portapapeles`;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    });
}
</script>
<?= $this->endSection() ?>
