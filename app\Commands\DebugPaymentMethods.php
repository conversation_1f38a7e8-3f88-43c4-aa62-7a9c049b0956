<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class DebugPaymentMethods extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'debug:payment-methods';
    protected $description = 'Debug métodos de pago en checkout';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== DEBUG MÉTODOS DE PAGO ===', 'yellow');
            CLI::newLine();
            
            // Consulta exacta que usa el CheckoutController
            CLI::write('Ejecutando consulta del CheckoutController...', 'white');
            $paymentMethods = $db->query("
                SELECT id, name, slug, description, type, icon, instructions
                FROM payment_methods 
                WHERE is_active = 1 
                ORDER BY sort_order, name
            ")->getResultArray();
            
            CLI::write('Resultados de la consulta:', 'cyan');
            CLI::write('Total encontrados: ' . count($paymentMethods), 'white');
            CLI::newLine();
            
            if (empty($paymentMethods)) {
                CLI::error('❌ No se encontraron métodos de pago activos');
                
                // Verificar todos los métodos
                CLI::write('Verificando TODOS los métodos de pago...', 'yellow');
                $allMethods = $db->query("SELECT id, name, slug, is_active FROM payment_methods")->getResultArray();
                
                foreach ($allMethods as $method) {
                    $status = $method['is_active'] ? 'ACTIVO' : 'INACTIVO';
                    CLI::write("ID: {$method['id']}, Nombre: {$method['name']}, Slug: {$method['slug']}, Estado: {$status}", 'white');
                }
            } else {
                foreach ($paymentMethods as $method) {
                    CLI::write("✅ ID: {$method['id']}", 'green');
                    CLI::write("   Nombre: {$method['name']}", 'white');
                    CLI::write("   Slug: {$method['slug']}", 'white');
                    CLI::write("   Tipo: {$method['type']}", 'white');
                    CLI::write("   Descripción: {$method['description']}", 'white');
                    CLI::write("   Icono: {$method['icon']}", 'white');
                    CLI::write('---', 'dark_gray');
                }
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
