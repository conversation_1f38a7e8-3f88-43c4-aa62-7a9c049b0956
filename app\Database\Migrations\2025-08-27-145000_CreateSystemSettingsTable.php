<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSystemSettingsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'setting_key' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
                'comment' => 'Clave única de la configuración'
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Valor de la configuración'
            ],
            'setting_type' => [
                'type' => 'ENUM',
                'constraint' => ['string', 'integer', 'decimal', 'boolean', 'json'],
                'default' => 'string',
                'comment' => 'Tipo de dato del valor'
            ],
            'description' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Descripción de la configuración'
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => 'general',
                'comment' => 'Categoría de la configuración'
            ],
            'is_editable' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => '1 = Editable desde admin, 0 = Solo código'
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP'
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP'
            ]
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('setting_key');
        $this->forge->addKey('category');
        $this->forge->createTable('system_settings');

        // Insert default tax settings
        $data = [
            [
                'setting_key' => 'tax_enabled',
                'setting_value' => '0',
                'setting_type' => 'boolean',
                'description' => 'Activar/desactivar cálculo de impuestos',
                'category' => 'taxes',
                'is_editable' => 1
            ],
            [
                'setting_key' => 'tax_rate',
                'setting_value' => '12.00',
                'setting_type' => 'decimal',
                'description' => 'Porcentaje de impuesto (ej: 12 para 12%)',
                'category' => 'taxes',
                'is_editable' => 1
            ],
            [
                'setting_key' => 'tax_name',
                'setting_value' => 'IVA',
                'setting_type' => 'string',
                'description' => 'Nombre del impuesto (IVA, IGV, etc.)',
                'category' => 'taxes',
                'is_editable' => 1
            ],
            [
                'setting_key' => 'tax_included_in_price',
                'setting_value' => '0',
                'setting_type' => 'boolean',
                'description' => 'Los precios incluyen impuestos (1) o se agregan al final (0)',
                'category' => 'taxes',
                'is_editable' => 1
            ],
            [
                'setting_key' => 'store_name',
                'setting_value' => 'MrCell Guatemala',
                'setting_type' => 'string',
                'description' => 'Nombre de la tienda',
                'category' => 'general',
                'is_editable' => 1
            ],
            [
                'setting_key' => 'store_currency',
                'setting_value' => 'Q',
                'setting_type' => 'string',
                'description' => 'Símbolo de moneda',
                'category' => 'general',
                'is_editable' => 1
            ]
        ];

        $this->db->table('system_settings')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('system_settings');
    }
}
