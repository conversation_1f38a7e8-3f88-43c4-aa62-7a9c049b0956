<?php

namespace App\Libraries;

use App\Libraries\SimpleCache;

/**
 * Optimizador de Rendimiento para cPanel
 * Sistema completo de optimización sin herramientas externas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class PerformanceOptimizer
{
    private $db;
    private $cache;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new SimpleCache();
        $this->config = [
            'slow_query_threshold' => 1.0, // 1 segundo
            'cache_ttl' => 3600, // 1 hora
            'image_quality' => 85, // Calidad JPEG
            'enable_webp' => true,
            'minify_html' => true,
            'minify_css' => true,
            'minify_js' => true
        ];
    }
    
    /**
     * Optimizar todas las consultas lentas detectadas
     * 
     * @return array Resultado de optimizaciones
     */
    public function optimizeSlowQueries(): array
    {
        $results = [];
        
        try {
            // Habilitar log de consultas lentas temporalmente
            $this->db->query("SET SESSION long_query_time = {$this->config['slow_query_threshold']}");
            
            // Analizar consultas más comunes
            $commonQueries = $this->getCommonQueries();
            
            foreach ($commonQueries as $query) {
                $optimization = $this->optimizeQuery($query);
                if ($optimization) {
                    $results[] = $optimization;
                }
            }
            
            // Optimizar índices automáticamente
            $indexOptimizations = $this->optimizeIndexes();
            $results = array_merge($results, $indexOptimizations);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en optimización de consultas: ' . $e->getMessage());
            $results[] = ['error' => $e->getMessage()];
        }
        
        return $results;
    }
    
    /**
     * Obtener consultas más comunes que necesitan optimización
     */
    private function getCommonQueries(): array
    {
        return [
            // Consultas de productos
            [
                'table' => 'products',
                'query' => 'SELECT * FROM products WHERE is_active = 1 AND deleted_at IS NULL',
                'optimization' => 'ADD INDEX idx_active_deleted (is_active, deleted_at)'
            ],
            [
                'table' => 'products',
                'query' => 'SELECT * FROM products WHERE category_id = ? AND is_active = 1',
                'optimization' => 'ADD INDEX idx_category_active (category_id, is_active)'
            ],
            [
                'table' => 'products',
                'query' => 'SELECT * FROM products WHERE price_regular BETWEEN ? AND ?',
                'optimization' => 'ADD INDEX idx_price_range (price_regular)'
            ],
            // Consultas de wishlist
            [
                'table' => 'wishlist',
                'query' => 'SELECT * FROM wishlist WHERE user_id = ? ORDER BY created_at DESC',
                'optimization' => 'ADD INDEX idx_user_created (user_id, created_at)'
            ],
            // Consultas de órdenes
            [
                'table' => 'orders',
                'query' => 'SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC',
                'optimization' => 'ADD INDEX idx_user_orders (user_id, created_at)'
            ],
            // Consultas de reviews
            [
                'table' => 'product_reviews',
                'query' => 'SELECT * FROM product_reviews WHERE product_id = ? AND is_approved = 1',
                'optimization' => 'ADD INDEX idx_product_approved (product_id, is_approved)'
            ]
        ];
    }
    
    /**
     * Optimizar una consulta específica
     */
    private function optimizeQuery(array $queryInfo): ?array
    {
        try {
            $table = $queryInfo['table'];
            $optimization = $queryInfo['optimization'];
            
            // Verificar si el índice ya existe
            $indexName = $this->extractIndexName($optimization);
            if ($this->indexExists($table, $indexName)) {
                return [
                    'table' => $table,
                    'status' => 'already_exists',
                    'index' => $indexName
                ];
            }
            
            // Crear el índice
            $this->db->query("ALTER TABLE {$table} {$optimization}");
            
            return [
                'table' => $table,
                'status' => 'created',
                'index' => $indexName,
                'optimization' => $optimization
            ];
            
        } catch (\Exception $e) {
            return [
                'table' => $queryInfo['table'],
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimizar índices automáticamente
     */
    private function optimizeIndexes(): array
    {
        $results = [];
        
        try {
            // Obtener tablas principales
            $tables = ['products', 'categories', 'brands', 'users', 'orders', 'wishlist', 'product_reviews'];
            
            foreach ($tables as $table) {
                // Analizar uso de índices
                $analysis = $this->analyzeTableIndexes($table);
                
                if ($analysis['needs_optimization']) {
                    foreach ($analysis['recommendations'] as $recommendation) {
                        $result = $this->applyIndexRecommendation($table, $recommendation);
                        $results[] = $result;
                    }
                }
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Error en optimización de índices: ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Analizar índices de una tabla
     */
    private function analyzeTableIndexes(string $table): array
    {
        try {
            // Obtener información de la tabla
            $query = $this->db->query("SHOW INDEX FROM {$table}");
            $indexes = $query->getResultArray();
            
            // Obtener estadísticas de uso
            $statsQuery = $this->db->query("
                SELECT * FROM information_schema.TABLE_STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{$table}'
            ");
            
            $recommendations = [];
            
            // Verificar índices faltantes comunes
            $missingIndexes = $this->detectMissingIndexes($table, $indexes);
            
            return [
                'table' => $table,
                'current_indexes' => count($indexes),
                'needs_optimization' => !empty($missingIndexes),
                'recommendations' => $missingIndexes
            ];
            
        } catch (\Exception $e) {
            return [
                'table' => $table,
                'needs_optimization' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Detectar índices faltantes
     */
    private function detectMissingIndexes(string $table, array $existingIndexes): array
    {
        $existing = array_column($existingIndexes, 'Key_name');
        $recommendations = [];
        
        // Recomendaciones específicas por tabla
        $tableRecommendations = [
            'products' => [
                'idx_featured_active' => 'ADD INDEX idx_featured_active (is_featured, is_active)',
                'idx_stock_status' => 'ADD INDEX idx_stock_status (stock_status, stock_quantity)',
                'idx_rating' => 'ADD INDEX idx_rating (rating_average, rating_count)',
                'idx_price_sale' => 'ADD INDEX idx_price_sale (price_sale, price_regular)'
            ],
            'orders' => [
                'idx_status_date' => 'ADD INDEX idx_status_date (status, created_at)',
                'idx_user_status' => 'ADD INDEX idx_user_status (user_id, status)'
            ],
            'wishlist' => [
                'idx_user_priority' => 'ADD INDEX idx_user_priority (user_id, priority)',
                'idx_notifications' => 'ADD INDEX idx_notifications (notification_enabled, price_alert_threshold)'
            ]
        ];
        
        if (isset($tableRecommendations[$table])) {
            foreach ($tableRecommendations[$table] as $indexName => $sql) {
                if (!in_array($indexName, $existing)) {
                    $recommendations[] = [
                        'index_name' => $indexName,
                        'sql' => $sql,
                        'reason' => 'Mejora rendimiento de consultas frecuentes'
                    ];
                }
            }
        }
        
        return $recommendations;
    }
    
    /**
     * Aplicar recomendación de índice
     */
    private function applyIndexRecommendation(string $table, array $recommendation): array
    {
        try {
            $this->db->query("ALTER TABLE {$table} {$recommendation['sql']}");
            
            return [
                'table' => $table,
                'index' => $recommendation['index_name'],
                'status' => 'created',
                'reason' => $recommendation['reason']
            ];
            
        } catch (\Exception $e) {
            return [
                'table' => $table,
                'index' => $recommendation['index_name'],
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Comprimir imágenes automáticamente
     */
    public function optimizeImages(string $directory = 'uploads'): array
    {
        $results = [];
        $uploadPath = FCPATH . $directory;
        
        if (!is_dir($uploadPath)) {
            return ['error' => 'Directorio no encontrado: ' . $uploadPath];
        }
        
        $images = glob($uploadPath . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);
        
        foreach ($images as $imagePath) {
            $result = $this->compressImage($imagePath);
            $results[] = $result;
        }
        
        return $results;
    }
    
    /**
     * Comprimir una imagen individual
     */
    private function compressImage(string $imagePath): array
    {
        try {
            $originalSize = filesize($imagePath);
            $imageInfo = getimagesize($imagePath);
            
            if (!$imageInfo) {
                return [
                    'file' => basename($imagePath),
                    'status' => 'error',
                    'error' => 'No es una imagen válida'
                ];
            }
            
            $mimeType = $imageInfo['mime'];
            
            // Crear imagen desde archivo
            switch ($mimeType) {
                case 'image/jpeg':
                    $image = imagecreatefromjpeg($imagePath);
                    break;
                case 'image/png':
                    $image = imagecreatefrompng($imagePath);
                    break;
                case 'image/gif':
                    $image = imagecreatefromgif($imagePath);
                    break;
                default:
                    return [
                        'file' => basename($imagePath),
                        'status' => 'skipped',
                        'reason' => 'Formato no soportado'
                    ];
            }
            
            if (!$image) {
                return [
                    'file' => basename($imagePath),
                    'status' => 'error',
                    'error' => 'No se pudo crear la imagen'
                ];
            }
            
            // Crear versión WebP si está habilitado
            if ($this->config['enable_webp'] && function_exists('imagewebp')) {
                $webpPath = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $imagePath);
                imagewebp($image, $webpPath, $this->config['image_quality']);
            }
            
            // Comprimir imagen original
            switch ($mimeType) {
                case 'image/jpeg':
                    imagejpeg($image, $imagePath, $this->config['image_quality']);
                    break;
                case 'image/png':
                    // PNG usa compresión sin pérdida
                    imagepng($image, $imagePath, 9);
                    break;
                case 'image/gif':
                    imagegif($image, $imagePath);
                    break;
            }
            
            imagedestroy($image);
            
            $newSize = filesize($imagePath);
            $savings = $originalSize - $newSize;
            $percentage = round(($savings / $originalSize) * 100, 2);
            
            return [
                'file' => basename($imagePath),
                'status' => 'compressed',
                'original_size' => $originalSize,
                'new_size' => $newSize,
                'savings' => $savings,
                'percentage' => $percentage,
                'webp_created' => isset($webpPath) && file_exists($webpPath)
            ];
            
        } catch (\Exception $e) {
            return [
                'file' => basename($imagePath),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Minificar CSS y JS
     */
    public function minifyAssets(): array
    {
        $results = [];
        
        // Minificar CSS
        $cssFiles = glob(FCPATH . 'assets/css/*.css');
        foreach ($cssFiles as $cssFile) {
            if (strpos($cssFile, '.min.css') === false) {
                $result = $this->minifyCSS($cssFile);
                $results['css'][] = $result;
            }
        }
        
        // Minificar JS
        $jsFiles = glob(FCPATH . 'assets/js/*.js');
        foreach ($jsFiles as $jsFile) {
            if (strpos($jsFile, '.min.js') === false) {
                $result = $this->minifyJS($jsFile);
                $results['js'][] = $result;
            }
        }
        
        return $results;
    }
    
    /**
     * Minificar archivo CSS
     */
    private function minifyCSS(string $filePath): array
    {
        try {
            $css = file_get_contents($filePath);
            $originalSize = strlen($css);
            
            // Minificar CSS
            $css = preg_replace('/\/\*.*?\*\//s', '', $css); // Remover comentarios
            $css = preg_replace('/\s+/', ' ', $css); // Comprimir espacios
            $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ', '], [';', '{', '{', '}', '}', ':', ','], $css);
            $css = trim($css);
            
            $minifiedSize = strlen($css);
            $savings = $originalSize - $minifiedSize;
            $percentage = round(($savings / $originalSize) * 100, 2);
            
            // Guardar versión minificada
            $minPath = str_replace('.css', '.min.css', $filePath);
            file_put_contents($minPath, $css);
            
            return [
                'file' => basename($filePath),
                'status' => 'minified',
                'original_size' => $originalSize,
                'minified_size' => $minifiedSize,
                'savings' => $savings,
                'percentage' => $percentage,
                'minified_file' => basename($minPath)
            ];
            
        } catch (\Exception $e) {
            return [
                'file' => basename($filePath),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Minificar archivo JS
     */
    private function minifyJS(string $filePath): array
    {
        try {
            $js = file_get_contents($filePath);
            $originalSize = strlen($js);
            
            // Minificar JS (básico)
            $js = preg_replace('/\/\*.*?\*\//s', '', $js); // Remover comentarios multilinea
            $js = preg_replace('/\/\/.*$/m', '', $js); // Remover comentarios de línea
            $js = preg_replace('/\s+/', ' ', $js); // Comprimir espacios
            $js = str_replace(['; ', ' {', '{ ', ' }', '} ', ' (', '( ', ' )', ') ', ' =', '= ', ' +', '+ ', ' -', '- '], [';', '{', '{', '}', '}', '(', '(', ')', ')', '=', '=', '+', '+', '-', '-'], $js);
            $js = trim($js);
            
            $minifiedSize = strlen($js);
            $savings = $originalSize - $minifiedSize;
            $percentage = round(($savings / $originalSize) * 100, 2);
            
            // Guardar versión minificada
            $minPath = str_replace('.js', '.min.js', $filePath);
            file_put_contents($minPath, $js);
            
            return [
                'file' => basename($filePath),
                'status' => 'minified',
                'original_size' => $originalSize,
                'minified_size' => $minifiedSize,
                'savings' => $savings,
                'percentage' => $percentage,
                'minified_file' => basename($minPath)
            ];
            
        } catch (\Exception $e) {
            return [
                'file' => basename($filePath),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimizar tablas de base de datos
     */
    public function optimizeTables(): array
    {
        $results = [];
        $tables = ['products', 'categories', 'brands', 'users', 'orders', 'wishlist', 'product_reviews'];
        
        foreach ($tables as $table) {
            try {
                $this->db->query("OPTIMIZE TABLE {$table}");
                $results[] = [
                    'table' => $table,
                    'status' => 'optimized'
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'table' => $table,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Generar reporte completo de rendimiento
     */
    public function generatePerformanceReport(): array
    {
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'database' => [
                'slow_queries' => $this->optimizeSlowQueries(),
                'table_optimization' => $this->optimizeTables()
            ],
            'assets' => [
                'minification' => $this->minifyAssets(),
                'image_optimization' => $this->optimizeImages()
            ],
            'cache' => SimpleCache::info(),
            'recommendations' => $this->getPerformanceRecommendations()
        ];
    }
    
    /**
     * Obtener recomendaciones de rendimiento
     */
    private function getPerformanceRecommendations(): array
    {
        return [
            'Enable GZIP compression in .htaccess',
            'Set proper cache headers for static assets',
            'Use WebP images when possible',
            'Implement lazy loading for images',
            'Consider using a CDN for static assets',
            'Regular database maintenance with OPTIMIZE TABLE',
            'Monitor slow query log regularly'
        ];
    }
    
    // Métodos auxiliares
    private function extractIndexName(string $sql): string
    {
        preg_match('/ADD INDEX (\w+)/', $sql, $matches);
        return $matches[1] ?? 'unknown';
    }
    
    private function indexExists(string $table, string $indexName): bool
    {
        $query = $this->db->query("SHOW INDEX FROM {$table} WHERE Key_name = '{$indexName}'");
        return $query->getNumRows() > 0;
    }
}
