<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\SimpleCache;

/**
 * Comando para limpiar cache
 * Compatible con cPanel hosting
 * 
 * Uso: php spark cache:cleanup
 */
class CacheCleanup extends BaseCommand
{
    protected $group       = 'MrCell';
    protected $name        = 'cache:cleanup';
    protected $description = 'Limpia el cache expirado del sistema';
    protected $usage       = 'cache:cleanup [options]';
    protected $arguments   = [];
    protected $options     = [
        '--all' => 'Limpiar todo el cache (no solo el expirado)',
        '--type' => 'Tipo específico de cache a limpiar (products, searches, categories)',
        '--stats' => 'Mostrar estadísticas del cache antes de limpiar'
    ];

    public function run(array $params)
    {
        CLI::write('🧹 Limpieza de Cache - MrCell Guatemala', 'green');
        CLI::newLine();
        
        $all = CLI::getOption('all');
        $type = CLI::getOption('type');
        $showStats = CLI::getOption('stats');
        
        // Mostrar estadísticas si se solicita
        if ($showStats) {
            $this->showCacheStats();
        }
        
        // Limpiar según opciones
        if ($all) {
            $this->clearAllCache();
        } elseif ($type) {
            $this->clearCacheByType($type);
        } else {
            $this->clearExpiredCache();
        }
        
        CLI::newLine();
        CLI::write('✅ Limpieza completada!', 'green');
    }
    
    /**
     * Mostrar estadísticas del cache
     */
    private function showCacheStats()
    {
        CLI::write('📊 Estadísticas del Cache:', 'yellow');
        
        try {
            $info = SimpleCache::info();
            
            CLI::write("  📁 Directorio: {$info['cache_dir']}", 'white');
            CLI::write("  📄 Total archivos: {$info['total_files']}", 'white');
            CLI::write("  ✅ Archivos activos: {$info['active_files']}", 'green');
            CLI::write("  ⏰ Archivos expirados: {$info['expired_files']}", 'red');
            CLI::write("  💾 Tamaño total: {$info['total_size_mb']} MB", 'white');
            
        } catch (\Exception $e) {
            CLI::write("  ❌ Error obteniendo estadísticas: " . $e->getMessage(), 'red');
        }
        
        CLI::newLine();
    }
    
    /**
     * Limpiar todo el cache
     */
    private function clearAllCache()
    {
        CLI::write('🗑️  Limpiando TODO el cache...', 'yellow');
        
        try {
            $result = SimpleCache::clear();
            
            if ($result) {
                CLI::write('  ✅ Todo el cache ha sido eliminado', 'green');
            } else {
                CLI::write('  ❌ Error al limpiar el cache', 'red');
            }
            
        } catch (\Exception $e) {
            CLI::write("  ❌ Error: " . $e->getMessage(), 'red');
        }
    }
    
    /**
     * Limpiar cache por tipo
     */
    private function clearCacheByType(string $type)
    {
        CLI::write("🎯 Limpiando cache de tipo: {$type}", 'yellow');
        
        $cacheDir = WRITEPATH . 'cache/simple/';
        $deleted = 0;
        
        try {
            $files = glob($cacheDir . '*.cache');
            
            foreach ($files as $file) {
                $filename = basename($file, '.cache');
                
                $shouldDelete = false;
                
                switch ($type) {
                    case 'products':
                        $shouldDelete = strpos($filename, 'product_') === 0;
                        break;
                    case 'searches':
                        $shouldDelete = strpos($filename, 'search_') === 0 || 
                                      strpos($filename, 'suggestions_') === 0;
                        break;
                    case 'categories':
                        $shouldDelete = strpos($filename, 'categories_') === 0;
                        break;
                    default:
                        CLI::write("  ❌ Tipo de cache no reconocido: {$type}", 'red');
                        return;
                }
                
                if ($shouldDelete && unlink($file)) {
                    $deleted++;
                }
            }
            
            CLI::write("  ✅ Eliminados {$deleted} archivos de cache de tipo '{$type}'", 'green');
            
        } catch (\Exception $e) {
            CLI::write("  ❌ Error: " . $e->getMessage(), 'red');
        }
    }
    
    /**
     * Limpiar solo cache expirado
     */
    private function clearExpiredCache()
    {
        CLI::write('⏰ Limpiando cache expirado...', 'yellow');
        
        try {
            $deleted = SimpleCache::clearExpired();
            
            if ($deleted > 0) {
                CLI::write("  ✅ Eliminados {$deleted} archivos de cache expirados", 'green');
            } else {
                CLI::write("  ℹ️  No se encontraron archivos de cache expirados", 'blue');
            }
            
        } catch (\Exception $e) {
            CLI::write("  ❌ Error: " . $e->getMessage(), 'red');
        }
    }
}
