<?php

namespace App\Libraries;

/**
 * Gestor de Blockchain y Criptomonedas
 * Sistema completo de pagos con criptomonedas, NFTs y tokens de fidelidad
 */
class BlockchainManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'enabled' => env('BLOCKCHAIN_ENABLED', true),
            'network' => env('BLOCKCHAIN_NETWORK', 'mainnet'), // mainnet, testnet
            
            // Bitcoin
            'bitcoin_enabled' => env('BITCOIN_ENABLED', true),
            'bitcoin_api_key' => env('BITCOIN_API_KEY', ''),
            'bitcoin_wallet' => env('BITCOIN_WALLET_ADDRESS', ''),
            
            // Ethereum
            'ethereum_enabled' => env('ETHEREUM_ENABLED', true),
            'ethereum_api_key' => env('ETHEREUM_API_KEY', ''),
            'ethereum_wallet' => env('ETHEREUM_WALLET_ADDRESS', ''),
            'ethereum_rpc_url' => env('ETHEREUM_RPC_URL', 'https://mainnet.infura.io/v3/'),
            
            // Binance Smart Chain
            'bsc_enabled' => env('BSC_ENABLED', true),
            'bsc_api_key' => env('BSC_API_KEY', ''),
            'bsc_wallet' => env('BSC_WALLET_ADDRESS', ''),
            
            // Stablecoins
            'usdt_enabled' => env('USDT_ENABLED', true),
            'usdc_enabled' => env('USDC_ENABLED', true),
            'busd_enabled' => env('BUSD_ENABLED', true),
            
            // NFTs
            'nft_enabled' => env('NFT_ENABLED', true),
            'nft_contract_address' => env('NFT_CONTRACT_ADDRESS', ''),
            'nft_marketplace_fee' => env('NFT_MARKETPLACE_FEE', 2.5), // %
            
            // Tokens de Fidelidad
            'loyalty_token_enabled' => env('LOYALTY_TOKEN_ENABLED', true),
            'loyalty_token_symbol' => env('LOYALTY_TOKEN_SYMBOL', 'MRCELL'),
            'loyalty_token_contract' => env('LOYALTY_TOKEN_CONTRACT', ''),
            'loyalty_conversion_rate' => env('LOYALTY_CONVERSION_RATE', 100), // 1 USD = 100 tokens
            
            // Configuración general
            'confirmation_blocks' => env('BLOCKCHAIN_CONFIRMATIONS', 3),
            'transaction_timeout' => env('BLOCKCHAIN_TIMEOUT', 600), // 10 minutos
            'gas_price_multiplier' => env('GAS_PRICE_MULTIPLIER', 1.2),
            'auto_convert_fiat' => env('AUTO_CONVERT_FIAT', true)
        ];
        
        $this->createBlockchainTables();
    }
    
    /**
     * Procesar pago con criptomoneda
     */
    public function processCryptoPayment(array $paymentData): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'Blockchain payments disabled'];
        }
        
        try {
            $currency = strtoupper($paymentData['currency']);
            $amount = $paymentData['amount'];
            $orderId = $paymentData['order_id'];
            
            // Validar moneda soportada
            if (!$this->isCurrencySupported($currency)) {
                throw new \Exception("Cryptocurrency $currency not supported");
            }
            
            // Generar dirección de pago única
            $paymentAddress = $this->generatePaymentAddress($currency, $orderId);
            
            // Calcular precio en criptomoneda
            $cryptoAmount = $this->convertToCrypto($amount, $currency);
            
            // Crear transacción pendiente
            $transactionId = $this->createPendingTransaction([
                'order_id' => $orderId,
                'currency' => $currency,
                'amount_fiat' => $amount,
                'amount_crypto' => $cryptoAmount,
                'payment_address' => $paymentAddress,
                'status' => 'pending',
                'expires_at' => date('Y-m-d H:i:s', time() + $this->config['transaction_timeout'])
            ]);
            
            // Iniciar monitoreo de la transacción
            $this->startTransactionMonitoring($transactionId);
            
            $this->logger->info("Crypto payment initiated", [
                'transaction_id' => $transactionId,
                'order_id' => $orderId,
                'currency' => $currency,
                'amount' => $cryptoAmount
            ]);
            
            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'payment_address' => $paymentAddress,
                'amount_crypto' => $cryptoAmount,
                'currency' => $currency,
                'qr_code' => $this->generatePaymentQR($paymentAddress, $cryptoAmount, $currency),
                'expires_at' => date('c', time() + $this->config['transaction_timeout'])
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Crypto payment error: " . $e->getMessage(), $paymentData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verificar estado de transacción
     */
    public function checkTransactionStatus(string $transactionId): array
    {
        try {
            $transaction = $this->getTransaction($transactionId);
            
            if (!$transaction) {
                throw new \Exception("Transaction not found");
            }
            
            // Si ya está confirmada, devolver estado
            if ($transaction['status'] === 'confirmed') {
                return [
                    'success' => true,
                    'status' => 'confirmed',
                    'confirmations' => $transaction['confirmations'],
                    'tx_hash' => $transaction['tx_hash']
                ];
            }
            
            // Verificar en blockchain
            $blockchainStatus = $this->checkBlockchainTransaction($transaction);
            
            if ($blockchainStatus['found']) {
                // Actualizar transacción
                $this->updateTransaction($transactionId, [
                    'status' => $blockchainStatus['confirmations'] >= $this->config['confirmation_blocks'] ? 'confirmed' : 'pending',
                    'confirmations' => $blockchainStatus['confirmations'],
                    'tx_hash' => $blockchainStatus['tx_hash'],
                    'block_height' => $blockchainStatus['block_height']
                ]);
                
                // Si está confirmada, procesar orden
                if ($blockchainStatus['confirmations'] >= $this->config['confirmation_blocks']) {
                    $this->processConfirmedPayment($transaction);
                }
                
                return [
                    'success' => true,
                    'status' => $blockchainStatus['confirmations'] >= $this->config['confirmation_blocks'] ? 'confirmed' : 'pending',
                    'confirmations' => $blockchainStatus['confirmations'],
                    'required_confirmations' => $this->config['confirmation_blocks'],
                    'tx_hash' => $blockchainStatus['tx_hash']
                ];
            }
            
            // Verificar si expiró
            if (strtotime($transaction['expires_at']) < time()) {
                $this->updateTransaction($transactionId, ['status' => 'expired']);
                
                return [
                    'success' => true,
                    'status' => 'expired'
                ];
            }
            
            return [
                'success' => true,
                'status' => 'pending',
                'confirmations' => 0
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Transaction status check error: " . $e->getMessage(), [
                'transaction_id' => $transactionId
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Crear NFT para producto exclusivo
     */
    public function createProductNFT(array $productData): array
    {
        if (!$this->config['nft_enabled']) {
            return ['success' => false, 'error' => 'NFT functionality disabled'];
        }
        
        try {
            $nftData = [
                'product_id' => $productData['product_id'],
                'name' => $productData['name'],
                'description' => $productData['description'],
                'image_url' => $productData['image_url'],
                'attributes' => json_encode($productData['attributes'] ?? []),
                'max_supply' => $productData['max_supply'] ?? 1,
                'price_eth' => $productData['price_eth'],
                'creator_address' => $this->config['ethereum_wallet'],
                'status' => 'pending'
            ];
            
            $nftId = $this->db->table('product_nfts')->insert($nftData);
            
            // Crear metadata JSON
            $metadata = $this->createNFTMetadata($nftData);
            
            // Subir metadata a IPFS (simulado)
            $metadataUri = $this->uploadToIPFS($metadata);
            
            // Mintear NFT en blockchain (simulado)
            $mintResult = $this->mintNFT($nftId, $metadataUri);
            
            if ($mintResult['success']) {
                $this->db->table('product_nfts')
                        ->where('id', $nftId)
                        ->update([
                            'token_id' => $mintResult['token_id'],
                            'contract_address' => $mintResult['contract_address'],
                            'metadata_uri' => $metadataUri,
                            'status' => 'minted',
                            'tx_hash' => $mintResult['tx_hash']
                        ]);
                
                $this->logger->info("NFT created successfully", [
                    'nft_id' => $nftId,
                    'token_id' => $mintResult['token_id'],
                    'product_id' => $productData['product_id']
                ]);
                
                return [
                    'success' => true,
                    'nft_id' => $nftId,
                    'token_id' => $mintResult['token_id'],
                    'contract_address' => $mintResult['contract_address'],
                    'metadata_uri' => $metadataUri
                ];
            }
            
            throw new \Exception('NFT minting failed');
            
        } catch (\Exception $e) {
            $this->logger->error("NFT creation error: " . $e->getMessage(), $productData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Otorgar tokens de fidelidad
     */
    public function awardLoyaltyTokens(int $userId, float $purchaseAmount, string $reason = 'purchase'): array
    {
        if (!$this->config['loyalty_token_enabled']) {
            return ['success' => false, 'error' => 'Loyalty tokens disabled'];
        }
        
        try {
            // Calcular tokens a otorgar
            $tokensToAward = floor($purchaseAmount * $this->config['loyalty_conversion_rate']);
            
            if ($tokensToAward <= 0) {
                return ['success' => false, 'error' => 'No tokens to award'];
            }
            
            // Registrar transacción de tokens
            $tokenTransactionId = $this->db->table('loyalty_token_transactions')->insert([
                'user_id' => $userId,
                'amount' => $tokensToAward,
                'type' => 'earned',
                'reason' => $reason,
                'purchase_amount' => $purchaseAmount,
                'status' => 'completed',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Actualizar balance del usuario
            $this->updateUserTokenBalance($userId, $tokensToAward);
            
            // Enviar tokens en blockchain (simulado)
            $blockchainResult = $this->sendLoyaltyTokens($userId, $tokensToAward);
            
            $this->logger->info("Loyalty tokens awarded", [
                'user_id' => $userId,
                'tokens' => $tokensToAward,
                'purchase_amount' => $purchaseAmount,
                'reason' => $reason
            ]);
            
            return [
                'success' => true,
                'tokens_awarded' => $tokensToAward,
                'transaction_id' => $tokenTransactionId,
                'new_balance' => $this->getUserTokenBalance($userId)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Loyalty tokens award error: " . $e->getMessage(), [
                'user_id' => $userId,
                'purchase_amount' => $purchaseAmount
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Canjear tokens de fidelidad
     */
    public function redeemLoyaltyTokens(int $userId, int $tokensToRedeem, string $reason = 'discount'): array
    {
        try {
            $currentBalance = $this->getUserTokenBalance($userId);
            
            if ($currentBalance < $tokensToRedeem) {
                throw new \Exception('Insufficient token balance');
            }
            
            // Calcular valor en fiat
            $fiatValue = $tokensToRedeem / $this->config['loyalty_conversion_rate'];
            
            // Registrar transacción
            $transactionId = $this->db->table('loyalty_token_transactions')->insert([
                'user_id' => $userId,
                'amount' => -$tokensToRedeem,
                'type' => 'redeemed',
                'reason' => $reason,
                'fiat_value' => $fiatValue,
                'status' => 'completed',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Actualizar balance
            $this->updateUserTokenBalance($userId, -$tokensToRedeem);
            
            $this->logger->info("Loyalty tokens redeemed", [
                'user_id' => $userId,
                'tokens' => $tokensToRedeem,
                'fiat_value' => $fiatValue
            ]);
            
            return [
                'success' => true,
                'tokens_redeemed' => $tokensToRedeem,
                'fiat_value' => $fiatValue,
                'new_balance' => $this->getUserTokenBalance($userId)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener precios de criptomonedas
     */
    public function getCryptoPrices(): array
    {
        try {
            $cacheKey = 'crypto_prices';
            $prices = $this->cache->get($cacheKey);
            
            if ($prices === null) {
                // Obtener precios de API externa (simulado)
                $prices = $this->fetchCryptoPrices();
                $this->cache->set($cacheKey, $prices, 300); // Cache por 5 minutos
            }
            
            return [
                'success' => true,
                'prices' => $prices,
                'last_updated' => date('c')
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de blockchain
     */
    public function getBlockchainStats(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            // Transacciones de criptomonedas
            $cryptoTransactions = $this->db->table('crypto_transactions')
                                          ->where('created_at >=', $dateFrom)
                                          ->where('status', 'confirmed')
                                          ->get()
                                          ->getResultArray();
            
            // NFTs creados
            $nftsCreated = $this->db->table('product_nfts')
                                   ->where('created_at >=', $dateFrom)
                                   ->where('status', 'minted')
                                   ->countAllResults();
            
            // Tokens de fidelidad otorgados
            $tokensAwarded = $this->db->table('loyalty_token_transactions')
                                     ->where('created_at >=', $dateFrom)
                                     ->where('type', 'earned')
                                     ->selectSum('amount')
                                     ->get()
                                     ->getRowArray()['amount'] ?? 0;
            
            $stats = [
                'crypto_transactions' => [
                    'total' => count($cryptoTransactions),
                    'volume_usd' => array_sum(array_column($cryptoTransactions, 'amount_fiat')),
                    'by_currency' => $this->groupTransactionsByCurrency($cryptoTransactions)
                ],
                'nfts' => [
                    'created' => $nftsCreated,
                    'total_supply' => $this->getTotalNFTSupply()
                ],
                'loyalty_tokens' => [
                    'awarded' => $tokensAwarded,
                    'total_supply' => $this->getTotalTokenSupply(),
                    'active_holders' => $this->getActiveTokenHolders()
                ]
            ];
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => $stats
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function isCurrencySupported(string $currency): bool
    {
        $supportedCurrencies = [];
        
        if ($this->config['bitcoin_enabled']) $supportedCurrencies[] = 'BTC';
        if ($this->config['ethereum_enabled']) $supportedCurrencies[] = 'ETH';
        if ($this->config['bsc_enabled']) $supportedCurrencies[] = 'BNB';
        if ($this->config['usdt_enabled']) $supportedCurrencies[] = 'USDT';
        if ($this->config['usdc_enabled']) $supportedCurrencies[] = 'USDC';
        if ($this->config['busd_enabled']) $supportedCurrencies[] = 'BUSD';
        
        return in_array($currency, $supportedCurrencies);
    }
    
    private function generatePaymentAddress(string $currency, int $orderId): string
    {
        // En producción, generar dirección real usando APIs de blockchain
        return strtolower($currency) . '_' . $orderId . '_' . bin2hex(random_bytes(16));
    }
    
    private function convertToCrypto(float $fiatAmount, string $currency): float
    {
        $prices = $this->fetchCryptoPrices();
        $rate = $prices[$currency] ?? 1;
        
        return round($fiatAmount / $rate, 8);
    }
    
    private function generatePaymentQR(string $address, float $amount, string $currency): string
    {
        // Generar QR code para pago
        $qrData = strtolower($currency) . ':' . $address . '?amount=' . $amount;
        return 'data:image/png;base64,' . base64_encode('QR_CODE_DATA'); // Simulado
    }
    
    private function createPendingTransaction(array $data): int
    {
        return $this->db->table('crypto_transactions')->insert(array_merge($data, [
            'created_at' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function getTransaction(string $transactionId): ?array
    {
        return $this->db->table('crypto_transactions')
                       ->where('id', $transactionId)
                       ->get()
                       ->getRowArray();
    }
    
    private function checkBlockchainTransaction(array $transaction): array
    {
        // Simular verificación en blockchain
        return [
            'found' => true,
            'confirmations' => rand(1, 6),
            'tx_hash' => '0x' . bin2hex(random_bytes(32)),
            'block_height' => rand(15000000, 16000000)
        ];
    }
    
    private function updateTransaction(string $transactionId, array $data): void
    {
        $this->db->table('crypto_transactions')
                ->where('id', $transactionId)
                ->update(array_merge($data, ['updated_at' => date('Y-m-d H:i:s')]));
    }
    
    private function processConfirmedPayment(array $transaction): void
    {
        // Procesar orden confirmada
        $this->db->table('orders')
                ->where('id', $transaction['order_id'])
                ->update(['payment_status' => 'paid', 'status' => 'processing']);
    }
    
    private function startTransactionMonitoring(int $transactionId): void
    {
        // En producción, iniciar proceso de monitoreo en background
    }
    
    private function fetchCryptoPrices(): array
    {
        // Simular precios de criptomonedas
        return [
            'BTC' => 45000.00,
            'ETH' => 3000.00,
            'BNB' => 300.00,
            'USDT' => 1.00,
            'USDC' => 1.00,
            'BUSD' => 1.00
        ];
    }
    
    private function createNFTMetadata(array $nftData): array
    {
        return [
            'name' => $nftData['name'],
            'description' => $nftData['description'],
            'image' => $nftData['image_url'],
            'attributes' => json_decode($nftData['attributes'], true),
            'external_url' => base_url("products/{$nftData['product_id']}")
        ];
    }
    
    private function uploadToIPFS(array $metadata): string
    {
        // Simular subida a IPFS
        return 'ipfs://QmYwAPJzv5CZsnA625s3Xf2nemtYgPpHdWEz79ojWnPbdG';
    }
    
    private function mintNFT(int $nftId, string $metadataUri): array
    {
        // Simular minteo de NFT
        return [
            'success' => true,
            'token_id' => $nftId,
            'contract_address' => $this->config['nft_contract_address'],
            'tx_hash' => '0x' . bin2hex(random_bytes(32))
        ];
    }
    
    private function sendLoyaltyTokens(int $userId, int $amount): array
    {
        // Simular envío de tokens
        return ['success' => true, 'tx_hash' => '0x' . bin2hex(random_bytes(32))];
    }
    
    private function updateUserTokenBalance(int $userId, int $amount): void
    {
        $this->db->query("
            INSERT INTO user_token_balances (user_id, balance, updated_at) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            balance = balance + VALUES(balance), 
            updated_at = VALUES(updated_at)
        ", [$userId, $amount, date('Y-m-d H:i:s')]);
    }
    
    private function getUserTokenBalance(int $userId): int
    {
        $result = $this->db->table('user_token_balances')
                          ->where('user_id', $userId)
                          ->get()
                          ->getRowArray();
        
        return $result['balance'] ?? 0;
    }
    
    private function groupTransactionsByCurrency(array $transactions): array
    {
        $grouped = [];
        foreach ($transactions as $tx) {
            $currency = $tx['currency'];
            if (!isset($grouped[$currency])) {
                $grouped[$currency] = ['count' => 0, 'volume' => 0];
            }
            $grouped[$currency]['count']++;
            $grouped[$currency]['volume'] += $tx['amount_fiat'];
        }
        return $grouped;
    }
    
    private function getTotalNFTSupply(): int
    {
        return $this->db->table('product_nfts')
                       ->where('status', 'minted')
                       ->selectSum('max_supply')
                       ->get()
                       ->getRowArray()['max_supply'] ?? 0;
    }
    
    private function getTotalTokenSupply(): int
    {
        return $this->db->table('loyalty_token_transactions')
                       ->where('type', 'earned')
                       ->selectSum('amount')
                       ->get()
                       ->getRowArray()['amount'] ?? 0;
    }
    
    private function getActiveTokenHolders(): int
    {
        return $this->db->table('user_token_balances')
                       ->where('balance >', 0)
                       ->countAllResults();
    }
    
    /**
     * Crear tablas de blockchain
     */
    private function createBlockchainTables(): void
    {
        try {
            // Tabla de transacciones de criptomonedas
            $this->db->query("
                CREATE TABLE IF NOT EXISTS crypto_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    currency VARCHAR(10) NOT NULL,
                    amount_fiat DECIMAL(10,2) NOT NULL,
                    amount_crypto DECIMAL(18,8) NOT NULL,
                    payment_address VARCHAR(255) NOT NULL,
                    status ENUM('pending', 'confirmed', 'expired', 'failed') DEFAULT 'pending',
                    confirmations INT DEFAULT 0,
                    tx_hash VARCHAR(255),
                    block_height INT,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_order_id (order_id),
                    INDEX idx_status (status),
                    INDEX idx_currency (currency)
                )
            ");
            
            // Tabla de NFTs de productos
            $this->db->query("
                CREATE TABLE IF NOT EXISTS product_nfts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    token_id INT,
                    contract_address VARCHAR(255),
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    image_url VARCHAR(500),
                    attributes JSON,
                    metadata_uri VARCHAR(500),
                    max_supply INT DEFAULT 1,
                    price_eth DECIMAL(18,8),
                    creator_address VARCHAR(255),
                    status ENUM('pending', 'minted', 'sold') DEFAULT 'pending',
                    tx_hash VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_product_id (product_id),
                    INDEX idx_status (status)
                )
            ");
            
            // Tabla de transacciones de tokens de fidelidad
            $this->db->query("
                CREATE TABLE IF NOT EXISTS loyalty_token_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    amount INT NOT NULL,
                    type ENUM('earned', 'redeemed', 'transferred') NOT NULL,
                    reason VARCHAR(255),
                    purchase_amount DECIMAL(10,2),
                    fiat_value DECIMAL(10,2),
                    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
                    tx_hash VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_user_id (user_id),
                    INDEX idx_type (type),
                    INDEX idx_created_at (created_at)
                )
            ");
            
            // Tabla de balances de tokens de usuarios
            $this->db->query("
                CREATE TABLE IF NOT EXISTS user_token_balances (
                    user_id INT PRIMARY KEY,
                    balance INT DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Blockchain tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'supported_currencies' => $this->getSupportedCurrencies(),
            'nft_enabled' => $this->config['nft_enabled'],
            'loyalty_token_enabled' => $this->config['loyalty_token_enabled'],
            'loyalty_token_symbol' => $this->config['loyalty_token_symbol'],
            'confirmation_blocks' => $this->config['confirmation_blocks']
        ];
    }
    
    private function getSupportedCurrencies(): array
    {
        $currencies = [];
        if ($this->config['bitcoin_enabled']) $currencies[] = 'BTC';
        if ($this->config['ethereum_enabled']) $currencies[] = 'ETH';
        if ($this->config['bsc_enabled']) $currencies[] = 'BNB';
        if ($this->config['usdt_enabled']) $currencies[] = 'USDT';
        if ($this->config['usdc_enabled']) $currencies[] = 'USDC';
        if ($this->config['busd_enabled']) $currencies[] = 'BUSD';
        return $currencies;
    }
}
