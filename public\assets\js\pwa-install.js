/**
 * Simple PWA Installation Manager
 * Focused only on installation functionality
 */

class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.init();
    }

    init() {
        console.log('PWA Installer: Initializing...');
        
        // Check if already installed
        this.checkInstallStatus();
        
        // Register service worker
        this.registerServiceWorker();
        
        // Setup install prompt listener
        this.setupInstallPrompt();
        
        // Setup install banner
        this.setupInstallBanner();
    }

    checkInstallStatus() {
        // Check if running as installed PWA
        if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
            this.isInstalled = true;
            console.log('PWA Installer: App is already installed');
            return;
        }

        // Check if running in PWA mode (iOS)
        if (window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('PWA Installer: App is running in standalone mode (iOS)');
            return;
        }

        console.log('PWA Installer: App is not installed');
    }

    async registerServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.log('PWA Installer: Service Worker not supported');
            return;
        }

        try {
            console.log('PWA Installer: Registering Service Worker...');
            
            const registration = await navigator.serviceWorker.register('/sw.js', {
                scope: '/',
                updateViaCache: 'none'
            });

            console.log('PWA Installer: Service Worker registered successfully');
            
            // Handle updates
            registration.addEventListener('updatefound', () => {
                console.log('PWA Installer: Service Worker update found');
            });

        } catch (error) {
            console.error('PWA Installer: Service Worker registration failed:', error);
        }
    }

    setupInstallPrompt() {
        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA Installer: beforeinstallprompt event fired');
            
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            
            // Save the event so it can be triggered later
            this.deferredPrompt = e;
            
            // Show install banner
            this.showInstallBanner();
        });

        // Listen for the appinstalled event
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA Installer: App was installed');
            this.isInstalled = true;
            this.hideInstallBanner();
            this.deferredPrompt = null;
        });
    }

    setupInstallBanner() {
        // Create install banner if it doesn't exist
        if (!document.getElementById('pwa-install-banner')) {
            const banner = document.createElement('div');
            banner.id = 'pwa-install-banner';
            banner.className = 'pwa-install-banner';
            banner.style.display = 'none';
            banner.innerHTML = `
                <div class="pwa-banner-content">
                    <div class="pwa-banner-icon">
                        <img src="/icon-192x192.png" alt="MrCell" width="40" height="40">
                    </div>
                    <div class="pwa-banner-text">
                        <strong>Instalar MrCell</strong>
                        <p>Instala nuestra app para una mejor experiencia</p>
                    </div>
                    <div class="pwa-banner-actions">
                        <button onclick="pwaInstaller.install()" class="btn btn-primary btn-sm">Instalar</button>
                        <button onclick="pwaInstaller.dismissBanner()" class="btn btn-outline-secondary btn-sm">Ahora no</button>
                    </div>
                </div>
            `;
            document.body.appendChild(banner);
        }
    }

    showInstallBanner() {
        if (this.isInstalled) {
            console.log('PWA Installer: App already installed, not showing banner');
            return;
        }

        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.style.display = 'block';
            console.log('PWA Installer: Install banner shown');
        }
    }

    hideInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.style.display = 'none';
            console.log('PWA Installer: Install banner hidden');
        }
    }

    dismissBanner() {
        this.hideInstallBanner();
        // Remember user dismissed (optional - could use localStorage)
        localStorage.setItem('pwa-install-dismissed', Date.now());
    }

    async install() {
        if (!this.deferredPrompt) {
            console.log('PWA Installer: No install prompt available');
            alert('La instalación no está disponible en este momento. Intenta desde Chrome en Android o Edge en Windows.');
            return;
        }

        console.log('PWA Installer: Showing install prompt');
        
        // Show the install prompt
        this.deferredPrompt.prompt();
        
        // Wait for the user to respond to the prompt
        const { outcome } = await this.deferredPrompt.userChoice;
        
        console.log(`PWA Installer: User response: ${outcome}`);
        
        if (outcome === 'accepted') {
            console.log('PWA Installer: User accepted the install prompt');
        } else {
            console.log('PWA Installer: User dismissed the install prompt');
        }
        
        // Clear the deferredPrompt
        this.deferredPrompt = null;
        this.hideInstallBanner();
    }

    // Check if installation is possible
    canInstall() {
        return this.deferredPrompt !== null && !this.isInstalled;
    }

    // Force show banner (for testing)
    forceShowBanner() {
        this.showInstallBanner();
    }
}

// Initialize PWA Installer
const pwaInstaller = new PWAInstaller();

// Global functions for backward compatibility
window.installPWA = () => pwaInstaller.install();
window.dismissPWA = () => pwaInstaller.dismissBanner();

// Export for use in other scripts
window.pwaInstaller = pwaInstaller;
