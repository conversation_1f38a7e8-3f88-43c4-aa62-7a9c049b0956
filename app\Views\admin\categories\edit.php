<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-edit me-2"></i>Editar Categoría</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/admin/categories">Categorías</a></li>
                <li class="breadcrumb-item active">Editar <?= esc($category['name']) ?></li>
            </ol>
        </nav>
    </div>
    <div class="mt-3">
        <a href="/admin/categories" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Editar Categoría: <?= esc($category['name']) ?></h5>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/categories/edit/<?= $category['id'] ?>" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre de la Categoría *</label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       value="<?= old('name', $category['name']) ?>" placeholder="Ej: Smartphones">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Categoría Padre</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">Sin categoría padre</option>
                                    <?php if (!empty($parentCategories)): ?>
                                        <?php foreach ($parentCategories as $parent): ?>
                                            <?php if ($parent['id'] != $category['id']): // No puede ser padre de sí misma ?>
                                                <option value="<?= $parent['id'] ?>" 
                                                        <?= old('parent_id', $category['parent_id']) == $parent['id'] ? 'selected' : '' ?>>
                                                    <?= esc($parent['display_name']) ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Descripción de la categoría"><?= old('description', $category['description']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">Imagen de la Categoría</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Formatos permitidos: JPG, PNG, GIF. Máximo 2MB. Dejar vacío para mantener la imagen actual.</div>

                        <!-- Imagen actual -->
                        <?php if (!empty($category['image'])): ?>
                            <div class="mt-3">
                                <label class="form-label">Imagen Actual</label>
                                <div class="position-relative d-inline-block">
                                    <img src="<?= base_url($category['image']) ?>" alt="Imagen actual"
                                         class="img-fluid rounded shadow-sm"
                                         style="max-height: 200px;">
                                    <button type="button" class="btn btn-danger btn-sm position-absolute"
                                            onclick="removeCurrentImage()"
                                            style="top: 5px; right: 5px; border-radius: 50%; width: 30px; height: 30px; padding: 0;"
                                            title="Eliminar imagen actual">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <input type="hidden" id="remove_image" name="remove_image" value="0">
                            </div>
                        <?php endif; ?>

                        <!-- Vista previa de nueva imagen -->
                        <div id="image_preview" class="mt-3" style="display: none;">
                            <label class="form-label">Vista Previa de Nueva Imagen</label>
                            <div class="position-relative d-inline-block">
                                <img id="preview_img" src="" alt="Vista previa"
                                     class="img-fluid rounded shadow-sm"
                                     style="max-height: 200px; cursor: pointer;">
                                <button type="button" class="btn btn-danger btn-sm position-absolute"
                                        onclick="removeImagePreview()"
                                        style="top: 5px; right: 5px; border-radius: 50%; width: 30px; height: 30px; padding: 0;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Orden de Clasificación</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?= old('sort_order', $category['sort_order']) ?>" min="0" placeholder="0">
                                <div class="form-text">Número para ordenar las categorías (menor número = mayor prioridad)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" <?= old('is_active', $category['is_active']) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        Categoría activa
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="window.location.href='/admin/categories'">
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Categoría
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información de la Categoría</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td><?= $category['id'] ?></td>
                    </tr>
                    <tr>
                        <td><strong>Productos:</strong></td>
                        <td><?= $category['products_count'] ?></td>
                    </tr>
                    <tr>
                        <td><strong>Creada:</strong></td>
                        <td><?= date('d/m/Y H:i', strtotime($category['created_at'])) ?></td>
                    </tr>
                    <?php if ($category['updated_at']): ?>
                    <tr>
                        <td><strong>Actualizada:</strong></td>
                        <td><?= date('d/m/Y H:i', strtotime($category['updated_at'])) ?></td>
                    </tr>
                    <?php endif; ?>
                </table>

                <hr>

                <h6>Consejos para editar categorías:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Cambiar el nombre afectará URLs</li>
                    <li><i class="fas fa-info-circle text-info me-2"></i>No puedes eliminar categorías con productos</li>
                    <li><i class="fas fa-check text-success me-2"></i>Desactivar oculta la categoría de la tienda</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Vista previa de imagen
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validar tamaño (2MB máximo)
        if (file.size > 2 * 1024 * 1024) {
            alert('La imagen es demasiado grande. Máximo 2MB.');
            this.value = '';
            return;
        }

        // Validar tipo
        if (!file.type.match('image.*')) {
            alert('Por favor selecciona un archivo de imagen válido.');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview_img').src = e.target.result;
            document.getElementById('image_preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

function removeImagePreview() {
    document.getElementById('image').value = '';
    document.getElementById('image_preview').style.display = 'none';
}

function removeCurrentImage() {
    if (confirm('¿Estás seguro de que quieres eliminar la imagen actual?')) {
        document.getElementById('remove_image').value = '1';
        // Ocultar la imagen actual
        event.target.closest('.position-relative').style.display = 'none';
    }
}
</script>
<?= $this->endSection() ?>
