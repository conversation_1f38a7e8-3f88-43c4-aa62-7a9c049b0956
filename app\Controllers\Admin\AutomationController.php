<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AutomationManager;
use App\Libraries\SystemMonitor;
use App\Libraries\SocialMediaManager;
use App\Libraries\GoogleAnalytics4Manager;

/**
 * Controlador de Administración de Automatizaciones
 * Panel web para gestionar automatizaciones sin CLI
 */
class AutomationController extends BaseController
{
    protected $automationManager;
    protected $systemMonitor;
    protected $socialManager;
    protected $ga4Manager;
    
    public function __construct()
    {
        $this->automationManager = new AutomationManager();
        $this->systemMonitor = new SystemMonitor();
        $this->socialManager = new SocialMediaManager();
        $this->ga4Manager = new GoogleAnalytics4Manager();
    }
    
    /**
     * Dashboard principal de automatizaciones
     */
    public function index()
    {
        // Verificar permisos de administrador
        if (!$this->isAdmin()) {
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado');
        }
        
        $data = [
            'title' => 'Panel de Automatizaciones',
            'automation_stats' => $this->getAutomationStats(),
            'system_status' => $this->getSystemStatus(),
            'recent_logs' => $this->getRecentLogs(),
            'cron_instructions' => $this->getCronInstructions()
        ];
        
        return view('admin/automation/dashboard', $data);
    }
    
    /**
     * Ejecutar automatización específica
     */
    public function runTask($task = null)
    {
        if (!$this->isAdmin()) {
            return $this->response->setJSON(['success' => false, 'error' => 'Acceso denegado']);
        }
        
        try {
            set_time_limit(300); // 5 minutos
            
            switch ($task) {
                case 'all':
                    $result = $this->automationManager->runScheduledTasks();
                    break;
                case 'prices':
                    $result = $this->automationManager->runPriceMonitoring();
                    break;
                case 'stock':
                    $result = $this->automationManager->runStockAlerts();
                    break;
                case 'wishlist':
                    $result = $this->automationManager->runWishlistReminders();
                    break;
                case 'cleanup':
                    $result = $this->automationManager->runDataCleanup();
                    break;
                case 'backup':
                    $result = $this->automationManager->runBackup();
                    break;
                case 'performance':
                    $result = $this->automationManager->runPerformanceAnalysis();
                    break;
                case 'analytics':
                    $result = $this->automationManager->runAnalyticsSync();
                    break;
                case 'cache':
                    $result = $this->automationManager->runCacheMaintenance();
                    break;
                default:
                    return $this->response->setJSON(['success' => false, 'error' => 'Tarea no válida']);
            }
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Ejecutar verificación del sistema
     */
    public function systemCheck()
    {
        if (!$this->isAdmin()) {
            return $this->response->setJSON(['success' => false, 'error' => 'Acceso denegado']);
        }
        
        try {
            $result = $this->systemMonitor->runSystemCheck();
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Configuración de cron jobs
     */
    public function cronJobs()
    {
        if (!$this->isAdmin()) {
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado');
        }
        
        $data = [
            'title' => 'Configuración de Cron Jobs',
            'cron_jobs' => $this->getCronJobsConfig(),
            'instructions' => $this->getCronInstructions(),
            'server_info' => $this->getServerInfo()
        ];
        
        return view('admin/automation/cron_jobs', $data);
    }
    
    /**
     * Logs de automatizaciones
     */
    public function logs()
    {
        if (!$this->isAdmin()) {
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado');
        }
        
        $page = (int) ($this->request->getGet('page') ?? 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        $db = \Config\Database::connect();
        
        // Obtener logs de automatización
        $automationLogs = $db->table('automation_log')
                            ->orderBy('created_at', 'DESC')
                            ->limit($limit, $offset)
                            ->get()
                            ->getResultArray();
        
        // Obtener total para paginación
        $totalLogs = $db->table('automation_log')->countAllResults();
        
        $data = [
            'title' => 'Logs de Automatizaciones',
            'logs' => $automationLogs,
            'current_page' => $page,
            'total_pages' => ceil($totalLogs / $limit),
            'total_logs' => $totalLogs
        ];
        
        return view('admin/automation/logs', $data);
    }
    
    /**
     * Configuraciones del sistema
     */
    public function settings()
    {
        if (!$this->isAdmin()) {
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado');
        }
        
        $db = \Config\Database::connect();
        
        if ($this->request->getMethod() === 'POST') {
            // Actualizar configuraciones
            $settings = $this->request->getPost('settings');
            
            foreach ($settings as $category => $categorySettings) {
                foreach ($categorySettings as $key => $value) {
                    $db->table('system_settings')
                       ->where('category', $category)
                       ->where('key', $key)
                       ->update([
                           'value' => $value,
                           'updated_at' => date('Y-m-d H:i:s')
                       ]);
                }
            }
            
            return redirect()->to('/admin/automation/settings')
                           ->with('success', 'Configuraciones actualizadas correctamente');
        }
        
        // Obtener configuraciones actuales
        $settings = $db->table('system_settings')
                      ->where('is_editable', 1)
                      ->orderBy('category, key')
                      ->get()
                      ->getResultArray();
        
        // Agrupar por categoría
        $groupedSettings = [];
        foreach ($settings as $setting) {
            $groupedSettings[$setting['category']][] = $setting;
        }
        
        $data = [
            'title' => 'Configuraciones del Sistema',
            'settings' => $groupedSettings
        ];
        
        return view('admin/automation/settings', $data);
    }
    
    /**
     * Alertas del sistema
     */
    public function alerts()
    {
        if (!$this->isAdmin()) {
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado');
        }
        
        $db = \Config\Database::connect();
        
        // Obtener alertas activas
        $activeAlerts = $db->table('system_alerts')
                          ->where('is_resolved', 0)
                          ->orderBy('severity DESC, created_at DESC')
                          ->limit(50)
                          ->get()
                          ->getResultArray();
        
        // Obtener estadísticas de alertas
        $alertStats = $db->table('system_alerts')
                        ->select('
                            alert_type,
                            severity,
                            COUNT(*) as count
                        ')
                        ->where('created_at >', date('Y-m-d H:i:s', strtotime('-7 days')))
                        ->groupBy('alert_type, severity')
                        ->get()
                        ->getResultArray();
        
        $data = [
            'title' => 'Alertas del Sistema',
            'active_alerts' => $activeAlerts,
            'alert_stats' => $alertStats
        ];
        
        return view('admin/automation/alerts', $data);
    }
    
    /**
     * Resolver alerta
     */
    public function resolveAlert($alertId)
    {
        if (!$this->isAdmin()) {
            return $this->response->setJSON(['success' => false, 'error' => 'Acceso denegado']);
        }
        
        try {
            $db = \Config\Database::connect();
            
            $updated = $db->table('system_alerts')
                         ->where('id', $alertId)
                         ->update([
                             'is_resolved' => 1,
                             'resolved_at' => date('Y-m-d H:i:s'),
                             'resolved_by' => session()->get('user_id')
                         ]);
            
            if ($updated) {
                return $this->response->setJSON(['success' => true, 'message' => 'Alerta resuelta']);
            } else {
                return $this->response->setJSON(['success' => false, 'error' => 'Alerta no encontrada']);
            }
            
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Obtener estadísticas de automatizaciones
     */
    private function getAutomationStats()
    {
        try {
            return $this->automationManager->getAutomationStats(7);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Obtener estado del sistema
     */
    private function getSystemStatus()
    {
        try {
            $db = \Config\Database::connect();
            
            // Última verificación del sistema
            $lastCheck = $db->table('system_metrics')
                           ->where('metric_name', 'system_health_check')
                           ->orderBy('created_at', 'DESC')
                           ->limit(1)
                           ->get()
                           ->getRowArray();
            
            // Alertas activas
            $activeAlerts = $db->table('system_alerts')
                              ->where('is_resolved', 0)
                              ->countAllResults();
            
            return [
                'last_check' => $lastCheck,
                'active_alerts' => $activeAlerts,
                'status' => $activeAlerts > 0 ? 'warning' : 'healthy'
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Obtener logs recientes
     */
    private function getRecentLogs()
    {
        try {
            $db = \Config\Database::connect();
            
            return $db->table('automation_log')
                     ->orderBy('created_at', 'DESC')
                     ->limit(10)
                     ->get()
                     ->getResultArray();
                     
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Obtener configuración de cron jobs
     */
    private function getCronJobsConfig()
    {
        $baseUrl = base_url();
        
        return [
            [
                'name' => 'Monitoreo del Sistema',
                'schedule' => '*/5 * * * *',
                'description' => 'Verificación cada 5 minutos',
                'url' => $baseUrl . 'admin/automation/run-task/system-check',
                'priority' => 'high'
            ],
            [
                'name' => 'Alertas de Stock',
                'schedule' => '*/15 * * * *',
                'description' => 'Verificación cada 15 minutos',
                'url' => $baseUrl . 'admin/automation/run-task/stock',
                'priority' => 'high'
            ],
            [
                'name' => 'Monitor de Precios',
                'schedule' => '*/30 * * * *',
                'description' => 'Verificación cada 30 minutos',
                'url' => $baseUrl . 'admin/automation/run-task/prices',
                'priority' => 'medium'
            ],
            [
                'name' => 'Automatizaciones Completas',
                'schedule' => '0 * * * *',
                'description' => 'Ejecución cada hora',
                'url' => $baseUrl . 'admin/automation/run-task/all',
                'priority' => 'medium'
            ],
            [
                'name' => 'Backup Diario',
                'schedule' => '0 2 * * *',
                'description' => 'Backup a las 2:00 AM',
                'url' => $baseUrl . 'admin/automation/run-task/backup',
                'priority' => 'high'
            ],
            [
                'name' => 'Limpieza de Datos',
                'schedule' => '0 3 * * *',
                'description' => 'Limpieza a las 3:00 AM',
                'url' => $baseUrl . 'admin/automation/run-task/cleanup',
                'priority' => 'low'
            ],
            [
                'name' => 'Recordatorios Wishlist',
                'schedule' => '0 10 * * 0',
                'description' => 'Domingos a las 10:00 AM',
                'url' => $baseUrl . 'admin/automation/run-task/wishlist',
                'priority' => 'low'
            ],
            [
                'name' => 'Sincronización Analytics',
                'schedule' => '0 */2 * * *',
                'description' => 'Cada 2 horas',
                'url' => $baseUrl . 'admin/automation/run-task/analytics',
                'priority' => 'medium'
            ]
        ];
    }
    
    /**
     * Obtener instrucciones para cPanel
     */
    private function getCronInstructions()
    {
        return [
            'cpanel' => [
                'title' => 'Configuración en cPanel',
                'steps' => [
                    'Acceder al panel de cPanel de tu hosting',
                    'Buscar la sección "Cron Jobs" o "Tareas Cron"',
                    'Agregar cada tarea con su horario correspondiente',
                    'Usar el comando: wget -q -O - "URL_DE_LA_TAREA"',
                    'Configurar email de notificaciones (opcional)'
                ]
            ],
            'alternative' => [
                'title' => 'Ejecución Manual',
                'description' => 'Si no puedes configurar cron jobs, puedes ejecutar las tareas manualmente desde este panel.'
            ]
        ];
    }
    
    /**
     * Obtener información del servidor
     */
    private function getServerInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'base_url' => base_url(),
            'timezone' => date_default_timezone_get(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time')
        ];
    }
    
    /**
     * Verificar si el usuario es administrador
     */
    private function isAdmin()
    {
        $session = session();
        return $session->get('is_admin') === true || $session->get('user_role') === 'admin';
    }
}
