<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-file-invoice me-2"></i>Configuración de Facturación Electrónica
    </h1>
    <div>
        <a href="<?= base_url('admin/billing/history') ?>" class="btn btn-info">
            <i class="fas fa-history me-2"></i>Historial de Facturas
        </a>
        <button type="button" class="btn btn-success" onclick="testConnection()">
            <i class="fas fa-plug me-2"></i>Probar Conexión
        </button>
    </div>
</div>

<!-- Alertas -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Resultado de prueba de conexión -->
<?php if (!empty($test_connection_result)): ?>
    <div class="alert alert-<?= $test_connection_result['success'] ? 'success' : 'danger' ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $test_connection_result['success'] ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
        <strong>Prueba de Conexión:</strong> <?= esc($test_connection_result['message']) ?>
        <?php if (!empty($test_connection_result['details'])): ?>
            <br><small><?= esc($test_connection_result['details']) ?></small>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/billing/config/update') ?>" method="POST" id="billingConfigForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Configuración de API -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>Configuración de API
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="api_url" class="form-label">URL de la API <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="api_url" name="api_url" 
                                       value="<?= old('api_url', $config['api_url'] ?? 'https://facturalo.mayansource.com/api') ?>" 
                                       required placeholder="https://facturalo.mayansource.com/api">
                                <small class="form-text text-muted">URL base de la API de facturación electrónica</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="environment" class="form-label">Entorno <span class="text-danger">*</span></label>
                                <select class="form-select" id="environment" name="environment" required>
                                    <option value="sandbox" <?= old('environment', $config['environment'] ?? '') === 'sandbox' ? 'selected' : '' ?>>
                                        Sandbox (Pruebas)
                                    </option>
                                    <option value="production" <?= old('environment', $config['environment'] ?? '') === 'production' ? 'selected' : '' ?>>
                                        Producción
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" 
                                   value="<?= old('api_key', $config['api_key'] ?? '') ?>" 
                                   required placeholder="Tu API Key de facturalo.mayansource.com">
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('api_key')">
                                <i class="fas fa-eye" id="api_key_icon"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">Clave de API proporcionada por facturalo.mayansource.com</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timeout" class="form-label">Timeout (segundos)</label>
                                <input type="number" class="form-control" id="timeout" name="timeout" 
                                       value="<?= old('timeout', $config['timeout'] ?? 30) ?>" 
                                       min="5" max="300" placeholder="30">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="retry_attempts" class="form-label">Intentos de Reintento</label>
                                <input type="number" class="form-control" id="retry_attempts" name="retry_attempts" 
                                       value="<?= old('retry_attempts', $config['retry_attempts'] ?? 3) ?>" 
                                       min="1" max="10" placeholder="3">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="webhook_url" class="form-label">URL de Webhook (Opcional)</label>
                        <input type="url" class="form-control" id="webhook_url" name="webhook_url" 
                               value="<?= old('webhook_url', $config['webhook_url'] ?? '') ?>" 
                               placeholder="https://tudominio.com/webhook/billing">
                        <small class="form-text text-muted">URL para recibir notificaciones de estado de facturas</small>
                    </div>
                </div>
            </div>

            <!-- Información de la Empresa -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-building me-2"></i>Información de la Empresa
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_nit" class="form-label">NIT de la Empresa <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="company_nit" name="company_nit" 
                                       value="<?= old('company_nit', $config['company_nit'] ?? '') ?>" 
                                       required placeholder="12345678-9">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_name" class="form-label">Razón Social <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?= old('company_name', $config['company_name'] ?? '') ?>" 
                                       required placeholder="MrCell Guatemala, S.A.">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="company_address" class="form-label">Dirección <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="company_address" name="company_address" 
                                  rows="2" required placeholder="Dirección completa de la empresa"><?= old('company_address', $config['company_address'] ?? '') ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_phone" class="form-label">Teléfono</label>
                                <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                       value="<?= old('company_phone', $config['company_phone'] ?? '') ?>" 
                                       placeholder="2234-5678">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" 
                                       value="<?= old('company_email', $config['company_email'] ?? '') ?>" 
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel Lateral -->
        <div class="col-lg-4">
            <!-- Estado y Opciones -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-toggle-on me-2"></i>Opciones de Facturación
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="auto_generate" name="auto_generate" 
                                   value="1" <?= old('auto_generate', $config['auto_generate'] ?? 0) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="auto_generate">
                                Generar Automáticamente
                            </label>
                        </div>
                        <small class="form-text text-muted">Generar facturas automáticamente al completar pedidos</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="send_email" name="send_email" 
                                   value="1" <?= old('send_email', $config['send_email'] ?? 1) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="send_email">
                                Enviar por Email
                            </label>
                        </div>
                        <small class="form-text text-muted">Enviar facturas por email a los clientes</small>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Configuración
                        </button>
                        <button type="button" class="btn btn-success" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i>Probar Conexión
                        </button>
                    </div>
                </div>
            </div>

            <!-- Estado Actual -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Estado Actual
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($config)): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Estado:</span>
                                <span class="badge bg-<?= $config['environment'] === 'production' ? 'success' : 'warning' ?>">
                                    <?= $config['environment'] === 'production' ? 'Producción' : 'Pruebas' ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Auto-generar:</span>
                                <span class="badge bg-<?= $config['auto_generate'] ? 'success' : 'secondary' ?>">
                                    <?= $config['auto_generate'] ? 'Activo' : 'Inactivo' ?>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Última actualización:</span>
                                <small class="text-muted">
                                    <?= date('d/m/Y H:i', strtotime($config['updated_at'] ?? $config['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <p class="text-muted">No hay configuración guardada</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Ayuda -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-question-circle me-2"></i>Ayuda
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Configuración Inicial:</h6>
                        <ul class="list-unstyled small">
                            <li>1. Obtén tu API Key de facturalo.mayansource.com</li>
                            <li>2. Configura los datos de tu empresa</li>
                            <li>3. Prueba la conexión</li>
                            <li>4. Activa la generación automática</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary">Soporte:</h6>
                        <p class="small text-muted">
                            Para obtener tu API Key o resolver problemas, contacta a:
                            <br><strong><EMAIL></strong>
                        </p>
                    </div>

                    <a href="https://facturalo.mayansource.com/docs" target="_blank" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-external-link-alt me-2"></i>Documentación
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Mostrar/ocultar contraseña
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Probar conexión
function testConnection() {
    const form = document.getElementById('billingConfigForm');
    const formData = new FormData(form);
    
    // Cambiar la acción del formulario temporalmente
    const originalAction = form.action;
    form.action = '<?= base_url('admin/billing/config/test-connection') ?>';
    
    // Mostrar indicador de carga
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Probando...';
    button.disabled = true;
    
    // Enviar formulario
    form.submit();
}

// Validación del formulario
document.getElementById('billingConfigForm').addEventListener('submit', function(e) {
    const apiUrl = document.getElementById('api_url').value;
    const apiKey = document.getElementById('api_key').value;
    const companyNit = document.getElementById('company_nit').value;
    
    if (!apiUrl || !apiKey || !companyNit) {
        e.preventDefault();
        alert('Por favor completa todos los campos requeridos');
        return;
    }
    
    // Validar formato de NIT (básico)
    const nitPattern = /^\d{8,}-?\d?$/;
    if (!nitPattern.test(companyNit)) {
        e.preventDefault();
        alert('El formato del NIT no es válido');
        return;
    }
});

// Auto-guardar configuración cada 5 minutos si hay cambios
let hasChanges = false;
const formInputs = document.querySelectorAll('#billingConfigForm input, #billingConfigForm select, #billingConfigForm textarea');

formInputs.forEach(input => {
    input.addEventListener('change', () => {
        hasChanges = true;
    });
});

// Advertir sobre cambios no guardados
window.addEventListener('beforeunload', function(e) {
    if (hasChanges) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Marcar como guardado al enviar
document.getElementById('billingConfigForm').addEventListener('submit', function() {
    hasChanges = false;
});
</script>
<?= $this->endSection() ?>
