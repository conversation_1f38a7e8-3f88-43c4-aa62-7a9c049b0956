<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class WhatsAppGroupSettingSeeder extends Seeder
{
    public function run()
    {
        try {
            // Verificar si la configuración ya existe
            $existing = $this->db->query("
                SELECT id FROM system_settings 
                WHERE setting_key = 'whatsapp_alerts_group' 
                LIMIT 1
            ")->getRowArray();

            if (!$existing) {
                // Insertar la configuración del grupo de WhatsApp
                $this->db->table('system_settings')->insert([
                    'setting_key' => 'whatsapp_alerts_group',
                    'setting_value' => '120363416393766854',
                    'setting_type' => 'text',
                    'setting_group' => 'notifications',
                    'setting_description' => 'Número del grupo de WhatsApp para alertas automáticas del sistema',
                    'is_public' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                echo "✅ Configuración de grupo WhatsApp agregada: 120363416393766854\n";
            } else {
                // Actualizar la configuración existente
                $this->db->table('system_settings')
                    ->where('setting_key', 'whatsapp_alerts_group')
                    ->update([
                        'setting_value' => '120363416393766854',
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                echo "✅ Configuración de grupo WhatsApp actualizada: 120363416393766854\n";
            }

            // También verificar si existe en la tabla settings (por compatibilidad)
            $tableExists = $this->db->query("
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                    AND table_name = 'settings'
            ")->getRowArray();

            if ($tableExists && $tableExists['count'] > 0) {
                $existingSettings = $this->db->query("
                    SELECT id FROM settings 
                    WHERE key = 'whatsapp_alerts_group' 
                    LIMIT 1
                ")->getRowArray();

                if (!$existingSettings) {
                    $this->db->table('settings')->insert([
                        'key' => 'whatsapp_alerts_group',
                        'value' => '120363416393766854',
                        'description' => 'Número del grupo de WhatsApp para alertas automáticas',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    
                    echo "✅ Configuración también agregada a tabla settings\n";
                } else {
                    $this->db->table('settings')
                        ->where('key', 'whatsapp_alerts_group')
                        ->update([
                            'value' => '120363416393766854',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    
                    echo "✅ Configuración también actualizada en tabla settings\n";
                }
            }

            echo "\n🎉 Configuración de grupo WhatsApp completada\n";
            echo "📱 Número configurado: 120363416393766854\n";
            echo "⚙️ Editable desde: /admin/settings?tab=notifications\n";

        } catch (\Exception $e) {
            echo "❌ Error configurando grupo WhatsApp: " . $e->getMessage() . "\n";
        }
    }
}
