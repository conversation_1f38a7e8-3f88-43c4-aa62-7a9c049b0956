<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-edit me-2"></i>Editar Plantilla</h1>
        <a href="/admin/whatsapp/templates" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Volver a Plantillas
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <strong>Errores de validación:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    <?= esc($template['template_name']) ?>
                    <?php if ($template['is_mandatory']): ?>
                        <span class="badge bg-warning ms-2">Obligatoria</span>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/whatsapp/templates/edit/<?= $template['id'] ?>">
                    <div class="mb-3">
                        <label for="template_name" class="form-label">Nombre de la Plantilla</label>
                        <input type="text" class="form-control" id="template_name" name="template_name" 
                               value="<?= esc($template['template_name']) ?>" required>
                        <div class="form-text">Nombre descriptivo para identificar la plantilla</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" rows="2"><?= esc($template['description']) ?></textarea>
                        <div class="form-text">Descripción opcional de cuándo se usa esta plantilla</div>
                    </div>

                    <div class="mb-3">
                        <label for="message_template" class="form-label">Contenido del Mensaje</label>
                        <textarea class="form-control" id="message_template" name="message_template" 
                                  rows="8" required><?= esc($template['message_template']) ?></textarea>
                        <div class="form-text">
                            Usa variables entre llaves, ej: {customer_name}, {order_number}
                        </div>
                    </div>

                    <?php if (!$template['is_mandatory']): ?>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                       <?= $template['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    <strong>Plantilla activa</strong>
                                </label>
                            </div>
                            <div class="form-text">Las plantillas inactivas no se pueden usar</div>
                        </div>
                    <?php else: ?>
                        <input type="hidden" name="is_active" value="1">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Esta es una plantilla obligatoria y no se puede desactivar.
                        </div>
                    <?php endif; ?>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Cambios
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="previewMessage()">
                            <i class="fas fa-eye me-2"></i>Vista Previa
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="testTemplate()">
                            <i class="fas fa-paper-plane me-2"></i>Enviar Prueba
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Variables Detectadas -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Variables Detectadas</h6>
            </div>
            <div class="card-body">
                <div id="detectedVariables">
                    <?php if (!empty($template['variables'])): ?>
                        <?php $variables = is_string($template['variables']) ? json_decode($template['variables'], true) : $template['variables']; ?>
                        <?php if (is_array($variables)): ?>
                            <?php foreach ($variables as $var): ?>
                                <span class="badge bg-info me-1 mb-1">{<?= esc($var) ?>}</span>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="text-muted">Sin variables</span>
                    <?php endif; ?>
                </div>
                <hr>
                <small class="text-muted">
                    Las variables se detectan automáticamente cuando usas el formato {variable_name}
                </small>
            </div>
        </div>

        <!-- Vista Previa -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Vista Previa</h6>
            </div>
            <div class="card-body">
                <div class="border rounded p-3 bg-light">
                    <div id="messagePreview" class="small">
                        <?= nl2br(esc($template['message_template'])) ?>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary mt-2 w-100" onclick="updatePreview()">
                    <i class="fas fa-sync me-1"></i>Actualizar Vista Previa
                </button>
            </div>
        </div>

        <!-- Variables Comunes -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Variables Comunes</h6>
            </div>
            <div class="card-body">
                <h6 class="small">Cliente:</h6>
                <div class="mb-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('customer_name')">
                        {customer_name}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('customer_email')">
                        {customer_email}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('customer_phone')">
                        {customer_phone}
                    </button>
                </div>

                <h6 class="small">Orden:</h6>
                <div class="mb-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('order_number')">
                        {order_number}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('order_total')">
                        {order_total}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('order_status')">
                        {order_status}
                    </button>
                </div>

                <h6 class="small">Producto:</h6>
                <div class="mb-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('product_name')">
                        {product_name}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('product_price')">
                        {product_price}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1 mb-1" onclick="insertVariable('discount_amount')">
                        {discount_amount}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para prueba de mensaje -->
<div class="modal fade" id="testMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Enviar Mensaje de Prueba</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testMessageForm">
                    <div class="mb-3">
                        <label for="testPhoneNumber" class="form-label">Número de Teléfono</label>
                        <input type="text" class="form-control" id="testPhoneNumber" 
                               placeholder="Ej: 50212345678" required>
                    </div>
                    <div id="testVariables">
                        <!-- Variables cargadas dinámicamente -->
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Vista Previa del Mensaje</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="testMessagePreview"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="sendTestMessage()">
                    <i class="fas fa-paper-plane me-1"></i>Enviar
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Detectar variables en tiempo real
document.getElementById('message_template').addEventListener('input', function() {
    detectVariables();
    updatePreview();
});

function detectVariables() {
    const message = document.getElementById('message_template').value;
    const variables = message.match(/\{([^}]+)\}/g);
    
    const container = document.getElementById('detectedVariables');
    
    if (variables && variables.length > 0) {
        const uniqueVars = [...new Set(variables)];
        container.innerHTML = uniqueVars.map(v => 
            `<span class="badge bg-info me-1 mb-1">${v}</span>`
        ).join('');
    } else {
        container.innerHTML = '<span class="text-muted">Sin variables</span>';
    }
}

function updatePreview() {
    const message = document.getElementById('message_template').value;
    document.getElementById('messagePreview').innerHTML = message.replace(/\n/g, '<br>');
}

function insertVariable(variable) {
    const textarea = document.getElementById('message_template');
    const cursorPos = textarea.selectionStart;
    const textBefore = textarea.value.substring(0, cursorPos);
    const textAfter = textarea.value.substring(cursorPos);
    
    textarea.value = textBefore + '{' + variable + '}' + textAfter;
    textarea.focus();
    textarea.setSelectionRange(cursorPos + variable.length + 2, cursorPos + variable.length + 2);
    
    detectVariables();
    updatePreview();
}

function previewMessage() {
    updatePreview();
    // Scroll to preview
    document.getElementById('messagePreview').scrollIntoView({ behavior: 'smooth' });
}

function testTemplate() {
    const message = document.getElementById('message_template').value;
    const variables = message.match(/\{([^}]+)\}/g);
    
    let variablesHtml = '';
    if (variables && variables.length > 0) {
        const uniqueVars = [...new Set(variables.map(v => v.slice(1, -1)))];
        variablesHtml = '<h6>Variables:</h6>';
        uniqueVars.forEach(variable => {
            variablesHtml += `
                <div class="mb-3">
                    <label for="test_var_${variable}" class="form-label">{${variable}}</label>
                    <input type="text" class="form-control test-variable" 
                           id="test_var_${variable}" data-variable="${variable}"
                           placeholder="Valor para ${variable}">
                </div>
            `;
        });
    }
    
    document.getElementById('testVariables').innerHTML = variablesHtml;
    updateTestPreview();
    
    // Agregar event listeners
    document.querySelectorAll('.test-variable').forEach(input => {
        input.addEventListener('input', updateTestPreview);
    });
    
    new bootstrap.Modal(document.getElementById('testMessageModal')).show();
}

function updateTestPreview() {
    let message = document.getElementById('message_template').value;
    
    document.querySelectorAll('.test-variable').forEach(input => {
        const variable = input.dataset.variable;
        const value = input.value || `{${variable}}`;
        message = message.replace(new RegExp(`\\{${variable}\\}`, 'g'), value);
    });
    
    document.getElementById('testMessagePreview').innerHTML = message.replace(/\n/g, '<br>');
}

function sendTestMessage() {
    const phoneNumber = document.getElementById('testPhoneNumber').value;
    if (!phoneNumber) {
        alert('Por favor ingresa un número de teléfono');
        return;
    }
    
    let message = document.getElementById('message_template').value;
    
    // Reemplazar variables
    document.querySelectorAll('.test-variable').forEach(input => {
        const variable = input.dataset.variable;
        const value = input.value || `{${variable}}`;
        message = message.replace(new RegExp(`\\{${variable}\\}`, 'g'), value);
    });
    
    fetch('/admin/whatsapp/send-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: phoneNumber,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Mensaje enviado correctamente');
            bootstrap.Modal.getInstance(document.getElementById('testMessageModal')).hide();
        } else {
            alert('Error al enviar mensaje: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error de conexión');
    });
}

// Inicializar
detectVariables();
updatePreview();
</script>
<?= $this->endSection() ?>
