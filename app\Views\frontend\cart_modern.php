<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?><PERSON><PERSON> de Compras - MrCell Guatemala<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
        :root {
            --primary-color: #dc2626;
            --primary-dark: #b91c1c;
        }
        
        .cart-item {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .cart-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            max-width: 100%;
            transition: transform 0.2s ease;
        }

        .product-image:hover {
            transform: scale(1.05);
        }

        .cart-item h5 a {
            color: var(--dark-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .cart-item h5 a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        @media (max-width: 576px) {
            .product-image {
                width: 80px;
                height: 80px;
            }

            .cart-item {
                padding: 1rem;
            }
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px;
        }
        
        .cart-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            position: sticky;
            top: 100px;
        }
        
        .empty-cart {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-cart i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .page-title {
            color: white;
            margin-bottom: 0;
        }

        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 1rem;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.9);
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Cart Summary */
        .cart-summary {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            position: sticky;
            top: 2rem;
        }

        .cart-summary h4 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }


</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Carrito de Compras</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-shopping-cart me-2"></i>Carrito de Compras
                </h1>
            </div>
        </div>
    </div>
</div>

<!-- Cart Content -->
<div class="container my-5">
  <div class="row g-4">
    <!-- Col izquierda -->
    <div class="col-12 col-lg-8">
      <!-- Cart Items -->
      <div id="cart-items">
        <?php if (empty($cart_items)): ?>
          <!-- Empty Cart Message -->
          <div class="empty-cart">
            <i class="fas fa-shopping-cart"></i>
            <h3>Tu carrito está vacío</h3>
            <p class="text-muted mb-4">¡Agrega algunos productos increíbles a tu carrito!</p>
            <a href="<?= base_url('tienda') ?>" class="btn btn-primary btn-lg">
              <i class="fas fa-shopping-bag me-2"></i>Ir a la Tienda
            </a>
          </div>
        <?php else: ?>
          <!-- Cart Items -->
          <?php foreach ($cart_items as $item): ?>
            <div class="cart-item mb-3" data-product-id="<?= $item['product_id'] ?>">
              <div class="row align-items-center">
                <div class="col-12 col-sm-3 col-md-2 text-center mb-3 mb-sm-0">
                  <a href="<?= base_url('producto/' . ($item['slug'] ?? $item['product_id'])) ?>">
                    <img src="<?= product_image_url($item['image']) ?>"
                         alt="<?= esc($item['name']) ?>" class="product-image">
                  </a>
                </div>
                <div class="col-12 col-sm-9 col-md-10">
                  <div class="row align-items-center">
                    <div class="col-12 col-md-4 mb-2 mb-md-0">
                      <h5 class="mb-1">
                        <a href="<?= base_url('producto/' . ($item['slug'] ?? $item['product_id'])) ?>"
                           class="text-decoration-none text-dark">
                          <?= esc($item['name']) ?>
                        </a>
                      </h5>
                      <p class="text-muted mb-0 small">SKU: <?= esc($item['sku']) ?></p>
                      <small class="text-muted">Stock: <?= $item['stock'] ?></small>
                    </div>
                    <div class="col-6 col-md-3 mb-2 mb-md-0">
                      <label class="form-label small text-muted">Cantidad:</label>
                      <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(<?= $item['product_id'] ?>, <?= $item['quantity'] - 1 ?>)">
                          <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="<?= $item['quantity'] ?>"
                               min="1" max="<?= $item['stock'] ?>" readonly>
                        <button class="quantity-btn" onclick="updateQuantity(<?= $item['product_id'] ?>, <?= $item['quantity'] + 1 ?>)">
                          <i class="fas fa-plus"></i>
                        </button>
                      </div>
                    </div>
                    <div class="col-3 col-md-2 text-center mb-2 mb-md-0">
                      <small class="text-muted d-block">Precio:</small>
                      <strong>Q<?= number_format($item['price'], 2) ?></strong>
                    </div>
                    <div class="col-3 col-md-2 text-center mb-2 mb-md-0">
                      <small class="text-muted d-block">Subtotal:</small>
                      <strong class="text-primary">Q<?= number_format($item['subtotal'], 2) ?></strong>
                    </div>
                    <div class="col-12 col-md-1 text-center">
                      <button class="btn btn-sm btn-outline-danger"
                              onclick="removeFromCart(<?= $item['product_id'] ?>)"
                              title="Eliminar producto">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
    </div> <!-- ← CIERRE de la col-izquierda (.col-lg-8) -->

    <!-- Col derecha -->
    <div class="col-12 col-lg-4">
      <div class="cart-summary">
        <h4 class="mb-3">Resumen del Pedido</h4>

        <div class="d-flex justify-content-between mb-2">
          <span>Subtotal:</span>
          <span id="subtotal">Q<?= number_format($cart_subtotal, 2) ?></span>
        </div>

        <!-- Información de Envío -->
        <div class="mb-3">
          <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Información de Envío</h6>
            <p class="mb-0">Los métodos y costos de envío se calcularán según tu dirección de entrega en el siguiente paso.</p>
          </div>
        </div>



        <?php if (($tax_settings['tax_enabled'] ?? false)): ?>
        <div class="d-flex justify-content-between mb-2">
          <span><?= ($tax_settings['tax_name'] ?? 'IVA') ?> (<?= ($tax_settings['tax_rate'] ?? 12) ?>%):</span>
          <span id="tax">Q<?= number_format($tax_amount, 2) ?></span>
        </div>
        <?php endif; ?>

        <hr>

        <div class="d-flex justify-content-between mb-2">
          <strong>Total (sin envío):</strong>
          <strong class="text-primary" id="total">Q<?= number_format($cart_total, 2) ?></strong>
        </div>
        <small class="text-muted d-block mb-3">* El costo de envío se calculará según tu dirección</small>

        <div class="d-grid gap-2">
          <button class="btn btn-primary btn-lg" onclick="proceedToCheckout()" id="checkout-btn">
            <i class="fas fa-credit-card me-2"></i>Proceder al Checkout
          </button>
          <a href="<?= base_url('tienda') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Seguir Comprando
          </a>
        </div>

        <div class="mt-4 p-3 bg-light rounded">
          <h6><i class="fas fa-truck me-2"></i>Información de Envío</h6>
          <small class="text-muted">
            • Selecciona un método de envío para continuar<br>
            • Entrega en 2-3 días hábiles<br>
            • Cobertura en toda Guatemala
          </small>
        </div>
      </div>
    </div>
  </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Update quantity
    async function updateQuantity(productId, newQuantity) {
        if (newQuantity < 1) {
            removeFromCart(productId);
            return;
        }

        try {
            const response = await fetch('<?= base_url('api/cart/update') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: newQuantity
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                location.reload(); // Reload to show updated cart
            } else {
                alert('Error al actualizar cantidad: ' + data.message);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al actualizar cantidad');
        }
    }

    // Remove from cart
    async function removeFromCart(productId) {
        if (!confirm('¿Estás seguro de que quieres eliminar este producto del carrito?')) {
            return;
        }

        try {
            const response = await fetch(`<?= base_url('api/cart/remove') ?>/${productId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Update cart count using global function
                if (window.updateCartCount) {
                    window.updateCartCount();
                }
                location.reload(); // Reload to show updated cart
            } else {
                alert('Error al eliminar producto: ' + data.message);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al eliminar producto');
        }
    }

    // Función para proceder al checkout
    function proceedToCheckout() {
        // Verificar que hay productos en el carrito
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems.length === 0) {
            showAlert('error', 'Tu carrito está vacío');
            return;
        }

        // Redirigir al checkout
        window.location.href = '<?= base_url('checkout') ?>';
    }

    // Función para mostrar alertas
    function showAlert(type, message) {
        // Crear elemento de alerta
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insertar al inicio del contenedor principal
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
        }

        // Auto-remover después de 5 segundos
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Load cart items on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Update cart count using global function
        if (window.updateCartCount) {
            window.updateCartCount();
        }

        // Verificar si hay productos en el carrito
        const cartItems = document.querySelectorAll('.cart-item');
        const checkoutBtn = document.getElementById('checkout-btn');

        if (cartItems.length === 0) {
            checkoutBtn.disabled = true;
            checkoutBtn.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Carrito Vacío';
        }
    });
</script>
<?= $this->endSection() ?>
