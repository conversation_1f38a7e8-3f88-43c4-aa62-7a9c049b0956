<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CheckTableStructure extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'debug:table-structure';
    protected $description = 'Verificar estructura de tabla payment_methods';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== ESTRUCTURA DE TABLA payment_methods ===', 'yellow');
            CLI::newLine();
            
            // Obtener estructura de la tabla
            $fields = $db->getFieldData('payment_methods');
            
            CLI::write('Campos de la tabla:', 'cyan');
            foreach ($fields as $field) {
                CLI::write("- {$field->name}: {$field->type} ({$field->max_length})", 'white');
            }
            
            CLI::newLine();
            CLI::write('=== DATOS ACTUALES ===', 'yellow');
            
            // Obtener datos actuales
            $data = $db->query("SELECT * FROM payment_methods WHERE slug = 'recurrente'")->getRowArray();
            
            CLI::write('Datos de Recurrente:', 'cyan');
            foreach ($data as $key => $value) {
                CLI::write("- {$key}: '{$value}'", 'white');
            }
            
            CLI::newLine();
            CLI::write('=== INTENTANDO ACTUALIZACIÓN DIRECTA ===', 'yellow');
            
            // Intentar actualización con diferentes enfoques
            $queries = [
                "UPDATE payment_methods SET type = 'gateway' WHERE slug = 'recurrente'",
                "UPDATE payment_methods SET `type` = 'gateway' WHERE slug = 'recurrente'",
                "UPDATE payment_methods SET type = 'gateway' WHERE id = 8"
            ];
            
            foreach ($queries as $i => $query) {
                CLI::write("Query " . ($i + 1) . ": {$query}", 'white');
                try {
                    $result = $db->query($query);
                    CLI::write("Resultado: " . ($result ? 'SUCCESS' : 'FAILED'), $result ? 'green' : 'red');
                    
                    // Verificar cambio
                    $check = $db->query("SELECT type FROM payment_methods WHERE slug = 'recurrente'")->getRowArray();
                    CLI::write("Tipo después: '{$check['type']}'", 'cyan');
                } catch (\Exception $e) {
                    CLI::error("Error: " . $e->getMessage());
                }
                CLI::newLine();
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
