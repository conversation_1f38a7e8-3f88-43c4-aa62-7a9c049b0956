<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-tags me-2"></i>Gestión de Categorías</h1>
        <a href="/admin/categories/create" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nueva Categoría
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Categorías</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Imagen</th>
                                <th>Nombre</th>
                                <th>Categoría Padre</th>
                                <th>Productos</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($categories)): ?>
                                <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td><?= $category['id'] ?></td>
                                    <td>
                                        <?php if (!empty($category['image'])): ?>
                                            <img src="<?= base_url($category['image']) ?>" alt="<?= esc($category['name']) ?>"
                                                 class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        // Mostrar jerarquía visual - verificar parent_id directamente
                                        $isChild = !empty($category['parent_id']) && $category['parent_id'] !== null;
                                        if ($isChild): ?>
                                            <span class="text-muted me-2">└─</span>
                                        <?php endif; ?>
                                        <strong><?= esc($category['name']) ?></strong>
                                        <?php if ($isChild): ?>
                                            <br><small class="text-muted">Subcategoría</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($category['parent_id']) && $category['parent_id'] !== null): ?>
                                            <span class="badge bg-info"><?= esc($category['parent_name']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">—</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= $category['products_count'] ?></span>
                                    </td>
                                    <td>
                                        <?php if ($category['is_active']): ?>
                                            <span class="badge bg-success">Activa</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactiva</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="/admin/categories/edit/<?= $category['id'] ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" title="Eliminar" onclick="deleteCategory(<?= $category['id'] ?>, '<?= esc($category['name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No hay categorías registradas</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación de Eliminación -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirmar Eliminación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3">¿Estás seguro de que deseas eliminar la categoría?</h6>
                <p class="text-muted mb-0" id="categoryNameToDelete"></p>
                <small class="text-muted">Esta acción no se puede deshacer.</small>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>Eliminar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Carga -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content border-0 shadow">
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
                <h6 class="mb-2">Procesando...</h6>
                <p class="text-muted mb-0 small">Por favor espere un momento</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="resultModalLabel">
                    <i id="resultIcon" class="me-2"></i>
                    <span id="resultTitle"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i id="resultMainIcon" style="font-size: 3rem;"></i>
                </div>
                <p id="resultMessage" class="mb-0"></p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="resultOkBtn">
                    <i class="fas fa-check me-2"></i>Entendido
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para gestión de categorías
    console.log('Gestión de categorías cargada');

    let categoryToDelete = null;

    function deleteCategory(id, name) {
        // Guardar datos de la categoría a eliminar
        categoryToDelete = { id, name };

        // Mostrar el nombre en el modal
        document.getElementById('categoryNameToDelete').innerHTML = `<strong>"${name}"</strong>`;

        // Mostrar modal de confirmación
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Manejar confirmación de eliminación
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!categoryToDelete) return;

        // Cerrar modal de confirmación
        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        deleteModal.hide();

        // Mostrar modal de carga
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Realizar la eliminación
        fetch(`/admin/categories/delete/${categoryToDelete.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Cerrar modal de carga
            loadingModal.hide();

            // Mostrar resultado
            showResultModal(data.success, data.message);

            // Si fue exitoso, recargar después de cerrar el modal
            if (data.success) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Cerrar modal de carga
            loadingModal.hide();

            // Mostrar error
            showResultModal(false, 'Error al eliminar la categoría. Por favor, inténtalo de nuevo.');
        })
        .finally(() => {
            categoryToDelete = null;
        });
    });

    function showResultModal(success, message) {
        const resultModal = document.getElementById('resultModal');
        const resultIcon = document.getElementById('resultIcon');
        const resultTitle = document.getElementById('resultTitle');
        const resultMainIcon = document.getElementById('resultMainIcon');
        const resultMessage = document.getElementById('resultMessage');
        const resultOkBtn = document.getElementById('resultOkBtn');

        if (success) {
            // Éxito
            resultIcon.className = 'fas fa-check-circle text-success me-2';
            resultTitle.textContent = 'Eliminación Exitosa';
            resultMainIcon.className = 'fas fa-check-circle text-success';
            resultMessage.textContent = message;
            resultOkBtn.className = 'btn btn-success';
            resultOkBtn.innerHTML = '<i class="fas fa-check me-2"></i>Perfecto';
        } else {
            // Error
            resultIcon.className = 'fas fa-exclamation-circle text-danger me-2';
            resultTitle.textContent = 'Error en la Eliminación';
            resultMainIcon.className = 'fas fa-exclamation-circle text-danger';
            resultMessage.textContent = message;
            resultOkBtn.className = 'btn btn-danger';
            resultOkBtn.innerHTML = '<i class="fas fa-times me-2"></i>Entendido';
        }

        const modal = new bootstrap.Modal(resultModal);
        modal.show();
    }
</script>
<?= $this->endSection() ?>
