<?php

namespace App\Controllers;

use App\Controllers\BaseController;

/**
 * Controlador Público de Cupones
 * Manejo de cupones para usuarios finales
 */
class CouponController extends BaseController
{
    protected $db;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Página principal de cupones
     */
    public function index()
    {
        try {
            log_message('info', 'Loading coupons page');

            // Obtener cupones disponibles (simplificado para depuración)
            $availableCoupons = $this->db->table('coupons')
                                        ->get()
                                        ->getResultArray();

            log_message('info', 'Found ' . count($availableCoupons) . ' coupons');

            $data = [
                'title' => 'Cupones Disponibles',
                'available_coupons' => $availableCoupons,
                'user_coupons' => []
            ];

            log_message('info', 'Rendering coupons view');
            return view('coupons/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error loading coupons page: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('/')->with('error', 'Error al cargar los cupones');
        }
    }

    /**
     * Obtener cupones del usuario
     */
    private function getUserCoupons(int $userId): array
    {
        return $this->db->table('coupon_usage cu')
                       ->select('cu.*, c.code, c.name, c.type, c.value')
                       ->join('coupons c', 'c.id = cu.coupon_id')
                       ->where('cu.user_id', $userId)
                       ->orderBy('cu.used_at', 'DESC')
                       ->limit(10)
                       ->get()
                       ->getResultArray();
    }
    
    /**
     * Aplicar cupón al carrito
     */
    public function apply()
    {
        try {
            $couponCode = $this->request->getPost('coupon_code');
            $cartTotal = (float)$this->request->getPost('cart_total');
            $cartItems = $this->request->getPost('cart_items') ?? [];
            $userId = session('user_id');
            
            if (empty($couponCode)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Código de cupón requerido'
                ]);
            }
            
            if ($cartTotal <= 0) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Carrito vacío'
                ]);
            }
            
            $orderData = [
                'total' => $cartTotal,
                'items' => $cartItems,
                'shipping_cost' => (float)($this->request->getPost('shipping_cost') ?? 0)
            ];
            
            $result = $this->couponManager->applyCoupon($couponCode, $orderData, $userId);
            
            if ($result['success']) {
                // Guardar cupón aplicado en sesión
                session()->set('applied_coupon', [
                    'code' => $couponCode,
                    'discount_amount' => $result['discount']['amount'],
                    'coupon_id' => $result['coupon']['id']
                ]);
                
                $this->logger->info("Coupon applied by user", [
                    'user_id' => $userId,
                    'coupon_code' => $couponCode,
                    'discount_amount' => $result['discount']['amount']
                ]);
            }
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            $this->logger->error("Coupon application error: " . $e->getMessage(), [
                'user_id' => session('user_id'),
                'post_data' => $this->request->getPost()
            ]);
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al aplicar cupón'
            ]);
        }
    }
    
    /**
     * Remover cupón del carrito
     */
    public function remove()
    {
        try {
            session()->remove('applied_coupon');
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Cupón removido exitosamente'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al remover cupón'
            ]);
        }
    }
    
    /**
     * Validar cupón sin aplicar
     */
    public function validateCoupon()
    {
        try {
            $couponCode = $this->request->getPost('coupon_code');
            $cartTotal = (float)$this->request->getPost('cart_total');
            $userId = session('user_id');
            
            if (empty($couponCode)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Código de cupón requerido'
                ]);
            }
            
            $orderData = [
                'total' => $cartTotal,
                'items' => $this->request->getPost('cart_items') ?? []
            ];
            
            $result = $this->couponManager->applyCoupon($couponCode, $orderData, $userId);
            
            // No guardar en sesión, solo validar
            return $this->response->setJSON([
                'success' => $result['success'],
                'valid' => $result['success'],
                'error' => $result['error'] ?? null,
                'discount_preview' => $result['success'] ? $result['discount'] : null
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'valid' => false,
                'error' => 'Error al validar cupón'
            ]);
        }
    }
    
    /**
     * Obtener cupones automáticos aplicables
     */
    public function getAutomatic()
    {
        try {
            $cartTotal = (float)$this->request->getPost('cart_total');
            $cartItems = $this->request->getPost('cart_items') ?? [];
            $userId = session('user_id');
            
            $orderData = [
                'total' => $cartTotal,
                'items' => $cartItems
            ];
            
            $automaticCoupons = $this->couponManager->getAutomaticCoupons($orderData, $userId);
            
            return $this->response->setJSON([
                'success' => true,
                'coupons' => $automaticCoupons
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener cupones automáticos'
            ]);
        }
    }
    
    /**
     * Aplicar descuentos por volumen
     */
    public function applyBulkDiscounts()
    {
        try {
            $cartItems = $this->request->getPost('cart_items') ?? [];
            
            if (empty($cartItems)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Carrito vacío'
                ]);
            }
            
            $bulkDiscounts = $this->couponManager->applyBulkDiscounts($cartItems);
            
            return $this->response->setJSON([
                'success' => true,
                'bulk_discounts' => $bulkDiscounts
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al aplicar descuentos por volumen'
            ]);
        }
    }
    
    /**
     * Obtener cupón aplicado actual
     */
    public function getCurrent()
    {
        try {
            $appliedCoupon = session('applied_coupon');
            
            return $this->response->setJSON([
                'success' => true,
                'has_coupon' => !empty($appliedCoupon),
                'coupon' => $appliedCoupon
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener cupón actual'
            ]);
        }
    }
    
    /**
     * Página de cupones disponibles
     */
    public function available()
    {
        $data = [
            'title' => 'Cupones Disponibles - MrCell Guatemala',
            'public_coupons' => $this->getPublicCoupons(),
            'user_coupons' => $this->getUserSpecificCoupons(),
            'seasonal_promotions' => $this->getSeasonalPromotions()
        ];
        
        return view('coupons/available', $data);
    }
    
    /**
     * Historial de cupones del usuario
     */
    public function history()
    {
        $userId = session('user_id');
        
        if (!$userId) {
            return redirect()->to('/login');
        }
        
        $couponHistory = $this->db->table('coupon_usage cu')
                                 ->select('cu.*, c.code, c.name, c.type')
                                 ->join('coupons c', 'c.id = cu.coupon_id')
                                 ->where('cu.user_id', $userId)
                                 ->orderBy('cu.created_at', 'DESC')
                                 ->get()
                                 ->getResultArray();
        
        $data = [
            'title' => 'Historial de Cupones - MrCell Guatemala',
            'coupon_history' => $couponHistory,
            'total_savings' => array_sum(array_column($couponHistory, 'discount_amount'))
        ];
        
        return view('coupons/history', $data);
    }
    
    /**
     * Suscribirse a newsletter para cupones
     */
    public function subscribe()
    {
        try {
            $email = $this->request->getPost('email');
            $name = $this->request->getPost('name') ?? '';
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Email válido requerido'
                ]);
            }
            
            // Verificar si ya está suscrito
            $existing = $this->db->table('newsletter_subscribers')
                                ->where('email', $email)
                                ->countAllResults();
            
            if ($existing > 0) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Este email ya está suscrito'
                ]);
            }
            
            // Suscribir
            $this->db->table('newsletter_subscribers')->insert([
                'email' => $email,
                'name' => $name,
                'source' => 'coupon_page',
                'subscribed_at' => date('Y-m-d H:i:s')
            ]);
            
            // Crear cupón de bienvenida
            $welcomeCoupon = $this->createWelcomeCoupon($email);
            
            $this->logger->info("Newsletter subscription with welcome coupon", [
                'email' => $email,
                'coupon_code' => $welcomeCoupon['code'] ?? null
            ]);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Suscripción exitosa',
                'welcome_coupon' => $welcomeCoupon
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error en la suscripción'
            ]);
        }
    }

    /**
     * Suscribirse al newsletter y recibir cupón de bienvenida
     */
    public function subscribeNewsletter()
    {
        try {
            $email = $this->request->getPost('email');

            if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Email válido requerido'
                ]);
            }

            // Verificar si ya está suscrito
            $existingSubscription = $this->db->table('newsletter_subscriptions')
                                            ->where('email', $email)
                                            ->get()
                                            ->getRowArray();

            if ($existingSubscription) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Este email ya está suscrito al newsletter'
                ]);
            }

            // Suscribir al newsletter
            $subscriptionData = [
                'email' => $email,
                'status' => 'active',
                'subscribed_at' => date('Y-m-d H:i:s'),
                'source' => 'coupon_page'
            ];

            $this->db->table('newsletter_subscriptions')->insert($subscriptionData);

            // Crear cupón de bienvenida
            $welcomeCoupon = $this->createWelcomeCoupon($email);

            if (!empty($welcomeCoupon)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => '¡Suscripción exitosa! Revisa tu email para el cupón de bienvenida.',
                    'coupon' => $welcomeCoupon
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => '¡Suscripción exitosa! Te enviaremos ofertas especiales.'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al procesar la suscripción'
            ]);
        }
    }

    /**
     * Métodos privados auxiliares
     */
    private function getPublicCoupons(): array
    {
        return $this->db->table('coupons')
                       ->where('is_active', 1)
                       ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                       ->where('usage_limit IS NULL OR usage_limit > (SELECT COUNT(*) FROM coupon_usage WHERE coupon_id = coupons.id)', null, false)
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getUserSpecificCoupons(): array
    {
        $userId = session('user_id');

        if (!$userId) {
            return [];
        }

        // Obtener información del usuario
        $user = $this->db->table('users')->where('id', $userId)->get()->getRowArray();
        if (!$user) {
            return [];
        }

        $userCoupons = [];

        // Cupones de cumpleaños (si es el mes del cumpleaños)
        if (!empty($user['birth_date'])) {
            $birthMonth = date('m', strtotime($user['birth_date']));
            $currentMonth = date('m');

            if ($birthMonth === $currentMonth) {
                $birthdayCoupon = $this->db->table('coupons')
                    ->where('code', 'BIRTHDAY_' . $userId)
                    ->where('is_active', 1)
                    ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                    ->get()
                    ->getRowArray();

                if ($birthdayCoupon) {
                    $userCoupons[] = $birthdayCoupon;
                }
            }
        }

        // Cupones de fidelidad (basados en número de compras)
        $orderCount = $this->db->table('orders')
            ->where('user_id', $userId)
            ->where('status', 'completed')
            ->countAllResults();

        if ($orderCount >= 5 && $orderCount < 10) {
            // Cupón para clientes con 5+ compras
            $loyaltyCoupon = $this->db->table('coupons')
                ->where('code', 'LOYALTY5')
                ->where('is_active', 1)
                ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                ->get()
                ->getRowArray();

            if ($loyaltyCoupon) {
                $userCoupons[] = $loyaltyCoupon;
            }
        } elseif ($orderCount >= 10) {
            // Cupón VIP para clientes con 10+ compras
            $vipCoupon = $this->db->table('coupons')
                ->where('code', 'VIP10')
                ->where('is_active', 1)
                ->where('(valid_until IS NULL OR valid_until >', date('Y-m-d H:i:s'))
                ->get()
                ->getRowArray();

            if ($vipCoupon) {
                $userCoupons[] = $vipCoupon;
            }
        }

        // Cupones personalizados asignados específicamente al usuario
        $personalCoupons = $this->db->table('user_coupons uc')
            ->select('c.*')
            ->join('coupons c', 'c.id = uc.coupon_id')
            ->where('uc.user_id', $userId)
            ->where('c.is_active', 1)
            ->where('(c.valid_until IS NULL OR c.valid_until >', date('Y-m-d H:i:s'))
            ->get()
            ->getResultArray();

        $userCoupons = array_merge($userCoupons, $personalCoupons);

        // Agregar información de uso para cada cupón
        foreach ($userCoupons as &$coupon) {
            $coupon['user_uses'] = $this->db->table('coupon_usage')
                ->where('coupon_id', $coupon['id'])
                ->where('user_id', $userId)
                ->countAllResults();
        }

        return $userCoupons;
    }
    
    private function getSeasonalPromotions(): array
    {
        return $this->db->table('promotional_campaigns')
                       ->where('is_active', 1)
                       ->where('start_date <=', date('Y-m-d H:i:s'))
                       ->where('end_date >=', date('Y-m-d H:i:s'))
                       ->orderBy('start_date', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function createWelcomeCoupon(string $email): array
    {
        try {
            $couponData = [
                'name' => 'Cupón de Bienvenida - ' . $email,
                'type' => 'percentage',
                'value' => 10, // 10% de descuento
                'min_order_amount' => 50,
                'usage_limit_per_user' => 1,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days')),
                'first_order_only' => 1,
                'is_active' => 1
            ];
            
            $result = $this->couponManager->createCoupon($couponData);
            
            if ($result['success']) {
                return [
                    'code' => $result['code'],
                    'discount' => '10%',
                    'expires' => date('d/m/Y', strtotime('+30 days'))
                ];
            }
            
            return [];
            
        } catch (\Exception $e) {
            return [];
        }
    }
}
