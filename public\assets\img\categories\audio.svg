<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#4CAF50"/>

    <!-- Gradiente sutil -->
    <defs>
        <linearGradient id="gradaudio" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#4CAF50;stop-opacity:0.8" />
        </linearGradient>
    </defs>
    <rect width="100%" height="100%" fill="url(#gradaudio)"/>

    <!-- Icono -->
    <g transform="translate(176,110)">
        <svg width="48" height="48" viewBox="0 0 24 24">
            <path d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12Z" fill="#FFFFFF" fill-opacity="0.9"/>
        </svg>
    </g>

    <!-- Texto -->
    <text x="200" y="180"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#FFFFFF"
          fill-opacity="0.95">
        Audio
    </text>

    <!-- Sombra del texto -->
    <text x="201" y="181"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#4CAF50"
          fill-opacity="0.3">
        Audio
    </text>
</svg>