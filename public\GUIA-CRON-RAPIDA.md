# 🚀 GUÍA RÁPIDA: CONFIGURAR CRON EN CPANEL

## 📁 ESTRUCTURA DETECTADA: /public

Los archivos están en la carpeta `/public` porque el proyecto tiene esta estructura.

## ⚡ CONFIGURACIÓN RÁPIDA

### **PASO 1: Ejecutar configuración automática**
Visita en tu navegador:
```
https://mrcell.com.gt/setup-cron-cpanel.php
```

### **PASO 2: Configurar en cPanel**

1. **Accede a cPanel**
2. **Ve a "Cron Jobs"**
3. **Agrega nuevo cron:**

**Comando:**
```bash
/opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-alerts.php
```

**Programación (cada 12 horas):**
- Minuto: `0`
- Hora: `8,20`
- <PERSON>ía: `*`
- Mes: `*`
- <PERSON><PERSON> de la semana: `*`

### **PASO 3: Probar**

**Probar entorno:**
```
https://mrcell.com.gt/test-cron-environment.php
```

**Ejecutar cron manualmente desde SSH (si tienes acceso):**
```bash
/opt/cpanel/ea-php82/root/usr/bin/php /home/<USER>/public_html/accounts/mrcell.com.gt/public/cron-alerts.php
```

## 📱 CONFIGURACIÓN DE WHATSAPP

- **Número actual:** `120363416393766854`
- **Editable desde:** `/admin/settings?tab=notifications`

## 📝 LOGS

Los logs se guardan en:
```
/writable/logs/cron-alerts.log
```

## 🔍 VERIFICAR QUE FUNCIONA

1. **Revisar logs** en File Manager de cPanel
2. **Verificar tabla** `cron_executions` en la base de datos
3. **Comprobar mensajes** en el grupo de WhatsApp

## ⚠️ SOLUCIÓN DE PROBLEMAS

### Error "Command not found"
- Verifica la ruta de PHP: `/opt/cpanel/ea-php82/root/usr/bin/php`

### Error "Permission denied"
- Asegúrate de que `/writable/logs/` tenga permisos de escritura

### No se ejecuta
- Verifica que el cron esté activo en cPanel
- Revisa los logs de cron del hosting

## 🎯 RESULTADO ESPERADO

El sistema enviará alertas cada 12 horas sobre:
- 🔴 Productos con bajo stock
- ⏰ Productos próximos a caducar
- 📦 Pedidos pendientes
- 🚚 Pedidos enviados sin entregar

---

**¡Configuración lista para producción!** 🎉
