<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateInstallmentPaymentTables extends Migration
{
    public function up()
    {
        // Tabla para planes de pago a plazos
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'Nombre del plan (ej: 3 cuotas, 6 cuotas)'
            ],
            'installments' => [
                'type' => 'TINYINT',
                'constraint' => 2,
                'unsigned' => true,
                'comment' => 'Número de cuotas (máximo 12)'
            ],
            'interest_rate' => [
                'type' => 'DECIMAL',
                'constraint' => '5,4',
                'default' => 0.0000,
                'comment' => 'Tasa de interés mensual (ej: 0.0250 = 2.5%)'
            ],
            'processing_fee' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'default' => 0.00,
                'comment' => 'Comisión por procesamiento'
            ],
            'min_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'default' => 0.00,
                'comment' => 'Monto mínimo para aplicar'
            ],
            'max_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Monto máximo para aplicar'
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => '1 = Activo, 0 = Inactivo'
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP'
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP'
            ]
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('installments');
        $this->forge->addKey('is_active');
        $this->forge->createTable('installment_plans');

        // Tabla para pagos a plazos de órdenes
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'order_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID de la orden'
            ],
            'installment_plan_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID del plan de cuotas'
            ],
            'total_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'comment' => 'Monto total de la orden'
            ],
            'installment_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'comment' => 'Monto de cada cuota'
            ],
            'total_installments' => [
                'type' => 'TINYINT',
                'constraint' => 2,
                'unsigned' => true,
                'comment' => 'Número total de cuotas'
            ],
            'paid_installments' => [
                'type' => 'TINYINT',
                'constraint' => 2,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Número de cuotas pagadas'
            ],
            'next_payment_date' => [
                'type' => 'DATE',
                'comment' => 'Fecha del próximo pago'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'completed', 'cancelled', 'defaulted'],
                'default' => 'active',
                'comment' => 'Estado del plan de pagos'
            ],
            'payment_method_token' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Token de la tarjeta para pagos recurrentes'
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP'
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP'
            ]
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('order_id');
        $this->forge->addKey('installment_plan_id');
        $this->forge->addKey('status');
        $this->forge->addKey('next_payment_date');
        $this->forge->createTable('order_installments');

        // Tabla para el historial de pagos de cuotas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'order_installment_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID del plan de cuotas'
            ],
            'installment_number' => [
                'type' => 'TINYINT',
                'constraint' => 2,
                'unsigned' => true,
                'comment' => 'Número de la cuota (1, 2, 3, etc.)'
            ],
            'amount' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'comment' => 'Monto de la cuota'
            ],
            'due_date' => [
                'type' => 'DATE',
                'comment' => 'Fecha de vencimiento'
            ],
            'paid_date' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'comment' => 'Fecha de pago'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'paid', 'overdue', 'failed'],
                'default' => 'pending',
                'comment' => 'Estado de la cuota'
            ],
            'payment_transaction_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'ID de la transacción de pago'
            ],
            'payment_method' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'Método de pago utilizado'
            ],
            'failure_reason' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Razón del fallo en caso de error'
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP'
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP'
            ]
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('order_installment_id');
        $this->forge->addKey('installment_number');
        $this->forge->addKey('status');
        $this->forge->addKey('due_date');
        $this->forge->createTable('installment_payments');

        // Agregar foreign keys
        $this->forge->addForeignKey('order_id', 'orders', 'id', 'CASCADE', 'CASCADE', 'fk_order_installments_order');
        $this->forge->addForeignKey('installment_plan_id', 'installment_plans', 'id', 'CASCADE', 'CASCADE', 'fk_order_installments_plan');
        $this->db->query('ALTER TABLE order_installments ADD CONSTRAINT fk_order_installments_order FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE ON UPDATE CASCADE');
        $this->db->query('ALTER TABLE order_installments ADD CONSTRAINT fk_order_installments_plan FOREIGN KEY (installment_plan_id) REFERENCES installment_plans(id) ON DELETE CASCADE ON UPDATE CASCADE');
        
        $this->db->query('ALTER TABLE installment_payments ADD CONSTRAINT fk_installment_payments_order_installment FOREIGN KEY (order_installment_id) REFERENCES order_installments(id) ON DELETE CASCADE ON UPDATE CASCADE');
    }

    public function down()
    {
        $this->forge->dropTable('installment_payments');
        $this->forge->dropTable('order_installments');
        $this->forge->dropTable('installment_plans');
    }
}
