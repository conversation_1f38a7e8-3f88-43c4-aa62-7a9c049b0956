<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CheckPaymentMethods extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:check-payment-methods';
    protected $description = 'Verificar métodos de pago en la base de datos';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== MÉTODOS DE PAGO ACTUALES ===', 'yellow');
            CLI::newLine();
            
            $paymentMethods = $db->query("SELECT * FROM payment_methods ORDER BY sort_order, name")->getResultArray();
            
            if (empty($paymentMethods)) {
                CLI::error('❌ No hay métodos de pago en la base de datos');
            } else {
                CLI::write('✅ Métodos de pago encontrados:', 'green');
                CLI::newLine();
                
                foreach ($paymentMethods as $method) {
                    CLI::write("ID: {$method['id']}", 'white');
                    CLI::write("Nombre: {$method['name']}", 'white');
                    CLI::write("Tipo: {$method['type']}", 'white');
                    CLI::write("Slug: {$method['slug']}", 'white');
                    CLI::write("Activo: " . ($method['is_active'] ? 'Sí' : 'No'), 'white');
                    CLI::write("Descripción: " . ($method['description'] ?: 'N/A'), 'white');
                    CLI::write('---', 'dark_gray');
                }
            }
            
            CLI::newLine();
            CLI::write('=== CONFIGURACIONES DEL SISTEMA ===', 'yellow');
            CLI::newLine();
            
            $settings = $db->query("SELECT * FROM system_settings WHERE setting_group IN ('payment', 'integrations', 'recurrente') ORDER BY setting_group, setting_key")->getResultArray();
            
            if (empty($settings)) {
                CLI::error('❌ No hay configuraciones de pago/integraciones');
            } else {
                CLI::write('✅ Configuraciones encontradas:', 'green');
                CLI::newLine();
                
                $currentGroup = '';
                foreach ($settings as $setting) {
                    if ($setting['setting_group'] !== $currentGroup) {
                        $currentGroup = $setting['setting_group'];
                        CLI::write("--- GRUPO: {$currentGroup} ---", 'cyan');
                    }
                    CLI::write("{$setting['setting_key']}: {$setting['setting_value']}", 'white');
                }
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
