<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\MobileOptimizer;
use App\Libraries\PushNotificationManager;

/**
 * Controlador API para funcionalidades móviles
 * Gestiona PWA, notificaciones push y optimizaciones móviles
 */
class MobileController extends ResourceController
{
    protected $format = 'json';
    protected $mobileOptimizer;
    protected $pushManager;
    
    public function __construct()
    {
        $this->mobileOptimizer = new MobileOptimizer();
        $this->pushManager = new PushNotificationManager();
    }
    
    /**
     * Obtener configuración móvil
     */
    public function config()
    {
        try {
            $deviceConfig = $this->mobileOptimizer->getDeviceConfig();
            $pushConfig = $this->pushManager->getPublicConfig();
            
            $config = [
                'device' => $deviceConfig,
                'push_notifications' => $pushConfig,
                'pwa' => $this->mobileOptimizer->generatePWAConfig(),
                'features' => [
                    'supports_webp' => $deviceConfig['supports_webp'],
                    'supports_service_worker' => $deviceConfig['supports_service_worker'],
                    'supports_push_notifications' => $deviceConfig['supports_push_notifications'],
                    'is_mobile' => $deviceConfig['is_mobile'],
                    'is_tablet' => $deviceConfig['is_tablet']
                ]
            ];
            
            return $this->respond($config);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting mobile config: ' . $e->getMessage());
        }
    }
    
    /**
     * Suscribir a notificaciones push
     */
    public function subscribePush()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('User not authenticated');
            }
            
            $subscription = $this->request->getJSON(true);
            
            if (empty($subscription)) {
                return $this->failValidationError('Subscription data required');
            }
            
            $result = $this->pushManager->subscribe($userId, $subscription);
            
            if ($result['success']) {
                return $this->respondCreated($result);
            } else {
                return $this->failValidationError($result['error']);
            }
            
        } catch (\Exception $e) {
            return $this->failServerError('Error subscribing to push notifications: ' . $e->getMessage());
        }
    }
    
    /**
     * Cancelar suscripción push
     */
    public function unsubscribePush()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('User not authenticated');
            }
            
            $data = $this->request->getJSON(true);
            $endpoint = $data['endpoint'] ?? null;
            
            $result = $this->pushManager->unsubscribe($userId, $endpoint);
            
            if ($result['success']) {
                return $this->respondDeleted(['message' => 'Unsubscribed successfully']);
            } else {
                return $this->failServerError('Error unsubscribing');
            }
            
        } catch (\Exception $e) {
            return $this->failServerError('Error unsubscribing: ' . $e->getMessage());
        }
    }
    
    /**
     * Probar notificación push
     */
    public function testPush()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->failUnauthorized('User not authenticated');
            }
            
            $notification = [
                'title' => '🧪 Notificación de Prueba',
                'body' => 'Esta es una notificación de prueba de MrCell Guatemala',
                'icon' => base_url('icon-192x192.png'),
                'data' => [
                    'type' => 'test',
                    'url' => base_url()
                ],
                'actions' => [
                    [
                        'action' => 'view',
                        'title' => 'Ver Tienda',
                        'icon' => base_url('assets/images/icons/view.png')
                    ]
                ]
            ];
            
            $result = $this->pushManager->sendToUser($userId, $notification);
            
            return $this->respond($result);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error sending test notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Registrar sesión móvil
     */
    public function registerSession()
    {
        try {
            $data = $this->request->getJSON(true);
            $userId = $this->getUserId();
            
            $sessionData = [
                'user_id' => $userId,
                'session_id' => session_id(),
                'device_type' => $this->mobileOptimizer->getDeviceType(),
                'browser_name' => $this->mobileOptimizer->getBrowserInfo()['name'],
                'browser_version' => $this->mobileOptimizer->getBrowserInfo()['version'],
                'screen_width' => $data['screen_width'] ?? null,
                'screen_height' => $data['screen_height'] ?? null,
                'is_pwa' => $data['is_pwa'] ?? false,
                'supports_webp' => $this->mobileOptimizer->getBrowserInfo()['supports_webp'],
                'supports_service_worker' => $this->mobileOptimizer->getBrowserInfo()['supports_service_worker'],
                'connection_type' => $data['connection_type'] ?? 'unknown',
                'last_activity' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $db = \Config\Database::connect();
            
            // Actualizar o insertar sesión
            $existing = $db->table('mobile_sessions')
                          ->where('session_id', session_id())
                          ->get()
                          ->getRowArray();
            
            if ($existing) {
                $db->table('mobile_sessions')
                  ->where('id', $existing['id'])
                  ->update([
                      'last_activity' => date('Y-m-d H:i:s'),
                      'is_pwa' => $data['is_pwa'] ?? false
                  ]);
            } else {
                $db->table('mobile_sessions')->insert($sessionData);
            }
            
            return $this->respondCreated(['message' => 'Session registered successfully']);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error registering session: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener imagen optimizada
     */
    public function optimizedImage()
    {
        try {
            $imagePath = $this->request->getGet('path');
            $width = (int) ($this->request->getGet('width') ?? 400);
            $quality = (int) ($this->request->getGet('quality') ?? 80);
            $format = $this->request->getGet('format') ?? 'auto';
            
            if (empty($imagePath)) {
                return $this->failValidationError('Image path required');
            }
            
            $optimizedPath = $this->mobileOptimizer->optimizeImageForMobile($imagePath, [
                'width' => $width,
                'quality' => $quality,
                'format' => $format
            ]);
            
            return $this->respond([
                'original_path' => $imagePath,
                'optimized_path' => $optimizedPath,
                'width' => $width,
                'quality' => $quality,
                'format' => $format
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error optimizing image: ' . $e->getMessage());
        }
    }
    
    /**
     * Generar imagen responsiva HTML
     */
    public function responsiveImage()
    {
        try {
            $imagePath = $this->request->getGet('path');
            $alt = $this->request->getGet('alt') ?? '';
            $lazy = $this->request->getGet('lazy') === 'true';
            
            if (empty($imagePath)) {
                return $this->failValidationError('Image path required');
            }
            
            $html = $this->mobileOptimizer->generateResponsiveImage($imagePath, $alt, [
                'lazy' => $lazy
            ]);
            
            return $this->respond([
                'html' => $html,
                'lazy_loading' => $lazy
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error generating responsive image: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener CSS crítico para móvil
     */
    public function criticalCSS()
    {
        try {
            $css = $this->mobileOptimizer->generateCriticalCSS();
            
            return $this->respond([
                'css' => $css,
                'device_type' => $this->mobileOptimizer->getDeviceType(),
                'is_mobile' => $this->mobileOptimizer->isMobile()
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error generating critical CSS: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener JavaScript para gestos táctiles
     */
    public function touchGesturesJS()
    {
        try {
            $js = $this->mobileOptimizer->generateTouchGesturesJS();
            
            return $this->respond([
                'javascript' => $js,
                'enabled' => $this->mobileOptimizer->isMobile()
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error generating touch gestures JS: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas móviles
     */
    public function stats()
    {
        try {
            $days = (int) ($this->request->getGet('days') ?? 7);
            
            $pushStats = $this->pushManager->getStats($days);
            
            // Obtener estadísticas de sesiones móviles
            $db = \Config\Database::connect();
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $sessionStats = $db->table('mobile_sessions')
                              ->select('device_type, COUNT(*) as sessions, COUNT(DISTINCT user_id) as unique_users')
                              ->where('created_at >=', $startDate)
                              ->groupBy('device_type')
                              ->get()
                              ->getResultArray();
            
            $pwaStats = $db->table('mobile_sessions')
                          ->select('COUNT(*) as total_pwa_sessions')
                          ->where('is_pwa', 1)
                          ->where('created_at >=', $startDate)
                          ->get()
                          ->getRowArray();
            
            return $this->respond([
                'period_days' => $days,
                'push_notifications' => $pushStats,
                'sessions' => $sessionStats,
                'pwa_sessions' => (int) ($pwaStats['total_pwa_sessions'] ?? 0)
            ]);
            
        } catch (\Exception $e) {
            return $this->failServerError('Error getting mobile stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener ID del usuario actual
     */
    private function getUserId(): ?int
    {
        $session = session();
        return $session->get('user_id');
    }
}
