<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class RateLimitFilter implements FilterInterface
{
    protected $db;
    protected $cache;

    public function __construct()
    {
        try {
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - RateLimit constructor iniciado\n", FILE_APPEND);
            $this->db = \Config\Database::connect();
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - DB conectada\n", FILE_APPEND);
            $this->cache = \Config\Services::cache();
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - <PERSON><PERSON> inicializado\n", FILE_APPEND);
        } catch (\Exception $e) {
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - ERROR en constructor: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * Verificar rate limit antes de ejecutar el controlador
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        try {
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - RateLimit before iniciado\n", FILE_APPEND);

            // Configuraciones por defecto
        $defaultConfig = [
            'max_attempts' => 5,
            'window_minutes' => 15,
            'block_minutes' => 60
        ];

        // Configuraciones específicas por tipo
        $configs = [
            'register' => [
                'max_attempts' => 3,
                'window_minutes' => 60,
                'block_minutes' => 120
            ],
            'review' => [
                'max_attempts' => 5,
                'window_minutes' => 10,
                'block_minutes' => 30
            ],
            'login' => [
                'max_attempts' => 5,
                'window_minutes' => 15,
                'block_minutes' => 60
            ],
            'contact' => [
                'max_attempts' => 3,
                'window_minutes' => 30,
                'block_minutes' => 60
            ]
        ];

        // Obtener configuración según el argumento
        $type = $arguments[0] ?? 'default';
        $config = $configs[$type] ?? $defaultConfig;

        // Identificar cliente
        $clientId = $this->getClientIdentifier($request);
        $action = $this->getActionFromRequest($request, $type);

        // Verificar rate limit
        if ($this->isRateLimited($clientId, $action, $config)) {
            return $this->handleRateLimit($request, $config);
        }

        // Registrar intento
        $this->recordAttempt($clientId, $action);

        file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - RateLimit before completado exitosamente\n", FILE_APPEND);
        return null;

        } catch (\Exception $e) {
            file_put_contents(WRITEPATH . 'logs/ratelimit_debug.log', date('Y-m-d H:i:s') . " - ERROR en before: " . $e->getMessage() . "\n", FILE_APPEND);
            // En caso de error, permitir el acceso para no bloquear la aplicación
            return null;
        }
    }

    /**
     * Después de la ejecución del controlador
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        return $response;
    }

    /**
     * Obtener identificador único del cliente
     */
    private function getClientIdentifier(RequestInterface $request): string
    {
        $ip = $request->getIPAddress();
        $userAgent = $request->getUserAgent()->getAgentString();
        
        // Si hay usuario logueado, incluir su ID
        $userId = session()->get('user_id');
        if ($userId) {
            return "user_{$userId}_{$ip}";
        }

        // Para usuarios anónimos, usar IP + hash del user agent
        $agentHash = substr(md5($userAgent), 0, 8);
        return "anon_{$ip}_{$agentHash}";
    }

    /**
     * Obtener acción desde la request
     */
    private function getActionFromRequest(RequestInterface $request, string $type): string
    {
        $uri = $request->getUri()->getPath();
        return "{$type}_{$uri}";
    }

    /**
     * Verificar si el cliente está limitado por rate limit
     */
    private function isRateLimited(string $clientId, string $action, array $config): bool
    {
        $cacheKey = "rate_limit_" . md5($clientId . '_' . $action);
        $blockKey = "rate_limit_block_" . md5($clientId . '_' . $action);

        // Verificar si está bloqueado
        if ($this->cache->get($blockKey)) {
            return true;
        }

        // Obtener intentos actuales
        $attempts = $this->cache->get($cacheKey) ?? [];
        $now = time();
        $windowStart = $now - ($config['window_minutes'] * 60);

        // Filtrar intentos dentro de la ventana de tiempo
        $recentAttempts = array_filter($attempts, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });

        return count($recentAttempts) >= $config['max_attempts'];
    }

    /**
     * Registrar un intento
     */
    private function recordAttempt(string $clientId, string $action): void
    {
        $cacheKey = "rate_limit_" . md5($clientId . '_' . $action);
        $attempts = $this->cache->get($cacheKey) ?? [];
        
        // Agregar timestamp actual
        $attempts[] = time();
        
        // Mantener solo los últimos 100 intentos para evitar que crezca indefinidamente
        if (count($attempts) > 100) {
            $attempts = array_slice($attempts, -100);
        }

        // Guardar en cache por 24 horas
        $this->cache->save($cacheKey, $attempts, 86400);
    }

    /**
     * Manejar cuando se alcanza el rate limit
     */
    private function handleRateLimit(RequestInterface $request, array $config)
    {
        // Registrar intento de rate limit
        $this->logRateLimitViolation($request, $config);

        // Bloquear temporalmente
        $clientId = $this->getClientIdentifier($request);
        $action = $this->getActionFromRequest($request, 'blocked');
        $blockKey = "rate_limit_block_{$clientId}_{$action}";
        $this->cache->save($blockKey, true, $config['block_minutes'] * 60);

        // Responder según el tipo de request
        if ($request->isAJAX()) {
            return service('response')->setJSON([
                'status' => 'error',
                'message' => 'Demasiados intentos. Intenta de nuevo en ' . $config['block_minutes'] . ' minutos.',
                'retry_after' => $config['block_minutes'] * 60
            ])->setStatusCode(429);
        }

        // Para requests normales, redirigir con mensaje
        session()->setFlashdata('error', 'Demasiados intentos. Intenta de nuevo en ' . $config['block_minutes'] . ' minutos.');
        return redirect()->back();
    }

    /**
     * Registrar violación de rate limit
     */
    private function logRateLimitViolation(RequestInterface $request, array $config): void
    {
        $data = [
            'ip_address' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent()->getAgentString(),
            'uri' => $request->getUri()->getPath(),
            'method' => $request->getMethod(),
            'user_id' => session()->get('user_id'),
            'config' => json_encode($config),
            'created_at' => date('Y-m-d H:i:s')
        ];

        try {
            // Crear tabla si no existe
            $this->createRateLimitLogTable();
            
            // Insertar log
            $this->db->table('rate_limit_logs')->insert($data);
        } catch (\Exception $e) {
            log_message('error', 'Error logging rate limit violation: ' . $e->getMessage());
        }

        // También log en archivo
        log_message('warning', "Rate limit exceeded: IP {$data['ip_address']}, URI {$data['uri']}, User ID: " . ($data['user_id'] ?? 'anonymous'));
    }

    /**
     * Crear tabla de logs si no existe
     */
    private function createRateLimitLogTable(): void
    {
        if (!$this->db->tableExists('rate_limit_logs')) {
            $forge = \Config\Database::forge();
            
            $forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true
                ],
                'ip_address' => [
                    'type' => 'VARCHAR',
                    'constraint' => 45
                ],
                'user_agent' => [
                    'type' => 'TEXT'
                ],
                'uri' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255
                ],
                'method' => [
                    'type' => 'VARCHAR',
                    'constraint' => 10
                ],
                'user_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true
                ],
                'config' => [
                    'type' => 'JSON',
                    'null' => true
                ],
                'created_at' => [
                    'type' => 'TIMESTAMP',
                    'default' => 'CURRENT_TIMESTAMP'
                ]
            ]);
            
            $forge->addKey('id', true);
            $forge->addKey(['ip_address', 'created_at']);
            $forge->addKey('user_id');
            
            $forge->createTable('rate_limit_logs');
        }
    }
}
