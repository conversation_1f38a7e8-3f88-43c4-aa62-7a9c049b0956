<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-list me-2"></i>Lista de Cupones
    </h1>
    <div>
        <a href="<?= base_url('admin/coupons/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Crear <PERSON>
        </a>
        <a href="<?= base_url('admin/coupons') ?>" class="btn btn-secondary">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </a>
    </div>
</div>

<!-- Filtros -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= base_url('admin/coupons/coupons') ?>">
            <div class="row">
                <div class="col-md-3">
                    <label for="type" class="form-label">Tipo de Cupón</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">Todos los tipos</option>
                        <?php foreach ($coupon_types as $type): ?>
                            <option value="<?= $type ?>" <?= (request()->getGet('type') === $type) ? 'selected' : '' ?>>
                                <?= ucfirst(str_replace('_', ' ', $type)) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Estado</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Todos los estados</option>
                        <option value="active" <?= (request()->getGet('status') === 'active') ? 'selected' : '' ?>>Activo</option>
                        <option value="inactive" <?= (request()->getGet('status') === 'inactive') ? 'selected' : '' ?>>Inactivo</option>
                        <option value="expired" <?= (request()->getGet('status') === 'expired') ? 'selected' : '' ?>>Expirado</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Buscar</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Código o nombre del cupón" 
                           value="<?= esc(request()->getGet('search')) ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="<?= base_url('admin/coupons/coupons') ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Limpiar
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabla de Cupones -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Cupones 
            <?php if (!empty($coupons)): ?>
                <span class="badge badge-primary"><?= count($coupons) ?></span>
            <?php endif; ?>
        </h6>
    </div>
    <div class="card-body">
        <?php if (!empty($coupons)): ?>
            <div class="table-responsive">
                <table class="table table-bordered" id="couponsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nombre</th>
                            <th>Tipo</th>
                            <th>Valor</th>
                            <th>Usos</th>
                            <th>Estado</th>
                            <th>Vencimiento</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($coupons as $coupon): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($coupon['code']) ?></strong>
                                    <?php if ($coupon['first_order_only']): ?>
                                        <br><small class="text-info"><i class="fas fa-star"></i> Solo primera compra</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= esc($coupon['name']) ?>
                                    <?php if (!empty($coupon['description'])): ?>
                                        <br><small class="text-muted"><?= esc(substr($coupon['description'], 0, 50)) ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $typeClass = 'badge-primary';
                                    $typeIcon = 'fa-percentage';
                                    switch($coupon['type']) {
                                        case 'fixed': 
                                            $typeClass = 'badge-success'; 
                                            $typeIcon = 'fa-dollar-sign'; 
                                            break;
                                        case 'free_shipping': 
                                            $typeClass = 'badge-info'; 
                                            $typeIcon = 'fa-truck'; 
                                            break;
                                        case 'buy_x_get_y': 
                                            $typeClass = 'badge-warning'; 
                                            $typeIcon = 'fa-gift'; 
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $typeClass ?>">
                                        <i class="fas <?= $typeIcon ?> me-1"></i>
                                        <?= ucfirst(str_replace('_', ' ', $coupon['type'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($coupon['type'] === 'percentage'): ?>
                                        <strong><?= $coupon['value'] ?>%</strong>
                                    <?php elseif ($coupon['type'] === 'fixed'): ?>
                                        <strong>Q<?= number_format($coupon['value'], 2) ?></strong>
                                    <?php elseif ($coupon['type'] === 'free_shipping'): ?>
                                        <span class="text-info">Envío Gratis</span>
                                    <?php else: ?>
                                        <span class="text-warning">Especial</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($coupon['min_order_amount'] > 0): ?>
                                        <br><small class="text-muted">Min: Q<?= number_format($coupon['min_order_amount'], 2) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <strong><?= $coupon['current_uses'] ?? 0 ?></strong>
                                        <?php if ($coupon['usage_limit']): ?>
                                            / <?= $coupon['usage_limit'] ?>
                                        <?php else: ?>
                                            <br><small class="text-muted">Ilimitado</small>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($coupon['usage_limit'] && $coupon['current_uses'] >= $coupon['usage_limit']): ?>
                                        <small class="text-danger">Agotado</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $isActive = $coupon['is_active'];
                                    $isExpired = $coupon['valid_until'] && strtotime($coupon['valid_until']) < time();
                                    $isUsedUp = $coupon['usage_limit'] && $coupon['current_uses'] >= $coupon['usage_limit'];
                                    
                                    if (!$isActive) {
                                        $statusClass = 'badge-secondary';
                                        $statusText = 'Inactivo';
                                        $statusIcon = 'fa-pause';
                                    } elseif ($isExpired) {
                                        $statusClass = 'badge-danger';
                                        $statusText = 'Expirado';
                                        $statusIcon = 'fa-clock';
                                    } elseif ($isUsedUp) {
                                        $statusClass = 'badge-warning';
                                        $statusText = 'Agotado';
                                        $statusIcon = 'fa-ban';
                                    } else {
                                        $statusClass = 'badge-success';
                                        $statusText = 'Activo';
                                        $statusIcon = 'fa-check';
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>">
                                        <i class="fas <?= $statusIcon ?> me-1"></i>
                                        <?= $statusText ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($coupon['valid_until']): ?>
                                        <?= date('d/m/Y', strtotime($coupon['valid_until'])) ?>
                                        <br><small class="text-muted"><?= date('H:i', strtotime($coupon['valid_until'])) ?></small>
                                        
                                        <?php if (strtotime($coupon['valid_until']) < strtotime('+7 days')): ?>
                                            <br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Próximo a vencer</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-success">Sin vencimiento</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/coupons/view/' . $coupon['id']) ?>" 
                                           class="btn btn-sm btn-info" title="Ver detalles">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('admin/coupons/edit/' . $coupon['id']) ?>" 
                                           class="btn btn-sm btn-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <?php if ($coupon['is_active']): ?>
                                            <button type="button" class="btn btn-sm btn-warning" 
                                                    onclick="toggleCouponStatus(<?= $coupon['id'] ?>, 0)" 
                                                    title="Desactivar">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="toggleCouponStatus(<?= $coupon['id'] ?>, 1)" 
                                                    title="Activar">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="deleteCoupon(<?= $coupon['id'] ?>)" 
                                                title="Eliminar">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-ticket-alt fa-4x text-gray-300 mb-4"></i>
                <h4 class="text-gray-500">No se encontraron cupones</h4>
                <p class="text-gray-400 mb-4">
                    <?php if (request()->getGet('search') || request()->getGet('type') || request()->getGet('status')): ?>
                        No hay cupones que coincidan con los filtros aplicados.
                    <?php else: ?>
                        Aún no has creado ningún cupón. ¡Comienza creando tu primer cupón!
                    <?php endif; ?>
                </p>
                <a href="<?= base_url('admin/coupons/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Crear Primer Cupón
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Acciones en Lote -->
<?php if (!empty($coupons)): ?>
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Acciones en Lote</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <button type="button" class="btn btn-success me-2" onclick="bulkActivate()">
                    <i class="fas fa-play me-2"></i>Activar Seleccionados
                </button>
                <button type="button" class="btn btn-warning me-2" onclick="bulkDeactivate()">
                    <i class="fas fa-pause me-2"></i>Desactivar Seleccionados
                </button>
                <button type="button" class="btn btn-danger" onclick="bulkDelete()">
                    <i class="fas fa-trash me-2"></i>Eliminar Seleccionados
                </button>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-info" onclick="exportCoupons()">
                    <i class="fas fa-download me-2"></i>Exportar Lista
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Inicializar DataTable
    $('#couponsTable').DataTable({
        "pageLength": 25,
        "order": [[ 0, "asc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json"
        },
        "columnDefs": [
            { "orderable": false, "targets": 7 } // Columna de acciones no ordenable
        ]
    });
});

// Cambiar estado del cupón
function toggleCouponStatus(couponId, status) {
    const action = status ? 'activar' : 'desactivar';
    
    if (confirm(`¿Estás seguro de que deseas ${action} este cupón?`)) {
        $.ajax({
            url: '<?= base_url('admin/coupons/toggle-status') ?>',
            method: 'POST',
            data: {
                coupon_id: couponId,
                status: status,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function() {
                alert('Error al cambiar el estado del cupón');
            }
        });
    }
}

// Eliminar cupón
function deleteCoupon(couponId) {
    if (confirm('¿Estás seguro de que deseas eliminar este cupón? Esta acción no se puede deshacer.')) {
        $.ajax({
            url: '<?= base_url('admin/coupons/delete') ?>',
            method: 'POST',
            data: {
                coupon_id: couponId,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function() {
                alert('Error al eliminar el cupón');
            }
        });
    }
}

// Funciones de acciones en lote
function getSelectedCoupons() {
    const selected = [];
    $('input[name="selected_coupons[]"]:checked').each(function() {
        selected.push($(this).val());
    });
    return selected;
}

function bulkActivate() {
    const selected = getSelectedCoupons();
    if (selected.length === 0) {
        alert('Por favor selecciona al menos un cupón');
        return;
    }
    
    if (confirm(`¿Activar ${selected.length} cupón(es) seleccionado(s)?`)) {
        // Implementar acción en lote
        console.log('Activar cupones:', selected);
    }
}

function bulkDeactivate() {
    const selected = getSelectedCoupons();
    if (selected.length === 0) {
        alert('Por favor selecciona al menos un cupón');
        return;
    }
    
    if (confirm(`¿Desactivar ${selected.length} cupón(es) seleccionado(s)?`)) {
        // Implementar acción en lote
        console.log('Desactivar cupones:', selected);
    }
}

function bulkDelete() {
    const selected = getSelectedCoupons();
    if (selected.length === 0) {
        alert('Por favor selecciona al menos un cupón');
        return;
    }
    
    if (confirm(`¿Eliminar ${selected.length} cupón(es) seleccionado(s)? Esta acción no se puede deshacer.`)) {
        // Implementar acción en lote
        console.log('Eliminar cupones:', selected);
    }
}

function exportCoupons() {
    window.open('<?= base_url('admin/coupons/export') ?>', '_blank');
}
</script>
<?= $this->endSection() ?>
