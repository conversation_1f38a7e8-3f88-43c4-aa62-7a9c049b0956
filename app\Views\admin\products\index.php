<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-box me-2"></i>Gestión de Productos</h1>
        <a href="/admin/products/create" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nuevo Producto
        </a>
    </div>
</div>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="/admin/products" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Buscar Producto</label>
                    <input type="text" name="search" class="form-control" placeholder="Nombre, SKU, descripción..." value="<?= esc($search ?? '') ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Categoría</label>
                    <select name="category_id" class="form-select">
                        <option value="">Todas las categorías</option>
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= (isset($_GET['category_id']) && $_GET['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                    <?= esc($category['display_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Estado de Stock</label>
                    <select name="stock_status" class="form-select">
                        <option value="">Todos</option>
                        <option value="high" <?= (isset($_GET['stock_status']) && $_GET['stock_status'] == 'high') ? 'selected' : '' ?>>Stock Alto</option>
                        <option value="medium" <?= (isset($_GET['stock_status']) && $_GET['stock_status'] == 'medium') ? 'selected' : '' ?>>Stock Medio</option>
                        <option value="low" <?= (isset($_GET['stock_status']) && $_GET['stock_status'] == 'low') ? 'selected' : '' ?>>Stock Bajo</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Estado</label>
                    <select name="status" class="form-select">
                        <option value="">Todos</option>
                        <option value="active" <?= (isset($_GET['status']) && $_GET['status'] == 'active') ? 'selected' : '' ?>>Activo</option>
                        <option value="inactive" <?= (isset($_GET['status']) && $_GET['status'] == 'inactive') ? 'selected' : '' ?>>Inactivo</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-search"></i> Buscar
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Limpiar
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabla de Productos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Productos</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="productsTable">
                <thead>
                    <tr>
                        <th>Imagen</th>
                        <th>Producto</th>
                        <th>SKU</th>
                        <th>Precio</th>
                        <th>Stock</th>
                        <th>Estado</th>
                        <th>Categoría</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($products)): ?>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <?php if (!empty($product['featured_image'])): ?>
                                    <img src="<?= base_url($product['featured_image']) ?>"
                                         alt="<?= esc($product['name']) ?>"
                                         class="product-image"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="product-image-placeholder"
                                         style="width: 50px; height: 50px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: none; align-items: center; justify-content: center;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="product-image-placeholder"
                                         style="width: 50px; height: 50px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= esc($product['name']) ?></strong><br>
                                <small class="text-muted"><?= esc(substr($product['short_description'] ?? $product['description'] ?? '', 0, 50)) ?>...</small>
                            </td>
                            <td><?= esc($product['sku']) ?></td>
                            <td><?= format_currency($product['price_regular'], $product['currency'] ?? 'GTQ') ?></td>
                            <td>
                                <?php 
                                $stockClass = 'bg-success';
                                if ($product['stock_quantity'] < 5) $stockClass = 'bg-danger';
                                elseif ($product['stock_quantity'] < 20) $stockClass = 'bg-warning';
                                ?>
                                <span class="badge <?= $stockClass ?>"><?= $product['stock_quantity'] ?></span>
                            </td>
                            <td>
                                <?php if ($product['is_active']): ?>
                                    <span class="badge bg-success">Activo</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Inactivo</span>
                                <?php endif; ?>
                            </td>
                            <td><?= esc($product['category_name'] ?? 'Sin categoría') ?></td>
                            <td>
                                <a href="/admin/products/view/<?= $product['id'] ?>" class="btn btn-sm btn-outline-info" title="Ver">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/admin/products/edit/<?= $product['id'] ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-secondary" title="Duplicar" onclick="duplicateProduct(<?= $product['id'] ?>, '<?= esc($product['name']) ?>')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" title="Eliminar" onclick="deleteProduct(<?= $product['id'] ?>, '<?= esc($product['name']) ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center">No hay productos registrados</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Paginación -->
        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                Mostrando <?= (($pagination['current_page'] - 1) * $pagination['per_page']) + 1 ?> -
                <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_products']) ?>
                de <?= $pagination['total_products'] ?> productos
            </div>

            <nav aria-label="Paginación de productos">
                <ul class="pagination mb-0">
                    <!-- Botón Anterior -->
                    <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
                        <?php if ($pagination['has_previous']): ?>
                            <?php
                            $prevParams = $_GET;
                            $prevParams['page'] = $pagination['current_page'] - 1;
                            $prevUrl = '/admin/products?' . http_build_query($prevParams);
                            ?>
                            <a class="page-link" href="<?= $prevUrl ?>">
                                <i class="fas fa-chevron-left"></i> Anterior
                            </a>
                        <?php else: ?>
                            <span class="page-link">
                                <i class="fas fa-chevron-left"></i> Anterior
                            </span>
                        <?php endif; ?>
                    </li>

                    <?php
                    // Calcular rango de páginas a mostrar
                    $start = max(1, $pagination['current_page'] - 2);
                    $end = min($pagination['total_pages'], $pagination['current_page'] + 2);
                    ?>

                    <!-- Primera página -->
                    <?php if ($start > 1): ?>
                        <li class="page-item">
                            <?php
                            $firstParams = $_GET;
                            $firstParams['page'] = 1;
                            $firstUrl = '/admin/products?' . http_build_query($firstParams);
                            ?>
                            <a class="page-link" href="<?= $firstUrl ?>">1</a>
                        </li>
                        <?php if ($start > 2): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Páginas del rango -->
                    <?php for ($i = $start; $i <= $end; $i++): ?>
                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                            <?php if ($i == $pagination['current_page']): ?>
                                <span class="page-link"><?= $i ?></span>
                            <?php else: ?>
                                <?php
                                $pageParams = $_GET;
                                $pageParams['page'] = $i;
                                $pageUrl = '/admin/products?' . http_build_query($pageParams);
                                ?>
                                <a class="page-link" href="<?= $pageUrl ?>"><?= $i ?></a>
                            <?php endif; ?>
                        </li>
                    <?php endfor; ?>

                    <!-- Última página -->
                    <?php if ($end < $pagination['total_pages']): ?>
                        <?php if ($end < $pagination['total_pages'] - 1): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <?php
                            $lastParams = $_GET;
                            $lastParams['page'] = $pagination['total_pages'];
                            $lastUrl = '/admin/products?' . http_build_query($lastParams);
                            ?>
                            <a class="page-link" href="<?= $lastUrl ?>"><?= $pagination['total_pages'] ?></a>
                        </li>
                    <?php endif; ?>

                    <!-- Botón Siguiente -->
                    <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                        <?php if ($pagination['has_next']): ?>
                            <?php
                            $nextParams = $_GET;
                            $nextParams['page'] = $pagination['current_page'] + 1;
                            $nextUrl = '/admin/products?' . http_build_query($nextParams);
                            ?>
                            <a class="page-link" href="<?= $nextUrl ?>">
                                Siguiente <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php else: ?>
                            <span class="page-link">
                                Siguiente <i class="fas fa-chevron-right"></i>
                            </span>
                        <?php endif; ?>
                    </li>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    console.log('Gestión de productos cargada');

    function clearFilters() {
        window.location.href = '/admin/products';
    }

    function duplicateProduct(id, name) {
        if (confirm(`¿Estás seguro de que deseas duplicar el producto "${name}"?`)) {
            fetch(`/admin/products/duplicate/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al duplicar el producto');
            });
        }
    }

    function deleteProduct(id, name) {
        if (confirm(`¿Estás seguro de que deseas eliminar el producto "${name}"?`)) {
            fetch(`/admin/products/delete/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al eliminar el producto');
            });
        }
    }
</script>
<?= $this->endSection() ?>
