<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Pago con PayPal - MrCell Guatemala' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --paypal-blue: #0070ba;
            --paypal-dark-blue: #003087;
            --paypal-yellow: #ffc439;
            --success-color: #28a745;
        }
        
        .payment-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .payment-header {
            background: linear-gradient(135deg, var(--paypal-blue), var(--paypal-dark-blue));
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .paypal-logo {
            font-size: 2rem;
            color: var(--paypal-yellow);
            margin-bottom: 10px;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .currency-conversion {
            background: #e7f3ff;
            border: 1px solid var(--paypal-blue);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .paypal-button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-info {
            background: #f0f8ff;
            border-left: 4px solid var(--paypal-blue);
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin-top: 20px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--paypal-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-mobile-alt me-2"></i>MrCell
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?= base_url() ?>">
                    <i class="fas fa-home me-1"></i>Inicio
                </a>
            </div>
        </div>
    </nav>

    <div class="payment-container">
        <div class="container">
            <div class="payment-card">
                <div class="payment-header">
                    <div class="paypal-logo">
                        <i class="fab fa-paypal"></i>
                    </div>
                    <h2>Pago con PayPal</h2>
                    <p class="mb-0">Pago seguro y rápido con tu cuenta PayPal</p>
                </div>

                <div class="p-4">
                    <!-- Resumen del Pedido -->
                    <div class="order-summary">
                        <h5><i class="fas fa-receipt me-2"></i>Resumen del Pedido</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Número de Pedido:</strong> <?= $order['order_number'] ?></p>
                                <p><strong>Cliente:</strong> <?= $order['customer_name'] ?></p>
                                <p><strong>Email:</strong> <?= $order['customer_email'] ?></p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <p><strong>Subtotal:</strong> Q<?= number_format($order['subtotal'], 2) ?></p>
                                <p><strong>IVA:</strong> Q<?= number_format($order['tax_amount'], 2) ?></p>
                                <p><strong>Envío:</strong> Q<?= number_format($order['shipping_cost'], 2) ?></p>
                                <h5 class="text-primary"><strong>Total: Q<?= number_format($order['total'], 2) ?></strong></h5>
                            </div>
                        </div>
                    </div>

                    <!-- Conversión de Moneda -->
                    <div class="currency-conversion">
                        <h6><i class="fas fa-exchange-alt me-2"></i>Conversión de Moneda</h6>
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center">
                                <div class="h4 text-primary">Q<?= number_format($order['total'], 2) ?></div>
                                <small class="text-muted">Quetzales (GTQ)</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-arrow-right text-muted"></i>
                                <br>
                                <small class="text-muted">Tasa: Q<?= $exchange_rate ?> = $1 USD</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="h4 text-success">$<?= number_format($amount_usd, 2) ?> USD</div>
                                <small class="text-muted">Dólares (USD)</small>
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                PayPal procesará el pago en dólares estadounidenses
                            </small>
                        </div>
                    </div>

                    <!-- Botón de PayPal -->
                    <div class="paypal-button-container">
                        <div id="paypal-button-container"></div>
                        
                        <!-- Botón de respaldo si PayPal no carga -->
                        <div id="paypal-fallback" style="display: none;">
                            <button class="btn btn-primary btn-lg" onclick="simulatePayPalPayment()">
                                <i class="fab fa-paypal me-2"></i>
                                Pagar con PayPal - $<?= number_format($amount_usd, 2) ?> USD
                            </button>
                            <br>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Modo de demostración - PayPal no disponible
                            </small>
                        </div>
                    </div>

                    <!-- Información de Seguridad -->
                    <div class="security-info">
                        <h6><i class="fas fa-shield-alt me-2"></i>Información de Seguridad</h6>
                        <ul class="mb-0">
                            <li>Tus datos financieros están protegidos por PayPal</li>
                            <li>No compartimos tu información de pago con terceros</li>
                            <li>Transacción encriptada con SSL de 256 bits</li>
                            <li>Protección del comprador de PayPal incluida</li>
                        </ul>
                    </div>

                    <div class="text-center mt-4">
                        <a href="<?= base_url('checkout') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Volver al Checkout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h5>Procesando Pago</h5>
            <p class="mb-0">Por favor espera mientras procesamos tu pago con PayPal...</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- PayPal SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id=<?= $paypal_config['client_id'] ?>&currency=USD&intent=capture"></script>
    
    <script>
        // Configuración de PayPal
        const paypalConfig = <?= json_encode($paypal_config) ?>;
        const orderData = <?= json_encode($order) ?>;
        const amountUSD = <?= $amount_usd ?>;

        // Intentar cargar PayPal
        let paypalLoaded = false;
        
        // Timeout para mostrar botón de respaldo si PayPal no carga
        setTimeout(function() {
            if (!paypalLoaded) {
                document.getElementById('paypal-button-container').style.display = 'none';
                document.getElementById('paypal-fallback').style.display = 'block';
            }
        }, 5000);

        // Configurar botones de PayPal
        if (typeof paypal !== 'undefined') {
            paypal.Buttons({
                createOrder: function(data, actions) {
                    paypalLoaded = true;
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: amountUSD.toFixed(2),
                                currency_code: 'USD'
                            },
                            description: 'Pedido #' + orderData.order_number + ' - MrCell Guatemala'
                        }]
                    });
                },
                
                onApprove: function(data, actions) {
                    showLoading();
                    
                    return actions.order.capture().then(function(details) {
                        // Enviar datos al servidor para confirmar el pago
                        fetch('<?= base_url('payment/paypal/confirm') ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                order_id: orderData.id,
                                paypal_order_id: data.orderID,
                                paypal_details: details,
                                amount_usd: amountUSD,
                                amount_gtq: orderData.total
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            hideLoading();
                            if (data.status === 'success') {
                                window.location.href = '<?= base_url('payment/success/') ?>' + orderData.id;
                            } else {
                                alert('Error al procesar el pago: ' + data.message);
                            }
                        })
                        .catch(error => {
                            hideLoading();
                            console.error('Error:', error);
                            alert('Error al procesar el pago. Por favor contacta al soporte.');
                        });
                    });
                },
                
                onError: function(err) {
                    hideLoading();
                    console.error('PayPal Error:', err);
                    alert('Error en PayPal. Por favor intenta de nuevo o usa otro método de pago.');
                },
                
                onCancel: function(data) {
                    hideLoading();
                    alert('Pago cancelado. Puedes intentar de nuevo cuando gustes.');
                }
                
            }).render('#paypal-button-container');
        } else {
            // PayPal no disponible, mostrar botón de respaldo
            document.getElementById('paypal-button-container').style.display = 'none';
            document.getElementById('paypal-fallback').style.display = 'block';
        }

        // Función para simular pago de PayPal (modo demo)
        function simulatePayPalPayment() {
            if (confirm('¿Confirmas el pago de $' + amountUSD.toFixed(2) + ' USD con PayPal?\n\n(Esto es una simulación para demostración)')) {
                showLoading();
                
                // Simular procesamiento
                setTimeout(function() {
                    fetch('<?= base_url('payment/paypal/simulate') ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: orderData.id,
                            amount_usd: amountUSD,
                            amount_gtq: orderData.total
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.status === 'success') {
                            window.location.href = '<?= base_url('payment/success/') ?>' + orderData.id;
                        } else {
                            alert('Error al procesar el pago: ' + data.message);
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('Error:', error);
                        alert('Error al procesar el pago. Por favor contacta al soporte.');
                    });
                }, 2000);
            }
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
    </script>
</body>
</html>
