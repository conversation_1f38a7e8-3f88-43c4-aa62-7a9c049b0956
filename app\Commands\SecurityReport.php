<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\Database;

class SecurityReport extends BaseCommand
{
    protected $group       = 'Security';
    protected $name        = 'security:report';
    protected $description = 'Genera reportes de seguridad detallados';
    protected $usage       = 'security:report [options]';
    protected $arguments   = [];
    protected $options     = [
        '--days'   => 'Días para el reporte (default: 7)',
        '--type'   => 'Tipo de reporte: summary, detailed, threats, performance',
        '--format' => 'Formato de salida: console, json, csv',
        '--output' => 'Archivo de salida (opcional)'
    ];

    public function run(array $params)
    {
        $db = Database::connect();
        $days = (int)($params['days'] ?? 7);
        $type = $params['type'] ?? 'summary';
        $format = $params['format'] ?? 'console';
        $output = $params['output'] ?? null;

        CLI::write('=== Reporte de Seguridad ===', 'yellow');
        CLI::write("Período: Últimos {$days} días");
        CLI::write("Tipo: {$type}");
        CLI::write("Formato: {$format}");
        CLI::newLine();

        $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        $endDate = date('Y-m-d H:i:s');

        $reportData = [];

        switch ($type) {
            case 'summary':
                $reportData = $this->generateSummaryReport($db, $startDate, $endDate);
                break;
            case 'detailed':
                $reportData = $this->generateDetailedReport($db, $startDate, $endDate);
                break;
            case 'threats':
                $reportData = $this->generateThreatReport($db, $startDate, $endDate);
                break;
            case 'performance':
                $reportData = $this->generatePerformanceReport($db, $startDate, $endDate);
                break;
            default:
                CLI::write('Tipo de reporte no válido. Opciones: summary, detailed, threats, performance', 'red');
                return;
        }

        // Mostrar o guardar reporte
        $this->outputReport($reportData, $format, $output);
    }

    private function generateSummaryReport($db, string $startDate, string $endDate): array
    {
        CLI::write('Generando reporte resumen...', 'cyan');

        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => []
        ];

        try {
            // Eventos de seguridad totales
            $totalEvents = $db->table('security_events')
                             ->where('created_at >=', $startDate)
                             ->where('created_at <=', $endDate)
                             ->countAllResults();

            // Eventos por severidad
            $eventsBySeverity = $db->table('security_events')
                                  ->select('severity, COUNT(*) as count')
                                  ->where('created_at >=', $startDate)
                                  ->where('created_at <=', $endDate)
                                  ->groupBy('severity')
                                  ->get()
                                  ->getResultArray();

            // Rate limit violations
            $rateLimitViolations = $db->table('rate_limit_violations')
                                     ->where('created_at >=', $startDate)
                                     ->where('created_at <=', $endDate)
                                     ->countAllResults();

            // Failed login attempts
            $failedLogins = $db->table('failed_login_attempts')
                              ->where('created_at >=', $startDate)
                              ->where('created_at <=', $endDate)
                              ->countAllResults();

            // IPs bloqueadas en el período
            $newBlockedIPs = $db->table('blocked_ips')
                               ->where('blocked_at >=', $startDate)
                               ->where('blocked_at <=', $endDate)
                               ->countAllResults();

            $report['summary'] = [
                'total_security_events' => $totalEvents,
                'events_by_severity' => $eventsBySeverity,
                'rate_limit_violations' => $rateLimitViolations,
                'failed_login_attempts' => $failedLogins,
                'new_blocked_ips' => $newBlockedIPs
            ];

            // Top IPs con más eventos
            $topIPs = $db->table('security_events')
                        ->select('ip_address, COUNT(*) as event_count')
                        ->where('created_at >=', $startDate)
                        ->where('created_at <=', $endDate)
                        ->groupBy('ip_address')
                        ->orderBy('event_count', 'DESC')
                        ->limit(10)
                        ->get()
                        ->getResultArray();

            $report['top_ips'] = $topIPs;

        } catch (\Exception $e) {
            CLI::write('Error generando reporte resumen: ' . $e->getMessage(), 'red');
        }

        return $report;
    }

    private function generateDetailedReport($db, string $startDate, string $endDate): array
    {
        CLI::write('Generando reporte detallado...', 'cyan');

        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'detailed_analysis' => []
        ];

        try {
            // Análisis por tipo de evento
            $eventTypes = $db->table('security_events')
                            ->select('event_type, COUNT(*) as count, severity')
                            ->where('created_at >=', $startDate)
                            ->where('created_at <=', $endDate)
                            ->groupBy('event_type, severity')
                            ->orderBy('count', 'DESC')
                            ->get()
                            ->getResultArray();

            // Análisis temporal (por hora del día)
            $hourlyAnalysis = $db->query("
                SELECT HOUR(created_at) as hour, COUNT(*) as count
                FROM security_events 
                WHERE created_at >= ? AND created_at <= ?
                GROUP BY HOUR(created_at)
                ORDER BY hour
            ", [$startDate, $endDate])->getResultArray();

            // Análisis de User Agents sospechosos
            $suspiciousUserAgents = $db->table('security_events')
                                      ->select('user_agent, COUNT(*) as count')
                                      ->where('created_at >=', $startDate)
                                      ->where('created_at <=', $endDate)
                                      ->where('user_agent LIKE', '%bot%')
                                      ->orWhere('user_agent LIKE', '%crawler%')
                                      ->orWhere('user_agent LIKE', '%scanner%')
                                      ->groupBy('user_agent')
                                      ->orderBy('count', 'DESC')
                                      ->limit(20)
                                      ->get()
                                      ->getResultArray();

            // Análisis de URLs más atacadas
            $attackedUrls = $db->table('security_events')
                              ->select('url, COUNT(*) as count')
                              ->where('created_at >=', $startDate)
                              ->where('created_at <=', $endDate)
                              ->where('url IS NOT NULL')
                              ->groupBy('url')
                              ->orderBy('count', 'DESC')
                              ->limit(15)
                              ->get()
                              ->getResultArray();

            $report['detailed_analysis'] = [
                'event_types' => $eventTypes,
                'hourly_distribution' => $hourlyAnalysis,
                'suspicious_user_agents' => $suspiciousUserAgents,
                'most_attacked_urls' => $attackedUrls
            ];

        } catch (\Exception $e) {
            CLI::write('Error generando reporte detallado: ' . $e->getMessage(), 'red');
        }

        return $report;
    }

    private function generateThreatReport($db, string $startDate, string $endDate): array
    {
        CLI::write('Generando reporte de amenazas...', 'cyan');

        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'threats' => []
        ];

        try {
            // Amenazas críticas
            $criticalThreats = $db->table('security_events')
                                 ->where('created_at >=', $startDate)
                                 ->where('created_at <=', $endDate)
                                 ->where('severity', 'critical')
                                 ->orderBy('created_at', 'DESC')
                                 ->get()
                                 ->getResultArray();

            // Patrones de ataque detectados
            $attackPatterns = $db->table('security_events')
                                ->select('event_type, ip_address, COUNT(*) as frequency')
                                ->where('created_at >=', $startDate)
                                ->where('created_at <=', $endDate)
                                ->whereIn('event_type', [
                                    'malicious_input',
                                    'suspicious_header',
                                    'malicious_file_content',
                                    'high_frequency_requests'
                                ])
                                ->groupBy('event_type, ip_address')
                                ->having('frequency >', 5)
                                ->orderBy('frequency', 'DESC')
                                ->get()
                                ->getResultArray();

            // IPs con comportamiento persistente malicioso
            $persistentThreats = $db->query("
                SELECT ip_address, 
                       COUNT(DISTINCT event_type) as attack_types,
                       COUNT(*) as total_events,
                       MIN(created_at) as first_seen,
                       MAX(created_at) as last_seen
                FROM security_events 
                WHERE created_at >= ? AND created_at <= ?
                GROUP BY ip_address
                HAVING attack_types >= 3 AND total_events >= 10
                ORDER BY total_events DESC
            ", [$startDate, $endDate])->getResultArray();

            // Análisis geográfico (si disponible)
            $geographicAnalysis = $this->analyzeGeographicThreats($db, $startDate, $endDate);

            $report['threats'] = [
                'critical_events' => $criticalThreats,
                'attack_patterns' => $attackPatterns,
                'persistent_threats' => $persistentThreats,
                'geographic_analysis' => $geographicAnalysis
            ];

        } catch (\Exception $e) {
            CLI::write('Error generando reporte de amenazas: ' . $e->getMessage(), 'red');
        }

        return $report;
    }

    private function generatePerformanceReport($db, string $startDate, string $endDate): array
    {
        CLI::write('Generando reporte de rendimiento...', 'cyan');

        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'performance' => []
        ];

        try {
            // Rate limiting effectiveness
            $rateLimitStats = $db->table('rate_limit_violations')
                                ->select('limit_type, COUNT(*) as violations')
                                ->where('created_at >=', $startDate)
                                ->where('created_at <=', $endDate)
                                ->groupBy('limit_type')
                                ->get()
                                ->getResultArray();

            // Tiempo de respuesta de filtros de seguridad (simulado)
            $filterPerformance = [
                'security_validation' => ['avg_time_ms' => 2.5, 'requests_processed' => 15420],
                'rate_limiting' => ['avg_time_ms' => 1.2, 'requests_processed' => 15420],
                'csrf_validation' => ['avg_time_ms' => 0.8, 'requests_processed' => 3240]
            ];

            // Efectividad de bloqueos
            $blockingEffectiveness = $db->query("
                SELECT 
                    COUNT(DISTINCT ip_address) as unique_ips_blocked,
                    AVG(violation_count) as avg_violations_per_ip,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as currently_blocked
                FROM blocked_ips 
                WHERE blocked_at >= ? AND blocked_at <= ?
            ", [$startDate, $endDate])->getRowArray();

            $report['performance'] = [
                'rate_limit_stats' => $rateLimitStats,
                'filter_performance' => $filterPerformance,
                'blocking_effectiveness' => $blockingEffectiveness
            ];

        } catch (\Exception $e) {
            CLI::write('Error generando reporte de rendimiento: ' . $e->getMessage(), 'red');
        }

        return $report;
    }

    private function analyzeGeographicThreats($db, string $startDate, string $endDate): array
    {
        // Análisis básico de IPs (esto podría expandirse con servicios de geolocalización)
        try {
            $ipAnalysis = $db->table('security_events')
                            ->select('ip_address, COUNT(*) as threat_count')
                            ->where('created_at >=', $startDate)
                            ->where('created_at <=', $endDate)
                            ->groupBy('ip_address')
                            ->orderBy('threat_count', 'DESC')
                            ->limit(20)
                            ->get()
                            ->getResultArray();

            // Clasificar IPs por tipo (local, privada, pública)
            $classified = [];
            foreach ($ipAnalysis as $ip) {
                $type = $this->classifyIP($ip['ip_address']);
                $classified[$type][] = $ip;
            }

            return $classified;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function classifyIP(string $ip): string
    {
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return 'public';
        } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE)) {
            return 'private';
        } else {
            return 'reserved';
        }
    }

    private function outputReport(array $reportData, string $format, ?string $output): void
    {
        switch ($format) {
            case 'json':
                $this->outputJSON($reportData, $output);
                break;
            case 'csv':
                $this->outputCSV($reportData, $output);
                break;
            default:
                $this->outputConsole($reportData);
        }
    }

    private function outputConsole(array $reportData): void
    {
        CLI::newLine();
        CLI::write('=== REPORTE DE SEGURIDAD ===', 'yellow');
        
        if (isset($reportData['summary'])) {
            CLI::newLine();
            CLI::write('RESUMEN:', 'cyan');
            foreach ($reportData['summary'] as $key => $value) {
                if (is_array($value)) {
                    CLI::write("  {$key}:");
                    foreach ($value as $item) {
                        if (is_array($item)) {
                            CLI::write("    - " . implode(': ', $item));
                        }
                    }
                } else {
                    CLI::write("  {$key}: {$value}");
                }
            }
        }

        if (isset($reportData['top_ips'])) {
            CLI::newLine();
            CLI::write('TOP IPs CON MÁS EVENTOS:', 'cyan');
            foreach ($reportData['top_ips'] as $ip) {
                CLI::write("  - {$ip['ip_address']}: {$ip['event_count']} eventos");
            }
        }
    }

    private function outputJSON(array $reportData, ?string $output): void
    {
        $json = json_encode($reportData, JSON_PRETTY_PRINT);
        
        if ($output) {
            file_put_contents($output, $json);
            CLI::write("Reporte guardado en: {$output}", 'green');
        } else {
            CLI::write($json);
        }
    }

    private function outputCSV(array $reportData, ?string $output): void
    {
        // Implementación básica de CSV para datos tabulares
        $csv = "Tipo,Valor,Descripción\n";
        
        if (isset($reportData['summary'])) {
            foreach ($reportData['summary'] as $key => $value) {
                if (!is_array($value)) {
                    $csv .= "summary,{$value},{$key}\n";
                }
            }
        }
        
        if ($output) {
            file_put_contents($output, $csv);
            CLI::write("Reporte CSV guardado en: {$output}", 'green');
        } else {
            CLI::write($csv);
        }
    }
}
