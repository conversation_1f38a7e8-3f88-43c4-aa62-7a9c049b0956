<?php
/**
 * Logger mejorado para errores de productos
 * Archivo: app/Libraries/ProductErrorLogger.php
 */

namespace App\Libraries;

class ProductErrorLogger
{
    private $db;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }
    
    /**
     * Log de error detallado
     */
    public function logError($errorType, $errorMessage, $context = [])
    {
        try {
            $request = \Config\Services::request();
            
            $data = [
                "error_type" => $errorType,
                "error_message" => $errorMessage,
                "file_path" => $context["file"] ?? null,
                "line_number" => $context["line"] ?? null,
                "user_id" => session("admin_id") ?? session("user_id") ?? null,
                "request_uri" => $request->getUri()->getPath(),
                "request_method" => $request->getMethod(),
                "request_data" => json_encode($request->getPost() ?: $request->getGet()),
                "user_agent" => $request->getUserAgent()->getAgentString(),
                "ip_address" => $request->getIPAddress(),
                "created_at" => date("Y-m-d H:i:s")
            ];
            
            $this->db->table("error_logs")->insert($data);
            
            // También log en archivo
            log_message("error", "[$errorType] $errorMessage - " . json_encode($context));
            
        } catch (\Exception $e) {
            // Fallback a log de archivo si falla la BD
            log_message("error", "ProductErrorLogger failed: " . $e->getMessage());
            log_message("error", "Original error: [$errorType] $errorMessage");
        }
    }
    
    /**
     * Log específico para productos
     */
    public function logProductError($productId, $action, $error, $context = [])
    {
        $context["product_id"] = $productId;
        $context["action"] = $action;
        
        $this->logError("product_" . $action, $error, $context);
    }
    
    /**
     * Obtener errores recientes
     */
    public function getRecentErrors($limit = 50)
    {
        return $this->db->table("error_logs")
                       ->orderBy("created_at", "DESC")
                       ->limit($limit)
                       ->get()
                       ->getResultArray();
    }
}
?>