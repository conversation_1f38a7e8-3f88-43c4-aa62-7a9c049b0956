<?php

namespace App\Libraries;

use App\Libraries\SimpleCache;

/**
 * Sistema de Listas de Deseos Compartidas
 * Permite crear listas públicas, compartir por enlaces y colaborar
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SharedWishlist
{
    private $db;
    private $cache;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new SimpleCache();
        $this->config = [
            'max_shared_lists_per_user' => env('MAX_SHARED_LISTS_PER_USER', 10),
            'max_items_per_shared_list' => env('MAX_ITEMS_PER_SHARED_LIST', 100),
            'share_link_expiry_days' => env('SHARE_LINK_EXPIRY_DAYS', 30),
            'enable_public_lists' => env('ENABLE_PUBLIC_LISTS', true),
            'enable_collaborative_lists' => env('ENABLE_COLLABORATIVE_LISTS', true),
            'cache_ttl' => 3600 // 1 hora
        ];
    }
    
    /**
     * Crear lista compartida
     * 
     * @param int $userId ID del usuario propietario
     * @param array $data Datos de la lista
     * @return array Resultado de la creación
     */
    public function createSharedList(int $userId, array $data): array
    {
        try {
            // Verificar límite de listas por usuario
            if (!$this->canCreateMoreLists($userId)) {
                return [
                    'success' => false,
                    'error' => 'Has alcanzado el límite máximo de listas compartidas'
                ];
            }
            
            // Validar datos
            $validation = $this->validateListData($data);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }
            
            // Generar slug único
            $slug = $this->generateUniqueSlug($data['name']);
            
            // Generar token de compartir
            $shareToken = $this->generateShareToken();
            
            // Crear lista
            $listData = [
                'user_id' => $userId,
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'slug' => $slug,
                'share_token' => $shareToken,
                'is_public' => $data['is_public'] ?? false,
                'is_collaborative' => $data['is_collaborative'] ?? false,
                'allow_comments' => $data['allow_comments'] ?? true,
                'allow_voting' => $data['allow_voting'] ?? false,
                'privacy_level' => $data['privacy_level'] ?? 'private', // private, friends, public
                'category' => $data['category'] ?? 'general',
                'tags' => json_encode($data['tags'] ?? []),
                'settings' => json_encode($data['settings'] ?? []),
                'expires_at' => $data['expires_at'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $listId = $this->db->table('shared_wishlists')->insert($listData, true);
            
            if (!$listId) {
                return [
                    'success' => false,
                    'error' => 'Error al crear la lista'
                ];
            }
            
            // Agregar productos iniciales si se proporcionan
            if (!empty($data['products'])) {
                $this->addProductsToSharedList($listId, $data['products'], $userId);
            }
            
            // Limpiar cache
            $this->clearUserListsCache($userId);
            
            return [
                'success' => true,
                'list_id' => $listId,
                'slug' => $slug,
                'share_token' => $shareToken,
                'share_url' => base_url("lista/{$slug}"),
                'share_link' => base_url("compartir/{$shareToken}")
            ];
            
        } catch (\Exception $e) {
            log_message('error', 'Error creating shared list: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error interno del servidor'
            ];
        }
    }
    
    /**
     * Obtener lista compartida
     */
    public function getSharedList(string $identifier, int $viewerId = null): array
    {
        try {
            $cacheKey = "shared_list_{$identifier}";
            $cached = $this->cache->get($cacheKey);
            
            if ($cached) {
                return $cached;
            }
            
            // Buscar por slug o token
            $builder = $this->db->table('shared_wishlists sl');
            $builder->select('sl.*, u.first_name, u.last_name, u.name as user_name, u.avatar');
            $builder->join('users u', 'sl.user_id = u.id');
            
            if (strlen($identifier) === 32) {
                // Es un token de compartir
                $builder->where('sl.share_token', $identifier);
            } else {
                // Es un slug
                $builder->where('sl.slug', $identifier);
            }
            
            $list = $builder->get()->getRowArray();
            
            if (!$list) {
                return [
                    'success' => false,
                    'error' => 'Lista no encontrada'
                ];
            }
            
            // Verificar permisos de acceso
            $accessCheck = $this->checkListAccess($list, $viewerId);
            if (!$accessCheck['allowed']) {
                return [
                    'success' => false,
                    'error' => $accessCheck['error']
                ];
            }
            
            // Obtener productos de la lista
            $products = $this->getSharedListProducts($list['id']);
            
            // Obtener estadísticas
            $stats = $this->getListStats($list['id']);
            
            // Obtener colaboradores si es colaborativa
            $collaborators = [];
            if ($list['is_collaborative']) {
                $collaborators = $this->getListCollaborators($list['id']);
            }
            
            // Obtener comentarios si están habilitados
            $comments = [];
            if ($list['allow_comments']) {
                $comments = $this->getListComments($list['id'], 10);
            }
            
            $result = [
                'success' => true,
                'list' => [
                    'id' => $list['id'],
                    'name' => $list['name'],
                    'description' => $list['description'],
                    'slug' => $list['slug'],
                    'is_public' => (bool) $list['is_public'],
                    'is_collaborative' => (bool) $list['is_collaborative'],
                    'allow_comments' => (bool) $list['allow_comments'],
                    'allow_voting' => (bool) $list['allow_voting'],
                    'privacy_level' => $list['privacy_level'],
                    'category' => $list['category'],
                    'tags' => json_decode($list['tags'] ?? '[]', true),
                    'created_at' => $list['created_at'],
                    'updated_at' => $list['updated_at'],
                    'owner' => [
                        'id' => $list['user_id'],
                        'name' => $list['first_name'] . ' ' . $list['last_name'],
                        'username' => $list['user_name'],
                        'avatar' => $list['avatar']
                    ]
                ],
                'products' => $products,
                'stats' => $stats,
                'collaborators' => $collaborators,
                'comments' => $comments,
                'permissions' => $this->getUserPermissions($list, $viewerId)
            ];
            
            // Incrementar contador de vistas
            $this->incrementViewCount($list['id']);
            
            // Guardar en cache
            $this->cache->set($cacheKey, $result, $this->config['cache_ttl']);
            
            return $result;
            
        } catch (\Exception $e) {
            log_message('error', 'Error getting shared list: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error interno del servidor'
            ];
        }
    }
    
    /**
     * Agregar producto a lista compartida
     */
    public function addProductToSharedList(int $listId, int $productId, int $userId, array $options = []): array
    {
        try {
            // Verificar permisos
            $list = $this->getListById($listId);
            if (!$list) {
                return ['success' => false, 'error' => 'Lista no encontrada'];
            }
            
            $permissions = $this->getUserPermissions($list, $userId);
            if (!$permissions['can_add_products']) {
                return ['success' => false, 'error' => 'No tienes permisos para agregar productos'];
            }
            
            // Verificar límite de productos
            $currentCount = $this->getListProductCount($listId);
            if ($currentCount >= $this->config['max_items_per_shared_list']) {
                return ['success' => false, 'error' => 'La lista ha alcanzado el límite máximo de productos'];
            }
            
            // Verificar si el producto ya está en la lista
            $exists = $this->db->table('shared_wishlist_items')
                              ->where('shared_wishlist_id', $listId)
                              ->where('product_id', $productId)
                              ->countAllResults() > 0;
            
            if ($exists) {
                return ['success' => false, 'error' => 'El producto ya está en la lista'];
            }
            
            // Agregar producto
            $itemData = [
                'shared_wishlist_id' => $listId,
                'product_id' => $productId,
                'added_by_user_id' => $userId,
                'priority' => $options['priority'] ?? 'medium',
                'notes' => $options['notes'] ?? '',
                'price_alert_threshold' => $options['price_alert_threshold'] ?? null,
                'quantity_desired' => $options['quantity_desired'] ?? 1,
                'is_purchased' => false,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $itemId = $this->db->table('shared_wishlist_items')->insert($itemData, true);
            
            if (!$itemId) {
                return ['success' => false, 'error' => 'Error al agregar el producto'];
            }
            
            // Actualizar timestamp de la lista
            $this->updateListTimestamp($listId);
            
            // Limpiar cache
            $this->clearListCache($listId);
            
            // Notificar a colaboradores si es necesario
            if ($list['is_collaborative'] && $userId !== $list['user_id']) {
                $this->notifyCollaborators($listId, $userId, 'product_added', [
                    'product_id' => $productId,
                    'item_id' => $itemId
                ]);
            }
            
            return [
                'success' => true,
                'item_id' => $itemId,
                'message' => 'Producto agregado exitosamente'
            ];
            
        } catch (\Exception $e) {
            log_message('error', 'Error adding product to shared list: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error interno del servidor'];
        }
    }
    
    /**
     * Obtener listas públicas populares
     */
    public function getPopularPublicLists(int $limit = 20, array $filters = []): array
    {
        try {
            $cacheKey = 'popular_public_lists_' . md5(serialize([$limit, $filters]));
            $cached = $this->cache->get($cacheKey);
            
            if ($cached) {
                return $cached;
            }
            
            $builder = $this->db->table('shared_wishlists sl');
            $builder->select('sl.*, u.first_name, u.last_name, u.name as user_name, u.avatar,
                             COUNT(swi.id) as product_count,
                             sl.view_count,
                             sl.like_count');
            $builder->join('users u', 'sl.user_id = u.id');
            $builder->join('shared_wishlist_items swi', 'sl.id = swi.shared_wishlist_id', 'left');
            
            // Solo listas públicas
            $builder->where('sl.is_public', 1);
            $builder->where('sl.privacy_level', 'public');
            
            // Filtros opcionales
            if (!empty($filters['category'])) {
                $builder->where('sl.category', $filters['category']);
            }
            
            if (!empty($filters['tags'])) {
                foreach ($filters['tags'] as $tag) {
                    $builder->like('sl.tags', $tag);
                }
            }
            
            // No incluir listas expiradas
            $builder->where('(sl.expires_at IS NULL OR sl.expires_at > NOW())');
            
            $builder->groupBy('sl.id');
            
            // Ordenar por popularidad (combinando vistas, likes y productos)
            $builder->orderBy('(sl.view_count + sl.like_count * 2 + COUNT(swi.id) * 3)', 'DESC');
            $builder->limit($limit);
            
            $lists = $builder->get()->getResultArray();
            
            // Formatear resultados
            $formattedLists = array_map(function($list) {
                return [
                    'id' => $list['id'],
                    'name' => $list['name'],
                    'description' => $list['description'],
                    'slug' => $list['slug'],
                    'category' => $list['category'],
                    'tags' => json_decode($list['tags'] ?? '[]', true),
                    'product_count' => (int) $list['product_count'],
                    'view_count' => (int) $list['view_count'],
                    'like_count' => (int) $list['like_count'],
                    'created_at' => $list['created_at'],
                    'owner' => [
                        'name' => $list['first_name'] . ' ' . $list['last_name'],
                        'username' => $list['user_name'],
                        'avatar' => $list['avatar']
                    ],
                    'url' => base_url("lista/{$list['slug']}")
                ];
            }, $lists);
            
            $result = [
                'success' => true,
                'lists' => $formattedLists,
                'total' => count($formattedLists)
            ];
            
            // Guardar en cache
            $this->cache->set($cacheKey, $result, $this->config['cache_ttl']);
            
            return $result;
            
        } catch (\Exception $e) {
            log_message('error', 'Error getting popular public lists: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error interno del servidor'
            ];
        }
    }
    
    /**
     * Agregar comentario a lista
     */
    public function addComment(int $listId, int $userId, string $comment, int $parentId = null): array
    {
        try {
            // Verificar que la lista permite comentarios
            $list = $this->getListById($listId);
            if (!$list || !$list['allow_comments']) {
                return ['success' => false, 'error' => 'Los comentarios no están permitidos en esta lista'];
            }
            
            // Validar comentario
            if (empty(trim($comment)) || strlen($comment) > 1000) {
                return ['success' => false, 'error' => 'El comentario debe tener entre 1 y 1000 caracteres'];
            }
            
            $commentData = [
                'shared_wishlist_id' => $listId,
                'user_id' => $userId,
                'parent_id' => $parentId,
                'comment' => trim($comment),
                'is_approved' => true, // Auto-aprobar por ahora
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $commentId = $this->db->table('shared_wishlist_comments')->insert($commentData, true);
            
            if (!$commentId) {
                return ['success' => false, 'error' => 'Error al agregar el comentario'];
            }
            
            // Limpiar cache
            $this->clearListCache($listId);
            
            // Notificar al propietario si no es él quien comenta
            if ($userId !== $list['user_id']) {
                $this->notifyListOwner($listId, $userId, 'comment_added', [
                    'comment_id' => $commentId,
                    'comment' => $comment
                ]);
            }
            
            return [
                'success' => true,
                'comment_id' => $commentId,
                'message' => 'Comentario agregado exitosamente'
            ];
            
        } catch (\Exception $e) {
            log_message('error', 'Error adding comment: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error interno del servidor'];
        }
    }
    
    /**
     * Dar like/unlike a una lista
     */
    public function toggleLike(int $listId, int $userId): array
    {
        try {
            // Verificar si ya le dio like
            $existingLike = $this->db->table('shared_wishlist_likes')
                                   ->where('shared_wishlist_id', $listId)
                                   ->where('user_id', $userId)
                                   ->get()
                                   ->getRowArray();
            
            if ($existingLike) {
                // Quitar like
                $this->db->table('shared_wishlist_likes')
                        ->where('id', $existingLike['id'])
                        ->delete();
                
                // Decrementar contador
                $this->db->table('shared_wishlists')
                        ->where('id', $listId)
                        ->set('like_count', 'like_count - 1', false)
                        ->update();
                
                $action = 'unliked';
            } else {
                // Agregar like
                $this->db->table('shared_wishlist_likes')->insert([
                    'shared_wishlist_id' => $listId,
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                // Incrementar contador
                $this->db->table('shared_wishlists')
                        ->where('id', $listId)
                        ->set('like_count', 'like_count + 1', false)
                        ->update();
                
                $action = 'liked';
            }
            
            // Obtener nuevo contador
            $newCount = $this->db->table('shared_wishlists')
                                ->select('like_count')
                                ->where('id', $listId)
                                ->get()
                                ->getRowArray()['like_count'] ?? 0;
            
            // Limpiar cache
            $this->clearListCache($listId);
            
            return [
                'success' => true,
                'action' => $action,
                'like_count' => (int) $newCount
            ];
            
        } catch (\Exception $e) {
            log_message('error', 'Error toggling like: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error interno del servidor'];
        }
    }
    
    // Métodos auxiliares privados
    
    private function canCreateMoreLists(int $userId): bool
    {
        $count = $this->db->table('shared_wishlists')
                         ->where('user_id', $userId)
                         ->countAllResults();
        
        return $count < $this->config['max_shared_lists_per_user'];
    }
    
    private function validateListData(array $data): array
    {
        if (empty($data['name']) || strlen($data['name']) > 100) {
            return ['valid' => false, 'error' => 'El nombre debe tener entre 1 y 100 caracteres'];
        }
        
        if (isset($data['description']) && strlen($data['description']) > 500) {
            return ['valid' => false, 'error' => 'La descripción no puede exceder 500 caracteres'];
        }
        
        return ['valid' => true];
    }
    
    private function generateUniqueSlug(string $name): string
    {
        $baseSlug = url_title($name, '-', true);
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    private function slugExists(string $slug): bool
    {
        return $this->db->table('shared_wishlists')
                       ->where('slug', $slug)
                       ->countAllResults() > 0;
    }
    
    private function generateShareToken(): string
    {
        do {
            $token = bin2hex(random_bytes(16));
        } while ($this->tokenExists($token));
        
        return $token;
    }
    
    private function tokenExists(string $token): bool
    {
        return $this->db->table('shared_wishlists')
                       ->where('share_token', $token)
                       ->countAllResults() > 0;
    }
    
    private function checkListAccess(array $list, int $viewerId = null): array
    {
        // Lista pública
        if ($list['is_public'] && $list['privacy_level'] === 'public') {
            return ['allowed' => true];
        }
        
        // Propietario siempre puede acceder
        if ($viewerId && $viewerId === $list['user_id']) {
            return ['allowed' => true];
        }
        
        // Lista privada sin usuario
        if (!$viewerId) {
            return ['allowed' => false, 'error' => 'Esta lista es privada'];
        }
        
        // Verificar si es colaborador
        if ($list['is_collaborative']) {
            $isCollaborator = $this->db->table('shared_wishlist_collaborators')
                                      ->where('shared_wishlist_id', $list['id'])
                                      ->where('user_id', $viewerId)
                                      ->where('status', 'accepted')
                                      ->countAllResults() > 0;
            
            if ($isCollaborator) {
                return ['allowed' => true];
            }
        }
        
        return ['allowed' => false, 'error' => 'No tienes permisos para ver esta lista'];
    }
    
    private function getSharedListProducts(int $listId): array
    {
        $builder = $this->db->table('shared_wishlist_items swi');
        $builder->select('swi.*, p.name, p.slug, p.price_regular, p.price_sale, p.featured_image, 
                         p.stock_quantity, p.stock_status, p.rating_average,
                         u.first_name, u.last_name');
        $builder->join('products p', 'swi.product_id = p.id');
        $builder->join('users u', 'swi.added_by_user_id = u.id');
        $builder->where('swi.shared_wishlist_id', $listId);
        $builder->orderBy('swi.created_at', 'DESC');
        
        return $builder->get()->getResultArray();
    }
    
    private function getListStats(int $listId): array
    {
        $stats = $this->db->table('shared_wishlists')
                         ->select('view_count, like_count')
                         ->where('id', $listId)
                         ->get()
                         ->getRowArray();
        
        $productCount = $this->getListProductCount($listId);
        $commentCount = $this->getListCommentCount($listId);
        
        return [
            'product_count' => $productCount,
            'view_count' => (int) ($stats['view_count'] ?? 0),
            'like_count' => (int) ($stats['like_count'] ?? 0),
            'comment_count' => $commentCount
        ];
    }
    
    private function getListProductCount(int $listId): int
    {
        return $this->db->table('shared_wishlist_items')
                       ->where('shared_wishlist_id', $listId)
                       ->countAllResults();
    }
    
    private function getListCommentCount(int $listId): int
    {
        return $this->db->table('shared_wishlist_comments')
                       ->where('shared_wishlist_id', $listId)
                       ->where('is_approved', 1)
                       ->countAllResults();
    }
    
    private function getListCollaborators(int $listId): array
    {
        $builder = $this->db->table('shared_wishlist_collaborators swc');
        $builder->select('swc.*, u.first_name, u.last_name, u.name, u.avatar');
        $builder->join('users u', 'swc.user_id = u.id');
        $builder->where('swc.shared_wishlist_id', $listId);
        $builder->where('swc.status', 'accepted');
        
        return $builder->get()->getResultArray();
    }
    
    private function getListComments(int $listId, int $limit = 10): array
    {
        $builder = $this->db->table('shared_wishlist_comments swc');
        $builder->select('swc.*, u.first_name, u.last_name, u.avatar');
        $builder->join('users u', 'swc.user_id = u.id');
        $builder->where('swc.shared_wishlist_id', $listId);
        $builder->where('swc.is_approved', 1);
        $builder->orderBy('swc.created_at', 'DESC');
        $builder->limit($limit);
        
        return $builder->get()->getResultArray();
    }
    
    private function getUserPermissions(array $list, int $userId = null): array
    {
        $permissions = [
            'can_view' => true,
            'can_edit' => false,
            'can_delete' => false,
            'can_add_products' => false,
            'can_remove_products' => false,
            'can_comment' => false,
            'can_like' => false,
            'can_share' => true
        ];
        
        if (!$userId) {
            return $permissions;
        }
        
        // Propietario tiene todos los permisos
        if ($userId === $list['user_id']) {
            return array_map(function() { return true; }, $permissions);
        }
        
        // Usuario logueado puede dar like y comentar
        $permissions['can_like'] = true;
        $permissions['can_comment'] = $list['allow_comments'];
        
        // Si es colaborativa, verificar permisos de colaborador
        if ($list['is_collaborative']) {
            $collaborator = $this->db->table('shared_wishlist_collaborators')
                                   ->where('shared_wishlist_id', $list['id'])
                                   ->where('user_id', $userId)
                                   ->where('status', 'accepted')
                                   ->get()
                                   ->getRowArray();
            
            if ($collaborator) {
                $permissions['can_add_products'] = true;
                $permissions['can_remove_products'] = $collaborator['can_remove_items'] ?? false;
            }
        }
        
        return $permissions;
    }
    
    private function incrementViewCount(int $listId): void
    {
        $this->db->table('shared_wishlists')
                ->where('id', $listId)
                ->set('view_count', 'view_count + 1', false)
                ->update();
    }
    
    private function getListById(int $listId): ?array
    {
        return $this->db->table('shared_wishlists')
                       ->where('id', $listId)
                       ->get()
                       ->getRowArray();
    }
    
    private function updateListTimestamp(int $listId): void
    {
        $this->db->table('shared_wishlists')
                ->where('id', $listId)
                ->update(['updated_at' => date('Y-m-d H:i:s')]);
    }
    
    private function clearListCache(int $listId): void
    {
        // Implementar limpieza de cache específica
        $this->cache->delete("shared_list_{$listId}");
    }
    
    private function clearUserListsCache(int $userId): void
    {
        // Implementar limpieza de cache de usuario
        $this->cache->delete("user_shared_lists_{$userId}");
    }
    
    // Métodos de notificación (implementación básica)
    private function notifyCollaborators(int $listId, int $userId, string $action, array $data): void
    {
        // Implementar notificaciones a colaboradores
    }
    
    private function notifyListOwner(int $listId, int $userId, string $action, array $data): void
    {
        // Implementar notificaciones al propietario
    }
    
    private function addProductsToSharedList(int $listId, array $productIds, int $userId): void
    {
        foreach ($productIds as $productId) {
            $this->addProductToSharedList($listId, $productId, $userId);
        }
    }
}
