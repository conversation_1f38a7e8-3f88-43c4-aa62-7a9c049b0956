<?php

require_once 'vendor/autoload.php';

// Configurar CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Cargar helper
helper('expiration');

echo "🧪 PROBANDO DATOS DE CADUCIDAD...\n\n";

try {
    // Probar función de productos próximos a caducar
    $expiringProducts = getExpiringProducts();
    echo "📦 Productos próximos a caducar: " . count($expiringProducts) . "\n";
    
    if (!empty($expiringProducts)) {
        echo "📋 Lista de productos:\n";
        foreach ($expiringProducts as $product) {
            echo "  - {$product['name']} (SKU: {$product['sku']}) - Caduca: {$product['expiration_date']} - Estado: {$product['expiration_status']}\n";
        }
    } else {
        echo "⚠️ No se encontraron productos próximos a caducar\n";
    }
    
    // Probar resumen de caducidad
    $summary = getExpirationSummary();
    echo "\n📊 Resumen de caducidad:\n";
    echo "  - Caducados: " . ($summary['expired'] ?? 0) . "\n";
    echo "  - Caducan hoy: " . ($summary['expires_today'] ?? 0) . "\n";
    echo "  - Caducan esta semana: " . ($summary['expires_this_week'] ?? 0) . "\n";
    echo "  - Caducan este mes: " . ($summary['expires_this_month'] ?? 0) . "\n";
    
    // Verificar productos con caducidad en la base de datos
    $db = \Config\Database::connect();
    $totalWithExpiration = $db->query("
        SELECT COUNT(*) as total 
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
    ")->getRowArray();
    
    echo "\n📈 Total productos con caducidad: " . $totalWithExpiration['total'] . "\n";
    
    // Mostrar algunos productos con caducidad
    $productsWithExpiration = $db->query("
        SELECT name, sku, expiration_date, has_expiration, is_active
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
        LIMIT 5
    ")->getResultArray();
    
    echo "\n📋 Productos con caducidad en BD:\n";
    foreach ($productsWithExpiration as $product) {
        echo "  - {$product['name']} (SKU: {$product['sku']}) - Caduca: {$product['expiration_date']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ Prueba completada\n";
