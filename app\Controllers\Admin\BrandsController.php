<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\BrandModel;

class BrandsController extends BaseController
{
    protected $brandModel;
    protected $db;

    public function __construct()
    {
        $this->brandModel = new BrandModel();
        $this->db = \Config\Database::connect();
    }

    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!session()->get('admin_id') || !session()->get('is_admin_logged_in')) {
            return redirect()->to('/admin/login');
        }
        return true;
    }

    /**
     * Display brands management page
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $brands = $this->brandModel->getBrandsWithProductCount();

            $data = [
                'title' => 'Gestión de Marcas - Admin MrCell',
                'brands' => $brands
            ];

            return view('admin/brands/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en BrandsController::index: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Error al cargar marcas');
        }
    }

    /**
     * Show create brand form
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->store();
        }

        $data = [
            'title' => 'Nueva Marca - Admin MrCell'
        ];

        return view('admin/brands/create', $data);
    }

    /**
     * Store new brand
     */
    public function store()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $data = [
                'name' => trim($this->request->getPost('name')),
                'description' => trim($this->request->getPost('description')),
                'website' => trim($this->request->getPost('website')),
                'email' => trim($this->request->getPost('email')),
                'phone' => trim($this->request->getPost('phone')),
                'address' => trim($this->request->getPost('address')),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'sort_order' => (int)($this->request->getPost('sort_order') ?: 0)
            ];

            // Handle logo upload
            $logoFile = $this->request->getFile('logo');
            if ($logoFile && $logoFile->isValid() && !$logoFile->hasMoved()) {
                $newName = $logoFile->getRandomName();
                $logoFile->move(ROOTPATH . 'public/assets/img/brands', $newName);
                $data['logo'] = 'assets/img/brands/' . $newName;
            }

            if ($this->brandModel->insert($data)) {
                return redirect()->to('/admin/brands')->with('success', 'Marca creada correctamente');
            } else {
                $errors = $this->brandModel->errors();
                $errorMessage = is_array($errors) ? implode(', ', $errors) : 'Error al crear la marca';
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error al crear marca: ' . $e->getMessage());
        }
    }

    /**
     * Show edit brand form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->update($id);
        }

        try {
            $brand = $this->brandModel->find($id);
            
            if (!$brand) {
                return redirect()->to('/admin/brands')->with('error', 'Marca no encontrada');
            }

            $data = [
                'title' => 'Editar Marca - Admin MrCell',
                'brand' => $brand
            ];

            return view('admin/brands/edit', $data);

        } catch (\Exception $e) {
            return redirect()->to('/admin/brands')->with('error', 'Error al cargar marca: ' . $e->getMessage());
        }
    }

    /**
     * Update brand
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $brand = $this->brandModel->find($id);
            
            if (!$brand) {
                return redirect()->to('/admin/brands')->with('error', 'Marca no encontrada');
            }

            $data = [
                'name' => trim($this->request->getPost('name')),
                'description' => trim($this->request->getPost('description')),
                'website' => trim($this->request->getPost('website')),
                'email' => trim($this->request->getPost('email')),
                'phone' => trim($this->request->getPost('phone')),
                'address' => trim($this->request->getPost('address')),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'sort_order' => (int)($this->request->getPost('sort_order') ?: 0)
            ];

            // Handle logo upload
            $logoFile = $this->request->getFile('logo');
            if ($logoFile && $logoFile->isValid() && !$logoFile->hasMoved()) {
                // Delete old logo if exists
                if (!empty($brand['logo']) && file_exists(ROOTPATH . 'public/' . $brand['logo'])) {
                    unlink(ROOTPATH . 'public/' . $brand['logo']);
                }
                
                $newName = $logoFile->getRandomName();
                $logoFile->move(ROOTPATH . 'public/assets/img/brands', $newName);
                $data['logo'] = 'assets/img/brands/' . $newName;
            }

            if ($this->brandModel->update($id, $data)) {
                return redirect()->to('/admin/brands')->with('success', 'Marca actualizada correctamente');
            } else {
                $errors = $this->brandModel->errors();
                $errorMessage = is_array($errors) ? implode(', ', $errors) : 'Error al actualizar la marca';
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error al actualizar marca: ' . $e->getMessage());
        }
    }

    /**
     * Delete brand
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $brand = $this->brandModel->find($id);
            
            if (!$brand) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Marca no encontrada'
                ]);
            }

            // Check if brand can be deleted
            if (!$this->brandModel->canDelete($id)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No se puede eliminar la marca porque tiene productos asociados'
                ]);
            }

            if ($this->brandModel->delete($id)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Marca eliminada correctamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error al eliminar la marca'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error al eliminar marca: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Toggle brand status
     */
    public function toggleStatus($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $brand = $this->brandModel->find($id);
            
            if (!$brand) {
                return redirect()->to('/admin/brands')->with('error', 'Marca no encontrada');
            }

            $newStatus = $brand['is_active'] ? 0 : 1;
            
            if ($this->brandModel->update($id, ['is_active' => $newStatus])) {
                $statusText = $newStatus ? 'activada' : 'desactivada';
                return redirect()->to('/admin/brands')->with('success', "Marca {$statusText} correctamente");
            } else {
                return redirect()->to('/admin/brands')->with('error', 'Error al cambiar estado de la marca');
            }

        } catch (\Exception $e) {
            return redirect()->to('/admin/brands')->with('error', 'Error al cambiar estado: ' . $e->getMessage());
        }
    }
}
