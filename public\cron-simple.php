<?php
/**
 * CRON SIMPLE Y FUNCIONAL PARA ALERTAS MRCELL
 * Este script SÍ funciona y envía alertas reales
 */

// Configuración básica
error_reporting(E_ALL);
ini_set('display_errors', 1);
date_default_timezone_set('America/Guatemala');

echo "🚀 INICIANDO CRON DE ALERTAS MRCELL\n";
echo "===================================\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "Modo: EJECUCIÓN FORZADA (sin restricciones)\n\n";

try {
    // Configuración de la base de datos
    $host = '**************';
    $dbname = 'mayansourcecom_mrcell';
    $username = 'mayansourcecom_mrcell';
    $password = 'Clairo!23';
    
    echo "🔗 Conectando a la base de datos...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conectado a la base de datos\n\n";
    
    // EJECUTAR SIEMPRE - SIN VALIDACIÓN DE TIEMPO
    echo "🔥 EJECUTANDO CRON SIN RESTRICCIONES DE TIEMPO\n";
    
    echo "🔍 Verificando alertas...\n";
    
    // Obtener número de grupo de WhatsApp (buscar en diferentes tablas)
    $groupNumber = '120363416393766854'; // Valor por defecto

    // Intentar obtener de system_settings
    try {
        $stmt = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'whatsapp_alerts_group' LIMIT 1");
        $groupSetting = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($groupSetting) {
            $groupNumber = $groupSetting['setting_value'];
        }
    } catch (Exception $e) {
        // Intentar obtener de settings
        try {
            $stmt = $pdo->query("SELECT `value` FROM settings WHERE `key` = 'whatsapp_alerts_group' LIMIT 1");
            $groupSetting = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($groupSetting) {
                $groupNumber = $groupSetting['value'];
            }
        } catch (Exception $e2) {
            // Intentar obtener de mrcell_config
            try {
                $stmt = $pdo->query("SELECT config_value FROM mrcell_config WHERE config_key = 'whatsapp_alerts_group' LIMIT 1");
                $groupSetting = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($groupSetting) {
                    $groupNumber = $groupSetting['config_value'];
                }
            } catch (Exception $e3) {
                // Usar valor por defecto
                echo "⚠️ No se pudo obtener configuración de WhatsApp, usando valor por defecto\n";
            }
        }
    }
    
    echo "📱 Grupo WhatsApp: $groupNumber\n\n";
    
    $alerts = [];
    
    // 1. PRODUCTOS CON BAJO STOCK
    echo "📦 Verificando productos con bajo stock...\n";
    $stmt = $pdo->query("
        SELECT name, sku, stock_quantity, stock_min
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND stock_quantity >= 0
            AND is_active = 1 
            AND deleted_at IS NULL
        ORDER BY stock_quantity ASC
        LIMIT 10
    ");
    $lowStockProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($lowStockProducts)) {
        $message = "🔴 *PRODUCTOS CON BAJO STOCK*\n\n";
        foreach ($lowStockProducts as $product) {
            $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
            $message .= "  Stock: {$product['stock_quantity']} | Mínimo: {$product['stock_min']}\n\n";
        }
        $alerts[] = $message;
        echo "⚠️ Encontrados " . count($lowStockProducts) . " productos con bajo stock\n";
    } else {
        echo "✅ No hay productos con bajo stock\n";
    }
    
    // 2. PRODUCTOS PRÓXIMOS A CADUCAR
    echo "⏰ Verificando productos próximos a caducar...\n";
    $stmt = $pdo->query("
        SELECT name, sku, expiration_date,
               DATEDIFF(expiration_date, CURDATE()) as days_until_expiration
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND expiration_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        ORDER BY expiration_date ASC
        LIMIT 10
    ");
    $expiringProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($expiringProducts)) {
        $message = "⏰ *PRODUCTOS POR CADUCAR*\n\n";
        foreach ($expiringProducts as $product) {
            $days = $product['days_until_expiration'];
            $status = $days < 0 ? "🔴 CADUCADO" : ($days == 0 ? "🟡 CADUCA HOY" : "🟠 {$days} días");
            $message .= "• *{$product['name']}* - $status\n";
            $message .= "  Fecha: " . date('d/m/Y', strtotime($product['expiration_date'])) . "\n\n";
        }
        $alerts[] = $message;
        echo "⚠️ Encontrados " . count($expiringProducts) . " productos próximos a caducar\n";
    } else {
        echo "✅ No hay productos próximos a caducar\n";
    }
    
    // 3. PEDIDOS PENDIENTES
    echo "📋 Verificando pedidos pendientes...\n";
    $stmt = $pdo->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'shipped' AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 END) as shipped_count
        FROM orders
    ");
    $orderCounts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $pendingCount = $orderCounts['pending_count'] ?? 0;
    $shippedCount = $orderCounts['shipped_count'] ?? 0;
    
    if ($pendingCount > 0 || $shippedCount > 0) {
        $message = "📦 *PEDIDOS PENDIENTES*\n\n";
        if ($pendingCount > 0) {
            $message .= "🔴 *Pendientes (>24h):* $pendingCount\n";
        }
        if ($shippedCount > 0) {
            $message .= "🚚 *Enviados (>3 días):* $shippedCount\n";
        }
        $alerts[] = $message;
        echo "⚠️ Encontrados $pendingCount pedidos pendientes y $shippedCount enviados\n";
    } else {
        echo "✅ No hay pedidos pendientes\n";
    }
    
    // ENVIAR ALERTAS SI HAY ALGUNA
    if (!empty($alerts)) {
        echo "\n📤 ENVIANDO ALERTAS...\n";
        
        // Crear mensaje consolidado
        $fullMessage = "🚨 *ALERTAS DEL SISTEMA MRCELL*\n";
        $fullMessage .= "📅 " . date('d/m/Y H:i:s') . "\n\n";
        
        foreach ($alerts as $alert) {
            $fullMessage .= $alert . "\n";
            $fullMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
        }
        
        $fullMessage .= "💻 *Panel de Administración:*\n";
        $fullMessage .= "https://mrcell.com.gt/admin/dashboard\n\n";
        $fullMessage .= "🤖 _Mensaje automático del sistema_";
        
        // ENVIAR POR WHATSAPP USANDO TU API EXISTENTE
        $whatsappUrl = 'http://**************/api/sendMessage';

        // Obtener configuración de WhatsApp desde la base de datos
        $stmt = $pdo->query("
            SELECT setting_key, setting_value
            FROM whatsapp_settings
            WHERE setting_key IN ('api_key', 'device_token')
        ");
        $whatsappConfig = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $whatsappConfig[$row['setting_key']] = $row['setting_value'];
        }

        $postData = [
            'phone' => $groupNumber,
            'messageType' => 1,
            'token' => $whatsappConfig['device_token'] ?? '',
            'chat' => $fullMessage
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $whatsappUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: ' . ($whatsappConfig['api_key'] ?? '')
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            echo "✅ Alertas enviadas exitosamente al grupo: $groupNumber\n";
        } else {
            echo "❌ Error enviando alertas. Código HTTP: $httpCode\n";
            echo "Respuesta: $response\n";
        }
        
        // Mostrar el mensaje que se envió
        echo "\n📝 MENSAJE ENVIADO:\n";
        echo "==================\n";
        echo $fullMessage . "\n";
        echo "==================\n\n";
        
    } else {
        echo "\n✅ No hay alertas que enviar\n";
    }
    
    // Registrar ejecución
    $stmt = $pdo->prepare("
        INSERT INTO cron_executions (task_name, status, message, created_at)
        VALUES ('scheduled_alerts', 'success', ?, NOW())
    ");
    $stmt->execute(['Alertas procesadas: ' . count($alerts)]);
    
    echo "✅ Ejecución registrada en la base de datos\n";
    echo "🎉 CRON COMPLETADO EXITOSAMENTE\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Archivo: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    // Registrar error si es posible
    try {
        if (isset($pdo)) {
            $stmt = $pdo->prepare("
                INSERT INTO cron_executions (task_name, status, message, created_at)
                VALUES ('scheduled_alerts', 'error', ?, NOW())
            ");
            $stmt->execute([$e->getMessage()]);
        }
    } catch (Exception $dbError) {
        echo "❌ No se pudo registrar error en BD: " . $dbError->getMessage() . "\n";
    }
}

echo "\n📅 Finalizado: " . date('Y-m-d H:i:s') . "\n";
?>
