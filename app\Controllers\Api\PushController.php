<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Libraries\PushNotificationManager;

/**
 * Controlador de API para Notificaciones Push
 * Maneja suscripciones y envío de notificaciones push
 */
class PushController extends BaseController
{
    private $pushManager;
    
    public function __construct()
    {
        $this->pushManager = new PushNotificationManager();
    }
    
    /**
     * Suscribir usuario a notificaciones push
     */
    public function subscribe()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['subscription'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Datos de suscripción requeridos'
                ]);
            }
            
            $subscription = $json['subscription'];
            $userId = $json['user_id'] ?? null;
            $deviceInfo = $json['device_info'] ?? [];
            
            // Validar estructura de suscripción
            if (!isset($subscription['endpoint']) || !isset($subscription['keys'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Estructura de suscripción inválida'
                ]);
            }
            
            $result = $this->pushManager->subscribe(
                $subscription,
                $userId,
                $deviceInfo
            );
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message'],
                'subscription_id' => $result['subscription_id'] ?? null
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error procesando suscripción: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Cancelar suscripción a notificaciones push
     */
    public function unsubscribe()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['endpoint'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Endpoint requerido para cancelar suscripción'
                ]);
            }
            
            $result = $this->pushManager->unsubscribe($json['endpoint']);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error cancelando suscripción: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Enviar notificación push de prueba
     */
    public function sendTest()
    {
        try {
            $json = $this->request->getJSON(true);
            
            $notification = [
                'title' => $json['title'] ?? '🧪 Notificación de Prueba',
                'body' => $json['body'] ?? 'Esta es una notificación de prueba desde MrCell Guatemala',
                'icon' => $json['icon'] ?? '/assets/images/icon-192x192.png',
                'badge' => $json['badge'] ?? '/assets/images/badge-72x72.png',
                'data' => [
                    'url' => $json['url'] ?? '/',
                    'timestamp' => time(),
                    'type' => 'test'
                ]
            ];
            
            $targetUserId = $json['user_id'] ?? null;
            $targetEndpoint = $json['endpoint'] ?? null;
            
            if ($targetUserId) {
                $result = $this->pushManager->sendToUser($targetUserId, $notification);
            } elseif ($targetEndpoint) {
                $result = $this->pushManager->sendToEndpoint($targetEndpoint, $notification);
            } else {
                $result = $this->pushManager->sendToAll($notification);
            }
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message'],
                'sent_count' => $result['sent_count'] ?? 0,
                'failed_count' => $result['failed_count'] ?? 0
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error enviando notificación: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener estadísticas de notificaciones push
     */
    public function getStats()
    {
        try {
            $stats = $this->pushManager->getStatistics();
            
            return $this->response->setJSON([
                'success' => true,
                'stats' => $stats
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error obteniendo estadísticas: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener suscripciones activas
     */
    public function getSubscriptions()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $limit = $this->request->getGet('limit') ?? 50;
            
            $subscriptions = $this->pushManager->getSubscriptions($page, $limit);
            
            return $this->response->setJSON([
                'success' => true,
                'subscriptions' => $subscriptions['data'],
                'pagination' => $subscriptions['pagination']
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error obteniendo suscripciones: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Enviar notificación de precio
     */
    public function sendPriceAlert()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['product_id'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de producto requerido'
                ]);
            }
            
            $productId = $json['product_id'];
            $oldPrice = $json['old_price'] ?? 0;
            $newPrice = $json['new_price'] ?? 0;
            $targetUsers = $json['target_users'] ?? [];
            
            $result = $this->pushManager->sendPriceAlert(
                $productId,
                $oldPrice,
                $newPrice,
                $targetUsers
            );
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message'],
                'sent_count' => $result['sent_count'] ?? 0
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error enviando alerta de precio: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Enviar notificación de stock
     */
    public function sendStockAlert()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['product_id'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de producto requerido'
                ]);
            }
            
            $productId = $json['product_id'];
            $targetUsers = $json['target_users'] ?? [];
            
            $result = $this->pushManager->sendStockAlert($productId, $targetUsers);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message'],
                'sent_count' => $result['sent_count'] ?? 0
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error enviando alerta de stock: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Enviar notificación de carrito abandonado
     */
    public function sendCartReminder()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['user_id'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de usuario requerido'
                ]);
            }
            
            $userId = $json['user_id'];
            $cartItems = $json['cart_items'] ?? [];
            
            $result = $this->pushManager->sendCartReminder($userId, $cartItems);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error enviando recordatorio de carrito: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Enviar notificación de promoción
     */
    public function sendPromotion()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['title']) || !isset($json['message'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Título y mensaje requeridos'
                ]);
            }
            
            $promotion = [
                'title' => $json['title'],
                'message' => $json['message'],
                'image' => $json['image'] ?? null,
                'url' => $json['url'] ?? '/',
                'expiry_date' => $json['expiry_date'] ?? null
            ];
            
            $targetSegment = $json['target_segment'] ?? 'all';
            
            $result = $this->pushManager->sendPromotion($promotion, $targetSegment);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message'],
                'sent_count' => $result['sent_count'] ?? 0
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error enviando promoción: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Actualizar preferencias de notificaciones
     */
    public function updatePreferences()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json || !isset($json['endpoint'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Endpoint requerido'
                ]);
            }
            
            $endpoint = $json['endpoint'];
            $preferences = $json['preferences'] ?? [];
            
            $result = $this->pushManager->updatePreferences($endpoint, $preferences);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error actualizando preferencias: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener configuración de VAPID para el cliente
     */
    public function getVapidKey()
    {
        try {
            $vapidKey = $this->pushManager->getPublicVapidKey();
            
            return $this->response->setJSON([
                'success' => true,
                'vapid_key' => $vapidKey
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error obteniendo clave VAPID: ' . $e->getMessage()
            ]);
        }
    }
}
