<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FixProductSlugs extends Migration
{
    public function up()
    {
        // <PERSON>gar helper de slugs
        helper('slug');
        
        // Obtener todos los productos que necesitan actualización de slug
        $products = $this->db->query("
            SELECT id, name, slug 
            FROM products 
            WHERE deleted_at IS NULL 
              AND (slug IS NULL OR slug = '' OR slug REGEXP '[áéíóúñüÁÉÍÓÚÑÜ]')
        ")->getResultArray();
        
        foreach ($products as $product) {
            // Generar nuevo slug usando el helper
            $newSlug = generate_unique_product_slug($product['name'], $product['id']);
            
            // Actualizar el producto
            $this->db->query("
                UPDATE products 
                SET slug = ?, updated_at = NOW() 
                WHERE id = ?
            ", [$newSlug, $product['id']]);
            
            echo "Producto ID {$product['id']}: '{$product['name']}' -> slug actualizado a '{$newSlug}'\n";
        }
        
        // Crear índice para mejorar rendimiento de búsquedas por slug
        try {
            $this->forge->addKey('slug');
            $this->forge->addKey('deleted_at');
            $this->forge->processIndexes('products');
        } catch (\Exception $e) {
            // Índice ya existe o error, continuar
            log_message('info', 'Índice de productos ya existe o error: ' . $e->getMessage());
        }
    }

    public function down()
    {
        // Remover índice si existe
        try {
            $this->forge->dropIndex('products', 'idx_products_slug');
        } catch (\Exception $e) {
            // Índice no existe o error, continuar
            log_message('info', 'Error eliminando índice: ' . $e->getMessage());
        }
    }
}
