<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use App\Models\ProductModel;
use CodeIgniter\Controller;

class UserController extends BaseController
{
    protected $userModel;
    protected $orderModel;
    protected $productModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->orderModel = new OrderModel();
        $this->productModel = new ProductModel();
        $this->session = session();
    }

    /**
     * Página de login
     */
    public function login()
    {
        // Si ya está logueado, redirigir al dashboard
        if ($this->session->get('user_id')) {
            return redirect()->to('/cuenta');
        }

        $data = [
            'title' => 'Iniciar Sesión - MrCell Guatemala',
            'page_title' => 'Iniciar Sesión'
        ];

        return view('frontend/auth/login', $data);
    }

    /**
     * Procesar login usando SP
     */
    public function authenticate()
    {
        $validation = \Config\Services::validation();

        $validation->setRules([
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        try {
            $db = \Config\Database::connect();

            // Usar stored procedure para login
            $query = $db->query("CALL sp_login_user(?, @user_id, @user_data, @result)", [$email]);
            $result = $db->query("SELECT @user_id as user_id, @user_data as user_data, @result as result")->getRow();

            if (strpos($result->result, 'SUCCESS') === 0 && $result->user_id > 0) {
                $userData = json_decode($result->user_data, true);

                // Verificar contraseña
                $user = $this->userModel->find($result->user_id);
                if ($user && password_verify($password, $user['password'])) {
                    // Login exitoso
                    $sessionData = [
                        'user_id' => $user['id'],
                        'user_name' => $user['name'],
                        'user_email' => $user['email'],
                        'is_logged_in' => true
                    ];

                    $this->session->set($sessionData);
                    return redirect()->to('/cuenta')->with('success', 'Bienvenido de vuelta, ' . $user['name']);
                }
            }

            return redirect()->back()->withInput()->with('error', 'Email o contraseña incorrectos');

        } catch (\Exception $e) {
            log_message('error', 'Error en login: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al iniciar sesión. Inténtalo de nuevo.');
        }
    }

    /**
     * User profile
     */
    public function profile()
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            return redirect()->to(base_url('login'))->with('error', 'Debes iniciar sesión para acceder a tu perfil');
        }

        $userId = session()->get('user_id');
        $userModel = new \App\Models\UserModel();

        // Handle POST request (profile update)
        if ($this->request->getMethod() === 'POST') {
            return $this->updateProfile();
        }

        // Get user data
        $user = $userModel->find($userId);

        if (!$user) {
            session()->destroy();
            return redirect()->to(base_url('login'))->with('error', 'Usuario no encontrado');
        }

        $data = [
            'title' => 'Mi Perfil - MrCell Guatemala',
            'description' => 'Edita tu información personal',
            'user' => $user,
            'contact' => [
                'address' => 'Guatemala, Guatemala',
                'phone' => '+502 9999-8888',
                'email' => '<EMAIL>'
            ]
        ];

        return view('frontend/user_profile', $data);
    }

    /**
     * User orders
     */
    public function orders()
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            return redirect()->to(base_url('login'))->with('error', 'Debes iniciar sesión');
        }

        $data = [
            'title' => 'Mis Pedidos - MrCell Guatemala',
            'description' => 'Historial de pedidos',
            'orders' => [] // TODO: Load user orders
        ];

        return view('frontend/user_orders', $data);
    }



    /**
     * Register page
     */
    public function register()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('logged_in')) {
            return redirect()->to(base_url('cuenta'));
        }

        $data = [
            'title' => 'Registrarse - MrCell Guatemala',
            'description' => 'Crea tu cuenta en MrCell'
        ];

        return view('frontend/register', $data);
    }

    /**
     * Logout
     */
    public function logout()
    {
        session()->destroy();
        return redirect()->to(base_url())->with('success', 'Has cerrado sesión exitosamente');
    }

    /**
     * Update profile
     */
    private function updateProfile()
    {
        $userId = session()->get('user_id');
        $userModel = new \App\Models\UserModel();

        $data = [
            'name' => $this->request->getPost('name'),
            'phone' => $this->request->getPost('phone'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($userModel->update($userId, $data)) {
            return redirect()->to(base_url('cuenta/perfil'))->with('success', 'Perfil actualizado exitosamente');
        } else {
            return redirect()->to(base_url('cuenta/perfil'))->with('error', 'Error al actualizar perfil');
        }
    }
}
