<?php

/**
 * Script para eliminar permanentemente productos que han sido eliminados (soft delete)
 * 
 * Este script elimina de la base de datos todos los productos que tienen 
 * deleted_at diferente a NULL, junto con todas sus dependencias.
 * 
 * ADVERTENCIA: Esta operación es IRREVERSIBLE
 * 
 * Uso: php purge_deleted_products.php [--dry-run] [--confirm]
 */

// Configuración de base de datos (desde app/Config/Database.php)
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// Conectar a la base de datos usando PDO
try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (PDOException $e) {
    echo "❌ Error de conexión a la base de datos: " . $e->getMessage() . "\n";
    exit(1);
}

// Parsear argumentos de línea de comandos
$options = getopt('', ['dry-run', 'confirm', 'help']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

$dryRun = isset($options['dry-run']);
$confirm = isset($options['confirm']);

echo "=== ELIMINACIÓN PERMANENTE DE PRODUCTOS ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "Base de datos: " . $dbConfig['database'] . "\n";
echo "Modo: " . ($dryRun ? 'DRY RUN (solo consulta)' : 'EJECUCIÓN REAL') . "\n";
echo "==========================================\n\n";

try {
    // Primero, mostrar qué productos serían eliminados
    echo "🔍 Consultando productos marcados para eliminación...\n";

    $stmt = $db->prepare("
        SELECT
            id,
            name,
            sku,
            deleted_at,
            DATEDIFF(NOW(), deleted_at) as days_deleted
        FROM products
        WHERE deleted_at IS NOT NULL
        ORDER BY deleted_at DESC
    ");

    $stmt->execute();
    $deletedProducts = $stmt->fetchAll();
    
    if (empty($deletedProducts)) {
        echo "✅ No hay productos marcados para eliminación.\n";
        exit(0);
    }
    
    echo "📋 Productos encontrados para eliminación permanente:\n";
    echo "ID\tSKU\t\tNombre\t\t\tEliminado hace (días)\n";
    echo "---\t---\t\t------\t\t\t--------------------\n";
    
    foreach ($deletedProducts as $product) {
        printf(
            "%d\t%s\t\t%s\t\t%d días\n",
            $product['id'],
            substr($product['sku'], 0, 15),
            substr($product['name'], 0, 30),
            $product['days_deleted']
        );
    }
    
    echo "\n📊 Total de productos a eliminar: " . count($deletedProducts) . "\n\n";
    
    if ($dryRun) {
        echo "🔍 Modo DRY RUN - No se realizarán cambios.\n";
        echo "Para ejecutar la eliminación real, use: php purge_deleted_products.php --confirm\n";
        exit(0);
    }
    
    if (!$confirm) {
        echo "⚠️  ADVERTENCIA: Esta operación es IRREVERSIBLE.\n";
        echo "Los productos y todas sus dependencias serán eliminados permanentemente.\n";
        echo "Para confirmar la eliminación, use: php purge_deleted_products.php --confirm\n";
        exit(1);
    }
    
    // Confirmación adicional en modo interactivo
    if (!isset($options['confirm'])) {
        echo "⚠️  ¿Está seguro de que desea eliminar permanentemente estos " . count($deletedProducts) . " productos? (sí/no): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'sí' && trim(strtolower($line)) !== 'si') {
            echo "❌ Operación cancelada.\n";
            exit(1);
        }
    }
    
    echo "🗑️  Iniciando eliminación permanente...\n";

    // Crear el stored procedure usando múltiples comandos
    try {
        // Primero eliminar el procedimiento si existe
        $db->exec("DROP PROCEDURE IF EXISTS sp_purge_deleted_products");

        // Crear el stored procedure sin DELIMITER
        $createProcedure = "
        CREATE PROCEDURE sp_purge_deleted_products(
            OUT p_result VARCHAR(200),
            OUT p_deleted_count INT
        )
        BEGIN
            DECLARE v_product_id INT DEFAULT 0;
            DECLARE v_done INT DEFAULT FALSE;
            DECLARE v_error_occurred INT DEFAULT FALSE;
            DECLARE v_error_message VARCHAR(500) DEFAULT '';

            DECLARE product_cursor CURSOR FOR
                SELECT id FROM products WHERE deleted_at IS NOT NULL;

            DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                ROLLBACK;
                SET v_error_occurred = TRUE;
                GET DIAGNOSTICS CONDITION 1
                    v_error_message = MESSAGE_TEXT;
                SET p_result = CONCAT('ERROR: ', v_error_message);
                SET p_deleted_count = 0;
            END;

            START TRANSACTION;

            SET p_deleted_count = 0;

            OPEN product_cursor;

            read_loop: LOOP
                FETCH product_cursor INTO v_product_id;

                IF v_done THEN
                    LEAVE read_loop;
                END IF;

                -- Eliminar dependencias que existen
                DELETE FROM product_variants WHERE product_id = v_product_id;

                -- Solo eliminar de tablas que existen
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'media_relations') THEN
                    DELETE FROM media_relations WHERE entity_type = 'product' AND entity_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'inventory_movements') THEN
                    DELETE FROM inventory_movements WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'inventory') THEN
                    DELETE FROM inventory WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'inventory_alerts') THEN
                    DELETE FROM inventory_alerts WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'wishlist') THEN
                    DELETE FROM wishlist WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'shared_wishlist_items') THEN
                    DELETE FROM shared_wishlist_items WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'cart_items') THEN
                    DELETE FROM cart_items WHERE product_id = v_product_id;
                END IF;

                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'price_history') THEN
                    DELETE FROM price_history WHERE product_id = v_product_id;
                END IF;

                UPDATE order_items
                SET product_name = CONCAT(product_name, ' [PRODUCTO ELIMINADO]')
                WHERE product_id = v_product_id
                AND product_name NOT LIKE '%[PRODUCTO ELIMINADO]%';

                DELETE FROM products WHERE id = v_product_id;

                SET p_deleted_count = p_deleted_count + 1;

            END LOOP;

            CLOSE product_cursor;

            IF v_error_occurred THEN
                ROLLBACK;
            ELSE
                COMMIT;
                SET p_result = CONCAT('SUCCESS: ', p_deleted_count, ' productos eliminados permanentemente');
            END IF;

        END";

        // Ejecutar creación del stored procedure
        $db->exec($createProcedure);
        echo "✅ Stored procedure creado exitosamente.\n";

    } catch (PDOException $e) {
        echo "❌ Error creando stored procedure: " . $e->getMessage() . "\n";
        exit(1);
    }

    // Ejecutar el stored procedure
    $db->exec("CALL sp_purge_deleted_products(@result, @deleted_count)");

    // Obtener resultados
    $stmt = $db->prepare("SELECT @result as result, @deleted_count as deleted_count");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result && strpos($result['result'], 'SUCCESS') === 0) {
        echo "✅ " . $result['result'] . "\n";

        // Log de la operación
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'purge_deleted_products',
            'products_deleted' => $result['deleted_count'],
            'status' => 'success'
        ];

        // Crear directorio de logs si no existe
        if (!is_dir('logs')) {
            mkdir('logs', 0755, true);
        }

        file_put_contents(
            'logs/purge_products_' . date('Y-m-d') . '.log',
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );

        echo "📝 Operación registrada en logs/purge_products_" . date('Y-m-d') . ".log\n";

    } else {
        echo "❌ Error: " . ($result ? $result['result'] : 'No se pudo obtener el resultado') . "\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ Error inesperado: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n🎉 Proceso completado exitosamente.\n";

function showHelp() {
    echo "Eliminación Permanente de Productos - MrCell Guatemala\n";
    echo "====================================================\n\n";
    echo "Este script elimina permanentemente productos que han sido marcados\n";
    echo "como eliminados (deleted_at IS NOT NULL) junto con sus dependencias.\n\n";
    echo "Uso:\n";
    echo "  php purge_deleted_products.php [opciones]\n\n";
    echo "Opciones:\n";
    echo "  --dry-run    Solo muestra qué productos serían eliminados (no ejecuta)\n";
    echo "  --confirm    Confirma la eliminación sin solicitar confirmación interactiva\n";
    echo "  --help       Muestra esta ayuda\n\n";
    echo "Ejemplos:\n";
    echo "  php purge_deleted_products.php --dry-run     # Ver qué se eliminaría\n";
    echo "  php purge_deleted_products.php --confirm     # Eliminar confirmando\n\n";
    echo "ADVERTENCIA: Esta operación es IRREVERSIBLE\n";
}
