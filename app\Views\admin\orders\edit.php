<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-edit me-2"></i>Editar Pedido</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/admin/orders">Pedidos</a></li>
                    <li class="breadcrumb-item active">Editar <?= esc($order['order_number']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/admin/orders/view/<?= $order['id'] ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-eye me-2"></i>Ver Detalle
            </a>
            <a href="/admin/orders" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver
            </a>
        </div>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <!-- Formulario de Edición -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Actualizar Estado del Pedido</h5>
            </div>
            <div class="card-body">
                <form action="/admin/orders/update/<?= $order['id'] ?>" method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Estado del Pedido</label>
                                <select class="form-select" id="status" name="status" required>
                                    <?php foreach ($statuses as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $order['status'] === $value ? 'selected' : '' ?>>
                                            <?= $label ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_status" class="form-label">Estado de Pago</label>
                                <select class="form-select" id="payment_status" name="payment_status">
                                    <option value="pending" <?= $order['payment_status'] === 'pending' ? 'selected' : '' ?>>Pendiente</option>
                                    <option value="paid" <?= $order['payment_status'] === 'paid' ? 'selected' : '' ?>>Pagado</option>
                                    <option value="refunded" <?= $order['payment_status'] === 'refunded' ? 'selected' : '' ?>>Reembolsado</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notas (Opcional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Agregar notas sobre la actualización..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Pedido
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Información del Pedido -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información del Pedido</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Número:</strong></td>
                        <td><?= esc($order['order_number']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Cliente:</strong></td>
                        <td><?= esc($order['customer_name']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Total:</strong></td>
                        <td><strong>Q<?= number_format($order['total'], 2) ?></strong></td>
                    </tr>
                    <tr>
                        <td><strong>Fecha:</strong></td>
                        <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Estado Actual:</strong></td>
                        <td>
                            <?php
                            $statusClass = 'bg-secondary';
                            $statusText = 'Desconocido';
                            
                            switch ($order['status']) {
                                case 'pending':
                                    $statusClass = 'bg-warning';
                                    $statusText = 'Pendiente';
                                    break;
                                case 'processing':
                                    $statusClass = 'bg-info';
                                    $statusText = 'Procesando';
                                    break;
                                case 'shipped':
                                    $statusClass = 'bg-primary';
                                    $statusText = 'Enviado';
                                    break;
                                case 'delivered':
                                    $statusClass = 'bg-success';
                                    $statusText = 'Entregado';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'bg-danger';
                                    $statusText = 'Cancelado';
                                    break;
                            }
                            ?>
                            <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Historial de Estados (si existe) -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Historial</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Pedido Creado</h6>
                            <small class="text-muted"><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></small>
                        </div>
                    </div>
                    <?php if ($order['updated_at']): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Última Actualización</h6>
                                <small class="text-muted"><?= date('d/m/Y H:i', strtotime($order['updated_at'])) ?></small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>

<?= $this->endSection() ?>
