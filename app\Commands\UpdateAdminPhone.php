<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class UpdateAdminPhone extends BaseCommand
{
    protected $group       = 'Admin';
    protected $name        = 'admin:update-phone';
    protected $description = 'Actualizar número de teléfono del administrador';

    public function run(array $params)
    {
        CLI::write('=== ACTUALIZANDO TELÉFONO DEL ADMINISTRADOR ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();

        try {
            // Actualizar el teléfono del administrador principal
            $adminEmail = '<EMAIL>';
            $newPhone = '+50230100452';

            CLI::write("📱 Actualizando teléfono para {$adminEmail}...", 'white');
            CLI::write("   Nuevo teléfono: {$newPhone}", 'white');

            $result = $db->table('users')
                        ->where('email', $adminEmail)
                        ->update([
                            'phone' => $newPhone,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

            if ($result) {
                CLI::write('   ✅ Teléfono actualizado exitosamente', 'green');
                
                // Verificar la actualización
                $user = $db->table('users')
                          ->where('email', $adminEmail)
                          ->get()
                          ->getRowArray();

                if ($user) {
                    CLI::newLine();
                    CLI::write('📋 Información actualizada:', 'cyan');
                    CLI::write("   Nombre: {$user['first_name']} {$user['last_name']}", 'white');
                    CLI::write("   Email: {$user['email']}", 'white');
                    CLI::write("   Teléfono: {$user['phone']}", 'white');
                    CLI::write("   Rol: {$user['role']}", 'white');
                    CLI::write("   Estado: {$user['status']}", 'white');
                }
            } else {
                CLI::write('   ❌ No se pudo actualizar el teléfono', 'red');
            }

        } catch (\Exception $e) {
            CLI::error('❌ Error actualizando teléfono: ' . $e->getMessage());
            return;
        }

        CLI::newLine();
        CLI::write('=== ACTUALIZACIÓN COMPLETADA ===', 'yellow');
    }
}
