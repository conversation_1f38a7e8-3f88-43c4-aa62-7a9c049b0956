<?php

namespace App\Controllers;

use App\Models\OrderModel;
use App\Models\PaymentModel;
use CodeIgniter\Controller;

class PaymentController extends BaseController
{
    protected $orderModel;
    protected $paymentModel;
    protected $session;

    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->paymentModel = new PaymentModel();
        $this->session = session();
    }

    /**
     * Procesar pago
     */
    public function process($orderId)
    {
        $order = $this->orderModel->find($orderId);
        
        if (!$order) {
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        if ($order['payment_status'] === 'paid') {
            return redirect()->to('/checkout/confirmation/' . $orderId)
                           ->with('info', 'Este pedido ya ha sido pagado');
        }

        $paymentMethod = $order['payment_method'];

        switch ($paymentMethod) {
            case 'transfer':
                return $this->processTransfer($order);
            case 'paypal':
                return $this->processPayPal($order);
            case 'card':
                return $this->processCard($order);
            case 'cash':
                return $this->processCash($order);
            case 'recurrente':
                return $this->processRecurrente($order);
            default:
                return redirect()->to('/checkout')->with('error', 'Método de pago no válido');
        }
    }

    /**
     * Procesar transferencia bancaria
     */
    private function processTransfer($order)
    {
        $bankAccounts = $this->getBankAccounts();
        
        $data = [
            'title' => 'Transferencia Bancaria - Pedido #' . $order['order_number'],
            'order' => $order,
            'bank_accounts' => $bankAccounts,
            'payment_instructions' => $this->getTransferInstructions()
        ];

        return view('frontend/payment/transfer', $data);
    }

    /**
     * Confirmar transferencia bancaria
     */
    public function confirmTransfer()
    {
        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'order_id' => 'required|integer',
            'bank_account' => 'required',
            'transfer_reference' => 'required|min_length[5]|max_length[50]',
            'transfer_amount' => 'required|decimal',
            'transfer_date' => 'required|valid_date'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $orderId = $this->request->getPost('order_id');
        $order = $this->orderModel->find($orderId);

        if (!$order) {
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        // Crear registro de pago
        $paymentData = [
            'order_id' => $orderId,
            'payment_method' => 'transfer',
            'amount' => $this->request->getPost('transfer_amount'),
            'currency' => 'GTQ',
            'status' => 'pending_verification',
            'reference_number' => $this->request->getPost('transfer_reference'),
            'bank_account' => $this->request->getPost('bank_account'),
            'payment_date' => $this->request->getPost('transfer_date'),
            'notes' => $this->request->getPost('notes'),
            'created_at' => date('Y-m-d H:i:s')
        ];

        if ($this->paymentModel->insert($paymentData)) {
            // Actualizar estado del pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'pending_verification',
                'notes' => 'Transferencia reportada - Pendiente de verificación'
            ]);

            return redirect()->to('/payment/success/' . $orderId)
                           ->with('success', 'Transferencia reportada correctamente. Verificaremos tu pago en las próximas 24 horas.');
        } else {
            return redirect()->back()->withInput()
                           ->with('error', 'Error al procesar la transferencia. Inténtalo de nuevo.');
        }
    }

    /**
     * Procesar pago con PayPal
     */
    private function processPayPal($order)
    {
        // Configuración de PayPal (sandbox para desarrollo)
        $paypalConfig = [
            'client_id' => env('PAYPAL_CLIENT_ID', 'sandbox_client_id'),
            'client_secret' => env('PAYPAL_CLIENT_SECRET', 'sandbox_client_secret'),
            'mode' => env('PAYPAL_MODE', 'sandbox'), // sandbox o live
            'currency' => 'USD' // PayPal requiere USD para Guatemala
        ];

        // Convertir GTQ a USD (tasa aproximada)
        $exchangeRate = 7.75; // 1 USD = 7.75 GTQ aproximadamente
        $amountUSD = round($order['total'] / $exchangeRate, 2);

        $data = [
            'title' => 'Pago con PayPal - Pedido #' . $order['order_number'],
            'order' => $order,
            'amount_usd' => $amountUSD,
            'exchange_rate' => $exchangeRate,
            'paypal_config' => $paypalConfig
        ];

        return view('frontend/payment/paypal', $data);
    }

    /**
     * Procesar pago con tarjeta
     */
    private function processCard($order)
    {
        $data = [
            'title' => 'Pago con Tarjeta - Pedido #' . $order['order_number'],
            'order' => $order,
            'card_types' => ['visa', 'mastercard', 'amex']
        ];

        return view('frontend/payment/card', $data);
    }

    /**
     * Procesar tarjeta de crédito/débito
     */
    public function processCardPayment()
    {
        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'order_id' => 'required|integer',
            'card_number' => 'required|min_length[16]|max_length[19]',
            'card_holder' => 'required|min_length[2]|max_length[100]',
            'expiry_month' => 'required|integer|greater_than[0]|less_than[13]',
            'expiry_year' => 'required|integer|greater_than[2023]',
            'cvv' => 'required|min_length[3]|max_length[4]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $orderId = $this->request->getPost('order_id');
        $order = $this->orderModel->find($orderId);

        if (!$order) {
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        // Simular procesamiento de tarjeta (en producción usar Stripe, Square, etc.)
        $cardNumber = $this->request->getPost('card_number');
        $lastFour = substr($cardNumber, -4);
        
        // Simular respuesta exitosa (90% de éxito)
        $success = rand(1, 10) <= 9;

        if ($success) {
            // Crear registro de pago exitoso
            $paymentData = [
                'order_id' => $orderId,
                'payment_method' => 'card',
                'amount' => $order['total'],
                'currency' => 'GTQ',
                'status' => 'completed',
                'reference_number' => 'CARD_' . time() . '_' . $lastFour,
                'card_last_four' => $lastFour,
                'card_type' => $this->detectCardType($cardNumber),
                'payment_date' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->paymentModel->insert($paymentData);

            // Actualizar estado del pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'notes' => 'Pago procesado con tarjeta terminada en ' . $lastFour
            ]);

            return redirect()->to('/payment/success/' . $orderId)
                           ->with('success', 'Pago procesado exitosamente con tarjeta terminada en ' . $lastFour);
        } else {
            // Simular error de pago
            return redirect()->back()->withInput()
                           ->with('error', 'Error al procesar el pago. Verifica los datos de tu tarjeta e inténtalo de nuevo.');
        }
    }

    /**
     * Procesar pago contra entrega
     */
    private function processCash($order)
    {
        // Actualizar estado del pedido para pago contra entrega
        $this->orderModel->update($order['id'], [
            'payment_status' => 'pending',
            'status' => 'confirmed',
            'notes' => 'Pago contra entrega - Se cobrará al momento de la entrega'
        ]);

        // Crear registro de pago
        $paymentData = [
            'order_id' => $order['id'],
            'payment_method' => 'cash',
            'amount' => $order['total'],
            'currency' => 'GTQ',
            'status' => 'pending',
            'reference_number' => 'CASH_' . $order['order_number'],
            'payment_date' => null,
            'notes' => 'Pago contra entrega',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->paymentModel->insert($paymentData);

        return redirect()->to('/payment/success/' . $order['id'])
                       ->with('success', 'Pedido confirmado. El pago se realizará contra entrega.');
    }

    /**
     * Página de éxito del pago
     */
    public function success($orderId)
    {
        $order = $this->orderModel->find($orderId);
        
        if (!$order) {
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        $payment = $this->paymentModel->where('order_id', $orderId)
                                     ->orderBy('created_at', 'DESC')
                                     ->first();

        $data = [
            'title' => 'Pago Exitoso - Pedido #' . $order['order_number'],
            'order' => $order,
            'payment' => $payment
        ];

        return view('frontend/payment/success', $data);
    }

    /**
     * Obtener cuentas bancarias
     */
    private function getBankAccounts()
    {
        return [
            'banrural' => [
                'name' => 'Banco de Desarrollo Rural (Banrural)',
                'account_number' => '**********',
                'account_type' => 'Cuenta Monetaria',
                'account_holder' => 'MrCell Guatemala S.A.'
            ],
            'bac' => [
                'name' => 'BAC Credomatic',
                'account_number' => '**********',
                'account_type' => 'Cuenta de Ahorro',
                'account_holder' => 'MrCell Guatemala S.A.'
            ],
            'industrial' => [
                'name' => 'Banco Industrial',
                'account_number' => '**********',
                'account_type' => 'Cuenta Empresarial',
                'account_holder' => 'MrCell Guatemala S.A.'
            ]
        ];
    }

    /**
     * Obtener instrucciones de transferencia
     */
    private function getTransferInstructions()
    {
        return [
            'Realiza la transferencia por el monto exacto del pedido',
            'Incluye el número de pedido en la referencia de la transferencia',
            'Guarda el comprobante de la transferencia',
            'Completa el formulario con los datos de la transferencia',
            'Verificaremos tu pago en un máximo de 24 horas',
            'Recibirás una confirmación por email una vez verificado el pago'
        ];
    }

    /**
     * Confirmar pago de PayPal
     */
    public function confirmPayPal()
    {
        $json = $this->request->getJSON(true);

        if (!$json || !isset($json['order_id']) || !isset($json['paypal_order_id'])) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Datos de PayPal incompletos'
            ]);
        }

        $orderId = $json['order_id'];
        $order = $this->orderModel->find($orderId);

        if (!$order) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Pedido no encontrado'
            ]);
        }

        // Crear registro de pago
        $paymentData = [
            'order_id' => $orderId,
            'payment_method' => 'paypal',
            'amount' => $json['amount_gtq'],
            'currency' => 'GTQ',
            'status' => 'completed',
            'reference_number' => $json['paypal_order_id'],
            'transaction_id' => $json['paypal_details']['id'] ?? null,
            'gateway_response' => json_encode($json['paypal_details']),
            'payment_date' => date('Y-m-d H:i:s'),
            'notes' => 'Pago procesado con PayPal - $' . number_format($json['amount_usd'], 2) . ' USD',
            'created_at' => date('Y-m-d H:i:s')
        ];

        if ($this->paymentModel->insert($paymentData)) {
            // Actualizar estado del pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'notes' => 'Pago completado con PayPal'
            ]);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pago procesado exitosamente'
            ]);
        } else {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error al registrar el pago'
            ]);
        }
    }

    /**
     * Simular pago de PayPal (para demostración)
     */
    public function simulatePayPal()
    {
        $json = $this->request->getJSON(true);

        if (!$json || !isset($json['order_id'])) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Datos incompletos'
            ]);
        }

        $orderId = $json['order_id'];
        $order = $this->orderModel->find($orderId);

        if (!$order) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Pedido no encontrado'
            ]);
        }

        // Simular éxito del 95%
        $success = rand(1, 100) <= 95;

        if ($success) {
            // Crear registro de pago simulado
            $paymentData = [
                'order_id' => $orderId,
                'payment_method' => 'paypal',
                'amount' => $json['amount_gtq'],
                'currency' => 'GTQ',
                'status' => 'completed',
                'reference_number' => 'DEMO_PP_' . time(),
                'transaction_id' => 'DEMO_' . uniqid(),
                'payment_date' => date('Y-m-d H:i:s'),
                'notes' => 'Pago simulado con PayPal - $' . number_format($json['amount_usd'], 2) . ' USD (DEMO)',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->paymentModel->insert($paymentData);

            // Actualizar estado del pedido
            $this->orderModel->update($orderId, [
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'notes' => 'Pago simulado completado con PayPal (DEMO)'
            ]);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pago simulado exitosamente'
            ]);
        } else {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error simulado en el procesamiento de PayPal'
            ]);
        }
    }

    /**
     * Procesar pago con Recurrente
     */
    private function processRecurrente($order)
    {
        // Cargar el servicio de Recurrente
        $recurrenteService = new \App\Services\RecurrenteService();

        if (!$recurrenteService->isEnabled()) {
            return redirect()->to('/checkout')
                           ->with('error', 'Recurrente no está disponible en este momento');
        }

        try {
            // Mostrar formulario de pago de Recurrente
            $data = [
                'title' => 'Pago con Recurrente - Pedido #' . $order['order_number'],
                'order' => $order,
                'recurrente_config' => $recurrenteService->getPublicConfig()
            ];

            return view('frontend/payment/recurrente', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error en Recurrente: ' . $e->getMessage());
            return redirect()->to('/checkout')
                           ->with('error', 'Error al procesar el pago con Recurrente: ' . $e->getMessage());
        }
    }

    /**
     * Manejar retorno exitoso de Recurrente
     */
    public function recurrenteSuccess($orderId)
    {
        // Buscar pedido por ID numérico o por external_id
        $order = null;

        if (is_numeric($orderId)) {
            $order = $this->orderModel->find($orderId);
        } else {
            // Buscar por external_id (ID temporal de Recurrente)
            $order = $this->orderModel->where('external_id', $orderId)->first();
        }

        if (!$order) {
            log_message('warning', "Pedido no encontrado para Recurrente success: {$orderId}");
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        // Verificar el estado del pago en Recurrente
        $recurrenteService = new \App\Services\RecurrenteService();
        $payment = $this->paymentModel->where('order_id', $orderId)
                                     ->where('payment_method', 'recurrente')
                                     ->orderBy('created_at', 'DESC')
                                     ->first();

        if ($payment && $payment['transaction_id']) {
            try {
                $checkoutData = $recurrenteService->getCheckout($payment['transaction_id']);

                if ($checkoutData['status'] === 'completed') {
                    // Actualizar pago como completado
                    $this->paymentModel->update($payment['id'], [
                        'status' => 'completed',
                        'gateway_response' => json_encode($checkoutData),
                        'notes' => 'Pago completado en Recurrente'
                    ]);

                    // Actualizar estado del pedido
                    $this->orderModel->update($orderId, [
                        'payment_status' => 'paid',
                        'status' => 'confirmed',
                        'notes' => 'Pago completado con Recurrente'
                    ]);

                    return redirect()->to('/payment/success/' . $orderId)
                                   ->with('success', 'Pago procesado exitosamente con Recurrente');
                }
            } catch (\Exception $e) {
                log_message('error', 'Error verificando pago Recurrente: ' . $e->getMessage());
            }
        }

        return redirect()->to('/checkout')
                       ->with('error', 'No se pudo verificar el estado del pago');
    }

    /**
     * Manejar cancelación de Recurrente
     */
    public function recurrenteCancel($orderId)
    {
        // Log detallado para debug
        log_message('info', "=== WEBHOOK RECURRENTE CANCEL INICIADO ===");
        log_message('info', "Order ID recibido: {$orderId}");
        log_message('info', "Método HTTP: " . $this->request->getMethod());
        log_message('info', "URL completa: " . current_url());
        log_message('info', "User Agent: " . $this->request->getUserAgent());

        // Buscar pedido por ID numérico o por external_id
        $order = null;

        if (is_numeric($orderId)) {
            log_message('info', "Buscando pedido por ID numérico: {$orderId}");
            $order = $this->orderModel->find($orderId);
        } else {
            log_message('info', "Buscando pedido por external_id: {$orderId}");
            // Buscar por external_id (ID temporal de Recurrente)
            $order = $this->orderModel->where('external_id', $orderId)->first();
        }

        if (!$order) {
            log_message('warning', "Pedido no encontrado para Recurrente cancel: {$orderId}");
            log_message('info', "Redirigiendo a home con error");
            return redirect()->to('/')->with('error', 'Pedido no encontrado');
        }

        log_message('info', "Pedido encontrado - ID: {$order['id']}, Status: {$order['status']}");

        // Actualizar estado del pedido
        $this->orderModel->update($order['id'], [
            'status' => 'cancelled',
            'payment_status' => 'cancelled'
        ]);

        // Actualizar pago como cancelado
        $payment = $this->paymentModel->where('order_id', $order['id'])
                                     ->where('payment_method', 'recurrente')
                                     ->orderBy('created_at', 'DESC')
                                     ->first();

        if ($payment) {
            $this->paymentModel->update($payment['id'], [
                'status' => 'cancelled',
                'notes' => 'Pago cancelado por el usuario en Recurrente'
            ]);
        }

        // Log de la cancelación
        log_message('info', "Pago Recurrente cancelado - Order ID: {$order['id']}, External ID: {$orderId}");
        log_message('info', "Redirigiendo a /checkout con mensaje de cancelación");
        log_message('info', "=== WEBHOOK RECURRENTE CANCEL COMPLETADO ===");

        return redirect()->to('/checkout')
                       ->with('warning', 'Pago cancelado. Puedes intentar con otro método de pago o <a href="/carrito/vaciar" class="alert-link">vaciar el carrito</a> para empezar de nuevo.')
                       ->with('cancelled_order_id', $order['id'])
                       ->with('show_clear_cart', true);
    }

    /**
     * Detectar tipo de tarjeta
     */
    private function detectCardType($cardNumber)
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber)) {
            return 'mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        } else {
            return 'unknown';
        }
    }


}
