<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\BlockchainManager;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Pagos con Criptomonedas
 * Manejo de pagos públicos con Bitcoin, Ethereum y otras criptomonedas
 */
class CryptoPaymentController extends BaseController
{
    private $blockchainManager;
    private $logger;
    
    public function __construct()
    {
        $this->blockchainManager = new BlockchainManager();
        $this->logger = new AdvancedLogger();
    }
    
    /**
     * Mostrar opciones de pago con criptomonedas
     */
    public function index($orderId = null)
    {
        if (!$orderId) {
            return redirect()->to('/');
        }
        
        // Verificar que la orden existe y pertenece al usuario
        $order = $this->getOrderForUser($orderId);
        
        if (!$order) {
            session()->setFlashdata('error', 'Orden no encontrada');
            return redirect()->to('/orders');
        }
        
        $data = [
            'title' => 'Pago con Criptomonedas - MrCell Guatemala',
            'order' => $order,
            'supported_currencies' => $this->blockchainManager->getConfig()['supported_currencies'],
            'crypto_prices' => $this->blockchainManager->getCryptoPrices(),
            'conversion_rates' => $this->calculateConversionRates($order['total'])
        ];
        
        return view('crypto_payment/index', $data);
    }
    
    /**
     * Iniciar pago con criptomoneda
     */
    public function initiate()
    {
        try {
            $orderId = $this->request->getPost('order_id');
            $currency = $this->request->getPost('currency');
            
            // Verificar orden
            $order = $this->getOrderForUser($orderId);
            if (!$order) {
                throw new \Exception('Orden no encontrada');
            }
            
            if ($order['payment_status'] === 'paid') {
                throw new \Exception('Esta orden ya ha sido pagada');
            }
            
            // Iniciar pago con blockchain
            $paymentData = [
                'order_id' => $orderId,
                'amount' => $order['total'],
                'currency' => $currency
            ];
            
            $result = $this->blockchainManager->processCryptoPayment($paymentData);
            
            if ($result['success']) {
                // Actualizar orden con información de pago
                $this->db->table('orders')
                        ->where('id', $orderId)
                        ->update([
                            'payment_method' => 'crypto_' . strtolower($currency),
                            'payment_status' => 'pending',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                
                $this->logger->info("Crypto payment initiated by user", [
                    'user_id' => session('user_id'),
                    'order_id' => $orderId,
                    'currency' => $currency,
                    'transaction_id' => $result['transaction_id']
                ]);
                
                return $this->response->setJSON([
                    'success' => true,
                    'redirect_url' => base_url("crypto-payment/pay/{$result['transaction_id']}")
                ]);
            }
            
            throw new \Exception($result['error'] ?? 'Error al iniciar pago');
            
        } catch (\Exception $e) {
            $this->logger->error("Crypto payment initiation error: " . $e->getMessage(), [
                'user_id' => session('user_id'),
                'post_data' => $this->request->getPost()
            ]);
            
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Página de pago con instrucciones
     */
    public function pay($transactionId = null)
    {
        if (!$transactionId) {
            return redirect()->to('/');
        }
        
        // Obtener información de la transacción
        $transaction = $this->getTransactionForUser($transactionId);
        
        if (!$transaction) {
            session()->setFlashdata('error', 'Transacción no encontrada');
            return redirect()->to('/orders');
        }
        
        // Verificar si ya expiró
        if (strtotime($transaction['expires_at']) < time()) {
            session()->setFlashdata('error', 'Esta transacción ha expirado');
            return redirect()->to("/crypto-payment/{$transaction['order_id']}");
        }
        
        $data = [
            'title' => 'Realizar Pago - MrCell Guatemala',
            'transaction' => $transaction,
            'order' => $this->getOrderById($transaction['order_id']),
            'time_remaining' => strtotime($transaction['expires_at']) - time()
        ];
        
        return view('crypto_payment/pay', $data);
    }
    
    /**
     * Verificar estado del pago
     */
    public function checkStatus($transactionId)
    {
        try {
            // Verificar que la transacción pertenece al usuario
            $transaction = $this->getTransactionForUser($transactionId);
            
            if (!$transaction) {
                throw new \Exception('Transacción no encontrada');
            }
            
            $result = $this->blockchainManager->checkTransactionStatus($transactionId);
            
            // Si el pago fue confirmado, otorgar tokens de fidelidad
            if ($result['success'] && $result['status'] === 'confirmed') {
                $order = $this->getOrderById($transaction['order_id']);
                
                if ($order && $order['payment_status'] !== 'paid') {
                    // Marcar orden como pagada
                    $this->db->table('orders')
                            ->where('id', $transaction['order_id'])
                            ->update([
                                'payment_status' => 'paid',
                                'status' => 'processing',
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                    
                    // Otorgar tokens de fidelidad
                    $this->blockchainManager->awardLoyaltyTokens(
                        session('user_id'),
                        $order['total'],
                        'crypto_purchase'
                    );
                    
                    $this->logger->info("Crypto payment confirmed", [
                        'user_id' => session('user_id'),
                        'order_id' => $transaction['order_id'],
                        'transaction_id' => $transactionId
                    ]);
                }
            }
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Página de éxito
     */
    public function success($transactionId = null)
    {
        if (!$transactionId) {
            return redirect()->to('/');
        }
        
        $transaction = $this->getTransactionForUser($transactionId);
        
        if (!$transaction || $transaction['status'] !== 'confirmed') {
            return redirect()->to("/crypto-payment/pay/$transactionId");
        }
        
        $data = [
            'title' => 'Pago Exitoso - MrCell Guatemala',
            'transaction' => $transaction,
            'order' => $this->getOrderById($transaction['order_id']),
            'tokens_awarded' => $this->getTokensAwarded($transaction['order_id'])
        ];
        
        return view('crypto_payment/success', $data);
    }
    
    /**
     * Obtener precios actuales
     */
    public function getPrices()
    {
        try {
            $prices = $this->blockchainManager->getCryptoPrices();
            
            return $this->response->setJSON($prices);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Calcular conversión a criptomoneda
     */
    public function calculateConversion()
    {
        try {
            $amount = (float)$this->request->getPost('amount');
            $currency = $this->request->getPost('currency');
            
            if ($amount <= 0) {
                throw new \Exception('Monto inválido');
            }
            
            $prices = $this->blockchainManager->getCryptoPrices();
            
            if (!$prices['success'] || !isset($prices['prices'][$currency])) {
                throw new \Exception('Precio no disponible para ' . $currency);
            }
            
            $cryptoAmount = round($amount / $prices['prices'][$currency], 8);
            
            return $this->response->setJSON([
                'success' => true,
                'fiat_amount' => $amount,
                'crypto_amount' => $cryptoAmount,
                'currency' => $currency,
                'rate' => $prices['prices'][$currency]
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Historial de transacciones del usuario
     */
    public function history()
    {
        $userId = session('user_id');
        
        if (!$userId) {
            return redirect()->to('/login');
        }
        
        $transactions = $this->db->table('crypto_transactions ct')
                                ->select('ct.*, o.total as order_total')
                                ->join('orders o', 'o.id = ct.order_id')
                                ->where('o.user_id', $userId)
                                ->orderBy('ct.created_at', 'DESC')
                                ->get()
                                ->getResultArray();
        
        $data = [
            'title' => 'Historial de Pagos Crypto - MrCell Guatemala',
            'transactions' => $transactions,
            'user_token_balance' => $this->getUserTokenBalance($userId)
        ];
        
        return view('crypto_payment/history', $data);
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getOrderForUser(int $orderId): ?array
    {
        $userId = session('user_id');
        
        if (!$userId) {
            return null;
        }
        
        return $this->db->table('orders')
                       ->where('id', $orderId)
                       ->where('user_id', $userId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getOrderById(int $orderId): ?array
    {
        return $this->db->table('orders')
                       ->where('id', $orderId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getTransactionForUser(string $transactionId): ?array
    {
        $userId = session('user_id');
        
        if (!$userId) {
            return null;
        }
        
        return $this->db->table('crypto_transactions ct')
                       ->select('ct.*')
                       ->join('orders o', 'o.id = ct.order_id')
                       ->where('ct.id', $transactionId)
                       ->where('o.user_id', $userId)
                       ->get()
                       ->getRowArray();
    }
    
    private function calculateConversionRates(float $amount): array
    {
        $prices = $this->blockchainManager->getCryptoPrices();
        
        if (!$prices['success']) {
            return [];
        }
        
        $rates = [];
        foreach ($prices['prices'] as $currency => $price) {
            $rates[$currency] = [
                'price_usd' => $price,
                'amount_needed' => round($amount / $price, 8)
            ];
        }
        
        return $rates;
    }
    
    private function getTokensAwarded(int $orderId): int
    {
        $result = $this->db->table('loyalty_token_transactions')
                          ->where('reason', 'crypto_purchase')
                          ->where('purchase_amount', '(SELECT total FROM orders WHERE id = ?)', false)
                          ->where('user_id', session('user_id'))
                          ->get()
                          ->getRowArray();
        
        return $result['amount'] ?? 0;
    }
    
    private function getUserTokenBalance(int $userId): int
    {
        $result = $this->db->table('user_token_balances')
                          ->where('user_id', $userId)
                          ->get()
                          ->getRowArray();
        
        return $result['balance'] ?? 0;
    }
}
