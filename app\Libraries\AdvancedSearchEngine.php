<?php

namespace App\Libraries;

use App\Libraries\SimpleCache;

/**
 * Motor de Búsqueda Avanzado para MrCell
 * Sistema inteligente de búsqueda con relevancia mejorada
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class AdvancedSearchEngine
{
    private $db;
    private $cache;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new SimpleCache();
        $this->config = [
            'min_search_length' => 2,
            'max_suggestions' => 15,
            'fuzzy_threshold' => 0.7,
            'enable_voice_search' => true,
            'enable_visual_search' => true,
            'boost_factors' => [
                'exact_match' => 10.0,
                'title_match' => 5.0,
                'brand_match' => 3.0,
                'category_match' => 2.0,
                'description_match' => 1.0,
                'featured_product' => 2.0,
                'high_rating' => 1.5,
                'in_stock' => 1.3,
                'recent_product' => 1.2,
                'popular_product' => 1.4
            ],
            'search_fields' => [
                'name' => 5.0,
                'brand' => 3.0,
                'category' => 2.0,
                'sku' => 4.0,
                'description' => 1.0,
                'short_description' => 2.0,
                'tags' => 2.5
            ]
        ];
    }
    
    /**
     * Búsqueda inteligente con algoritmo de relevancia avanzado
     * 
     * @param string $query Término de búsqueda
     * @param array $filters Filtros adicionales
     * @param array $options Opciones de búsqueda
     * @return array Resultados con relevancia calculada
     */
    public function intelligentSearch(string $query, array $filters = [], array $options = []): array
    {
        $startTime = microtime(true);
        
        // Configurar opciones por defecto
        $options = array_merge([
            'limit' => 20,
            'offset' => 0,
            'include_suggestions' => true,
            'include_analytics' => true,
            'boost_popular' => true,
            'enable_fuzzy' => true
        ], $options);
        
        // Limpiar y procesar query
        $processedQuery = $this->processSearchQuery($query);
        
        if (strlen($processedQuery['clean']) < $this->config['min_search_length']) {
            return [
                'products' => [],
                'total' => 0,
                'suggestions' => [],
                'analytics' => ['query_too_short' => true],
                'execution_time' => microtime(true) - $startTime
            ];
        }
        
        // Verificar cache
        $cacheKey = 'advanced_search_' . md5(serialize([$processedQuery, $filters, $options]));
        $cached = $this->cache->get($cacheKey);
        if ($cached) {
            $cached['cached'] = true;
            return $cached;
        }
        
        // Construir consulta base con relevancia
        $searchResults = $this->buildIntelligentQuery($processedQuery, $filters, $options);
        
        // Aplicar algoritmo de relevancia avanzado
        $rankedResults = $this->applyAdvancedRanking($searchResults, $processedQuery, $options);
        
        // Obtener sugerencias si se solicita
        $suggestions = [];
        if ($options['include_suggestions']) {
            $suggestions = $this->getIntelligentSuggestions($processedQuery['clean'], $filters);
        }
        
        // Analytics de búsqueda
        $analytics = [];
        if ($options['include_analytics']) {
            $analytics = $this->generateSearchAnalytics($processedQuery, $rankedResults, $filters);
        }
        
        // Registrar búsqueda para analytics
        $this->logSearch($query, count($rankedResults), $filters);
        
        $result = [
            'products' => array_slice($rankedResults, $options['offset'], $options['limit']),
            'total' => count($rankedResults),
            'suggestions' => $suggestions,
            'analytics' => $analytics,
            'query_info' => $processedQuery,
            'execution_time' => microtime(true) - $startTime,
            'cached' => false
        ];
        
        // Guardar en cache
        $this->cache->set($cacheKey, $result, 900); // 15 minutos
        
        return $result;
    }
    
    /**
     * Procesar query de búsqueda
     */
    private function processSearchQuery(string $query): array
    {
        $original = $query;
        $clean = trim(strtolower($query));
        
        // Remover caracteres especiales pero mantener espacios y guiones
        $clean = preg_replace('/[^\w\s\-áéíóúñü]/u', ' ', $clean);
        $clean = preg_replace('/\s+/', ' ', $clean);
        $clean = trim($clean);
        
        // Extraer palabras
        $words = explode(' ', $clean);
        $words = array_filter($words, function($word) {
            return strlen($word) >= 2;
        });
        
        // Detectar posibles marcas, categorías, etc.
        $detected = $this->detectSearchIntent($words);
        
        return [
            'original' => $original,
            'clean' => $clean,
            'words' => $words,
            'word_count' => count($words),
            'detected_brand' => $detected['brand'],
            'detected_category' => $detected['category'],
            'detected_intent' => $detected['intent']
        ];
    }
    
    /**
     * Detectar intención de búsqueda
     */
    private function detectSearchIntent(array $words): array
    {
        $intent = [
            'brand' => null,
            'category' => null,
            'intent' => 'product' // product, brand, category, comparison
        ];
        
        // Buscar marcas conocidas
        $brands = $this->getPopularBrands();
        foreach ($words as $word) {
            foreach ($brands as $brand) {
                if (stripos($brand['name'], $word) !== false || stripos($word, $brand['name']) !== false) {
                    $intent['brand'] = $brand;
                    break 2;
                }
            }
        }
        
        // Buscar categorías conocidas
        $categories = $this->getPopularCategories();
        foreach ($words as $word) {
            foreach ($categories as $category) {
                if (stripos($category['name'], $word) !== false || stripos($word, $category['name']) !== false) {
                    $intent['category'] = $category;
                    break 2;
                }
            }
        }
        
        // Detectar intención de comparación
        $comparisonWords = ['vs', 'versus', 'comparar', 'diferencia', 'mejor'];
        foreach ($words as $word) {
            if (in_array($word, $comparisonWords)) {
                $intent['intent'] = 'comparison';
                break;
            }
        }
        
        return $intent;
    }
    
    /**
     * Construir consulta inteligente
     */
    private function buildIntelligentQuery(array $processedQuery, array $filters, array $options): array
    {
        $builder = $this->db->table('products p');
        
        // Seleccionar campos con cálculo de relevancia
        $relevanceSQL = $this->buildRelevanceSQL($processedQuery);
        
        $builder->select("p.*, 
                         c.name as category_name, 
                         b.name as brand_name,
                         ({$relevanceSQL}) as relevance_score");
        
        $builder->join('categories c', 'p.category_id = c.id', 'left');
        $builder->join('brands b', 'p.brand_id = b.id', 'left');
        
        // Condiciones base
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        
        // Aplicar búsqueda FULLTEXT si hay términos
        if (!empty($processedQuery['clean'])) {
            $searchTerm = $this->prepareFullTextSearch($processedQuery['words']);
            $builder->where("MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE)", $searchTerm);
        }
        
        // Aplicar filtros
        $this->applySearchFilters($builder, $filters);
        
        // Aplicar boost por intención detectada
        if ($processedQuery['detected_brand']) {
            $builder->where('p.brand_id', $processedQuery['detected_brand']['id']);
        }
        
        if ($processedQuery['detected_category']) {
            $builder->where('p.category_id', $processedQuery['detected_category']['id']);
        }
        
        // Ordenar por relevancia
        $builder->orderBy('relevance_score', 'DESC');
        $builder->orderBy('p.is_featured', 'DESC');
        $builder->orderBy('p.rating_average', 'DESC');
        
        // Limitar resultados para procesamiento
        $builder->limit(500); // Procesaremos hasta 500 resultados
        
        $query = $builder->get();
        return $query->getResultArray();
    }
    
    /**
     * Construir SQL de relevancia
     */
    private function buildRelevanceSQL(array $processedQuery): string
    {
        if (empty($processedQuery['words'])) {
            return '1';
        }
        
        $relevanceParts = [];
        $boost = $this->config['boost_factors'];
        
        foreach ($processedQuery['words'] as $word) {
            $word = $this->db->escape($word);
            
            // Coincidencia exacta en nombre
            $relevanceParts[] = "CASE WHEN LOWER(p.name) LIKE LOWER('%{$word}%') THEN {$boost['title_match']} ELSE 0 END";
            
            // Coincidencia en marca
            $relevanceParts[] = "CASE WHEN LOWER(b.name) LIKE LOWER('%{$word}%') THEN {$boost['brand_match']} ELSE 0 END";
            
            // Coincidencia en categoría
            $relevanceParts[] = "CASE WHEN LOWER(c.name) LIKE LOWER('%{$word}%') THEN {$boost['category_match']} ELSE 0 END";
            
            // Coincidencia en SKU
            $relevanceParts[] = "CASE WHEN LOWER(p.sku) LIKE LOWER('%{$word}%') THEN {$boost['exact_match']} ELSE 0 END";
        }
        
        // Factores adicionales de boost
        $relevanceParts[] = "CASE WHEN p.is_featured = 1 THEN {$boost['featured_product']} ELSE 0 END";
        $relevanceParts[] = "CASE WHEN p.rating_average >= 4.0 THEN {$boost['high_rating']} ELSE 0 END";
        $relevanceParts[] = "CASE WHEN p.stock_quantity > 0 THEN {$boost['in_stock']} ELSE 0 END";
        $relevanceParts[] = "CASE WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN {$boost['recent_product']} ELSE 0 END";
        
        return '(' . implode(' + ', $relevanceParts) . ')';
    }
    
    /**
     * Aplicar ranking avanzado
     */
    private function applyAdvancedRanking(array $results, array $processedQuery, array $options): array
    {
        foreach ($results as &$product) {
            // Calcular score de popularidad
            $popularityScore = $this->calculatePopularityScore($product);
            
            // Calcular score de calidad
            $qualityScore = $this->calculateQualityScore($product);
            
            // Calcular score de disponibilidad
            $availabilityScore = $this->calculateAvailabilityScore($product);
            
            // Score final combinado
            $product['final_relevance'] = 
                ($product['relevance_score'] * 0.4) +
                ($popularityScore * 0.3) +
                ($qualityScore * 0.2) +
                ($availabilityScore * 0.1);
            
            // Información adicional para debugging
            $product['scoring_details'] = [
                'base_relevance' => $product['relevance_score'],
                'popularity' => $popularityScore,
                'quality' => $qualityScore,
                'availability' => $availabilityScore,
                'final' => $product['final_relevance']
            ];
        }
        
        // Ordenar por score final
        usort($results, function($a, $b) {
            return $b['final_relevance'] <=> $a['final_relevance'];
        });
        
        return $results;
    }
    
    /**
     * Calcular score de popularidad
     */
    private function calculatePopularityScore(array $product): float
    {
        $score = 0;
        
        // Rating promedio (0-5) normalizado a 0-10
        $score += ($product['rating_average'] ?? 0) * 2;
        
        // Número de reviews (logarítmico)
        $reviewCount = $product['rating_count'] ?? 0;
        if ($reviewCount > 0) {
            $score += min(log10($reviewCount + 1) * 2, 10);
        }
        
        // Producto destacado
        if ($product['is_featured']) {
            $score += 5;
        }
        
        return min($score, 100); // Máximo 100
    }
    
    /**
     * Calcular score de calidad
     */
    private function calculateQualityScore(array $product): float
    {
        $score = 0;
        
        // Completitud de información
        $fields = ['name', 'description', 'short_description', 'featured_image'];
        $completedFields = 0;
        
        foreach ($fields as $field) {
            if (!empty($product[$field])) {
                $completedFields++;
            }
        }
        
        $score += ($completedFields / count($fields)) * 30;
        
        // Calidad de descripción
        $descLength = strlen($product['description'] ?? '');
        if ($descLength > 100) {
            $score += min($descLength / 50, 20);
        }
        
        // Tiene imagen
        if (!empty($product['featured_image'])) {
            $score += 10;
        }
        
        return min($score, 100);
    }
    
    /**
     * Calcular score de disponibilidad
     */
    private function calculateAvailabilityScore(array $product): float
    {
        $score = 0;
        
        // En stock
        if ($product['stock_quantity'] > 0 && $product['stock_status'] === 'in_stock') {
            $score += 50;
            
            // Más stock = mejor score
            $stockLevel = min($product['stock_quantity'] / 10, 5);
            $score += $stockLevel * 10;
        }
        
        // Precio competitivo (si hay precio de oferta)
        if (!empty($product['price_sale']) && $product['price_sale'] < $product['price_regular']) {
            $discount = (($product['price_regular'] - $product['price_sale']) / $product['price_regular']) * 100;
            $score += min($discount, 30);
        }
        
        return min($score, 100);
    }
    
    /**
     * Obtener sugerencias inteligentes
     */
    private function getIntelligentSuggestions(string $query, array $filters = []): array
    {
        // Sugerencias basadas en búsquedas populares
        $popularSuggestions = $this->getPopularSearchSuggestions($query);
        
        // Sugerencias basadas en productos similares
        $productSuggestions = $this->getProductBasedSuggestions($query);
        
        // Sugerencias de corrección ortográfica
        $correctionSuggestions = $this->getSpellingSuggestions($query);
        
        // Combinar y rankear sugerencias
        $allSuggestions = array_merge($popularSuggestions, $productSuggestions, $correctionSuggestions);
        
        // Eliminar duplicados y limitar
        $uniqueSuggestions = array_unique($allSuggestions, SORT_REGULAR);
        
        return array_slice($uniqueSuggestions, 0, $this->config['max_suggestions']);
    }
    
    /**
     * Preparar búsqueda FULLTEXT
     */
    private function prepareFullTextSearch(array $words): string
    {
        $searchTerms = [];
        
        foreach ($words as $word) {
            if (strlen($word) >= 2) {
                // Búsqueda exacta con wildcard
                $searchTerms[] = "+{$word}*";
            }
        }
        
        return implode(' ', $searchTerms);
    }
    
    /**
     * Aplicar filtros de búsqueda
     */
    private function applySearchFilters($builder, array $filters): void
    {
        if (!empty($filters['category_id'])) {
            $builder->where('p.category_id', $filters['category_id']);
        }
        
        if (!empty($filters['brand_id'])) {
            $builder->where('p.brand_id', $filters['brand_id']);
        }
        
        if (!empty($filters['min_price'])) {
            $builder->where('COALESCE(p.price_sale, p.price_regular) >=', $filters['min_price']);
        }
        
        if (!empty($filters['max_price'])) {
            $builder->where('COALESCE(p.price_sale, p.price_regular) <=', $filters['max_price']);
        }
        
        if (!empty($filters['in_stock'])) {
            $builder->where('p.stock_quantity >', 0);
            $builder->where('p.stock_status', 'in_stock');
        }
        
        if (!empty($filters['featured'])) {
            $builder->where('p.is_featured', 1);
        }
        
        if (!empty($filters['min_rating'])) {
            $builder->where('p.rating_average >=', $filters['min_rating']);
        }
        
        if (!empty($filters['on_sale'])) {
            $builder->where('p.price_sale IS NOT NULL');
            $builder->where('p.price_sale <', 'p.price_regular', false);
        }
    }
    
    /**
     * Obtener marcas populares
     */
    private function getPopularBrands(): array
    {
        return $this->cache->remember('popular_brands', function() {
            $query = $this->db->query("
                SELECT b.id, b.name, COUNT(p.id) as product_count
                FROM brands b
                INNER JOIN products p ON b.id = p.brand_id
                WHERE p.is_active = 1 AND p.deleted_at IS NULL
                GROUP BY b.id, b.name
                ORDER BY product_count DESC
                LIMIT 20
            ");
            return $query->getResultArray();
        }, 3600);
    }
    
    /**
     * Obtener categorías populares
     */
    private function getPopularCategories(): array
    {
        return $this->cache->remember('popular_categories', function() {
            $query = $this->db->query("
                SELECT c.id, c.name, COUNT(p.id) as product_count
                FROM categories c
                INNER JOIN products p ON c.id = p.category_id
                WHERE p.is_active = 1 AND p.deleted_at IS NULL
                GROUP BY c.id, c.name
                ORDER BY product_count DESC
                LIMIT 20
            ");
            return $query->getResultArray();
        }, 3600);
    }
    
    /**
     * Registrar búsqueda para analytics
     */
    private function logSearch(string $query, int $resultCount, array $filters): void
    {
        try {
            $this->db->table('search_logs')->insert([
                'query' => $query,
                'result_count' => $resultCount,
                'filters' => json_encode($filters),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Ignorar errores de logging
            log_message('error', 'Error logging search: ' . $e->getMessage());
        }
    }
    
    /**
     * Generar analytics de búsqueda
     */
    private function generateSearchAnalytics(array $processedQuery, array $results, array $filters): array
    {
        return [
            'query_analysis' => [
                'word_count' => $processedQuery['word_count'],
                'detected_brand' => $processedQuery['detected_brand']['name'] ?? null,
                'detected_category' => $processedQuery['detected_category']['name'] ?? null,
                'search_intent' => $processedQuery['detected_intent']
            ],
            'result_analysis' => [
                'total_found' => count($results),
                'avg_relevance' => count($results) > 0 ? array_sum(array_column($results, 'final_relevance')) / count($results) : 0,
                'in_stock_count' => count(array_filter($results, function($p) { return $p['stock_quantity'] > 0; })),
                'featured_count' => count(array_filter($results, function($p) { return $p['is_featured']; })),
                'avg_rating' => count($results) > 0 ? array_sum(array_column($results, 'rating_average')) / count($results) : 0
            ],
            'filters_applied' => array_keys(array_filter($filters))
        ];
    }
    
    // Métodos adicionales para sugerencias
    private function getPopularSearchSuggestions(string $query): array
    {
        try {
            $builder = $this->db->table('search_logs');
            $builder->select('query, COUNT(*) as search_count');
            $builder->where('query LIKE', "%{$query}%");
            $builder->where('result_count >', 0);
            $builder->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')));
            $builder->groupBy('query');
            $builder->orderBy('search_count', 'DESC');
            $builder->limit(5);

            $results = $builder->get()->getResultArray();

            return array_map(function($item) {
                return [
                    'query' => $item['query'],
                    'type' => 'popular',
                    'count' => $item['search_count']
                ];
            }, $results);

        } catch (\Exception $e) {
            return [];
        }
    }

    private function getProductBasedSuggestions(string $query): array
    {
        try {
            $builder = $this->db->table('products p');
            $builder->select('p.name, p.slug, COUNT(*) as relevance');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');
            $builder->where("p.name LIKE", "%{$query}%");
            $builder->groupBy('p.id, p.name, p.slug');
            $builder->orderBy('relevance', 'DESC');
            $builder->limit(3);

            $results = $builder->get()->getResultArray();

            return array_map(function($item) {
                return [
                    'query' => $item['name'],
                    'type' => 'product',
                    'slug' => $item['slug']
                ];
            }, $results);

        } catch (\Exception $e) {
            return [];
        }
    }

    private function getSpellingSuggestions(string $query): array
    {
        // Correcciones ortográficas comunes
        $corrections = [
            'iphone' => 'iPhone',
            'samsung' => 'Samsung',
            'huawei' => 'Huawei',
            'xiaomi' => 'Xiaomi',
            'audifonos' => 'audífonos',
            'bluetooth' => 'Bluetooth',
            'wifi' => 'WiFi',
            'usb' => 'USB'
        ];

        $suggestions = [];
        $queryLower = strtolower($query);

        foreach ($corrections as $wrong => $correct) {
            if (stripos($queryLower, $wrong) !== false) {
                $corrected = str_ireplace($wrong, $correct, $query);
                if ($corrected !== $query) {
                    $suggestions[] = [
                        'query' => $corrected,
                        'type' => 'spelling',
                        'original' => $query
                    ];
                }
            }
        }

        return $suggestions;
    }
}
