<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class DiagnoseWhatsApp extends BaseCommand
{
    protected $group       = 'WhatsApp';
    protected $name        = 'whatsapp:diagnose';
    protected $description = 'Diagnosticar y arreglar el sistema de notificaciones de WhatsApp';

    public function run(array $params)
    {
        CLI::write('=== DIAGNÓSTICO DEL SISTEMA DE WHATSAPP ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();
        $issues = [];
        $fixes = [];

        // 1. Verificar configuración de WhatsApp en la base de datos
        CLI::write('1. Verificando configuración de WhatsApp...', 'white');
        try {
            $settings = $db->table('whatsapp_settings')->get()->getResultArray();
            $settingsMap = [];
            foreach ($settings as $setting) {
                $settingsMap[$setting['setting_key']] = $setting['setting_value'];
            }

            if (empty($settings)) {
                $issues[] = 'No hay configuraciones de WhatsApp en la base de datos';
            } else {
                CLI::write('   ✅ Configuraciones encontradas:', 'green');
                foreach ($settingsMap as $key => $value) {
                    $displayValue = ($key === 'api_key') ? substr($value, 0, 10) . '...' : $value;
                    CLI::write("      - {$key}: {$displayValue}", 'white');
                }

                // Verificar si está habilitado
                if (isset($settingsMap['enabled']) && $settingsMap['enabled'] == '1') {
                    CLI::write('   ✅ WhatsApp está habilitado', 'green');
                } else {
                    $issues[] = 'WhatsApp está deshabilitado en la configuración';
                }
            }
        } catch (\Exception $e) {
            $issues[] = 'Error accediendo a whatsapp_settings: ' . $e->getMessage();
        }

        CLI::newLine();

        // 2. Verificar plantillas de WhatsApp
        CLI::write('2. Verificando plantillas de WhatsApp...', 'white');
        try {
            $templates = $db->table('whatsapp_templates')
                           ->where('is_active', 1)
                           ->get()
                           ->getResultArray();

            if (empty($templates)) {
                $issues[] = 'No hay plantillas de WhatsApp activas';
            } else {
                CLI::write('   ✅ Plantillas activas encontradas:', 'green');
                foreach ($templates as $template) {
                    CLI::write("      - {$template['template_key']}: {$template['template_name']}", 'white');
                }

                // Verificar plantillas críticas
                $criticalTemplates = ['customer_registration', 'order_created'];
                foreach ($criticalTemplates as $key) {
                    $found = false;
                    foreach ($templates as $template) {
                        if ($template['template_key'] === $key) {
                            $found = true;
                            break;
                        }
                    }
                    if (!$found) {
                        $issues[] = "Plantilla crítica faltante: {$key}";
                    }
                }
            }
        } catch (\Exception $e) {
            $issues[] = 'Error accediendo a whatsapp_templates: ' . $e->getMessage();
        }

        CLI::newLine();

        // 3. Verificar configuración en .env
        CLI::write('3. Verificando configuración en .env...', 'white');
        $envVars = [
            'WHATSAPP_NOTIFICATIONS_ENABLED',
            'WHATSAPP_API_URL',
            'WHATSAPP_API_KEY',
            'WHATSAPP_DEVICE_TOKEN'
        ];

        foreach ($envVars as $var) {
            $value = env($var);
            if (empty($value)) {
                $issues[] = "Variable de entorno faltante: {$var}";
            } else {
                $displayValue = (strpos($var, 'KEY') !== false || strpos($var, 'TOKEN') !== false) 
                    ? substr($value, 0, 10) . '...' 
                    : $value;
                CLI::write("   ✅ {$var}: {$displayValue}", 'green');
            }
        }

        CLI::newLine();

        // 4. Probar conexión con la API de WhatsApp
        CLI::write('4. Probando conexión con API de WhatsApp...', 'white');
        try {
            $whatsappService = new \App\Services\WhatsAppService();
            
            // Verificar si el servicio puede inicializarse
            CLI::write('   ✅ Servicio de WhatsApp inicializado correctamente', 'green');
            
        } catch (\Exception $e) {
            $issues[] = 'Error inicializando servicio de WhatsApp: ' . $e->getMessage();
        }

        CLI::newLine();

        // 5. Verificar eventos registrados
        CLI::write('5. Verificando eventos de WhatsApp...', 'white');
        try {
            // Verificar si la clase existe
            if (class_exists('\App\Libraries\WhatsAppEventHandler')) {
                CLI::write('   ✅ WhatsAppEventHandler existe', 'green');
            } else {
                $issues[] = 'Clase WhatsAppEventHandler no encontrada';
            }
        } catch (\Exception $e) {
            $issues[] = 'Error verificando WhatsAppEventHandler: ' . $e->getMessage();
        }

        CLI::newLine();

        // Mostrar resumen
        if (empty($issues)) {
            CLI::write('🎉 ¡SISTEMA DE WHATSAPP FUNCIONANDO CORRECTAMENTE!', 'green');
        } else {
            CLI::write('❌ PROBLEMAS ENCONTRADOS:', 'red');
            foreach ($issues as $issue) {
                CLI::write("   - {$issue}", 'red');
            }

            CLI::newLine();
            CLI::write('🔧 APLICANDO CORRECCIONES...', 'yellow');
            
            // Aplicar correcciones automáticas
            $this->applyFixes($db, $issues);
        }

        CLI::newLine();
        CLI::write('=== DIAGNÓSTICO COMPLETADO ===', 'yellow');
    }

    private function applyFixes($db, $issues)
    {
        foreach ($issues as $issue) {
            if (strpos($issue, 'WhatsApp está deshabilitado') !== false) {
                CLI::write('   🔧 Habilitando WhatsApp...', 'white');
                $db->table('whatsapp_settings')
                   ->where('setting_key', 'enabled')
                   ->update(['setting_value' => '1']);
                CLI::write('   ✅ WhatsApp habilitado', 'green');
            }

            if (strpos($issue, 'Variable de entorno faltante') !== false) {
                CLI::write('   ⚠️ Necesitas configurar las variables de entorno en .env', 'yellow');
                CLI::write('   Agrega estas líneas a tu archivo .env:', 'white');
                CLI::write('   WHATSAPP_NOTIFICATIONS_ENABLED=true', 'cyan');
                CLI::write('   WHATSAPP_API_URL=http://167.114.111.52/api/sendMessage', 'cyan');
                CLI::write('   WHATSAPP_API_KEY=tu_api_key_aqui', 'cyan');
                CLI::write('   WHATSAPP_DEVICE_TOKEN=tu_device_token_aqui', 'cyan');
            }
        }
    }
}
