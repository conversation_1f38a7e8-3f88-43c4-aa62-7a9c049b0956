<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class DatabaseUpdate extends Controller
{
    public function addShippingFields()
    {
        $db = \Config\Database::connect();
        
        try {
            // Verificar si los campos ya existen
            $query = $db->query("SHOW COLUMNS FROM products LIKE 'shipping_available'");
            if ($query->getNumRows() > 0) {
                echo "Los campos de envío ya existen en la tabla products.\n";
                return;
            }
            
            // Leer y ejecutar el archivo SQL
            $sql = file_get_contents(ROOTPATH . 'add_shipping_fields.sql');
            
            if ($sql === false) {
                echo "Error: No se pudo leer el archivo SQL.\n";
                return;
            }
            
            $db->query($sql);
            
            echo "Campos de envío agregados exitosamente a la tabla products.\n";
            
            // Verificar que se agregaron
            $query = $db->query("SHOW COLUMNS FROM products LIKE '%shipping%'");
            $results = $query->getResultArray();
            
            echo "Campos agregados:\n";
            foreach($results as $row) {
                echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
            }
            
        } catch (\Exception $e) {
            echo "Error al agregar campos: " . $e->getMessage() . "\n";
        }
    }

    public function updateProductShippingData()
    {
        $db = \Config\Database::connect();

        try {
            echo "Actualizando datos de envío para productos de ejemplo...\n\n";

            // Productos grandes/pesados - Solo retiro en tienda
            $heavyProducts = [
                ['name' => 'FORD EDGE TITANIUM FWD', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'TESLA MODEL 3 STANDARD RANGE', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'MERCEDES-BENZ GLC 250', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'MERCEDES-BENZ C 200', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'CHEVROLET SILVERADO 1500 Z71 RST 4X4', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'BMW 235i 2014 KIT M', 'category' => 'heavy', 'pickup_only' => 1],
                ['name' => 'Mini LIMOUSINE EDICION TARTAN', 'category' => 'heavy', 'pickup_only' => 1]
            ];

            foreach ($heavyProducts as $product) {
                $db->query("
                    UPDATE products
                    SET shipping_available = 0,
                        pickup_only = 1,
                        shipping_category = 'heavy',
                        shipping_restrictions = '{\"reason\": \"Producto de gran tamaño, solo disponible para retiro en tienda\"}'
                    WHERE name LIKE ?
                ", ['%' . $product['name'] . '%']);

                echo "✓ Actualizado: {$product['name']} - Solo retiro en tienda\n";
            }

            // Productos pequeños - Envío disponible
            $smallProducts = [
                ['name' => 'iPhone 15 Pro Max', 'weight' => 0.221, 'length' => 16.0, 'width' => 7.7, 'height' => 0.83],
                ['name' => 'Samsung Galaxy S24 Ultra', 'weight' => 0.232, 'length' => 16.3, 'width' => 7.9, 'height' => 0.86],
                ['name' => 'Xiaomi 14 256GB', 'weight' => 0.193, 'length' => 15.2, 'width' => 7.1, 'height' => 0.81],
                ['name' => 'Funda Apple iPhone', 'weight' => 0.045, 'length' => 17.0, 'width' => 8.5, 'height' => 1.2],
                ['name' => 'Cargador Samsung', 'weight' => 0.120, 'length' => 10.0, 'width' => 5.0, 'height' => 3.0],
                ['name' => 'AirPods Pro 2da', 'weight' => 0.051, 'length' => 6.1, 'width' => 4.5, 'height' => 2.1],
                ['name' => 'Xiaomi Buds 4 Pro', 'weight' => 0.052, 'length' => 6.2, 'width' => 4.8, 'height' => 2.5]
            ];

            foreach ($smallProducts as $product) {
                $db->query("
                    UPDATE products
                    SET shipping_available = 1,
                        pickup_only = 0,
                        shipping_category = 'small',
                        shipping_weight_kg = ?,
                        shipping_length_cm = ?,
                        shipping_width_cm = ?,
                        shipping_height_cm = ?
                    WHERE name LIKE ?
                ", [
                    $product['weight'],
                    $product['length'],
                    $product['width'],
                    $product['height'],
                    '%' . $product['name'] . '%'
                ]);

                echo "✓ Actualizado: {$product['name']} - Envío disponible\n";
            }

            echo "\n✅ Actualización completada exitosamente!\n";

        } catch (\Exception $e) {
            echo "Error al actualizar datos: " . $e->getMessage() . "\n";
        }
    }

    public function createInstallmentTables()
    {
        $db = \Config\Database::connect();

        try {
            echo "Creando tablas para pagos a plazos...\n\n";

            // Crear tabla de planes de cuotas
            $db->query("
                CREATE TABLE IF NOT EXISTS installment_plans (
                    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL COMMENT 'Nombre del plan',
                    installments TINYINT(2) UNSIGNED NOT NULL COMMENT 'Número de cuotas',
                    interest_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT 'Tasa de interés mensual',
                    processing_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Comisión por procesamiento',
                    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Monto mínimo',
                    max_amount DECIMAL(10,2) NULL COMMENT 'Monto máximo',
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");

            // Insertar planes de cuotas predefinidos
            $plans = [
                ['name' => '3 Cuotas sin interés', 'installments' => 3, 'interest_rate' => 0.0000, 'processing_fee' => 25.00, 'min_amount' => 500.00],
                ['name' => '6 Cuotas - 2% mensual', 'installments' => 6, 'interest_rate' => 0.0200, 'processing_fee' => 50.00, 'min_amount' => 1000.00],
                ['name' => '9 Cuotas - 2.5% mensual', 'installments' => 9, 'interest_rate' => 0.0250, 'processing_fee' => 75.00, 'min_amount' => 2000.00],
                ['name' => '12 Cuotas - 3% mensual', 'installments' => 12, 'interest_rate' => 0.0300, 'processing_fee' => 100.00, 'min_amount' => 3000.00]
            ];

            foreach ($plans as $plan) {
                $db->query("
                    INSERT IGNORE INTO installment_plans (name, installments, interest_rate, processing_fee, min_amount)
                    VALUES (?, ?, ?, ?, ?)
                ", [$plan['name'], $plan['installments'], $plan['interest_rate'], $plan['processing_fee'], $plan['min_amount']]);

                echo "✓ Plan creado: {$plan['name']}\n";
            }

            echo "\n✅ Tablas y planes de cuotas creados exitosamente!\n";
            echo "\nPlanes disponibles:\n";
            echo "- 3 cuotas sin interés (mínimo Q500)\n";
            echo "- 6 cuotas con 2% mensual (mínimo Q1,000)\n";
            echo "- 9 cuotas con 2.5% mensual (mínimo Q2,000)\n";
            echo "- 12 cuotas con 3% mensual (mínimo Q3,000)\n";

        } catch (\Exception $e) {
            echo "Error al crear tablas: " . $e->getMessage() . "\n";
        }
    }

    public function createSystemSettingsTable()
    {
        $db = \Config\Database::connect();

        try {
            echo "Creando tabla de configuraciones del sistema...\n\n";

            // Verificar si la tabla existe
            $tableExists = $db->query("SHOW TABLES LIKE 'system_settings'")->getNumRows() > 0;

            if (!$tableExists) {
                // Crear tabla de configuraciones
                $db->query("
                    CREATE TABLE system_settings (
                        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) NOT NULL UNIQUE,
                        setting_value TEXT NULL,
                        setting_type ENUM('string', 'integer', 'decimal', 'boolean', 'json') DEFAULT 'string',
                        description VARCHAR(255) NULL,
                        category VARCHAR(50) DEFAULT 'general',
                        is_editable TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
                echo "✓ Tabla system_settings creada\n";
            } else {
                echo "✓ Tabla system_settings ya existe\n";

                // Verificar estructura de la tabla
                $columns = $db->query("DESCRIBE system_settings")->getResultArray();
                echo "Columnas existentes:\n";
                foreach ($columns as $col) {
                    echo "- " . $col['Field'] . " (" . $col['Type'] . ")\n";
                }
            }

            // Insertar configuraciones por defecto usando la estructura existente
            $settings = [
                ['tax_enabled', '0', 'taxes', 'Activar/Desactivar IVA', 'Activar/desactivar cálculo de impuestos', 'checkbox'],
                ['tax_rate', '12.00', 'taxes', 'Porcentaje de IVA', 'Porcentaje de impuesto (ej: 12 para 12%)', 'number'],
                ['tax_name', 'IVA', 'taxes', 'Nombre del Impuesto', 'Nombre del impuesto (IVA, IGV, etc.)', 'text'],
                ['tax_included_in_price', '0', 'taxes', 'IVA Incluido en Precios', 'Los precios incluyen impuestos (1) o se agregan al final (0)', 'checkbox']
            ];

            foreach ($settings as $setting) {
                // Verificar si ya existe
                $exists = $db->query("SELECT id FROM system_settings WHERE setting_key = ?", [$setting[0]])->getNumRows() > 0;

                if (!$exists) {
                    $db->query("
                        INSERT INTO system_settings (setting_key, setting_value, setting_group, display_name, description, setting_type, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, 1)
                    ", $setting);

                    echo "✓ Configuración creada: {$setting[0]} = {$setting[1]}\n";
                } else {
                    echo "- Configuración ya existe: {$setting[0]}\n";
                }
            }

            echo "\n✅ Tabla de configuraciones creada exitosamente!\n";
            echo "\nConfiguraciones por defecto:\n";
            echo "- IVA: DESACTIVADO por defecto\n";
            echo "- Tasa de IVA: 12%\n";
            echo "- Nombre del impuesto: IVA\n";
            echo "- Impuestos no incluidos en precios\n";

        } catch (\Exception $e) {
            echo "Error al crear tabla de configuraciones: " . $e->getMessage() . "\n";
        }
    }

    public function toggleTax()
    {
        $db = \Config\Database::connect();

        try {
            // Obtener estado actual
            $current = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'tax_enabled'")->getRow();

            if ($current) {
                $newValue = $current->setting_value == '1' ? '0' : '1';
                $status = $newValue == '1' ? 'ACTIVADO' : 'DESACTIVADO';

                $db->query("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'tax_enabled'", [$newValue]);

                echo "✅ IVA {$status} exitosamente!\n\n";
                echo "Estado actual del IVA: {$status}\n";

                if ($newValue == '1') {
                    $rate = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'tax_rate'")->getRow();
                    $name = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'tax_name'")->getRow();
                    echo "Tasa: {$rate->setting_value}%\n";
                    echo "Nombre: {$name->setting_value}\n";
                }
            } else {
                echo "❌ No se encontró la configuración de IVA\n";
            }

        } catch (\Exception $e) {
            echo "Error al cambiar configuración de IVA: " . $e->getMessage() . "\n";
        }
    }

    public function checkAdmin()
    {
        $db = \Config\Database::connect();

        $output = "<h2>Verificando cuenta de administrador: <EMAIL></h2>\n";

        try {
            // Verificar si la cuenta existe
            $admin = $db->query("SELECT id, nombre, email, is_active, bloqueado_hasta, intentos_login, ultimo_login, password FROM administradores WHERE email = '<EMAIL>'")->getRow();

            if (!$admin) {
                $output .= "<p style='color: red;'>❌ <NAME_EMAIL> NO EXISTE en la base de datos.</p>\n";
                $output .= "<p>Creando la cuenta...</p>\n";

                // Crear la cuenta
                $hashedPassword = password_hash('Clairo!23', PASSWORD_DEFAULT);
                $uuid = uniqid('admin_', true);

                $data = [
                    'uuid' => $uuid,
                    'nombre' => 'Engy Calderon',
                    'email' => '<EMAIL>',
                    'password' => $hashedPassword,
                    'rol_admin' => 'super_admin',
                    'is_active' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                if ($db->table('administradores')->insert($data)) {
                    $output .= "<p style='color: green;'>✅ Cuenta creada exitosamente!</p>\n";
                } else {
                    $output .= "<p style='color: red;'>❌ Error al crear la cuenta</p>\n";
                }

            } else {
                $output .= "<p style='color: green;'>✅ La cuenta existe en la base de datos.</p>\n";
                $output .= "<table border='1' cellpadding='5'>\n";
                $output .= "<tr><th>Campo</th><th>Valor</th></tr>\n";
                $output .= "<tr><td>ID</td><td>" . $admin->id . "</td></tr>\n";
                $output .= "<tr><td>Nombre</td><td>" . $admin->nombre . "</td></tr>\n";
                $output .= "<tr><td>Email</td><td>" . $admin->email . "</td></tr>\n";
                $output .= "<tr><td>Activo</td><td>" . ($admin->is_active ? 'Sí' : 'No') . "</td></tr>\n";
                $output .= "<tr><td>Bloqueado hasta</td><td>" . ($admin->bloqueado_hasta ?? 'No bloqueado') . "</td></tr>\n";
                $output .= "<tr><td>Intentos login</td><td>" . $admin->intentos_login . "</td></tr>\n";
                $output .= "<tr><td>Último login</td><td>" . ($admin->ultimo_login ?? 'Nunca') . "</td></tr>\n";
                $output .= "</table>\n";

                // Verificar si la contraseña es correcta
                if (password_verify('Clairo!23', $admin->password)) {
                    $output .= "<p style='color: green;'>✅ La contraseña es correcta.</p>\n";
                } else {
                    $output .= "<p style='color: red;'>❌ La contraseña NO es correcta. Actualizando...</p>\n";

                    // Actualizar la contraseña
                    $newHashedPassword = password_hash('Clairo!23', PASSWORD_DEFAULT);
                    if ($db->table('administradores')->where('email', '<EMAIL>')->update(['password' => $newHashedPassword])) {
                        $output .= "<p style='color: green;'>✅ Contraseña actualizada exitosamente!</p>\n";
                    } else {
                        $output .= "<p style='color: red;'>❌ Error al actualizar la contraseña</p>\n";
                    }
                }

                // Desbloquear cuenta si está bloqueada
                if ($admin->bloqueado_hasta && strtotime($admin->bloqueado_hasta) > time()) {
                    $output .= "<p style='color: orange;'>⚠️ La cuenta está bloqueada. Desbloqueando...</p>\n";

                    if ($db->table('administradores')->where('email', '<EMAIL>')->update(['bloqueado_hasta' => null, 'intentos_login' => 0])) {
                        $output .= "<p style='color: green;'>✅ Cuenta desbloqueada exitosamente!</p>\n";
                    } else {
                        $output .= "<p style='color: red;'>❌ Error al desbloquear la cuenta</p>\n";
                    }
                }

                // Activar cuenta si está inactiva
                if (!$admin->is_active) {
                    $output .= "<p style='color: orange;'>⚠️ La cuenta está inactiva. Activando...</p>\n";

                    if ($db->table('administradores')->where('email', '<EMAIL>')->update(['is_active' => 1])) {
                        $output .= "<p style='color: green;'>✅ Cuenta activada exitosamente!</p>\n";
                    } else {
                        $output .= "<p style='color: red;'>❌ Error al activar la cuenta</p>\n";
                    }
                }

                // Resetear intentos de login
                if ($admin->intentos_login > 0) {
                    $output .= "<p style='color: orange;'>⚠️ Reseteando intentos de login...</p>\n";

                    if ($db->table('administradores')->where('email', '<EMAIL>')->update(['intentos_login' => 0])) {
                        $output .= "<p style='color: green;'>✅ Intentos de login reseteados!</p>\n";
                    } else {
                        $output .= "<p style='color: red;'>❌ Error al resetear intentos</p>\n";
                    }
                }
            }

            $output .= "<hr>\n";
            $output .= "<h3>Estado final de la cuenta:</h3>\n";

            // Verificar estado final
            $finalAdmin = $db->query("SELECT id, nombre, email, is_active, bloqueado_hasta, intentos_login, ultimo_login FROM administradores WHERE email = '<EMAIL>'")->getRow();

            if ($finalAdmin) {
                $output .= "<table border='1' cellpadding='5'>\n";
                $output .= "<tr><th>Campo</th><th>Valor</th></tr>\n";
                $output .= "<tr><td>ID</td><td>" . $finalAdmin->id . "</td></tr>\n";
                $output .= "<tr><td>Nombre</td><td>" . $finalAdmin->nombre . "</td></tr>\n";
                $output .= "<tr><td>Email</td><td>" . $finalAdmin->email . "</td></tr>\n";
                $output .= "<tr><td>Activo</td><td>" . ($finalAdmin->is_active ? 'Sí' : 'No') . "</td></tr>\n";
                $output .= "<tr><td>Bloqueado hasta</td><td>" . ($finalAdmin->bloqueado_hasta ?? 'No bloqueado') . "</td></tr>\n";
                $output .= "<tr><td>Intentos login</td><td>" . $finalAdmin->intentos_login . "</td></tr>\n";
                $output .= "<tr><td>Último login</td><td>" . ($finalAdmin->ultimo_login ?? 'Nunca') . "</td></tr>\n";
                $output .= "</table>\n";

                $output .= "<p style='color: green; font-weight: bold;'>✅ La cuenta debería funcionar ahora. Intenta iniciar sesión con:</p>\n";
                $output .= "<p><strong>Email:</strong> <EMAIL></p>\n";
                $output .= "<p><strong>Contraseña:</strong> Clairo!23</p>\n";
                $output .= "<p><a href='" . base_url('admin/login') . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ir al Login de Admin</a></p>\n";
            }

        } catch (\Exception $e) {
            $output .= "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
        }

        return $output;
    }

    public function debugAdminLogin()
    {
        $db = \Config\Database::connect();

        $output = "<h2>Debug Admin Login Process</h2>\n";

        // Simular el proceso de login
        $email = '<EMAIL>';
        $password = 'Clairo!23';

        $output .= "<p><strong>Testing login for:</strong> {$email}</p>\n";

        try {
            // Paso 1: Buscar administrador
            $admin = $db->query("
                SELECT id, uuid, nombre, email, password, rol_admin, is_active,
                       bloqueado_hasta, intentos_login, session_timeout
                FROM administradores
                WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '')
            ", [$email])->getRow();

            if (!$admin) {
                $output .= "<p style='color: red;'>❌ Admin not found in database</p>\n";
                return $output;
            }

            $output .= "<p style='color: green;'>✅ Admin found in database</p>\n";
            $output .= "<p><strong>Admin ID:</strong> {$admin->id}</p>\n";
            $output .= "<p><strong>Is Active:</strong> " . ($admin->is_active ? 'Yes' : 'No') . "</p>\n";
            $output .= "<p><strong>Blocked Until:</strong> " . ($admin->bloqueado_hasta ?? 'Not blocked') . "</p>\n";
            $output .= "<p><strong>Login Attempts:</strong> {$admin->intentos_login}</p>\n";

            // Paso 2: Verificar si está activo
            if (!$admin->is_active) {
                $output .= "<p style='color: red;'>❌ Account is not active</p>\n";
                return $output;
            }

            $output .= "<p style='color: green;'>✅ Account is active</p>\n";

            // Paso 3: Verificar si está bloqueado
            if ($admin->bloqueado_hasta && strtotime($admin->bloqueado_hasta) > time()) {
                $output .= "<p style='color: red;'>❌ Account is blocked until: {$admin->bloqueado_hasta}</p>\n";
                return $output;
            }

            $output .= "<p style='color: green;'>✅ Account is not blocked</p>\n";

            // Paso 4: Verificar contraseña
            if (!password_verify($password, $admin->password)) {
                $output .= "<p style='color: red;'>❌ Password verification failed</p>\n";
                $output .= "<p><strong>Stored hash:</strong> " . substr($admin->password, 0, 50) . "...</p>\n";
                $output .= "<p><strong>Password verify result:</strong> " . (password_verify($password, $admin->password) ? 'true' : 'false') . "</p>\n";
                return $output;
            }

            $output .= "<p style='color: green;'>✅ Password verification successful</p>\n";

            // Paso 5: Simular actualización de último login
            $updateResult = $db->query("
                UPDATE administradores
                SET ultimo_login = NOW(), intentos_login = 0, bloqueado_hasta = NULL
                WHERE id = ?
            ", [$admin->id]);

            if ($updateResult) {
                $output .= "<p style='color: green;'>✅ Database update successful</p>\n";
            } else {
                $output .= "<p style='color: red;'>❌ Database update failed</p>\n";
            }

            // Paso 6: Verificar rutas
            $output .= "<hr><h3>Route Information:</h3>\n";
            $output .= "<p><strong>Login URL:</strong> " . base_url('admin/login') . "</p>\n";
            $output .= "<p><strong>Authenticate URL:</strong> " . base_url('admin/authenticate') . "</p>\n";
            $output .= "<p><strong>Dashboard URL:</strong> " . base_url('admin/dashboard') . "</p>\n";

            // Paso 7: Verificar controlador
            $output .= "<hr><h3>Controller Check:</h3>\n";
            if (class_exists('App\\Controllers\\Admin\\AdminController')) {
                $output .= "<p style='color: green;'>✅ AdminController class exists</p>\n";
            } else {
                $output .= "<p style='color: red;'>❌ AdminController class not found</p>\n";
            }

            $output .= "<p style='color: green; font-weight: bold;'>🎯 All checks passed! The login should work.</p>\n";
            $output .= "<p><a href='" . base_url('admin/login') . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Login Again</a></p>\n";

        } catch (\Exception $e) {
            $output .= "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>\n";
            $output .= "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
            $output .= "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        }

        return $output;
    }

    public function debugSession()
    {
        $session = session();

        $output = "<h2>Debug Session Status</h2>\n";
        $output .= "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

        // Verificar si hay sesión activa
        $output .= "<h3>Session Data:</h3>\n";
        $sessionData = $session->get();

        if (empty($sessionData)) {
            $output .= "<p style='color: red;'>❌ No session data found</p>\n";
        } else {
            $output .= "<table border='1' cellpadding='5'>\n";
            $output .= "<tr><th>Key</th><th>Value</th></tr>\n";
            foreach ($sessionData as $key => $value) {
                $displayValue = is_array($value) ? json_encode($value) : (string)$value;
                $output .= "<tr><td>{$key}</td><td>{$displayValue}</td></tr>\n";
            }
            $output .= "</table>\n";
        }

        // Verificar específicamente los datos de admin
        $output .= "<h3>Admin Session Check:</h3>\n";
        $adminId = $session->get('admin_id');
        $isLoggedIn = $session->get('is_admin_logged_in');
        $adminEmail = $session->get('admin_email');

        $output .= "<p><strong>Admin ID:</strong> " . ($adminId ?: 'Not set') . "</p>\n";
        $output .= "<p><strong>Is Logged In:</strong> " . ($isLoggedIn ? 'Yes' : 'No') . "</p>\n";
        $output .= "<p><strong>Admin Email:</strong> " . ($adminEmail ?: 'Not set') . "</p>\n";

        // Verificar configuración de sesión
        $output .= "<h3>Session Configuration:</h3>\n";
        $config = config('App');
        $output .= "<p><strong>Session Driver:</strong> " . ini_get('session.save_handler') . "</p>\n";
        $output .= "<p><strong>Session Path:</strong> " . ini_get('session.save_path') . "</p>\n";
        $output .= "<p><strong>Session Cookie Name:</strong> " . ini_get('session.name') . "</p>\n";
        $output .= "<p><strong>Session ID:</strong> " . session_id() . "</p>\n";

        // Test login
        $output .= "<hr><h3>Quick Actions:</h3>\n";
        $output .= "<p><a href='" . base_url('simple-admin-login') . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Simple Login</a></p>\n";
        $output .= "<p><a href='" . base_url('admin/login') . "' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Regular Login</a></p>\n";
        $output .= "<p><a href='" . base_url('test-dashboard') . "' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Dashboard (No Auth)</a></p>\n";

        return $output;
    }

    public function testDashboard()
    {
        return "<h2>Test Dashboard</h2><p>This is a test dashboard without authentication filter.</p><p>If you can see this, the dashboard route works.</p><p><a href='" . base_url('admin/dashboard') . "'>Try Real Dashboard</a></p>";
    }

    public function debugAdminAuth()
    {
        $db = \Config\Database::connect();

        $output = "<h2>Debug Admin Authentication</h2>\n";
        $output .= "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

        // Verificar si el admin existe
        $email = '<EMAIL>';
        $admin = $db->query("
            SELECT id, uuid, nombre, email, password, rol_admin, is_active,
                   bloqueado_hasta, intentos_login, session_timeout, created_at, updated_at
            FROM administradores
            WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '')
        ", [$email])->getRow();

        if (!$admin) {
            $output .= "<p style='color: red;'>❌ Admin not found with email: {$email}</p>\n";

            // Mostrar todos los admins
            $allAdmins = $db->query("SELECT id, nombre, email, is_active FROM administradores WHERE deleted_at IS NULL OR deleted_at = ''")->getResult();
            $output .= "<h3>Available Admins:</h3>\n";
            $output .= "<table border='1' cellpadding='5'>\n";
            $output .= "<tr><th>ID</th><th>Name</th><th>Email</th><th>Active</th></tr>\n";
            foreach ($allAdmins as $a) {
                $output .= "<tr><td>{$a->id}</td><td>{$a->nombre}</td><td>{$a->email}</td><td>" . ($a->is_active ? 'Yes' : 'No') . "</td></tr>\n";
            }
            $output .= "</table>\n";
        } else {
            $output .= "<p style='color: green;'>✅ Admin found!</p>\n";
            $output .= "<table border='1' cellpadding='5'>\n";
            $output .= "<tr><th>Field</th><th>Value</th></tr>\n";
            $output .= "<tr><td>ID</td><td>{$admin->id}</td></tr>\n";
            $output .= "<tr><td>UUID</td><td>{$admin->uuid}</td></tr>\n";
            $output .= "<tr><td>Name</td><td>{$admin->nombre}</td></tr>\n";
            $output .= "<tr><td>Email</td><td>{$admin->email}</td></tr>\n";
            $output .= "<tr><td>Role</td><td>{$admin->rol_admin}</td></tr>\n";
            $output .= "<tr><td>Active</td><td>" . ($admin->is_active ? 'Yes' : 'No') . "</td></tr>\n";
            $output .= "<tr><td>Blocked Until</td><td>" . ($admin->bloqueado_hasta ?: 'Not blocked') . "</td></tr>\n";
            $output .= "<tr><td>Login Attempts</td><td>{$admin->intentos_login}</td></tr>\n";
            $output .= "<tr><td>Session Timeout</td><td>{$admin->session_timeout}</td></tr>\n";
            $output .= "<tr><td>Created</td><td>{$admin->created_at}</td></tr>\n";
            $output .= "<tr><td>Updated</td><td>{$admin->updated_at}</td></tr>\n";
            $output .= "</table>\n";

            // Test password
            $testPassword = 'Clairo!23';
            $passwordMatch = password_verify($testPassword, $admin->password);
            $output .= "<h3>Password Test:</h3>\n";
            $output .= "<p><strong>Test Password:</strong> {$testPassword}</p>\n";
            $output .= "<p><strong>Stored Hash:</strong> " . substr($admin->password, 0, 50) . "...</p>\n";
            $output .= "<p><strong>Password Match:</strong> " . ($passwordMatch ? '<span style="color: green;">✅ YES</span>' : '<span style="color: red;">❌ NO</span>') . "</p>\n";

            if (!$passwordMatch) {
                // Try to create a new hash
                $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
                $output .= "<p><strong>New Hash for Testing:</strong> {$newHash}</p>\n";
                $output .= "<p><a href='#' onclick=\"if(confirm('Update password hash?')) { window.location.href = '" . base_url('update-admin-password') . "'; }\">Update Password Hash</a></p>\n";
            }
        }

        return $output;
    }

    public function updateAdminPassword()
    {
        $db = \Config\Database::connect();

        $email = '<EMAIL>';
        $newPassword = 'Clairo!23';
        $newHash = password_hash($newPassword, PASSWORD_DEFAULT);

        $result = $db->query("
            UPDATE administradores
            SET password = ?, updated_at = NOW()
            WHERE email = ?
        ", [$newHash, $email]);

        if ($result) {
            return "<h2>Password Updated Successfully!</h2><p>New hash: {$newHash}</p><p><a href='" . base_url('debug-admin-auth') . "'>Test Again</a></p>";
        } else {
            return "<h2>Error updating password</h2><p><a href='" . base_url('debug-admin-auth') . "'>Go Back</a></p>";
        }
    }

    public function simulateAdminAuth()
    {
        $db = \Config\Database::connect();
        $session = session();

        $output = "<h2>Simulate Admin Authentication Process</h2>\n";
        $output .= "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

        $email = '<EMAIL>';
        $password = 'Clairo!23';

        $output .= "<h3>Step 1: Input Validation</h3>\n";
        $output .= "<p>Email: {$email}</p>\n";
        $output .= "<p>Password: " . str_repeat('*', strlen($password)) . "</p>\n";

        if (empty($email) || empty($password)) {
            $output .= "<p style='color: red;'>❌ Empty credentials</p>\n";
            return $output;
        }
        $output .= "<p style='color: green;'>✅ Credentials provided</p>\n";

        $output .= "<h3>Step 2: Database Query</h3>\n";
        try {
            $admin = $db->query("
                SELECT id, uuid, nombre, email, password, rol_admin, is_active,
                       bloqueado_hasta, intentos_login, session_timeout
                FROM administradores
                WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '')
            ", [$email])->getRow();

            if (!$admin) {
                $output .= "<p style='color: red;'>❌ Admin not found</p>\n";
                return $output;
            }
            $output .= "<p style='color: green;'>✅ Admin found: ID {$admin->id}</p>\n";

            $output .= "<h3>Step 3: Account Status Check</h3>\n";
            if (!$admin->is_active) {
                $output .= "<p style='color: red;'>❌ Account inactive</p>\n";
                return $output;
            }
            $output .= "<p style='color: green;'>✅ Account active</p>\n";

            if ($admin->bloqueado_hasta && strtotime($admin->bloqueado_hasta) > time()) {
                $output .= "<p style='color: red;'>❌ Account blocked until: {$admin->bloqueado_hasta}</p>\n";
                return $output;
            }
            $output .= "<p style='color: green;'>✅ Account not blocked</p>\n";

            $output .= "<h3>Step 4: Password Verification</h3>\n";
            if (!password_verify($password, $admin->password)) {
                $output .= "<p style='color: red;'>❌ Password verification failed</p>\n";
                return $output;
            }
            $output .= "<p style='color: green;'>✅ Password verified</p>\n";

            $output .= "<h3>Step 5: Database Update</h3>\n";
            $updateResult = $db->query("
                UPDATE administradores
                SET ultimo_login = NOW(), intentos_login = 0, bloqueado_hasta = NULL
                WHERE id = ?
            ", [$admin->id]);

            if (!$updateResult) {
                $output .= "<p style='color: red;'>❌ Failed to update login record</p>\n";
                return $output;
            }
            $output .= "<p style='color: green;'>✅ Login record updated</p>\n";

            $output .= "<h3>Step 6: Session Creation</h3>\n";
            $currentTime = time();
            $sessionData = [
                'admin_id' => $admin->id,
                'admin_uuid' => $admin->uuid,
                'admin_name' => $admin->nombre,
                'admin_email' => $admin->email,
                'admin_role' => $admin->rol_admin,
                'is_admin_logged_in' => true,
                'admin_login_time' => $currentTime,
                'admin_last_activity' => $currentTime
            ];

            $session->set($sessionData);
            $output .= "<p style='color: green;'>✅ Session created with data:</p>\n";
            $output .= "<pre>" . print_r($sessionData, true) . "</pre>\n";

            $output .= "<h3>Step 7: Session Verification</h3>\n";
            $storedAdminId = $session->get('admin_id');
            $isLoggedIn = $session->get('is_admin_logged_in');

            if ($storedAdminId == $admin->id && $isLoggedIn) {
                $output .= "<p style='color: green;'>✅ Session verified successfully</p>\n";
                $output .= "<p><strong>Stored Admin ID:</strong> {$storedAdminId}</p>\n";
                $output .= "<p><strong>Is Logged In:</strong> " . ($isLoggedIn ? 'Yes' : 'No') . "</p>\n";
            } else {
                $output .= "<p style='color: red;'>❌ Session verification failed</p>\n";
                $output .= "<p><strong>Expected Admin ID:</strong> {$admin->id}</p>\n";
                $output .= "<p><strong>Stored Admin ID:</strong> {$storedAdminId}</p>\n";
                $output .= "<p><strong>Is Logged In:</strong> " . ($isLoggedIn ? 'Yes' : 'No') . "</p>\n";
            }

            $output .= "<h3>Final Result</h3>\n";
            $output .= "<p style='color: green; font-size: 18px;'>🎉 <strong>AUTHENTICATION SHOULD BE SUCCESSFUL!</strong></p>\n";
            $output .= "<p><a href='" . base_url('admin/dashboard') . "' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Dashboard Now</a></p>\n";

        } catch (\Exception $e) {
            $output .= "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>\n";
        }

        return $output;
    }

    public function testDirectAuth()
    {
        // Simulate the exact POST request
        $_POST['email'] = '<EMAIL>';
        $_POST['password'] = 'Clairo!23';

        // Create a request object
        $request = \Config\Services::request();

        // Create admin controller
        $adminController = new \App\Controllers\Admin\AdminController();

        // Call authenticate method directly
        try {
            $result = $adminController->authenticate();

            if ($result instanceof \CodeIgniter\HTTP\RedirectResponse) {
                $location = $result->getHeaderLine('Location');
                return "<h2>Direct Auth Test Result</h2><p><strong>Redirect to:</strong> {$location}</p><p>This shows where the authenticate method is trying to redirect.</p>";
            } else {
                return "<h2>Direct Auth Test Result</h2><p>Unexpected result type: " . get_class($result) . "</p>";
            }
        } catch (\Exception $e) {
            return "<h2>Direct Auth Test Result</h2><p><strong>Exception:</strong> " . $e->getMessage() . "</p>";
        }
    }
}
