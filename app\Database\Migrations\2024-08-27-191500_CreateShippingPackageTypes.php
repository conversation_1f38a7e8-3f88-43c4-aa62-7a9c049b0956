<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateShippingPackageTypes extends Migration
{
    public function up()
    {
        // Tabla para tipos de paquetes de envío
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'Nombre del tipo de paquete (Pequeño, Mediano, etc.)'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Descripción del tipo de paquete'
            ],
            'max_length_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'comment' => 'Longitud máxima en centímetros'
            ],
            'max_width_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'comment' => 'Ancho máximo en centímetros'
            ],
            'max_height_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'comment' => 'Alto máximo en centímetros'
            ],
            'max_weight_lbs' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'comment' => 'Peso máximo en libras'
            ],
            'min_weight_lbs' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'default' => 0,
                'comment' => 'Peso mínimo en libras'
            ],
            'base_cost' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'comment' => 'Costo base del envío para este tipo de paquete'
            ],
            'cost_per_km' => [
                'type' => 'DECIMAL',
                'constraint' => '8,4',
                'default' => 0,
                'comment' => 'Costo adicional por kilómetro'
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => '1 = Activo, 0 = Inactivo'
            ],
            'sort_order' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Orden de visualización'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['is_active', 'sort_order']);
        $this->forge->createTable('shipping_package_types');

        // Tabla para zonas de envío
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'Nombre de la zona (Ciudad de Guatemala, Zona 1, etc.)'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Descripción de la zona'
            ],
            'base_distance_km' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'default' => 0,
                'comment' => 'Distancia base en kilómetros desde el centro de distribución'
            ],
            'additional_cost' => [
                'type' => 'DECIMAL',
                'constraint' => '8,2',
                'default' => 0,
                'comment' => 'Costo adicional para esta zona'
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => '1 = Activo, 0 = Inactivo'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('is_active');
        $this->forge->createTable('shipping_zones');

        // Insertar datos de ejemplo para tipos de paquetes (basado en Forza Delivery)
        $packageTypes = [
            [
                'name' => 'Pequeño',
                'description' => 'Paquete pequeño - Si el lado más largo es menor o igual a 28 cm',
                'max_length_cm' => 28.00,
                'max_width_cm' => 28.00,
                'max_height_cm' => 28.00,
                'max_weight_lbs' => 10.00,
                'min_weight_lbs' => 0.00,
                'base_cost' => 25.00,
                'cost_per_km' => 0.50,
                'is_active' => 1,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Mediano',
                'description' => 'Paquete mediano - Máximo 36cm o 20lbs',
                'max_length_cm' => 36.00,
                'max_width_cm' => 36.00,
                'max_height_cm' => 36.00,
                'max_weight_lbs' => 20.00,
                'min_weight_lbs' => 0.00,
                'base_cost' => 35.00,
                'cost_per_km' => 0.75,
                'is_active' => 1,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Grande',
                'description' => 'Paquete grande - Máximo 47cm o 40lbs',
                'max_length_cm' => 47.00,
                'max_width_cm' => 47.00,
                'max_height_cm' => 47.00,
                'max_weight_lbs' => 40.00,
                'min_weight_lbs' => 0.00,
                'base_cost' => 50.00,
                'cost_per_km' => 1.00,
                'is_active' => 1,
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Extra Grande',
                'description' => 'Paquete extra grande - Máximo 51cm o 59lbs',
                'max_length_cm' => 51.00,
                'max_width_cm' => 51.00,
                'max_height_cm' => 51.00,
                'max_weight_lbs' => 59.00,
                'min_weight_lbs' => 0.00,
                'base_cost' => 75.00,
                'cost_per_km' => 1.25,
                'is_active' => 1,
                'sort_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Sobredimensionado',
                'description' => 'Paquete sobredimensionado - Mínimo 60lbs',
                'max_length_cm' => 100.00,
                'max_width_cm' => 100.00,
                'max_height_cm' => 100.00,
                'max_weight_lbs' => 200.00,
                'min_weight_lbs' => 60.00,
                'base_cost' => 100.00,
                'cost_per_km' => 2.00,
                'is_active' => 1,
                'sort_order' => 5,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('shipping_package_types')->insertBatch($packageTypes);

        // Insertar zonas de ejemplo
        $zones = [
            [
                'name' => 'Ciudad de Guatemala - Centro',
                'description' => 'Zona central de la Ciudad de Guatemala',
                'base_distance_km' => 0.00,
                'additional_cost' => 0.00,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Ciudad de Guatemala - Zonas',
                'description' => 'Otras zonas de la Ciudad de Guatemala',
                'base_distance_km' => 5.00,
                'additional_cost' => 10.00,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Municipios Cercanos',
                'description' => 'Municipios cercanos a la capital',
                'base_distance_km' => 15.00,
                'additional_cost' => 25.00,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('shipping_zones')->insertBatch($zones);
    }

    public function down()
    {
        $this->forge->dropTable('shipping_zones');
        $this->forge->dropTable('shipping_package_types');
    }
}
