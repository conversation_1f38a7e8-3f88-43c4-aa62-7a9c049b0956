<?php

/**
 * Script para probar el endpoint de validación de Recurrente
 */

echo "=== PRUEBA DEL ENDPOINT DE VALIDACIÓN DE RECURRENTE ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "======================================================\n\n";

// URL base de la API
$baseUrl = 'https://mrcell.com.gt/api/v1/payments/recurrente/validate';

// Casos de prueba
$testCases = [
    [
        'description' => 'Producto barato en GTQ (elegible)',
        'data' => [
            'cart_total' => 500.00,
            'currency' => 'GTQ'
        ]
    ],
    [
        'description' => 'Producto medio en GTQ (elegible)',
        'data' => [
            'cart_total' => 50000.00,
            'currency' => 'GTQ'
        ]
    ],
    [
        'description' => 'Producto caro en GTQ (no elegible)',
        'data' => [
            'cart_total' => 200000.00,
            'currency' => 'GTQ'
        ]
    ],
    [
        'description' => 'Producto en USD (elegible)',
        'data' => [
            'cart_total' => 5000.00,
            'currency' => 'USD'
        ]
    ],
    [
        'description' => 'Producto caro en USD (no elegible)',
        'data' => [
            'cart_total' => 20000.00,
            'currency' => 'USD'
        ]
    ],
    [
        'description' => 'Límite exacto en USD',
        'data' => [
            'cart_total' => 15000.00,
            'currency' => 'USD'
        ]
    ]
];

function makeApiRequest($url, $data) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false, // Solo para pruebas
        CURLOPT_SSL_VERIFYHOST => false  // Solo para pruebas
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

echo "🧪 EJECUTANDO CASOS DE PRUEBA:\n";
echo "==============================\n\n";

foreach ($testCases as $index => $testCase) {
    echo "📋 Caso " . ($index + 1) . ": {$testCase['description']}\n";
    echo "   Datos: " . json_encode($testCase['data']) . "\n";
    
    $result = makeApiRequest($baseUrl, $testCase['data']);
    
    echo "   HTTP Code: {$result['http_code']}\n";
    
    if ($result['error']) {
        echo "   ❌ Error de cURL: {$result['error']}\n";
    } else {
        $decodedResponse = json_decode($result['response'], true);
        
        if ($decodedResponse) {
            echo "   📄 Respuesta:\n";
            echo "      Status: " . ($decodedResponse['status'] ?? 'N/A') . "\n";
            
            if (isset($decodedResponse['data'])) {
                $data = $decodedResponse['data'];
                echo "      Elegible: " . ($data['eligible'] ? '✅ SÍ' : '❌ NO') . "\n";
                echo "      Total: {$data['currency']} " . number_format($data['cart_total'], 2) . "\n";
                
                if (isset($data['message']) && $data['message']) {
                    echo "      Mensaje: {$data['message']}\n";
                }
                
                echo "      Límites: USD {$data['limit_usd']}, GTQ {$data['limit_gtq']}\n";
            }
            
            if (isset($decodedResponse['message'])) {
                echo "      Mensaje API: {$decodedResponse['message']}\n";
            }
        } else {
            echo "   📄 Respuesta cruda: {$result['response']}\n";
        }
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

// Probar también el endpoint de métodos de pago
echo "🔧 PROBANDO ENDPOINT DE MÉTODOS DE PAGO:\n";
echo "=======================================\n\n";

$methodsUrl = 'https://mrcell.com.gt/api/v1/payments/methods';

// Probar sin parámetros
echo "📋 Sin parámetros de carrito:\n";
$result = makeApiRequest($methodsUrl . '?cart_total=&cart_currency=', []);
echo "   HTTP Code: {$result['http_code']}\n";

if (!$result['error']) {
    $decodedResponse = json_decode($result['response'], true);
    if ($decodedResponse && isset($decodedResponse['data'])) {
        foreach ($decodedResponse['data'] as $method) {
            if ($method['id'] === 'recurrente') {
                echo "   Recurrente habilitado: " . ($method['enabled'] ? '✅ SÍ' : '❌ NO') . "\n";
                if (isset($method['price_limit_message'])) {
                    echo "   Mensaje: {$method['price_limit_message']}\n";
                }
                break;
            }
        }
    }
}

echo "\n";

// Probar con carrito caro
echo "📋 Con carrito caro (Q200,000):\n";
$result = makeApiRequest($methodsUrl . '?cart_total=200000&cart_currency=GTQ', []);
echo "   HTTP Code: {$result['http_code']}\n";

if (!$result['error']) {
    $decodedResponse = json_decode($result['response'], true);
    if ($decodedResponse && isset($decodedResponse['data'])) {
        foreach ($decodedResponse['data'] as $method) {
            if ($method['id'] === 'recurrente') {
                echo "   Recurrente habilitado: " . ($method['enabled'] ? '✅ SÍ' : '❌ NO') . "\n";
                if (isset($method['price_limit_message'])) {
                    echo "   Mensaje: {$method['price_limit_message']}\n";
                }
                break;
            }
        }
    }
}

echo "\n";

// Probar con carrito barato
echo "📋 Con carrito barato (Q5,000):\n";
$result = makeApiRequest($methodsUrl . '?cart_total=5000&cart_currency=GTQ', []);
echo "   HTTP Code: {$result['http_code']}\n";

if (!$result['error']) {
    $decodedResponse = json_decode($result['response'], true);
    if ($decodedResponse && isset($decodedResponse['data'])) {
        foreach ($decodedResponse['data'] as $method) {
            if ($method['id'] === 'recurrente') {
                echo "   Recurrente habilitado: " . ($method['enabled'] ? '✅ SÍ' : '❌ NO') . "\n";
                if (isset($method['price_limit_message'])) {
                    echo "   Mensaje: {$method['price_limit_message']}\n";
                }
                break;
            }
        }
    }
}

echo "\n🎉 Pruebas completadas.\n";
