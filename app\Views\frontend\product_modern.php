<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    /* Product Detail Styles with Red/White/Black/Gray Palette */

    /* Breadcrumb */
    .breadcrumb {
        background: var(--gray-100);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: var(--primary-dark);
    }

    .breadcrumb-item.active {
        color: var(--gray-600);
    }

    /* Product Image */
    .product-image {
        width: 100%;
        height: 450px;
        object-fit: contain;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
        background: var(--white-color);
    }

    /* Hover Zoom Container */
    .image-container {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        background: var(--white-color);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .image-container:hover {
        box-shadow: 0 15px 40px rgba(220, 38, 38, 0.3);
        border-color: rgba(220, 38, 38, 0.2);
        transform: translateY(-2px);
    }

    /* Product Image with Hover Zoom */
    .product-image {
        width: 100%;
        height: auto;
        transition: transform 0.25s ease-out;
        cursor: zoom-in;
        display: block;
        transform-origin: center center;
        max-width: 100%;
        user-select: none;
    }

    .product-image:hover {
        transform: scale(2.3);
        transition: transform 0.25s ease-out;
        cursor: move;
    }

    /* Hover tooltip */
    .image-container::after {
        content: "🔍 Mueve el mouse para explorar";
        position: absolute;
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, rgba(220, 38, 38, 0.9), rgba(185, 28, 28, 0.9));
        color: white;
        padding: 10px 16px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        z-index: 10;
        white-space: nowrap;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .image-container:hover::after {
        opacity: 1;
        transform: translateX(-50%) translateY(-5px);
    }

    /* Reviews Section Styles */
    .reviews-section {
        background: var(--gray-50);
        border-radius: 20px;
        padding: 2rem;
        margin-top: 2rem;
    }

    .reviews-title {
        color: var(--dark-color);
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .product-specs .col-md-6 {
        background-color: #f8f9fa; /* gris clarito del contenedor */
        padding: 10px;
        border-radius: 4px; /* opcional para redondear esquinas */
    }

    .product-specs .col-md-6 table {
        background-color: #f8f9fa !important;
    }

    .product-specs table {
        background-color: #f8f9fa; /* mismo color de fondo que el contenedor */
        border-radius: 6px;       /* opcional para que se vea uniforme */
        padding: 10px;
    }

    .product-specs table td,
    .product-specs table th {
        background-color: #f8f9fa !important; /* gris claro */
    }


    .reviews-summary-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        border: 1px solid var(--gray-200);
    }

    .rating-circle {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-radius: 20px;
        padding: 2rem 1.5rem;
        margin: 0 auto;
        max-width: 200px;
    }

    .rating-number {
        font-size: 3rem;
        font-weight: 800;
        color: white;
    }

    .rating-stars {
        font-size: 1.2rem;
    }

    .rating-stars i {
        color: #ffd700 !important;
        margin: 0 2px;
    }

    .rating-breakdown {
        padding-left: 1rem;
    }

    .rating-bar {
        background: var(--gray-200);
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
        margin: 0.5rem 0;
    }

    .rating-bar-fill {
        background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .write-review-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        transition: all 0.3s ease;
    }

    .write-review-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    }

    .reviews-container {
        max-height: 600px;
        overflow-y: auto;
        padding-right: 10px;
    }

    .reviews-container::-webkit-scrollbar {
        width: 6px;
    }

    .reviews-container::-webkit-scrollbar-track {
        background: var(--gray-200);
        border-radius: 3px;
    }

    .reviews-container::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 3px;
    }

    .review-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }

    .review-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .review-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .review-author {
        font-weight: 600;
        color: var(--dark-color);
    }

    .review-date {
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .review-rating {
        color: #ffd700;
        margin: 0.5rem 0;
    }

    .review-title {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .review-content {
        color: var(--gray-700);
        line-height: 1.6;
    }

    /* Product Gallery */
    .product-gallery img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid var(--gray-300);
    }

    .product-gallery img:hover,
    .product-gallery img.active {
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    /* Product Info */
    .product-title {
        color: var(--gray-900);
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .product-meta {
        color: var(--gray-500);
        font-size: 0.95rem;
    }

    /* Price */
    .price {
        font-size: 2.2rem;
        font-weight: bold;
        color: var(--primary-color);
    }

    .old-price {
        text-decoration: line-through;
        color: var(--gray-500);
        font-size: 1.2rem;
    }

    .discount-badge {
        background: var(--primary-color);
        color: var(--white-color);
        padding: 0.3rem 0.7rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 700;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }

    /* Stock Status */
    .stock-status {
        padding: 0.6rem 1.2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .in-stock {
        background: var(--gray-100);
        color: var(--success-color);
        border: 1px solid var(--success-color);
    }

    .out-of-stock {
        background: var(--gray-100);
        color: var(--danger-color);
        border: 1px solid var(--danger-color);
    }

    /* Rating */
    .rating {
        color: #ffc107;
        margin-bottom: 0.5rem;
    }

    .rating .fa-star-o {
        color: var(--gray-300);
    }

    /* Buttons */
    .btn-add-cart {
        background: var(--primary-color);
        border: none;
        color: var(--white-color);
        padding: 15px 30px;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .btn-add-cart:hover {
        background: var(--primary-dark);
        color: var(--white-color);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
    }

    .btn-add-cart:disabled {
        background: var(--gray-400);
        box-shadow: none;
        transform: none;
    }

    /* Quantity Input */
    .quantity-input {
        border: 2px solid var(--gray-300);
        border-radius: 8px;
        padding: 0.5rem;
        text-align: center;
        font-weight: 600;
        transition: border-color 0.3s ease;
    }

    .quantity-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }

    /* Quantity Controls */
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0;
    }

    .quantity-btn-minus,
    .quantity-btn-plus {
        border: 2px solid var(--gray-300);
        background: var(--white-color);
        color: var(--gray-600);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .quantity-btn-minus {
        border-radius: 8px 0 0 8px;
        border-right: none;
    }

    .quantity-btn-plus {
        border-radius: 0 8px 8px 0;
        border-left: none;
    }

    .quantity-btn-minus:hover:not(:disabled),
    .quantity-btn-plus:hover:not(:disabled) {
        background: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .quantity-btn-minus:disabled,
    .quantity-btn-plus:disabled {
        background: var(--gray-100);
        color: var(--gray-400);
        cursor: not-allowed;
        border-color: var(--gray-200);
    }

    .quantity-controls .quantity-input {
        border-radius: 0;
        border-left: none;
        border-right: none;
        margin: 0;
        width: 80px;
        height: 40px;
        padding: 0.5rem 0.25rem;
    }

    /* Specifications Table */
    .specifications-table {
        background: var(--white-color);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }

    .specifications-table th {
        background: var(--gray-100);
        font-weight: 600;
        width: 30%;
        color: var(--gray-800);
        border-bottom: 1px solid var(--gray-200);
    }

    .specifications-table td {
        color: var(--gray-700);
        border-bottom: 1px solid var(--gray-200);
    }

    /* Related Products */
    .related-product {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        background: var(--white-color);
    }

    .related-product:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        border-color: var(--primary-light);
    }

    .related-product img {
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .related-product:hover img {
        transform: scale(1.05);
    }

    /* Product Description */
    .product-description {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 1.5rem;
        border-left: 4px solid var(--primary-color);
    }

    .product-description h5 {
        color: var(--gray-800);
        margin-bottom: 1rem;
    }

    .product-description p {
        color: var(--gray-600);
        line-height: 1.6;
        margin-bottom: 0;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>"><i class="fas fa-home me-1"></i>Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('tienda') ?>">Tienda</a></li>
                    <?php if (isset($product) && $product): ?>
                        <?php if (isset($product['category'])): ?>
                            <li class="breadcrumb-item"><a href="<?= base_url('categories/' . $product['category']) ?>"><?= esc($product['category']) ?></a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active"><?= esc($product['name']) ?></li>
                    <?php else: ?>
                        <li class="breadcrumb-item active">Producto no encontrado</li>
                    <?php endif; ?>
                </ol>
            </nav>
        </div>
    </div>

        <?php if (isset($product) && $product): ?>
            <!-- Product Details -->
            <div class="row">
                <div class="col-lg-6">
                    <!-- Product Image -->
                    <div class="mb-4 image-container" id="image-container">
                        <img src="<?= $product['image'] ?? 'https://via.placeholder.com/400x400/007bff/ffffff?text=Producto' ?>"
                             alt="<?= esc($product['name']) ?>"
                             class="product-image"
                             id="main-image"
                             onmousemove="handleMouseMove(event)"
                             onmouseleave="handleMouseLeave(event)"
                             title="Pasa el mouse para hacer zoom">
                    </div>
                    
                    <!-- Product Gallery -->

                    <?php if (isset($product['gallery']) && !empty($product['gallery']) && count($product['gallery']) > 1): ?>
                        <div class="product-gallery d-flex gap-2">
                            <?php foreach ($product['gallery'] as $index => $image): ?>
                                <img src="<?= $image ?>"
                                     alt="<?= esc($product['name']) ?> - Imagen <?= $index + 1 ?>"
                                     onclick="changeMainImage('<?= $image ?>')"
                                     class="<?= $index === 0 ? 'active' : '' ?>"
                                     title="Click para cambiar imagen">
                            <?php endforeach; ?>
                        </div>
                    <?php elseif (isset($product['gallery']) && count($product['gallery']) === 1): ?>
                        <!-- Single image gallery -->
                        <div class="product-gallery d-flex gap-2">
                            <img src="<?= $product['gallery'][0] ?>"
                                 alt="<?= esc($product['name']) ?> - Imagen principal"
                                 onclick="changeMainImage('<?= $product['gallery'][0] ?>')"
                                 class="active"
                                 title="Click para cambiar imagen">
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-6">
                    <!-- Product Info -->
                    <div class="mb-4">
                        <h1 class="product-title"><?= esc($product['name']) ?></h1>
                        <p class="product-meta">
                            <i class="fas fa-tag me-1"></i><?= esc($product['brand'] ?? 'Marca') ?> |
                            <i class="fas fa-barcode me-1"></i>SKU: <?= esc($product['sku'] ?? 'N/A') ?>
                        </p>

                        <!-- Rating -->
                        <?php if (isset($product['rating'])): ?>
                            <div class="mb-3">
                                <div class="rating">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star<?= $i <= $product['rating'] ? '' : '-o' ?>"></i>
                                    <?php endfor; ?>
                                    <span class="ms-2 text-muted">(<?= $product['reviews_count'] ?? 0 ?> reseñas)</span>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Price -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <?php
                                // Verificar si hay descuento real
                                $hasDiscount = isset($product['price_sale']) &&
                                              $product['price_sale'] > 0 &&
                                              $product['price_sale'] < $product['price_regular'];

                                // Usar discount_percentage si está disponible, sino calcularlo
                                $discountPercentage = isset($product['discount_percentage']) ?
                                                    $product['discount_percentage'] :
                                                    ($hasDiscount ? round((($product['price_regular'] - $product['price_sale']) / $product['price_regular']) * 100) : 0);
                                ?>

                                <?php if ($hasDiscount && $discountPercentage > 0): ?>
                                    <!-- Precio con descuento -->
                                    <span class="price"><?= format_currency($product['price_sale'], $product['currency'] ?? 'GTQ') ?></span>
                                    <span class="old-price"><?= format_currency($product['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                    <span class="discount-badge">
                                        -<?= $discountPercentage ?>%
                                    </span>
                                <?php else: ?>
                                    <!-- Precio normal sin descuento -->
                                    <span class="price"><?= format_currency($product['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                <?php endif; ?>
                            </div>

                            <!-- Conversión de Moneda -->
                            <?php
                            $currency = $product['currency'] ?? 'GTQ';
                            $targetCurrency = ($currency === 'USD') ? 'GTQ' : 'USD';
                            $exchangeRate = get_cached_exchange_rate($currency, $targetCurrency);
                            ?>
                            <div class="mt-2">
                                <?php if ($hasDiscount && $discountPercentage > 0): ?>
                                    <!-- Conversión para precio con descuento -->
                                    <?php if ($currency === 'USD'): ?>
                                        <small class="text-muted">
                                            ≈ Q<?= number_format($product['price_sale'] * $exchangeRate, 2) ?> GTQ
                                            <?php if ($product['price_regular'] != $product['price_sale']): ?>
                                                <span class="text-decoration-line-through ms-2">Q<?= number_format($product['price_regular'] * $exchangeRate, 2) ?></span>
                                            <?php endif; ?>
                                        </small>
                                    <?php else: ?>
                                        <small class="text-muted">
                                            ≈ $<?= number_format($product['price_sale'] / $exchangeRate, 2) ?> USD
                                            <?php if ($product['price_regular'] != $product['price_sale']): ?>
                                                <span class="text-decoration-line-through ms-2">$<?= number_format($product['price_regular'] * $exchangeRate, 2) ?></span>
                                            <?php endif; ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <!-- Conversión para precio normal -->
                                    <?php if ($currency === 'USD'): ?>
                                        <small class="text-muted">≈ Q<?= number_format($product['price_regular'] * $exchangeRate, 2) ?> GTQ</small>
                                    <?php else: ?>
                                        <small class="text-muted">≈ $<?= number_format($product['price_regular'] * $exchangeRate, 2) ?> USD</small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Stock Status -->
                        <div class="mb-4" id="stock-status-container">
                            <?php
                            // Determinar stock a mostrar (variante o producto)
                            $displayStock = isset($product['variant_stock']) ? $product['variant_stock'] : ($product['stock'] ?? 0);
                            ?>
                            <?php if ($displayStock > 0): ?>
                                <div class="stock-status in-stock">
                                    <i class="fas fa-check"></i>
                                    <span id="stock-quantity"><?= $displayStock ?></span> disponibles
                                </div>
                            <?php else: ?>
                                <div class="stock-status out-of-stock">
                                    <i class="fas fa-times"></i>
                                    Agotado
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Product Variants -->
                        <?php if (!empty($variants) && count($variants) > 0): ?>
                        <div class="product-variants mb-4">
                            <div class="variants-section">
                                <div class="variants-header">
                                    <h6 class="variants-title">
                                        <i class="fas fa-palette me-2"></i>Variantes:
                                        <span class="selected-variant-name" id="selected-variant-display">
                                            <?= isset($product['variant_name']) ? esc($product['variant_name']) : 'Selecciona una variante' ?>
                                        </span>
                                    </h6>
                                </div>

                                <div class="variants-grid">
                                    <?php foreach ($variants as $variant): ?>
                                        <div class="variant-option <?= isset($product['selected_variant_id']) && $product['selected_variant_id'] == $variant['id'] ? 'active' : '' ?>"
                                             data-variant-id="<?= $variant['id'] ?>"
                                             data-variant-name="<?= esc($variant['name']) ?>"
                                             data-variant-price-regular="<?= $variant['price_regular'] ?>"
                                             data-variant-price-sale="<?= $variant['price_sale'] ?? '' ?>"
                                             data-variant-stock="<?= $variant['stock_quantity'] ?>"
                                             data-variant-description="<?= esc($variant['description'] ?? '') ?>"
                                             data-variant-image="<?= $variant['image_url'] ?? '' ?>"
                                             data-variant-sku="<?= esc($variant['sku']) ?>"
                                             title="<?= esc($variant['name']) ?>">

                                            <div class="variant-image">
                                                <?php if (!empty($variant['image_url'])): ?>
                                                    <img src="<?= $variant['image_url'] ?>" alt="<?= esc($variant['name']) ?>">
                                                <?php else: ?>
                                                    <div class="variant-placeholder">
                                                        <i class="fas fa-image"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="variant-details">
                                                <div class="variant-name"><?= esc($variant['name']) ?></div>
                                                <div class="variant-price">
                                                    <?php if ($variant['price_sale'] && $variant['price_sale'] < $variant['price_regular']): ?>
                                                        <span class="price-sale"><?= format_currency($variant['price_sale'], $product['currency'] ?? 'GTQ') ?></span>
                                                        <span class="price-regular"><?= format_currency($variant['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                                    <?php else: ?>
                                                        <span class="price-current"><?= format_currency($variant['price_regular'], $product['currency'] ?? 'GTQ') ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="variant-stock">
                                                    <?php if ($variant['stock_quantity'] > 0): ?>
                                                        <span class="stock-available">
                                                            <i class="fas fa-check-circle"></i>
                                                            <?= $variant['stock_quantity'] ?> disponibles
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="stock-unavailable">
                                                            <i class="fas fa-times-circle"></i>
                                                            Agotado
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <div class="variant-overlay">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Descripción de la variante seleccionada -->
                                <div class="selected-variant-info" id="selected-variant-info">
                                    <div class="variant-description-box">
                                        <div class="description-header">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Descripción:</strong>
                                        </div>
                                        <div class="description-content" id="selected-variant-description">
                                            <?= isset($product['variant_description']) ? nl2br(esc($product['variant_description'])) : 'Selecciona una variante para ver su descripción.' ?>
                                        </div>
                                        <div class="description-meta">
                                            <small class="text-muted">
                                                <strong>SKU:</strong> <span id="selected-variant-sku"><?= isset($default_variant['sku']) ? esc($default_variant['sku']) : '' ?></span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Short Description -->
                        <?php if (!empty($product['short_description'])): ?>
                        <div class="product-short-description mb-4">
                            <h5><i class="fas fa-info-circle me-2"></i>Descripción Corta</h5>
                            <p class="text-muted"><?= nl2br(esc($product['short_description'])) ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- Technical Specifications -->
                        <div class="product-specs mb-4">
                            <h5><i class="fas fa-cogs me-2"></i>Especificaciones Técnicas</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>SKU:</strong></td>
                                            <td><?= esc($product['sku'] ?? 'N/A') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Categoría:</strong></td>
                                            <td><?= esc($product['category_name'] ?? 'N/A') ?></td>
                                        </tr>
                                        <?php if (!empty($product['weight']) && $product['weight'] > 0): ?>
                                        <tr>
                                            <td><strong>Peso:</strong></td>
                                            <td>
                                                <?= number_format($product['weight'], 2) ?> kg
                                                <small class="text-muted">(<?= number_format($product['weight'] * 2.20462, 2) ?> lb)</small>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <?php
                                        $hasNewDimensions = !empty($product['dimension_length']) || !empty($product['dimension_width']) || !empty($product['dimension_height']);
                                        if ($hasNewDimensions):
                                            $unit = $product['dimension_unit'] ?? 'cm';
                                        ?>
                                        <tr>
                                            <td><strong>Dimensiones:<br>A x An x F<br>Alto<br>Ancho<br>Fondo</strong></td>
                                            <td>
                                                <?php if (!empty($product['dimension_height'])): ?>
                                                    A: <?= $product['dimension_height'] ?> <?= $unit ?><br>
                                                <?php endif; ?>
                                                <?php if (!empty($product['dimension_width'])): ?>
                                                    An: <?= $product['dimension_width'] ?> <?= $unit ?><br>
                                                <?php endif; ?>
                                                <?php if (!empty($product['dimension_length'])): ?>
                                                    F: <?= $product['dimension_length'] ?> <?= $unit ?>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php elseif (!empty($product['dimensions'])): ?>
                                        <tr>
                                            <td><strong>Dimensiones:</strong></td>
                                            <td><?= esc($product['dimensions']) ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td><strong>Stock:</strong></td>
                                            <td>
                                                <span class="badge <?= ($product['stock_quantity'] ?? 0) > 0 ? 'bg-success' : 'bg-danger' ?>">
                                                    <?= ($product['stock_quantity'] ?? 0) > 0 ? 'Disponible (' . $product['stock_quantity'] . ')' : 'Agotado' ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Full Description -->
                        <?php if (!empty($product['description'])): ?>
                        <div class="product-description mb-4">
                            <h5><i class="fas fa-align-left me-2"></i>Descripción Completa</h5>
                            <div class="description-content">
                                <?= nl2br(esc($product['description'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Add to Cart -->
                        <div class="mb-4">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label fw-semibold">Cantidad:</label>
                                    <div class="quantity-controls d-flex">
                                        <button type="button" class="btn btn-outline-secondary quantity-btn-minus" onclick="decreaseQuantity()" id="btn-decrease">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control quantity-input text-center mx-2" id="quantity" value="1" min="1" max="<?= $product['stock'] ?? 10 ?>" onchange="validateQuantity()" oninput="validateQuantity()">
                                        <button type="button" class="btn btn-outline-secondary quantity-btn-plus" onclick="increaseQuantity()" id="btn-increase">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">Disponible: <?= $product['stock'] ?? 0 ?> unidades</small>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-add-cart w-100" onclick="addToCart(<?= $product['id'] ?>)"
                                            id="add-to-cart-btn"
                                            <?php
                                            $currentStock = isset($product['variant_stock']) ? $product['variant_stock'] : ($product['stock'] ?? 0);
                                            echo ($currentStock <= 0) ? 'disabled' : '';
                                            ?>>
                                        <i class="fas fa-cart-plus me-2"></i>
                                        <?= ($currentStock <= 0) ? 'Agotado' : 'Agregar al Carrito' ?>
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-danger w-100" onclick="toggleWishlist(<?= $product['id'] ?>)"
                                            id="wishlist-btn-<?= $product['id'] ?>" title="Agregar a Lista de Deseos">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
            
            <!-- Product Specifications -->
            <?php if (isset($product['specifications']) && !empty($product['specifications'])): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <h3>Especificaciones</h3>
                        <div class="table-responsive">
                            <table class="table table-striped specifications-table">
                                <?php foreach ($product['specifications'] as $key => $value): ?>
                                    <tr>
                                        <th><?= esc($key) ?></th>
                                        <td><?= esc($value) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Product Reviews Section -->
            <div class="row mt-5 justify-content-center">
                <div class="col-lg-10 col-xl-8">
                    <div class="reviews-section">
                        <div class="text-center mb-5">
                            <h3 class="reviews-title">
                                <i class="fas fa-star text-warning me-2"></i>Reseñas del Producto
                            </h3>
                            <p class="text-muted">Conoce la opinión de otros compradores</p>
                        </div>

                        <!-- Reviews Summary -->
                        <div class="reviews-summary-card mb-4">
                            <div class="row align-items-center g-4">
                                <div class="col-md-5">
                                    <div class="rating-summary text-center">
                                        <div class="rating-circle">
                                            <h2 class="rating-number mb-1" id="average-rating">0.0</h2>
                                            <div class="rating-stars mb-2" id="rating-stars">
                                                <i class="far fa-star text-muted"></i>
                                                <i class="far fa-star text-muted"></i>
                                                <i class="far fa-star text-muted"></i>
                                                <i class="far fa-star text-muted"></i>
                                                <i class="far fa-star text-muted"></i>
                                            </div>
                                            <p class="text-muted mb-0" id="total-reviews">0 reseñas</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="rating-breakdown" id="rating-breakdown">
                                        <!-- Rating breakdown will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Write Review Button -->
                        <div class="text-center mb-4">
                            <button class="btn btn-primary btn-lg write-review-btn" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                <i class="fas fa-edit me-2"></i>Escribir Reseña
                            </button>
                        </div>

                        <!-- Reviews List -->
                        <div class="reviews-container" id="reviews-list">
                            <div class="text-center py-5">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Cargando reseñas...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Products -->
            <?php if (isset($related_products) && !empty($related_products)): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <h3>Productos Relacionados</h3>
                        <div class="row g-4">
                            <?php foreach ($related_products as $relatedProduct): ?>
                                <div class="col-md-3">
                                    <div class="card related-product h-100">
                                        <img src="<?= $relatedProduct['image'] ?? 'https://via.placeholder.com/200x200' ?>"
                                             class="card-img-top"
                                             alt="<?= esc($relatedProduct['name']) ?>">
                                        <div class="card-body">
                                            <h6 class="card-title"><?= esc($relatedProduct['name']) ?></h6>
                                            <p class="text-primary fw-bold"><?= format_currency($relatedProduct['price'], $relatedProduct['currency'] ?? 'GTQ') ?></p>
                                            <a href="<?= base_url('producto/' . $relatedProduct['id']) ?>" class="btn btn-outline-primary btn-sm">
                                                Ver Producto
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
        <!-- Product Not Found -->
        <div class="row">
            <div class="col-12 text-center py-5">
                <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
                <h2>Producto no encontrado</h2>
                <p class="text-muted mb-4">El producto que buscas no existe o ha sido eliminado.</p>
                <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Volver a la Tienda
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">
                    <i class="fas fa-star text-warning me-2"></i>Escribir Reseña
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reviewForm">
                    <div class="mb-3">
                        <label for="customerName" class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="customerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerEmail" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="customerEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="reviewRating" class="form-label">Calificación *</label>
                        <div class="rating-input">
                            <input type="radio" name="rating" value="5" id="star5">
                            <label for="star5"><i class="fas fa-star"></i></label>
                            <input type="radio" name="rating" value="4" id="star4">
                            <label for="star4"><i class="fas fa-star"></i></label>
                            <input type="radio" name="rating" value="3" id="star3">
                            <label for="star3"><i class="fas fa-star"></i></label>
                            <input type="radio" name="rating" value="2" id="star2">
                            <label for="star2"><i class="fas fa-star"></i></label>
                            <input type="radio" name="rating" value="1" id="star1">
                            <label for="star1"><i class="fas fa-star"></i></label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reviewTitle" class="form-label">Título de la reseña *</label>
                        <input type="text" class="form-control" id="reviewTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="reviewComment" class="form-label">Comentario *</label>
                        <textarea class="form-control" id="reviewComment" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="submitReview()">
                    <i class="fas fa-paper-plane me-1"></i>Enviar Reseña
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Change main image
    function changeMainImage(imageSrc) {
        document.getElementById('main-image').src = imageSrc;

        // Update active gallery image
        document.querySelectorAll('.product-gallery img').forEach(img => {
            img.classList.remove('active');
            if (img.src === imageSrc) {
                img.classList.add('active');
            }
        });
    }

    // Hover Zoom with Mouse Movement
    function handleMouseMove(e) {
        const img = e.target;
        const rect = img.getBoundingClientRect();

        // Calcular la posición del mouse relativa a la imagen (0-100%)
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;

        // Limitar los valores para evitar que se salga de los bordes
        const clampedX = Math.max(0, Math.min(100, x));
        const clampedY = Math.max(0, Math.min(100, y));

        // Aplicar el transform-origin para que el zoom siga el mouse
        img.style.transformOrigin = `${clampedX}% ${clampedY}%`;
    }

    function handleMouseLeave(e) {
        const img = e.target;
        // Resetear el transform-origin al centro cuando se quita el mouse
        img.style.transformOrigin = 'center center';
        // Asegurar que la transición sea suave al salir
        img.style.transition = 'transform 0.3s ease-out';
    }

    // Quantity control variables
    const maxStock = <?= $product['stock'] ?? 10 ?>;

    // Increase quantity function
    function increaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);

        if (currentValue < maxStock) {
            quantityInput.value = currentValue + 1;
            validateQuantity();
        }
    }

    // Decrease quantity function
    function decreaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);

        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
            validateQuantity();
        }
    }

    // Validate quantity function
    function validateQuantity() {
        const quantityInput = document.getElementById('quantity');
        const btnDecrease = document.getElementById('btn-decrease');
        const btnIncrease = document.getElementById('btn-increase');
        let currentValue = parseInt(quantityInput.value);

        // Validate input value
        if (isNaN(currentValue) || currentValue < 1) {
            currentValue = 1;
            quantityInput.value = 1;
        } else if (currentValue > maxStock) {
            currentValue = maxStock;
            quantityInput.value = maxStock;
        }

        // Update button states
        btnDecrease.disabled = currentValue <= 1;
        btnIncrease.disabled = currentValue >= maxStock;

        // Add visual feedback
        if (currentValue >= maxStock) {
            quantityInput.classList.add('border-warning');
            btnIncrease.classList.add('btn-outline-warning');
            btnIncrease.classList.remove('btn-outline-secondary');
        } else {
            quantityInput.classList.remove('border-warning');
            btnIncrease.classList.remove('btn-outline-warning');
            btnIncrease.classList.add('btn-outline-secondary');
        }

        if (currentValue <= 1) {
            btnDecrease.classList.add('btn-outline-warning');
            btnDecrease.classList.remove('btn-outline-secondary');
        } else {
            btnDecrease.classList.remove('btn-outline-warning');
            btnDecrease.classList.add('btn-outline-secondary');
        }
    }

    // Add to cart functionality
    function addToCart(productId) {
        const quantity = document.getElementById('quantity').value;
        const button = document.querySelector('.btn-add-cart');

        // Validate quantity before adding to cart
        if (parseInt(quantity) > maxStock) {
            alert(`Solo hay ${maxStock} unidades disponibles en stock.`);
            return;
        }

        // Show loading
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Agregando...';
        button.disabled = true;

        // Simulate API call
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-check me-2"></i>¡Agregado!';
            button.classList.remove('btn-add-cart');
            button.classList.add('btn-success');

            // Update cart count
            let currentCount = parseInt(localStorage.getItem('cart_count') || 0);
            localStorage.setItem('cart_count', currentCount + parseInt(quantity));
            updateCartCount();

            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
                button.classList.remove('btn-success');
                button.classList.add('btn-add-cart');
                button.disabled = false;
            }, 2000);
        }, 1000);
    }

    // =====================================================
    // GESTIÓN DE VARIANTES
    // =====================================================

    let selectedVariantId = <?= isset($product['selected_variant_id']) ? $product['selected_variant_id'] : 'null' ?>;
    let currentProductId = <?= isset($product) ? $product['id'] : 'null' ?>;

    // Inicializar variantes
    document.addEventListener('DOMContentLoaded', function() {
        initializeVariants();
    });

    function initializeVariants() {
        const variantOptions = document.querySelectorAll('.variant-option');

        variantOptions.forEach(option => {
            option.addEventListener('click', function() {
                selectVariant(this);
            });
        });
    }

    function selectVariant(option) {
        // Remover clase active de todas las opciones
        document.querySelectorAll('.variant-option').forEach(opt => {
            opt.classList.remove('active');
        });

        // Agregar clase active a la opción seleccionada
        option.classList.add('active');

        // Obtener datos de la variante
        const variantId = option.getAttribute('data-variant-id');
        const variantName = option.getAttribute('data-variant-name');
        const variantPriceRegular = parseFloat(option.getAttribute('data-variant-price-regular'));
        const variantPriceSale = option.getAttribute('data-variant-price-sale');
        const variantStock = parseInt(option.getAttribute('data-variant-stock'));
        const variantDescription = option.getAttribute('data-variant-description');
        const variantImage = option.getAttribute('data-variant-image');
        const variantSku = option.getAttribute('data-variant-sku');

        // Actualizar variante seleccionada
        selectedVariantId = variantId;

        // Actualizar información en la página
        updateVariantInfo(variantName, variantSku, variantDescription);
        updatePricing(variantPriceRegular, variantPriceSale);
        updateStock(variantStock);
        updateImage(variantImage);
    }

    function updateVariantInfo(name, sku, description) {
        const nameDisplayElement = document.getElementById('selected-variant-display');
        const skuElement = document.getElementById('selected-variant-sku');
        const descriptionElement = document.getElementById('selected-variant-description');

        if (nameDisplayElement) nameDisplayElement.textContent = name;
        if (skuElement) skuElement.textContent = sku;
        if (descriptionElement) {
            descriptionElement.innerHTML = description ? description.replace(/\n/g, '<br>') : 'No hay descripción disponible para esta variante.';
        }
    }

    function updatePricing(priceRegular, priceSale) {
        const currency = '<?= $product['currency'] ?? 'GTQ' ?>';
        const currencySymbol = currency === 'USD' ? '$' : 'Q';

        // Determinar si hay descuento
        const hasDiscount = priceSale && parseFloat(priceSale) > 0 && parseFloat(priceSale) < priceRegular;

        // Actualizar precios en la sección principal
        const priceContainer = document.querySelector('.mb-4 .d-flex.align-items-center.flex-wrap.gap-2');
        if (priceContainer) {
            if (hasDiscount) {
                const discountPercentage = Math.round(((priceRegular - parseFloat(priceSale)) / priceRegular) * 100);
                priceContainer.innerHTML = `
                    <span class="price">${currencySymbol}${parseFloat(priceSale).toLocaleString()}</span>
                    <span class="old-price">${currencySymbol}${priceRegular.toLocaleString()}</span>
                    <span class="discount-badge">-${discountPercentage}%</span>
                `;
            } else {
                priceContainer.innerHTML = `
                    <span class="price">${currencySymbol}${priceRegular.toLocaleString()}</span>
                `;
            }
        }
    }

    function updateStock(stock) {
        const stockContainer = document.getElementById('stock-status-container');
        const stockQuantityElement = document.getElementById('stock-quantity');
        const addToCartBtn = document.getElementById('add-to-cart-btn');

        // Actualizar cantidad en stock
        if (stockQuantityElement) {
            stockQuantityElement.textContent = stock;
        }

        // Actualizar estado del stock
        if (stockContainer) {
            if (stock > 0) {
                stockContainer.innerHTML = `
                    <div class="stock-status in-stock">
                        <i class="fas fa-check"></i>
                        <span id="stock-quantity">${stock}</span> disponibles
                    </div>
                `;
            } else {
                stockContainer.innerHTML = `
                    <div class="stock-status out-of-stock">
                        <i class="fas fa-times"></i>
                        Agotado
                    </div>
                `;
            }
        }

        // Actualizar botón de agregar al carrito
        if (addToCartBtn) {
            if (stock > 0) {
                addToCartBtn.disabled = false;
                addToCartBtn.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
            } else {
                addToCartBtn.disabled = true;
                addToCartBtn.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agotado';
            }
        }

        // Actualizar maxStock para validación de cantidad
        maxStock = stock;

        // Resetear cantidad a 1 si excede el nuevo stock
        const quantityInput = document.getElementById('quantity');
        if (quantityInput && parseInt(quantityInput.value) > stock) {
            quantityInput.value = Math.min(1, stock);
            validateQuantity();
        }
    }

    function updateImage(imageUrl) {
        if (imageUrl) {
            const mainImage = document.getElementById('main-image');
            if (mainImage) {
                mainImage.src = imageUrl;
            }

            // También actualizar la galería si es necesario
            const galleryImages = document.querySelectorAll('.product-gallery img');
            if (galleryImages.length > 0) {
                // Remover clase active de todas las imágenes de galería
                galleryImages.forEach(img => img.classList.remove('active'));

                // Buscar si la imagen de la variante está en la galería
                let foundInGallery = false;
                galleryImages.forEach(img => {
                    if (img.src === imageUrl) {
                        img.classList.add('active');
                        foundInGallery = true;
                    }
                });

                // Si no está en la galería, agregar clase active a la primera imagen
                if (!foundInGallery && galleryImages.length > 0) {
                    galleryImages[0].classList.add('active');
                }
            }
        }
    }

    // Modificar función addToCart para incluir variante seleccionada
    const originalAddToCart = addToCart;
    addToCart = async function(productId) {
        const quantity = document.getElementById('quantity').value;
        const button = document.querySelector('.btn-add-cart');

        // Validate quantity before adding to cart
        if (parseInt(quantity) > maxStock) {
            alert(`Solo hay ${maxStock} unidades disponibles en stock.`);
            return;
        }

        // Show loading
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Agregando...';
        button.disabled = true;

        try {
            // Preparar datos para enviar
            const requestData = {
                product_id: productId,
                quantity: parseInt(quantity)
            };

            // Si hay variante seleccionada, incluirla
            if (selectedVariantId) {
                requestData.variant_id = selectedVariantId;
                console.log('Agregando al carrito - Producto:', productId, 'Variante:', selectedVariantId);
            }

            const response = await fetch('<?= base_url('api/cart/add') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Show success
                button.innerHTML = '<i class="fas fa-check me-2"></i>¡Agregado!';
                button.classList.remove('btn-add-cart');
                button.classList.add('btn-success');

                // Update cart count
                if (window.updateCartCount) {
                    window.updateCartCount();
                }

                // Show success message
                if (window.showCartModal) {
                    window.showCartModal(
                        data.product_name || 'Producto',
                        data.cart_count,
                        data.cart_total
                    );
                } else {
                    alert('Producto agregado al carrito');
                }

                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
                    button.classList.remove('btn-success');
                    button.classList.add('btn-add-cart');
                    button.disabled = false;
                }, 2000);
            } else {
                throw new Error(data.message || 'Error al agregar producto');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            alert('Error al agregar producto: ' + error.message);

            // Reset button
            button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Agregar al Carrito';
            button.disabled = false;
        }
    };

    // Reviews functionality

    // Load reviews when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize quantity controls
        validateQuantity();

        if (currentProductId) {
            loadProductReviews();
        }
    });

    // Load product reviews
    function loadProductReviews() {
        fetch(`/api/reviews/product/${currentProductId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayReviewsSummary(data.data.stats);
                    displayReviewsList(data.data.reviews);
                } else {
                    console.error('Error loading reviews:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading reviews:', error);
                document.getElementById('reviews-list').innerHTML =
                    '<p class="text-danger">Error al cargar las reseñas</p>';
            });
    }

    // Display reviews summary
    function displayReviewsSummary(stats) {
        const averageRating = parseFloat(stats.average_rating || 0);
        const totalReviews = parseInt(stats.total_reviews || 0);

        // Update average rating
        document.getElementById('average-rating').textContent = averageRating.toFixed(1);
        document.getElementById('total-reviews').textContent = `${totalReviews} reseña${totalReviews !== 1 ? 's' : ''}`;

        // Update stars
        const starsContainer = document.getElementById('rating-stars');
        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= Math.floor(averageRating)) {
                starsHtml += '<i class="fas fa-star text-warning"></i>';
            } else if (i <= Math.ceil(averageRating) && averageRating > Math.floor(averageRating)) {
                starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHtml += '<i class="far fa-star text-muted"></i>';
            }
        }
        starsContainer.innerHTML = starsHtml;

        // Update rating breakdown
        const breakdownContainer = document.getElementById('rating-breakdown');
        let breakdownHtml = '';
        for (let i = 5; i >= 1; i--) {
            const count = parseInt(stats[`rating_${i}`] || 0);
            const percentage = parseFloat(stats[`rating_${i}_percent`] || 0);
            breakdownHtml += `
                <div class="d-flex align-items-center mb-1">
                    <span class="me-2">${i} <i class="fas fa-star text-warning"></i></span>
                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: ${percentage}%"></div>
                    </div>
                    <small class="text-muted">${count}</small>
                </div>
            `;
        }
        breakdownContainer.innerHTML = breakdownHtml;
    }

    // Display reviews list
    function displayReviewsList(reviews) {
        const reviewsContainer = document.getElementById('reviews-list');

        if (reviews.length === 0) {
            reviewsContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                    <h5>No hay reseñas aún</h5>
                    <p class="text-muted">Sé el primero en escribir una reseña para este producto.</p>
                </div>
            `;
            return;
        }

        let reviewsHtml = '';
        reviews.forEach(review => {
            reviewsHtml += `
                <div class="review-card">
                    <div class="review-header">
                        <div>
                            <div class="review-author">${review.display_name}</div>
                            <div class="review-date">${review.created_at_formatted}</div>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div class="review-rating">
                                ${review.rating_stars}
                            </div>
                            ${review.is_verified_purchase == '1' ? '<span class="badge bg-success">Compra verificada</span>' : ''}
                        </div>
                    </div>
                    <h6 class="review-title">${review.title}</h6>
                    <p class="review-content mb-0">${review.comment}</p>
                    ${review.admin_response ? `
                        <div class="mt-3 p-3 bg-light rounded">
                            <strong><i class="fas fa-reply me-1"></i>Respuesta del vendedor:</strong>
                            <p class="mb-0 mt-1">${review.admin_response}</p>
                            <small class="text-muted">Respondido el: ${review.admin_response_date}</small>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        reviewsContainer.innerHTML = reviewsHtml;
    }

    // Submit review
    function submitReview() {
        const form = document.getElementById('reviewForm');
        const formData = new FormData(form);

        const reviewData = {
            product_id: currentProductId,
            customer_name: document.getElementById('customerName').value,
            customer_email: document.getElementById('customerEmail').value,
            rating: document.querySelector('input[name="rating"]:checked')?.value,
            title: document.getElementById('reviewTitle').value,
            comment: document.getElementById('reviewComment').value
        };

        // Validate
        if (!reviewData.customer_name || !reviewData.customer_email || !reviewData.rating ||
            !reviewData.title || !reviewData.comment) {
            alert('Por favor completa todos los campos requeridos');
            return;
        }

        // Submit
        fetch('/api/reviews', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(reviewData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('¡Reseña enviada exitosamente! Será publicada después de la moderación.');
                bootstrap.Modal.getInstance(document.getElementById('reviewModal')).hide();
                form.reset();
                // Clear rating selection
                document.querySelectorAll('input[name="rating"]').forEach(input => input.checked = false);
            } else {
                alert('Error al enviar la reseña: ' + (data.message || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error submitting review:', error);
            alert('Error al enviar la reseña');
        });
    }

    // Wishlist functionality
    function toggleWishlist(productId) {
        const button = document.getElementById(`wishlist-btn-${productId}`);
        const icon = button.querySelector('i');

        // Check if user is logged in
        <?php if (!session()->get('user_id')): ?>
            alert('Debes iniciar sesión para usar la lista de deseos');
            window.location.href = '<?= base_url('login') ?>';
            return;
        <?php endif; ?>

        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        fetch('<?= base_url('api/wishlist/toggle') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.action === 'added') {
                    icon.className = 'fas fa-heart';
                    button.classList.remove('btn-outline-danger');
                    button.classList.add('btn-danger');
                    button.title = 'Quitar de Lista de Deseos';
                } else {
                    icon.className = 'far fa-heart';
                    button.classList.remove('btn-danger');
                    button.classList.add('btn-outline-danger');
                    button.title = 'Agregar a Lista de Deseos';
                }

                // Show success message
                showNotification(data.message, 'success');
            } else {
                showNotification(data.error || 'Error al actualizar lista de deseos', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error de conexión', 'error');
        })
        .finally(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
</script>

<style>
    .rating-input {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .rating-input input {
        display: none;
    }

    .rating-input label {
        cursor: pointer;
        color: #ddd;
        font-size: 1.5rem;
        margin-right: 5px;
        transition: color 0.3s;
    }

    .rating-input label:hover,
    .rating-input label:hover ~ label,
    .rating-input input:checked ~ label {
        color: #ffc107;
    }

    /* Estilos para especificaciones técnicas */
    .product-specs {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border-left: 4px solid #007bff;
    }

    .product-specs h5 {
        color: #007bff;
        margin-bottom: 15px;
    }

    .product-specs table td {
        padding: 8px 0;
        vertical-align: top;
    }

    .product-specs table td:first-child {
        width: 40%;
        color: #6c757d;
    }

    .product-specs table td:last-child {
        font-weight: 500;
    }

    /* Estilos para descripciones */
    .product-short-description {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        border-left: 4px solid #2196f3;
    }

    .product-short-description h5 {
        color: #1976d2;
        margin-bottom: 10px;
    }

    .product-description {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
    }

    .product-description h5 {
        color: #495057;
        margin-bottom: 15px;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
    }

    .description-content {
        line-height: 1.6;
        color: #6c757d;
    }

    /* Badges mejorados */
    .badge {
        font-size: 0.85em;
        padding: 6px 12px;
    }

    /* =====================================================
       ESTILOS PARA VARIANTES DE PRODUCTOS - ESTILO ALIEXPRESS
       ===================================================== */

    .product-variants {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #f0f0f0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .variants-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .variants-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .variants-title i {
        color: #ff6b35;
    }

    .selected-variant-name {
        color: #ff6b35;
        font-weight: 700;
        margin-left: 8px;
    }

    .variants-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
        margin-bottom: 20px;
    }

    .variant-option {
        background: #fff;
        border: 2px solid #e8e8e8;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: 12px;
        min-height: 100px;
    }

    .variant-option:hover {
        border-color: #ff6b35;
        box-shadow: 0 4px 20px rgba(255, 107, 53, 0.15);
        transform: translateY(-2px);
    }

    .variant-option.active {
        border-color: #ff6b35;
        background: linear-gradient(135deg, #fff5f2, #fff);
        box-shadow: 0 6px 25px rgba(255, 107, 53, 0.2);
        transform: translateY(-2px);
    }

    .variant-image {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .variant-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .variant-option:hover .variant-image img {
        transform: scale(1.1);
    }

    .variant-placeholder {
        color: #ccc;
        font-size: 24px;
    }

    .variant-details {
        flex: 1;
        min-width: 0;
    }

    .variant-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.3;
    }

    .variant-price {
        margin-bottom: 6px;
    }

    .price-sale {
        color: #ff6b35;
        font-weight: 700;
        font-size: 16px;
        margin-right: 8px;
    }

    .price-regular {
        color: #999;
        text-decoration: line-through;
        font-size: 14px;
    }

    .price-current {
        color: #ff6b35;
        font-weight: 700;
        font-size: 16px;
    }

    .variant-stock {
        font-size: 12px;
    }

    .stock-available {
        color: #52c41a;
        font-weight: 500;
    }

    .stock-available i {
        margin-right: 4px;
    }

    .stock-unavailable {
        color: #ff4d4f;
        font-weight: 500;
    }

    .stock-unavailable i {
        margin-right: 4px;
    }

    .variant-overlay {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        background: #ff6b35;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        opacity: 0;
        transform: scale(0);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .variant-option.active .variant-overlay {
        opacity: 1;
        transform: scale(1);
    }

    .selected-variant-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        border-left: 4px solid #ff6b35;
    }

    .variant-description-box {

    }

    .description-header {
        color: #333;
        font-weight: 600;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .description-header i {
        color: #ff6b35;
    }

    .description-content {
        color: #666;
        line-height: 1.6;
        margin-bottom: 12px;
        font-size: 14px;
    }

    .description-meta {
        padding-top: 8px;
        border-top: 1px solid #e8e8e8;
    }

    /* Animaciones */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .selected-variant-info {
        animation: slideInUp 0.4s ease;
    }

    /* Efectos de shimmer */
    .variant-option::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s;
    }

    .variant-option:hover::before {
        left: 100%;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .product-specs .row {
            flex-direction: column;
        }

        .product-specs .col-md-6:first-child {
            margin-bottom: 15px;
        }

        .product-variants {
            padding: 16px;
            margin: 0 -15px;
            border-radius: 0;
            border-left: none;
            border-right: none;
        }

        .variants-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .variant-option {
            padding: 12px;
            min-height: 80px;
        }

        .variant-image {
            width: 50px;
            height: 50px;
        }

        .variant-name {
            font-size: 15px;
        }

        .price-sale, .price-current {
            font-size: 15px;
        }

        .price-regular {
            font-size: 13px;
        }

        .selected-variant-info {
            padding: 12px;
        }

        .description-content {
            font-size: 13px;
        }
    }

    @media (max-width: 576px) {
        .variants-title {
            font-size: 15px;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }

        .selected-variant-name {
            margin-left: 0;
        }

        .variant-option {
            gap: 10px;
        }

        .variant-image {
            width: 45px;
            height: 45px;
        }

        .variant-name {
            font-size: 14px;
        }

        .variant-stock {
            font-size: 11px;
        }
    }

    @media (min-width: 1200px) {
        .variants-grid {
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        }

        .variant-option {
            padding: 20px;
            min-height: 110px;
        }

        .variant-image {
            width: 70px;
            height: 70px;
        }
    }
</style>
<?= $this->endSection() ?>
