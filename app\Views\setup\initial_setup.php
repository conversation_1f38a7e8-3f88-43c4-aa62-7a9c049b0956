<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .setup-body {
            padding: 30px;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .step-item.pending {
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        
        .step-item.running {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .step-item.success {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .step-item.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .step-number.pending {
            background: #6c757d;
            color: white;
        }
        
        .step-number.running {
            background: #ffc107;
            color: #212529;
        }
        
        .step-number.success {
            background: #28a745;
            color: white;
        }
        
        .step-number.error {
            background: #dc3545;
            color: white;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-badge.success {
            background: #28a745;
            color: white;
        }
        
        .status-badge.error {
            background: #dc3545;
            color: white;
        }
        
        .status-badge.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <!-- Header -->
            <div class="setup-header">
                <h1><i class="fas fa-rocket"></i> MrCell Guatemala</h1>
                <h3>Configuración Inicial del Sistema</h3>
                <p class="mb-0">Configurando tu tienda inteligente con automatizaciones avanzadas</p>
            </div>
            
            <!-- Body -->
            <div class="setup-body">
                <!-- Estado del Sistema -->
                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-database fa-2x <?= $system_status['database'] ? 'text-success' : 'text-danger' ?>"></i>
                        </div>
                        <h6>Base de Datos</h6>
                        <span class="status-badge <?= $system_status['database'] ? 'success' : 'error' ?>">
                            <?= $system_status['database'] ? 'Conectada' : 'Error' ?>
                        </span>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-table fa-2x <?= $system_status['tables'] ? 'text-success' : 'text-warning' ?>"></i>
                        </div>
                        <h6>Tablas</h6>
                        <span class="status-badge <?= $system_status['tables'] ? 'success' : 'warning' ?>">
                            <?= $system_status['tables'] ? 'Completas' : 'Verificando' ?>
                        </span>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-cog fa-2x <?= $system_status['settings'] ? 'text-success' : 'text-warning' ?>"></i>
                        </div>
                        <h6>Configuraciones</h6>
                        <span class="status-badge <?= $system_status['settings'] ? 'success' : 'warning' ?>">
                            <?= $system_status['settings'] ? 'Listas' : 'Pendientes' ?>
                        </span>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-comments fa-2x <?= $system_status['templates'] ? 'text-success' : 'text-warning' ?>"></i>
                        </div>
                        <h6>Templates</h6>
                        <span class="status-badge <?= $system_status['templates'] ? 'success' : 'warning' ?>">
                            <?= $system_status['templates'] ? 'Creados' : 'Pendientes' ?>
                        </span>
                    </div>
                </div>
                
                <!-- Progreso General -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5>Progreso de Configuración</h5>
                        <span id="progress-text">0%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar progress-bar-striped" id="progress-bar" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Pasos de Configuración -->
                <div class="mb-4">
                    <h5>Pasos de Configuración</h5>
                    <div id="setup-steps">
                        <?php foreach ($configuration_steps as $step): ?>
                            <div class="step-item pending" id="step-<?= $step['step'] ?>">
                                <div class="step-number pending" id="step-number-<?= $step['step'] ?>">
                                    <?= $step['step'] ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= $step['title'] ?></h6>
                                    <p class="mb-0 text-muted"><?= $step['description'] ?></p>
                                </div>
                                <div class="step-status" id="step-status-<?= $step['step'] ?>">
                                    <i class="fas fa-clock text-muted"></i>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Botones de Acción -->
                <div class="text-center">
                    <button class="btn btn-primary btn-lg px-5" id="start-setup" onclick="startSetup()">
                        <i class="fas fa-play"></i> Iniciar Configuración
                    </button>
                    
                    <button class="btn btn-success btn-lg px-5" id="continue-setup" style="display: none;" onclick="continueToAdmin()">
                        <i class="fas fa-arrow-right"></i> Continuar al Panel
                    </button>
                </div>
                
                <!-- Log de Resultados -->
                <div class="mt-4" id="setup-log" style="display: none;">
                    <h6>Log de Configuración</h6>
                    <div class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                        <pre id="log-content"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStep = 0;
        const totalSteps = <?= count($configuration_steps) ?>;
        
        function startSetup() {
            document.getElementById('start-setup').disabled = true;
            document.getElementById('start-setup').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Configurando...';
            
            // Mostrar log
            document.getElementById('setup-log').style.display = 'block';
            
            // Iniciar configuración
            fetch('<?= base_url('setup/run-setup') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    processSetupResults(data.results);
                    showSuccess();
                } else {
                    showError(data.error);
                }
            })
            .catch(error => {
                showError('Error de conexión: ' + error.message);
            });
            
            // Simular progreso paso a paso
            simulateProgress();
        }
        
        function simulateProgress() {
            const steps = [
                'Verificando base de datos...',
                'Insertando configuraciones...',
                'Creando templates de WhatsApp...',
                'Configurando sistema móvil...',
                'Finalizando configuración...'
            ];
            
            let stepIndex = 0;
            
            const interval = setInterval(() => {
                if (stepIndex < steps.length) {
                    updateStep(stepIndex + 1, 'running', steps[stepIndex]);
                    updateProgress((stepIndex + 1) / totalSteps * 100);
                    
                    setTimeout(() => {
                        updateStep(stepIndex + 1, 'success', 'Completado');
                    }, 1000);
                    
                    stepIndex++;
                } else {
                    clearInterval(interval);
                }
            }, 1500);
        }
        
        function updateStep(stepNumber, status, message) {
            const stepElement = document.getElementById('step-' + stepNumber);
            const stepNumberElement = document.getElementById('step-number-' + stepNumber);
            const stepStatusElement = document.getElementById('step-status-' + stepNumber);
            
            // Remover clases anteriores
            stepElement.className = 'step-item ' + status;
            stepNumberElement.className = 'step-number ' + status;
            
            // Actualizar icono de estado
            let icon = '';
            switch (status) {
                case 'running':
                    icon = '<i class="fas fa-spinner fa-spin text-warning"></i>';
                    break;
                case 'success':
                    icon = '<i class="fas fa-check text-success"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-times text-danger"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-clock text-muted"></i>';
            }
            
            stepStatusElement.innerHTML = icon;
            
            // Actualizar log
            if (message) {
                const logContent = document.getElementById('log-content');
                logContent.textContent += `[${new Date().toLocaleTimeString()}] Paso ${stepNumber}: ${message}\n`;
                logContent.scrollTop = logContent.scrollHeight;
            }
        }
        
        function updateProgress(percentage) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = Math.round(percentage) + '%';
            
            if (percentage >= 100) {
                progressBar.classList.remove('progress-bar-striped');
                progressBar.classList.add('bg-success');
            }
        }
        
        function processSetupResults(results) {
            const logContent = document.getElementById('log-content');
            
            // Procesar resultados de tablas
            if (results.tables) {
                const tableCount = Object.values(results.tables).filter(exists => exists).length;
                logContent.textContent += `[${new Date().toLocaleTimeString()}] Tablas verificadas: ${tableCount}/<?= count($required_tables) ?>\n`;
            }
            
            // Procesar configuraciones
            if (results.settings) {
                logContent.textContent += `[${new Date().toLocaleTimeString()}] Configuraciones insertadas: ${results.settings.inserted}/${results.settings.total}\n`;
            }
            
            // Procesar templates
            if (results.templates) {
                logContent.textContent += `[${new Date().toLocaleTimeString()}] Templates creados: ${results.templates.inserted}/${results.templates.total}\n`;
            }
            
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function showSuccess() {
            updateProgress(100);
            
            document.getElementById('start-setup').style.display = 'none';
            document.getElementById('continue-setup').style.display = 'inline-block';
            
            const logContent = document.getElementById('log-content');
            logContent.textContent += `\n[${new Date().toLocaleTimeString()}] ✅ ¡Configuración completada exitosamente!\n`;
            logContent.textContent += `[${new Date().toLocaleTimeString()}] 🎉 El sistema está listo para usar.\n`;
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function showError(error) {
            const logContent = document.getElementById('log-content');
            logContent.textContent += `\n[${new Date().toLocaleTimeString()}] ❌ Error: ${error}\n`;
            logContent.scrollTop = logContent.scrollHeight;
            
            document.getElementById('start-setup').disabled = false;
            document.getElementById('start-setup').innerHTML = '<i class="fas fa-redo"></i> Reintentar Configuración';
        }
        
        function continueToAdmin() {
            window.location.href = '<?= base_url('admin/automation') ?>';
        }
    </script>
</body>
</html>
