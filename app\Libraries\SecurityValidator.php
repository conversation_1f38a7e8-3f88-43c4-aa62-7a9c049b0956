<?php

namespace App\Libraries;

use Config\Database;
use Config\Services;

class SecurityValidator
{
    protected $db;
    protected $cache;
    
    public function __construct()
    {
        $this->db = Database::connect();
        $this->cache = Services::cache();
    }

    /**
     * Validar cupón con verificaciones de seguridad
     */
    public function validateCouponSecurity(string $code, int $userId = null, string $sessionId = null): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'warnings' => []
        ];

        // 1. Validar formato del código
        if (!$this->validateCouponFormat($code)) {
            $result['valid'] = false;
            $result['errors'][] = 'Formato de cupón inválido';
            return $result;
        }

        // 2. Verificar intentos de fuerza bruta
        if (!$this->checkCouponBruteForce($sessionId ?? request()->getIPAddress())) {
            $result['valid'] = false;
            $result['errors'][] = 'Demasiados intentos de validación. Intenta más tarde.';
            return $result;
        }

        // 3. Verificar si el cupón existe y está activo
        $coupon = $this->db->table('coupons')
                          ->where('code', $code)
                          ->where('is_active', 1)
                          ->get()
                          ->getRowArray();

        if (!$coupon) {
            $this->recordCouponAttempt($code, $userId, $sessionId, 'not_found');
            $result['valid'] = false;
            $result['errors'][] = 'Cupón no encontrado o inactivo';
            return $result;
        }

        // 4. Verificar fechas de validez
        $now = date('Y-m-d H:i:s');
        if ($coupon['valid_from'] && $coupon['valid_from'] > $now) {
            $result['valid'] = false;
            $result['errors'][] = 'Cupón aún no válido';
            return $result;
        }

        if ($coupon['valid_until'] && $coupon['valid_until'] < $now) {
            $result['valid'] = false;
            $result['errors'][] = 'Cupón expirado';
            return $result;
        }

        // 5. Verificar límites de uso
        if (!$this->checkCouponUsageLimits($coupon, $userId)) {
            $result['valid'] = false;
            $result['errors'][] = 'Límite de uso del cupón excedido';
            return $result;
        }

        // 6. Verificar patrones sospechosos de uso
        $suspiciousActivity = $this->detectSuspiciousCouponActivity($code, $userId, $sessionId);
        if ($suspiciousActivity) {
            $result['warnings'][] = 'Actividad sospechosa detectada';
            $this->logSecurityEvent('suspicious_coupon_activity', [
                'coupon_code' => $code,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'activity' => $suspiciousActivity
            ]);
        }

        return $result;
    }

    /**
     * Validar datos de seguimiento con verificaciones de seguridad
     */
    public function validateTrackingRequest(string $trackingNumber, string $company = null): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'warnings' => []
        ];

        // 1. Validar formato del número de seguimiento
        if (!$this->validateTrackingFormat($trackingNumber)) {
            $result['valid'] = false;
            $result['errors'][] = 'Formato de número de seguimiento inválido';
            return $result;
        }

        // 2. Verificar rate limiting específico para tracking
        $ip = request()->getIPAddress();
        if (!$this->checkTrackingRateLimit($ip)) {
            $result['valid'] = false;
            $result['errors'][] = 'Demasiadas consultas de seguimiento. Espera un momento.';
            return $result;
        }

        // 3. Verificar si la empresa es válida
        if ($company && !$this->isValidShippingCompany($company)) {
            $result['valid'] = false;
            $result['errors'][] = 'Empresa de envío no válida';
            return $result;
        }

        // 4. Detectar patrones de scraping
        if ($this->detectTrackingScraping($ip, $trackingNumber)) {
            $result['warnings'][] = 'Posible actividad de scraping detectada';
            $this->logSecurityEvent('tracking_scraping_detected', [
                'tracking_number' => $trackingNumber,
                'company' => $company,
                'ip' => $ip
            ]);
        }

        return $result;
    }

    /**
     * Validar formato de código de cupón
     */
    private function validateCouponFormat(string $code): bool
    {
        // Longitud entre 3 y 50 caracteres
        if (strlen($code) < 3 || strlen($code) > 50) {
            return false;
        }

        // Solo letras, números, guiones y guiones bajos
        if (!preg_match('/^[A-Z0-9_-]+$/i', $code)) {
            return false;
        }

        // No debe contener patrones sospechosos
        $suspiciousPatterns = [
            '/script/i',
            '/select/i',
            '/union/i',
            '/drop/i',
            '/delete/i',
            '/insert/i',
            '/update/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $code)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Verificar intentos de fuerza bruta en cupones
     */
    private function checkCouponBruteForce(string $identifier): bool
    {
        $cacheKey = "coupon_attempts_" . md5($identifier);
        $attempts = $this->cache->get($cacheKey) ?? [];
        $now = time();

        // Limpiar intentos antiguos (últimos 15 minutos)
        $attempts = array_filter($attempts, function($timestamp) use ($now) {
            return ($now - $timestamp) < 900;
        });

        // Verificar límite (máximo 10 intentos en 15 minutos)
        if (count($attempts) >= 10) {
            return false;
        }

        // Agregar intento actual
        $attempts[] = $now;
        $this->cache->save($cacheKey, $attempts, 900);

        return true;
    }

    /**
     * Registrar intento de cupón
     */
    private function recordCouponAttempt(string $code, int $userId = null, string $sessionId = null, string $result = 'success'): void
    {
        try {
            $this->db->table('coupon_attempts')->insert([
                'code' => $code,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'ip_address' => request()->getIPAddress(),
                'user_agent' => request()->getUserAgent()->getAgentString(),
                'result' => $result,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Si la tabla no existe, solo log
            log_message('error', 'Could not record coupon attempt: ' . $e->getMessage());
        }
    }

    /**
     * Verificar límites de uso del cupón
     */
    private function checkCouponUsageLimits(array $coupon, int $userId = null): bool
    {
        // Verificar límite global
        if ($coupon['usage_limit']) {
            $totalUsage = $this->db->table('coupon_usage')
                                  ->where('coupon_id', $coupon['id'])
                                  ->countAllResults();
            
            if ($totalUsage >= $coupon['usage_limit']) {
                return false;
            }
        }

        // Verificar límite por usuario
        if ($userId && $coupon['usage_limit_per_user']) {
            $userUsage = $this->db->table('coupon_usage')
                                 ->where('coupon_id', $coupon['id'])
                                 ->where('user_id', $userId)
                                 ->countAllResults();
            
            if ($userUsage >= $coupon['usage_limit_per_user']) {
                return false;
            }
        }

        return true;
    }

    /**
     * Detectar actividad sospechosa con cupones
     */
    private function detectSuspiciousCouponActivity(string $code, int $userId = null, string $sessionId = null): ?string
    {
        $ip = request()->getIPAddress();
        
        // Verificar múltiples códigos desde la misma IP en poco tiempo
        $recentAttempts = $this->db->table('coupon_attempts')
                                  ->where('ip_address', $ip)
                                  ->where('created_at >', date('Y-m-d H:i:s', strtotime('-5 minutes')))
                                  ->countAllResults();
        
        if ($recentAttempts > 20) {
            return 'high_frequency_attempts';
        }

        // Verificar patrones de códigos secuenciales
        $recentCodes = $this->db->table('coupon_attempts')
                               ->select('code')
                               ->where('ip_address', $ip)
                               ->where('created_at >', date('Y-m-d H:i:s', strtotime('-1 hour')))
                               ->get()
                               ->getResultArray();
        
        if ($this->detectSequentialPatterns($recentCodes)) {
            return 'sequential_code_attempts';
        }

        return null;
    }

    /**
     * Validar formato de número de seguimiento
     */
    private function validateTrackingFormat(string $trackingNumber): bool
    {
        // Longitud entre 5 y 50 caracteres
        if (strlen($trackingNumber) < 5 || strlen($trackingNumber) > 50) {
            return false;
        }

        // Solo letras, números y algunos caracteres especiales
        if (!preg_match('/^[A-Z0-9\-_\.]+$/i', $trackingNumber)) {
            return false;
        }

        return true;
    }

    /**
     * Verificar rate limiting específico para tracking
     */
    private function checkTrackingRateLimit(string $ip): bool
    {
        $cacheKey = "tracking_requests_" . md5($ip);
        $requests = $this->cache->get($cacheKey) ?? [];
        $now = time();

        // Limpiar solicitudes antiguas (últimos 10 minutos)
        $requests = array_filter($requests, function($timestamp) use ($now) {
            return ($now - $timestamp) < 600;
        });

        // Verificar límite (máximo 30 solicitudes en 10 minutos)
        if (count($requests) >= 30) {
            return false;
        }

        // Agregar solicitud actual
        $requests[] = $now;
        $this->cache->save($cacheKey, $requests, 600);

        return true;
    }

    /**
     * Verificar si la empresa de envío es válida
     */
    private function isValidShippingCompany(string $company): bool
    {
        $validCompanies = $this->cache->get('valid_shipping_companies');
        
        if ($validCompanies === null) {
            $validCompanies = $this->db->table('shipping_companies')
                                      ->select('code')
                                      ->where('is_active', 1)
                                      ->get()
                                      ->getResultArray();
            
            $validCompanies = array_column($validCompanies, 'code');
            $this->cache->save('valid_shipping_companies', $validCompanies, 3600);
        }

        return in_array($company, $validCompanies);
    }

    /**
     * Detectar scraping de tracking
     */
    private function detectTrackingScraping(string $ip, string $trackingNumber): bool
    {
        // Verificar múltiples números de seguimiento diferentes desde la misma IP
        $uniqueNumbers = $this->db->table('tracking_requests')
                                 ->selectCount('DISTINCT tracking_number', 'unique_count')
                                 ->where('ip_address', $ip)
                                 ->where('created_at >', date('Y-m-d H:i:s', strtotime('-1 hour')))
                                 ->get()
                                 ->getRow();

        if ($uniqueNumbers && $uniqueNumbers->unique_count > 50) {
            return true;
        }

        // Verificar patrones secuenciales en números de seguimiento
        $recentNumbers = $this->db->table('tracking_requests')
                                 ->select('tracking_number')
                                 ->where('ip_address', $ip)
                                 ->where('created_at >', date('Y-m-d H:i:s', strtotime('-30 minutes')))
                                 ->orderBy('created_at', 'ASC')
                                 ->get()
                                 ->getResultArray();

        return $this->detectSequentialTrackingNumbers($recentNumbers);
    }

    /**
     * Detectar patrones secuenciales en códigos
     */
    private function detectSequentialPatterns(array $codes): bool
    {
        if (count($codes) < 5) {
            return false;
        }

        $codeValues = array_column($codes, 'code');
        $sequential = 0;

        for ($i = 1; $i < count($codeValues); $i++) {
            // Verificar si los códigos son secuenciales numéricamente
            if (is_numeric(substr($codeValues[$i], -1)) && is_numeric(substr($codeValues[$i-1], -1))) {
                $current = (int)substr($codeValues[$i], -1);
                $previous = (int)substr($codeValues[$i-1], -1);
                
                if ($current === $previous + 1) {
                    $sequential++;
                }
            }
        }

        // Si más del 70% son secuenciales, es sospechoso
        return ($sequential / count($codeValues)) > 0.7;
    }

    /**
     * Detectar números de seguimiento secuenciales
     */
    private function detectSequentialTrackingNumbers(array $numbers): bool
    {
        if (count($numbers) < 10) {
            return false;
        }

        $trackingNumbers = array_column($numbers, 'tracking_number');
        $sequential = 0;

        for ($i = 1; $i < count($trackingNumbers); $i++) {
            // Extraer parte numérica del número de seguimiento
            preg_match('/(\d+)/', $trackingNumbers[$i], $currentMatches);
            preg_match('/(\d+)/', $trackingNumbers[$i-1], $previousMatches);
            
            if (!empty($currentMatches) && !empty($previousMatches)) {
                $current = (int)$currentMatches[1];
                $previous = (int)$previousMatches[1];
                
                if ($current === $previous + 1) {
                    $sequential++;
                }
            }
        }

        // Si más del 60% son secuenciales, es sospechoso
        return ($sequential / count($trackingNumbers)) > 0.6;
    }

    /**
     * Log de eventos de seguridad
     */
    private function logSecurityEvent(string $type, array $data): void
    {
        $logData = array_merge($data, [
            'type' => $type,
            'ip' => request()->getIPAddress(),
            'user_agent' => request()->getUserAgent()->getAgentString(),
            'url' => (string)request()->getUri(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        log_message('warning', "Security validation event: {$type}", $logData);

        // Intentar guardar en base de datos
        try {
            $this->db->table('security_events')->insert([
                'event_type' => $type,
                'severity' => 'medium',
                'ip_address' => $logData['ip'],
                'user_agent' => $logData['user_agent'],
                'url' => $logData['url'],
                'event_data' => json_encode($data),
                'created_at' => $logData['timestamp']
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Could not save security event: ' . $e->getMessage());
        }
    }
}
