<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-user me-2"></i>Detalle del Usuario</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/admin/users">Usuarios</a></li>
                    <li class="breadcrumb-item active"><?= esc($user['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/admin/users" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Volver
            </a>
            <a href="/admin/users/edit/<?= $user['id'] ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Editar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Información del Usuario -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Información Personal</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <div class="user-avatar mb-3" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 2rem;">
                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Nombre:</strong></td>
                                <td><?= esc($user['name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?= esc($user['email']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Teléfono:</strong></td>
                                <td><?= esc($user['phone'] ?? 'No especificado') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Estado:</strong></td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                        <span class="badge bg-success">Activo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactivo</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Fecha de Registro:</strong></td>
                                <td><?= date('d/m/Y H:i', strtotime($user['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Última Actualización:</strong></td>
                                <td><?= $user['updated_at'] ? date('d/m/Y H:i', strtotime($user['updated_at'])) : 'Nunca' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles del Usuario -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>Roles Asignados</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($user_roles)): ?>
                    <div class="row">
                        <?php foreach ($user_roles as $role): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= esc($role['name']) ?></h6>
                                        <?php if ($role['description']): ?>
                                            <p class="card-text text-muted"><?= esc($role['description']) ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted">No tiene roles asignados</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Actividad Reciente -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Actividad Reciente</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($activity)): ?>
                    <div class="timeline">
                        <?php foreach ($activity as $item): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1"><?= esc($item['action']) ?></h6>
                                    <small class="text-muted">
                                        <?= esc($item['module']) ?> • 
                                        <?= date('d/m/Y H:i', strtotime($item['created_at'])) ?>
                                        <?php if (isset($item['ip_address'])): ?>
                                            • IP: <?= esc($item['ip_address']) ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted">No hay actividad registrada</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Estadísticas -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Estadísticas</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-3 mb-3">
                            <h4 class="text-primary mb-1"><?= $stats['total_orders'] ?? 0 ?></h4>
                            <small class="text-muted">Pedidos</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3 mb-3">
                            <h4 class="text-success mb-1">Q<?= number_format($stats['total_spent'] ?? 0, 2) ?></h4>
                            <small class="text-muted">Gastado</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <h6>Información de Sesión</h6>
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Último Login:</strong></td>
                        <td><?= isset($stats['last_login']) ? date('d/m/Y H:i', strtotime($stats['last_login'])) : 'Nunca' ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/users/edit/<?= $user['id'] ?>" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Editar Usuario
                    </a>
                    <button class="btn btn-outline-info" onclick="showUserActivity(<?= $user['id'] ?>)">
                        <i class="fas fa-history me-2"></i>Ver Actividad Completa
                    </button>
                    <button class="btn btn-outline-warning" onclick="resetPassword(<?= $user['id'] ?>)">
                        <i class="fas fa-key me-2"></i>Resetear Contraseña
                    </button>
                    <?php if ($user['is_active']): ?>
                        <button class="btn btn-outline-warning" onclick="toggleUserStatus(<?= $user['id'] ?>, false)">
                            <i class="fas fa-user-slash me-2"></i>Desactivar Usuario
                        </button>
                    <?php else: ?>
                        <button class="btn btn-outline-success" onclick="toggleUserStatus(<?= $user['id'] ?>, true)">
                            <i class="fas fa-user-check me-2"></i>Activar Usuario
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Elegir Tipo de Reseteo -->
<div class="modal fade" id="resetPasswordTypeModal" tabindex="-1" aria-labelledby="resetPasswordTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="resetPasswordTypeModalLabel">
                    <i class="fas fa-key me-2"></i>Resetear Contraseña
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-user-lock fa-3x text-warning mb-3"></i>
                    <h6>¿Cómo deseas resetear la contraseña?</h6>
                    <p class="text-muted small">Elige el método que prefieras para establecer la nueva contraseña.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-info" id="autoPasswordBtn">
                    <i class="fas fa-random me-2"></i>Contraseña Automática
                </button>
                <button type="button" class="btn btn-warning" id="manualPasswordBtn">
                    <i class="fas fa-edit me-2"></i>Contraseña Manual
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Contraseña Manual -->
<div class="modal fade" id="manualPasswordModal" tabindex="-1" aria-labelledby="manualPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="manualPasswordModalLabel">
                    <i class="fas fa-edit me-2"></i>Establecer Contraseña Manual
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newPassword" class="form-label">Nueva Contraseña</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="newPassword" placeholder="Ingrese la nueva contraseña" minlength="6">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">La contraseña debe tener al menos 6 caracteres.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmManualPasswordBtn">
                    <i class="fas fa-check me-2"></i>Establecer Contraseña
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="resultModalHeader">
                <h5 class="modal-title" id="resultModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Resultado
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="resultModalBody">
                    <!-- Contenido dinámico -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>Entendido
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación para Cambiar Estado -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1" aria-labelledby="toggleStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="toggleStatusModalHeader">
                <h5 class="modal-title" id="toggleStatusModalLabel">
                    <i class="fas fa-user-check me-2"></i>Cambiar Estado
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="toggleStatusModalBody">
                    <!-- Contenido dinámico -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmToggleStatusBtn">
                    <i class="fas fa-check me-2"></i>Confirmar
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function showUserActivity(userId) {
    // Crear modal dinámicamente
    const modalHtml = `
        <div class="modal fade" id="activityModal" tabindex="-1" aria-labelledby="activityModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activityModalLabel">
                            <i class="fas fa-history me-2"></i>Actividad Completa del Usuario
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="activityContent">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Cargando...</span>
                                </div>
                                <p class="mt-2">Cargando actividad...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Agregar modal al DOM si no existe
    if (!document.getElementById('activityModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('activityModal'));
    modal.show();

    // Cargar actividad
    fetch(`/admin/users/activity/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUserActivity(data.data);
            } else {
                document.getElementById('activityContent').innerHTML =
                    '<div class="alert alert-danger">Error al cargar la actividad</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('activityContent').innerHTML =
                '<div class="alert alert-danger">Error de conexión</div>';
        });
}

function displayUserActivity(activities) {
    let html = '';

    if (activities.length === 0) {
        html = '<div class="alert alert-info">No hay actividad registrada para este usuario</div>';
    } else {
        html = '<div class="timeline">';
        activities.forEach(activity => {
            html += `
                <div class="timeline-item">
                    <div class="timeline-marker bg-primary"></div>
                    <div class="timeline-content">
                        <h6 class="mb-1">${activity.action}</h6>
                        <p class="mb-1">${activity.description || 'Sin descripción'}</p>
                        <small class="text-muted">
                            ${activity.module} • ${activity.formatted_date}
                            ${activity.ip_address ? ' • IP: ' + activity.ip_address : ''}
                        </small>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    document.getElementById('activityContent').innerHTML = html;
}


let currentUserId = null;

function resetPassword(userId) {
    currentUserId = userId;

    // Mostrar modal para elegir tipo de reseteo
    const typeModal = new bootstrap.Modal(document.getElementById('resetPasswordTypeModal'));
    typeModal.show();
}

// Event listeners para los botones del modal de tipo
document.getElementById('autoPasswordBtn').addEventListener('click', function() {
    bootstrap.Modal.getInstance(document.getElementById('resetPasswordTypeModal')).hide();
    executePasswordReset(null);
});

document.getElementById('manualPasswordBtn').addEventListener('click', function() {
    bootstrap.Modal.getInstance(document.getElementById('resetPasswordTypeModal')).hide();

    // Mostrar modal para contraseña manual
    const manualModal = new bootstrap.Modal(document.getElementById('manualPasswordModal'));
    manualModal.show();

    // Limpiar campo
    document.getElementById('newPassword').value = '';
});

// Toggle para mostrar/ocultar contraseña
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('newPassword');
    const icon = this.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Confirmar contraseña manual
document.getElementById('confirmManualPasswordBtn').addEventListener('click', function() {
    const newPassword = document.getElementById('newPassword').value.trim();

    if (!newPassword || newPassword.length < 6) {
        showResultModal('error', 'Error de Validación', 'La contraseña debe tener al menos 6 caracteres.');
        return;
    }

    bootstrap.Modal.getInstance(document.getElementById('manualPasswordModal')).hide();
    executePasswordReset(newPassword);
});

function executePasswordReset(password) {
    const requestBody = password ? { password: password } : {};

    // Mostrar loading en el botón original
    const originalButton = document.querySelector(`button[onclick="resetPassword(${currentUserId})"]`);
    if (originalButton) {
        originalButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Reseteando...';
        originalButton.disabled = true;
    }

    fetch(`/admin/users/reset-password/${currentUserId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(requestBody)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const title = data.is_manual ? 'Contraseña Establecida' : 'Contraseña Restablecida';
            const message = data.is_manual
                ? `<strong>Nueva contraseña:</strong> ${data.new_password}<br><strong>Usuario:</strong> ${data.user_email}`
                : `<strong>Nueva contraseña temporal:</strong> ${data.new_password}<br><strong>Usuario:</strong> ${data.user_email}<br><small class="text-muted">Se ha enviado un email al usuario.</small>`;

            showResultModal('success', title, message);
        } else {
            showResultModal('error', 'Error', data.error || 'No se pudo resetear la contraseña');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showResultModal('error', 'Error de Conexión', 'No se pudo conectar con el servidor');
    })
    .finally(() => {
        // Restaurar botón original
        if (originalButton) {
            originalButton.innerHTML = '<i class="fas fa-key me-2"></i>Resetear Contraseña';
            originalButton.disabled = false;
        }
    });
}

function toggleUserStatus(userId, newStatus) {
    const statusText = newStatus ? 'activar' : 'desactivar';
    const statusIcon = newStatus ? 'fa-user-check' : 'fa-user-times';
    const statusColor = newStatus ? 'success' : 'warning';

    // Configurar modal
    const modal = document.getElementById('toggleStatusModal');
    const header = document.getElementById('toggleStatusModalHeader');
    const title = document.getElementById('toggleStatusModalLabel');
    const body = document.getElementById('toggleStatusModalBody');
    const confirmBtn = document.getElementById('confirmToggleStatusBtn');

    header.className = `modal-header bg-${statusColor} text-white`;
    title.innerHTML = `<i class="fas ${statusIcon} me-2"></i>Cambiar Estado`;
    body.innerHTML = `
        <i class="fas ${statusIcon} fa-3x text-${statusColor} mb-3"></i>
        <h6>¿Estás seguro de que deseas ${statusText} este usuario?</h6>
        <p class="text-muted small">Esta acción cambiará el estado del usuario inmediatamente.</p>
    `;

    // Configurar botón de confirmación
    confirmBtn.onclick = function() {
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Procesando...';
        confirmBtn.disabled = true;

        fetch(`/admin/users/toggle-status/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            bootstrap.Modal.getInstance(modal).hide();

            if (data.success) {
                showResultModal('success', 'Estado Actualizado', `El usuario ha sido ${statusText}do correctamente.`);
                // Recargar página después de un momento
                setTimeout(() => location.reload(), 2000);
            } else {
                showResultModal('error', 'Error', data.error || 'No se pudo cambiar el estado del usuario');
            }
        })
        .catch(error => {
            bootstrap.Modal.getInstance(modal).hide();
            showResultModal('error', 'Error de Conexión', 'No se pudo conectar con el servidor');
        })
        .finally(() => {
            confirmBtn.innerHTML = '<i class="fas fa-check me-2"></i>Confirmar';
            confirmBtn.disabled = false;
        });
    };

    // Mostrar modal
    const toggleModal = new bootstrap.Modal(modal);
    toggleModal.show();
}

function showResultModal(type, title, message) {
    const modal = document.getElementById('resultModal');
    const header = document.getElementById('resultModalHeader');
    const titleElement = document.getElementById('resultModalLabel');
    const body = document.getElementById('resultModalBody');

    // Configurar colores según el tipo
    if (type === 'success') {
        header.className = 'modal-header bg-success text-white';
        titleElement.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + title;
        body.innerHTML = `<i class="fas fa-check-circle fa-3x text-success mb-3"></i><div>${message}</div>`;
    } else {
        header.className = 'modal-header bg-danger text-white';
        titleElement.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>' + title;
        body.innerHTML = `<i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i><div>${message}</div>`;
    }

    const resultModal = new bootstrap.Modal(modal);
    resultModal.show();
}
</script>
<?= $this->endSection() ?>
