<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AddTestCartItems extends BaseCommand
{
    protected $group       = 'Test';
    protected $name        = 'test:add-cart-items';
    protected $description = 'Agregar productos de prueba al carrito para probar checkout';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== AGREGANDO PRODUCTOS AL CARRITO ===', 'yellow');
            CLI::newLine();
            
            // Obtener un producto de prueba
            $product = $db->query("SELECT id, name, price_regular FROM products WHERE is_active = 1 LIMIT 1")->getRowArray();
            
            if (!$product) {
                CLI::error('❌ No hay productos activos en la base de datos');
                return;
            }
            
            CLI::write("Producto encontrado: {$product['name']}", 'white');
            
            // Simular sesión de usuario
            $sessionId = 'test_session_' . time();
            
            // Agregar producto al carrito
            $cartData = [
                'session_id' => $sessionId,
                'product_id' => $product['id'],
                'quantity' => 2,
                'price' => $product['price_regular'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->query("
                INSERT INTO cart_items (session_id, product_id, quantity, price, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ", array_values($cartData));
            
            CLI::write('✅ Producto agregado al carrito', 'green');
            CLI::write("Session ID: {$sessionId}", 'cyan');
            CLI::write("Producto: {$product['name']}", 'white');
            CLI::write("Cantidad: 2", 'white');
            CLI::write("Precio: Q{$product['price_regular']}", 'white');
            
            CLI::newLine();
            CLI::write('Para probar el checkout:', 'yellow');
            CLI::write("1. Abre el navegador", 'white');
            CLI::write("2. Ve a http://localhost:8080/checkout", 'white');
            CLI::write("3. En las herramientas de desarrollador, establece la cookie:", 'white');
            CLI::write("   document.cookie = 'ci_session={$sessionId}; path=/';", 'cyan');
            CLI::write("4. Recarga la página", 'white');
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
