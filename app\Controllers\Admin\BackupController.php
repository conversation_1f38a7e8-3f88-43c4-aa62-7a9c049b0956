<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\BackupManager;

/**
 * Controlador de Gestión de Backups
 * Panel de administración para backups del sistema
 */
class BackupController extends BaseController
{
    private $backupManager;
    
    public function __construct()
    {
        $this->backupManager = new BackupManager();
    }
    
    /**
     * Dashboard principal de backups
     */
    public function index()
    {
        $data = [
            'title' => 'Gestión de Backups - MrCell Guatemala',
            'backups_list' => $this->backupManager->getBackupsList(),
            'backup_stats' => $this->getBackupStats(),
            'storage_info' => $this->getStorageInfo(),
            'backup_schedule' => $this->getBackupSchedule()
        ];
        
        return view('admin/backup/dashboard', $data);
    }
    
    /**
     * Crear backup completo
     */
    public function createFullBackup()
    {
        try {
            $result = $this->backupManager->createFullBackup();
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Backup completo creado exitosamente' : 'Error creando backup',
                'backup_id' => $result['backup_id'] ?? null,
                'duration' => $result['duration'] ?? 0,
                'details' => $result
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Crear backup incremental
     */
    public function createIncrementalBackup()
    {
        try {
            $result = $this->backupManager->createIncrementalBackup();
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Backup incremental creado exitosamente' : 'Error creando backup incremental',
                'backup_id' => $result['backup_id'] ?? null,
                'duration' => $result['duration'] ?? 0,
                'details' => $result
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Restaurar backup
     */
    public function restoreBackup()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!isset($json['backup_id'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'ID de backup requerido'
                ]);
            }
            
            $backupId = $json['backup_id'];
            $options = $json['options'] ?? [];
            
            $result = $this->backupManager->restoreBackup($backupId, $options);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Backup restaurado exitosamente' : 'Error restaurando backup',
                'details' => $result
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener información de un backup
     */
    public function getBackupInfo($backupId)
    {
        try {
            $backupInfo = $this->backupManager->getBackupInfo($backupId);
            
            if (!$backupInfo) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Backup no encontrado'
                ]);
            }
            
            return $this->response->setJSON([
                'success' => true,
                'backup' => $backupInfo
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Eliminar backup
     */
    public function deleteBackup($backupId)
    {
        try {
            $result = $this->backupManager->deleteBackup($backupId);
            
            return $this->response->setJSON([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Backup eliminado exitosamente' : 'Error eliminando backup',
                'error' => $result['error'] ?? null
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Descargar backup
     */
    public function downloadBackup($backupId)
    {
        try {
            $backupInfo = $this->backupManager->getBackupInfo($backupId);
            
            if (!$backupInfo) {
                throw new \Exception('Backup no encontrado');
            }
            
            $backupPath = WRITEPATH . 'backups/';
            $zipFile = $backupPath . $backupId . '.zip';
            
            if (!file_exists($zipFile)) {
                throw new \Exception('Archivo de backup no encontrado');
            }
            
            return $this->response->download($zipFile, null);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Verificar integridad de backup
     */
    public function verifyBackup($backupId)
    {
        try {
            // Implementar verificación de integridad
            $verification = [
                'backup_id' => $backupId,
                'integrity_check' => true,
                'files_verified' => 0,
                'errors' => []
            ];
            
            return $this->response->setJSON([
                'success' => true,
                'verification' => $verification
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Configurar programación de backups
     */
    public function updateSchedule()
    {
        try {
            $json = $this->request->getJSON(true);
            
            if (!$json) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Datos de configuración requeridos'
                ]);
            }
            
            // Guardar configuración de programación
            $db = \Config\Database::connect();
            
            $scheduleSettings = [
                'full_backup_frequency' => $json['full_backup_frequency'] ?? 'weekly',
                'incremental_backup_frequency' => $json['incremental_backup_frequency'] ?? 'daily',
                'backup_time' => $json['backup_time'] ?? '02:00',
                'max_backups_to_keep' => $json['max_backups_to_keep'] ?? 30,
                'compression_enabled' => $json['compression_enabled'] ?? true,
                'cloud_storage_enabled' => $json['cloud_storage_enabled'] ?? false
            ];
            
            foreach ($scheduleSettings as $key => $value) {
                $db->table('system_settings')
                   ->where('setting_key', 'backup_' . $key)
                   ->set('setting_value', is_bool($value) ? ($value ? '1' : '0') : $value)
                   ->update();
            }
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Configuración de backups actualizada'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener estadísticas de backups
     */
    private function getBackupStats(): array
    {
        try {
            $db = \Config\Database::connect();
            
            $totalBackups = $db->table('system_backups')->countAllResults();
            $fullBackups = $db->table('system_backups')->where('type', 'full')->countAllResults();
            $incrementalBackups = $db->table('system_backups')->where('type', 'incremental')->countAllResults();
            
            $lastBackup = $db->table('system_backups')
                            ->orderBy('created_at', 'DESC')
                            ->limit(1)
                            ->get()
                            ->getRowArray();
            
            $totalSize = $db->table('system_backups')
                           ->selectSum('file_size')
                           ->get()
                           ->getRow()
                           ->file_size ?? 0;
            
            return [
                'total_backups' => $totalBackups,
                'full_backups' => $fullBackups,
                'incremental_backups' => $incrementalBackups,
                'last_backup' => $lastBackup,
                'total_size_mb' => round($totalSize / 1024 / 1024, 2),
                'success_rate' => $this->calculateSuccessRate()
            ];
            
        } catch (\Exception $e) {
            return [
                'total_backups' => 0,
                'full_backups' => 0,
                'incremental_backups' => 0,
                'last_backup' => null,
                'total_size_mb' => 0,
                'success_rate' => 0
            ];
        }
    }
    
    /**
     * Obtener información de almacenamiento
     */
    private function getStorageInfo(): array
    {
        $backupPath = WRITEPATH . 'backups/';
        
        if (!is_dir($backupPath)) {
            return [
                'backup_directory_exists' => false,
                'used_space_mb' => 0,
                'available_space_mb' => 0,
                'total_space_mb' => 0
            ];
        }
        
        $usedSpace = $this->getDirectorySize($backupPath);
        $totalSpace = disk_total_space($backupPath);
        $freeSpace = disk_free_space($backupPath);
        
        return [
            'backup_directory_exists' => true,
            'used_space_mb' => round($usedSpace / 1024 / 1024, 2),
            'available_space_mb' => round($freeSpace / 1024 / 1024, 2),
            'total_space_mb' => round($totalSpace / 1024 / 1024, 2),
            'usage_percentage' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
    }
    
    /**
     * Obtener configuración de programación
     */
    private function getBackupSchedule(): array
    {
        try {
            $db = \Config\Database::connect();
            
            $settings = $db->table('system_settings')
                          ->whereIn('setting_key', [
                              'backup_full_backup_frequency',
                              'backup_incremental_backup_frequency',
                              'backup_time',
                              'backup_max_backups_to_keep',
                              'backup_compression_enabled',
                              'backup_cloud_storage_enabled'
                          ])
                          ->get()
                          ->getResultArray();
            
            $schedule = [];
            foreach ($settings as $setting) {
                $key = str_replace('backup_', '', $setting['setting_key']);
                $schedule[$key] = $setting['setting_value'];
            }
            
            return $schedule;
            
        } catch (\Exception $e) {
            return [
                'full_backup_frequency' => 'weekly',
                'incremental_backup_frequency' => 'daily',
                'backup_time' => '02:00',
                'max_backups_to_keep' => '30',
                'compression_enabled' => '1',
                'cloud_storage_enabled' => '0'
            ];
        }
    }
    
    /**
     * Calcular tasa de éxito de backups
     */
    private function calculateSuccessRate(): float
    {
        try {
            $db = \Config\Database::connect();
            
            $total = $db->table('system_backups')->countAllResults();
            $successful = $db->table('system_backups')->where('status', 'completed')->countAllResults();
            
            return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
            
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * Obtener tamaño de directorio
     */
    private function getDirectorySize(string $dir): int
    {
        $size = 0;
        
        if (is_dir($dir)) {
            foreach (glob(rtrim($dir, '/') . '/*', GLOB_NOSORT) as $each) {
                $size += is_file($each) ? filesize($each) : $this->getDirectorySize($each);
            }
        }
        
        return $size;
    }
}
