<?php

namespace Config;

use CodeIgniter\Events\Events;
use CodeIgniter\Exceptions\FrameworkException;
use CodeIgniter\HotReloader\HotReloader;

/*
 * --------------------------------------------------------------------
 * Application Events
 * --------------------------------------------------------------------
 * Events allow you to tap into the execution of the program without
 * modifying or extending core files. This file provides a central
 * location to define your events, though they can always be added
 * at run-time, also, if needed.
 *
 * You create code that can execute by subscribing to events with
 * the 'on()' method. This accepts any form of callable, including
 * Closures, that will be executed when the event is triggered.
 *
 * Example:
 *      Events::on('create', [$myInstance, 'myMethod']);
 */

Events::on('pre_system', static function (): void {
    if (ENVIRONMENT !== 'testing') {
        if (ini_get('zlib.output_compression')) {
            throw FrameworkException::forEnabledZlibOutputCompression();
        }

        while (ob_get_level() > 0) {
            ob_end_flush();
        }

        ob_start(static fn ($buffer) => $buffer);
    }

    /*
     * --------------------------------------------------------------------
     * Debug Toolbar Listeners.
     * --------------------------------------------------------------------
     * If you delete, they will no longer be collected.
     */
    if (CI_DEBUG && ! is_cli()) {
        Events::on('DBQuery', 'CodeIgniter\Debug\Toolbar\Collectors\Database::collect');
        service('toolbar')->respond();
        // Hot Reload route - for framework use on the hot reloader.
        if (ENVIRONMENT === 'development') {
            service('routes')->get('__hot-reload', static function (): void {
                (new HotReloader())->run();
            });
        }
    }
});

/*
 * --------------------------------------------------------------------
 * WhatsApp Events
 * --------------------------------------------------------------------
 * Register WhatsApp notification events
 */
Events::on('post_controller_constructor', static function (): void {
    // Register WhatsApp events when the application starts (temporalmente deshabilitado por problema de IP)
    if (class_exists('\App\Libraries\WhatsAppEventHandler')) {
         \App\Libraries\WhatsAppEventHandler::registerEvents();
    }
});

/*
 * --------------------------------------------------------------------
 * Database UTF-8 Configuration
 * --------------------------------------------------------------------
 * Force UTF-8 encoding on all database connections
 */
Events::on('DBConnect', static function ($db): void {
    if ($db->DBDriver === 'MySQLi') {
        // Force UTF-8 charset
        $db->query("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci");
        $db->query("SET CHARACTER SET utf8mb4");
        $db->query("SET character_set_connection=utf8mb4");
        $db->query("SET character_set_client=utf8mb4");
        $db->query("SET character_set_results=utf8mb4");

        log_message('info', 'Database connection forced to UTF-8');
    }
});

/*
 * --------------------------------------------------------------------
 * Cron Job Events
 * --------------------------------------------------------------------
 * Eventos para ejecutar tareas programadas cada 12 horas
 */
Events::on('cron_12_hours', static function (): void {
    try {
        $cronService = \Config\Services::cronService();
        $cronService->runScheduledTasks();
    } catch (\Exception $e) {
        log_message('error', 'Error ejecutando cron de 12 horas: ' . $e->getMessage());
    }
});
