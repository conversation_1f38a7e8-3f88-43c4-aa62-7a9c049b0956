<?php

namespace App\Libraries;

/**
 * Sistema de Notificaciones por WhatsApp
 * Compatible con múltiples proveedores de WhatsApp API
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class WhatsAppNotifier
{
    private $config;
    private $provider;
    
    public function __construct()
    {
        $this->config = [
            'enabled' => env('WHATSAPP_NOTIFICATIONS_ENABLED', false),
            'api_url' => env('WHATSAPP_API_URL', 'http://**************/api/sendMessage'),
            'api_key' => env('WHATSAPP_API_KEY', ''),
            'device_token' => env('WHATSAPP_DEVICE_TOKEN', ''),
            'templates' => [
                'price_alert' => [
                    'title' => '🔥 ¡Alerta de Precio!',
                    'message' => "¡Hola {name}! 👋\n\nEl producto que tienes en tu lista de deseos ha bajado de precio:\n\n📱 *{product_name}*\n💰 Precio anterior: Q{old_price}\n🎉 Precio actual: Q{new_price}\n💸 Te ahorras: Q{savings} ({percentage}%)\n\n¡No te lo pierdas! 🏃‍♂️\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱"
                ],
                'back_in_stock' => [
                    'title' => '📦 ¡Producto Disponible!',
                    'message' => "¡Hola {name}! 👋\n\nEl producto que esperabas ya está disponible:\n\n📱 *{product_name}*\n💰 Precio: Q{price}\n📦 Stock: {stock_quantity} unidades\n\n¡Cómpralo antes de que se agote! 🏃‍♂️\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱"
                ],
                'wishlist_reminder' => [
                    'title' => '❤️ Recordatorio de Lista de Deseos',
                    'message' => "¡Hola {name}! 👋\n\nTienes {count} productos en tu lista de deseos esperándote:\n\n{product_list}\n\n¿Qué tal si les echas un vistazo? 😊\n{wishlist_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱"
                ],
                'new_similar_product' => [
                    'title' => '🆕 ¡Producto Similar Disponible!',
                    'message' => "¡Hola {name}! 👋\n\nTenemos un nuevo producto que podría interesarte:\n\n📱 *{product_name}*\n💰 Precio: Q{price}\n⭐ Rating: {rating}/5\n\nEs similar a: {similar_to}\n\n¡Échale un vistazo! 👀\n{product_url}\n\n_MrCell Guatemala - Tu tienda de tecnología_ 📱"
                ]
            ]
        ];
        
    }
    
    /**
     * Enviar alerta de precio
     * 
     * @param array $user Datos del usuario
     * @param array $product Datos del producto
     * @param float $oldPrice Precio anterior
     * @param float $newPrice Precio nuevo
     * @return array Resultado del envío
     */
    public function sendPriceAlert(array $user, array $product, float $oldPrice, float $newPrice): array
    {
        if (!$this->isEnabled() || empty($user['phone'])) {
            return ['success' => false, 'error' => 'WhatsApp not enabled or phone missing'];
        }
        
        $savings = $oldPrice - $newPrice;
        $percentage = round(($savings / $oldPrice) * 100, 1);
        
        $variables = [
            'name' => $user['first_name'] ?? $user['name'] ?? 'Cliente',
            'product_name' => $product['name'],
            'old_price' => number_format($oldPrice, 2),
            'new_price' => number_format($newPrice, 2),
            'savings' => number_format($savings, 2),
            'percentage' => $percentage,
            'product_url' => base_url('producto/' . $product['slug'])
        ];
        
        return $this->sendTemplateMessage($user['phone'], 'price_alert', $variables);
    }
    
    /**
     * Enviar notificación de stock disponible
     */
    public function sendBackInStockAlert(array $user, array $product): array
    {
        if (!$this->isEnabled() || empty($user['phone'])) {
            return ['success' => false, 'error' => 'WhatsApp not enabled or phone missing'];
        }
        
        $variables = [
            'name' => $user['first_name'] ?? $user['name'] ?? 'Cliente',
            'product_name' => $product['name'],
            'price' => number_format($product['price_sale'] ?? $product['price_regular'], 2),
            'stock_quantity' => $product['stock_quantity'],
            'product_url' => base_url('producto/' . $product['slug'])
        ];
        
        return $this->sendTemplateMessage($user['phone'], 'back_in_stock', $variables);
    }
    
    /**
     * Enviar recordatorio de wishlist
     */
    public function sendWishlistReminder(array $user, array $wishlistItems): array
    {
        if (!$this->isEnabled() || empty($user['phone']) || empty($wishlistItems)) {
            return ['success' => false, 'error' => 'WhatsApp not enabled, phone missing, or empty wishlist'];
        }
        
        $productList = '';
        foreach (array_slice($wishlistItems, 0, 5) as $index => $item) {
            $price = $item['price_sale'] ?? $item['price_regular'];
            $productList .= ($index + 1) . ". {$item['name']} - Q" . number_format($price, 2) . "\n";
        }
        
        if (count($wishlistItems) > 5) {
            $productList .= "... y " . (count($wishlistItems) - 5) . " productos más\n";
        }
        
        $variables = [
            'name' => $user['first_name'] ?? $user['name'] ?? 'Cliente',
            'count' => count($wishlistItems),
            'product_list' => trim($productList),
            'wishlist_url' => base_url('wishlist')
        ];
        
        return $this->sendTemplateMessage($user['phone'], 'wishlist_reminder', $variables);
    }
    
    /**
     * Enviar notificación de producto similar
     */
    public function sendSimilarProductAlert(array $user, array $newProduct, array $similarTo): array
    {
        if (!$this->isEnabled() || empty($user['phone'])) {
            return ['success' => false, 'error' => 'WhatsApp not enabled or phone missing'];
        }
        
        $variables = [
            'name' => $user['first_name'] ?? $user['name'] ?? 'Cliente',
            'product_name' => $newProduct['name'],
            'price' => number_format($newProduct['price_sale'] ?? $newProduct['price_regular'], 2),
            'rating' => number_format($newProduct['rating_average'] ?? 0, 1),
            'similar_to' => $similarTo['name'],
            'product_url' => base_url('producto/' . $newProduct['slug'])
        ];
        
        return $this->sendTemplateMessage($user['phone'], 'new_similar_product', $variables);
    }
    
    /**
     * Enviar mensaje personalizado
     */
    public function sendCustomMessage(string $phone, string $message, array $mediaUrls = []): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'error' => 'WhatsApp not enabled'];
        }
        
        return $this->sendMessage($phone, $message, $mediaUrls);
    }
    
    /**
     * Enviar mensaje usando template de la base de datos
     */
    private function sendTemplateMessage(string $phone, string $templateKey, array $variables): array
    {
        try {
            $db = \Config\Database::connect();
            $template = $db->table('whatsapp_templates')
                          ->where('key', $templateKey)
                          ->where('is_active', 1)
                          ->get()
                          ->getRowArray();

            if (!$template) {
                // Fallback a templates hardcoded si no existe en BD
                $fallbackTemplate = $this->config['templates'][$templateKey] ?? null;
                if (!$fallbackTemplate) {
                    return ['success' => false, 'error' => "Template '{$templateKey}' not found"];
                }
                $message = $fallbackTemplate['message'];
            } else {
                $message = $template['message'];

                // Incrementar contador de uso
                $db->table('whatsapp_templates')
                  ->where('id', $template['id'])
                  ->set('usage_count', 'usage_count + 1', false)
                  ->update();
            }

            // Reemplazar variables
            foreach ($variables as $key => $value) {
                $message = str_replace('{' . $key . '}', $value, $message);
            }

            return $this->sendMessage($phone, $message);

        } catch (\Exception $e) {
            log_message('error', 'Error loading WhatsApp template: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Error loading template'];
        }
    }
    
    /**
     * Enviar mensaje usando tu sistema existente
     */
    private function sendMessage(string $phone, string $message, array $mediaUrls = []): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'WhatsApp notifications disabled'];
        }

        // Limpiar y formatear número de teléfono
        $phone = $this->formatPhoneNumber($phone);

        try {
            return $this->sendViaExistingSystem($phone, $message, $mediaUrls);

        } catch (\Exception $e) {
            log_message('error', 'WhatsApp send error: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Enviar usando tu API específica de WhatsApp
     */
    private function sendViaExistingSystem(string $phone, string $message, array $mediaUrls = []): array
    {
        $apiUrl = $this->config['api_url'];
        $apiKey = $this->config['api_key'];
        $deviceToken = $this->config['device_token'];

        // Formato específico de tu API
        $data = [
            'phone' => $phone,
            'messageType' => 1,
            'token' => $deviceToken,
            'chat' => $message
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: ' . $apiKey
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            $responseData = json_decode($response, true);
            return [
                'success' => $responseData['success'] ?? true,
                'message_id' => $responseData['message_id'] ?? $responseData['id'] ?? uniqid('wa_'),
                'provider' => 'mrcell_api'
            ];
        } else {
            return [
                'success' => false,
                'error' => 'HTTP ' . $httpCode . ': ' . $response,
                'provider' => 'mrcell_api'
            ];
        }
    }
    

    
    /**
     * Formatear número de teléfono
     */
    private function formatPhoneNumber(string $phone): string
    {
        // Remover caracteres no numéricos excepto +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Si no tiene código de país, agregar +502 (Guatemala)
        if (!str_starts_with($phone, '+')) {
            if (strlen($phone) === 8) {
                $phone = '+502' . $phone;
            } elseif (strlen($phone) === 11 && str_starts_with($phone, '502')) {
                $phone = '+' . $phone;
            } else {
                $phone = '+' . $phone;
            }
        }
        
        return $phone;
    }
    
    /**
     * Verificar si WhatsApp está habilitado
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] &&
               !empty($this->config['api_url']) &&
               !empty($this->config['api_key']) &&
               !empty($this->config['device_token']);
    }

    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'api_url' => $this->config['api_url'],
            'api_key_configured' => !empty($this->config['api_key']),
            'device_token_configured' => !empty($this->config['device_token']),
            'fully_configured' => $this->isEnabled()
        ];
    }
    
    /**
     * Probar conexión con tu sistema existente
     */
    public function testConnection(): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'error' => 'WhatsApp not enabled'];
        }

        // Enviar mensaje de prueba a número de prueba
        $testPhone = env('WHATSAPP_TEST_PHONE', '+50212345678');
        $testMessage = "🧪 Mensaje de prueba de MrCell Guatemala\n\nSi recibes este mensaje, la integración de WhatsApp está funcionando correctamente.\n\n_Enviado: " . date('Y-m-d H:i:s') . "_";

        return $this->sendMessage($testPhone, $testMessage);
    }
    
    /**
     * Obtener estadísticas de envío
     */
    public function getStats(int $days = 30): array
    {
        try {
            $db = \Config\Database::connect();
            
            // Esto requeriría una tabla de logs de WhatsApp
            // Por ahora retornamos estadísticas simuladas
            return [
                'total_sent' => 0,
                'success_rate' => 0,
                'failed_count' => 0,
                'period_days' => $days
            ];
            
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
}
