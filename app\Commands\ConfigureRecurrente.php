<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\SettingModel;

class ConfigureRecurrente extends BaseCommand
{
    protected $group       = 'Recurrente';
    protected $name        = 'recurrente:configure';
    protected $description = 'Configurar credenciales de Recurrente';
    protected $usage       = 'recurrente:configure [options]';
    protected $arguments   = [];
    protected $options     = [
        '--mode' => 'Modo de operación (test|live). Default: test'
    ];

    public function run(array $params)
    {
        CLI::write('=== Configuración de Recurrente ===', 'yellow');
        CLI::newLine();

        $mode = CLI::getOption('mode') ?? 'test';
        
        if (!in_array($mode, ['test', 'live'])) {
            CLI::error('Modo inválido. Use: test o live');
            return;
        }

        // Configuraciones a insertar/actualizar
        $recurrenteSettings = [
            'recurrente_enabled' => '1',
            'recurrente_mode' => $mode,
            'recurrente_public_key_test' => 'pk_test_JRZca6LgYaDcTlT1VwQQfoIfubWg6TyzSgFRNZH7bVUGdzRZY4vZ6xe7C',
            'recurrente_secret_key_test' => 'sk_test_4taMNsXSHb2KkHMAVSvVmr0mQta9tSAs1Vc2CQVqC0tvkjRnkDHqgRh5v',
            'recurrente_public_key_live' => 'pk_live_ENBlqhHVOqnztwPyJJrYJ196LLfrna06D3UT9nnfaU3weN9WVU9gUCQvw',
            'recurrente_secret_key_live' => '*****************************************************************',
            'recurrente_currency' => 'GTQ',
            'recurrente_fee_percentage' => '3.9',
            'recurrente_webhook_secret' => ''
        ];

        $db = \Config\Database::connect();

        try {
            CLI::write("Configurando Recurrente en modo: {$mode}", 'cyan');
            CLI::newLine();
            
            foreach ($recurrenteSettings as $key => $value) {
                // Verificar si ya existe
                $existing = $db->table('system_settings')
                    ->where('setting_key', $key)
                    ->get()
                    ->getRow();
                
                if ($existing) {
                    // Actualizar
                    $result = $db->table('system_settings')
                        ->where('setting_key', $key)
                        ->update([
                            'setting_value' => $value,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    CLI::write("✓ Actualizado: {$key}", 'green');
                } else {
                    // Insertar
                    $result = $db->table('system_settings')->insert([
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'setting_group' => 'payment',
                        'display_name' => ucfirst(str_replace(['recurrente_', '_'], ['', ' '], $key)),
                        'description' => 'Configuración de Recurrente',
                        'setting_type' => 'text',
                        'is_active' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    CLI::write("✓ Creado: {$key}", 'green');
                }
            }
            
            CLI::newLine();
            CLI::write('=== Verificando configuración ===', 'yellow');
            
            // Verificar configuración
            $settingModel = new SettingModel();
            $config = $settingModel->getRecurrenteConfig();
            
            CLI::write("Habilitado: " . ($config['enabled'] ? 'SÍ' : 'NO'), 'white');
            CLI::write("Modo: " . $config['mode'], 'white');
            CLI::write("Clave pública: " . substr($config['public_key'], 0, 20) . "...", 'white');
            CLI::write("Clave secreta: " . substr($config['secret_key'], 0, 20) . "...", 'white');
            CLI::write("Moneda: " . $config['currency'], 'white');
            CLI::write("URL base: " . $config['base_url'], 'white');
            
            CLI::newLine();
            CLI::write('=== Configuración completada ===', 'green');
            CLI::newLine();
            CLI::write('Ahora puedes ejecutar: php spark recurrente:sync-products --pending --dry-run', 'cyan');
            
        } catch (\Exception $e) {
            CLI::error("Error: " . $e->getMessage());
        }
    }
}
