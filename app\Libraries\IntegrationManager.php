<?php

namespace App\Libraries;

/**
 * Gestor de Integraciones
 * Sistema completo de integraciones con servicios externos
 */
class IntegrationManager
{
    private $db;
    private $cache;
    private $logger;
    private $webhookManager;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        $this->webhookManager = new WebhookManager();
        
        $this->config = [
            'enabled' => env('INTEGRATIONS_ENABLED', true),
            'timeout' => env('INTEGRATION_TIMEOUT', 30),
            'retry_attempts' => env('INTEGRATION_RETRY_ATTEMPTS', 3),
            'cache_ttl' => env('INTEGRATION_CACHE_TTL', 300),
            
            // PayPal
            'paypal_enabled' => env('PAYPAL_ENABLED', false),
            'paypal_client_id' => env('PAYPAL_CLIENT_ID', ''),
            'paypal_client_secret' => env('PAYPAL_CLIENT_SECRET', ''),
            'paypal_sandbox' => env('PAYPAL_SANDBOX', true),
            
            // Stripe
            'stripe_enabled' => env('STRIPE_ENABLED', false),
            'stripe_public_key' => env('STRIPE_PUBLIC_KEY', ''),
            'stripe_secret_key' => env('STRIPE_SECRET_KEY', ''),
            
            // WhatsApp Business API
            'whatsapp_enabled' => env('WHATSAPP_ENABLED', false),
            'whatsapp_token' => env('WHATSAPP_TOKEN', ''),
            'whatsapp_phone_id' => env('WHATSAPP_PHONE_ID', ''),
            
            // Google Analytics
            'ga_enabled' => env('GOOGLE_ANALYTICS_ENABLED', false),
            'ga_tracking_id' => env('GOOGLE_ANALYTICS_ID', ''),
            'ga_api_key' => env('GOOGLE_ANALYTICS_API_KEY', ''),
            
            // Facebook Pixel
            'fb_pixel_enabled' => env('FACEBOOK_PIXEL_ENABLED', false),
            'fb_pixel_id' => env('FACEBOOK_PIXEL_ID', ''),
            
            // Mailchimp
            'mailchimp_enabled' => env('MAILCHIMP_ENABLED', false),
            'mailchimp_api_key' => env('MAILCHIMP_API_KEY', ''),
            'mailchimp_list_id' => env('MAILCHIMP_LIST_ID', ''),
            
            // Twilio
            'twilio_enabled' => env('TWILIO_ENABLED', false),
            'twilio_sid' => env('TWILIO_SID', ''),
            'twilio_token' => env('TWILIO_TOKEN', ''),
            'twilio_phone' => env('TWILIO_PHONE', ''),
            
            // Slack
            'slack_enabled' => env('SLACK_ENABLED', false),
            'slack_webhook_url' => env('SLACK_WEBHOOK_URL', ''),
            'slack_channel' => env('SLACK_CHANNEL', '#general')
        ];
        
        $this->createIntegrationTables();
    }
    
    /**
     * Procesar pago con PayPal
     */
    public function processPayPalPayment(array $paymentData): array
    {
        if (!$this->config['paypal_enabled']) {
            return ['success' => false, 'error' => 'PayPal integration disabled'];
        }
        
        try {
            $baseUrl = $this->config['paypal_sandbox'] 
                ? 'https://api.sandbox.paypal.com' 
                : 'https://api.paypal.com';
            
            // Obtener token de acceso
            $accessToken = $this->getPayPalAccessToken();
            if (!$accessToken) {
                throw new \Exception('Failed to get PayPal access token');
            }
            
            // Crear orden
            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [[
                    'amount' => [
                        'currency_code' => $paymentData['currency'] ?? 'USD',
                        'value' => number_format($paymentData['amount'], 2, '.', '')
                    ],
                    'description' => $paymentData['description'] ?? 'MrCell Guatemala Purchase'
                ]]
            ];
            
            $response = $this->sendHttpRequest(
                $baseUrl . '/v2/checkout/orders',
                'POST',
                $orderData,
                [
                    'Authorization: Bearer ' . $accessToken,
                    'Content-Type: application/json'
                ]
            );
            
            if ($response['success'] && isset($response['data']['id'])) {
                $this->logger->info("PayPal order created", [
                    'order_id' => $response['data']['id'],
                    'amount' => $paymentData['amount']
                ]);
                
                return [
                    'success' => true,
                    'order_id' => $response['data']['id'],
                    'approval_url' => $this->getPayPalApprovalUrl($response['data'])
                ];
            }
            
            throw new \Exception('PayPal order creation failed');
            
        } catch (\Exception $e) {
            $this->logger->error("PayPal payment error: " . $e->getMessage(), $paymentData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Procesar pago con Stripe
     */
    public function processStripePayment(array $paymentData): array
    {
        if (!$this->config['stripe_enabled']) {
            return ['success' => false, 'error' => 'Stripe integration disabled'];
        }
        
        try {
            $stripeData = [
                'amount' => $paymentData['amount'] * 100, // Stripe usa centavos
                'currency' => $paymentData['currency'] ?? 'usd',
                'description' => $paymentData['description'] ?? 'MrCell Guatemala Purchase',
                'source' => $paymentData['token']
            ];
            
            $response = $this->sendHttpRequest(
                'https://api.stripe.com/v1/charges',
                'POST',
                http_build_query($stripeData),
                [
                    'Authorization: Bearer ' . $this->config['stripe_secret_key'],
                    'Content-Type: application/x-www-form-urlencoded'
                ]
            );
            
            if ($response['success'] && isset($response['data']['id'])) {
                $this->logger->info("Stripe payment processed", [
                    'charge_id' => $response['data']['id'],
                    'amount' => $paymentData['amount']
                ]);
                
                return [
                    'success' => true,
                    'charge_id' => $response['data']['id'],
                    'status' => $response['data']['status']
                ];
            }
            
            throw new \Exception('Stripe payment failed');
            
        } catch (\Exception $e) {
            $this->logger->error("Stripe payment error: " . $e->getMessage(), $paymentData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enviar mensaje por WhatsApp
     */
    public function sendWhatsAppMessage(string $phone, string $message): array
    {
        if (!$this->config['whatsapp_enabled']) {
            return ['success' => false, 'error' => 'WhatsApp integration disabled'];
        }
        
        try {
            $data = [
                'messaging_product' => 'whatsapp',
                'to' => $phone,
                'type' => 'text',
                'text' => ['body' => $message]
            ];
            
            $response = $this->sendHttpRequest(
                "https://graph.facebook.com/v17.0/{$this->config['whatsapp_phone_id']}/messages",
                'POST',
                $data,
                [
                    'Authorization: Bearer ' . $this->config['whatsapp_token'],
                    'Content-Type: application/json'
                ]
            );
            
            if ($response['success']) {
                $this->logger->info("WhatsApp message sent", [
                    'phone' => $phone,
                    'message_id' => $response['data']['messages'][0]['id'] ?? null
                ]);
                
                return [
                    'success' => true,
                    'message_id' => $response['data']['messages'][0]['id'] ?? null
                ];
            }
            
            throw new \Exception('WhatsApp message failed');
            
        } catch (\Exception $e) {
            $this->logger->error("WhatsApp message error: " . $e->getMessage(), [
                'phone' => $phone,
                'message' => $message
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enviar evento a Google Analytics
     */
    public function sendGoogleAnalyticsEvent(array $eventData): array
    {
        if (!$this->config['ga_enabled']) {
            return ['success' => false, 'error' => 'Google Analytics integration disabled'];
        }
        
        try {
            $data = [
                'client_id' => $eventData['client_id'] ?? uniqid(),
                'events' => [[
                    'name' => $eventData['event_name'],
                    'parameters' => $eventData['parameters'] ?? []
                ]]
            ];
            
            $response = $this->sendHttpRequest(
                "https://www.google-analytics.com/mp/collect?measurement_id={$this->config['ga_tracking_id']}&api_secret={$this->config['ga_api_key']}",
                'POST',
                $data,
                ['Content-Type: application/json']
            );
            
            if ($response['success']) {
                $this->logger->debug("Google Analytics event sent", $eventData);
                
                return ['success' => true];
            }
            
            throw new \Exception('Google Analytics event failed');
            
        } catch (\Exception $e) {
            $this->logger->error("Google Analytics error: " . $e->getMessage(), $eventData);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Suscribir email a Mailchimp
     */
    public function subscribeToMailchimp(string $email, array $mergeFields = []): array
    {
        if (!$this->config['mailchimp_enabled']) {
            return ['success' => false, 'error' => 'Mailchimp integration disabled'];
        }
        
        try {
            $data = [
                'email_address' => $email,
                'status' => 'subscribed',
                'merge_fields' => $mergeFields
            ];
            
            $dc = explode('-', $this->config['mailchimp_api_key'])[1];
            $url = "https://{$dc}.api.mailchimp.com/3.0/lists/{$this->config['mailchimp_list_id']}/members";
            
            $response = $this->sendHttpRequest(
                $url,
                'POST',
                $data,
                [
                    'Authorization: Basic ' . base64_encode('user:' . $this->config['mailchimp_api_key']),
                    'Content-Type: application/json'
                ]
            );
            
            if ($response['success']) {
                $this->logger->info("Mailchimp subscription successful", ['email' => $email]);
                
                return [
                    'success' => true,
                    'subscriber_id' => $response['data']['id'] ?? null
                ];
            }
            
            throw new \Exception('Mailchimp subscription failed');
            
        } catch (\Exception $e) {
            $this->logger->error("Mailchimp error: " . $e->getMessage(), ['email' => $email]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enviar SMS con Twilio
     */
    public function sendTwilioSMS(string $phone, string $message): array
    {
        if (!$this->config['twilio_enabled']) {
            return ['success' => false, 'error' => 'Twilio integration disabled'];
        }
        
        try {
            $data = [
                'From' => $this->config['twilio_phone'],
                'To' => $phone,
                'Body' => $message
            ];
            
            $response = $this->sendHttpRequest(
                "https://api.twilio.com/2010-04-01/Accounts/{$this->config['twilio_sid']}/Messages.json",
                'POST',
                http_build_query($data),
                [
                    'Authorization: Basic ' . base64_encode($this->config['twilio_sid'] . ':' . $this->config['twilio_token']),
                    'Content-Type: application/x-www-form-urlencoded'
                ]
            );
            
            if ($response['success']) {
                $this->logger->info("Twilio SMS sent", [
                    'phone' => $phone,
                    'message_sid' => $response['data']['sid'] ?? null
                ]);
                
                return [
                    'success' => true,
                    'message_sid' => $response['data']['sid'] ?? null
                ];
            }
            
            throw new \Exception('Twilio SMS failed');
            
        } catch (\Exception $e) {
            $this->logger->error("Twilio SMS error: " . $e->getMessage(), [
                'phone' => $phone,
                'message' => $message
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enviar notificación a Slack
     */
    public function sendSlackNotification(string $message, string $channel = null): array
    {
        if (!$this->config['slack_enabled']) {
            return ['success' => false, 'error' => 'Slack integration disabled'];
        }
        
        try {
            $data = [
                'text' => $message,
                'channel' => $channel ?? $this->config['slack_channel'],
                'username' => 'MrCell Bot',
                'icon_emoji' => ':robot_face:'
            ];
            
            $response = $this->sendHttpRequest(
                $this->config['slack_webhook_url'],
                'POST',
                $data,
                ['Content-Type: application/json']
            );
            
            if ($response['success']) {
                $this->logger->debug("Slack notification sent", ['message' => $message]);
                
                return ['success' => true];
            }
            
            throw new \Exception('Slack notification failed');
            
        } catch (\Exception $e) {
            $this->logger->error("Slack notification error: " . $e->getMessage(), [
                'message' => $message,
                'channel' => $channel
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Sincronizar datos con sistema externo
     */
    public function syncExternalData(string $system, array $data): array
    {
        try {
            // Registrar sincronización
            $syncId = $this->db->table('integration_syncs')->insert([
                'system' => $system,
                'data_type' => $data['type'] ?? 'unknown',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Procesar según el sistema
            switch ($system) {
                case 'inventory':
                    $result = $this->syncInventoryData($data);
                    break;
                case 'accounting':
                    $result = $this->syncAccountingData($data);
                    break;
                case 'crm':
                    $result = $this->syncCRMData($data);
                    break;
                default:
                    throw new \Exception("Unknown system: $system");
            }
            
            // Actualizar estado de sincronización
            $this->db->table('integration_syncs')
                    ->where('id', $syncId)
                    ->update([
                        'status' => $result['success'] ? 'completed' : 'failed',
                        'response' => json_encode($result),
                        'completed_at' => date('Y-m-d H:i:s')
                    ]);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("External sync error: " . $e->getMessage(), [
                'system' => $system,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener estadísticas de integraciones
     */
    public function getIntegrationStats(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
            
            $stats = [
                'enabled_integrations' => $this->getEnabledIntegrations(),
                'total_requests' => $this->getTotalRequests($dateFrom),
                'success_rate' => $this->getSuccessRate($dateFrom),
                'top_integrations' => $this->getTopIntegrations($dateFrom),
                'recent_errors' => $this->getRecentErrors($dateFrom)
            ];
            
            return [
                'success' => true,
                'period' => "$days days",
                'stats' => $stats
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getPayPalAccessToken(): ?string
    {
        $cacheKey = 'paypal_access_token';
        $token = $this->cache->get($cacheKey);
        
        if ($token) {
            return $token;
        }
        
        $baseUrl = $this->config['paypal_sandbox'] 
            ? 'https://api.sandbox.paypal.com' 
            : 'https://api.paypal.com';
        
        $response = $this->sendHttpRequest(
            $baseUrl . '/v1/oauth2/token',
            'POST',
            'grant_type=client_credentials',
            [
                'Authorization: Basic ' . base64_encode($this->config['paypal_client_id'] . ':' . $this->config['paypal_client_secret']),
                'Content-Type: application/x-www-form-urlencoded'
            ]
        );
        
        if ($response['success'] && isset($response['data']['access_token'])) {
            $token = $response['data']['access_token'];
            $expiresIn = $response['data']['expires_in'] ?? 3600;
            
            $this->cache->set($cacheKey, $token, $expiresIn - 60); // Cache con 1 minuto menos
            
            return $token;
        }
        
        return null;
    }
    
    private function getPayPalApprovalUrl(array $orderData): ?string
    {
        foreach ($orderData['links'] ?? [] as $link) {
            if ($link['rel'] === 'approve') {
                return $link['href'];
            }
        }
        
        return null;
    }
    
    private function sendHttpRequest(string $url, string $method, $data, array $headers): array
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['timeout'],
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($data) ? json_encode($data) : $data);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false) {
            return [
                'success' => false,
                'error' => $error ?: 'HTTP request failed',
                'http_code' => 0
            ];
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'http_code' => $httpCode,
            'data' => $decodedResponse,
            'raw_response' => $response
        ];
    }
    
    private function syncInventoryData(array $data): array
    {
        // Simular sincronización de inventario
        return ['success' => true, 'synced_items' => count($data['items'] ?? [])];
    }
    
    private function syncAccountingData(array $data): array
    {
        // Simular sincronización contable
        return ['success' => true, 'synced_transactions' => count($data['transactions'] ?? [])];
    }
    
    private function syncCRMData(array $data): array
    {
        // Simular sincronización CRM
        return ['success' => true, 'synced_contacts' => count($data['contacts'] ?? [])];
    }
    
    private function getEnabledIntegrations(): array
    {
        $enabled = [];
        
        if ($this->config['paypal_enabled']) $enabled[] = 'PayPal';
        if ($this->config['stripe_enabled']) $enabled[] = 'Stripe';
        if ($this->config['whatsapp_enabled']) $enabled[] = 'WhatsApp';
        if ($this->config['ga_enabled']) $enabled[] = 'Google Analytics';
        if ($this->config['mailchimp_enabled']) $enabled[] = 'Mailchimp';
        if ($this->config['twilio_enabled']) $enabled[] = 'Twilio';
        if ($this->config['slack_enabled']) $enabled[] = 'Slack';
        
        return $enabled;
    }
    
    private function getTotalRequests(string $dateFrom): int
    {
        return 1250; // Simular
    }
    
    private function getSuccessRate(string $dateFrom): float
    {
        return 98.5; // Simular
    }
    
    private function getTopIntegrations(string $dateFrom): array
    {
        return [
            ['name' => 'PayPal', 'requests' => 450],
            ['name' => 'WhatsApp', 'requests' => 320],
            ['name' => 'Mailchimp', 'requests' => 180]
        ];
    }
    
    private function getRecentErrors(string $dateFrom): array
    {
        return [
            ['integration' => 'Stripe', 'error' => 'Invalid API key', 'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours'))],
            ['integration' => 'Twilio', 'error' => 'Rate limit exceeded', 'timestamp' => date('Y-m-d H:i:s', strtotime('-5 hours'))]
        ];
    }
    
    private function createIntegrationTables(): void
    {
        try {
            $this->db->query("
                CREATE TABLE IF NOT EXISTS integration_syncs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    system VARCHAR(100) NOT NULL,
                    data_type VARCHAR(100) NOT NULL,
                    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
                    response JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP NULL,
                    INDEX idx_system (system),
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                )
            ");
        } catch (\Exception $e) {
            $this->logger->error("Integration tables creation failed: " . $e->getMessage());
        }
    }
    
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'integrations' => [
                'paypal' => $this->config['paypal_enabled'],
                'stripe' => $this->config['stripe_enabled'],
                'whatsapp' => $this->config['whatsapp_enabled'],
                'google_analytics' => $this->config['ga_enabled'],
                'mailchimp' => $this->config['mailchimp_enabled'],
                'twilio' => $this->config['twilio_enabled'],
                'slack' => $this->config['slack_enabled']
            ]
        ];
    }
}
