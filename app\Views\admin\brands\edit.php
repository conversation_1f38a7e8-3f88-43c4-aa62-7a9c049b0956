<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-edit me-2"></i>Editar Marca: <?= esc($brand['name']) ?></h1>
        <a href="/admin/brands" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver a Marcas
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información de la Marca</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre de la Marca <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= esc(old('name', $brand['name'])) ?>" required>
                                <div class="form-text">Nombre comercial de la marca</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="website" class="form-label">Sitio Web</label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="<?= esc(old('website', $brand['website'])) ?>" placeholder="https://ejemplo.com">
                                <div class="form-text">URL del sitio web oficial</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= esc(old('description', $brand['description'])) ?></textarea>
                        <div class="form-text">Descripción breve de la marca</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email de Contacto</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= esc(old('email', $brand['email'])) ?>" placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Teléfono</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= esc(old('phone', $brand['phone'])) ?>" placeholder="+502 1234-5678">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Dirección</label>
                        <textarea class="form-control" id="address" name="address" rows="2"><?= esc(old('address', $brand['address'])) ?></textarea>
                        <div class="form-text">Dirección física de la marca (opcional)</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-image me-2"></i>Logo de la Marca</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($brand['logo'])): ?>
                        <div class="mb-3">
                            <label class="form-label">Logo Actual</label>
                            <div class="text-center">
                                <img src="/<?= esc($brand['logo']) ?>" alt="Logo actual" class="img-fluid" style="max-height: 150px; border-radius: 8px;">
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="logo" class="form-label">Cambiar Logo</label>
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                        <div class="form-text">Formatos: JPG, PNG, GIF. Máximo 2MB. Deja vacío para mantener el actual.</div>
                    </div>
                    
                    <div id="logoPreview" class="text-center" style="display: none;">
                        <img id="logoImage" src="" alt="Vista previa" class="img-fluid" style="max-height: 150px; border-radius: 8px;">
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuración</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">Orden de Clasificación</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                               value="<?= esc(old('sort_order', $brand['sort_order'])) ?>" min="0">
                        <div class="form-text">Orden para mostrar en listas (menor = primero)</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" <?= old('is_active', $brand['is_active']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                Marca Activa
                            </label>
                        </div>
                        <div class="form-text">Solo las marcas activas aparecen en el sitio web</div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Marca
                        </button>
                        <a href="/admin/brands" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancelar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    console.log('Editar marca cargada');

    // Vista previa del logo
    document.getElementById('logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('logoPreview');
        const image = document.getElementById('logoImage');

        if (file) {
            // Validar tamaño del archivo (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('El archivo es demasiado grande. El tamaño máximo es 2MB.');
                e.target.value = '';
                preview.style.display = 'none';
                return;
            }

            // Validar tipo de archivo
            if (!file.type.startsWith('image/')) {
                alert('Por favor selecciona un archivo de imagen válido.');
                e.target.value = '';
                preview.style.display = 'none';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                image.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // Validación del formulario
    document.querySelector('form').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('El nombre de la marca es obligatorio.');
            document.getElementById('name').focus();
            return false;
        }

        if (name.length < 2) {
            e.preventDefault();
            alert('El nombre de la marca debe tener al menos 2 caracteres.');
            document.getElementById('name').focus();
            return false;
        }
    });
</script>

<style>
#logoPreview {
    margin-top: 15px;
    padding: 15px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.card-header h5 {
    color: #495057;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
    border: none;
    font-weight: 500;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #991b1b 0%, #7f1d1d 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}
</style>
<?= $this->endSection() ?>
