<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .stats-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
        border-color: var(--primary-light);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin: 0 auto 1rem;
    }

    .stats-icon.orders { background: linear-gradient(135deg, var(--primary-color), #c53030); }
    .stats-icon.wishlist { background: linear-gradient(135deg, #e53e3e, #fc8181); }
    .stats-icon.points { background: linear-gradient(135deg, #3182ce, #63b3ed); }
    .stats-icon.savings { background: linear-gradient(135deg, #38a169, #68d391); }

    .order-item {
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.1);
        border-color: var(--primary-light);
    }

    .order-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    .status-pending {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-processing {
        background: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-shipped {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-delivered {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }

    .status-cancelled {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .welcome-card {
        background: linear-gradient(135deg, var(--primary-color), #c53030);
        color: white;
        border: none;
    }

    .welcome-card h2 {
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Mi Cuenta</h1>
                <p class="mb-0 mt-2 opacity-75">Panel de control de tu cuenta</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item active">Mi Cuenta</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= $user['first_name'] ?? 'Usuario' ?> <?= $user['last_name'] ?? '' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>

                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>" class="active"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="col-lg-9">
            <!-- Welcome Message -->
            <div class="dashboard-card welcome-card">
                <h2 class="mb-3">¡Bienvenido de vuelta, <?= $user['first_name'] ?? 'Usuario' ?>!</h2>
                <p class="mb-0 opacity-90">Desde tu panel de control puedes ver tus pedidos recientes, gestionar tu información personal y mucho más.</p>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon orders">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h4 class="mb-1"><?= $stats['total_orders'] ?? 0 ?></h4>
                        <small class="text-muted">Pedidos Totales</small>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon wishlist">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h4 class="mb-1"><?= $stats['wishlist_items'] ?? 0 ?></h4>
                        <small class="text-muted">Lista de Deseos</small>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon points">
                            <i class="fas fa-star"></i>
                        </div>
                        <h4 class="mb-1"><?= $stats['loyalty_points'] ?? 0 ?></h4>
                        <small class="text-muted">Puntos</small>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon savings">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                        <h4 class="mb-1">Q<?= number_format($stats['total_savings'] ?? 0, 2) ?></h4>
                        <small class="text-muted">Ahorrado</small>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="dashboard-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Pedidos Recientes</h4>
                    <a href="<?= base_url('cuenta/pedidos') ?>" class="btn btn-outline-primary btn-sm">
                        Ver Todos
                    </a>
                </div>

                <?php if (empty($recent_orders)): ?>
                    <div class="empty-state">
                        <i class="fas fa-shopping-bag"></i>
                        <h5>No tienes pedidos aún</h5>
                        <p class="mb-4">Cuando realices tu primera compra, aparecerá aquí.</p>
                        <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                            <i class="fas fa-shopping-cart me-2"></i>Comenzar a Comprar
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($recent_orders as $order): ?>
                        <div class="order-item">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <strong>#<?= esc($order['order_number'] ?? 'MRC-000') ?></strong>
                                    <br><small class="text-muted"><?= date('d M Y', strtotime($order['created_at'] ?? 'now')) ?></small>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="mb-1"><?= esc($order['product_name'] ?? 'Producto') ?></h6>
                                    <small class="text-muted"><?= $order['total_items'] ?? 1 ?> producto(s)</small>
                                </div>
                                <div class="col-md-2">
                                    <strong>Q<?= number_format($order['total_amount'] ?? 0, 2) ?></strong>
                                </div>
                                <div class="col-md-2">
                                    <span class="order-status status-<?= strtolower($order['status'] ?? 'pending') ?>">
                                        <?= ucfirst($order['status'] ?? 'Pendiente') ?>
                                    </span>
                                </div>
                                <div class="col-md-2 text-end">
                                    <a href="<?= base_url('cuenta/pedidos/' . ($order['id'] ?? '1')) ?>"
                                       class="btn btn-outline-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <div class="text-center mt-4">
                        <p class="text-muted">¿No encuentras lo que buscas? <a href="<?= base_url('tienda') ?>">Explora nuestra tienda</a></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card">
                <h4 class="mb-3">Acciones Rápidas</h4>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="<?= base_url('cuenta/perfil') ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-edit me-2"></i>Actualizar Perfil
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="<?= base_url('cuenta/direcciones') ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-map-marker-alt me-2"></i>Gestionar Direcciones
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="<?= base_url('cuenta/seguridad') ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-lock me-2"></i>Configurar Seguridad
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Dashboard functionality can be added here
console.log('Dashboard loaded successfully');
</script>
<?= $this->endSection() ?>
