<?php

namespace App\Controllers;

use App\Services\CountryService;

class RegisterController extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Crear Cuenta - MrCell Guatemala',
            'page_title' => 'Crear Cuenta',
            'countries' => CountryService::getCountries(),
            'defaultCountry' => CountryService::detectCountryByIP()
        ];

        return view('frontend/auth/register', $data);
    }

    /**
     * API endpoint para detectar país por IP
     */
    public function detectCountry()
    {
        $countryCode = CountryService::detectCountryByIP();
        $countryInfo = CountryService::getCountryInfo($countryCode);

        return $this->response->setJSON([
            'country_code' => $countryCode,
            'country_info' => $countryInfo
        ]);
    }

    /**
     * Debug endpoint para ver información detallada de detección IP
     */
    public function debugCountryDetection()
    {
        $debug = CountryService::debugCountryDetection();

        echo "<h1>🔍 Debug - Detección de País por IP</h1>";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; margin: 20px 0;'>";

        foreach ($debug as $key => $value) {
            echo "<p><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> ";
            if (is_array($value)) {
                echo "<pre>" . json_encode($value, JSON_PRETTY_PRINT) . "</pre>";
            } else {
                echo htmlspecialchars($value);
            }
            echo "</p>";
        }

        echo "</div>";

        $countryCode = $debug['final_country_code'];
        $countryInfo = CountryService::getCountryInfo($countryCode);

        echo "<h2>🎯 Resultado Final</h2>";
        echo "<p><strong>País detectado:</strong> {$countryInfo['flag']} {$countryInfo['name']} (+{$countryInfo['code']})</p>";

        exit;
    }

    public function store()
    {
        try {
            // Validar datos del formulario
            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|min_length[2]|max_length[100]',
                'email' => 'required|valid_email|max_length[100]',
                'password' => 'required|min_length[6]',
                'password_confirm' => 'required|matches[password]',
                'phone' => 'required|min_length[8]|max_length[15]',
                'terms' => 'required'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return redirect()->back()->withInput()->with('errors', $validation->getErrors());
            }

            // Datos del formulario
            $name = $this->request->getPost('name');
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
            $phone = $this->request->getPost('phone');

            // Procesar teléfono - obtener código de país del formulario
            $countryCode = $this->request->getPost('country_code') ?? '502';
            $phone = preg_replace('/\D/', '', $phone);

            // Si el teléfono no tiene código de país, agregarlo
            if (!str_starts_with($phone, $countryCode)) {
                $phone = $countryCode . $phone;
            }

            // Hashear contraseña
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Conectar a base de datos
            $db = \Config\Database::connect();

            // Verificar si el email ya existe
            $existingUser = $db->query("SELECT id FROM users WHERE email = ? AND deleted_at IS NULL", [$email])->getRow();
            if ($existingUser) {
                return redirect()->back()->withInput()->with('error', 'El email ya está registrado.');
            }

            // Registrar usuario
            // Split name into first_name and last_name
            $nameParts = explode(' ', trim($name), 2);
            $firstName = $nameParts[0] ?? '';
            $lastName = $nameParts[1] ?? '';

            try {
                // Generate UUID and username
                $uuid = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                    mt_rand(0, 0xffff),
                    mt_rand(0, 0x0fff) | 0x4000,
                    mt_rand(0, 0x3fff) | 0x8000,
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
                );

                // Generate username from email
                $username = explode('@', $email)[0] . '_' . mt_rand(1000, 9999);

                // Format phone number with country code
                $formattedPhone = '+' . $phone;

                $insertData = [
                    'uuid' => $uuid,
                    'username' => $username,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $email,
                    'password' => $hashedPassword,
                    'phone' => $formattedPhone,
                    'role' => 'user',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $userId = $db->table('users')->insert($insertData);

                if ($userId) {
                    // Disparar evento de usuario registrado para WhatsApp
                    $userDataForEvent = [
                        'id' => $userId,
                        'name' => $firstName . ' ' . $lastName,
                        'email' => $email,
                        'phone' => $formattedPhone
                    ];
                    \App\Libraries\WhatsAppEventHandler::triggerUserRegistered($userDataForEvent);

                    // Registro exitoso - redirigir a página de verificación
                    session()->set([
                        'registration_success' => true,
                        'new_user_id' => $userId,
                        'new_user_phone' => $formattedPhone
                    ]);
                    return redirect()->to('/registro-exitoso');
                } else {
                    throw new \Exception("Error al crear la cuenta");
                }

            } catch (\Exception $e) {
                return redirect()->back()->withInput()->with('error', 'Error al crear la cuenta: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error inesperado: ' . $e->getMessage());
        }
    }

    /**
     * Página de registro exitoso con modal de verificación
     */
    public function success()
    {
        // Verificar que el usuario viene de un registro exitoso
        if (!session()->get('registration_success')) {
            return redirect()->to('/login');
        }

        $data = [
            'title' => 'Registro Exitoso - MrCell Guatemala',
            'page_title' => 'Bienvenido a MrCell',
            'user_phone' => session()->get('new_user_phone'),
            'show_verification_modal' => true
        ];

        // Limpiar datos de sesión
        session()->remove(['registration_success', 'new_user_id', 'new_user_phone']);

        return view('frontend/auth/registration_success', $data);
    }
}
