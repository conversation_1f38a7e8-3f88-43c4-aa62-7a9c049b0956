<?php

namespace App\Libraries;

use CodeIgniter\Email\Email;

/**
 * Sistema de Notificaciones por Email
 * Templates HTML responsivos para wishlist y alertas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class EmailNotifier
{
    private $email;
    private $config;
    
    public function __construct()
    {
        $this->email = \Config\Services::email();
        $this->config = [
            'enabled' => env('EMAIL_NOTIFICATIONS_ENABLED', true),
            'from_email' => env('EMAIL_FROM', '<EMAIL>'),
            'from_name' => env('EMAIL_FROM_NAME', 'MrCell Guatemala'),
            'reply_to' => env('EMAIL_REPLY_TO', '<EMAIL>'),
            'base_url' => base_url(),
            'logo_url' => base_url('assets/images/logo.png'),
            'social_links' => [
                'facebook' => 'https://facebook.com/mrcellguatemala',
                'instagram' => 'https://instagram.com/mrcellguatemala',
                'whatsapp' => 'https://wa.me/50212345678'
            ]
        ];
    }
    
    /**
     * Enviar alerta de precio por email
     */
    public function sendPriceAlert(array $user, array $product, float $oldPrice, float $newPrice): array
    {
        if (!$this->config['enabled'] || empty($user['email'])) {
            return ['success' => false, 'error' => 'Email not enabled or email missing'];
        }
        
        $savings = $oldPrice - $newPrice;
        $percentage = round(($savings / $oldPrice) * 100, 1);
        
        $data = [
            'user' => $user,
            'product' => $product,
            'old_price' => $oldPrice,
            'new_price' => $newPrice,
            'savings' => $savings,
            'percentage' => $percentage,
            'product_url' => base_url('producto/' . $product['slug']),
            'unsubscribe_url' => base_url('unsubscribe/' . base64_encode($user['email']))
        ];
        
        $subject = "🔥 ¡Alerta de Precio! {$product['name']} - {$percentage}% de descuento";
        $htmlContent = $this->renderTemplate('price_alert', $data);
        $textContent = $this->generateTextVersion($htmlContent);
        
        return $this->sendEmail($user['email'], $subject, $htmlContent, $textContent);
    }
    
    /**
     * Enviar notificación de stock disponible
     */
    public function sendBackInStockAlert(array $user, array $product): array
    {
        if (!$this->config['enabled'] || empty($user['email'])) {
            return ['success' => false, 'error' => 'Email not enabled or email missing'];
        }
        
        $data = [
            'user' => $user,
            'product' => $product,
            'price' => $product['price_sale'] ?? $product['price_regular'],
            'product_url' => base_url('producto/' . $product['slug']),
            'unsubscribe_url' => base_url('unsubscribe/' . base64_encode($user['email']))
        ];
        
        $subject = "📦 ¡{$product['name']} ya está disponible!";
        $htmlContent = $this->renderTemplate('back_in_stock', $data);
        $textContent = $this->generateTextVersion($htmlContent);
        
        return $this->sendEmail($user['email'], $subject, $htmlContent, $textContent);
    }
    
    /**
     * Enviar recordatorio de wishlist
     */
    public function sendWishlistReminder(array $user, array $wishlistItems): array
    {
        if (!$this->config['enabled'] || empty($user['email']) || empty($wishlistItems)) {
            return ['success' => false, 'error' => 'Email not enabled, email missing, or empty wishlist'];
        }
        
        $data = [
            'user' => $user,
            'wishlist_items' => $wishlistItems,
            'total_items' => count($wishlistItems),
            'wishlist_url' => base_url('wishlist'),
            'unsubscribe_url' => base_url('unsubscribe/' . base64_encode($user['email']))
        ];
        
        $subject = "❤️ Tu lista de deseos te está esperando - " . count($wishlistItems) . " productos";
        $htmlContent = $this->renderTemplate('wishlist_reminder', $data);
        $textContent = $this->generateTextVersion($htmlContent);
        
        return $this->sendEmail($user['email'], $subject, $htmlContent, $textContent);
    }
    
    /**
     * Enviar newsletter de wishlist personalizada
     */
    public function sendPersonalizedNewsletter(array $user, array $recommendations): array
    {
        if (!$this->config['enabled'] || empty($user['email'])) {
            return ['success' => false, 'error' => 'Email not enabled or email missing'];
        }
        
        $data = [
            'user' => $user,
            'recommendations' => $recommendations,
            'newsletter_url' => base_url('newsletter'),
            'unsubscribe_url' => base_url('unsubscribe/' . base64_encode($user['email']))
        ];
        
        $subject = "🎯 Productos recomendados especialmente para ti";
        $htmlContent = $this->renderTemplate('personalized_newsletter', $data);
        $textContent = $this->generateTextVersion($htmlContent);
        
        return $this->sendEmail($user['email'], $subject, $htmlContent, $textContent);
    }
    
    /**
     * Enviar resumen semanal de wishlist
     */
    public function sendWeeklySummary(array $user, array $summaryData): array
    {
        if (!$this->config['enabled'] || empty($user['email'])) {
            return ['success' => false, 'error' => 'Email not enabled or email missing'];
        }
        
        $data = [
            'user' => $user,
            'summary' => $summaryData,
            'week_start' => date('d/m/Y', strtotime('-7 days')),
            'week_end' => date('d/m/Y'),
            'wishlist_url' => base_url('wishlist'),
            'unsubscribe_url' => base_url('unsubscribe/' . base64_encode($user['email']))
        ];
        
        $subject = "📊 Tu resumen semanal de MrCell - Semana del " . $data['week_start'];
        $htmlContent = $this->renderTemplate('weekly_summary', $data);
        $textContent = $this->generateTextVersion($htmlContent);
        
        return $this->sendEmail($user['email'], $subject, $htmlContent, $textContent);
    }
    
    /**
     * Renderizar template de email
     */
    private function renderTemplate(string $templateName, array $data): string
    {
        $templatePath = APPPATH . 'Views/emails/' . $templateName . '.php';
        
        if (!file_exists($templatePath)) {
            // Si no existe el template específico, usar template genérico
            return $this->renderGenericTemplate($templateName, $data);
        }
        
        // Agregar datos globales
        $data['config'] = $this->config;
        $data['base_template'] = $this->getBaseTemplate();
        
        ob_start();
        extract($data);
        include $templatePath;
        return ob_get_clean();
    }
    
    /**
     * Renderizar template genérico
     */
    private function renderGenericTemplate(string $templateName, array $data): string
    {
        $baseTemplate = $this->getBaseTemplate();
        
        switch ($templateName) {
            case 'price_alert':
                $content = $this->getPriceAlertContent($data);
                break;
                
            case 'back_in_stock':
                $content = $this->getBackInStockContent($data);
                break;
                
            case 'wishlist_reminder':
                $content = $this->getWishlistReminderContent($data);
                break;
                
            case 'personalized_newsletter':
                $content = $this->getPersonalizedNewsletterContent($data);
                break;
                
            case 'weekly_summary':
                $content = $this->getWeeklySummaryContent($data);
                break;
                
            default:
                $content = '<p>Contenido del email no disponible.</p>';
        }
        
        return str_replace('{{CONTENT}}', $content, $baseTemplate);
    }
    
    /**
     * Obtener template base HTML
     */
    private function getBaseTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MrCell Guatemala</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .header img { max-height: 50px; }
        .content { padding: 30px; }
        .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; background: #f9f9f9; }
        .product-image { max-width: 150px; height: auto; border-radius: 8px; }
        .price { font-size: 24px; font-weight: bold; color: #667eea; }
        .old-price { text-decoration: line-through; color: #999; margin-right: 10px; }
        .savings { background: #e74c3c; color: white; padding: 5px 10px; border-radius: 20px; font-size: 14px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
        .footer { background: #333; color: white; padding: 20px; text-align: center; font-size: 12px; }
        .social-links a { color: white; margin: 0 10px; text-decoration: none; }
        @media (max-width: 600px) {
            .container { width: 100% !important; }
            .content { padding: 15px !important; }
            .product-card { padding: 15px !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="' . $this->config['logo_url'] . '" alt="MrCell Guatemala">
            <h1>MrCell Guatemala</h1>
            <p>Tu tienda de tecnología de confianza</p>
        </div>
        
        <div class="content">
            {{CONTENT}}
        </div>
        
        <div class="footer">
            <p><strong>MrCell Guatemala</strong></p>
            <p>Tu tienda de tecnología de confianza</p>
            <div class="social-links">
                <a href="' . $this->config['social_links']['facebook'] . '">Facebook</a>
                <a href="' . $this->config['social_links']['instagram'] . '">Instagram</a>
                <a href="' . $this->config['social_links']['whatsapp'] . '">WhatsApp</a>
            </div>
            <p style="margin-top: 20px;">
                <a href="{{unsubscribe_url}}" style="color: #ccc;">Cancelar suscripción</a>
            </p>
        </div>
    </div>
</body>
</html>';
    }
    
    /**
     * Contenido para alerta de precio
     */
    private function getPriceAlertContent(array $data): string
    {
        $userName = $data['user']['first_name'] ?? $data['user']['name'] ?? 'Cliente';
        
        return "
            <h2>🔥 ¡Alerta de Precio!</h2>
            <p>¡Hola {$userName}!</p>
            <p>El producto que tienes en tu lista de deseos ha bajado de precio:</p>
            
            <div class='product-card'>
                <h3>{$data['product']['name']}</h3>
                <div style='display: flex; align-items: center; gap: 20px;'>
                    <img src='" . base_url('uploads/' . ($data['product']['featured_image'] ?? 'no-image.jpg')) . "' alt='{$data['product']['name']}' class='product-image'>
                    <div>
                        <p class='price'>
                            <span class='old-price'>Q" . number_format($data['old_price'], 2) . "</span>
                            Q" . number_format($data['new_price'], 2) . "
                        </p>
                        <span class='savings'>Te ahorras Q" . number_format($data['savings'], 2) . " ({$data['percentage']}%)</span>
                    </div>
                </div>
            </div>
            
            <p>¡No te lo pierdas! Esta oferta podría terminar pronto.</p>
            
            <a href='{$data['product_url']}' class='button'>Ver Producto</a>
            
            <p style='font-size: 12px; color: #666; margin-top: 30px;'>
                Este email fue enviado porque tienes alertas de precio activadas en tu lista de deseos.
            </p>
        ";
    }
    
    /**
     * Contenido para stock disponible
     */
    private function getBackInStockContent(array $data): string
    {
        $userName = $data['user']['first_name'] ?? $data['user']['name'] ?? 'Cliente';
        
        return "
            <h2>📦 ¡Producto Disponible!</h2>
            <p>¡Hola {$userName}!</p>
            <p>El producto que esperabas ya está disponible:</p>
            
            <div class='product-card'>
                <h3>{$data['product']['name']}</h3>
                <div style='display: flex; align-items: center; gap: 20px;'>
                    <img src='" . base_url('uploads/' . ($data['product']['featured_image'] ?? 'no-image.jpg')) . "' alt='{$data['product']['name']}' class='product-image'>
                    <div>
                        <p class='price'>Q" . number_format($data['price'], 2) . "</p>
                        <p>📦 Stock: {$data['product']['stock_quantity']} unidades</p>
                    </div>
                </div>
            </div>
            
            <p>¡Cómpralo antes de que se agote!</p>
            
            <a href='{$data['product_url']}' class='button'>Comprar Ahora</a>
        ";
    }
    
    /**
     * Contenido para recordatorio de wishlist
     */
    private function getWishlistReminderContent(array $data): string
    {
        $userName = $data['user']['first_name'] ?? $data['user']['name'] ?? 'Cliente';
        $itemsHtml = '';
        
        foreach (array_slice($data['wishlist_items'], 0, 5) as $item) {
            $price = $item['price_sale'] ?? $item['price_regular'];
            $itemsHtml .= "
                <div style='border-bottom: 1px solid #eee; padding: 15px 0;'>
                    <div style='display: flex; align-items: center; gap: 15px;'>
                        <img src='" . base_url('uploads/' . ($item['featured_image'] ?? 'no-image.jpg')) . "' alt='{$item['name']}' style='width: 80px; height: 80px; object-fit: cover; border-radius: 8px;'>
                        <div>
                            <h4 style='margin: 0 0 5px 0;'>{$item['name']}</h4>
                            <p style='margin: 0; color: #667eea; font-weight: bold;'>Q" . number_format($price, 2) . "</p>
                        </div>
                    </div>
                </div>
            ";
        }
        
        if (count($data['wishlist_items']) > 5) {
            $remaining = count($data['wishlist_items']) - 5;
            $itemsHtml .= "<p style='text-align: center; color: #666;'>... y {$remaining} productos más</p>";
        }
        
        return "
            <h2>❤️ Tu Lista de Deseos te está Esperando</h2>
            <p>¡Hola {$userName}!</p>
            <p>Tienes {$data['total_items']} productos en tu lista de deseos:</p>
            
            <div class='product-card'>
                {$itemsHtml}
            </div>
            
            <p>¿Qué tal si les echas un vistazo? Algunos podrían tener ofertas especiales.</p>
            
            <a href='{$data['wishlist_url']}' class='button'>Ver Mi Lista de Deseos</a>
        ";
    }
    
    /**
     * Generar versión de texto plano
     */
    private function generateTextVersion(string $html): string
    {
        // Remover HTML tags y convertir a texto plano
        $text = strip_tags($html);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    /**
     * Enviar email
     */
    private function sendEmail(string $to, string $subject, string $htmlContent, string $textContent = ''): array
    {
        try {
            $this->email->setFrom($this->config['from_email'], $this->config['from_name']);
            $this->email->setTo($to);
            $this->email->setReplyTo($this->config['reply_to']);
            $this->email->setSubject($subject);
            
            // Reemplazar placeholder de unsubscribe
            $unsubscribeUrl = base_url('unsubscribe/' . base64_encode($to));
            $htmlContent = str_replace('{{unsubscribe_url}}', $unsubscribeUrl, $htmlContent);
            
            $this->email->setMessage($htmlContent);
            
            if (!empty($textContent)) {
                $this->email->setAltMessage($textContent);
            }
            
            if ($this->email->send()) {
                return [
                    'success' => true,
                    'message_id' => uniqid('email_'),
                    'to' => $to
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $this->email->printDebugger(['headers']),
                    'to' => $to
                ];
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Email send error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'to' => $to
            ];
        }
    }
    
    /**
     * Verificar si email está habilitado
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'];
    }
    
    /**
     * Probar envío de email
     */
    public function testEmail(string $testEmail): array
    {
        $testData = [
            'user' => ['first_name' => 'Usuario', 'email' => $testEmail],
            'product' => [
                'name' => 'iPhone 13 Pro',
                'slug' => 'iphone-13-pro',
                'featured_image' => 'iphone-13-pro.jpg'
            ],
            'old_price' => 1200.00,
            'new_price' => 999.00,
            'savings' => 201.00,
            'percentage' => 16.8
        ];
        
        return $this->sendPriceAlert($testData['user'], $testData['product'], $testData['old_price'], $testData['new_price']);
    }
    
    /**
     * Obtener configuración
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'from_email' => $this->config['from_email'],
            'from_name' => $this->config['from_name']
        ];
    }
}
