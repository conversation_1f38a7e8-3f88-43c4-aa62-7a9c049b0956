<!-- Modal de Verificación de Teléfono -->
<div class="modal fade" id="phoneVerificationModal" tabindex="-1" aria-labelledby="phoneVerificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="phoneVerificationModalLabel">
                    <i class="fas fa-mobile-alt me-2"></i>Verificar Número de Teléfono
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Paso 1: Solicitar código -->
                <div id="step1" class="verification-step">
                    <div class="text-center mb-4">
                        <div class="verification-icon">
                            <i class="fas fa-shield-alt fa-3x text-primary"></i>
                        </div>
                        <h6 class="mt-3">Verificación de Seguridad</h6>
                        <p class="text-muted">Para recibir notificaciones por WhatsApp, necesitamos verificar tu número de teléfono.</p>
                    </div>

                    <form id="sendCodeForm">
                        <div class="mb-3">
                            <label for="verificationPhone" class="form-label">Número de Teléfono</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <img src="<?= base_url('assets/images/guatemala-flag.png') ?>" alt="GT" style="width: 20px; height: 15px;" class="me-1">
                                    +502
                                </span>
                                <input type="tel" class="form-control" id="verificationPhone" name="phone" 
                                       value="<?= isset($user['phone']) ? str_replace('+502', '', $user['phone']) : '' ?>" 
                                       placeholder="12345678" maxlength="8" required>
                            </div>
                            <div class="form-text">Ingresa tu número sin el código de país (+502)</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="sendCodeBtn">
                                <i class="fas fa-paper-plane me-2"></i>Enviar Código
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Paso 2: Verificar código -->
                <div id="step2" class="verification-step d-none">
                    <div class="text-center mb-4">
                        <div class="verification-icon">
                            <i class="fas fa-sms fa-3x text-success"></i>
                        </div>
                        <h6 class="mt-3">Código Enviado</h6>
                        <p class="text-muted">Hemos enviado un código de 6 dígitos a tu WhatsApp:</p>
                        <p class="fw-bold text-primary" id="sentToPhone"></p>
                    </div>

                    <form id="verifyCodeForm">
                        <div class="mb-3">
                            <label for="verificationCode" class="form-label">Código de Verificación</label>
                            <input type="text" class="form-control text-center fs-4 letter-spacing" 
                                   id="verificationCode" name="code" placeholder="000000" 
                                   maxlength="6" pattern="[0-9]{6}" required>
                            <div class="form-text">Ingresa el código de 6 dígitos que recibiste</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success" id="verifyCodeBtn">
                                <i class="fas fa-check me-2"></i>Verificar Código
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="resendCodeBtn">
                                <i class="fas fa-redo me-2"></i>Reenviar Código
                            </button>
                        </div>

                        <div class="text-center mt-3">
                            <small class="text-muted">
                                El código expira en <span id="countdown" class="fw-bold text-warning"></span>
                            </small>
                        </div>
                    </form>
                </div>

                <!-- Paso 3: Verificación exitosa -->
                <div id="step3" class="verification-step d-none">
                    <div class="text-center">
                        <div class="verification-icon">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                        </div>
                        <h6 class="mt-3 text-success">¡Verificación Exitosa!</h6>
                        <p class="text-muted">Tu número de teléfono ha sido verificado correctamente.</p>
                        <p class="text-muted">Ahora podrás recibir notificaciones importantes por WhatsApp.</p>
                    </div>
                </div>

                <!-- Alertas -->
                <div id="verificationAlert" class="alert d-none" role="alert"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeModalBtn">
                    <span id="closeModalText">Cerrar</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.verification-step {
    min-height: 300px;
}

.verification-icon {
    margin-bottom: 1rem;
}

.letter-spacing {
    letter-spacing: 0.5rem;
}

#verificationCode {
    font-family: 'Courier New', monospace;
}

.countdown-expired {
    color: #dc3545 !important;
}

.input-group-text img {
    border-radius: 2px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('phoneVerificationModal');
    const sendCodeForm = document.getElementById('sendCodeForm');
    const verifyCodeForm = document.getElementById('verifyCodeForm');
    const resendCodeBtn = document.getElementById('resendCodeBtn');
    const verificationAlert = document.getElementById('verificationAlert');
    
    let countdownInterval;
    let expiresAt;

    // Enviar código
    sendCodeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const phone = document.getElementById('verificationPhone').value;
        const fullPhone = '+502' + phone;
        
        if (!/^\d{8}$/.test(phone)) {
            showAlert('error', 'El número debe tener exactamente 8 dígitos');
            return;
        }

        const sendBtn = document.getElementById('sendCodeBtn');
        const originalText = sendBtn.innerHTML;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
        sendBtn.disabled = true;

        fetch('<?= base_url('phone-verification/send-code') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'phone=' + encodeURIComponent(fullPhone)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('sentToPhone').textContent = fullPhone;
                expiresAt = new Date(data.expires_at);
                showStep(2);
                startCountdown();
                showAlert('success', data.message);
            } else {
                showAlert('error', data.error);
            }
        })
        .catch(error => {
            showAlert('error', 'Error de conexión. Inténtalo de nuevo.');
        })
        .finally(() => {
            sendBtn.innerHTML = originalText;
            sendBtn.disabled = false;
        });
    });

    // Verificar código
    verifyCodeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const code = document.getElementById('verificationCode').value;
        
        if (!/^\d{6}$/.test(code)) {
            showAlert('error', 'El código debe tener exactamente 6 dígitos');
            return;
        }

        const verifyBtn = document.getElementById('verifyCodeBtn');
        const originalText = verifyBtn.innerHTML;
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verificando...';
        verifyBtn.disabled = true;

        fetch('<?= base_url('phone-verification/verify-code') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'code=' + encodeURIComponent(code)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStep(3);
                showAlert('success', data.message);
                document.getElementById('closeModalText').textContent = 'Continuar';
                clearInterval(countdownInterval);
                
                // Actualizar estado en la página principal si existe
                if (typeof updatePhoneVerificationStatus === 'function') {
                    updatePhoneVerificationStatus(true);
                }
            } else {
                showAlert('error', data.error);
            }
        })
        .catch(error => {
            showAlert('error', 'Error de conexión. Inténtalo de nuevo.');
        })
        .finally(() => {
            verifyBtn.innerHTML = originalText;
            verifyBtn.disabled = false;
        });
    });

    // Reenviar código
    resendCodeBtn.addEventListener('click', function() {
        showStep(1);
        clearInterval(countdownInterval);
        hideAlert();
    });

    // Funciones auxiliares
    function showStep(step) {
        document.querySelectorAll('.verification-step').forEach(el => el.classList.add('d-none'));
        document.getElementById('step' + step).classList.remove('d-none');
    }

    function showAlert(type, message) {
        verificationAlert.className = `alert alert-${type === 'error' ? 'danger' : type}`;
        verificationAlert.textContent = message;
        verificationAlert.classList.remove('d-none');
    }

    function hideAlert() {
        verificationAlert.classList.add('d-none');
    }

    function startCountdown() {
        const countdownEl = document.getElementById('countdown');
        
        countdownInterval = setInterval(() => {
            const now = new Date();
            const timeLeft = expiresAt - now;
            
            if (timeLeft <= 0) {
                countdownEl.textContent = 'EXPIRADO';
                countdownEl.classList.add('countdown-expired');
                clearInterval(countdownInterval);
                document.getElementById('verifyCodeBtn').disabled = true;
                showAlert('warning', 'El código ha expirado. Solicita uno nuevo.');
            } else {
                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                countdownEl.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    // Auto-focus en campos
    modal.addEventListener('shown.bs.modal', function() {
        document.getElementById('verificationPhone').focus();
    });

    // Limpiar al cerrar modal
    modal.addEventListener('hidden.bs.modal', function() {
        showStep(1);
        hideAlert();
        clearInterval(countdownInterval);
        sendCodeForm.reset();
        verifyCodeForm.reset();
        document.getElementById('closeModalText').textContent = 'Cerrar';
    });
});
</script>
