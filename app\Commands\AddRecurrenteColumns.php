<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AddRecurrenteColumns extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:add-recurrente-columns';
    protected $description = 'Agregar columnas de Recurrente a la tabla products';

    public function run(array $params)
    {
        CLI::write('=== Agregando columnas de Recurrente ===', 'yellow');
        CLI::newLine();

        $db = \Config\Database::connect();

        try {
            // Verificar si las columnas ya existen
            $fields = $db->getFieldData('products');
            $existingFields = array_column($fields, 'name');

            $columnsToAdd = [
                'recurrente_product_id' => "VARCHAR(100) NULL COMMENT 'ID del producto en Recurrente'",
                'recurrente_synced_at' => "DATETIME NULL COMMENT 'Última fecha de sincronización con Recurrente'",
                'recurrente_sync_status' => "ENUM('pending', 'synced', 'error', 'disabled') DEFAULT 'pending' COMMENT 'Estado de sincronización con Recurrente'"
            ];

            foreach ($columnsToAdd as $columnName => $columnDefinition) {
                if (in_array($columnName, $existingFields)) {
                    CLI::write("✓ Columna {$columnName} ya existe", 'yellow');
                    continue;
                }

                $sql = "ALTER TABLE products ADD COLUMN {$columnName} {$columnDefinition}";
                
                if ($columnName === 'recurrente_product_id') {
                    $sql .= " AFTER uuid";
                } elseif ($columnName === 'recurrente_synced_at') {
                    $sql .= " AFTER recurrente_product_id";
                } elseif ($columnName === 'recurrente_sync_status') {
                    $sql .= " AFTER recurrente_synced_at";
                }

                CLI::write("Agregando columna: {$columnName}", 'cyan');
                $result = $db->query($sql);
                
                if ($result) {
                    CLI::write("✓ Columna {$columnName} agregada correctamente", 'green');
                } else {
                    CLI::write("✗ Error agregando columna {$columnName}", 'red');
                }
            }

            // Agregar índices
            CLI::newLine();
            CLI::write('Agregando índices...', 'cyan');

            $indexes = [
                'idx_recurrente_product_id' => 'recurrente_product_id',
                'idx_recurrente_sync_status' => 'recurrente_sync_status'
            ];

            foreach ($indexes as $indexName => $columnName) {
                try {
                    $sql = "ALTER TABLE products ADD INDEX {$indexName} ({$columnName})";
                    $result = $db->query($sql);
                    CLI::write("✓ Índice {$indexName} agregado", 'green');
                } catch (\Exception $e) {
                    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                        CLI::write("✓ Índice {$indexName} ya existe", 'yellow');
                    } else {
                        CLI::write("✗ Error agregando índice {$indexName}: " . $e->getMessage(), 'red');
                    }
                }
            }

            CLI::newLine();
            CLI::write('=== Verificando estructura ===', 'yellow');
            
            // Verificar que las columnas se agregaron
            $fields = $db->getFieldData('products');
            $fieldNames = array_column($fields, 'name');
            
            foreach (array_keys($columnsToAdd) as $columnName) {
                if (in_array($columnName, $fieldNames)) {
                    CLI::write("✓ {$columnName} existe", 'green');
                } else {
                    CLI::write("✗ {$columnName} NO existe", 'red');
                }
            }

            CLI::newLine();
            CLI::write('Columnas de Recurrente agregadas correctamente', 'green');

        } catch (\Exception $e) {
            CLI::error("Error: " . $e->getMessage());
        }
    }
}
