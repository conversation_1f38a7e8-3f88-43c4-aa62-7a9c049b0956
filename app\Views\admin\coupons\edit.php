<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-edit me-2"></i>Editar Cupón: <?= esc($coupon['code']) ?>
    </h1>
    <div>
        <a href="<?= base_url('admin/coupons/coupons') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver a Lista
        </a>
        <a href="<?= base_url('admin/coupons/view/' . $coupon['id']) ?>" class="btn btn-info">
            <i class="fas fa-eye me-2"></i>Ver Detalles
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/coupons/update/' . $coupon['id']) ?>" method="POST" id="couponForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Información Básica -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información Básica</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">Código del Cupón <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="code" name="code" 
                                           value="<?= old('code', $coupon['code']) ?>" required maxlength="50"
                                           placeholder="Ej: DESCUENTO20">
                                    <button type="button" class="btn btn-outline-secondary" onclick="generateCode()">
                                        <i class="fas fa-random"></i> Generar
                                    </button>
                                </div>
                                <small class="form-text text-muted">Código único que usarán los clientes</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre del Cupón <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $coupon['name']) ?>" required maxlength="255"
                                       placeholder="Ej: Descuento de Bienvenida">
                                <small class="form-text text-muted">Nombre descriptivo para administración</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Descripción opcional del cupón"><?= old('description', $coupon['description']) ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Configuración del Descuento -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Configuración del Descuento</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Tipo de Descuento <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required onchange="updateDiscountFields()">
                                    <option value="">Seleccionar tipo</option>
                                    <option value="percentage" <?= old('type', $coupon['type']) === 'percentage' ? 'selected' : '' ?>>
                                        Porcentaje (%)
                                    </option>
                                    <option value="fixed" <?= old('type', $coupon['type']) === 'fixed' ? 'selected' : '' ?>>
                                        Cantidad Fija (Q)
                                    </option>
                                    <option value="free_shipping" <?= old('type', $coupon['type']) === 'free_shipping' ? 'selected' : '' ?>>
                                        Envío Gratis
                                    </option>
                                    <option value="buy_x_get_y" <?= old('type', $coupon['type']) === 'buy_x_get_y' ? 'selected' : '' ?>>
                                        Compra X Lleva Y
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="valueField">
                                <label for="value" class="form-label">Valor del Descuento <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text" id="valuePrefix">Q</span>
                                    <input type="number" class="form-control" id="value" name="value" 
                                           value="<?= old('value', $coupon['value']) ?>" step="0.01" min="0" required>
                                    <span class="input-group-text" id="valueSuffix" style="display: none;">%</span>
                                </div>
                                <small class="form-text text-muted" id="valueHelp">Ingresa el valor del descuento</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="min_order_amount" class="form-label">Monto Mínimo de Orden</label>
                                <div class="input-group">
                                    <span class="input-group-text">Q</span>
                                    <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" 
                                           value="<?= old('min_order_amount', $coupon['min_order_amount']) ?>" step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted">Monto mínimo requerido para usar el cupón</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_discount_amount" class="form-label">Descuento Máximo</label>
                                <div class="input-group">
                                    <span class="input-group-text">Q</span>
                                    <input type="number" class="form-control" id="max_discount_amount" name="max_discount_amount" 
                                           value="<?= old('max_discount_amount', $coupon['max_discount_amount']) ?>" step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted">Límite máximo del descuento (opcional)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Restricciones de Uso -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Restricciones de Uso</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="usage_limit" class="form-label">Límite de Usos Total</label>
                                <input type="number" class="form-control" id="usage_limit" name="usage_limit" 
                                       value="<?= old('usage_limit', $coupon['usage_limit']) ?>" min="1">
                                <small class="form-text text-muted">Dejar vacío para usos ilimitados</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="usage_limit_per_user" class="form-label">Límite por Usuario</label>
                                <input type="number" class="form-control" id="usage_limit_per_user" name="usage_limit_per_user" 
                                       value="<?= old('usage_limit_per_user', $coupon['usage_limit_per_user']) ?>" min="1">
                                <small class="form-text text-muted">Cuántas veces puede usar cada usuario</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="valid_from" class="form-label">Válido Desde</label>
                                <input type="datetime-local" class="form-control" id="valid_from" name="valid_from" 
                                       value="<?= old('valid_from', date('Y-m-d\TH:i', strtotime($coupon['valid_from']))) ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="valid_until" class="form-label">Válido Hasta</label>
                                <input type="datetime-local" class="form-control" id="valid_until" name="valid_until" 
                                       value="<?= old('valid_until', $coupon['valid_until'] ? date('Y-m-d\TH:i', strtotime($coupon['valid_until'])) : '') ?>">
                                <small class="form-text text-muted">Dejar vacío para sin vencimiento</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="first_order_only" name="first_order_only" 
                                   value="1" <?= old('first_order_only', $coupon['first_order_only']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="first_order_only">
                                Solo para primera compra
                            </label>
                            <small class="form-text text-muted d-block">El cupón solo puede usarse en la primera compra del usuario</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel Lateral -->
        <div class="col-lg-4">
            <!-- Estado y Acciones -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estado y Acciones</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" <?= old('is_active', $coupon['is_active']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                Cupón Activo
                            </label>
                        </div>
                        <small class="form-text text-muted">Los cupones inactivos no pueden ser utilizados</small>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Actualizar Cupón
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="previewCoupon()">
                            <i class="fas fa-eye me-2"></i>Vista Previa
                        </button>
                    </div>
                </div>
            </div>

            <!-- Estadísticas de Uso -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estadísticas de Uso</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?= $coupon['current_uses'] ?? 0 ?></h4>
                                <small class="text-muted">Usos Totales</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">Q<?= number_format($coupon['total_discount_given'] ?? 0, 2) ?></h4>
                            <small class="text-muted">Descuento Otorgado</small>
                        </div>
                    </div>
                    
                    <?php if ($coupon['usage_limit']): ?>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-1">
                                <small>Progreso de Uso</small>
                                <small><?= round((($coupon['current_uses'] ?? 0) / $coupon['usage_limit']) * 100, 1) ?>%</small>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: <?= min(100, (($coupon['current_uses'] ?? 0) / $coupon['usage_limit']) * 100) ?>%">
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Aplicabilidad -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aplicabilidad</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="applicable_products" class="form-label">Productos Aplicables</label>
                        <select class="form-select" id="applicable_products" name="applicable_products[]" multiple>
                            <?php if (!empty($products)): ?>
                                <?php 
                                $selectedProducts = json_decode($coupon['applicable_products'] ?? '[]', true) ?: [];
                                ?>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?= $product['id'] ?>" 
                                            <?= in_array($product['id'], $selectedProducts) ? 'selected' : '' ?>>
                                        <?= esc($product['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small class="form-text text-muted">Dejar vacío para aplicar a todos los productos</small>
                    </div>

                    <div class="mb-3">
                        <label for="applicable_categories" class="form-label">Categorías Aplicables</label>
                        <select class="form-select" id="applicable_categories" name="applicable_categories[]" multiple>
                            <?php if (!empty($categories)): ?>
                                <?php 
                                $selectedCategories = json_decode($coupon['applicable_categories'] ?? '[]', true) ?: [];
                                ?>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" 
                                            <?= in_array($category['id'], $selectedCategories) ? 'selected' : '' ?>>
                                        <?= esc($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small class="form-text text-muted">Dejar vacío para aplicar a todas las categorías</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Actualizar campos según el tipo de descuento
function updateDiscountFields() {
    const type = document.getElementById('type').value;
    const valueField = document.getElementById('valueField');
    const valuePrefix = document.getElementById('valuePrefix');
    const valueSuffix = document.getElementById('valueSuffix');
    const valueHelp = document.getElementById('valueHelp');
    const valueInput = document.getElementById('value');

    switch(type) {
        case 'percentage':
            valuePrefix.style.display = 'none';
            valueSuffix.style.display = 'block';
            valueSuffix.textContent = '%';
            valueHelp.textContent = 'Porcentaje de descuento (1-100)';
            valueInput.max = '100';
            valueInput.step = '0.01';
            break;
        case 'fixed':
            valuePrefix.style.display = 'block';
            valueSuffix.style.display = 'none';
            valuePrefix.textContent = 'Q';
            valueHelp.textContent = 'Cantidad fija de descuento en quetzales';
            valueInput.max = '';
            valueInput.step = '0.01';
            break;
        case 'free_shipping':
            valueField.style.display = 'none';
            break;
        case 'buy_x_get_y':
            valuePrefix.style.display = 'block';
            valueSuffix.style.display = 'none';
            valuePrefix.textContent = 'X';
            valueHelp.textContent = 'Cantidad de productos para activar la promoción';
            valueInput.max = '';
            valueInput.step = '1';
            break;
        default:
            valueField.style.display = 'block';
            valuePrefix.style.display = 'block';
            valueSuffix.style.display = 'none';
    }

    if (type === 'free_shipping') {
        valueField.style.display = 'none';
    } else {
        valueField.style.display = 'block';
    }
}

// Generar código aleatorio
function generateCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('code').value = result;
}

// Vista previa del cupón
function previewCoupon() {
    const formData = new FormData(document.getElementById('couponForm'));
    const preview = {
        code: formData.get('code'),
        name: formData.get('name'),
        type: formData.get('type'),
        value: formData.get('value'),
        min_order_amount: formData.get('min_order_amount')
    };

    let previewText = `Código: ${preview.code}\n`;
    previewText += `Nombre: ${preview.name}\n`;
    previewText += `Tipo: ${preview.type}\n`;
    
    if (preview.type !== 'free_shipping') {
        previewText += `Valor: ${preview.value}\n`;
    }
    
    if (preview.min_order_amount > 0) {
        previewText += `Monto mínimo: Q${preview.min_order_amount}\n`;
    }

    alert(previewText);
}

// Validación del formulario
document.getElementById('couponForm').addEventListener('submit', function(e) {
    const type = document.getElementById('type').value;
    const value = document.getElementById('value').value;
    
    if (type === 'percentage' && (value < 0 || value > 100)) {
        e.preventDefault();
        alert('El porcentaje debe estar entre 0 y 100');
        return;
    }
    
    if (type === 'fixed' && value <= 0) {
        e.preventDefault();
        alert('La cantidad fija debe ser mayor a 0');
        return;
    }
});

// Inicializar campos al cargar
document.addEventListener('DOMContentLoaded', function() {
    updateDiscountFields();
});
</script>
<?= $this->endSection() ?>
