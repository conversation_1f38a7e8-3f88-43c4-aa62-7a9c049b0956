<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

class SecurityValidationFilter implements FilterInterface
{
    /**
     * Patrones de ataques comunes
     */
    private array $maliciousPatterns = [
        // SQL Injection
        '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
        '/(\bor\b\s+\d+\s*=\s*\d+)|(\band\b\s+\d+\s*=\s*\d+)/i',
        '/(\bdrop\b\s+\btable\b)|(\bdelete\b\s+\bfrom\b)/i',
        '/(\binsert\b\s+\binto\b)|(\bupdate\b.*\bset\b)/i',
        
        // XSS
        '/<script[^>]*>.*?<\/script>/is',
        '/javascript\s*:/i',
        '/on\w+\s*=\s*["\'][^"\']*["\']/',
        '/<iframe[^>]*>.*?<\/iframe>/is',
        
        // Path Traversal
        '/\.\.\//',
        '/\.\.\\\\/',
        '/\.\.\%2f/',
        '/\.\.\%5c/',
        
        // Command Injection
        '/;\s*(rm|del|format|shutdown)/i',
        '/\|\s*(cat|type|more|less)/i',
        '/`[^`]*`/',
        '/\$\([^)]*\)/',
        
        // LDAP Injection
        '/\(\|\(.*\)\)/',
        '/\(\&\(.*\)\)/',
        
        // XML Injection
        '/<!ENTITY/i',
        '/<!DOCTYPE.*\[/i'
    ];

    /**
     * Headers sospechosos
     */
    private array $suspiciousHeaders = [
        'X-Forwarded-For' => '/[^\d\.,\s]/',
        'X-Real-IP' => '/[^\d\.]/',
        'User-Agent' => '/(sqlmap|nikto|nmap|masscan|nessus)/i',
        'Referer' => '/(javascript:|data:|vbscript:)/i'
    ];

    /**
     * Límites de tamaño de datos
     */
    private array $sizeLimits = [
        'max_post_size' => 10485760, // 10MB
        'max_file_size' => 52428800, // 50MB
        'max_field_length' => 10000,
        'max_fields_count' => 100
    ];

    public function before(RequestInterface $request, $arguments = null)
    {
        $response = Services::response();
        
        // 1. Validar tamaño de la solicitud
        if (!$this->validateRequestSize($request)) {
            return $this->securityViolation($response, 'REQUEST_TOO_LARGE', 'Solicitud demasiado grande');
        }
        
        // 2. Validar headers
        if (!$this->validateHeaders($request)) {
            return $this->securityViolation($response, 'SUSPICIOUS_HEADERS', 'Headers sospechosos detectados');
        }
        
        // 3. Validar datos de entrada
        if (!$this->validateInputData($request)) {
            return $this->securityViolation($response, 'MALICIOUS_INPUT', 'Datos de entrada maliciosos detectados');
        }
        
        // 4. Validar archivos subidos
        if (!$this->validateUploadedFiles($request)) {
            return $this->securityViolation($response, 'MALICIOUS_FILE', 'Archivo malicioso detectado');
        }
        
        // 5. Validar frecuencia de solicitudes por IP
        if (!$this->validateRequestFrequency($request)) {
            return $this->securityViolation($response, 'SUSPICIOUS_FREQUENCY', 'Frecuencia de solicitudes sospechosa');
        }
        
        // 6. Validar geolocalización (opcional)
        if (!$this->validateGeolocation($request)) {
            return $this->securityViolation($response, 'BLOCKED_LOCATION', 'Ubicación geográfica bloqueada');
        }
        
        return null;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Agregar headers de seguridad
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        $response->setHeader('X-Frame-Options', 'DENY');
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        // CSP para páginas HTML
        if (strpos($response->getHeaderLine('Content-Type'), 'text/html') !== false) {
            $csp = "default-src 'self'; " .
                   "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
                   "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
                   "img-src 'self' data: https:; " .
                   "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
                   "connect-src 'self' https://facturalo.mayansource.com https://socialmanager.mayansource.com;";
            
            $response->setHeader('Content-Security-Policy', $csp);
        }
        
        return $response;
    }

    /**
     * Validar tamaño de la solicitud
     */
    private function validateRequestSize(RequestInterface $request): bool
    {
        $contentLength = $request->getHeaderLine('Content-Length');
        
        if ($contentLength && (int)$contentLength > $this->sizeLimits['max_post_size']) {
            return false;
        }
        
        // Validar número de campos
        $postData = $request->getPost();
        if (is_array($postData) && count($postData) > $this->sizeLimits['max_fields_count']) {
            return false;
        }
        
        // Validar longitud de campos individuales
        foreach ($postData as $value) {
            if (is_string($value) && strlen($value) > $this->sizeLimits['max_field_length']) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Validar headers HTTP
     */
    private function validateHeaders(RequestInterface $request): bool
    {
        foreach ($this->suspiciousHeaders as $header => $pattern) {
            $value = $request->getHeaderLine($header);
            if ($value && preg_match($pattern, $value)) {
                $this->logSecurityEvent('suspicious_header', [
                    'header' => $header,
                    'value' => $value,
                    'pattern' => $pattern
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * Validar datos de entrada contra patrones maliciosos
     */
    private function validateInputData(RequestInterface $request): bool
    {
        $allData = array_merge(
            $request->getGet() ?? [],
            $request->getPost() ?? [],
            $request->getCookie() ?? []
        );
        
        foreach ($allData as $key => $value) {
            if (is_string($value)) {
                foreach ($this->maliciousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->logSecurityEvent('malicious_input', [
                            'field' => $key,
                            'value' => substr($value, 0, 200),
                            'pattern' => $pattern
                        ]);
                        return false;
                    }
                }
            }
        }
        
        return true;
    }

    /**
     * Validar archivos subidos
     
    private function validateUploadedFiles(RequestInterface $request): bool
    {
        $files = $request->getFiles();
        
        if (empty($files)) {
            return true;
        }
        
        foreach ($files as $file) {
            if ($file->isValid()) {
                // Validar tamaño
                if ($file->getSize() > $this->sizeLimits['max_file_size']) {
                    return false;
                }
                
                // Validar tipo MIME
                $allowedMimes = [
                    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                    'application/pdf', 'text/plain', 'text/csv',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ];
                
                if (!in_array($file->getMimeType(), $allowedMimes)) {
                    $this->logSecurityEvent('invalid_file_type', [
                        'filename' => $file->getName(),
                        'mime_type' => $file->getMimeType()
                    ]);
                    return false;
                }
                
                // Validar extensión
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'txt', 'csv', 'xls', 'xlsx'];
                $extension = strtolower($file->getExtension());
                
                if (!in_array($extension, $allowedExtensions)) {
                    return false;
                }
                
                // Validar contenido del archivo (básico)
                if (!$this->validateFileContent($file)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    */

    private function validateUploadedFiles(RequestInterface $request): bool
{
    $files = $request->getFiles();

    if (empty($files)) {
        return true;
    }

    foreach ($files as $fileField) {
        if (is_array($fileField)) {
            // Caso multiple files
            foreach ($fileField as $file) {
                if (!$this->processFile($file)) {
                    return false;
                }
            }
        } else {
            // Caso single file
            if (!$this->processFile($fileField)) {
                return false;
            }
        }
    }

    return true;
}

private function processFile($file): bool
{
    if ($file->isValid()) {
        // Validar tamaño
        if ($file->getSize() > $this->sizeLimits['max_file_size']) {
            return false;
        }

        // Validar tipo MIME
        $allowedMimes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $this->logSecurityEvent('invalid_file_type', [
                'filename' => $file->getName(),
                'mime_type' => $file->getMimeType()
            ]);
            return false;
        }

        // Validar extensión
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'txt', 'csv', 'xls', 'xlsx'];
        $extension = strtolower($file->getExtension());

        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }

        // Validar contenido
        if (!$this->validateFileContent($file)) {
            return false;
        }
    }

    return true;
}


    /**
     * Validar contenido de archivo
     */
    private function validateFileContent($file): bool
    {
        $content = file_get_contents($file->getTempName());
        
        // Buscar patrones maliciosos en el contenido
        $maliciousFilePatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i'
        ];
        
        foreach ($maliciousFilePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $this->logSecurityEvent('malicious_file_content', [
                    'filename' => $file->getName(),
                    'pattern' => $pattern
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * Validar frecuencia de solicitudes
     */
    private function validateRequestFrequency(RequestInterface $request): bool
    {
        $cache = Services::cache();
        $ip = $request->getIPAddress();
        $cacheKey = "security_freq_" . md5($ip);
        
        $requests = $cache->get($cacheKey) ?? [];
        $now = time();
        
        // Limpiar solicitudes antiguas (últimos 5 minutos)
        $requests = array_filter($requests, function($timestamp) use ($now) {
            return ($now - $timestamp) < 300;
        });
        
        // Agregar solicitud actual
        $requests[] = $now;
        
        // Guardar en cache
        $cache->save($cacheKey, $requests, 300);
        
        // Verificar si excede el límite (más de 200 solicitudes en 5 minutos)
        if (count($requests) > 200) {
            $this->logSecurityEvent('high_frequency_requests', [
                'ip' => $ip,
                'request_count' => count($requests),
                'time_window' => 300
            ]);
            return false;
        }
        
        return true;
    }

    /**
     * Validar geolocalización (opcional)
     */
    private function validateGeolocation(RequestInterface $request): bool
    {
        // Lista de países bloqueados (códigos ISO)
        $blockedCountries = ['CN', 'RU', 'KP']; // Ejemplo
        
        $ip = $request->getIPAddress();
        
        // Aquí podrías integrar con un servicio de geolocalización
        // Por ahora, solo validamos IPs locales conocidas como maliciosas
        $knownMaliciousIPs = [
            '127.0.0.1', // Ejemplo - nunca bloquear localhost en producción
        ];
        
        if (in_array($ip, $knownMaliciousIPs)) {
            $this->logSecurityEvent('blocked_ip', ['ip' => $ip]);
            return false;
        }
        
        return true;
    }

    /**
     * Respuesta de violación de seguridad
     */
    private function securityViolation(ResponseInterface $response, string $code, string $message): ResponseInterface
    {
        $response->setStatusCode(403);
        $response->setHeader('Content-Type', 'application/json');
        
        $body = json_encode([
            'success' => false,
            'error' => $code,
            'message' => $message,
            'timestamp' => date('c')
        ]);
        
        $response->setBody($body);
        
        return $response;
    }

    /**
     * Log de eventos de seguridad
     */
    private function logSecurityEvent(string $type, array $data): void
    {
        $logData = array_merge($data, [
            'type' => $type,
            'ip' => request()->getIPAddress(),
            'user_agent' => request()->getUserAgent()->getAgentString(),
            'url' => (string)request()->getUri(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        log_message('warning', "Security event: {$type}", $logData);
        
        // Intentar guardar en base de datos para análisis
        try {
            $db = \Config\Database::connect();
            $db->table('security_events')->insert([
                'event_type' => $type,
                'ip_address' => $logData['ip'],
                'user_agent' => $logData['user_agent'],
                'url' => $logData['url'],
                'event_data' => json_encode($data),
                'created_at' => $logData['timestamp']
            ]);
        } catch (\Exception $e) {
            // Si la tabla no existe, solo log
            log_message('error', 'Could not save security event to database: ' . $e->getMessage());
        }
    }
}
