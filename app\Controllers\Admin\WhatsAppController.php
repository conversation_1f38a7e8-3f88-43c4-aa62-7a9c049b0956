<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\WhatsAppSettingsModel;
use App\Models\WhatsAppTemplateModel;
use App\Models\WhatsAppMessageLogModel;
use App\Services\WhatsAppService;

class WhatsAppController extends BaseController
{
    protected $settingsModel;
    protected $templateModel;
    protected $messageLogModel;
    protected $whatsappService;

    public function __construct()
    {
        $this->settingsModel = new WhatsAppSettingsModel();
        $this->templateModel = new WhatsAppTemplateModel();
        $this->messageLogModel = new WhatsAppMessageLogModel();
        $this->whatsappService = new WhatsAppService();
    }

    /**
     * Verificar autenticación de admin
     */
    protected function checkAuth()
    {
        // Verificar si el admin está logueado usando el mismo método que AdminController
        if (!session()->get('admin_id') || !session()->get('is_admin_logged_in')) {
            // Si es una petición AJAX, devolver JSON
            if ($this->request->isAJAX()) {
                return service('response')->setJSON([
                    'status' => 'error',
                    'message' => 'Sesión expirada. Por favor, inicia sesión nuevamente.',
                    'redirect' => '/admin/login'
                ])->setStatusCode(401);
            }

            // Redirigir al login
            return redirect()->to('/admin/login')->with('error', 'Acceso denegado. Por favor, inicia sesión.');
        }
        return true;
    }

    /**
     * Dashboard principal de WhatsApp
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $data = [
            'title' => 'WhatsApp - Configuración',
            'settings' => $this->settingsModel->getAllSettings(),
            'templates' => $this->templateModel->getActiveTemplates(),
            'stats' => $this->messageLogModel->getMessageStats(),
            'recent_messages' => $this->messageLogModel->orderBy('created_at', 'DESC')->limit(10)->findAll()
        ];

        return view('admin/whatsapp/index', $data);
    }

    /**
     * Configuración general
     */
    public function settings()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->updateSettings();
        }

        $data = [
            'title' => 'WhatsApp - Configuración General',
            'settings' => $this->settingsModel->getAllSettings()
        ];

        return view('admin/whatsapp/settings', $data);
    }

    /**
     * Actualizar configuración
     */
    protected function updateSettings()
    {
        try {
            $rules = [
                'api_url' => 'required|valid_url',
                'api_key' => 'required|min_length[10]',
                'device_token' => 'required|min_length[3]',
                'enabled' => 'permit_empty|in_list[0,1]'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->validator->getErrors());
            }

            $settings = [
                'api_url' => $this->request->getPost('api_url'),
                'api_key' => $this->request->getPost('api_key'),
                'device_token' => $this->request->getPost('device_token'),
                'enabled' => $this->request->getPost('enabled') ? '1' : '0'
            ];

            foreach ($settings as $key => $value) {
                $this->settingsModel->updateSetting($key, $value);
            }

            return redirect()->to('/admin/whatsapp/settings')
                ->with('success', 'Configuración actualizada correctamente');

        } catch (\Exception $e) {
            log_message('error', 'Error actualizando configuración WhatsApp: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error al actualizar configuración: ' . $e->getMessage());
        }
    }

    /**
     * Gestión de plantillas
     */
    public function templates()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $data = [
            'title' => 'WhatsApp - Plantillas de Mensajes',
            'templates' => $this->templateModel->findAll()
        ];

        return view('admin/whatsapp/templates', $data);
    }

    /**
     * Crear nueva plantilla
     */
    public function createTemplate()
    {
        if ($this->request->getMethod() === 'POST') {
            try {
                $templateName = $this->request->getPost('template_name');
                $description = $this->request->getPost('description');
                $messageTemplate = $this->request->getPost('message_template');
                $variables = $this->request->getPost('variables');
                $templateType = $this->request->getPost('template_type');
                $isActive = $this->request->getPost('is_active') ? 1 : 0;

                // Validaciones
                if (empty($templateName) || empty($messageTemplate)) {
                    return $this->response->setJSON([
                        'success' => false,
                        'error' => 'Nombre y mensaje de plantilla son requeridos'
                    ]);
                }

                // Verificar que no exista una plantilla con el mismo nombre
                $existing = $this->templateModel->where('template_name', $templateName)->first();
                if ($existing) {
                    return $this->response->setJSON([
                        'success' => false,
                        'error' => 'Ya existe una plantilla con ese nombre'
                    ]);
                }

                // Crear plantilla
                $data = [
                    'template_name' => $templateName,
                    'description' => $description,
                    'message_template' => $messageTemplate,
                    'variables' => $variables,
                    'template_type' => $templateType,
                    'is_active' => $isActive,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($this->templateModel->insert($data)) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Plantilla creada exitosamente'
                    ]);
                } else {
                    return $this->response->setJSON([
                        'success' => false,
                        'error' => 'Error al crear la plantilla'
                    ]);
                }

            } catch (\Exception $e) {
                log_message('error', 'Error creando plantilla WhatsApp: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error interno del servidor'
                ]);
            }
        }

        return redirect()->to('/admin/whatsapp/templates');
    }

    /**
     * Editar plantilla
     */
    public function editTemplate($id = null)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            return $this->updateTemplate($id);
        }

        $template = $this->templateModel->find($id);
        if (!$template) {
            return redirect()->to('/admin/whatsapp/templates')
                ->with('error', 'Plantilla no encontrada');
        }

        $data = [
            'title' => 'Editar Plantilla - ' . $template['template_name'],
            'template' => $template
        ];

        return view('admin/whatsapp/edit_template', $data);
    }

    /**
     * Actualizar plantilla
     */
    protected function updateTemplate($id)
    {
        try {
            $rules = [
                'template_name' => 'required|max_length[200]',
                'message_template' => 'required',
                'description' => 'permit_empty',
                'is_active' => 'permit_empty|in_list[0,1]'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->validator->getErrors());
            }

            $data = [
                'template_name' => $this->request->getPost('template_name'),
                'message_template' => $this->request->getPost('message_template'),
                'description' => $this->request->getPost('description'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];

            // Extraer variables automáticamente
            $data['variables'] = $this->templateModel->extractVariables($data['message_template']);

            if ($this->templateModel->update($id, $data)) {
                return redirect()->to('/admin/whatsapp/templates')
                    ->with('success', 'Plantilla actualizada correctamente');
            } else {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Error al actualizar la plantilla');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error actualizando plantilla WhatsApp: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error al actualizar plantilla: ' . $e->getMessage());
        }
    }

    /**
     * Log de mensajes
     */
    public function messageLog()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $page = $this->request->getGet('page') ?? 1;
        $perPage = 50;
        $offset = ($page - 1) * $perPage;

        $messages = $this->messageLogModel
            ->orderBy('created_at', 'DESC')
            ->limit($perPage, $offset)
            ->findAll();

        $total = $this->messageLogModel->countAllResults();
        $totalPages = ceil($total / $perPage);

        $data = [
            'title' => 'WhatsApp - Log de Mensajes',
            'messages' => $messages,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'per_page' => $perPage,
                'total_records' => $total
            ],
            'stats' => $this->messageLogModel->getMessageStats()
        ];

        return view('admin/whatsapp/message_log', $data);
    }

    /**
     * Probar conexión
     */
    public function testConnection()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            // Obtener datos JSON o POST
            $input = $this->request->getJSON(true);
            if (!$input) {
                $input = $this->request->getPost();
            }

            $phoneNumber = $input['phone_number'] ?? '';

            if (empty($phoneNumber)) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Número de teléfono requerido'
                ]);
            }

            $result = $this->whatsappService->sendMessage(
                $phoneNumber,
                'Mensaje de prueba desde MrCell Admin - ' . date('Y-m-d H:i:s')
            );

            return $this->response->setJSON($result);
        }

        $data = [
            'title' => 'WhatsApp - Probar Conexión',
            'settings' => $this->settingsModel->getAllSettings()
        ];

        return view('admin/whatsapp/test_connection', $data);
    }

    /**
     * Estadísticas
     */
    public function statistics()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $days = $this->request->getGet('days') ?? 30;
        
        $data = [
            'title' => 'WhatsApp - Estadísticas',
            'stats' => $this->messageLogModel->getMessageStats(),
            'daily_stats' => $this->messageLogModel->getDailyMessageSummary($days),
            'template_stats' => $this->getTemplateStats(),
            'days' => $days
        ];

        return view('admin/whatsapp/statistics', $data);
    }

    /**
     * Obtener estadísticas por plantilla
     */
    protected function getTemplateStats()
    {
        return $this->messageLogModel
            ->select('template_key, COUNT(*) as total_messages, 
                     SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_messages,
                     SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_messages')
            ->where('template_key IS NOT NULL')
            ->groupBy('template_key')
            ->orderBy('total_messages', 'DESC')
            ->findAll();
    }

    /**
     * Enviar mensaje manual
     */
    public function sendMessage()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            // Obtener datos JSON o POST
            $input = $this->request->getJSON(true);
            if (!$input) {
                $input = $this->request->getPost();
            }

            $rules = [
                'phone_number' => 'required',
                'message' => 'required|min_length[1]'
            ];

            if (!$this->validate($rules, $input)) {
                return $this->response->setJSON([
                    'success' => false,
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $result = $this->whatsappService->sendMessage(
                $input['phone_number'],
                $input['message']
            );

            return $this->response->setJSON($result);
        }

        $data = [
            'title' => 'WhatsApp - Enviar Mensaje Manual'
        ];

        return view('admin/whatsapp/send_message', $data);
    }

    /**
     * API para obtener estadísticas en tiempo real
     */
    public function apiStats()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'No autorizado'])->setStatusCode(401);
        }

        $stats = $this->messageLogModel->getMessageStats();
        return $this->response->setJSON($stats);
    }
}
