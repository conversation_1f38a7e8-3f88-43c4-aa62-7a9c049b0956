<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#9C27B0"/>

    <!-- Gradiente sutil -->
    <defs>
        <linearGradient id="gradtablets" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#9C27B0;stop-opacity:0.8" />
        </linearGradient>
    </defs>
    <rect width="100%" height="100%" fill="url(#gradtablets)"/>

    <!-- Icono -->
    <g transform="translate(176,110)">
        <svg width="48" height="48" viewBox="0 0 24 24">
            <path d="M19,18H5V6H19M21,4H3C1.89,4 1,4.89 1,6V18A2,2 0 0,0 3,20H21A2,2 0 0,0 23,18V6C23,4.89 22.1,4 21,4Z" fill="#FFFFFF" fill-opacity="0.9"/>
        </svg>
    </g>

    <!-- Texto -->
    <text x="200" y="180"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#FFFFFF"
          fill-opacity="0.95">
        Tablets
    </text>

    <!-- Sombra del texto -->
    <text x="201" y="181"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#9C27B0"
          fill-opacity="0.3">
        Tablets
    </text>
</svg>