<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateBrandsTable extends Migration
{
    public function up()
    {
        // Crear tabla brands si no existe
        if (!$this->db->tableExists('brands')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INTEGER',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'uuid' => [
                    'type' => 'VARCHAR',
                    'constraint' => 36,
                    'null' => false,
                ],
                'name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => false,
                ],
                'slug' => [
                    'type' => 'VARCHAR',
                    'constraint' => 120,
                    'null' => false,
                ],
                'description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'logo' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                ],
                'website' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                ],
                'email' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                ],
                'phone' => [
                    'type' => 'VARCHAR',
                    'constraint' => 20,
                    'null' => true,
                ],
                'address' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'is_active' => [
                    'type' => 'BOOLEAN',
                    'default' => true,
                ],
                'sort_order' => [
                    'type' => 'INTEGER',
                    'constraint' => 11,
                    'default' => 0,
                ],
                'meta_title' => [
                    'type' => 'VARCHAR',
                    'constraint' => 160,
                    'null' => true,
                ],
                'meta_description' => [
                    'type' => 'VARCHAR',
                    'constraint' => 320,
                    'null' => true,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'deleted_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addUniqueKey('uuid');
            $this->forge->addUniqueKey('slug');
            $this->forge->createTable('brands');

            // Insertar algunas marcas de ejemplo
            $brands = [
                [
                    'uuid' => $this->generateUUID(),
                    'name' => 'Apple',
                    'slug' => 'apple',
                    'description' => 'Productos Apple iPhone, iPad, Mac',
                    'is_active' => 1,
                    'sort_order' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => $this->generateUUID(),
                    'name' => 'Samsung',
                    'slug' => 'samsung',
                    'description' => 'Productos Samsung Galaxy, Note, Tab',
                    'is_active' => 1,
                    'sort_order' => 2,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => $this->generateUUID(),
                    'name' => 'Xiaomi',
                    'slug' => 'xiaomi',
                    'description' => 'Productos Xiaomi Mi, Redmi, POCO',
                    'is_active' => 1,
                    'sort_order' => 3,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => $this->generateUUID(),
                    'name' => 'Huawei',
                    'slug' => 'huawei',
                    'description' => 'Productos Huawei P, Mate, Nova',
                    'is_active' => 1,
                    'sort_order' => 4,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'uuid' => $this->generateUUID(),
                    'name' => 'Motorola',
                    'slug' => 'motorola',
                    'description' => 'Productos Motorola Moto, Edge',
                    'is_active' => 1,
                    'sort_order' => 5,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            $this->db->table('brands')->insertBatch($brands);
        }

        // Agregar columna brand_id a products si no existe
        if ($this->db->tableExists('products') && !$this->db->fieldExists('brand_id', 'products')) {
            $this->forge->addColumn('products', [
                'brand_id' => [
                    'type' => 'INTEGER',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                    'after' => 'category_id'
                ]
            ]);

            // Agregar foreign key constraint
            $this->forge->addForeignKey('brand_id', 'brands', 'id', 'SET NULL', 'CASCADE');
        }
    }

    public function down()
    {
        // Eliminar foreign key de products si existe
        if ($this->db->tableExists('products') && $this->db->fieldExists('brand_id', 'products')) {
            $this->forge->dropForeignKey('products', 'products_brand_id_foreign');
            $this->forge->dropColumn('products', 'brand_id');
        }

        // Eliminar tabla brands
        $this->forge->dropTable('brands', true);
    }

    private function generateUUID()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
