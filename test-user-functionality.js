const { chromium } = require('playwright');

async function testUserFunctionality() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('🧪 PRUEBAS DE FUNCIONALIDAD DE USUARIO...\n');

        // ========================================
        // PRUEBA 1: LOGIN USUARIO
        // ========================================
        console.log('📝 PRUEBA 1: Login de Usuario');
        await page.goto('http://localhost:8080/login');
        await page.waitForLoadState('networkidle');

        // Llenar formulario de login
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'Clairo!23');
        await page.click('button[type="submit"]');
        
        // Esperar redirección o error
        await page.waitForTimeout(3000);
        
        const currentUrl = page.url();
        console.log(`🔗 URL después del login: ${currentUrl}`);
        
        if (currentUrl.includes('/cuenta') || currentUrl.includes('/dashboard')) {
            console.log('✅ Login exitoso - redirigido al dashboard');
        } else if (currentUrl.includes('/login')) {
            console.log('❌ Login falló - permanece en página de login');
            
            // Verificar si hay mensaje de error
            const errorMessage = await page.locator('.alert-danger, .error, .text-danger').textContent().catch(() => '');
            if (errorMessage) {
                console.log(`⚠️ Mensaje de error: ${errorMessage}`);
            }
            
            // Intentar con credenciales de admin
            console.log('🔄 Intentando con credenciales de admin...');
            await page.fill('input[name="email"]', '<EMAIL>');
            await page.fill('input[name="password"]', 'Clairo!23');
            await page.click('button[type="submit"]');
            await page.waitForTimeout(3000);
            
            const newUrl = page.url();
            if (newUrl.includes('/admin') || newUrl.includes('/cuenta')) {
                console.log('✅ Login con admin exitoso');
            } else {
                console.log('❌ Login con admin también falló');
                return; // Salir si no podemos hacer login
            }
        }

        // ========================================
        // PRUEBA 2: DASHBOARD - ESTADÍSTICAS
        // ========================================
        console.log('\n📊 PRUEBA 2: Dashboard con Estadísticas');
        
        // Ir al dashboard de usuario
        await page.goto('http://localhost:8080/cuenta');
        await page.waitForLoadState('networkidle');
        
        // Buscar estadísticas en diferentes formatos posibles
        const statsSelectors = [
            '.stat-number',
            '.stats-card .number',
            '.dashboard-stat',
            '.metric-value',
            'h3, h4, h5', // Números grandes que podrían ser estadísticas
        ];
        
        let foundStats = false;
        for (const selector of statsSelectors) {
            const elements = await page.locator(selector).count();
            if (elements > 0) {
                console.log(`📈 Encontradas ${elements} estadísticas con selector: ${selector}`);
                
                // Obtener los valores
                const values = await page.locator(selector).allTextContents();
                console.log(`📊 Valores: ${values.slice(0, 5).join(', ')}`);
                foundStats = true;
                break;
            }
        }
        
        if (!foundStats) {
            console.log('⚠️ No se encontraron estadísticas específicas');
            // Mostrar el contenido general de la página
            const pageContent = await page.textContent('body');
            if (pageContent.includes('pedidos') || pageContent.includes('total')) {
                console.log('✅ Página de dashboard cargada con contenido');
            }
        }

        // ========================================
        // PRUEBA 3: PÁGINA DE PEDIDOS
        // ========================================
        console.log('\n📦 PRUEBA 3: Página de Pedidos');
        await page.goto('http://localhost:8080/cuenta/pedidos');
        await page.waitForLoadState('networkidle');
        
        // Verificar si hay pestañas con contadores
        const tabsWithBadges = await page.locator('.nav-link .badge, .tab .badge').count();
        console.log(`🏷️ Pestañas con contadores: ${tabsWithBadges}`);
        
        // Verificar contenido de pedidos
        const hasOrders = await page.locator('.order-card, .order-item').count() > 0;
        const hasEmptyMessage = await page.locator(':has-text("No tienes pedidos")').count() > 0;
        
        if (hasOrders) {
            console.log('✅ Pedidos encontrados en la página');
            
            // Probar enlace a detalles de pedido
            const orderLink = page.locator('a[href*="/pedidos/"]').first();
            if (await orderLink.count() > 0) {
                const href = await orderLink.getAttribute('href');
                console.log(`🔗 Probando enlace a detalles: ${href}`);
                
                await orderLink.click();
                await page.waitForLoadState('networkidle');
                
                const detailUrl = page.url();
                if (detailUrl.includes('/pedidos/') && !detailUrl.endsWith('/pedidos')) {
                    console.log('✅ Página de detalles de pedido carga correctamente');
                } else {
                    console.log('❌ Error al cargar detalles de pedido');
                }
            }
        } else if (hasEmptyMessage) {
            console.log('ℹ️ No hay pedidos - mensaje vacío mostrado');
        } else {
            console.log('⚠️ Página de pedidos sin contenido claro');
        }

        // ========================================
        // PRUEBA 4: FUNCIONALIDAD DE WISHLIST
        // ========================================
        console.log('\n❤️ PRUEBA 4: Funcionalidad de Wishlist');
        
        // Ir a un producto
        await page.goto('http://localhost:8080/tienda');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const firstProduct = page.locator('.product-card a').first();
        if (await firstProduct.count() > 0) {
            await firstProduct.click();
            await page.waitForLoadState('networkidle');
            
            // Buscar y probar botón de wishlist
            const wishlistButton = page.locator('button[onclick*="toggleWishlist"]').first();
            if (await wishlistButton.count() > 0) {
                console.log('✅ Botón de wishlist encontrado');
                
                // Hacer click y esperar respuesta
                await wishlistButton.click();
                await page.waitForTimeout(3000);
                
                // Verificar si hay alguna notificación o cambio
                const notifications = await page.locator('.alert, .notification, .toast').count();
                if (notifications > 0) {
                    const notificationText = await page.locator('.alert, .notification, .toast').first().textContent();
                    console.log(`📢 Notificación: ${notificationText}`);
                    console.log('✅ Wishlist funciona con notificaciones');
                } else {
                    console.log('⚠️ Wishlist clickeado pero sin notificación visible');
                }
            } else {
                console.log('❌ Botón de wishlist no encontrado');
            }
        }

        console.log('\n🎉 PRUEBAS DE USUARIO COMPLETADAS');

    } catch (error) {
        console.error('❌ Error durante las pruebas:', error.message);
    } finally {
        await browser.close();
    }
}

// Ejecutar pruebas
testUserFunctionality().catch(console.error);
