<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Frontend extends BaseConfig
{
    /**
     * Site configuration
     */
    public $siteName = 'MrCell Guatemala';
    public $siteDescription = 'Tu tienda de confianza para celulares y tecnología en Guatemala';
    public $siteKeywords = 'celulares Guatemala, smartphones, accesorios móviles, tecnología, MrCell';
    public $siteUrl = null; // Will use base_url() dynamically
    
    /**
     * Contact information
     */
    public $contactEmail = '<EMAIL>';
    public $contactPhone = '+502 2234-5678';
    public $contactWhatsapp = '+502 5555-1234';
    public $contactAddress = 'Ciudad de Guatemala, Guatemala';
    
    /**
     * Social media links
     */
    public $socialMedia = [
        'facebook' => 'https://facebook.com/mrcellgt',
        'instagram' => 'https://instagram.com/mrcellgt',
        'twitter' => 'https://twitter.com/mrcellgt',
        'youtube' => 'https://youtube.com/mrcellgt'
    ];
    
    /**
     * Business hours
     */
    public $businessHours = [
        'monday' => '9:00 AM - 6:00 PM',
        'tuesday' => '9:00 AM - 6:00 PM',
        'wednesday' => '9:00 AM - 6:00 PM',
        'thursday' => '9:00 AM - 6:00 PM',
        'friday' => '9:00 AM - 6:00 PM',
        'saturday' => '9:00 AM - 4:00 PM',
        'sunday' => 'Cerrado'
    ];
    
    /**
     * Currency settings
     */
    public $currency = [
        'code' => 'GTQ',
        'symbol' => 'Q',
        'position' => 'before' // before or after
    ];
    
    /**
     * Pagination settings
     */
    public $pagination = [
        'products_per_page' => 12,
        'orders_per_page' => 10,
        'reviews_per_page' => 5
    ];
    
    /**
     * Image settings
     */
    public $images = [
        'product_thumb_width' => 300,
        'product_thumb_height' => 300,
        'product_large_width' => 800,
        'product_large_height' => 800,
        'allowed_types' => 'jpg,jpeg,png,webp',
        'max_size' => 2048 // KB
    ];
    
    /**
     * SEO settings
     */
    public $seo = [
        'meta_title_suffix' => ' - MrCell Guatemala',
        'meta_description_length' => 160,
        'og_image_default' => 'assets/images/og-default.jpg'
    ];
    
    /**
     * Cart settings
     */
    public $cart = [
        'session_key' => 'mrcell_cart',
        'max_quantity_per_item' => 10,
        'shipping_threshold' => 500, // Free shipping over this amount
        'tax_rate' => 0.12 // 12% IVA
    ];
    
    /**
     * Order settings
     */
    public $orders = [
        'order_number_prefix' => 'MRC',
        'order_number_length' => 8,
        'default_status' => 'pending',
        'auto_cancel_hours' => 24
    ];
    
    /**
     * Email settings
     */
    public $email = [
        'from_email' => '<EMAIL>',
        'from_name' => 'MrCell Guatemala',
        'admin_email' => '<EMAIL>'
    ];
    
    /**
     * Security settings
     */
    public $security = [
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'password_min_length' => 8,
        'session_timeout' => 28800 // 8 hours - aumentado para evitar cierres frecuentes
    ];
    
    /**
     * Feature flags
     */
    public $features = [
        'reviews_enabled' => true,
        'wishlist_enabled' => true,
        'compare_enabled' => true,
        'guest_checkout' => true,
        'social_login' => false,
        'multi_currency' => false
    ];
    
    /**
     * Analytics
     */
    public $analytics = [
        'google_analytics_id' => '',
        'facebook_pixel_id' => '',
        'google_tag_manager_id' => ''
    ];
    
    /**
     * Get site URL dynamically
     */
    public function getSiteUrl()
    {
        return $this->siteUrl ?: base_url();
    }

    /**
     * Get formatted currency
     */
    public function formatCurrency($amount)
    {
        $symbol = $this->currency['symbol'];
        $formatted = number_format($amount, 2);

        return $this->currency['position'] === 'before'
            ? $symbol . $formatted
            : $formatted . $symbol;
    }
    
    /**
     * Get business hours for a specific day
     */
    public function getBusinessHours($day = null)
    {
        if ($day === null) {
            $day = strtolower(date('l'));
        }
        
        return $this->businessHours[$day] ?? 'Cerrado';
    }
    
    /**
     * Check if a feature is enabled
     */
    public function isFeatureEnabled($feature)
    {
        return $this->features[$feature] ?? false;
    }
}
