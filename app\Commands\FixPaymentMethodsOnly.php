<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class FixPaymentMethodsOnly extends BaseCommand
{
    protected $group       = 'Fix';
    protected $name        = 'fix:payment-methods-only';
    protected $description = 'Corregir solo los métodos de pago';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== CORRIGIENDO MÉTODOS DE PAGO ===', 'yellow');
            CLI::newLine();

            // Desactivar "Pago En Bodega"
            $db->query('UPDATE payment_methods SET is_active = 0 WHERE slug = "pago-en-bodega"');
            CLI::write('✅ "Pago En Bodega" desactivado', 'green');

            // Corregir tipo de Recurrente
            $db->query('UPDATE payment_methods SET type = "gateway" WHERE slug = "recurrente"');
            CLI::write('✅ Tipo de Recurrente corregido a "gateway"', 'green');

            CLI::newLine();
            CLI::write('=== VERIFICANDO RESULTADO ===', 'cyan');
            
            $activeMethods = $db->query("
                SELECT id, name, slug, type, is_active
                FROM payment_methods 
                WHERE is_active = 1
                ORDER BY sort_order, name
            ")->getResultArray();
            
            CLI::write('Métodos de pago activos:', 'white');
            foreach ($activeMethods as $method) {
                CLI::write("- {$method['name']} (slug: {$method['slug']}, tipo: {$method['type']})", 'green');
            }
            
            CLI::newLine();
            CLI::write('✅ Corrección completada', 'green');
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
