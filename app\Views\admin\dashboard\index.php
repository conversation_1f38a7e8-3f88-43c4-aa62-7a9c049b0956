<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<!-- Estadísticas Principales -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="bg-primary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $stats['total_orders'] ?? '0' ?></h3>
                        <small class="text-muted">Total Pedidos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="bg-success text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <h3 class="mb-0">Q<?= number_format($stats['total_revenue'] ?? 0, 2) ?></h3>
                        <small class="text-muted">Ingresos Totales</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-box"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $stats['total_products'] ?? '0' ?></h3>
                        <small class="text-muted">Productos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $stats['total_customers'] ?? '0' ?></h3>
                        <small class="text-muted">Clientes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contenido Principal -->
<div class="row">
    <!-- Pedidos Recientes -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Pedidos Recientes</h5>
                    <a href="/admin/orders" class="btn btn-sm btn-outline-primary">Ver Todos</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_orders)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Pedido</th>
                                    <th>Cliente</th>
                                    <th>Total</th>
                                    <th>Estado</th>
                                    <th>Fecha</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><strong>#<?= $order['order_number'] ?></strong></td>
                                        <td><?= esc($order['customer_name']) ?></td>
                                        <td>Q<?= number_format($order['total_amount'], 2) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $order['status'] == 'pending' ? 'warning' : ($order['status'] == 'completed' ? 'success' : 'info') ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y', strtotime($order['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay pedidos recientes</h5>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Stock Bajo -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Stock Bajo</h5>
                    <a href="/admin/inventory" class="btn btn-sm btn-outline-danger">Ver Inventario</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($low_stock)): ?>
                    <?php foreach ($low_stock as $product): ?>
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <div>
                                <div class="fw-bold"><?= esc($product['name']) ?></div>
                                <small class="text-muted"><?= esc($product['sku']) ?></small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-danger"><?= $product['stock_quantity'] ?> unidades</span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-success">Stock en buen estado</h6>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Métodos de Envío -->
<?php if (!empty($shipping_methods)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-truck me-2"></i>Métodos de Envío</h5>
                    <a href="/admin/shipping" class="btn btn-sm btn-outline-primary">Gestionar</a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($shipping_methods as $method): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <i class="fas fa-truck fa-2x text-primary mb-2"></i>
                                    <h6><?= esc($method['name']) ?></h6>
                                    <p class="text-muted small"><?= esc($method['description']) ?></p>
                                    <span class="badge bg-info"><?= $method['rates_count'] ?> tarifas</span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad del dashboard
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Dashboard cargado correctamente');
    });
</script>
<?= $this->endSection() ?>
