<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-list me-2"></i>Log de Mensajes WhatsApp</h1>
        <a href="/admin/whatsapp" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Volver
        </a>
    </div>
</div>

<!-- Estadísticas Rápidas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-paper-plane fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['total_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Total Mensajes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['sent_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Enviados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['failed_messages'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Fallidos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-percentage fa-2x"></i>
                </div>
                <h3 class="mb-1"><?= number_format($stats['success_rate'] ?? 0, 1) ?>%</h3>
                <p class="text-muted mb-0">Tasa de Éxito</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Mensajes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Historial de Mensajes</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($messages)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Fecha/Hora</th>
                                    <th>Número</th>
                                    <th>Plantilla</th>
                                    <th>Mensaje</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($messages as $message): ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('d/m/Y', strtotime($message['created_at'])) ?><br>
                                                <?= date('H:i:s', strtotime($message['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <strong><?= esc($message['phone_number']) ?></strong>
                                            <?php if ($message['user_id']): ?>
                                                <br><small class="text-muted">Usuario ID: <?= $message['user_id'] ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($message['template_key']): ?>
                                                <span class="badge bg-info"><?= esc($message['template_key']) ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">Manual</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="message-preview" style="max-width: 300px;">
                                                <?= esc(substr($message['message_content'], 0, 100)) ?>
                                                <?php if (strlen($message['message_content']) > 100): ?>
                                                    <span class="text-muted">...</span>
                                                    <button type="button" class="btn btn-sm btn-link p-0" 
                                                            onclick="showFullMessage(<?= $message['id'] ?>)">
                                                        Ver completo
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = match($message['status']) {
                                                'sent' => 'bg-success',
                                                'failed' => 'bg-danger',
                                                'pending' => 'bg-warning',
                                                default => 'bg-secondary'
                                            };
                                            $statusIcon = match($message['status']) {
                                                'sent' => 'fas fa-check',
                                                'failed' => 'fas fa-times',
                                                'pending' => 'fas fa-clock',
                                                default => 'fas fa-question'
                                            };
                                            ?>
                                            <span class="badge <?= $statusClass ?>">
                                                <i class="<?= $statusIcon ?> me-1"></i>
                                                <?= ucfirst($message['status']) ?>
                                            </span>
                                            <?php if ($message['sent_at']): ?>
                                                <br><small class="text-muted">
                                                    Enviado: <?= date('H:i:s', strtotime($message['sent_at'])) ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" 
                                                        onclick="showMessageDetails(<?= $message['id'] ?>)"
                                                        title="Ver detalles">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($message['status'] === 'failed'): ?>
                                                    <button type="button" class="btn btn-outline-warning" 
                                                            onclick="retryMessage(<?= $message['id'] ?>)"
                                                            title="Reintentar">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginación -->
                    <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                        <nav aria-label="Paginación de mensajes" class="mt-3">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['current_page'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>

                        <div class="text-center text-muted">
                            Mostrando <?= ($pagination['current_page'] - 1) * $pagination['per_page'] + 1 ?> - 
                            <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_records']) ?> 
                            de <?= number_format($pagination['total_records']) ?> mensajes
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay mensajes registrados</h5>
                        <p class="text-muted">Los mensajes enviados aparecerán aquí</p>
                        <a href="/admin/whatsapp/test-connection" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Enviar Mensaje de Prueba
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal para detalles del mensaje -->
<div class="modal fade" id="messageDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalles del Mensaje</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="messageDetailsContent">
                    <!-- Contenido cargado dinámicamente -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para mensaje completo -->
<div class="modal fade" id="fullMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mensaje Completo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="border rounded p-3 bg-light">
                    <div id="fullMessageContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
const messages = <?= json_encode($messages ?? []) ?>;

function showMessageDetails(messageId) {
    const message = messages.find(m => m.id == messageId);
    if (!message) return;

    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Información General</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>${message.id}</td>
                    </tr>
                    <tr>
                        <td><strong>Número:</strong></td>
                        <td>${message.phone_number}</td>
                    </tr>
                    <tr>
                        <td><strong>Usuario ID:</strong></td>
                        <td>${message.user_id || 'N/A'}</td>
                    </tr>
                    <tr>
                        <td><strong>Plantilla:</strong></td>
                        <td>${message.template_key || 'Manual'}</td>
                    </tr>
                    <tr>
                        <td><strong>Estado:</strong></td>
                        <td><span class="badge bg-${message.status === 'sent' ? 'success' : message.status === 'failed' ? 'danger' : 'warning'}">${message.status}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Creado:</strong></td>
                        <td>${new Date(message.created_at).toLocaleString()}</td>
                    </tr>
                    ${message.sent_at ? `<tr><td><strong>Enviado:</strong></td><td>${new Date(message.sent_at).toLocaleString()}</td></tr>` : ''}
                </table>
            </div>
            <div class="col-md-6">
                <h6>Mensaje</h6>
                <div class="border rounded p-3 bg-light">
                    <pre class="mb-0">${message.message_content}</pre>
                </div>
                ${message.error_message ? `
                    <h6 class="mt-3">Error</h6>
                    <div class="alert alert-danger">
                        ${message.error_message}
                    </div>
                ` : ''}
            </div>
        </div>
        ${message.api_response ? `
            <div class="mt-3">
                <h6>Respuesta de la API</h6>
                <pre class="bg-light p-3 rounded small">${JSON.stringify(JSON.parse(message.api_response), null, 2)}</pre>
            </div>
        ` : ''}
    `;

    document.getElementById('messageDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('messageDetailsModal')).show();
}

function showFullMessage(messageId) {
    const message = messages.find(m => m.id == messageId);
    if (!message) return;

    document.getElementById('fullMessageContent').innerHTML = message.message_content.replace(/\n/g, '<br>');
    new bootstrap.Modal(document.getElementById('fullMessageModal')).show();
}

function retryMessage(messageId) {
    if (!confirm('¿Estás seguro de que quieres reintentar enviar este mensaje?')) {
        return;
    }

    const message = messages.find(m => m.id == messageId);
    if (!message) return;

    // Aquí podrías implementar la lógica para reintentar el mensaje
    alert('Funcionalidad de reintento no implementada aún');
}

// Auto-refresh cada 30 segundos
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);
</script>
<?= $this->endSection() ?>
