<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Logs
 * Panel de administración para gestión de logs del sistema
 */
class LogsController extends BaseController
{
    protected $logger;
    
    public function __construct()
    {
        $this->logger = new AdvancedLogger();
    }
    
    /**
     * Dashboard principal de logs
     */
    public function index()
    {
        $stats = $this->logger->getLogStats(7);
        $recentLogs = $this->logger->getLogs([], 20);
        
        $data = [
            'title' => 'Sistema de Logs - MrCell Guatemala',
            'stats' => $stats['success'] ? $stats['stats'] : [],
            'recent_logs' => $recentLogs['success'] ? $recentLogs['logs'] : [],
            'config' => $this->logger->getConfig()
        ];
        
        return view('admin/logs/dashboard', $data);
    }
    
    /**
     * Ver logs con filtros
     */
    public function view()
    {
        $filters = [
            'level' => $this->request->getGet('level'),
            'user_id' => $this->request->getGet('user_id'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'search' => $this->request->getGet('search')
        ];
        
        $page = (int)($this->request->getGet('page') ?? 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        $logs = $this->logger->getLogs($filters, $limit, $offset);
        
        $data = [
            'title' => 'Ver Logs - MrCell Guatemala',
            'logs' => $logs['success'] ? $logs['logs'] : [],
            'filters' => $filters,
            'current_page' => $page,
            'has_more' => count($logs['logs'] ?? []) === $limit
        ];
        
        return view('admin/logs/view', $data);
    }
    
    /**
     * Obtener logs via AJAX
     */
    public function getLogs()
    {
        $filters = [
            'level' => $this->request->getPost('level'),
            'user_id' => $this->request->getPost('user_id'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'search' => $this->request->getPost('search')
        ];
        
        $limit = (int)($this->request->getPost('limit') ?? 50);
        $offset = (int)($this->request->getPost('offset') ?? 0);
        
        $result = $this->logger->getLogs($filters, $limit, $offset);
        
        return $this->response->setJSON($result);
    }
    
    /**
     * Obtener estadísticas de logs
     */
    public function getStats()
    {
        $days = (int)($this->request->getPost('days') ?? 7);
        $result = $this->logger->getLogStats($days);
        
        return $this->response->setJSON($result);
    }
    
    /**
     * Limpiar logs antiguos
     */
    public function cleanup()
    {
        try {
            $result = $this->logger->cleanupOldLogs();
            
            // Log de la acción
            $this->logger->userAction('logs_cleanup', session('user_id') ?? 0, [
                'deleted_rows' => $result['deleted_rows'] ?? 0,
                'deleted_files' => $result['deleted_files'] ?? 0
            ]);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Exportar logs
     */
    public function export()
    {
        try {
            $filters = [
                'level' => $this->request->getPost('level'),
                'user_id' => $this->request->getPost('user_id'),
                'date_from' => $this->request->getPost('date_from'),
                'date_to' => $this->request->getPost('date_to'),
                'search' => $this->request->getPost('search')
            ];
            
            $format = $this->request->getPost('format') ?? 'csv';
            
            $result = $this->logger->exportLogs($filters, $format);
            
            if ($result['success']) {
                // Log de la acción
                $this->logger->userAction('logs_export', session('user_id') ?? 0, [
                    'format' => $format,
                    'filters' => $filters,
                    'file_size' => $result['size']
                ]);
            }
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Descargar archivo exportado
     */
    public function download($filename)
    {
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if (!file_exists($filepath)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('File not found');
        }
        
        // Verificar que el archivo sea de logs
        if (!preg_match('/^logs_export_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.(csv|json)$/', $filename)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Invalid file');
        }
        
        return $this->response->download($filepath, null);
    }
    
    /**
     * Ver log específico
     */
    public function viewLog($id)
    {
        try {
            $logs = $this->logger->getLogs(['id' => $id], 1);
            
            if (!$logs['success'] || empty($logs['logs'])) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Log not found');
            }
            
            $log = $logs['logs'][0];
            
            // Decodificar contexto JSON
            if (!empty($log['context'])) {
                $log['context_decoded'] = json_decode($log['context'], true);
            }
            
            $data = [
                'title' => 'Detalle de Log - MrCell Guatemala',
                'log' => $log
            ];
            
            return view('admin/logs/detail', $data);
            
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException($e->getMessage());
        }
    }
    
    /**
     * Análisis de logs en tiempo real
     */
    public function realtime()
    {
        $data = [
            'title' => 'Logs en Tiempo Real - MrCell Guatemala',
            'config' => $this->logger->getConfig()
        ];
        
        return view('admin/logs/realtime', $data);
    }
    
    /**
     * Stream de logs en tiempo real (SSE)
     */
    public function stream()
    {
        // Configurar headers para Server-Sent Events
        $this->response->setHeader('Content-Type', 'text/event-stream');
        $this->response->setHeader('Cache-Control', 'no-cache');
        $this->response->setHeader('Connection', 'keep-alive');
        
        // Obtener timestamp del último log enviado
        $lastTimestamp = $this->request->getGet('last_timestamp') ?? date('Y-m-d H:i:s', strtotime('-1 minute'));
        
        while (true) {
            // Obtener logs nuevos
            $logs = $this->logger->getLogs([
                'date_from' => $lastTimestamp
            ], 10);
            
            if ($logs['success'] && !empty($logs['logs'])) {
                foreach ($logs['logs'] as $log) {
                    echo "data: " . json_encode($log) . "\n\n";
                    $lastTimestamp = $log['created_at'];
                }
                flush();
            }
            
            // Esperar 2 segundos antes de la siguiente verificación
            sleep(2);
            
            // Verificar si la conexión sigue activa
            if (connection_aborted()) {
                break;
            }
        }
    }
    
    /**
     * Configuración de logs
     */
    public function settings()
    {
        $data = [
            'title' => 'Configuración de Logs - MrCell Guatemala',
            'config' => $this->logger->getConfig()
        ];
        
        return view('admin/logs/settings', $data);
    }
    
    /**
     * Actualizar configuración de logs
     */
    public function updateSettings()
    {
        try {
            $settings = [
                'log_level' => $this->request->getPost('log_level'),
                'retention_days' => (int)$this->request->getPost('retention_days'),
                'log_to_file' => (bool)$this->request->getPost('log_to_file'),
                'log_to_database' => (bool)$this->request->getPost('log_to_database'),
                'log_user_actions' => (bool)$this->request->getPost('log_user_actions'),
                'log_system_events' => (bool)$this->request->getPost('log_system_events'),
                'log_security_events' => (bool)$this->request->getPost('log_security_events'),
                'log_performance' => (bool)$this->request->getPost('log_performance')
            ];
            
            // Actualizar archivo .env (simulado)
            // En producción, actualizar el archivo .env real
            
            // Log de la acción
            $this->logger->userAction('logs_settings_updated', session('user_id') ?? 0, $settings);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Configuración actualizada exitosamente'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Generar log de prueba
     */
    public function testLog()
    {
        try {
            $level = $this->request->getPost('level') ?? 'INFO';
            $message = $this->request->getPost('message') ?? 'Test log message';
            
            switch (strtoupper($level)) {
                case 'DEBUG':
                    $this->logger->debug($message, ['test' => true]);
                    break;
                case 'INFO':
                    $this->logger->info($message, ['test' => true]);
                    break;
                case 'WARNING':
                    $this->logger->warning($message, ['test' => true]);
                    break;
                case 'ERROR':
                    $this->logger->error($message, ['test' => true]);
                    break;
                case 'CRITICAL':
                    $this->logger->critical($message, ['test' => true]);
                    break;
                default:
                    $this->logger->info($message, ['test' => true]);
            }
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Log de prueba generado exitosamente'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Análisis de patrones en logs
     */
    public function analyze()
    {
        try {
            $days = (int)($this->request->getPost('days') ?? 7);
            
            // Análisis básico de patrones
            $analysis = [
                'error_patterns' => $this->analyzeErrorPatterns($days),
                'user_activity' => $this->analyzeUserActivity($days),
                'security_events' => $this->analyzeSecurityEvents($days),
                'performance_issues' => $this->analyzePerformanceIssues($days)
            ];
            
            return $this->response->setJSON([
                'success' => true,
                'analysis' => $analysis
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Analizar patrones de errores
     */
    private function analyzeErrorPatterns(int $days): array
    {
        $db = \Config\Database::connect();
        $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $result = $db->query("
            SELECT message, COUNT(*) as count, MAX(created_at) as last_occurrence
            FROM system_logs 
            WHERE level IN ('ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY') 
            AND created_at >= ? 
            GROUP BY message 
            ORDER BY count DESC 
            LIMIT 10
        ", [$dateFrom])->getResultArray();
        
        return $result;
    }
    
    /**
     * Analizar actividad de usuarios
     */
    private function analyzeUserActivity(int $days): array
    {
        $db = \Config\Database::connect();
        $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $result = $db->query("
            SELECT user_id, COUNT(*) as actions, 
                   COUNT(DISTINCT DATE(created_at)) as active_days
            FROM system_logs 
            WHERE level = 'USER_ACTION' 
            AND created_at >= ? 
            AND user_id IS NOT NULL
            GROUP BY user_id 
            ORDER BY actions DESC 
            LIMIT 10
        ", [$dateFrom])->getResultArray();
        
        return $result;
    }
    
    /**
     * Analizar eventos de seguridad
     */
    private function analyzeSecurityEvents(int $days): array
    {
        $db = \Config\Database::connect();
        $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $result = $db->query("
            SELECT ip_address, COUNT(*) as events, 
                   GROUP_CONCAT(DISTINCT message SEPARATOR '; ') as event_types
            FROM system_logs 
            WHERE level = 'SECURITY' 
            AND created_at >= ? 
            GROUP BY ip_address 
            ORDER BY events DESC 
            LIMIT 10
        ", [$dateFrom])->getResultArray();
        
        return $result;
    }
    
    /**
     * Analizar problemas de rendimiento
     */
    private function analyzePerformanceIssues(int $days): array
    {
        $db = \Config\Database::connect();
        $dateFrom = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $result = $db->query("
            SELECT message, 
                   AVG(JSON_EXTRACT(context, '$.duration_ms')) as avg_duration,
                   COUNT(*) as occurrences
            FROM system_logs 
            WHERE message LIKE 'Performance:%' 
            AND created_at >= ? 
            GROUP BY message 
            HAVING avg_duration > 1000
            ORDER BY avg_duration DESC 
            LIMIT 10
        ", [$dateFrom])->getResultArray();
        
        return $result;
    }
}
