/**
 * Sistema de Búsqueda Optimizada para MrCell Guatemala
 * Compatible con cPanel hosting - Sin dependencias externas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class MrCellSearchOptimized {
    constructor(options = {}) {
        this.apiUrl = options.apiUrl || '/api/search';
        this.suggestionsUrl = options.suggestionsUrl || '/api/search/suggestions';
        this.filtersUrl = options.filtersUrl || '/api/search/filters';
        
        this.searchInput = null;
        this.suggestionsContainer = null;
        this.resultsContainer = null;
        this.filtersContainer = null;
        this.loadingIndicator = null;
        
        this.currentQuery = '';
        this.currentFilters = {};
        this.currentPage = 1;
        this.isLoading = false;
        this.suggestionsTimeout = null;
        
        // Cache simple en localStorage
        this.cachePrefix = 'mrcell_search_';
        this.cacheTTL = 15 * 60 * 1000; // 15 minutos
        
        this.init();
    }
    
    /**
     * Inicializar el sistema de búsqueda
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadFilters();
    }
    
    /**
     * Vincular elementos del DOM
     */
    bindElements() {
        this.searchInput = document.getElementById('search-input');
        this.suggestionsContainer = document.getElementById('search-suggestions');
        this.resultsContainer = document.getElementById('search-results');
        this.filtersContainer = document.getElementById('search-filters');
        this.loadingIndicator = document.getElementById('search-loading');
        
        // Crear elementos si no existen
        if (!this.suggestionsContainer && this.searchInput) {
            this.createSuggestionsContainer();
        }
    }
    
    /**
     * Crear contenedor de sugerencias
     */
    createSuggestionsContainer() {
        this.suggestionsContainer = document.createElement('div');
        this.suggestionsContainer.id = 'search-suggestions';
        this.suggestionsContainer.className = 'search-suggestions';
        this.suggestionsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
            max-height: 300px;
            overflow-y: auto;
        `;
        
        // Insertar después del input de búsqueda
        const parent = this.searchInput.parentNode;
        parent.style.position = 'relative';
        parent.appendChild(this.suggestionsContainer);
    }
    
    /**
     * Vincular eventos
     */
    bindEvents() {
        if (this.searchInput) {
            // Búsqueda en tiempo real
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
            
            // Navegación con teclado
            this.searchInput.addEventListener('keydown', (e) => {
                this.handleKeyNavigation(e);
            });
            
            // Ocultar sugerencias al hacer clic fuera
            document.addEventListener('click', (e) => {
                if (!this.searchInput.contains(e.target) && 
                    !this.suggestionsContainer.contains(e.target)) {
                    this.hideSuggestions();
                }
            });
        }
        
        // Eventos de filtros
        if (this.filtersContainer) {
            this.filtersContainer.addEventListener('change', (e) => {
                this.handleFilterChange(e);
            });
        }
        
        // Paginación
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('search-pagination-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                this.loadPage(page);
            }
        });
    }
    
    /**
     * Manejar entrada de búsqueda
     */
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // Limpiar timeout anterior
        if (this.suggestionsTimeout) {
            clearTimeout(this.suggestionsTimeout);
        }
        
        if (this.currentQuery.length >= 2) {
            // Mostrar sugerencias después de 300ms
            this.suggestionsTimeout = setTimeout(() => {
                this.loadSuggestions(this.currentQuery);
            }, 300);
        } else {
            this.hideSuggestions();
        }
        
        // Búsqueda completa después de 800ms
        if (this.suggestionsTimeout) {
            clearTimeout(this.suggestionsTimeout);
        }
        
        this.suggestionsTimeout = setTimeout(() => {
            if (this.currentQuery.length >= 2) {
                this.performSearch();
            } else if (this.currentQuery.length === 0) {
                this.clearResults();
            }
        }, 800);
    }
    
    /**
     * Cargar sugerencias
     */
    async loadSuggestions(query) {
        try {
            // Verificar cache
            const cached = this.getFromCache(`suggestions_${query}`);
            if (cached) {
                this.displaySuggestions(cached.suggestions);
                return;
            }
            
            const response = await fetch(`${this.suggestionsUrl}?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.setCache(`suggestions_${query}`, data.data);
                this.displaySuggestions(data.data.suggestions);
            }
            
        } catch (error) {
            console.error('Error cargando sugerencias:', error);
        }
    }
    
    /**
     * Mostrar sugerencias
     */
    displaySuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }
        
        let html = '';
        suggestions.forEach((item, index) => {
            html += `
                <div class="suggestion-item" data-index="${index}" data-name="${item.name}">
                    <div class="suggestion-content">
                        ${item.image ? `<img src="${item.image}" alt="${item.name}" class="suggestion-image">` : ''}
                        <div class="suggestion-text">
                            <div class="suggestion-name">${this.highlightMatch(item.name, this.currentQuery)}</div>
                            <div class="suggestion-price">${item.price_formatted}</div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        this.suggestionsContainer.innerHTML = html;
        this.suggestionsContainer.style.display = 'block';
        
        // Agregar eventos de clic
        this.suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const name = item.dataset.name;
                this.searchInput.value = name;
                this.currentQuery = name;
                this.hideSuggestions();
                this.performSearch();
            });
        });
    }
    
    /**
     * Resaltar coincidencias en texto
     */
    highlightMatch(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }
    
    /**
     * Ocultar sugerencias
     */
    hideSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'none';
        }
    }
    
    /**
     * Realizar búsqueda completa
     */
    async performSearch(page = 1) {
        if (this.isLoading) return;
        
        this.currentPage = page;
        this.showLoading();
        this.hideSuggestions();
        
        try {
            const params = new URLSearchParams({
                q: this.currentQuery,
                page: page,
                limit: 20
            });
            
            // Agregar filtros
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key]) {
                    params.append(key, this.currentFilters[key]);
                }
            });
            
            // Verificar cache
            const cacheKey = `search_${params.toString()}`;
            const cached = this.getFromCache(cacheKey);
            if (cached && page === 1) {
                this.displayResults(cached);
                this.hideLoading();
                return;
            }
            
            const response = await fetch(`${this.apiUrl}?${params}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.setCache(cacheKey, data.data);
                this.displayResults(data.data);
            } else {
                this.showError('Error en la búsqueda');
            }
            
        } catch (error) {
            console.error('Error en búsqueda:', error);
            this.showError('Error de conexión');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Mostrar resultados
     */
    displayResults(data) {
        if (!this.resultsContainer) return;
        
        const { products, pagination } = data;
        
        let html = '';
        
        if (products.length === 0) {
            html = `
                <div class="no-results">
                    <h3>No se encontraron productos</h3>
                    <p>Intenta con otros términos de búsqueda</p>
                </div>
            `;
        } else {
            // Información de resultados
            html += `
                <div class="search-info">
                    <p>Se encontraron ${pagination.total} productos</p>
                </div>
            `;
            
            // Grid de productos
            html += '<div class="products-grid">';
            products.forEach(product => {
                html += this.renderProduct(product);
            });
            html += '</div>';
            
            // Paginación
            if (pagination.total_pages > 1) {
                html += this.renderPagination(pagination);
            }
        }
        
        this.resultsContainer.innerHTML = html;
        
        // Scroll suave a resultados
        this.resultsContainer.scrollIntoView({ behavior: 'smooth' });
    }
    
    /**
     * Renderizar producto
     */
    renderProduct(product) {
        const discountBadge = product.price.has_discount ? 
            `<span class="discount-badge">-${product.price.discount_percentage}%</span>` : '';
        
        const priceHtml = product.price.has_discount ? 
            `<span class="price-sale">${product.price.formatted}</span>
             <span class="price-regular">${product.price.regular}</span>` :
            `<span class="price">${product.price.formatted}</span>`;
        
        return `
            <div class="product-card">
                ${discountBadge}
                <a href="${product.url}" class="product-link">
                    <div class="product-image">
                        <img src="${product.image || '/assets/images/no-image.jpg'}" 
                             alt="${product.name}" loading="lazy">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <div class="product-price">${priceHtml}</div>
                        <div class="product-rating">
                            ${this.renderStars(product.rating.average)}
                            <span class="rating-count">(${product.rating.count})</span>
                        </div>
                        <div class="product-stock ${product.stock.in_stock ? 'in-stock' : 'out-of-stock'}">
                            ${product.stock.in_stock ? 'En stock' : 'Agotado'}
                        </div>
                    </div>
                </a>
            </div>
        `;
    }
    
    /**
     * Renderizar estrellas de rating
     */
    renderStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            } else {
                stars += '<i class="far fa-star"></i>';
            }
        }
        return `<div class="stars">${stars}</div>`;
    }
    
    /**
     * Renderizar paginación
     */
    renderPagination(pagination) {
        let html = '<div class="search-pagination">';
        
        // Botón anterior
        if (pagination.has_previous) {
            html += `<button class="search-pagination-btn" data-page="${pagination.previous_page}">Anterior</button>`;
        }
        
        // Números de página
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            html += `<button class="search-pagination-btn ${activeClass}" data-page="${i}">${i}</button>`;
        }
        
        // Botón siguiente
        if (pagination.has_next) {
            html += `<button class="search-pagination-btn" data-page="${pagination.next_page}">Siguiente</button>`;
        }
        
        html += '</div>';
        return html;
    }
    
    /**
     * Cargar página específica
     */
    loadPage(page) {
        this.performSearch(page);
    }
    
    /**
     * Mostrar/ocultar loading
     */
    showLoading() {
        this.isLoading = true;
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'block';
        }
    }
    
    hideLoading() {
        this.isLoading = false;
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
        }
    }
    
    /**
     * Cache simple en localStorage
     */
    setCache(key, data) {
        try {
            const cacheData = {
                data: data,
                timestamp: Date.now()
            };
            localStorage.setItem(this.cachePrefix + key, JSON.stringify(cacheData));
        } catch (e) {
            // Ignorar errores de localStorage
        }
    }
    
    getFromCache(key) {
        try {
            const cached = localStorage.getItem(this.cachePrefix + key);
            if (cached) {
                const cacheData = JSON.parse(cached);
                if (Date.now() - cacheData.timestamp < this.cacheTTL) {
                    return cacheData.data;
                } else {
                    localStorage.removeItem(this.cachePrefix + key);
                }
            }
        } catch (e) {
            // Ignorar errores de localStorage
        }
        return null;
    }
    
    /**
     * Limpiar resultados
     */
    clearResults() {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = '';
        }
    }
    
    /**
     * Mostrar error
     */
    showError(message) {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="search-error">
                    <p>${message}</p>
                </div>
            `;
        }
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    window.mrCellSearch = new MrCellSearchOptimized();
});
