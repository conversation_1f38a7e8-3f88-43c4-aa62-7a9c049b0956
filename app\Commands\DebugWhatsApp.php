<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class DebugWhatsApp extends BaseCommand
{
    protected $group       = 'WhatsApp';
    protected $name        = 'whatsapp:debug';
    protected $description = 'Debug detallado del sistema de WhatsApp';

    public function run(array $params)
    {
        CLI::write('=== DEBUG DETALLADO DE WHATSAPP ===', 'yellow');
        CLI::newLine();

        try {
            $whatsappService = new \App\Services\WhatsAppService();

            // 1. Verificar configuración
            CLI::write('1. Verificando configuración...', 'white');
            $this->debugConfiguration();

            CLI::newLine();

            // 2. Verificar plantillas
            CLI::write('2. Verificando plantillas...', 'white');
            $this->debugTemplates();

            CLI::newLine();

            // 3. Probar envío directo
            CLI::write('3. Probando envío directo...', 'white');
            $this->debugDirectSend($whatsappService);

        } catch (\Exception $e) {
            CLI::error('Error en debug: ' . $e->getMessage());
            CLI::write('Stack trace: ' . $e->getTraceAsString(), 'red');
        }

        CLI::newLine();
        CLI::write('=== DEBUG COMPLETADO ===', 'yellow');
    }

    private function debugConfiguration()
    {
        $db = \Config\Database::connect();
        
        // Configuración de base de datos
        $settings = $db->table('whatsapp_settings')->get()->getResultArray();
        foreach ($settings as $setting) {
            $value = ($setting['setting_key'] === 'api_key') ? substr($setting['setting_value'], 0, 10) . '...' : $setting['setting_value'];
            CLI::write("   DB {$setting['setting_key']}: {$value}", 'white');
        }

        // Variables de entorno
        $envVars = ['WHATSAPP_NOTIFICATIONS_ENABLED', 'WHATSAPP_API_URL', 'WHATSAPP_API_KEY', 'WHATSAPP_DEVICE_TOKEN'];
        foreach ($envVars as $var) {
            $value = env($var, 'NO DEFINIDA');
            $displayValue = (strpos($var, 'KEY') !== false || strpos($var, 'TOKEN') !== false) 
                ? substr($value, 0, 10) . '...' 
                : $value;
            CLI::write("   ENV {$var}: {$displayValue}", 'white');
        }
    }

    private function debugTemplates()
    {
        $db = \Config\Database::connect();
        
        $templates = $db->table('whatsapp_templates')
                       ->where('is_active', 1)
                       ->get()
                       ->getResultArray();

        foreach ($templates as $template) {
            CLI::write("   ✅ {$template['template_key']}: {$template['template_name']}", 'green');
            CLI::write("      Contenido: " . substr($template['message_template'], 0, 100) . '...', 'white');
        }
    }

    private function debugDirectSend($whatsappService)
    {
        // Probar envío directo usando el método interno
        $phone = '+50255555555';
        $message = 'Mensaje de prueba desde MrCell';

        CLI::write("   📱 Enviando a: {$phone}", 'white');
        CLI::write("   💬 Mensaje: {$message}", 'white');

        try {
            // Obtener configuración
            $settingsModel = new \App\Models\WhatsAppSettingsModel();
            $config = $settingsModel->getApiConfig();

            CLI::write("   🔧 API URL: {$config['api_url']}", 'white');
            CLI::write("   🔧 Device Token: {$config['device_token']}", 'white');

            // Preparar datos para envío
            $data = [
                'phone' => $phone,
                'messageType' => 1,
                'token' => $config['device_token'],
                'chat' => $message
            ];

            CLI::write("   📤 Datos a enviar: " . json_encode($data), 'cyan');

            // Hacer la petición HTTP
            $client = \Config\Services::curlrequest();
            
            $headers = [
                'Content-Type' => 'application/json',
                'X-API-Key' => $config['api_key']
            ];

            CLI::write("   🔑 Headers: " . json_encode($headers), 'cyan');

            $response = $client->post($config['api_url'], [
                'headers' => $headers,
                'json' => $data,
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody();

            CLI::write("   📊 Status Code: {$statusCode}", 'white');
            CLI::write("   📄 Response Body: {$body}", 'white');

            if ($statusCode === 200) {
                CLI::write("   ✅ Envío exitoso", 'green');
            } else {
                CLI::write("   ❌ Error en envío", 'red');
            }

        } catch (\Exception $e) {
            CLI::write("   ❌ Excepción: " . $e->getMessage(), 'red');
        }
    }
}
