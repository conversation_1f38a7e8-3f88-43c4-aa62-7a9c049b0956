<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 text-primary mb-3">
            <i class="fas fa-question-circle me-3"></i>Centro de Ayuda - Envíos
        </h1>
        <p class="lead text-muted">Todo lo que necesitas saber sobre nuestros envíos y seguimiento</p>
    </div>

    <!-- Búsqueda Rápida -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow border-0">
                <div class="card-body p-4">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="helpSearch" 
                               placeholder="Buscar en la ayuda..." onkeyup="searchHelp()">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="h3 text-center mb-4">Acciones Rápidas</h2>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0 text-center">
                <div class="card-body py-4">
                    <i class="fas fa-search fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Rastrear Envío</h5>
                    <p class="card-text text-muted">Busca tu paquete con el número de seguimiento</p>
                    <a href="<?= base_url('tracking') ?>" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Rastrear
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0 text-center">
                <div class="card-body py-4">
                    <i class="fas fa-calculator fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Calcular Envío</h5>
                    <p class="card-text text-muted">Conoce el costo de envío a tu departamento</p>
                    <button class="btn btn-success" onclick="showShippingCalculator()">
                        <i class="fas fa-calculator me-2"></i>Calcular
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0 text-center">
                <div class="card-body py-4">
                    <i class="fas fa-headset fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Contactar Soporte</h5>
                    <p class="card-text text-muted">Habla con nuestro equipo de atención al cliente</p>
                    <a href="<?= base_url('contact') ?>" class="btn btn-info">
                        <i class="fas fa-headset me-2"></i>Contactar
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0 text-center">
                <div class="card-body py-4">
                    <i class="fas fa-list fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Mis Pedidos</h5>
                    <p class="card-text text-muted">Ve todos tus pedidos y su estado</p>
                    <a href="<?= base_url('user/orders') ?>" class="btn btn-warning">
                        <i class="fas fa-list me-2"></i>Ver Pedidos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Preguntas Frecuentes -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="h3 text-center mb-4">Preguntas Frecuentes</h2>
        </div>
        
        <div class="col-lg-10 mx-auto">
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item" data-keywords="seguimiento numero tracking">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            <i class="fas fa-barcode me-2"></i>¿Dónde encuentro mi número de seguimiento?
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>Tu número de seguimiento se encuentra en varios lugares:</p>
                            <ul>
                                <li><strong>Email de confirmación:</strong> Revisa tu bandeja de entrada después de realizar la compra</li>
                                <li><strong>Tu cuenta:</strong> En la sección "Mis Pedidos" de tu perfil de usuario</li>
                                <li><strong>SMS:</strong> Si proporcionaste tu número de teléfono, recibirás un mensaje</li>
                            </ul>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Tip:</strong> El número de seguimiento generalmente tiene entre 8-15 caracteres y puede contener letras y números.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-keywords="tiempo entrega demora">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            <i class="fas fa-clock me-2"></i>¿Cuánto tiempo tarda mi envío?
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>Los tiempos de entrega varían según la empresa y destino:</p>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="text-primary">Cargo Expreso</h6>
                                            <p class="mb-0">2-3 días hábiles</p>
                                            <small class="text-muted">Nacional</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="text-success">GuatEx</h6>
                                            <p class="mb-0">1-2 días hábiles</p>
                                            <small class="text-muted">Zona Metro</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="text-warning">Forza</h6>
                                            <p class="mb-0">24 horas</p>
                                            <small class="text-muted">Express</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-keywords="costo precio envio gratis">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            <i class="fas fa-dollar-sign me-2"></i>¿Cuánto cuesta el envío?
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>Los costos de envío dependen del destino y peso del paquete:</p>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Departamento</th>
                                            <th>Costo Estándar</th>
                                            <th>Costo Express</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Guatemala (Zona 1-21)</td>
                                            <td>Q25.00</td>
                                            <td>Q45.00</td>
                                        </tr>
                                        <tr>
                                            <td>Zona Metropolitana</td>
                                            <td>Q35.00</td>
                                            <td>Q55.00</td>
                                        </tr>
                                        <tr>
                                            <td>Departamentos</td>
                                            <td>Q45.00 - Q75.00</td>
                                            <td>Q65.00 - Q95.00</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="alert alert-success">
                                <i class="fas fa-gift me-2"></i>
                                <strong>¡Envío gratis!</strong> En compras mayores a Q200.00
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-keywords="problema retraso perdido">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                            <i class="fas fa-exclamation-triangle me-2"></i>¿Qué hago si hay un problema con mi envío?
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>Si tienes problemas con tu envío, sigue estos pasos:</p>
                            <ol>
                                <li><strong>Verifica el seguimiento:</strong> Asegúrate de que el número sea correcto</li>
                                <li><strong>Espera 24 horas:</strong> A veces hay retrasos en las actualizaciones</li>
                                <li><strong>Contacta al transportista:</strong> Llama directamente a la empresa de envío</li>
                                <li><strong>Reporta el problema:</strong> Usa nuestro formulario de reporte</li>
                            </ol>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button class="btn btn-warning w-100" onclick="reportIssue()">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Reportar Problema
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <a href="<?= base_url('contact') ?>" class="btn btn-primary w-100">
                                        <i class="fas fa-headset me-2"></i>Contactar Soporte
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-keywords="cambiar direccion modificar">
                    <h2 class="accordion-header" id="faq5">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                            <i class="fas fa-map-marker-alt me-2"></i>¿Puedo cambiar la dirección de entrega?
                        </button>
                    </h2>
                    <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>El cambio de dirección depende del estado del envío:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0"><i class="fas fa-check me-2"></i>Posible cambiar</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-circle fa-xs text-success me-2"></i>Pedido procesándose</li>
                                                <li><i class="fas fa-circle fa-xs text-success me-2"></i>En preparación</li>
                                                <li><i class="fas fa-circle fa-xs text-success me-2"></i>Aún no enviado</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0"><i class="fas fa-times me-2"></i>No se puede cambiar</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-circle fa-xs text-danger me-2"></i>Ya enviado</li>
                                                <li><i class="fas fa-circle fa-xs text-danger me-2"></i>En tránsito</li>
                                                <li><i class="fas fa-circle fa-xs text-danger me-2"></i>En reparto</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-2"></i>
                                Para cambios de dirección, contacta inmediatamente a nuestro servicio al cliente.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de Empresas -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="h3 text-center mb-4">Nuestras Empresas de Envío</h2>
        </div>
        
        <?php if (!empty($companies)): ?>
            <?php foreach ($companies as $company): ?>
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title text-primary">
                                <i class="fas fa-truck me-2"></i><?= esc($company['name']) ?>
                            </h5>
                            <p class="card-text"><?= esc($company['description'] ?? '') ?></p>
                            
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Teléfono</small>
                                    <div class="fw-bold"><?= esc($company['phone'] ?? 'N/A') ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Horario</small>
                                    <div class="fw-bold"><?= esc($company['hours'] ?? '8AM-6PM') ?></div>
                                </div>
                            </div>
                            
                            <?php if (!empty($company['website'])): ?>
                                <a href="<?= esc($company['website']) ?>" target="_blank" class="btn btn-outline-primary btn-sm w-100">
                                    <i class="fas fa-external-link-alt me-2"></i>Sitio Web
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Calculadora de Envío -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow border-0" id="shippingCalculator" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>Calculadora de Envío
                    </h5>
                </div>
                <div class="card-body">
                    <form id="calculateShippingForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="calc_department" class="form-label">Departamento de Destino</label>
                                <select class="form-select" id="calc_department" required>
                                    <option value="">Seleccionar departamento</option>
                                    <?php if (!empty($departments)): ?>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= $dept['id'] ?>"><?= esc($dept['name']) ?></option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="calc_weight" class="form-label">Peso Aproximado (kg)</label>
                                <input type="number" class="form-control" id="calc_weight" step="0.1" min="0.1" value="1" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="calc_company" class="form-label">Empresa de Envío</label>
                                <select class="form-select" id="calc_company">
                                    <option value="">Todas las empresas</option>
                                    <option value="cargoexpreso">Cargo Expreso</option>
                                    <option value="guatex">GuatEx</option>
                                    <option value="forza">Forza</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-calculator me-2"></i>Calcular Costo
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div id="calculationResult" style="display: none;">
                        <hr>
                        <h6 class="text-success">Resultado del Cálculo:</h6>
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contacto -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h3 class="mb-3">¿No encontraste lo que buscabas?</h3>
                    <p class="mb-4">Nuestro equipo de soporte está aquí para ayudarte con cualquier pregunta sobre tus envíos.</p>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-4 mb-2">
                            <a href="<?= base_url('contact') ?>" class="btn btn-light w-100">
                                <i class="fas fa-headset me-2"></i>Chat en Vivo
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="mailto:<EMAIL>" class="btn btn-outline-light w-100">
                                <i class="fas fa-envelope me-2"></i>Email
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="tel:+50212345678" class="btn btn-outline-light w-100">
                                <i class="fas fa-phone me-2"></i>Teléfono
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Búsqueda en la ayuda
function searchHelp() {
    const searchTerm = document.getElementById('helpSearch').value.toLowerCase();
    const accordionItems = document.querySelectorAll('.accordion-item');
    
    accordionItems.forEach(item => {
        const keywords = item.getAttribute('data-keywords') || '';
        const title = item.querySelector('.accordion-button').textContent.toLowerCase();
        const content = item.querySelector('.accordion-body').textContent.toLowerCase();
        
        const isMatch = keywords.includes(searchTerm) || 
                       title.includes(searchTerm) || 
                       content.includes(searchTerm);
        
        item.style.display = isMatch || searchTerm === '' ? 'block' : 'none';
    });
}

// Mostrar calculadora de envío
function showShippingCalculator() {
    const calculator = document.getElementById('shippingCalculator');
    calculator.style.display = calculator.style.display === 'none' ? 'block' : 'none';
    
    if (calculator.style.display === 'block') {
        calculator.scrollIntoView({ behavior: 'smooth' });
    }
}

// Calcular costo de envío
document.getElementById('calculateShippingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const department = document.getElementById('calc_department').value;
    const weight = document.getElementById('calc_weight').value;
    const company = document.getElementById('calc_company').value;
    
    if (!department || !weight) {
        alert('Por favor completa todos los campos requeridos');
        return;
    }
    
    // Simular cálculo (aquí iría la llamada AJAX real)
    fetch('<?= base_url('tracking/calculate-shipping') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            department: department,
            weight: weight,
            company: company
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showCalculationResult(data.rates);
        } else {
            alert('Error: ' + (data.error || 'No se pudo calcular el costo'));
        }
    })
    .catch(error => {
        // Mostrar resultado simulado
        const simulatedRates = [
            { company: 'Cargo Expreso', cost: 45.00, time: '2-3 días' },
            { company: 'GuatEx', cost: 55.00, time: '1-2 días' },
            { company: 'Forza', cost: 65.00, time: '24 horas' }
        ];
        showCalculationResult(simulatedRates);
    });
});

function showCalculationResult(rates) {
    const resultDiv = document.getElementById('calculationResult');
    const contentDiv = document.getElementById('resultContent');
    
    let html = '<div class="row">';
    rates.forEach(rate => {
        html += `
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">${rate.company}</h6>
                        <div class="h4 text-success">Q${rate.cost.toFixed(2)}</div>
                        <small class="text-muted">${rate.time}</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    contentDiv.innerHTML = html;
    resultDiv.style.display = 'block';
}

// Reportar problema
function reportIssue() {
    const trackingNumber = prompt('Ingresa tu número de seguimiento:');
    
    if (trackingNumber) {
        const issue = prompt('Describe el problema:');
        
        if (issue) {
            // Aquí iría la llamada AJAX real
            alert('Tu reporte ha sido enviado. Nos pondremos en contacto contigo pronto.');
        }
    }
}
</script>
<?= $this->endSection() ?>
