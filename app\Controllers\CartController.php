<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ProductModel;
use App\Models\ShippingPackageTypeModel;
use App\Models\ShippingZoneModel;

/**
 * Cart Controller
 *
 * Handles cart and checkout pages
 */
class CartController extends Controller
{
    protected $productModel;
    protected $packageTypeModel;
    protected $zoneModel;

    public function __construct()
    {
        $this->productModel = new ProductModel();
        $this->packageTypeModel = new ShippingPackageTypeModel();
        $this->zoneModel = new ShippingZoneModel();
    }

    /**
     * Cart page
     */
    public function index()
    {
        $cartItems = $this->getCartItems();
        $cartCount = $this->getCartCount();
        $cartSubtotal = $this->getCartSubtotal();
        $shippingCost = $this->getShippingCost();
        $taxSettings = $this->getTaxSettings();
        $taxAmount = $this->getTaxAmount($taxSettings);
        $cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

        $data = [
            'cart_items' => $cartItems,
            'cart_count' => $cartCount,
            'cart_total' => $cartTotal,
            'cart_subtotal' => $cartSubtotal,
            'shipping_cost' => $shippingCost,
            'tax_amount' => $taxAmount,
            'tax_settings' => $taxSettings,
            'shipping_methods' => $this->getShippingMethods()
        ];

        return view('frontend/cart_modern', $data);
    }

    /**
     * Checkout page
     */
    public function checkout()
    {
        return view('frontend/checkout_modern');
    }

    /**
     * Order confirmation page
     */
    public function confirmation($orderId = null)
    {
        if (!$orderId) {
            return redirect()->to(base_url());
        }

        $data = [
            'order_id' => $orderId,
            'title' => 'Confirmación de Pedido - MrCell Guatemala'
        ];

        return view('frontend/confirmation', $data);
    }

    /**
     * Legacy cart page (if needed)
     */
    public function legacy()
    {
        return view('frontend/cart');
    }

    /**
     * Vaciar carrito completamente
     */
    public function clear()
    {
        // Eliminar todos los items del carrito de la sesión
        session()->remove('cart');
        session()->remove('cart_count');
        session()->remove('cart_total');

        // Log de la acción
        log_message('info', 'Carrito vaciado por el usuario');

        // Responder según el tipo de petición
        if ($this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Carrito vaciado exitosamente',
                'cart_count' => 0,
                'cart_total' => 0
            ]);
        }

        return redirect()->to('/carrito')
                       ->with('success', 'Carrito vaciado exitosamente');
    }

    /**
     * Get cart items from session with product details
     */
    private function getCartItems()
    {
        $cart = session()->get('cart') ?? [];
        $cartItems = [];

        foreach ($cart as $cartKey => $item) {
            // Extraer product_id del cart key (puede ser "123" o "123_variant_456")
            $productId = isset($item['product_id']) ? $item['product_id'] : explode('_', $cartKey)[0];
            $variantId = isset($item['variant_id']) ? $item['variant_id'] : null;

            $product = $this->productModel->find($productId);
            if ($product && $product['is_active']) {
                $cartItem = [
                    'cart_key' => $cartKey,
                    'product_id' => $product['id'],
                    'variant_id' => $variantId,
                    'name' => $item['name'] ?? $product['name'],
                    'sku' => $item['sku'] ?? $product['sku'],
                    'price' => $item['price'] ?? ($product['price_sale'] ?: $product['price_regular']),
                    'quantity' => $item['quantity'] ?? 1,
                    'image' => $item['image'] ?? $product['featured_image'],
                    'stock' => $item['stock'] ?? $product['stock_quantity'],
                    'subtotal' => ($item['price'] ?? ($product['price_sale'] ?: $product['price_regular'])) * ($item['quantity'] ?? 1)
                ];

                // Si es una variante, agregar información adicional
                if ($variantId) {
                    $cartItem['is_variant'] = true;
                    $cartItem['variant_name'] = $item['name']; // El nombre ya viene de la variante
                    $cartItem['product_name'] = $product['name']; // Nombre del producto padre
                } else {
                    $cartItem['is_variant'] = false;
                }

                $cartItems[] = $cartItem;
            }
        }

        return $cartItems;
    }

    /**
     * Get total count of items in cart
     */
    private function getCartCount()
    {
        $cart = session()->get('cart') ?? [];
        $count = 0;

        foreach ($cart as $item) {
            $count += $item['quantity'] ?? 1;
        }

        return $count;
    }

    /**
     * Get cart subtotal (before shipping and tax)
     */
    private function getCartSubtotal()
    {
        $items = $this->getCartItems();
        $subtotal = 0;

        foreach ($items as $item) {
            $subtotal += $item['subtotal'];
        }

        return $subtotal;
    }

    /**
     * Get cart total (including shipping and tax)
     */
    private function getCartTotal()
    {
        return $this->getCartSubtotal() + $this->getShippingCost() + $this->getTaxAmount();
    }

    /**
     * Get shipping cost
     */
    private function getShippingCost()
    {
        // El envío es Q0.00 hasta que se seleccione un método
        // No hay envío gratis automático
        return 0.00;
    }

    /**
     * Get available shipping methods based on cart contents
     */
    private function getShippingMethods()
    {
        $cartItems = $this->getCartItems();
        $shippingMethods = [];

        // Verificar configuración de envíos
        $db = \Config\Database::connect();
        $shippingSettings = [];
        try {
            $settings = $db->query("
                SELECT setting_key, setting_value
                FROM system_settings
                WHERE setting_group = 'shipping' AND is_active = 1
            ")->getResultArray();

            foreach ($settings as $setting) {
                $shippingSettings[$setting['setting_key']] = $setting['setting_value'];
            }
        } catch (\Exception $e) {
            // Valores por defecto si hay error
            $shippingSettings = [
                'shipping_enabled' => '1',
                'free_shipping_enabled' => '1',
                'free_shipping_threshold' => '500',
                'default_shipping_cost' => '25'
            ];
        }

        // Si los envíos están deshabilitados, solo mostrar retiro en tienda
        if (($shippingSettings['shipping_enabled'] ?? '1') == '0') {
            return [[
                'id' => 'pickup',
                'name' => 'Retiro en Tienda',
                'description' => 'Retiro gratuito en nuestras sucursales',
                'rate' => 0.00
            ]];
        }

        // Calcular dimensiones y peso total del carrito
        $totalWeight = 0;
        $maxLength = 0;
        $maxWidth = 0;
        $maxHeight = 0;
        $cartSubtotal = $this->getCartSubtotal();

        foreach ($cartItems as $item) {
            $totalWeight += ($item['weight'] ?? 1) * $item['quantity'];
            $maxLength = max($maxLength, $item['length'] ?? 10);
            $maxWidth = max($maxWidth, $item['width'] ?? 10);
            $maxHeight = max($maxHeight, $item['height'] ?? 5);
        }

        // Verificar envío gratis
        if (($shippingSettings['free_shipping_enabled'] ?? '1') == '1' &&
            $cartSubtotal >= floatval($shippingSettings['free_shipping_threshold'] ?? 500)) {
            $shippingMethods[] = [
                'id' => 'free',
                'name' => 'Envío Gratis',
                'description' => 'Compra mayor a Q' . number_format($shippingSettings['free_shipping_threshold'], 0),
                'rate' => 0.00
            ];
        }

        // Siempre incluir retiro en tienda
        $shippingMethods[] = [
            'id' => 'pickup',
            'name' => 'Retiro en Tienda',
            'description' => 'Sin costo adicional',
            'rate' => 0.00
        ];

        // Determinar tipo de paquete apropiado
        $packageType = $this->packageTypeModel->determinePackageType($maxLength, $maxWidth, $maxHeight, $totalWeight);

        if ($packageType) {
            // Obtener zonas de envío
            $zones = $this->zoneModel->getActiveZones();

            foreach ($zones as $zone) {
                $cost = $this->packageTypeModel->calculateShippingCost(
                    $packageType['id'],
                    $zone['base_distance_km'],
                    $zone['additional_cost']
                );

                // Simplificar nombres de zonas para mejor legibilidad
                $zoneName = str_replace(['Ciudad de Guatemala - ', 'Municipios '], ['', ''], $zone['name']);

                $shippingMethods[] = [
                    'id' => 'zone_' . $zone['id'],
                    'name' => $zoneName,
                    'description' => $this->getSimpleDescription($zone['name'], $packageType['name']),
                    'rate' => $cost
                ];
            }
        } else {
            // Usar costo por defecto si no se puede determinar el tipo
            $defaultCost = floatval($shippingSettings['default_shipping_cost'] ?? 25);
            $shippingMethods[] = [
                'id' => 'standard',
                'name' => 'Envío Estándar',
                'description' => '3-5 días hábiles',
                'rate' => $defaultCost
            ];
        }

        return $shippingMethods;
    }

    /**
     * Get tax amount based on settings
     */
    private function getTaxAmount($taxSettings = null)
    {
        if (!$taxSettings) {
            $taxSettings = $this->getTaxSettings();
        }

        // Only calculate tax if enabled
        if (!($taxSettings['tax_enabled'] ?? false)) {
            return 0.00;
        }

        $subtotal = $this->getCartSubtotal();
        $taxRate = ($taxSettings['tax_rate'] ?? 0) / 100;

        return $subtotal * $taxRate;
    }

    /**
     * Get tax settings from database
     */
    private function getTaxSettings()
    {
        $db = \Config\Database::connect();

        try {
            $settings = $db->query("
                SELECT setting_key, setting_value, setting_type
                FROM system_settings
                WHERE setting_group = 'taxes' AND is_active = 1
            ")->getResultArray();

            $taxSettings = [];
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];

                // Cast values based on type
                if ($setting['setting_type'] === 'checkbox') {
                    $value = (bool) $value;
                } elseif ($setting['setting_type'] === 'number') {
                    $value = (float) $value;
                }

                $taxSettings[$setting['setting_key']] = $value;
            }

            return $taxSettings;

        } catch (\Exception $e) {
            log_message('error', 'Error getting tax settings: ' . $e->getMessage());
            return [
                'tax_enabled' => false,
                'tax_rate' => 0,
                'tax_name' => 'IVA',
                'tax_included_in_price' => false
            ];
        }
    }

    /**
     * API endpoint para calcular envío dinámicamente
     */
    public function calculateShipping()
    {
        try {
            $cartItems = $this->getCartItems();
            $shippingMethods = $this->getShippingMethods();

            return $this->response->setJSON([
                'success' => true,
                'shipping_methods' => $shippingMethods,
                'cart_subtotal' => $this->getCartSubtotal(),
                'tax_amount' => $this->getTaxAmount()
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al calcular envío: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generar descripción simple para métodos de envío
     */
    private function getSimpleDescription($zoneName, $packageType)
    {
        // Mapear zonas a descripciones más cortas
        $zoneDescriptions = [
            'Ciudad de Guatemala - Centro' => '2-3 días',
            'Ciudad de Guatemala - Zonas' => '3-4 días',
            'Municipios Cercanos' => '4-5 días'
        ];

        // Mapear tipos de paquete a descripciones simples
        $packageDescriptions = [
            'Pequeño' => 'hasta 28cm',
            'Mediano' => 'hasta 36cm',
            'Grande' => 'hasta 47cm',
            'Extra Grande' => 'hasta 51cm',
            'Sobredimensionado' => 'más de 60lbs'
        ];

        $timeDescription = $zoneDescriptions[$zoneName] ?? '3-5 días';
        $sizeDescription = $packageDescriptions[$packageType] ?? '';

        return $timeDescription . ($sizeDescription ? ' • ' . $sizeDescription : '');
    }
}
