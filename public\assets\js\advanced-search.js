/**
 * Sistema de Búsqueda Avanzada para MrCell Guatemala
 * Integra búsqueda inteligente, filtros dinámicos y búsqueda por voz
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class AdvancedSearchManager {
    constructor(options = {}) {
        this.options = {
            searchInputSelector: '#search-input',
            resultsContainerSelector: '#search-results',
            filtersContainerSelector: '#search-filters',
            suggestionsContainerSelector: '#search-suggestions',
            apiBaseUrl: '/api/search',
            debounceDelay: 300,
            minSearchLength: 2,
            maxSuggestions: 10,
            enableVoiceSearch: true,
            enableSmartFilters: true,
            enableAnalytics: true,
            ...options
        };
        
        this.searchInput = null;
        this.resultsContainer = null;
        this.filtersContainer = null;
        this.suggestionsContainer = null;
        
        this.currentQuery = '';
        this.currentFilters = {};
        this.currentPage = 1;
        this.isSearching = false;
        this.searchTimeout = null;
        
        this.cache = new Map();
        this.cacheTTL = 5 * 60 * 1000; // 5 minutos
        
        this.init();
    }
    
    /**
     * Inicializar el sistema
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadSmartFilters();
        
        // Inicializar búsqueda por voz si está habilitado
        if (this.options.enableVoiceSearch && window.VoiceSearchManager) {
            this.initVoiceSearch();
        }
        
        // Cargar estado inicial si hay query en URL
        this.loadInitialState();
    }
    
    /**
     * Vincular elementos del DOM
     */
    bindElements() {
        this.searchInput = document.querySelector(this.options.searchInputSelector);
        this.resultsContainer = document.querySelector(this.options.resultsContainerSelector);
        this.filtersContainer = document.querySelector(this.options.filtersContainerSelector);
        this.suggestionsContainer = document.querySelector(this.options.suggestionsContainerSelector);
        
        if (!this.searchInput) {
            console.warn('Advanced Search: Search input not found');
            return;
        }
    }
    
    /**
     * Vincular eventos
     */
    bindEvents() {
        if (!this.searchInput) return;
        
        // Evento de input con debounce
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.handleSearchInput(e.target.value);
            }, this.options.debounceDelay);
        });
        
        // Evento de submit del formulario
        const searchForm = this.searchInput.closest('form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(this.searchInput.value);
            });
        }
        
        // Eventos de teclado para navegación de sugerencias
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });
        
        // Evento de focus para mostrar sugerencias
        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.length >= this.options.minSearchLength) {
                this.showSuggestions();
            }
        });
        
        // Evento de blur para ocultar sugerencias (con delay)
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideSuggestions();
            }, 200);
        });
        
        // Eventos de filtros
        if (this.filtersContainer) {
            this.filtersContainer.addEventListener('change', (e) => {
                this.handleFilterChange(e);
            });
        }
        
        // Eventos globales
        document.addEventListener('click', (e) => {
            // Cerrar sugerencias al hacer clic fuera
            if (!e.target.closest('.search-suggestions')) {
                this.hideSuggestions();
            }
        });
    }
    
    /**
     * Manejar input de búsqueda
     */
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        if (this.currentQuery.length >= this.options.minSearchLength) {
            this.loadSuggestions(this.currentQuery);
            
            // Búsqueda automática si está habilitada
            if (this.options.autoSearch) {
                this.performSearch(this.currentQuery);
            }
        } else {
            this.hideSuggestions();
        }
    }
    
    /**
     * Realizar búsqueda principal
     */
    async performSearch(query = null, page = 1) {
        if (this.isSearching) return;
        
        query = query || this.currentQuery;
        if (!query || query.length < this.options.minSearchLength) {
            return;
        }
        
        this.isSearching = true;
        this.currentQuery = query;
        this.currentPage = page;
        
        try {
            this.showSearchLoading();
            
            // Construir parámetros
            const params = new URLSearchParams({
                q: query,
                page: page,
                limit: 20,
                filters: JSON.stringify(this.currentFilters),
                include_suggestions: 'true',
                include_analytics: this.options.enableAnalytics ? 'true' : 'false'
            });
            
            // Verificar cache
            const cacheKey = `search_${params.toString()}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                this.displaySearchResults(cached);
                this.isSearching = false;
                return;
            }
            
            // Realizar búsqueda
            const response = await fetch(`${this.options.apiBaseUrl}/advanced?${params}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.setCache(cacheKey, data.data);
                this.displaySearchResults(data.data);
                
                // Actualizar filtros inteligentes
                if (this.options.enableSmartFilters) {
                    this.updateSmartFilters(query);
                }
                
                // Analytics
                if (this.options.enableAnalytics) {
                    this.trackSearch(query, data.data.pagination.total);
                }
            } else {
                this.showSearchError(data.message || 'Error en la búsqueda');
            }
            
        } catch (error) {
            console.error('Search error:', error);
            this.showSearchError('Error de conexión');
        } finally {
            this.isSearching = false;
            this.hideSearchLoading();
        }
    }
    
    /**
     * Cargar sugerencias
     */
    async loadSuggestions(query) {
        try {
            const params = new URLSearchParams({
                q: query,
                limit: this.options.maxSuggestions,
                include_products: 'true',
                include_categories: 'true',
                include_brands: 'true'
            });
            
            const response = await fetch(`${this.options.apiBaseUrl}/smart-suggestions?${params}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displaySuggestions(data.data);
            }
            
        } catch (error) {
            console.error('Suggestions error:', error);
        }
    }
    
    /**
     * Cargar filtros inteligentes
     */
    async loadSmartFilters(query = '') {
        if (!this.options.enableSmartFilters || !this.filtersContainer) {
            return;
        }
        
        try {
            const params = new URLSearchParams({
                q: query,
                current_filters: JSON.stringify(this.currentFilters)
            });
            
            const response = await fetch(`${this.options.apiBaseUrl}/smart-filters?${params}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displaySmartFilters(data.data);
            }
            
        } catch (error) {
            console.error('Smart filters error:', error);
        }
    }
    
    /**
     * Mostrar resultados de búsqueda
     */
    displaySearchResults(data) {
        if (!this.resultsContainer) return;
        
        let html = '';
        
        // Header con información de resultados
        html += `
            <div class="search-results-header">
                <div class="results-info">
                    <span class="results-count">${data.pagination.total} resultados</span>
                    ${data.query_info ? `<span class="search-query">para "${data.query_info.original}"</span>` : ''}
                </div>
                <div class="results-meta">
                    ${data.analytics ? this.renderAnalytics(data.analytics) : ''}
                </div>
            </div>
        `;
        
        // Productos
        if (data.products && data.products.length > 0) {
            html += '<div class="search-results-grid">';
            data.products.forEach(product => {
                html += this.renderProductCard(product);
            });
            html += '</div>';
            
            // Paginación
            if (data.pagination.total_pages > 1) {
                html += this.renderPagination(data.pagination);
            }
        } else {
            html += this.renderNoResults();
        }
        
        this.resultsContainer.innerHTML = html;
        
        // Mostrar sugerencias si las hay
        if (data.suggestions && data.suggestions.length > 0) {
            this.displaySearchSuggestions(data.suggestions);
        }
    }
    
    /**
     * Mostrar sugerencias de autocompletado
     */
    displaySuggestions(suggestions) {
        if (!this.suggestionsContainer || !suggestions.length) {
            this.hideSuggestions();
            return;
        }
        
        let html = '<div class="search-suggestions-list">';
        
        suggestions.forEach((suggestion, index) => {
            const iconClass = this.getSuggestionIcon(suggestion.type);
            html += `
                <div class="suggestion-item" data-index="${index}" data-query="${suggestion.text}">
                    <i class="${iconClass}"></i>
                    <span class="suggestion-text">${this.highlightQuery(suggestion.text, this.currentQuery)}</span>
                    <span class="suggestion-type">${this.getSuggestionTypeLabel(suggestion.type)}</span>
                </div>
            `;
        });
        
        html += '</div>';
        
        this.suggestionsContainer.innerHTML = html;
        this.suggestionsContainer.style.display = 'block';
        
        // Vincular eventos de clic
        this.suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const query = item.dataset.query;
                this.searchInput.value = query;
                this.performSearch(query);
                this.hideSuggestions();
            });
        });
    }
    
    /**
     * Mostrar filtros inteligentes
     */
    displaySmartFilters(filters) {
        if (!this.filtersContainer) return;
        
        let html = '';
        
        Object.keys(filters).forEach(filterType => {
            const filter = filters[filterType];
            if (!filter.options || filter.options.length === 0) return;
            
            html += `
                <div class="smart-filter-group" data-filter-type="${filterType}">
                    <h4 class="filter-title">${filter.label}</h4>
                    <div class="filter-options ${filter.display_type}">
                        ${this.renderFilterOptions(filter, filterType)}
                    </div>
                </div>
            `;
        });
        
        this.filtersContainer.innerHTML = html;
    }
    
    /**
     * Renderizar opciones de filtro
     */
    renderFilterOptions(filter, filterType) {
        let html = '';
        
        switch (filter.display_type) {
            case 'list':
                filter.options.forEach(option => {
                    const isSelected = this.isFilterSelected(filterType, option.id);
                    html += `
                        <label class="filter-option ${isSelected ? 'selected' : ''}">
                            <input type="${filter.allow_multiple ? 'checkbox' : 'radio'}" 
                                   name="${filterType}" 
                                   value="${option.id}"
                                   ${isSelected ? 'checked' : ''}>
                            <span class="option-text">${option.name}</span>
                            <span class="option-count">(${option.product_count})</span>
                        </label>
                    `;
                });
                break;
                
            case 'grid':
                filter.options.forEach(option => {
                    const isSelected = this.isFilterSelected(filterType, option.id);
                    html += `
                        <label class="filter-option-grid ${isSelected ? 'selected' : ''}">
                            <input type="checkbox" name="${filterType}" value="${option.id}" ${isSelected ? 'checked' : ''}>
                            ${option.logo ? `<img src="${option.logo}" alt="${option.name}" class="option-logo">` : ''}
                            <span class="option-text">${option.name}</span>
                            <span class="option-count">(${option.product_count})</span>
                        </label>
                    `;
                });
                break;
                
            case 'range':
                if (filter.min_value !== undefined && filter.max_value !== undefined) {
                    html += `
                        <div class="price-range-slider">
                            <input type="range" 
                                   id="${filterType}-min" 
                                   min="${filter.min_value}" 
                                   max="${filter.max_value}" 
                                   value="${this.currentFilters.min_price || filter.min_value}"
                                   class="range-input">
                            <input type="range" 
                                   id="${filterType}-max" 
                                   min="${filter.min_value}" 
                                   max="${filter.max_value}" 
                                   value="${this.currentFilters.max_price || filter.max_value}"
                                   class="range-input">
                            <div class="range-values">
                                <span id="${filterType}-min-value">${filter.currency}${this.currentFilters.min_price || filter.min_value}</span>
                                -
                                <span id="${filterType}-max-value">${filter.currency}${this.currentFilters.max_price || filter.max_value}</span>
                            </div>
                        </div>
                    `;
                }
                break;
                
            case 'stars':
                filter.options.forEach(option => {
                    const isSelected = this.isFilterSelected(filterType, option.value);
                    html += `
                        <label class="filter-option-stars ${isSelected ? 'selected' : ''}">
                            <input type="radio" name="${filterType}" value="${option.value}" ${isSelected ? 'checked' : ''}>
                            <div class="stars">
                                ${this.renderStars(option.value)}
                            </div>
                            <span class="option-text">${option.label}</span>
                            <span class="option-count">(${option.product_count})</span>
                        </label>
                    `;
                });
                break;
        }
        
        return html;
    }
    
    /**
     * Renderizar tarjeta de producto
     */
    renderProductCard(product) {
        const discountBadge = product.price.has_discount ? 
            `<span class="discount-badge">-${product.price.discount_percentage}%</span>` : '';
        
        const stockClass = product.stock.in_stock ? 'in-stock' : 'out-of-stock';
        const stockText = product.stock.in_stock ? 'En stock' : 'Agotado';
        
        return `
            <div class="product-card" data-product-id="${product.id}">
                ${discountBadge}
                <div class="product-image">
                    <img src="${product.image.featured || '/assets/images/no-image.jpg'}" 
                         alt="${product.name}" loading="lazy">
                    <div class="product-actions">
                        <button class="btn-wishlist" data-product-id="${product.id}">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="btn-quick-view" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">
                        <a href="${product.url}">${product.name}</a>
                    </h3>
                    <div class="product-meta">
                        <span class="product-brand">${product.brand.name}</span>
                        <span class="product-category">${product.category.name}</span>
                    </div>
                    <div class="product-rating">
                        ${this.renderStars(product.rating.average)}
                        <span class="rating-count">(${product.rating.count})</span>
                    </div>
                    <div class="product-price">
                        ${product.price.has_discount ? 
                            `<span class="price-sale">${product.price.formatted}</span>
                             <span class="price-regular">Q${product.price.regular.toFixed(2)}</span>` :
                            `<span class="price">${product.price.formatted}</span>`
                        }
                    </div>
                    <div class="product-stock ${stockClass}">
                        ${stockText}
                    </div>
                    <div class="product-actions-bottom">
                        <button class="btn btn-primary btn-add-cart" 
                                data-product-id="${product.id}"
                                ${!product.stock.in_stock ? 'disabled' : ''}>
                            <i class="fas fa-shopping-cart"></i>
                            ${product.stock.in_stock ? 'Agregar al Carrito' : 'Agotado'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Renderizar estrellas de rating
     */
    renderStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            } else {
                stars += '<i class="far fa-star"></i>';
            }
        }
        return `<div class="stars">${stars}</div>`;
    }
    
    /**
     * Inicializar búsqueda por voz
     */
    initVoiceSearch() {
        if (window.voiceSearchManager) {
            window.voiceSearchManager.on('result', (result) => {
                if (result.isFinal && result.final.trim()) {
                    this.performSearch(result.final.trim());
                }
            });
        }
    }
    
    /**
     * Manejar cambio de filtros
     */
    handleFilterChange(event) {
        const input = event.target;
        const filterType = input.name;
        const value = input.value;
        
        if (input.type === 'checkbox') {
            if (!this.currentFilters[filterType]) {
                this.currentFilters[filterType] = [];
            }
            
            if (input.checked) {
                if (!this.currentFilters[filterType].includes(value)) {
                    this.currentFilters[filterType].push(value);
                }
            } else {
                this.currentFilters[filterType] = this.currentFilters[filterType].filter(v => v !== value);
                if (this.currentFilters[filterType].length === 0) {
                    delete this.currentFilters[filterType];
                }
            }
        } else {
            if (input.checked) {
                this.currentFilters[filterType] = value;
            } else {
                delete this.currentFilters[filterType];
            }
        }
        
        // Realizar nueva búsqueda con filtros actualizados
        this.performSearch(this.currentQuery, 1);
    }
    
    /**
     * Verificar si un filtro está seleccionado
     */
    isFilterSelected(filterType, value) {
        if (!this.currentFilters[filterType]) {
            return false;
        }
        
        if (Array.isArray(this.currentFilters[filterType])) {
            return this.currentFilters[filterType].includes(value.toString());
        }
        
        return this.currentFilters[filterType].toString() === value.toString();
    }
    
    /**
     * Obtener icono de sugerencia
     */
    getSuggestionIcon(type) {
        const icons = {
            'product': 'fas fa-box',
            'category': 'fas fa-tags',
            'brand': 'fas fa-trademark'
        };
        return icons[type] || 'fas fa-search';
    }
    
    /**
     * Obtener etiqueta de tipo de sugerencia
     */
    getSuggestionTypeLabel(type) {
        const labels = {
            'product': 'Producto',
            'category': 'Categoría',
            'brand': 'Marca'
        };
        return labels[type] || '';
    }
    
    /**
     * Resaltar query en texto
     */
    highlightQuery(text, query) {
        if (!query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    /**
     * Mostrar/ocultar elementos
     */
    showSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'block';
        }
    }
    
    hideSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'none';
        }
    }
    
    showSearchLoading() {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="search-loading">
                    <div class="spinner"></div>
                    <p>Buscando productos...</p>
                </div>
            `;
        }
    }
    
    hideSearchLoading() {
        // Se oculta automáticamente al mostrar resultados
    }
    
    showSearchError(message) {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="search-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    }
    
    /**
     * Cache simple
     */
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this.cacheTTL) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }
    
    /**
     * Cargar estado inicial
     */
    loadInitialState() {
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q');
        
        if (query && this.searchInput) {
            this.searchInput.value = query;
            this.performSearch(query);
        }
    }
    
    /**
     * Tracking de analytics
     */
    trackSearch(query, resultCount) {
        if (window.gtag) {
            gtag('event', 'search', {
                search_term: query,
                result_count: resultCount
            });
        }
    }
    
    // Métodos adicionales para renderizado
    renderNoResults() {
        return `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con otros términos de búsqueda o revisa los filtros aplicados.</p>
            </div>
        `;
    }
    
    renderPagination(pagination) {
        let html = '<div class="search-pagination">';
        
        if (pagination.has_previous) {
            html += `<button class="btn-page" data-page="${pagination.previous_page}">Anterior</button>`;
        }
        
        for (let i = Math.max(1, pagination.current_page - 2); 
             i <= Math.min(pagination.total_pages, pagination.current_page + 2); 
             i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            html += `<button class="btn-page ${activeClass}" data-page="${i}">${i}</button>`;
        }
        
        if (pagination.has_next) {
            html += `<button class="btn-page" data-page="${pagination.next_page}">Siguiente</button>`;
        }
        
        html += '</div>';
        
        // Vincular eventos de paginación
        setTimeout(() => {
            document.querySelectorAll('.btn-page').forEach(btn => {
                btn.addEventListener('click', () => {
                    const page = parseInt(btn.dataset.page);
                    this.performSearch(this.currentQuery, page);
                });
            });
        }, 100);
        
        return html;
    }
    
    renderAnalytics(analytics) {
        if (!analytics.query_analysis) return '';
        
        let html = '<div class="search-analytics">';
        
        if (analytics.query_analysis.detected_brand) {
            html += `<span class="analytics-tag">Marca: ${analytics.query_analysis.detected_brand}</span>`;
        }
        
        if (analytics.query_analysis.detected_category) {
            html += `<span class="analytics-tag">Categoría: ${analytics.query_analysis.detected_category}</span>`;
        }
        
        html += '</div>';
        
        return html;
    }
}

// Inicializar automáticamente
document.addEventListener('DOMContentLoaded', function() {
    if (!window.advancedSearchManager) {
        window.advancedSearchManager = new AdvancedSearchManager();
    }
});

// Exportar para uso global
window.AdvancedSearchManager = AdvancedSearchManager;
