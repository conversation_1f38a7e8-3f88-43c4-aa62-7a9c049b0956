<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Base Model with UTF-8 support
 * All models should extend this class to ensure proper UTF-8 handling
 */
class BaseModel extends Model
{
    protected $DBGroup = 'default';
    
    /**
     * Constructor - Force UTF-8 encoding
     */
    public function __construct()
    {
        parent::__construct();
        
        // Force UTF-8 encoding on database connection
        $this->forceUTF8();
    }
    
    /**
     * Force UTF-8 encoding on database connection
     */
    protected function forceUTF8(): void
    {
        try {
            $db = $this->db();
            
            if ($db->DBDriver === 'MySQLi') {
                $db->query("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
                $db->query("SET CHARACTER SET utf8mb4");
                $db->query("SET character_set_connection=utf8mb4");
                $db->query("SET character_set_client=utf8mb4");
                $db->query("SET character_set_results=utf8mb4");
                $db->query("SET collation_connection=utf8mb4_unicode_ci");
            }
        } catch (\Exception $e) {
            log_message('error', 'Error forcing UTF-8: ' . $e->getMessage());
        }
    }
    
    /**
     * Override find method to ensure UTF-8 encoding
     */
    public function find($id = null)
    {
        $this->forceUTF8();
        return parent::find($id);
    }
    
    /**
     * Override findAll method to ensure UTF-8 encoding
     */
    public function findAll(?int $limit = null, int $offset = 0)
    {
        $this->forceUTF8();
        return parent::findAll($limit, $offset);
    }
    
    /**
     * Override where method to ensure UTF-8 encoding
     */
    public function where($key, $value = null, bool $escape = null)
    {
        $this->forceUTF8();
        return parent::where($key, $value, $escape);
    }
    
    /**
     * Override insert method to ensure UTF-8 encoding
     */
    public function insert($data = null, bool $returnID = true)
    {
        $this->forceUTF8();
        
        // Ensure data is properly encoded
        if (is_array($data)) {
            $data = $this->ensureUTF8Array($data);
        }
        
        return parent::insert($data, $returnID);
    }
    
    /**
     * Override update method to ensure UTF-8 encoding
     */
    public function update($id = null, $data = null): bool
    {
        $this->forceUTF8();
        
        // Ensure data is properly encoded
        if (is_array($data)) {
            $data = $this->ensureUTF8Array($data);
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * Ensure array data is properly UTF-8 encoded
     */
    protected function ensureUTF8Array(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Convert to UTF-8 if not already
                if (!mb_check_encoding($value, 'UTF-8')) {
                    $data[$key] = mb_convert_encoding($value, 'UTF-8', 'auto');
                }
            } elseif (is_array($value)) {
                $data[$key] = $this->ensureUTF8Array($value);
            }
        }
        
        return $data;
    }
    
    /**
     * Execute raw query with UTF-8 support
     */
    public function query(string $sql, $binds = null, bool $setEscapeFlags = true)
    {
        $this->forceUTF8();
        return $this->db()->query($sql, $binds, $setEscapeFlags);
    }
}
