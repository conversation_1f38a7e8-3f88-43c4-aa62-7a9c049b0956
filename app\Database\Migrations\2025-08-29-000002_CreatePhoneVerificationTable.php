<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePhoneVerificationTable extends Migration
{
    public function up()
    {
        // Tabla para verificaciones de teléfono
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'phone' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => false,
                'comment' => 'Número de teléfono a verificar',
            ],
            'verification_code' => [
                'type' => 'VARCHAR',
                'constraint' => 6,
                'null' => false,
                'comment' => 'Código de verificación de 6 dígitos',
            ],
            'is_verified' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '1 = verificado, 0 = pendiente',
            ],
            'attempts' => [
                'type' => 'INT',
                'constraint' => 3,
                'default' => 0,
                'comment' => 'Número de intentos de verificación',
            ],
            'expires_at' => [
                'type' => 'DATETIME',
                'null' => false,
                'comment' => 'Fecha de expiración del código',
            ],
            'verified_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Fecha de verificación exitosa',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('phone');
        $this->forge->addKey('verification_code');
        $this->forge->addKey('expires_at');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('phone_verifications');

        // Agregar foreign key para user_id
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');

        // Agregar columna phone_verified a la tabla users
        $this->forge->addColumn('users', [
            'phone_verified' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'after' => 'phone',
                'comment' => '1 = teléfono verificado, 0 = no verificado',
            ],
            'whatsapp_notifications_enabled' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'after' => 'phone_verified',
                'comment' => '1 = notificaciones habilitadas, 0 = deshabilitadas',
            ],
        ]);
    }

    public function down()
    {
        $this->forge->dropTable('phone_verifications');
        $this->forge->dropColumn('users', ['phone_verified', 'whatsapp_notifications_enabled']);
    }
}
