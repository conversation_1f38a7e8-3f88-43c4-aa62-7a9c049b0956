<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProductReviewModel;

class ReviewsController extends BaseController
{
    protected $reviewModel;

    public function __construct()
    {
        $this->reviewModel = new ProductReviewModel();
    }

    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!session()->get('admin_id') || !session()->get('is_admin_logged_in')) {
            return redirect()->to('/admin/login');
        }
        return true;
    }

    /**
     * Display reviews management page
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        $data = [
            'title' => 'Gestión de Reseñas - Admin MrCell',
            'page' => 'reviews'
        ];

        return view('admin/reviews/index', $data);
    }

    /**
     * Get all reviews for admin (API endpoint)
     */
    public function getAllReviews()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $db = \Config\Database::connect();
            
            $reviews = $db->table('product_reviews pr')
                         ->select('pr.*, p.name as product_name')
                         ->join('products p', 'p.id = pr.product_id', 'left')
                         ->where('pr.deleted_at IS NULL')
                         ->orderBy('pr.created_at', 'DESC')
                         ->get()
                         ->getResultArray();

            // Format reviews
            foreach ($reviews as &$review) {
                $review['rating_stars'] = str_repeat('★', $review['rating']) . str_repeat('☆', 5 - $review['rating']);
                $review['created_at_formatted'] = date('d/m/Y H:i', strtotime($review['created_at']));
                $review['display_name'] = $review['customer_name'];
                
                if ($review['admin_response_date']) {
                    $review['admin_response_date'] = date('d/m/Y H:i', strtotime($review['admin_response_date']));
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting all reviews: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error retrieving reviews'
            ], 500);
        }
    }

    /**
     * Get approved reviews (API endpoint)
     */
    public function getApprovedReviews()
    {
        try {
            $db = \Config\Database::connect();
            
            $reviews = $db->table('product_reviews pr')
                         ->select('pr.*, p.name as product_name')
                         ->join('products p', 'p.id = pr.product_id', 'left')
                         ->where('pr.is_approved', 1)
                         ->where('pr.deleted_at IS NULL')
                         ->orderBy('pr.created_at', 'DESC')
                         ->get()
                         ->getResultArray();

            // Format reviews
            foreach ($reviews as &$review) {
                $review['rating_stars'] = str_repeat('★', $review['rating']) . str_repeat('☆', 5 - $review['rating']);
                $review['created_at_formatted'] = date('d/m/Y H:i', strtotime($review['created_at']));
                $review['display_name'] = $review['customer_name'];
                
                if ($review['admin_response_date']) {
                    $review['admin_response_date'] = date('d/m/Y H:i', strtotime($review['admin_response_date']));
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting approved reviews: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error retrieving approved reviews'
            ], 500);
        }
    }

    /**
     * Get review statistics
     */
    public function getStats()
    {
        try {
            $db = \Config\Database::connect();
            
            // Get counts
            $pendingCount = $db->table('product_reviews')
                              ->where('is_approved', 0)
                              ->where('deleted_at IS NULL')
                              ->countAllResults();
            
            $approvedCount = $db->table('product_reviews')
                               ->where('is_approved', 1)
                               ->where('deleted_at IS NULL')
                               ->countAllResults();
            
            $totalCount = $db->table('product_reviews')
                            ->where('deleted_at IS NULL')
                            ->countAllResults();
            
            // Get average rating
            $avgRating = $db->table('product_reviews')
                           ->selectAvg('rating')
                           ->where('is_approved', 1)
                           ->where('deleted_at IS NULL')
                           ->get()
                           ->getRow();

            $averageRating = $avgRating ? round($avgRating->rating, 1) : 0;

            return $this->response->setJSON([
                'status' => 'success',
                'data' => [
                    'pending_count' => $pendingCount,
                    'approved_count' => $approvedCount,
                    'total_count' => $totalCount,
                    'average_rating' => $averageRating
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting review stats: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error retrieving statistics'
            ], 500);
        }
    }

    /**
     * Approve a review
     */
    public function approve($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Review ID is required'
            ], 400);
        }

        try {
            $db = \Config\Database::connect();
            
            $result = $db->table('product_reviews')
                        ->where('id', $id)
                        ->update(['is_approved' => 1, 'updated_at' => date('Y-m-d H:i:s')]);

            if ($result) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Review approved successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Review not found'
                ], 404);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error approving review: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error approving review'
            ], 500);
        }
    }

    /**
     * Reject a review (soft delete)
     */
    public function reject($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Review ID is required'
            ], 400);
        }

        try {
            $db = \Config\Database::connect();
            
            $result = $db->table('product_reviews')
                        ->where('id', $id)
                        ->update(['deleted_at' => date('Y-m-d H:i:s')]);

            if ($result) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Review rejected successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Review not found'
                ], 404);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error rejecting review: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error rejecting review'
            ], 500);
        }
    }

    /**
     * Add admin response to review
     */
    public function addResponse($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Review ID is required'
            ], 400);
        }

        try {
            $input = $this->request->getJSON(true) ?? $this->request->getPost();
            $response = $input['response'] ?? '';
            
            if (empty($response)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Response is required'
                ], 400);
            }

            $db = \Config\Database::connect();
            
            $result = $db->table('product_reviews')
                        ->where('id', $id)
                        ->update([
                            'admin_response' => $response,
                            'admin_response_date' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

            if ($result) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Response added successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Review not found'
                ], 404);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error adding admin response: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error adding response'
            ], 500);
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Review ID is required'
            ], 400);
        }

        try {
            $db = \Config\Database::connect();
            
            // Get current featured status
            $review = $db->table('product_reviews')
                        ->select('is_featured')
                        ->where('id', $id)
                        ->get()
                        ->getRowArray();

            if (!$review) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Review not found'
                ], 404);
            }

            $newFeaturedStatus = $review['is_featured'] ? 0 : 1;
            
            $result = $db->table('product_reviews')
                        ->where('id', $id)
                        ->update([
                            'is_featured' => $newFeaturedStatus,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

            if ($result) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Featured status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Error updating featured status'
                ], 500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error toggling featured status: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error updating featured status'
            ], 500);
        }
    }
}
