/**
 * 🚀 MrCell Main JavaScript
 * Core functionality for the e-commerce platform
 */

// Global utilities
window.MrCellUtils = {
    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Format currency
    formatCurrency(amount, currency = 'Q') {
        const numericAmount = parseFloat(amount) || 0;
        return `${currency}${numericAmount.toFixed(2)}`;
    },

    // Show notification
    showNotification(message, type = 'info') {
        if (window.mrCell && window.mrCell.notifications) {
            window.mrCell.notifications.open({
                type: type,
                message: message
            });
        } else {
            // Fallback to console
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    },

    // API request helper
    async apiRequest(method, url, data = null) {
        const config = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.MrCellConfig?.csrfToken || ''
            }
        };

        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            config.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, config);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || `HTTP error! status: ${response.status}`);
            }
            
            return result;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }
};

// DOM Ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 MrCell Main JS Loaded');
    
    // Initialize core functionality
    initializeCore();
    initializeSearch();
    initializeCart();
    initializeUI();
});

// Initialize core functionality
function initializeCore() {
    // Back to top button
    const backToTopBtn = document.getElementById('back-to-top');
    if (backToTopBtn) {
        window.addEventListener('scroll', MrCellUtils.throttle(() => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('d-none');
            } else {
                backToTopBtn.classList.add('d-none');
            }
        }, 100));

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Loading overlay
    window.showLoading = function() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('d-none');
        }
    };

    window.hideLoading = function() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    };
}

// Initialize search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const searchSuggestions = document.querySelector('.search-suggestions');
    
    if (searchInput) {
        searchInput.addEventListener('input', MrCellUtils.debounce(async (e) => {
            const query = e.target.value.trim();
            
            if (query.length >= 2) {
                try {
                    const response = await MrCellUtils.apiRequest(
                        'GET', 
                        `${window.MrCellConfig.apiUrl}/search/suggestions?q=${encodeURIComponent(query)}`
                    );
                    
                    if (response.success && response.data.length > 0) {
                        showSearchSuggestions(response.data);
                    } else {
                        hideSearchSuggestions();
                    }
                } catch (error) {
                    console.error('Search suggestions error:', error);
                    hideSearchSuggestions();
                }
            } else {
                hideSearchSuggestions();
            }
        }, 300));

        searchInput.addEventListener('focus', () => {
            if (searchInput.value.trim().length >= 2) {
                searchSuggestions?.classList.remove('d-none');
            }
        });

        searchInput.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => {
                hideSearchSuggestions();
            }, 200);
        });
    }
}

// Show search suggestions
function showSearchSuggestions(suggestions) {
    const searchSuggestions = document.querySelector('.search-suggestions');
    const suggestionsList = document.getElementById('search-suggestions-list');
    
    if (searchSuggestions && suggestionsList) {
        suggestionsList.innerHTML = '';
        
        suggestions.forEach(item => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item d-flex align-items-center p-2 border-bottom';
            suggestionItem.innerHTML = `
                <img src="${item.image || '/assets/img/placeholder.jpg'}" 
                     alt="${item.name}" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                <div>
                    <div class="fw-medium">${item.name}</div>
                    <small class="text-muted">${MrCellUtils.formatCurrency(item.price)}</small>
                </div>
            `;
            
            suggestionItem.addEventListener('click', () => {
                // Codificar el slug para manejar caracteres especiales
                const encodedSlug = encodeURIComponent(item.slug);
                window.location.href = `${window.MrCellConfig.baseUrl}producto/${encodedSlug}`;
            });
            
            suggestionsList.appendChild(suggestionItem);
        });
        
        searchSuggestions.classList.remove('d-none');
    }
}

// Hide search suggestions
function hideSearchSuggestions() {
    const searchSuggestions = document.querySelector('.search-suggestions');
    if (searchSuggestions) {
        searchSuggestions.classList.add('d-none');
    }
}

// Initialize cart functionality
function initializeCart() {
    // Update cart count display
    window.updateCartCount = function(count) {
        const cartCounts = document.querySelectorAll('.cart-count, .cart-count-mobile');
        cartCounts.forEach(element => {
            element.textContent = count;
        });
    };

    // Update cart total display
    window.updateCartTotal = function(total) {
        const cartTotals = document.querySelectorAll('#cart-total');
        cartTotals.forEach(element => {
            element.textContent = MrCellUtils.formatCurrency(total);
        });
    };

    // Add to cart function
    window.addToCart = async function(productId, quantity = 1, options = {}) {
        try {
            window.showLoading();
            
            const response = await MrCellUtils.apiRequest('POST', `${window.MrCellConfig.apiUrl}/cart/add`, {
                product_id: productId,
                quantity: quantity,
                options: options
            });

            if (response.success) {
                window.updateCartCount(response.data.count);
                window.updateCartTotal(response.data.total);

                // Mostrar modal elegante en lugar de notificación
                if (window.showCartModal) {
                    window.showCartModal(
                        response.data.product_name || 'Producto',
                        response.data.count,
                        response.data.total
                    );
                } else {
                    // Fallback a notificación si el modal no está disponible
                    MrCellUtils.showNotification('Producto agregado al carrito', 'success');
                }

                // Animate cart icon
                animateCartIcon();

                return response.data;
            } else {
                throw new Error(response.message || 'Error al agregar producto');
            }
            
        } catch (error) {
            console.error('Error adding to cart:', error);
            MrCellUtils.showNotification(error.message || 'Error al agregar al carrito', 'error');
            throw error;
        } finally {
            window.hideLoading();
        }
    };

    // Remove from cart function
    window.removeFromCart = async function(itemId) {
        try {
            window.showLoading();
            
            const response = await MrCellUtils.apiRequest('DELETE', `${window.MrCellConfig.apiUrl}/cart/remove/${itemId}`);

            if (response.success) {
                window.updateCartCount(response.data.count);
                window.updateCartTotal(response.data.total);
                
                MrCellUtils.showNotification('Producto eliminado del carrito', 'info');
                
                return response.data;
            } else {
                throw new Error(response.message || 'Error al eliminar producto');
            }
            
        } catch (error) {
            console.error('Error removing from cart:', error);
            MrCellUtils.showNotification(error.message || 'Error al eliminar del carrito', 'error');
            throw error;
        } finally {
            window.hideLoading();
        }
    };
}

// Animate cart icon
function animateCartIcon() {
    const cartButtons = document.querySelectorAll('[data-bs-target="#cartOffcanvas"]');
    cartButtons.forEach(button => {
        button.classList.add('animate__animated', 'animate__pulse');
        setTimeout(() => {
            button.classList.remove('animate__animated', 'animate__pulse');
        }, 1000);
    });
}

// Initialize UI components
function initializeUI() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(tooltipTriggerEl => {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(popoverTriggerEl => {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Dark mode toggle
    const darkModeToggle = document.getElementById('mobile-dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('change', function() {
            document.body.classList.toggle('dark-mode', this.checked);
            localStorage.setItem('darkMode', this.checked);
        });

        // Load saved dark mode preference
        const savedDarkMode = localStorage.getItem('darkMode') === 'true';
        if (savedDarkMode) {
            darkModeToggle.checked = true;
            document.body.classList.add('dark-mode');
        }
    }
}

// Wishlist functions - ACTUALIZADAS para nueva API
window.addToWishlist = async function(productId, button = null) {
    try {
        if (button) {
            button.disabled = true;
        }

        const response = await fetch('/api/wishlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_id: productId
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            MrCellUtils.showNotification('Producto agregado a tu lista de deseos', 'success');
            updateWishlistCount(data.data.count);

            // Actualizar botón si se proporciona
            if (button) {
                updateWishlistButton(button, true);
            }
        } else {
            throw new Error(data.message || 'Error al agregar a favoritos');
        }

    } catch (error) {
        console.error('Error adding to wishlist:', error);
        MrCellUtils.showNotification(error.message || 'Error al agregar a favoritos', 'error');
    } finally {
        if (button) {
            button.disabled = false;
        }
    }
};

window.removeFromWishlist = async function(productId, button = null) {
    try {
        if (button) {
            button.disabled = true;
        }

        const response = await fetch(`/api/wishlist/${productId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.status === 'success') {
            MrCellUtils.showNotification('Producto eliminado de tu lista de deseos', 'info');
            updateWishlistCount(data.data.count);

            // Actualizar botón si se proporciona
            if (button) {
                updateWishlistButton(button, false);
            }
        } else {
            throw new Error(data.message || 'Error al eliminar de favoritos');
        }

    } catch (error) {
        console.error('Error removing from wishlist:', error);
        MrCellUtils.showNotification(error.message || 'Error al eliminar de favoritos', 'error');
    } finally {
        if (button) {
            button.disabled = false;
        }
    }
};

// Update wishlist count
function updateWishlistCount(count) {
    const wishlistCounts = document.querySelectorAll('.wishlist-count, .wishlist-count-mobile');
    wishlistCounts.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.classList.remove('d-none');
        } else {
            element.classList.add('d-none');
        }
    });
}

// Update wishlist button appearance
function updateWishlistButton(button, inWishlist) {
    if (!button) return;

    const icon = button.querySelector('i');
    if (icon) {
        if (inWishlist) {
            icon.className = 'fas fa-heart';
            button.classList.add('active');
            button.title = 'Eliminar de lista de deseos';
        } else {
            icon.className = 'far fa-heart';
            button.classList.remove('active');
            button.title = 'Agregar a lista de deseos';
        }
    }
}

// Toggle wishlist (agregar/quitar automáticamente)
window.toggleWishlist = async function(productId, button = null) {
    try {
        if (button) {
            button.disabled = true;
        }

        // Verificar si está en wishlist
        const checkResponse = await fetch(`/api/wishlist/check/${productId}`);
        const checkData = await checkResponse.json();

        if (checkData.data.requires_login) {
            window.location.href = '/login';
            return;
        }

        if (checkData.data.in_wishlist) {
            await window.removeFromWishlist(productId, button);
        } else {
            await window.addToWishlist(productId, button);
        }

    } catch (error) {
        console.error('Error toggling wishlist:', error);
        MrCellUtils.showNotification('Error al procesar solicitud', 'error');
    } finally {
        if (button) {
            button.disabled = false;
        }
    }
};

// Quick view function
window.quickView = function(productId) {
    // This would open a modal with product details
    console.log('Quick view for product:', productId);
    MrCellUtils.showNotification('Vista rápida próximamente', 'info');
};

// Export utilities for other scripts
window.MrCellMain = {
    showLoading: window.showLoading,
    hideLoading: window.hideLoading,
    addToCart: window.addToCart,
    removeFromCart: window.removeFromCart,
    addToWishlist: window.addToWishlist,
    removeFromWishlist: window.removeFromWishlist,
    toggleWishlist: window.toggleWishlist,
    updateCartCount: window.updateCartCount,
    updateCartTotal: window.updateCartTotal,
    updateWishlistCount: updateWishlistCount,
    updateWishlistButton: updateWishlistButton
};
