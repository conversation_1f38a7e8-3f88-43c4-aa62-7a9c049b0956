<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class CategoriesApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Obtener todas las categorías en estructura jerárquica
     * GET /api/categories
     */
    public function index()
    {
        try {
            $includeInactive = $this->request->getGet('include_inactive') === 'true';
            $parentId = $this->request->getGet('parent_id');
            
            // Convertir parent_id a NULL si es 'null' o vacío
            if ($parentId === 'null' || $parentId === '') {
                $parentId = null;
            }

            $query = $this->db->query("CALL sp_get_categories_tree(?, ?)", [
                $parentId, $includeInactive
            ]);

            $categories = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $categories,
                'message' => 'Categorías obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::index: ' . $e->getMessage());
            return $this->failServerError('Error al obtener categorías');
        }
    }

    /**
     * Obtener una categoría específica
     * GET /api/categories/{id}
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de categoría requerido');
            }

            $query = $this->db->query("
                SELECT c.*, 
                       COUNT(sc.id) as children_count,
                       COUNT(p.id) as products_count,
                       pc.name as parent_name
                FROM categories c
                LEFT JOIN categories sc ON sc.parent_id = c.id AND sc.deleted_at IS NULL
                LEFT JOIN products p ON p.category_id = c.id AND p.deleted_at IS NULL
                LEFT JOIN categories pc ON pc.id = c.parent_id AND pc.deleted_at IS NULL
                WHERE c.id = ? AND c.deleted_at IS NULL
                GROUP BY c.id
            ", [$id]);

            $category = $query->getRowArray();

            if (!$category) {
                return $this->failNotFound('Categoría no encontrada');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $category,
                'message' => 'Categoría obtenida correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::show: ' . $e->getMessage());
            return $this->failServerError('Error al obtener categoría');
        }
    }

    /**
     * Crear nueva categoría
     * POST /api/categories
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|max_length[100]',
                'slug' => 'required|max_length[120]|alpha_dash',
                'description' => 'permit_empty|string',
                'parent_id' => 'permit_empty|integer',
                'image' => 'permit_empty|max_length[255]',
                'icon' => 'permit_empty|max_length[100]',
                'sort_order' => 'permit_empty|integer',
                'meta_title' => 'permit_empty|max_length[160]',
                'meta_description' => 'permit_empty|max_length[320]'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para crear categoría
            $query = $this->db->query("CALL sp_create_category(?, ?, ?, ?, ?, ?, ?, ?, ?, @category_id, @result)", [
                $data['name'],
                $data['slug'],
                $data['description'] ?? null,
                $data['parent_id'] ?? null,
                $data['image'] ?? null,
                $data['icon'] ?? null,
                $data['sort_order'] ?? 0,
                $data['meta_title'] ?? null,
                $data['meta_description'] ?? null
            ]);

            // Obtener resultados
            $resultQuery = $this->db->query("SELECT @category_id as category_id, @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'data' => ['id' => $result['category_id']],
                    'message' => 'Categoría creada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::create: ' . $e->getMessage());
            return $this->failServerError('Error al crear categoría');
        }
    }

    /**
     * Actualizar categoría
     * PUT /api/categories/{id}
     */
    public function update($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de categoría requerido');
            }

            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|max_length[100]',
                'slug' => 'required|max_length[120]|alpha_dash',
                'description' => 'permit_empty|string',
                'parent_id' => 'permit_empty|integer',
                'image' => 'permit_empty|max_length[255]',
                'icon' => 'permit_empty|max_length[100]',
                'sort_order' => 'permit_empty|integer',
                'is_active' => 'permit_empty|in_list[0,1]',
                'meta_title' => 'permit_empty|max_length[160]',
                'meta_description' => 'permit_empty|max_length[320]'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para actualizar categoría
            $query = $this->db->query("CALL sp_update_category(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @result)", [
                $id,
                $data['name'],
                $data['slug'],
                $data['description'] ?? null,
                $data['parent_id'] ?? null,
                $data['image'] ?? null,
                $data['icon'] ?? null,
                $data['sort_order'] ?? 0,
                $data['is_active'] ?? 1,
                $data['meta_title'] ?? null,
                $data['meta_description'] ?? null
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Categoría actualizada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::update: ' . $e->getMessage());
            return $this->failServerError('Error al actualizar categoría');
        }
    }

    /**
     * Eliminar categoría (soft delete)
     * DELETE /api/categories/{id}
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de categoría requerido');
            }

            // Llamar SP para eliminar categoría
            $query = $this->db->query("CALL sp_delete_category(?, @result)", [$id]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondDeleted([
                    'status' => 'success',
                    'message' => 'Categoría eliminada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::delete: ' . $e->getMessage());
            return $this->failServerError('Error al eliminar categoría');
        }
    }

    /**
     * Obtener breadcrumb de una categoría
     * GET /api/categories/{id}/breadcrumb
     */
    public function breadcrumb($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de categoría requerido');
            }

            $query = $this->db->query("CALL sp_get_category_breadcrumb(?)", [$id]);
            $breadcrumb = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $breadcrumb,
                'message' => 'Breadcrumb obtenido correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::breadcrumb: ' . $e->getMessage());
            return $this->failServerError('Error al obtener breadcrumb');
        }
    }

    /**
     * Obtener categorías para select/dropdown
     * GET /api/categories/select
     */
    public function select()
    {
        try {
            $query = $this->db->query("
                SELECT id, name, parent_id, 
                       CASE 
                           WHEN parent_id IS NULL THEN name
                           ELSE CONCAT('-- ', name)
                       END as display_name
                FROM categories 
                WHERE deleted_at IS NULL AND is_active = 1
                ORDER BY parent_id IS NULL DESC, sort_order, name
            ");

            $categories = $query->getResultArray();

            return $this->respond([
                'status' => 'success',
                'data' => $categories,
                'message' => 'Categorías para select obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en CategoriesApiSP::select: ' . $e->getMessage());
            return $this->failServerError('Error al obtener categorías para select');
        }
    }
}
