<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\SEOOptimizer;
use App\Libraries\AssetOptimizer;
use App\Libraries\PerformanceOptimizer;

/**
 * Controlador de Optimización
 * Panel de administración para todas las optimizaciones del sistema
 */
class OptimizationController extends BaseController
{
    private $seoOptimizer;
    private $assetOptimizer;
    private $performanceOptimizer;
    
    public function __construct()
    {
        $this->seoOptimizer = new SEOOptimizer();
        $this->assetOptimizer = new AssetOptimizer();
        $this->performanceOptimizer = new PerformanceOptimizer();
    }
    
    /**
     * Dashboard principal de optimización
     */
    public function index()
    {
        $data = [
            'title' => 'Panel de Optimización - MrCell Guatemala',
            'seo_config' => $this->seoOptimizer->getConfig(),
            'asset_config' => $this->assetOptimizer->getConfig(),
            'performance_config' => $this->performanceOptimizer->getConfig(),
            'optimization_stats' => $this->getOptimizationStats()
        ];
        
        return view('admin/optimization/dashboard', $data);
    }
    
    /**
     * Panel de optimización SEO
     */
    public function seo()
    {
        $data = [
            'title' => 'Optimización SEO - MrCell Guatemala',
            'seo_config' => $this->seoOptimizer->getConfig()
        ];
        
        return view('admin/optimization/seo', $data);
    }
    
    /**
     * Generar sitemap XML
     */
    public function generateSitemap()
    {
        try {
            $sitemap = $this->seoOptimizer->generateSitemap();
            
            // Guardar sitemap
            $sitemapPath = FCPATH . 'sitemap.xml';
            file_put_contents($sitemapPath, $sitemap);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Sitemap generado exitosamente',
                'path' => 'sitemap.xml',
                'size' => strlen($sitemap)
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Generar robots.txt
     */
    public function generateRobots()
    {
        try {
            $robotsTxt = $this->seoOptimizer->generateRobotsTxt();
            
            // Guardar robots.txt
            $robotsPath = FCPATH . 'robots.txt';
            file_put_contents($robotsPath, $robotsTxt);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'robots.txt generado exitosamente',
                'path' => 'robots.txt',
                'size' => strlen($robotsTxt)
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Analizar SEO de una página
     */
    public function analyzePage()
    {
        $url = $this->request->getPost('url');
        
        if (empty($url)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'URL requerida'
            ]);
        }
        
        try {
            // Obtener contenido de la página
            $content = file_get_contents($url);
            
            if ($content === false) {
                throw new \Exception('No se pudo obtener el contenido de la página');
            }
            
            $analysis = $this->seoOptimizer->analyzePage($url, $content);
            
            return $this->response->setJSON([
                'success' => true,
                'analysis' => $analysis
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Panel de optimización de assets
     */
    public function assets()
    {
        $data = [
            'title' => 'Optimización de Assets - MrCell Guatemala',
            'asset_config' => $this->assetOptimizer->getConfig(),
            'optimization_stats' => $this->assetOptimizer->getOptimizationStats()
        ];
        
        return view('admin/optimization/assets', $data);
    }
    
    /**
     * Optimizar archivos CSS
     */
    public function optimizeCSS()
    {
        $cssFiles = $this->request->getPost('css_files') ?? [];
        
        if (empty($cssFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos CSS'
            ]);
        }
        
        $results = [];
        
        foreach ($cssFiles as $cssFile) {
            $cssPath = FCPATH . ltrim($cssFile, '/');
            $result = $this->assetOptimizer->optimizeCSS($cssPath);
            $results[] = array_merge($result, ['file' => $cssFile]);
        }
        
        return $this->response->setJSON([
            'success' => true,
            'results' => $results
        ]);
    }
    
    /**
     * Optimizar archivos JavaScript
     */
    public function optimizeJS()
    {
        $jsFiles = $this->request->getPost('js_files') ?? [];
        
        if (empty($jsFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos JavaScript'
            ]);
        }
        
        $results = [];
        
        foreach ($jsFiles as $jsFile) {
            $jsPath = FCPATH . ltrim($jsFile, '/');
            $result = $this->assetOptimizer->optimizeJS($jsPath);
            $results[] = array_merge($result, ['file' => $jsFile]);
        }
        
        return $this->response->setJSON([
            'success' => true,
            'results' => $results
        ]);
    }
    
    /**
     * Optimizar imágenes
     */
    public function optimizeImages()
    {
        $imageFiles = $this->request->getPost('image_files') ?? [];
        
        if (empty($imageFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos de imagen'
            ]);
        }
        
        $results = [];
        
        foreach ($imageFiles as $imageFile) {
            $imagePath = FCPATH . ltrim($imageFile, '/');
            $result = $this->assetOptimizer->optimizeImage($imagePath);
            $results[] = array_merge($result, ['file' => $imageFile]);
        }
        
        return $this->response->setJSON([
            'success' => true,
            'results' => $results
        ]);
    }
    
    /**
     * Combinar archivos CSS
     */
    public function combineCSS()
    {
        $cssFiles = $this->request->getPost('css_files') ?? [];
        $outputFile = $this->request->getPost('output_file') ?? 'assets/css/combined.min.css';
        
        if (empty($cssFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos CSS'
            ]);
        }
        
        // Convertir rutas relativas a absolutas
        $cssPaths = array_map(function($file) {
            return FCPATH . ltrim($file, '/');
        }, $cssFiles);
        
        $outputPath = FCPATH . ltrim($outputFile, '/');
        
        $result = $this->assetOptimizer->combineCSS($cssPaths, $outputPath);
        
        return $this->response->setJSON($result);
    }
    
    /**
     * Combinar archivos JavaScript
     */
    public function combineJS()
    {
        $jsFiles = $this->request->getPost('js_files') ?? [];
        $outputFile = $this->request->getPost('output_file') ?? 'assets/js/combined.min.js';
        
        if (empty($jsFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos JavaScript'
            ]);
        }
        
        // Convertir rutas relativas a absolutas
        $jsPaths = array_map(function($file) {
            return FCPATH . ltrim($file, '/');
        }, $jsFiles);
        
        $outputPath = FCPATH . ltrim($outputFile, '/');
        
        $result = $this->assetOptimizer->combineJS($jsPaths, $outputPath);
        
        return $this->response->setJSON($result);
    }
    
    /**
     * Generar versiones WebP
     */
    public function generateWebP()
    {
        $imageFiles = $this->request->getPost('image_files') ?? [];
        
        if (empty($imageFiles)) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No se especificaron archivos de imagen'
            ]);
        }
        
        $results = [];
        
        foreach ($imageFiles as $imageFile) {
            $imagePath = FCPATH . ltrim($imageFile, '/');
            $result = $this->assetOptimizer->generateWebP($imagePath);
            $results[] = array_merge($result, ['file' => $imageFile]);
        }
        
        return $this->response->setJSON([
            'success' => true,
            'results' => $results
        ]);
    }
    
    /**
     * Limpiar cache de assets
     */
    public function clearAssetCache()
    {
        $result = $this->assetOptimizer->clearCache();
        
        return $this->response->setJSON($result);
    }
    
    /**
     * Panel de optimización de rendimiento
     */
    public function performance()
    {
        $data = [
            'title' => 'Optimización de Rendimiento - MrCell Guatemala',
            'performance_config' => $this->performanceOptimizer->getConfig(),
            'performance_stats' => $this->getPerformanceStats()
        ];
        
        return view('admin/optimization/performance', $data);
    }
    
    /**
     * Ejecutar optimización completa
     */
    public function runFullOptimization()
    {
        try {
            $results = [];
            
            // Optimización de base de datos
            $results['database'] = $this->performanceOptimizer->optimizeDatabase();
            
            // Optimización de cache
            $results['cache'] = $this->performanceOptimizer->optimizeCache();
            
            // Limpieza de archivos
            $results['cleanup'] = $this->performanceOptimizer->cleanupFiles();
            
            // Generar sitemap
            $sitemap = $this->seoOptimizer->generateSitemap();
            file_put_contents(FCPATH . 'sitemap.xml', $sitemap);
            $results['sitemap'] = ['success' => true, 'size' => strlen($sitemap)];
            
            // Generar robots.txt
            $robotsTxt = $this->seoOptimizer->generateRobotsTxt();
            file_put_contents(FCPATH . 'robots.txt', $robotsTxt);
            $results['robots'] = ['success' => true, 'size' => strlen($robotsTxt)];
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Optimización completa ejecutada exitosamente',
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener estadísticas de optimización
     */
    private function getOptimizationStats(): array
    {
        return [
            'seo' => [
                'sitemap_exists' => file_exists(FCPATH . 'sitemap.xml'),
                'robots_exists' => file_exists(FCPATH . 'robots.txt'),
                'sitemap_size' => file_exists(FCPATH . 'sitemap.xml') ? filesize(FCPATH . 'sitemap.xml') : 0,
                'robots_size' => file_exists(FCPATH . 'robots.txt') ? filesize(FCPATH . 'robots.txt') : 0
            ],
            'assets' => $this->assetOptimizer->getOptimizationStats()['stats'] ?? [],
            'performance' => $this->getPerformanceStats()
        ];
    }
    
    /**
     * Obtener estadísticas de rendimiento
     */
    private function getPerformanceStats(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'php_version' => PHP_VERSION,
            'server_load' => sys_getloadavg()[0] ?? 0
        ];
    }
}
