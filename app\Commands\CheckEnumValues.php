<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CheckEnumValues extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'debug:enum-values';
    protected $description = 'Verificar valores del ENUM type';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        try {
            CLI::write('=== VERIFICANDO ENUM VALUES ===', 'yellow');
            CLI::newLine();
            
            // Obtener definición de la columna type
            $result = $db->query('SHOW COLUMNS FROM payment_methods WHERE Field = "type"')->getRowArray();
            
            CLI::write('Definición de la columna type:', 'cyan');
            CLI::write($result['Type'], 'white');
            CLI::newLine();
            
            // Extraer valores del ENUM
            $enumString = $result['Type'];
            preg_match_all("/'([^']+)'/", $enumString, $matches);
            $enumValues = $matches[1];
            
            CLI::write('Valores válidos del ENUM:', 'cyan');
            foreach ($enumValues as $value) {
                CLI::write("- '{$value}'", 'green');
            }
            
            CLI::newLine();
            CLI::write('=== INTENTANDO CON VALORES VÁLIDOS ===', 'yellow');
            
            // Probar con cada valor válido
            foreach ($enumValues as $value) {
                CLI::write("Probando con '{$value}':", 'white');
                
                $updateResult = $db->query("UPDATE payment_methods SET type = ? WHERE slug = 'recurrente'", [$value]);
                CLI::write("Update result: " . ($updateResult ? 'SUCCESS' : 'FAILED'), $updateResult ? 'green' : 'red');
                
                $check = $db->query("SELECT type FROM payment_methods WHERE slug = 'recurrente'")->getRowArray();
                CLI::write("Tipo después: '{$check['type']}'", 'cyan');
                
                if ($check['type'] === $value) {
                    CLI::write("✅ ÉXITO con '{$value}'", 'green');
                    break;
                } else {
                    CLI::write("❌ No funcionó con '{$value}'", 'red');
                }
                CLI::newLine();
            }
            
        } catch (\Exception $e) {
            CLI::error('❌ ERROR: ' . $e->getMessage());
        }
    }
}
