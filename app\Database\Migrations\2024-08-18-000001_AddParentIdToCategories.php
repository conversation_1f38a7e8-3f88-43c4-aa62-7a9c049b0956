<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddParentIdToCategories extends Migration
{
    public function up()
    {
        // Verificar si la columna parent_id ya existe
        if (!$this->db->fieldExists('parent_id', 'categories')) {
            // Agregar columna parent_id
            $this->forge->addColumn('categories', [
                'parent_id' => [
                    'type' => 'INTEGER',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                    'after' => 'description'
                ]
            ]);

            // Agregar foreign key constraint
            $this->forge->addForeignKey('parent_id', 'categories', 'id', 'SET NULL', 'CASCADE');
        }

        // Verificar si la columna uuid ya existe
        if (!$this->db->fieldExists('uuid', 'categories')) {
            $this->forge->addColumn('categories', [
                'uuid' => [
                    'type' => 'VARCHAR',
                    'constraint' => 36,
                    'null' => true,
                    'after' => 'id'
                ]
            ]);
        }

        // Verificar si la columna deleted_at ya existe
        if (!$this->db->fieldExists('deleted_at', 'categories')) {
            $this->forge->addColumn('categories', [
                'deleted_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                    'after' => 'updated_at'
                ]
            ]);
        }

        // Verificar si la columna meta_title ya existe
        if (!$this->db->fieldExists('meta_title', 'categories')) {
            $this->forge->addColumn('categories', [
                'meta_title' => [
                    'type' => 'VARCHAR',
                    'constraint' => 160,
                    'null' => true,
                    'after' => 'is_active'
                ],
                'meta_description' => [
                    'type' => 'VARCHAR',
                    'constraint' => 320,
                    'null' => true,
                    'after' => 'meta_title'
                ],
                'meta_keywords' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                    'after' => 'meta_description'
                ]
            ]);
        }

        // Verificar si la columna image ya existe
        if (!$this->db->fieldExists('image', 'categories')) {
            $this->forge->addColumn('categories', [
                'image' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                    'after' => 'parent_id'
                ]
            ]);
        }

        // Generar UUIDs para categorías existentes que no los tengan
        $this->db->query("UPDATE categories SET uuid = UUID() WHERE uuid IS NULL OR uuid = ''");
    }

    public function down()
    {
        // Eliminar foreign key constraint si existe
        if ($this->db->fieldExists('parent_id', 'categories')) {
            $this->forge->dropForeignKey('categories', 'categories_parent_id_foreign');
            $this->forge->dropColumn('categories', 'parent_id');
        }

        // Eliminar otras columnas agregadas
        $columnsToRemove = ['uuid', 'deleted_at', 'meta_title', 'meta_description', 'meta_keywords', 'image'];
        
        foreach ($columnsToRemove as $column) {
            if ($this->db->fieldExists($column, 'categories')) {
                $this->forge->dropColumn('categories', $column);
            }
        }
    }
}
