#!/bin/bash

# Script de Mantenimiento Automatizado - MrCell Guatemala
# Versión: 2.0.0
# Ejecuta tareas de mantenimiento del sistema

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuración
LOG_FILE="writable/logs/maintenance.log"
MAX_LOG_SIZE=10485760  # 10MB
BACKUP_RETENTION_DAYS=30
CACHE_MAX_AGE_HOURS=24
TEMP_CLEANUP_DAYS=7

# Funciones de logging
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case $level in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Verificar si el script se está ejecutando
check_lock() {
    local lock_file="writable/maintenance.lock"
    
    if [ -f "$lock_file" ]; then
        local pid=$(cat "$lock_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "WARNING" "Mantenimiento ya en ejecución (PID: $pid)"
            exit 1
        else
            rm -f "$lock_file"
        fi
    fi
    
    echo $$ > "$lock_file"
    trap "rm -f $lock_file" EXIT
}

# Limpiar logs antiguos
cleanup_logs() {
    log_message "INFO" "Iniciando limpieza de logs..."
    
    local logs_cleaned=0
    
    # Limpiar logs del sistema
    find writable/logs/ -name "*.log" -type f -mtime +$TEMP_CLEANUP_DAYS -exec rm {} \; 2>/dev/null
    logs_cleaned=$((logs_cleaned + $(find writable/logs/ -name "*.log" -type f -mtime +$TEMP_CLEANUP_DAYS | wc -l)))
    
    # Rotar log actual si es muy grande
    if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt $MAX_LOG_SIZE ]; then
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
        log_message "INFO" "Log rotado por tamaño"
    fi
    
    log_message "SUCCESS" "Limpieza de logs completada ($logs_cleaned archivos eliminados)"
}

# Limpiar archivos temporales
cleanup_temp_files() {
    log_message "INFO" "Iniciando limpieza de archivos temporales..."
    
    local temp_cleaned=0
    
    # Limpiar cache expirado
    if [ -d "writable/cache" ]; then
        find writable/cache/ -type f -mtime +1 -exec rm {} \; 2>/dev/null
        temp_cleaned=$((temp_cleaned + $(find writable/cache/ -type f -mtime +1 | wc -l)))
    fi
    
    # Limpiar archivos de sesión antiguos
    if [ -d "writable/session" ]; then
        find writable/session/ -type f -mtime +1 -exec rm {} \; 2>/dev/null
        temp_cleaned=$((temp_cleaned + $(find writable/session/ -type f -mtime +1 | wc -l)))
    fi
    
    # Limpiar uploads temporales
    if [ -d "writable/uploads/temp" ]; then
        find writable/uploads/temp/ -type f -mtime +1 -exec rm {} \; 2>/dev/null
        temp_cleaned=$((temp_cleaned + $(find writable/uploads/temp/ -type f -mtime +1 | wc -l)))
    fi
    
    log_message "SUCCESS" "Limpieza de archivos temporales completada ($temp_cleaned archivos eliminados)"
}

# Optimizar base de datos
optimize_database() {
    log_message "INFO" "Iniciando optimización de base de datos..."
    
    # Verificar si existe configuración de BD
    if [ ! -f ".env" ]; then
        log_message "ERROR" "Archivo .env no encontrado"
        return 1
    fi
    
    # Extraer configuración
    local db_host=$(grep "database.default.hostname" .env | cut -d'=' -f2 | tr -d ' ')
    local db_name=$(grep "database.default.database" .env | cut -d'=' -f2 | tr -d ' ')
    local db_user=$(grep "database.default.username" .env | cut -d'=' -f2 | tr -d ' ')
    local db_pass=$(grep "database.default.password" .env | cut -d'=' -f2 | tr -d ' ')
    
    if [ -z "$db_host" ] || [ -z "$db_name" ] || [ -z "$db_user" ]; then
        log_message "ERROR" "Configuración de base de datos incompleta"
        return 1
    fi
    
    # Ejecutar optimización
    if command -v mysql >/dev/null 2>&1; then
        local tables_optimized=0
        
        # Obtener lista de tablas
        local tables=$(mysql -h"$db_host" -u"$db_user" -p"$db_pass" -D"$db_name" -e "SHOW TABLES;" 2>/dev/null | tail -n +2)
        
        for table in $tables; do
            mysql -h"$db_host" -u"$db_user" -p"$db_pass" -D"$db_name" -e "OPTIMIZE TABLE $table;" >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                tables_optimized=$((tables_optimized + 1))
            fi
        done
        
        log_message "SUCCESS" "Optimización de base de datos completada ($tables_optimized tablas optimizadas)"
    else
        log_message "WARNING" "Cliente MySQL no disponible para optimización"
    fi
}

# Verificar espacio en disco
check_disk_space() {
    log_message "INFO" "Verificando espacio en disco..."
    
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt 90 ]; then
        log_message "ERROR" "Espacio en disco crítico: ${disk_usage}% usado"
        return 1
    elif [ "$disk_usage" -gt 80 ]; then
        log_message "WARNING" "Espacio en disco alto: ${disk_usage}% usado"
    else
        log_message "SUCCESS" "Espacio en disco OK: ${disk_usage}% usado"
    fi
    
    return 0
}

# Verificar permisos de archivos
check_permissions() {
    log_message "INFO" "Verificando permisos de archivos..."
    
    local permission_errors=0
    
    # Verificar directorios writable
    local writable_dirs=("writable" "writable/logs" "writable/cache" "writable/backups")
    
    for dir in "${writable_dirs[@]}"; do
        if [ -d "$dir" ]; then
            if [ ! -w "$dir" ]; then
                chmod 755 "$dir" 2>/dev/null
                if [ ! -w "$dir" ]; then
                    log_message "ERROR" "No se puede escribir en directorio: $dir"
                    permission_errors=$((permission_errors + 1))
                fi
            fi
        else
            mkdir -p "$dir" 2>/dev/null
            chmod 755 "$dir" 2>/dev/null
        fi
    done
    
    if [ $permission_errors -eq 0 ]; then
        log_message "SUCCESS" "Permisos de archivos verificados"
    else
        log_message "ERROR" "Errores de permisos encontrados: $permission_errors"
    fi
    
    return $permission_errors
}

# Verificar servicios críticos
check_services() {
    log_message "INFO" "Verificando servicios críticos..."
    
    local service_errors=0
    
    # Verificar servidor web
    if command -v curl >/dev/null 2>&1; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost" 2>/dev/null)
        if [ "$response" = "200" ] || [ "$response" = "302" ]; then
            log_message "SUCCESS" "Servidor web respondiendo"
        else
            log_message "WARNING" "Servidor web no responde correctamente (HTTP: $response)"
            service_errors=$((service_errors + 1))
        fi
    fi
    
    # Verificar PHP
    if command -v php >/dev/null 2>&1; then
        php -v >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            log_message "SUCCESS" "PHP funcionando correctamente"
        else
            log_message "ERROR" "PHP no funciona correctamente"
            service_errors=$((service_errors + 1))
        fi
    fi
    
    return $service_errors
}

# Generar reporte de estado
generate_status_report() {
    log_message "INFO" "Generando reporte de estado..."
    
    local report_file="writable/logs/system_status_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "REPORTE DE ESTADO DEL SISTEMA - MRCELL GUATEMALA"
        echo "================================================"
        echo "Fecha: $(date)"
        echo "Servidor: $(hostname)"
        echo ""
        
        echo "INFORMACIÓN DEL SISTEMA:"
        echo "- OS: $(uname -s) $(uname -r)"
        echo "- PHP: $(php -v | head -1)"
        echo "- Memoria PHP: $(php -r "echo ini_get('memory_limit');")"
        echo "- Espacio disco: $(df -h . | tail -1)"
        echo ""
        
        echo "ESTADO DE DIRECTORIOS:"
        for dir in writable writable/logs writable/cache writable/backups; do
            if [ -d "$dir" ]; then
                local size=$(du -sh "$dir" 2>/dev/null | cut -f1)
                local perms=$(ls -ld "$dir" | cut -d' ' -f1)
                echo "- $dir: $size ($perms)"
            else
                echo "- $dir: NO EXISTE"
            fi
        done
        echo ""
        
        echo "ARCHIVOS DE LOG RECIENTES:"
        find writable/logs/ -name "*.log" -type f -mtime -1 2>/dev/null | head -10
        echo ""
        
        echo "PROCESOS PHP ACTIVOS:"
        ps aux | grep php | grep -v grep | wc -l
        echo ""
        
    } > "$report_file"
    
    log_message "SUCCESS" "Reporte generado: $report_file"
}

# Función principal
main() {
    echo "🔧 MANTENIMIENTO AUTOMATIZADO - MRCELL GUATEMALA"
    echo "================================================"
    echo ""
    
    # Verificar lock
    check_lock
    
    log_message "INFO" "Iniciando mantenimiento automatizado..."
    
    local start_time=$(date +%s)
    local errors=0
    
    # Ejecutar tareas de mantenimiento
    cleanup_logs || errors=$((errors + 1))
    cleanup_temp_files || errors=$((errors + 1))
    optimize_database || errors=$((errors + 1))
    check_disk_space || errors=$((errors + 1))
    check_permissions || errors=$((errors + 1))
    check_services || errors=$((errors + 1))
    generate_status_report || errors=$((errors + 1))
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    log_message "INFO" "Mantenimiento completado en ${duration}s con $errors errores"
    
    if [ $errors -eq 0 ]; then
        log_message "SUCCESS" "🎉 Mantenimiento exitoso - Sistema optimizado"
    else
        log_message "WARNING" "⚠️  Mantenimiento completado con $errors errores"
    fi
    
    return $errors
}

# Verificar argumentos
case "${1:-}" in
    "logs")
        cleanup_logs
        ;;
    "temp")
        cleanup_temp_files
        ;;
    "database")
        optimize_database
        ;;
    "permissions")
        check_permissions
        ;;
    "status")
        generate_status_report
        ;;
    "help"|"-h"|"--help")
        echo "Uso: $0 [opción]"
        echo ""
        echo "Opciones:"
        echo "  logs        - Limpiar solo logs"
        echo "  temp        - Limpiar solo archivos temporales"
        echo "  database    - Optimizar solo base de datos"
        echo "  permissions - Verificar solo permisos"
        echo "  status      - Generar solo reporte de estado"
        echo "  help        - Mostrar esta ayuda"
        echo ""
        echo "Sin argumentos ejecuta mantenimiento completo"
        ;;
    *)
        main
        ;;
esac
