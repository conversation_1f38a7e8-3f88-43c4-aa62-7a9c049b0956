<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Pedido Confirmado - MrCell Guatemala<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    :root {
        --primary-color: #dc2626;
        --success-color: #28a745;
    }

    .confirmation-container {
        max-width: 600px;
        margin: 0 auto;
        text-align: center;
        padding: 3rem 1rem;
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background-color: var(--success-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    .order-id {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .next-steps {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .contact-info {
        background-color: #e7f3ff;
        border: 1px solid #007bff;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: #b91c1c;
        border-color: #b91c1c;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container my-5">
    <div class="confirmation-container">
        <!-- Icono de éxito -->
        <div class="success-icon">
            <i class="fas fa-check fa-2x text-white"></i>
        </div>

        <!-- Mensaje principal -->
        <h1 class="mb-3">¡Pedido Confirmado!</h1>
        <p class="lead text-muted mb-4">
            Tu pedido ha sido recibido y está siendo procesado.
        </p>

        <!-- Número de pedido -->
        <div class="order-id">
            Pedido #<?= esc($order_id) ?>
        </div>

        <p class="text-muted">
            Guarda este número para futuras referencias.
        </p>

        <!-- Próximos pasos -->
        <div class="next-steps">
            <h5 class="mb-3"><i class="fas fa-list-ol me-2"></i>¿Qué sigue?</h5>
            
            <div class="step-item">
                <div class="step-number">1</div>
                <div>
                    <strong>Confirmación por WhatsApp</strong>
                    <p class="mb-0 text-muted">
                        Te contactaremos por WhatsApp para confirmar los detalles de tu pedido y coordinar la entrega.
                    </p>
                </div>
            </div>

            <div class="step-item">
                <div class="step-number">2</div>
                <div>
                    <strong>Preparación del pedido</strong>
                    <p class="mb-0 text-muted">
                        Nuestro equipo preparará cuidadosamente todos los productos de tu pedido.
                    </p>
                </div>
            </div>

            <div class="step-item">
                <div class="step-number">3</div>
                <div>
                    <strong>Entrega o retiro</strong>
                    <p class="mb-0 text-muted">
                        Según el método seleccionado, te entregaremos el pedido en tu dirección o podrás retirarlo en nuestra tienda.
                    </p>
                </div>
            </div>
        </div>

        <!-- Información de contacto -->
        <div class="contact-info">
            <h6><i class="fas fa-headset me-2"></i>¿Necesitas ayuda?</h6>
            <p class="mb-2">
                <strong>WhatsApp:</strong> 
                <a href="https://wa.me/50212345678" target="_blank" class="text-decoration-none">
                    <i class="fab fa-whatsapp me-1"></i>+502 1234-5678
                </a>
            </p>
            <p class="mb-2">
                <strong>Teléfono:</strong> 
                <a href="tel:+50212345678" class="text-decoration-none">
                    <i class="fas fa-phone me-1"></i>+502 1234-5678
                </a>
            </p>
            <p class="mb-0">
                <strong>Email:</strong> 
                <a href="mailto:<EMAIL>" class="text-decoration-none">
                    <i class="fas fa-envelope me-1"></i><EMAIL>
                </a>
            </p>
        </div>

        <!-- Botones de acción -->
        <div class="d-grid gap-2 d-md-block">
            <a href="/" class="btn btn-primary btn-lg">
                <i class="fas fa-home me-2"></i>Volver al Inicio
            </a>
            <a href="/productos" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-shopping-bag me-2"></i>Seguir Comprando
            </a>
        </div>

        <!-- Información adicional -->
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Recibirás un mensaje de confirmación por WhatsApp en los próximos minutos.
                Si no recibes nuestro mensaje, por favor contáctanos directamente.
            </small>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Opcional: Enviar evento de conversión para analytics
    document.addEventListener('DOMContentLoaded', function() {
        // Google Analytics, Facebook Pixel, etc.
        if (typeof gtag !== 'undefined') {
            gtag('event', 'purchase', {
                'transaction_id': '<?= esc($order_id) ?>',
                'currency': 'GTQ'
            });
        }
        
        // Mostrar notificación de éxito
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('¡Pedido confirmado!', {
                body: 'Tu pedido #<?= esc($order_id) ?> ha sido recibido correctamente.',
                icon: '/assets/images/logo-small.png'
            });
        }
    });
</script>
<?= $this->endSection() ?>
