<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAddressNameField extends Migration
{
    public function up()
    {
        // Agregar campo para el nombre personalizado de la dirección
        $this->forge->addColumn('user_addresses', [
            'address_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'type',
                'comment' => 'Nombre personalizado de la dirección (Casa, Oficina, etc.)'
            ]
        ]);
    }

    public function down()
    {
        // Eliminar el campo si se revierte la migración
        $this->forge->dropColumn('user_addresses', 'address_name');
    }
}
