<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .order-detail-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .order-status {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.875rem;
    }

    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-processing { background-color: #cce5ff; color: #004085; }
    .status-shipped { background-color: #d4edda; color: #155724; }
    .status-delivered { background-color: #d1ecf1; color: #0c5460; }
    .status-cancelled { background-color: #f8d7da; color: #721c24; }

    .timeline {
        position: relative;
        padding-left: 2rem;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -0.5rem;
        top: 0.25rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: #007bff;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }

    .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 0.375rem;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container my-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('cuenta/pedidos') ?>">Mis Pedidos</a></li>
            <li class="breadcrumb-item active" aria-current="page">Pedido #<?= $order['order_number'] ?? $order['id'] ?></li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Información del Pedido -->
            <div class="card order-detail-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        Pedido #<?= $order['order_number'] ?? $order['id'] ?>
                    </h5>
                    <span class="order-status status-<?= strtolower($order['status']) ?>">
                        <?= ucfirst($order['status']) ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Fecha del Pedido:</strong> <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                            <p><strong>Total:</strong> Q<?= number_format($order['total_amount'], 2) ?></p>
                            <p><strong>Método de Pago:</strong> <?= ucfirst($order['payment_method'] ?? 'No especificado') ?></p>
                        </div>
                        <div class="col-md-6">
                            <?php if (!empty($order['address_line_1'])): ?>
                                <p><strong>Dirección de Envío:</strong></p>
                                <address class="mb-0">
                                    <?= $order['address_line_1'] ?><br>
                                    <?php if (!empty($order['address_line_2'])): ?>
                                        <?= $order['address_line_2'] ?><br>
                                    <?php endif; ?>
                                    <?= $order['city'] ?>, <?= $order['state'] ?> <?= $order['postal_code'] ?><br>
                                    <?= $order['country'] ?>
                                </address>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Productos del Pedido -->
            <div class="card order-detail-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Productos (<?= count($order_items) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($order_items)): ?>
                        <?php foreach ($order_items as $item): ?>
                            <div class="row align-items-center border-bottom py-3">
                                <div class="col-md-2">
                                    <?php if (!empty($item['featured_image'])): ?>
                                        <img src="<?= base_url('uploads/products/' . $item['featured_image']) ?>" 
                                             alt="<?= esc($item['name']) ?>" class="product-image">
                                    <?php else: ?>
                                        <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="mb-1"><?= esc($item['name']) ?></h6>
                                    <?php if (!empty($item['variant_name'])): ?>
                                        <small class="text-muted">Variante: <?= esc($item['variant_name']) ?></small><br>
                                    <?php endif; ?>
                                    <?php if (!empty($item['sku'])): ?>
                                        <small class="text-muted">SKU: <?= esc($item['sku']) ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="fw-bold">x<?= $item['quantity'] ?></span>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="fw-bold">Q<?= number_format($item['price'] * $item['quantity'], 2) ?></div>
                                    <small class="text-muted">Q<?= number_format($item['price'], 2) ?> c/u</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted mb-0">No se encontraron productos para este pedido.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Resumen del Pedido -->
            <div class="card order-detail-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Resumen del Pedido
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>Q<?= number_format($order['subtotal'] ?? $order['total_amount'], 2) ?></span>
                    </div>
                    <?php if (!empty($order['shipping_cost']) && $order['shipping_cost'] > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Envío:</span>
                            <span>Q<?= number_format($order['shipping_cost'], 2) ?></span>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($order['tax_amount']) && $order['tax_amount'] > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Impuestos:</span>
                            <span>Q<?= number_format($order['tax_amount'], 2) ?></span>
                        </div>
                    <?php endif; ?>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span>Q<?= number_format($order['total_amount'], 2) ?></span>
                    </div>
                </div>
            </div>

            <!-- Historial del Pedido -->
            <div class="card order-detail-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Historial del Pedido
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php if (!empty($status_history)): ?>
                            <?php foreach ($status_history as $history): ?>
                                <div class="timeline-item">
                                    <div class="fw-bold"><?= ucfirst($history['status']) ?></div>
                                    <small class="text-muted">
                                        <?= date('d/m/Y H:i', strtotime($history['created_at'])) ?>
                                    </small>
                                    <?php if (!empty($history['notes'])): ?>
                                        <p class="mb-0 mt-1"><?= esc($history['notes']) ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="timeline-item">
                                <div class="fw-bold"><?= ucfirst($order['status']) ?></div>
                                <small class="text-muted">
                                    <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                </small>
                                <p class="mb-0 mt-1">Pedido creado</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Acciones -->
            <div class="card order-detail-card">
                <div class="card-body text-center">
                    <a href="<?= base_url('cuenta/pedidos') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Volver a Mis Pedidos
                    </a>
                    <?php if (in_array(strtolower($order['status']), ['pending', 'processing'])): ?>
                        <button class="btn btn-outline-danger ms-2" onclick="cancelOrder(<?= $order['id'] ?>)">
                            <i class="fas fa-times me-2"></i>Cancelar Pedido
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function cancelOrder(orderId) {
    if (confirm('¿Estás seguro de que deseas cancelar este pedido?')) {
        // Aquí iría la lógica para cancelar el pedido
        alert('Funcionalidad de cancelación pendiente de implementar');
    }
}
</script>
<?= $this->endSection() ?>
