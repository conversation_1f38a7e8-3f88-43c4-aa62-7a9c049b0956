<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    /* Categories Page Styles */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: var(--white-color);
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .category-card {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        text-decoration: none;
        color: inherit;
        height: 100%;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        text-decoration: none;
        color: inherit;
    }

    .category-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        background: var(--gray-100);
    }

    .category-content {
        padding: 1.5rem;
    }

    .category-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--dark-color);
    }

    .category-description {
        color: var(--gray-600);
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .product-count {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .empty-categories {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--gray-600);
    }

    .empty-categories i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: var(--gray-400);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-th-large me-2"></i>Categorías</h1>
                <p class="mb-0 mt-2 opacity-75">Explora nuestras categorías de productos</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Categories Content -->
<div class="container my-5">
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= esc($error_message) ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($categories)): ?>
        <div class="row g-4">
            <?php foreach ($categories as $category): ?>
                <div class="col-md-6 col-lg-4">
                    <a href="<?= base_url('tienda?category=' . $category['id']) ?>" class="category-card d-block">
                        <img src="<?= $category['image'] ?: 'https://via.placeholder.com/300x200/dc2626/ffffff?text=' . urlencode($category['name']) ?>"
                             alt="<?= esc($category['name']) ?>"
                             class="category-image"
                             onerror="this.src='https://via.placeholder.com/300x200/dc2626/ffffff?text=<?= urlencode($category['name']) ?>'">
                        
                        <div class="category-content">
                            <h3 class="category-title"><?= esc($category['name']) ?></h3>
                            
                            <?php if (!empty($category['description'])): ?>
                                <p class="category-description"><?= esc($category['description']) ?></p>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="product-count">
                                    <?= $category['product_count'] ?> productos
                                </span>
                                <i class="fas fa-arrow-right text-primary"></i>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="empty-categories">
            <i class="fas fa-th-large"></i>
            <h3>No hay categorías disponibles</h3>
            <p class="text-muted mb-4">Actualmente no tenemos categorías configuradas.</p>
            <a href="<?= base_url('tienda') ?>" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Ver Todos los Productos
            </a>
        </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Categories page functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects or additional functionality if needed
        console.log('Categories page loaded');
    });
</script>
<?= $this->endSection() ?>
