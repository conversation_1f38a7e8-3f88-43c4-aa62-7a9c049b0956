<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-user-plus me-2"></i>Crear <PERSON></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/admin/users">Usuarios</a></li>
                    <li class="breadcrumb-item active">Crear <PERSON>ua<PERSON></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/admin/users" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver
            </a>
        </div>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Información del Usuario</h5>
            </div>
            <div class="card-body">
                <form action="/admin/users/create" method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= old('name') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nombre de Usuario</label>
                                <input type="text" class="form-control" id="username" name="username" value="<?= old('username') ?>">
                                <div class="form-text">Se generará automáticamente si se deja vacío</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= old('email') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Teléfono <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= old('phone') ?>" placeholder="+50212345678" required>
                                <div class="form-text">Formato: +502XXXXXXXX (incluir código de país)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Contraseña <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Mínimo 6 caracteres</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Espacio para futuras opciones -->
                        </div>
                    </div>

                    
                    <div class="mb-3">
                        <label class="form-label">Roles</label>
                        <div class="row">
                            <?php if (!empty($roles)): ?>
                                <?php foreach ($roles as $role): ?>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="roles[]" value="<?= $role['id'] ?>" id="role_<?= $role['id'] ?>" <?= in_array($role['id'], old('roles') ?? []) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="role_<?= $role['id'] ?>">
                                                <?= esc($role['name']) ?>
                                                <?php if ($role['description']): ?>
                                                    <small class="text-muted d-block"><?= esc($role['description']) ?></small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <p class="text-muted">No hay roles disponibles</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Usuario
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información</h5>
            </div>
            <div class="card-body">
                <h6>Campos Requeridos</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Nombre completo</li>
                    <li><i class="fas fa-check text-success me-2"></i>Email válido</li>
                    <li><i class="fas fa-check text-success me-2"></i>Contraseña (mín. 6 caracteres)</li>
                </ul>
                
                <hr>
                
                <h6>Roles Disponibles</h6>
                <ul class="list-unstyled">
                    <?php if (!empty($roles)): ?>
                        <?php foreach ($roles as $role): ?>
                            <li class="mb-1">
                                <strong><?= esc($role['name']) ?></strong>
                                <?php if ($role['description']): ?>
                                    <br><small class="text-muted"><?= esc($role['description']) ?></small>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li class="text-muted">No hay roles configurados</li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación del formulario
    const form = document.querySelector('form');
    const passwordInput = document.getElementById('password');
    
    form.addEventListener('submit', function(e) {
        if (passwordInput.value.length < 6) {
            e.preventDefault();
            alert('La contraseña debe tener al menos 6 caracteres');
            passwordInput.focus();
        }
    });
});
</script>
<?= $this->endSection() ?>
