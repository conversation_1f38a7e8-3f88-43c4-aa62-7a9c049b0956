<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductModel extends Model
{
    protected $table            = 'products';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'vendor_id',
        'uuid',
        'recurrente_product_id',
        'recurrente_synced_at',
        'recurrente_sync_status',
        'sku',
        'name',
        'slug',
        'description',
        'short_description',
        'category_id',
        'brand_id',
        'price_regular',
        'price_sale',
        'price_wholesale',
        'cost_price',
        'currency',
        'stock_quantity',
        'stock_min',
        'stock_status',
        'weight',
        'dimensions',
        'dimension_length',
        'dimension_width',
        'dimension_height',
        'dimension_unit',
        'featured_image',
        'gallery_images',
        'is_active',
        'is_featured',
        'has_expiration',
        'expiration_date',
        'expiration_alert_days',
        'is_digital',
        'tags',
        'attributes',
        'meta_title',
        'meta_description',
        'recurrente_product_id',
        'recurrente_sync_status',
        'recurrente_synced_at',
        'recurrente_storefront_link',
        'meta_keywords'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Callbacks
    protected $afterInsert = ['syncWithRecurrente'];
    protected $afterUpdate = ['syncWithRecurrenteOnUpdate'];
    protected $beforeDelete = ['deleteFromRecurrente'];

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[255]',
        'sku' => 'required|min_length[2]|max_length[100]|is_unique[products.sku,id,{id}]',
        'price_regular' => 'required|decimal|greater_than[0]',
        'category_id' => 'required|integer',
        'currency' => 'permit_empty|in_list[GTQ,USD]',
        'stock_quantity' => 'permit_empty|integer|greater_than_equal_to[0]',
        'weight' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'dimension_length' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'dimension_width' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'dimension_height' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'dimension_unit' => 'permit_empty|in_list[cm,in,mm]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'El nombre del producto es requerido',
            'min_length' => 'El nombre debe tener al menos 2 caracteres',
            'max_length' => 'El nombre no puede exceder 255 caracteres'
        ],
        'sku' => [
            'required' => 'El SKU es requerido',
            'is_unique' => 'Este SKU ya está en uso'
        ],
        'price_regular' => [
            'required' => 'El precio es requerido',
            'decimal' => 'El precio debe ser un número decimal válido',
            'greater_than' => 'El precio debe ser mayor a 0'
        ],
        'currency' => [
            'in_list' => 'La moneda debe ser GTQ o USD'
        ],
        'weight' => [
            'decimal' => 'El peso debe ser un número decimal válido',
            'greater_than_equal_to' => 'El peso debe ser mayor o igual a 0'
        ],
        'dimension_length' => [
            'decimal' => 'El largo debe ser un número decimal válido',
            'greater_than_equal_to' => 'El largo debe ser mayor o igual a 0'
        ],
        'dimension_width' => [
            'decimal' => 'El ancho debe ser un número decimal válido',
            'greater_than_equal_to' => 'El ancho debe ser mayor o igual a 0'
        ],
        'dimension_height' => [
            'decimal' => 'El alto debe ser un número decimal válido',
            'greater_than_equal_to' => 'El alto debe ser mayor o igual a 0'
        ],
        'dimension_unit' => [
            'in_list' => 'La unidad de medida debe ser cm, in o mm'
        ]
    ];

    protected $skipValidation = false;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateSlug', 'generateUUID'];
    protected $beforeUpdate   = ['generateSlug'];

    /**
     * Generate slug from name if not provided
     */
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            helper('slug');
            $excludeId = isset($data['id']) ? $data['id'] : null;
            $data['data']['slug'] = generate_unique_product_slug($data['data']['name'], $excludeId);
        }
        return $data;
    }

    /**
     * Generate UUID if not provided
     */
    protected function generateUUID(array $data)
    {
        if (empty($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->generateUniqueUUID();
        }
        return $data;
    }

    /**
     * Get products with category information
     */
    public function getProductsWithCategory($limit = 20, $offset = 0, $filters = [])
    {
        $builder = $this->builder();
        
        $builder->select('products.*, categories.name as category_name, categories.slug as category_slug')
               ->join('categories', 'categories.id = products.category_id', 'left')
               ->where('products.is_active', 1);

        // Apply filters
        if (isset($filters['category_id'])) {
            $builder->where('products.category_id', $filters['category_id']);
        }

        if (isset($filters['is_featured'])) {
            $builder->where('products.is_featured', $filters['is_featured']);
        }

        if (isset($filters['search'])) {
            $builder->groupStart()
                   ->like('products.name', $filters['search'])
                   ->orLike('products.short_description', $filters['search'])
                   ->orLike('products.sku', $filters['search'])
                   ->groupEnd();
        }

        if (isset($filters['min_price'])) {
            $builder->where('products.price_regular >=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $builder->where('products.price_regular <=', $filters['max_price']);
        }

        // Sorting
        $sort = $filters['sort'] ?? 'created_at';
        $order = $filters['order'] ?? 'DESC';
        
        $validSorts = ['name', 'price_regular', 'created_at', 'is_featured'];
        if (in_array($sort, $validSorts)) {
            $builder->orderBy('products.' . $sort, $order);
        } else {
            $builder->orderBy('products.is_featured', 'DESC')
                   ->orderBy('products.created_at', 'DESC');
        }

        return $builder->limit($limit, $offset)->get()->getResultArray();
    }

    /**
     * Get featured products
     */
    public function getFeaturedProducts($limit = 8)
    {
        return $this->getProductsWithCategory($limit, 0, ['is_featured' => 1]);
    }

    /**
     * Get product by ID with category
     */
    public function getProductWithCategory($id)
    {
        return $this->builder()
                   ->select('products.*, categories.name as category_name, categories.slug as category_slug')
                   ->join('categories', 'categories.id = products.category_id', 'left')
                   ->where('products.id', $id)
                   ->where('products.is_active', 1)
                   ->get()
                   ->getRowArray();
    }

    /**
     * Get related products by category
     */
    public function getRelatedProducts($categoryId, $excludeId, $limit = 4)
    {
        return $this->builder()
                   ->select('products.*, categories.name as category_name')
                   ->join('categories', 'categories.id = products.category_id', 'left')
                   ->where('products.category_id', $categoryId)
                   ->where('products.id !=', $excludeId)
                   ->where('products.is_active', 1)
                   ->orderBy('products.is_featured', 'DESC')
                   ->orderBy('products.created_at', 'DESC')
                   ->limit($limit)
                   ->get()
                   ->getResultArray();
    }

    /**
     * Search products
     */
    public function searchProducts($query, $filters = [], $limit = 20, $offset = 0)
    {
        $builder = $this->builder();
        
        $builder->select('products.*, categories.name as category_name, categories.slug as category_slug')
               ->join('categories', 'categories.id = products.category_id', 'left')
               ->where('products.is_active', 1);

        // Search query
        $builder->groupStart()
               ->like('products.name', $query)
               ->orLike('products.short_description', $query)
               ->orLike('products.description', $query)
               ->orLike('products.sku', $query)
               ->groupEnd();

        // Apply additional filters
        if (isset($filters['category_id'])) {
            $builder->where('products.category_id', $filters['category_id']);
        }

        if (isset($filters['min_price'])) {
            $builder->where('products.price_regular >=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $builder->where('products.price_regular <=', $filters['max_price']);
        }

        return $builder->orderBy('products.is_featured', 'DESC')
                      ->orderBy('products.name', 'ASC')
                      ->limit($limit, $offset)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Update stock quantity
     */
    public function updateStock($productId, $quantity)
    {
        return $this->update($productId, ['stock_quantity' => $quantity]);
    }

    /**
     * Decrease stock (for orders)
     */
    public function decreaseStock($productId, $quantity)
    {
        $product = $this->find($productId);
        if ($product && $product['stock_quantity'] >= $quantity) {
            $newStock = $product['stock_quantity'] - $quantity;
            return $this->update($productId, ['stock_quantity' => $newStock]);
        }
        return false;
    }

    /**
     * Get low stock products
     */
    public function getLowStockProducts()
    {
        return $this->where('stock_quantity <=', 'stock_min', false)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Generate unique UUID
     */
    private function generateUniqueUUID()
    {
        do {
            $uuid = sprintf(
                '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            );
        } while ($this->where('uuid', $uuid)->first());

        return $uuid;
    }

    /**
     * Get product statistics
     */
    public function getProductStats()
    {
        $stats = [
            'total_products' => $this->countAllResults(),
            'active_products' => $this->where('is_active', 1)->countAllResults(),
            'featured_products' => $this->where('is_featured', 1)->countAllResults(),
            'low_stock_products' => count($this->getLowStockProducts()),
            'out_of_stock' => $this->where('stock_quantity', 0)->countAllResults(),
            'products_gtq' => $this->where('currency', 'GTQ')->countAllResults(),
            'products_usd' => $this->where('currency', 'USD')->countAllResults()
        ];

        return $stats;
    }

    /**
     * Convert price between currencies
     */
    public function convertPrice($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $settingModel = new \App\Models\SettingModel();
        $exchangeRate = floatval($settingModel->getSetting('exchange_rate_usd_to_gtq', 7.75));

        if ($fromCurrency === 'USD' && $toCurrency === 'GTQ') {
            return round($amount * $exchangeRate, 2);
        } elseif ($fromCurrency === 'GTQ' && $toCurrency === 'USD') {
            return round($amount / $exchangeRate, 2);
        }

        return $amount;
    }

    /**
     * Get product with price conversions
     */
    public function getProductWithConversions($id)
    {
        $product = $this->getProductWithCategory($id);

        if (!$product) {
            return null;
        }

        $settingModel = new \App\Models\SettingModel();
        $showConversion = $settingModel->getSetting('show_currency_conversion', '1') === '1';

        if ($showConversion) {
            $product['price_regular_gtq'] = $product['currency'] === 'GTQ'
                ? $product['price_regular']
                : $this->convertPrice($product['price_regular'], 'USD', 'GTQ');

            $product['price_regular_usd'] = $product['currency'] === 'USD'
                ? $product['price_regular']
                : $this->convertPrice($product['price_regular'], 'GTQ', 'USD');

            if (!empty($product['price_sale'])) {
                $product['price_sale_gtq'] = $product['currency'] === 'GTQ'
                    ? $product['price_sale']
                    : $this->convertPrice($product['price_sale'], 'USD', 'GTQ');

                $product['price_sale_usd'] = $product['currency'] === 'USD'
                    ? $product['price_sale']
                    : $this->convertPrice($product['price_sale'], 'GTQ', 'USD');
            }
        }

        return $product;
    }

    /**
     * Callback: Sincronizar con Recurrente después de insertar
     */
    protected function syncWithRecurrente(array $data)
    {
        if (!isset($data['id'])) {
            return $data;
        }

        try {
            // Solo sincronizar si el producto está activo
            $productId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            $product = $this->find($productId);

            if ($product && $product['is_active'] == 1) {
                $this->syncProductWithRecurrente($productId);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error sincronizando producto con Recurrente después de insertar: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * Callback: Sincronizar con Recurrente después de actualizar
     */
    protected function syncWithRecurrenteOnUpdate(array $data)
    {
        if (!isset($data['id'])) {
            return $data;
        }

        try {
            $productId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            $product = $this->find($productId);

            // Solo sincronizar si el producto está activo y hay cambios relevantes
            if ($product && $product['is_active'] == 1) {
                // Verificar si hay cambios en campos relevantes para Recurrente
                $relevantFields = ['name', 'description', 'short_description', 'price_regular', 'price_sale', 'currency', 'featured_image'];
                $hasRelevantChanges = false;

                foreach ($relevantFields as $field) {
                    if (isset($data['data'][$field])) {
                        $hasRelevantChanges = true;
                        break;
                    }
                }

                if ($hasRelevantChanges || empty($product['recurrente_product_id'])) {
                    $this->syncProductWithRecurrente($productId);
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Error sincronizando producto con Recurrente después de actualizar: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * Sincronizar un producto específico con Recurrente
     */
    private function syncProductWithRecurrente($productId)
    {
        try {
            // Cargar el servicio de sincronización
            $productSyncService = new \App\Services\ProductSyncService();

            // Ejecutar sincronización en segundo plano para no bloquear la operación principal
            $this->executeInBackground(function() use ($productSyncService, $productId) {
                $productSyncService->syncSingleProduct($productId);
            });

        } catch (\Exception $e) {
            log_message('error', 'Error en syncProductWithRecurrente: ' . $e->getMessage());
        }
    }

    /**
     * Callback: Eliminar de Recurrente antes de eliminar de la base de datos local
     */
    protected function deleteFromRecurrente(array $data)
    {
        if (!isset($data['id'])) {
            return $data;
        }

        try {
            $productId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            $product = $this->find($productId);

            // Solo intentar eliminar si el producto tiene ID de Recurrente
            if ($product && !empty($product['recurrente_product_id'])) {
                $this->deleteProductFromRecurrente($product['recurrente_product_id'], $productId);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error eliminando producto de Recurrente antes de eliminar localmente: ' . $e->getMessage());
            // No interrumpir la eliminación local si falla la eliminación en Recurrente
        }

        return $data;
    }

    /**
     * Eliminar un producto específico de Recurrente
     */
    private function deleteProductFromRecurrente($recurrenteProductId, $localProductId)
    {
        try {
            // Cargar el servicio de Recurrente
            $recurrenteService = new \App\Services\RecurrenteService();

            // Verificar si Recurrente está habilitado
            if (!$recurrenteService->isEnabled()) {
                log_message('info', "Recurrente deshabilitado, no se elimina producto {$localProductId} de Recurrente");
                return;
            }

            // Eliminar producto de Recurrente
            $response = $recurrenteService->deleteProduct($recurrenteProductId);

            log_message('info', "Producto {$localProductId} eliminado de Recurrente exitosamente. ID Recurrente: {$recurrenteProductId}");

        } catch (\Exception $e) {
            log_message('error', "Error eliminando producto {$localProductId} de Recurrente: " . $e->getMessage());
            // No lanzar excepción para no interrumpir la eliminación local
        }
    }

    /**
     * Ejecutar función en segundo plano (simulado)
     */
    private function executeInBackground($callback)
    {
        try {
            // En un entorno de producción, esto podría usar una cola de trabajos
            // Por ahora, ejecutamos directamente pero con manejo de errores
            call_user_func($callback);
        } catch (\Exception $e) {
            log_message('error', 'Error ejecutando tarea en segundo plano: ' . $e->getMessage());
        }
    }
}
