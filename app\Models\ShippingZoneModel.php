<?php

namespace App\Models;

use CodeIgniter\Model;

class ShippingZoneModel extends Model
{
    protected $table            = 'shipping_zones';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'description',
        'base_distance_km',
        'additional_cost',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'base_distance_km' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'additional_cost' => 'permit_empty|decimal|greater_than_equal_to[0]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'El nombre de la zona es requerido',
            'max_length' => 'El nombre no puede exceder 100 caracteres'
        ],
        'base_distance_km' => [
            'decimal' => 'La distancia debe ser un número decimal',
            'greater_than_equal_to' => 'La distancia no puede ser negativa'
        ],
        'additional_cost' => [
            'decimal' => 'El costo adicional debe ser un número decimal',
            'greater_than_equal_to' => 'El costo adicional no puede ser negativo'
        ]
    ];

    /**
     * Obtener zonas activas
     */
    public function getActiveZones()
    {
        return $this->where('is_active', 1)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Obtener zona por nombre
     */
    public function getZoneByName($name)
    {
        return $this->where('name', $name)
                   ->where('is_active', 1)
                   ->first();
    }

    /**
     * Calcular costo total de envío para una zona específica
     */
    public function calculateZoneShippingCost($zoneId, $packageTypeId)
    {
        $zone = $this->find($zoneId);
        if (!$zone) {
            return 0;
        }

        $packageTypeModel = new ShippingPackageTypeModel();
        $baseCost = $packageTypeModel->calculateShippingCost(
            $packageTypeId, 
            $zone['base_distance_km'], 
            $zone['additional_cost']
        );

        return $baseCost;
    }

    /**
     * Obtener estadísticas de uso de zonas
     */
    public function getZoneUsageStats()
    {
        $db = \Config\Database::connect();
        
        try {
            $query = $db->query("
                SELECT 
                    sz.name,
                    sz.id,
                    COUNT(o.id) as delivery_count,
                    AVG(o.shipping_cost) as avg_shipping_cost,
                    SUM(o.shipping_cost) as total_shipping_revenue
                FROM shipping_zones sz
                LEFT JOIN orders o ON o.shipping_zone_id = sz.id
                WHERE sz.is_active = 1
                GROUP BY sz.id, sz.name
                ORDER BY delivery_count DESC
            ");
            
            return $query->getResultArray();
        } catch (\Exception $e) {
            return [];
        }
    }
}
