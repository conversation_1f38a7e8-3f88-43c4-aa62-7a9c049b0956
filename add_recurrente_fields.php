<?php

/**
 * Script para agregar campos de Recurrente a la tabla products
 */

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "=== AGREGANDO CAMPOS DE RECURRENTE ===\n";
    echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
    echo "Base de datos: " . $dbConfig['database'] . "\n";
    echo "=====================================\n\n";

    // Verificar si las columnas ya existen
    $stmt = $db->prepare("
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME = 'products' 
        AND COLUMN_NAME IN ('recurrente_product_id', 'recurrente_sync_status', 'recurrente_synced_at', 'recurrente_storefront_link')
    ");
    $stmt->execute([$dbConfig['database']]);
    $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (count($existingColumns) > 0) {
        echo "⚠️  Las siguientes columnas ya existen: " . implode(', ', $existingColumns) . "\n";
        echo "¿Desea continuar y agregar solo las columnas faltantes? (s/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 's') {
            echo "❌ Operación cancelada.\n";
            exit(1);
        }
    }

    $columnsToAdd = [
        'recurrente_product_id' => "ADD COLUMN recurrente_product_id VARCHAR(50) NULL COMMENT 'ID del producto en Recurrente'",
        'recurrente_sync_status' => "ADD COLUMN recurrente_sync_status ENUM('pending', 'synced', 'error', 'disabled') DEFAULT 'pending' COMMENT 'Estado de sincronización con Recurrente'",
        'recurrente_synced_at' => "ADD COLUMN recurrente_synced_at DATETIME NULL COMMENT 'Fecha de última sincronización con Recurrente'",
        'recurrente_storefront_link' => "ADD COLUMN recurrente_storefront_link VARCHAR(500) NULL COMMENT 'Link del storefront de Recurrente'"
    ];

    $addedColumns = [];
    
    foreach ($columnsToAdd as $columnName => $alterStatement) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $db->exec("ALTER TABLE products " . $alterStatement);
                echo "✅ Columna agregada: {$columnName}\n";
                $addedColumns[] = $columnName;
            } catch (PDOException $e) {
                echo "❌ Error agregando columna {$columnName}: " . $e->getMessage() . "\n";
            }
        } else {
            echo "ℹ️  Columna ya existe: {$columnName}\n";
        }
    }

    // Agregar índices si se agregaron columnas
    if (count($addedColumns) > 0) {
        try {
            if (in_array('recurrente_product_id', $addedColumns)) {
                $db->exec("ALTER TABLE products ADD INDEX idx_recurrente_product_id (recurrente_product_id)");
                echo "✅ Índice agregado: idx_recurrente_product_id\n";
            }
            
            if (in_array('recurrente_sync_status', $addedColumns)) {
                $db->exec("ALTER TABLE products ADD INDEX idx_recurrente_sync_status (recurrente_sync_status)");
                echo "✅ Índice agregado: idx_recurrente_sync_status\n";
            }
        } catch (PDOException $e) {
            echo "⚠️  Error agregando índices: " . $e->getMessage() . "\n";
        }
    }

    echo "\n🎉 Proceso completado exitosamente.\n";
    echo "📊 Columnas agregadas: " . count($addedColumns) . "\n";

} catch (PDOException $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "\n";
    exit(1);
}
