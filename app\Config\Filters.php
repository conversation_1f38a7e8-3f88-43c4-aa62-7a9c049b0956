<?php

namespace Config;

use CodeIgniter\Config\Filters as BaseFilters;
use CodeIgniter\Filters\Cors;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\ForceHTTPS;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\PageCache;
use CodeIgniter\Filters\PerformanceMetrics;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseFilters
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     *
     * @var array<string, class-string|list<class-string>>
     *
     * [filter_name => classname]
     * or [filter_name => [classname1, classname2, ...]]
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'cors'          => Cors::class,
        'forcehttps'    => ForceHTTPS::class,
        'pagecache'     => PageCache::class,
        'permission'    => \App\Filters\PermissionFilter::class,
        'performance'   => PerformanceMetrics::class,
        'adminauth'     => \App\Filters\AdminAuthFilter::class,
        'ratelimit'     => \App\Filters\RateLimitFilter::class,
        'security'      => \App\Filters\SecurityValidationFilter::class,
        'errordebug'    => \App\Filters\ErrorDebugFilter::class,
    ];

    /**
     * List of special required filters.
     *
     * The filters listed here are special. They are applied before and after
     * other kinds of filters, and always applied even if a route does not exist.
     *
     * Filters set by default provide framework functionality. If removed,
     * those functions will no longer work.
     *
     * @see https://codeigniter.com/user_guide/incoming/filters.html#provided-filters
     *
     * @var array{before: list<string>, after: list<string>}
     */
    public array $required = [
        'before' => [
            // 'forcehttps', // Force Global Secure Requests - Disabled for development
            // 'pagecache',  // Web Page Caching - TEMPORALMENTE DESHABILITADO PARA DEBUG
        ],
        'after' => [
            // 'pagecache',   // Web Page Caching - TEMPORALMENTE DESHABILITADO PARA DEBUG
            'performance', // Performance Metrics
            'toolbar',     // Debug Toolbar
        ],
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     *
     * @var array{
     *     before: array<string, array{except: list<string>|string}>|list<string>,
     *     after: array<string, array{except: list<string>|string}>|list<string>
     * }
     */
    public array $globals = [
        'before' => [
            'invalidchars',
            'security',
        ],
        'after' => [
            'secureheaders',
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     *
     * Example:
     * 'POST' => ['foo', 'bar']
     *
     * If you use this, you should disable auto-routing because auto-routing
     * permits any HTTP method to access a controller. Accessing the controller
     * with a method you don't expect could bypass the filter.
     *
     * @var array<string, list<string>>
     */
    public array $methods = [];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     *
     * Example:
     * 'isLoggedIn' => ['before' => ['account/*', 'profiles/*']]
     *
     * @var array<string, array<string, list<string>>>
     */
    public array $filters = [
        // Rate limiting específico por tipo de endpoint
        'ratelimit:public' => [
            'before' => ['coupons/*', 'tracking/*']
        ],
        'ratelimit:admin' => [
            'before' => ['admin/*']
        ],
        'errordebug' => [
            'before' => ['admin/*'],
            'after' => ['admin/*']
        ],
        'ratelimit:api' => [
            'before' => ['api/*']
        ],
        'ratelimit:tracking' => [
            'before' => ['tracking/track', 'tracking/calculate-shipping']
        ],
        'ratelimit:coupons' => [
            'before' => ['coupons/validate', 'coupons/apply']
        ],

        // Autenticación administrativa
        'adminauth' => [
            'before' => [
                'admin/*',
                'admin/coupons/*',
                'admin/shipping/*',
                'admin/billing/*',
                'admin/social/*'
            ]
        ],

        // CSRF para formularios
        'csrf' => [
            'before' => [
                'admin/authenticate',
                'admin/login',
                'admin/coupons/store',
                'admin/coupons/update/*',
                'admin/shipping/*/store',
                'admin/shipping/*/update/*',
                'admin/billing/config/update',
                'admin/social/config/update',
                'coupons/apply',
                'tracking/report-issue'
            ]
        ]
    ];
}
