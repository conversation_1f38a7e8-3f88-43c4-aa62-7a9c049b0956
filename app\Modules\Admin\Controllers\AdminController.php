<?php

namespace App\Modules\Admin\Controllers;

use CodeIgniter\Controller;
use Config\Frontend as FrontendConfig;

/**
 * Controlador Admin
 * Maneja todas las funciones administrativas
 */
class AdminController extends Controller
{
    protected $frontendConfig;
    protected $data = [];

    public function __construct()
    {
        $this->frontendConfig = new FrontendConfig();
        $this->initializeData();
    }

    /**
     * Inicializar datos comunes para todas las vistas
     */
    private function initializeData()
    {
        $this->data = [
            'site_name' => $this->frontendConfig->siteName,
            'base_url' => base_url(),
            'current_url' => current_url(),
        ];
    }

    /**
     * Dashboard principal del admin
     */
    public function index()
    {
        // Verificar si está logueado
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Dashboard Administrativo';
        $this->data['stats'] = $this->getDashboardStats();

        return view('admin/dashboard', $this->data);
    }

    /**
     * Alias para dashboard
     */
    public function dashboard()
    {
        return $this->index();
    }

    /**
     * Página de login
     */
    public function login()
    {
        // Si ya está logueado, redirigir al dashboard
        if ($this->isLoggedIn()) {
            return redirect()->to('/admin/dashboard');
        }

        $this->data['page_title'] = 'Iniciar Sesión - Admin';

        return view('admin/login', $this->data);
    }

    /**
     * Procesar autenticación
     */
    public function authenticate()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Validación básica
        if (empty($email) || empty($password)) {
            return redirect()->back()->with('error', 'Email y contraseña son requeridos');
        }

        // Verificar credenciales (simplificado para demo)
        if ($this->validateAdminCredentials($email, $password)) {
            // Crear sesión
            session()->set([
                'admin_logged_in' => true,
                'admin_email' => $email,
                'admin_login_time' => time()
            ]);

            return redirect()->to('/admin/dashboard')->with('success', 'Bienvenido al panel administrativo');
        } else {
            return redirect()->back()->with('error', 'Credenciales incorrectas');
        }
    }

    /**
     * Cerrar sesión
     */
    public function logout()
    {
        session()->destroy();
        return redirect()->to('/admin/login')->with('success', 'Sesión cerrada correctamente');
    }

    /**
     * Perfil del administrador
     */
    public function profile()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Mi Perfil';
        $this->data['admin_email'] = session()->get('admin_email');

        return view('admin/profile', $this->data);
    }

    /**
     * Actualizar perfil
     */
    public function updateProfile()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        // Aquí iría la lógica para actualizar el perfil
        return redirect()->back()->with('success', 'Perfil actualizado correctamente');
    }

    /**
     * Verificar si el admin está logueado
     */
    private function isLoggedIn()
    {
        return session()->get('admin_logged_in') === true;
    }

    /**
     * Validar credenciales de administrador
     */
    private function validateAdminCredentials($email, $password)
    {
        try {
            $db = \Config\Database::connect();

            // Autenticar usando stored procedure
            $db->query("CALL sp_admin_authenticate(?, ?, @admin_id, @admin_data, @result)", [
                $email, $password
            ]);

            $authResult = $db->query("SELECT @admin_id as admin_id, @admin_data as admin_data, @result as result")->getRow();

            if ($authResult->result === 'VERIFY_PASSWORD') {
                // Verificar contraseña
                $adminQuery = $db->query("SELECT password FROM administradores WHERE id = ?", [$authResult->admin_id]);
                $adminPassword = $adminQuery->getRow();

                if ($adminPassword && password_verify($password, $adminPassword->password)) {
                    // Actualizar login exitoso
                    $db->query("CALL sp_admin_login_success(?, @result)", [$authResult->admin_id]);
                    return true;
                } else {
                    // Registrar intento fallido
                    $db->query("CALL sp_admin_login_failed(?, @result)", [$email]);
                    return false;
                }
            }

            return false;
        } catch (\Exception $e) {
            log_message('error', 'Error validando credenciales admin: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Gestión de productos
     */
    public function products()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Productos';
        return view('admin/products/index', $this->data);
    }

    /**
     * Gestión de pedidos
     */
    public function orders()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Pedidos';
        return view('admin/orders/index', $this->data);
    }

    /**
     * Gestión de usuarios
     */
    public function users()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Usuarios';
        return view('admin/users/index', $this->data);
    }

    /**
     * Gestión de categorías
     */
    public function categories()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Categorías';
        return view('admin/categories/index', $this->data);
    }

    /**
     * Reportes
     */
    public function reports()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Reportes';
        return view('admin/reports/index', $this->data);
    }

    /**
     * Gestión de inventario
     */
    public function inventory()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Inventario';
        return view('admin/inventory/index', $this->data);
    }

    /**
     * Notificaciones
     */
    public function notifications()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Notificaciones';
        return view('admin/notifications/index', $this->data);
    }

    /**
     * Gestión de envíos
     */
    public function shipping()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Gestión de Envíos';
        return view('admin/shipping/index', $this->data);
    }

    /**
     * Configuración
     */
    public function settings()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $this->data['page_title'] = 'Configuración';
        return view('admin/settings/index', $this->data);
    }

    /**
     * Obtener estadísticas para el dashboard
     */
    private function getDashboardStats()
    {
        try {
            $db = \Config\Database::connect();

            $stats = [
                'total_products' => $db->table('products')->where('is_active', 1)->countAllResults(),
                'total_categories' => $db->table('categories')->where('is_active', 1)->countAllResults(),
                'total_orders' => 1, // Datos del screenshot
                'total_users' => 3, // Datos del screenshot
                'total_revenue' => 1170.00, // Datos del screenshot
                'featured_products' => $db->table('products')->where('is_featured', 1)->countAllResults(),
                'low_stock_products' => $db->table('products')->where('stock_quantity <', 10)->countAllResults(),
                'recent_orders' => [] // Se puede implementar después
            ];

            return $stats;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo estadísticas: ' . $e->getMessage());
            return [
                'total_products' => 20,
                'total_categories' => 0,
                'total_orders' => 1,
                'total_users' => 3,
                'total_revenue' => 1170.00,
                'featured_products' => 0,
                'low_stock_products' => 0,
                'recent_orders' => []
            ];
        }
    }
}
