<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddRecurrenteSettings extends Migration
{
    public function up()
    {
        // Agregar configuraciones de Recurrente a la tabla de configuraciones
        $data = [
            [
                'setting_key' => 'recurrente_enabled',
                'setting_value' => '0',
                'description' => 'Habilitar/deshabilitar pagos con Recurrente',
                'type' => 'boolean',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_mode',
                'setting_value' => 'test',
                'description' => 'Modo de Recurrente (test/live)',
                'type' => 'select',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_public_key',
                'setting_value' => '',
                'description' => 'Clave pública de Recurrente',
                'type' => 'text',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_secret_key',
                'setting_value' => '',
                'description' => 'Clave secreta de Recurrente',
                'type' => 'password',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_webhook_secret',
                'setting_value' => '',
                'description' => 'Secreto del webhook de Recurrente',
                'type' => 'password',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_currency',
                'setting_value' => 'GTQ',
                'description' => 'Moneda para pagos con Recurrente',
                'type' => 'select',
                'category' => 'payment'
            ],
            [
                'setting_key' => 'recurrente_fee_percentage',
                'setting_value' => '3.9',
                'description' => 'Porcentaje de comisión de Recurrente',
                'type' => 'decimal',
                'category' => 'payment'
            ]
        ];

        // Verificar si existe la tabla settings, si no, crearla
        if (!$this->db->tableExists('settings')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'setting_key' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'unique' => true,
                ],
                'setting_value' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'type' => [
                    'type' => 'ENUM',
                    'constraint' => ['text', 'textarea', 'select', 'boolean', 'number', 'decimal', 'password'],
                    'default' => 'text',
                ],
                'category' => [
                    'type' => 'VARCHAR',
                    'constraint' => 50,
                    'default' => 'general',
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);
            $this->forge->addKey('id', true);
            $this->forge->addKey('setting_key');
            $this->forge->addKey('category');
            $this->forge->createTable('settings');
        }

        // Insertar configuraciones de Recurrente
        foreach ($data as $setting) {
            $setting['created_at'] = date('Y-m-d H:i:s');
            $setting['updated_at'] = date('Y-m-d H:i:s');
            
            // Verificar si ya existe la configuración
            $existing = $this->db->table('settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();
                
            if (!$existing) {
                $this->db->table('settings')->insert($setting);
            }
        }

        // Agregar Recurrente a la tabla payment_methods si existe
        if ($this->db->tableExists('payment_methods')) {
            $existing = $this->db->table('payment_methods')
                ->where('slug', 'recurrente')
                ->get()
                ->getRow();
                
            if (!$existing) {
                $this->db->table('payment_methods')->insert([
                    'name' => 'Recurrente',
                    'slug' => 'recurrente',
                    'description' => 'Pago seguro con tarjeta de crédito/débito a través de Recurrente',
                    'type' => 'gateway',
                    'icon' => 'fas fa-credit-card',
                    'instructions' => 'Paga de forma segura con tu tarjeta de crédito o débito',
                    'is_active' => 0,
                    'sort_order' => 3,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    public function down()
    {
        // Eliminar configuraciones de Recurrente
        $keys = [
            'recurrente_enabled',
            'recurrente_mode',
            'recurrente_public_key',
            'recurrente_secret_key',
            'recurrente_webhook_secret',
            'recurrente_currency',
            'recurrente_fee_percentage'
        ];

        if ($this->db->tableExists('settings')) {
            $this->db->table('settings')
                ->whereIn('setting_key', $keys)
                ->delete();
        }

        // Eliminar de payment_methods
        if ($this->db->tableExists('payment_methods')) {
            $this->db->table('payment_methods')
                ->where('slug', 'recurrente')
                ->delete();
        }
    }
}
