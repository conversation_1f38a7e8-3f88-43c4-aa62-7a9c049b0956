<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class RecurrenteController extends BaseController
{
    protected $db;
    protected $recurrenteSettings;

    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->loadRecurrenteSettings();
    }

    private function loadRecurrenteSettings()
    {
        try {
            $settings = $this->db->query("
                SELECT setting_key, setting_value
                FROM system_settings
                WHERE setting_group = 'integrations' AND setting_key LIKE 'recurrente_%' AND is_active = 1
            ")->getResultArray();

            $this->recurrenteSettings = [];
            foreach ($settings as $setting) {
                $key = str_replace('recurrente_', '', $setting['setting_key']);
                $this->recurrenteSettings[$key] = $setting['setting_value'];
            }
        } catch (\Exception $e) {
            log_message('error', 'Error loading Recurrente settings: ' . $e->getMessage());
            $this->recurrenteSettings = [];
        }
    }

    public function processPayment()
    {
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->to('/checkout')->with('error', 'Método no válido');
        }

        try {
            // Obtener datos del pedido
            $orderId = $this->request->getPost('order_id');
            $amount = $this->request->getPost('amount');
            $currency = $this->recurrenteSettings['currency'] ?? 'GTQ';
            $installments = $this->request->getPost('installments') ?? 1;

            if (!$orderId || !$amount) {
                return redirect()->to('/checkout')->with('error', 'Datos de pago incompletos');
            }

            // Verificar que Recurrente esté habilitado
            if (($this->recurrenteSettings['enabled'] ?? '0') !== '1') {
                return redirect()->to('/checkout')->with('error', 'Método de pago no disponible');
            }

            // Crear payload para Recurrente API
            $payload = [
                'amount' => floatval($amount) * 100, // Recurrente usa centavos
                'currency' => $currency,
                'description' => "Pedido #{$orderId} - MrCell Guatemala",
                'order_id' => $orderId,
                'success_url' => $this->recurrenteSettings['success_url'] ?? base_url('checkout/success'),
                'cancel_url' => $this->recurrenteSettings['cancel_url'] ?? base_url('checkout/cancel'),
                'error_url' => $this->recurrenteSettings['error_url'] ?? base_url('checkout/error'),
                'webhook_url' => $this->recurrenteSettings['webhook_url'] ?? base_url('webhooks/recurrente'),
                'installments' => intval($installments),
                'metadata' => [
                    'order_id' => $orderId,
                    'customer_id' => session()->get('user_id'),
                    'source' => 'mrcell_website'
                ]
            ];

            // Llamar a la API de Recurrente
            $response = $this->callRecurrenteAPI('charges', $payload);

            if ($response && isset($response['checkout_url'])) {
                // Guardar información del pago en la base de datos
                $this->db->query("
                    INSERT INTO payment_transactions (order_id, payment_method, transaction_id, amount, currency, status, gateway_response, created_at)
                    VALUES (?, 'recurrente', ?, ?, ?, 'pending', ?, NOW())
                ", [$orderId, $response['id'] ?? '', $amount, $currency, json_encode($response)]);

                // Redirigir al checkout de Recurrente
                return redirect()->to($response['checkout_url']);
            } else {
                log_message('error', 'Recurrente API error: ' . json_encode($response));
                return redirect()->to('/checkout')->with('error', 'Error al procesar el pago. Intenta nuevamente.');
            }

        } catch (\Exception $e) {
            log_message('error', 'Recurrente payment error: ' . $e->getMessage());
            return redirect()->to('/checkout')->with('error', 'Error interno. Intenta nuevamente.');
        }
    }

    public function webhook()
    {
        try {
            $payload = $this->request->getBody();
            $signature = $this->request->getHeaderLine('Recurrente-Signature');

            // Verificar webhook signature si está configurado
            if (!empty($this->recurrenteSettings['webhook_secret'])) {
                $expectedSignature = hash_hmac('sha256', $payload, $this->recurrenteSettings['webhook_secret']);
                if (!hash_equals($expectedSignature, $signature)) {
                    log_message('error', 'Invalid webhook signature from Recurrente');
                    return $this->response->setStatusCode(401);
                }
            }

            $data = json_decode($payload, true);
            if (!$data) {
                log_message('error', 'Invalid webhook payload from Recurrente');
                return $this->response->setStatusCode(400);
            }

            // Procesar el webhook según el tipo de evento
            $eventType = $data['type'] ?? '';
            $chargeData = $data['data'] ?? [];

            switch ($eventType) {
                case 'charge.succeeded':
                    $this->handleSuccessfulPayment($chargeData);
                    break;
                case 'charge.failed':
                    $this->handleFailedPayment($chargeData);
                    break;
                case 'charge.refunded':
                    $this->handleRefundedPayment($chargeData);
                    break;
                default:
                    log_message('info', 'Unhandled Recurrente webhook event: ' . $eventType);
            }

            return $this->response->setStatusCode(200)->setJSON(['status' => 'ok']);

        } catch (\Exception $e) {
            log_message('error', 'Recurrente webhook error: ' . $e->getMessage());
            return $this->response->setStatusCode(500);
        }
    }

    private function handleSuccessfulPayment($chargeData)
    {
        $orderId = $chargeData['metadata']['order_id'] ?? '';
        $transactionId = $chargeData['id'] ?? '';

        if ($orderId) {
            // Actualizar estado del pedido
            $this->db->query("
                UPDATE orders SET status = 'paid', payment_status = 'completed', updated_at = NOW()
                WHERE id = ?
            ", [$orderId]);

            // Actualizar transacción
            $this->db->query("
                UPDATE payment_transactions 
                SET status = 'completed', transaction_id = ?, gateway_response = ?, updated_at = NOW()
                WHERE order_id = ? AND payment_method = 'recurrente'
            ", [$transactionId, json_encode($chargeData), $orderId]);

            log_message('info', "Payment completed for order {$orderId}");
        }
    }

    private function handleFailedPayment($chargeData)
    {
        $orderId = $chargeData['metadata']['order_id'] ?? '';
        $transactionId = $chargeData['id'] ?? '';

        if ($orderId) {
            // Actualizar estado del pedido
            $this->db->query("
                UPDATE orders SET payment_status = 'failed', updated_at = NOW()
                WHERE id = ?
            ", [$orderId]);

            // Actualizar transacción
            $this->db->query("
                UPDATE payment_transactions 
                SET status = 'failed', transaction_id = ?, gateway_response = ?, updated_at = NOW()
                WHERE order_id = ? AND payment_method = 'recurrente'
            ", [$transactionId, json_encode($chargeData), $orderId]);

            log_message('info', "Payment failed for order {$orderId}");
        }
    }

    private function handleRefundedPayment($chargeData)
    {
        $orderId = $chargeData['metadata']['order_id'] ?? '';
        $transactionId = $chargeData['id'] ?? '';

        if ($orderId) {
            // Actualizar estado del pedido
            $this->db->query("
                UPDATE orders SET payment_status = 'refunded', updated_at = NOW()
                WHERE id = ?
            ", [$orderId]);

            // Actualizar transacción
            $this->db->query("
                UPDATE payment_transactions 
                SET status = 'refunded', transaction_id = ?, gateway_response = ?, updated_at = NOW()
                WHERE order_id = ? AND payment_method = 'recurrente'
            ", [$transactionId, json_encode($chargeData), $orderId]);

            log_message('info', "Payment refunded for order {$orderId}");
        }
    }

    private function callRecurrenteAPI($endpoint, $data)
    {
        $apiKey = $this->recurrenteSettings['secret_key'] ?? '';
        $mode = $this->recurrenteSettings['mode'] ?? 'test';
        
        $baseUrl = $mode === 'live' ? 'https://api.recurrente.com/v1/' : 'https://api.recurrente.com/v1/';
        $url = $baseUrl . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $httpCode >= 400) {
            log_message('error', "Recurrente API error: HTTP {$httpCode}, Response: {$response}");
            return null;
        }

        return json_decode($response, true);
    }

    public function success()
    {
        $orderId = $this->request->getGet('order_id');
        return view('checkout/success', ['order_id' => $orderId]);
    }

    public function cancel()
    {
        $orderId = $this->request->getGet('order_id');
        return redirect()->to('/checkout')->with('error', 'Pago cancelado');
    }

    public function error()
    {
        $orderId = $this->request->getGet('order_id');
        return redirect()->to('/checkout')->with('error', 'Error en el pago. Intenta nuevamente.');
    }
}
