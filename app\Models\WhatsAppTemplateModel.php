<?php

namespace App\Models;

use CodeIgniter\Model;

class WhatsAppTemplateModel extends Model
{
    protected $table = 'whatsapp_templates';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'template_key',
        'template_name',
        'message_template',
        'description',
        'variables',
        'is_mandatory',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'template_key' => 'required|max_length[100]|is_unique[whatsapp_templates.template_key,id,{id}]',
        'template_name' => 'required|max_length[200]',
        'message_template' => 'required',
        'description' => 'permit_empty',
        'is_mandatory' => 'permit_empty|in_list[0,1]',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'template_key' => [
            'required' => 'La clave de plantilla es requerida',
            'is_unique' => 'Esta clave de plantilla ya existe'
        ],
        'template_name' => [
            'required' => 'El nombre de la plantilla es requerido'
        ],
        'message_template' => [
            'required' => 'El contenido del mensaje es requerido'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['processVariables'];
    protected $beforeUpdate = ['processVariables'];
    protected $afterFind = ['parseVariables'];

    /**
     * Procesar variables antes de guardar
     */
    protected function processVariables(array $data)
    {
        if (isset($data['data']['variables']) && is_array($data['data']['variables'])) {
            $data['data']['variables'] = json_encode($data['data']['variables']);
        }
        return $data;
    }

    /**
     * Parsear variables después de obtener
     */
    protected function parseVariables(array $data)
    {
        if (isset($data['data'])) {
            // Caso de un solo registro
            if (isset($data['data']['variables']) && is_string($data['data']['variables'])) {
                $data['data']['variables'] = json_decode($data['data']['variables'], true) ?: [];
            }
        } else {
            // Caso de múltiples registros
            foreach ($data['data'] as &$row) {
                if (isset($row['variables']) && is_string($row['variables'])) {
                    $row['variables'] = json_decode($row['variables'], true) ?: [];
                }
            }
        }
        return $data;
    }

    /**
     * Obtener plantilla por clave
     */
    public function getTemplate($templateKey)
    {
        return $this->where('template_key', $templateKey)
                   ->where('is_active', 1)
                   ->first();
    }

    /**
     * Obtener todas las plantillas activas
     */
    public function getActiveTemplates()
    {
        return $this->where('is_active', 1)
                   ->orderBy('template_name', 'ASC')
                   ->findAll();
    }

    /**
     * Obtener plantillas obligatorias
     */
    public function getMandatoryTemplates()
    {
        return $this->where('is_mandatory', 1)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Obtener plantillas opcionales
     */
    public function getOptionalTemplates()
    {
        return $this->where('is_mandatory', 0)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Procesar plantilla con variables
     */
    public function processTemplate($templateKey, $variables = [])
    {
        $template = $this->getTemplate($templateKey);
        
        if (!$template) {
            return null;
        }

        $message = $template['message_template'];
        
        // Reemplazar variables en el mensaje
        foreach ($variables as $key => $value) {
            $message = str_replace('{' . $key . '}', $value, $message);
        }
        
        return [
            'template' => $template,
            'message' => $message,
            'processed_variables' => $variables
        ];
    }

    /**
     * Extraer variables de una plantilla
     */
    public function extractVariables($messageTemplate)
    {
        preg_match_all('/\{([^}]+)\}/', $messageTemplate, $matches);
        return array_unique($matches[1]);
    }

    /**
     * Validar variables requeridas
     */
    public function validateVariables($templateKey, $variables = [])
    {
        $template = $this->getTemplate($templateKey);
        
        if (!$template) {
            return ['error' => 'Plantilla no encontrada'];
        }

        $requiredVars = $template['variables'] ?: [];
        $missingVars = [];
        
        foreach ($requiredVars as $var) {
            if (!isset($variables[$var]) || empty($variables[$var])) {
                $missingVars[] = $var;
            }
        }
        
        if (!empty($missingVars)) {
            return ['error' => 'Variables faltantes: ' . implode(', ', $missingVars)];
        }
        
        return ['success' => true];
    }

    /**
     * Actualizar plantilla
     */
    public function updateTemplate($templateKey, $data)
    {
        $template = $this->where('template_key', $templateKey)->first();
        
        if (!$template) {
            return false;
        }
        
        // Extraer variables automáticamente del mensaje
        if (isset($data['message_template'])) {
            $data['variables'] = $this->extractVariables($data['message_template']);
        }
        
        return $this->update($template['id'], $data);
    }
}
