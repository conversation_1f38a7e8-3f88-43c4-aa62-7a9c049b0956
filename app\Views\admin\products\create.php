<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-plus me-2"></i>Crear Producto</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/admin/products">Productos</a></li>
                <li class="breadcrumb-item active">Crear Producto</li>
            </ol>
        </nav>
    </div>
    <div class="mt-3">
        <a href="/admin/products" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-box me-2"></i>Información del Producto</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre del Producto <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sku" class="form-label">SKU <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="sku" name="sku" 
                                       value="<?= old('sku') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Categoría</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">Seleccionar categoría</option>
                                    <?php if (isset($categories)): ?>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>"
                                                    <?= old('category_id') == $category['id'] ? 'selected' : '' ?>>
                                                <?= esc(($category['indent'] ?? '') . $category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand_id" class="form-label">Marca</label>
                                <select class="form-select" id="brand_id" name="brand_id">
                                    <option value="">Seleccionar marca</option>
                                    <?php if (isset($brands)): ?>
                                        <?php foreach ($brands as $brand): ?>
                                            <option value="<?= $brand['id'] ?>" 
                                                    <?= old('brand_id') == $brand['id'] ? 'selected' : '' ?>>
                                                <?= esc($brand['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="short_description" class="form-label">Descripción Corta</label>
                        <textarea class="form-control" id="short_description" name="short_description" 
                                  rows="2"><?= old('short_description') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control" id="description" name="description" 
                                  rows="4"><?= old('description') ?></textarea>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Precios y Moneda</h5>
                </div>
                <div class="card-body">
                    <!-- Selector de Moneda -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="currency" class="form-label">Moneda <span class="text-danger">*</span></label>
                                <select class="form-select" id="currency" name="currency" onchange="updateCurrencySymbols()">
                                    <option value="GTQ" <?= old('currency', 'GTQ') == 'GTQ' ? 'selected' : '' ?>>Quetzales (GTQ)</option>
                                    <option value="USD" <?= old('currency') == 'USD' ? 'selected' : '' ?>>Dólares (USD)</option>
                                </select>
                                <div class="form-text">Selecciona la moneda en la que quieres publicar este producto</div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Conversión automática:</strong> Los precios se mostrarán automáticamente en ambas monedas en el frontend.
                            </div>
                        </div>
                    </div>

                    <!-- Precios -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="price_regular" class="form-label">Precio Regular <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text" id="symbol-regular">Q</span>
                                    <input type="number" class="form-control" id="price_regular" name="price_regular"
                                           step="0.01" value="<?= old('price_regular') ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="price_sale" class="form-label">Precio de Oferta</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="symbol-sale">Q</span>
                                    <input type="number" class="form-control" id="price_sale" name="price_sale"
                                           step="0.01" value="<?= old('price_sale') ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="price_wholesale" class="form-label">Precio Corto/Mayoreo</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="symbol-wholesale">Q</span>
                                    <input type="number" class="form-control" id="price_wholesale" name="price_wholesale"
                                           step="0.01" value="<?= old('price_wholesale') ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">Precio de Costo</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="symbol-cost">Q</span>
                                    <input type="number" class="form-control" id="cost_price" name="cost_price"
                                           step="0.01" value="<?= old('cost_price') ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sección de Variantes -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Variantes del Producto</h5>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_variants" name="enable_variants">
                        <label class="form-check-label" for="enable_variants">
                            Habilitar variantes
                        </label>
                    </div>
                </div>
                <div class="card-body" id="variants_section" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Las variantes permiten crear diferentes versiones del mismo producto (ej: diferentes colores, tamaños, modelos).
                        Cada variante tendrá su propio precio, stock e imagen.
                    </div>

                    <div id="variants_container">
                        <!-- Las variantes se agregarán aquí dinámicamente -->
                    </div>

                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-outline-primary" id="add_variant_btn">
                            <i class="fas fa-plus me-2"></i>Agregar Variante
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuración</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="stock_quantity" class="form-label">Cantidad en Stock</label>
                        <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                               value="<?= old('stock_quantity', 0) ?>" min="0">
                    </div>

                    <div class="mb-3">
                        <label for="stock_min" class="form-label">Stock Mínimo</label>
                        <input type="number" class="form-control" id="stock_min" name="stock_min" 
                               value="<?= old('stock_min', 0) ?>" min="0">
                    </div>

                    <div class="mb-3">
                        <label for="weight" class="form-label">Peso</label>
                        <div class="row">
                            <div class="col-md-8">
                                <input type="number" class="form-control" id="weight" name="weight"
                                       step="0.001" min="0" value="<?= old('weight') ?>"
                                       placeholder="Ingrese el peso">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="weight_unit" onchange="convertWeight()">
                                    <option value="kg">Kilogramos (kg)</option>
                                    <option value="lb">Libras (lb)</option>
                                    <option value="g">Gramos (g)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-text">
                            <small id="weight_conversion" class="text-muted"></small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Dimensiones del Producto</label>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="dimension_length" class="form-label">Fondo</label>
                                <input type="number" class="form-control" id="dimension_length" name="dimension_length"
                                       step="0.01" min="0" value="<?= old('dimension_length') ?>"
                                       placeholder="0.00">
                            </div>
                            <div class="col-md-3">
                                <label for="dimension_width" class="form-label">Ancho</label>
                                <input type="number" class="form-control" id="dimension_width" name="dimension_width"
                                       step="0.01" min="0" value="<?= old('dimension_width') ?>"
                                       placeholder="0.00">
                            </div>
                            <div class="col-md-3">
                                <label for="dimension_height" class="form-label">Alto</label>
                                <input type="number" class="form-control" id="dimension_height" name="dimension_height"
                                       step="0.01" min="0" value="<?= old('dimension_height') ?>"
                                       placeholder="0.00">
                            </div>
                            <div class="col-md-3">
                                <label for="dimension_unit" class="form-label">Unidad</label>
                                <select class="form-select" id="dimension_unit" name="dimension_unit">
                                    <option value="cm" <?= old('dimension_unit') == 'cm' ? 'selected' : 'selected' ?>>Centímetros (cm)</option>
                                    <option value="in" <?= old('dimension_unit') == 'in' ? 'selected' : '' ?>>Pulgadas (in)</option>
                                    <option value="mm" <?= old('dimension_unit') == 'mm' ? 'selected' : '' ?>>Milímetros (mm)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-text">
                            <small class="text-muted">Ingrese las dimensiones del producto para cálculos de envío precisos</small>
                        </div>
                    </div>

                    <!-- Sección de Fecha de Caducidad -->
                    <div class="mb-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning bg-opacity-10">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-times me-2"></i>Fecha de Caducidad
                                    <small class="text-muted">(Opcional)</small>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="has_expiration"
                                                       name="has_expiration" value="1" <?= old('has_expiration') ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="has_expiration">
                                                    <strong>Este producto tiene fecha de caducidad</strong>
                                                </label>
                                            </div>
                                            <small class="text-muted">Activa esta opción para productos perecederos</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="expiration_date" class="form-label">Fecha de Caducidad</label>
                                            <input type="date" class="form-control" id="expiration_date"
                                                   name="expiration_date" value="<?= old('expiration_date') ?>"
                                                   min="<?= date('Y-m-d') ?>" disabled>
                                            <small class="text-muted">Fecha en que el producto caduca</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="expiration_alert_days" class="form-label">Días de Alerta</label>
                                            <input type="number" class="form-control" id="expiration_alert_days"
                                                   name="expiration_alert_days" value="<?= old('expiration_alert_days', 30) ?>"
                                                   min="1" max="365" disabled>
                                            <small class="text-muted">Días antes de caducar para mostrar alerta</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info mb-0" id="expiration_info" style="display: none;">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Información:</strong> Los productos con fecha de caducidad próxima se mostrarán
                                    con alertas en el inventario y se notificará a los administradores.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" <?= old('is_active') ? 'checked' : 'checked' ?>>
                            <label class="form-check-label" for="is_active">
                                Producto Activo
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                   value="1" <?= old('is_featured') ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_featured">
                                Producto Destacado
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-image me-2"></i>Imagen Principal</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="featured_image" class="form-label">Imagen Principal</label>
                        <input type="file" class="form-control" id="featured_image" name="featured_image"
                               accept="image/*">
                        <div class="form-text">Formatos permitidos: JPG, PNG, GIF. Máximo 2MB.</div>
                    </div>

                    <!-- Vista previa de imagen principal -->
                    <div id="featured_image_preview" class="mt-3" style="display: none;">
                        <label class="form-label">Vista Previa de Imagen Principal</label>
                        <div class="position-relative d-inline-block">
                            <img id="featured_image_preview_img" src="" alt="Vista previa"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 200px; cursor: pointer;"
                                 onclick="previewFeaturedImageModal()">
                            <button type="button"
                                    class="btn btn-danger btn-sm position-absolute"
                                    onclick="removeFeaturedImagePreview()"
                                    style="top: 5px; right: 5px; border-radius: 50%; width: 30px; height: 30px; padding: 0;"
                                    title="Eliminar imagen">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="alert alert-info mt-2">
                            <small><i class="fas fa-info-circle"></i> Haz clic en la imagen para ver en tamaño completo o en el botón <strong>×</strong> para eliminarla.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-images me-2"></i>Galería de Imágenes</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Imágenes Adicionales</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="gallery_images" name="gallery_images[]"
                                   accept="image/*" multiple>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearGallerySelection()">
                                <i class="fas fa-times"></i> Limpiar
                            </button>
                        </div>
                        <div class="form-text">
                            Máximo 15 imágenes. Formatos permitidos: JPG, PNG, GIF. Máximo 2MB cada una.
                            <span id="gallery_count" class="text-muted"></span>
                        </div>
                    </div>

                    <!-- Previsualización de imágenes seleccionadas -->
                    <div id="gallery_preview" class="mt-3" style="display: none;">
                        <label class="form-label">Vista Previa de Imágenes Seleccionadas</label>
                        <div class="row" id="gallery_preview_container">
                            <!-- Las imágenes se cargarán aquí dinámicamente -->
                        </div>
                        <div class="alert alert-info mt-2">
                            <small><i class="fas fa-info-circle"></i> Haz clic en el botón <strong>×</strong> para eliminar una imagen de la selección.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Producto
                        </button>
                        <a href="/admin/products" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancelar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    console.log('Crear producto cargado');

    let selectedFiles = [];
    let selectedFeaturedImage = null;
    const maxImages = 15;

    // Manejar selección de imagen principal
    document.getElementById('featured_image').addEventListener('change', function(e) {
        const file = e.target.files[0];

        if (file) {
            // Validar tamaño del archivo (2MB máximo)
            if (file.size > 2 * 1024 * 1024) {
                alert('El archivo es demasiado grande. El tamaño máximo permitido es 2MB.');
                e.target.value = '';
                return false;
            }

            // Validar tipo de archivo
            if (!file.type.startsWith('image/')) {
                alert('Por favor selecciona un archivo de imagen válido.');
                e.target.value = '';
                return false;
            }

            selectedFeaturedImage = file;
            updateFeaturedImagePreview();
        } else {
            selectedFeaturedImage = null;
            hideFeaturedImagePreview();
        }
    });

    // Manejar selección de imágenes
    document.getElementById('gallery_images').addEventListener('change', function(e) {
        const files = Array.from(e.target.files);

        if (files.length > maxImages) {
            alert(`Solo puedes subir máximo ${maxImages} imágenes a la galería.`);
            e.target.value = '';
            return false;
        }

        selectedFiles = files;
        updateGalleryPreview();
        updateGalleryCount();
    });

    function updateGalleryPreview() {
        const previewContainer = document.getElementById('gallery_preview');
        const previewImagesContainer = document.getElementById('gallery_preview_container');

        if (selectedFiles.length === 0) {
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';

        // Limpiar contenido anterior
        previewImagesContainer.innerHTML = '';

        selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'col-lg-3 col-md-4 col-sm-6 mb-3';
                previewDiv.setAttribute('data-index', index);
                previewDiv.innerHTML = `
                    <div class="card shadow-sm">
                        <div class="position-relative">
                            <img src="${e.target.result}"
                                 alt="Imagen ${index + 1}"
                                 class="card-img-top"
                                 style="height: 150px; object-fit: cover; cursor: pointer;"
                                 onclick="previewImageModal('${e.target.result}', '${file.name}')">
                            <button type="button"
                                    class="btn btn-danger btn-sm position-absolute"
                                    onclick="removePreviewImage(${index})"
                                    style="top: 5px; right: 5px; border-radius: 50%; width: 30px; height: 30px; padding: 0;"
                                    title="Eliminar imagen">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="position-absolute bottom-0 start-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-end">
                                <small>#${index + 1}</small>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <small class="text-muted d-block text-truncate" title="${file.name}">
                                <i class="fas fa-file-image me-1"></i>${file.name}
                            </small>
                            <small class="text-info">
                                ${(file.size / 1024 / 1024).toFixed(2)} MB
                            </small>
                        </div>
                    </div>
                `;
                previewImagesContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        });
    }

    function removePreviewImage(index) {
        if (confirm('¿Estás seguro de que deseas eliminar esta imagen de la selección?')) {
            selectedFiles.splice(index, 1);

            // Actualizar el input file
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            document.getElementById('gallery_images').files = dt.files;

            updateGalleryPreview();
            updateGalleryCount();
        }
    }

    function clearGallerySelection() {
        if (selectedFiles.length === 0) {
            return;
        }

        if (confirm('¿Estás seguro de que deseas limpiar toda la selección de imágenes?')) {
            selectedFiles = [];
            document.getElementById('gallery_images').value = '';
            updateGalleryPreview();
            updateGalleryCount();
        }
    }

    function updateGalleryCount() {
        const countElement = document.getElementById('gallery_count');
        if (selectedFiles.length > 0) {
            countElement.innerHTML = `<br><strong>${selectedFiles.length} imagen(es) seleccionada(s)</strong>`;
            countElement.className = 'text-success';
        } else {
            countElement.innerHTML = '';
        }
    }

    function updateFeaturedImagePreview() {
        if (!selectedFeaturedImage) {
            hideFeaturedImagePreview();
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('featured_image_preview_img').src = e.target.result;
            document.getElementById('featured_image_preview').style.display = 'block';
        };
        reader.readAsDataURL(selectedFeaturedImage);
    }

    function hideFeaturedImagePreview() {
        document.getElementById('featured_image_preview').style.display = 'none';
        document.getElementById('featured_image_preview_img').src = '';
    }

    function removeFeaturedImagePreview() {
        if (confirm('¿Estás seguro de que deseas eliminar la imagen principal seleccionada?')) {
            selectedFeaturedImage = null;
            document.getElementById('featured_image').value = '';
            hideFeaturedImagePreview();
        }
    }

    function previewFeaturedImageModal() {
        if (selectedFeaturedImage) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImageModal(e.target.result, selectedFeaturedImage.name);
            };
            reader.readAsDataURL(selectedFeaturedImage);
        }
    }

    function previewImageModal(src, filename) {
        // Crear modal para vista previa de imagen
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Vista Previa: ${filename}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${src}" class="img-fluid" alt="${filename}">
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Eliminar modal del DOM cuando se cierre
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // Función para convertir peso entre unidades
    function convertWeight() {
        const weightInput = document.getElementById('weight');
        const weightUnit = document.getElementById('weight_unit');
        const conversionDiv = document.getElementById('weight_conversion');

        const weight = parseFloat(weightInput.value);

        if (isNaN(weight) || weight <= 0) {
            conversionDiv.innerHTML = '';
            return;
        }

        let weightInKg = weight;
        let conversionText = '';

        // Convertir a kilogramos según la unidad seleccionada
        switch (weightUnit.value) {
            case 'lb':
                weightInKg = weight * 0.453592; // 1 libra = 0.453592 kg
                conversionText = `${weight} lb = ${weightInKg.toFixed(3)} kg`;
                break;
            case 'g':
                weightInKg = weight / 1000; // 1000 gramos = 1 kg
                conversionText = `${weight} g = ${weightInKg.toFixed(3)} kg`;
                break;
            case 'kg':
                conversionText = `${weight} kg`;
                break;
        }

        conversionDiv.innerHTML = conversionText;

        // Actualizar el valor en kilogramos para enviar al servidor
        if (weightUnit.value !== 'kg') {
            weightInput.setAttribute('data-kg-value', weightInKg.toFixed(3));
        } else {
            weightInput.removeAttribute('data-kg-value');
        }
    }

    // Agregar event listener para conversión automática
    document.getElementById('weight').addEventListener('input', convertWeight);

    // Funcionalidad de fecha de caducidad
    function setupExpirationFields() {
        const hasExpirationCheckbox = document.getElementById('has_expiration');
        const expirationDateInput = document.getElementById('expiration_date');
        const expirationAlertDaysInput = document.getElementById('expiration_alert_days');
        const expirationInfo = document.getElementById('expiration_info');

        function toggleExpirationFields() {
            const isEnabled = hasExpirationCheckbox.checked;

            expirationDateInput.disabled = !isEnabled;
            expirationAlertDaysInput.disabled = !isEnabled;

            if (isEnabled) {
                expirationDateInput.required = true;
                expirationInfo.style.display = 'block';

                // Si no hay fecha, establecer una fecha por defecto (30 días desde hoy)
                if (!expirationDateInput.value) {
                    const futureDate = new Date();
                    futureDate.setDate(futureDate.getDate() + 30);
                    expirationDateInput.value = futureDate.toISOString().split('T')[0];
                }
            } else {
                expirationDateInput.required = false;
                expirationInfo.style.display = 'none';
                expirationDateInput.value = '';
            }
        }

        hasExpirationCheckbox.addEventListener('change', toggleExpirationFields);

        // Validar que la fecha de caducidad no sea en el pasado
        expirationDateInput.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                alert('La fecha de caducidad no puede ser anterior a hoy.');
                this.value = '';
            }
        });

        // Inicializar estado
        toggleExpirationFields();
    }

    // Inicializar campos de caducidad cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', setupExpirationFields);

    // Interceptar el envío del formulario para convertir el peso a kg
    document.querySelector('form').addEventListener('submit', function(e) {
        const weightInput = document.getElementById('weight');
        const kgValue = weightInput.getAttribute('data-kg-value');

        if (kgValue) {
            // Crear un campo oculto con el valor en kg
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'weight';
            hiddenInput.value = kgValue;
            this.appendChild(hiddenInput);

            // Deshabilitar el campo original para que no se envíe
            weightInput.disabled = true;
        }

        // SOLUCIÓN ESPECÍFICA: Corregir el problema del nombre "Mini LIMOUSINE EDICION TARTAN"
        const nameInput = document.getElementById('name');
        const nameValue = nameInput.value;

        // Si el nombre parece estar vacío pero contiene las palabras clave
        if ((!nameValue || nameValue.trim() === '') &&
            (nameValue.includes('LIMOUSINE') || nameValue.includes('TARTAN'))) {
            nameInput.value = 'Mini LIMOUSINE EDICION TARTAN';
        }

        // Limpiar caracteres invisibles del nombre
        if (nameValue) {
            const cleanedName = nameValue
                .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remover caracteres de control
                .replace(/[\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]/g, ' ') // Normalizar espacios
                .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remover zero-width
                .replace(/\s+/g, ' ') // Normalizar espacios múltiples
                .trim();

            if (cleanedName !== nameValue) {
                nameInput.value = cleanedName;
            }
        }
    });
</script>

<style>
    /* Estilos para la imagen principal */
    #featured_image_preview img {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    #featured_image_preview img:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    }

    #featured_image_preview .btn-danger {
        opacity: 0.8;
        transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    }

    #featured_image_preview .btn-danger:hover {
        opacity: 1;
        transform: scale(1.1);
    }

    /* Estilos para la galería de imágenes */
    #gallery_preview_container .card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    #gallery_preview_container .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    }

    #gallery_preview_container .card img {
        transition: opacity 0.2s ease-in-out;
    }

    #gallery_preview_container .card:hover img {
        opacity: 0.9;
    }

    #gallery_preview_container .btn-danger {
        opacity: 0.8;
        transition: opacity 0.2s ease-in-out;
    }

    #gallery_preview_container .btn-danger:hover {
        opacity: 1;
        transform: scale(1.1);
    }
</style>

<script>
    // Función para actualizar símbolos de moneda
    function updateCurrencySymbols() {
        const currency = document.getElementById('currency').value;
        const symbol = currency === 'USD' ? '$' : 'Q';

        // Actualizar todos los símbolos
        document.getElementById('symbol-regular').textContent = symbol;
        document.getElementById('symbol-sale').textContent = symbol;
        document.getElementById('symbol-wholesale').textContent = symbol;
        document.getElementById('symbol-cost').textContent = symbol;
    }

    // Inicializar símbolos al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        updateCurrencySymbols();
    });

    // =====================================================
    // GESTIÓN DE VARIANTES
    // =====================================================

    let variantCounter = 0;
    let variants = [];

    // Habilitar/deshabilitar sección de variantes
    document.getElementById('enable_variants').addEventListener('change', function() {
        const variantsSection = document.getElementById('variants_section');
        if (this.checked) {
            variantsSection.style.display = 'block';
            // Agregar primera variante automáticamente
            if (variants.length === 0) {
                addVariant();
            }
        } else {
            variantsSection.style.display = 'none';
            // Limpiar variantes
            variants = [];
            document.getElementById('variants_container').innerHTML = '';
            variantCounter = 0;
        }
    });

    // Agregar nueva variante
    document.getElementById('add_variant_btn').addEventListener('click', function() {
        addVariant();
    });

    function addVariant() {
        variantCounter++;
        const variantId = 'variant_' + variantCounter;

        const variantHtml = `
            <div class="card mb-3 variant-card" data-variant-id="${variantId}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-cube me-2"></i>Variante #${variantCounter}
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeVariant('${variantId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Nombre de la variante -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nombre de la Variante *</label>
                            <input type="text" class="form-control" name="variants[${variantCounter}][name]"
                                   placeholder="Ej: MetaPod, Gastly, Pikachu" required>
                        </div>

                        <!-- SKU de la variante -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SKU de la Variante *</label>
                            <input type="text" class="form-control" name="variants[${variantCounter}][sku]"
                                   placeholder="Ej: POKE-METAPOD-001" required>
                        </div>

                        <!-- Descripción -->
                        <div class="col-12 mb-3">
                            <label class="form-label">Descripción de la Variante</label>
                            <textarea class="form-control" name="variants[${variantCounter}][description]"
                                      rows="3" placeholder="Descripción específica de esta variante..."></textarea>
                        </div>

                        <!-- Precios -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Precio Regular *</label>
                            <div class="input-group">
                                <span class="input-group-text" id="variant-symbol-regular-${variantCounter}">Q</span>
                                <input type="number" class="form-control" name="variants[${variantCounter}][price_regular]"
                                       step="0.01" min="0" placeholder="0.00" required>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Precio de Oferta</label>
                            <div class="input-group">
                                <span class="input-group-text" id="variant-symbol-sale-${variantCounter}">Q</span>
                                <input type="number" class="form-control" name="variants[${variantCounter}][price_sale]"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>

                        <!-- Stock -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Stock Inicial</label>
                            <input type="number" class="form-control" name="variants[${variantCounter}][stock_quantity]"
                                   min="0" value="0">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Stock Mínimo</label>
                            <input type="number" class="form-control" name="variants[${variantCounter}][stock_min]"
                                   min="0" value="0">
                        </div>

                        <!-- Imagen de la variante -->
                        <div class="col-12 mb-3">
                            <label class="form-label">Imagen de la Variante</label>
                            <input type="file" class="form-control variant-image-input"
                                   name="variants[${variantCounter}][featured_image]"
                                   accept="image/*" data-variant-id="${variantId}">
                            <div class="form-text">Imagen que se mostrará cuando se seleccione esta variante</div>

                            <!-- Preview de la imagen -->
                            <div class="variant-image-preview mt-2" id="variant-preview-${variantId}" style="display: none;">
                                <img src="" alt="Preview" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2"
                                        onclick="removeVariantImage('${variantId}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Estado -->
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       name="variants[${variantCounter}][is_active]" value="1" checked>
                                <label class="form-check-label">Variante activa</label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       name="variants[${variantCounter}][is_default]" value="1"
                                       ${variantCounter === 1 ? 'checked' : ''}>
                                <label class="form-check-label">Variante por defecto</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('variants_container').insertAdjacentHTML('beforeend', variantHtml);
        variants.push(variantId);

        // Actualizar símbolos de moneda para la nueva variante
        updateVariantCurrencySymbols(variantCounter);

        // Agregar event listener para la imagen
        const imageInput = document.querySelector(`input[data-variant-id="${variantId}"]`);
        imageInput.addEventListener('change', function(e) {
            handleVariantImagePreview(e, variantId);
        });
    }

    function removeVariant(variantId) {
        if (variants.length <= 1) {
            alert('Debe mantener al menos una variante');
            return;
        }

        const variantCard = document.querySelector(`[data-variant-id="${variantId}"]`);
        variantCard.remove();

        variants = variants.filter(id => id !== variantId);

        // Renumerar variantes
        renumberVariants();
    }

    function renumberVariants() {
        const variantCards = document.querySelectorAll('.variant-card');
        variantCards.forEach((card, index) => {
            const newNumber = index + 1;
            const header = card.querySelector('.card-header h6');
            header.innerHTML = `<i class="fas fa-cube me-2"></i>Variante #${newNumber}`;
        });
    }

    function handleVariantImagePreview(event, variantId) {
        const file = event.target.files[0];
        const preview = document.getElementById(`variant-preview-${variantId}`);
        const img = preview.querySelector('img');

        if (file) {
            // Validar tamaño del archivo (2MB máximo)
            if (file.size > 2 * 1024 * 1024) {
                alert('El archivo es demasiado grande. El tamaño máximo permitido es 2MB.');
                event.target.value = '';
                return;
            }

            // Validar tipo de archivo
            if (!file.type.startsWith('image/')) {
                alert('Por favor selecciona un archivo de imagen válido.');
                event.target.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    }

    function removeVariantImage(variantId) {
        const input = document.querySelector(`input[data-variant-id="${variantId}"]`);
        const preview = document.getElementById(`variant-preview-${variantId}`);

        input.value = '';
        preview.style.display = 'none';
    }

    function updateVariantCurrencySymbols(variantNumber) {
        const currency = document.getElementById('currency').value;
        const symbol = currency === 'USD' ? '$' : 'Q';

        const regularSymbol = document.getElementById(`variant-symbol-regular-${variantNumber}`);
        const saleSymbol = document.getElementById(`variant-symbol-sale-${variantNumber}`);

        if (regularSymbol) regularSymbol.textContent = symbol;
        if (saleSymbol) saleSymbol.textContent = symbol;
    }

    // Actualizar símbolos de moneda en variantes cuando cambie la moneda principal
    const originalUpdateCurrencySymbols = updateCurrencySymbols;
    updateCurrencySymbols = function() {
        originalUpdateCurrencySymbols();

        // Actualizar símbolos en todas las variantes
        for (let i = 1; i <= variantCounter; i++) {
            updateVariantCurrencySymbols(i);
        }
    };
</script>

<style>
    /* Animación para nuevas imágenes */
    #gallery_preview_container .card {
        animation: fadeInUp 0.3s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<?= $this->endSection() ?>
