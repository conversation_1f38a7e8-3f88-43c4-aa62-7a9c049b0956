<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\BlockchainManager;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Blockchain y Criptomonedas
 * Panel de administración para gestión de blockchain, NFTs y tokens
 */
class BlockchainController extends BaseController
{
    private $blockchainManager;
    protected $logger;
    
    public function __construct()
    {
        $this->blockchainManager = new BlockchainManager();
        $this->logger = new AdvancedLogger();
    }
    
    /**
     * Dashboard de blockchain
     */
    public function index()
    {
        $data = [
            'title' => 'Panel de Blockchain - MrCell Guatemala',
            'crypto_prices' => $this->blockchainManager->getCryptoPrices(),
            'blockchain_stats' => $this->blockchainManager->getBlockchainStats(),
            'recent_transactions' => $this->getRecentTransactions(),
            'nft_stats' => $this->getNFTStats(),
            'token_stats' => $this->getTokenStats()
        ];
        
        return view('admin/blockchain/dashboard', $data);
    }
    
    /**
     * Gestión de criptomonedas
     */
    public function crypto()
    {
        $data = [
            'title' => 'Gestión de Criptomonedas - MrCell Guatemala',
            'supported_currencies' => $this->blockchainManager->getConfig()['supported_currencies'],
            'crypto_prices' => $this->blockchainManager->getCryptoPrices(),
            'pending_transactions' => $this->getPendingTransactions(),
            'confirmed_transactions' => $this->getConfirmedTransactions()
        ];
        
        return view('admin/blockchain/crypto', $data);
    }
    
    /**
     * Procesar pago con criptomoneda
     */
    public function processCryptoPayment()
    {
        try {
            $paymentData = [
                'order_id' => $this->request->getPost('order_id'),
                'amount' => (float)$this->request->getPost('amount'),
                'currency' => $this->request->getPost('currency')
            ];
            
            $result = $this->blockchainManager->processCryptoPayment($paymentData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Verificar estado de transacción
     */
    public function checkTransaction($transactionId)
    {
        try {
            $result = $this->blockchainManager->checkTransactionStatus($transactionId);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Gestión de NFTs
     */
    public function nfts()
    {
        $data = [
            'title' => 'Gestión de NFTs - MrCell Guatemala',
            'nfts' => $this->getAllNFTs(),
            'nft_stats' => $this->getNFTStats(),
            'products_eligible' => $this->getEligibleProducts()
        ];
        
        return view('admin/blockchain/nfts', $data);
    }
    
    /**
     * Crear NFT para producto
     */
    public function createNFT()
    {
        try {
            $nftData = [
                'product_id' => $this->request->getPost('product_id'),
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'image_url' => $this->request->getPost('image_url'),
                'price_eth' => (float)$this->request->getPost('price_eth'),
                'max_supply' => (int)$this->request->getPost('max_supply'),
                'attributes' => $this->request->getPost('attributes') ?? []
            ];
            
            $result = $this->blockchainManager->createProductNFT($nftData);
            
            if ($result['success']) {
                session()->setFlashdata('success', 'NFT creado exitosamente');
            } else {
                session()->setFlashdata('error', $result['error']);
            }
            
            return redirect()->to('/admin/blockchain/nfts');
            
        } catch (\Exception $e) {
            session()->setFlashdata('error', $e->getMessage());
            return redirect()->to('/admin/blockchain/nfts');
        }
    }
    
    /**
     * Gestión de tokens de fidelidad
     */
    public function loyaltyTokens()
    {
        $data = [
            'title' => 'Tokens de Fidelidad - MrCell Guatemala',
            'token_stats' => $this->getTokenStats(),
            'recent_token_transactions' => $this->getRecentTokenTransactions(),
            'top_holders' => $this->getTopTokenHolders(),
            'token_config' => $this->blockchainManager->getConfig()
        ];
        
        return view('admin/blockchain/loyalty_tokens', $data);
    }
    
    /**
     * Otorgar tokens manualmente
     */
    public function awardTokens()
    {
        try {
            $userId = (int)$this->request->getPost('user_id');
            $amount = (float)$this->request->getPost('amount');
            $reason = $this->request->getPost('reason') ?? 'manual_award';
            
            $result = $this->blockchainManager->awardLoyaltyTokens($userId, $amount, $reason);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Configuración de blockchain
     */
    public function settings()
    {
        $data = [
            'title' => 'Configuración de Blockchain - MrCell Guatemala',
            'config' => $this->blockchainManager->getConfig(),
            'crypto_prices' => $this->blockchainManager->getCryptoPrices()
        ];
        
        return view('admin/blockchain/settings', $data);
    }
    
    /**
     * Actualizar configuración
     */
    public function updateSettings()
    {
        try {
            // Aquí se actualizarían las configuraciones de blockchain
            // Por ahora solo simulamos
            
            session()->setFlashdata('success', 'Configuración actualizada exitosamente');
            return redirect()->to('/admin/blockchain/settings');
            
        } catch (\Exception $e) {
            session()->setFlashdata('error', $e->getMessage());
            return redirect()->to('/admin/blockchain/settings');
        }
    }
    
    /**
     * Estadísticas de blockchain
     */
    public function stats()
    {
        try {
            $days = (int)($this->request->getGet('days') ?? 7);
            $stats = $this->blockchainManager->getBlockchainStats($days);
            
            return $this->response->setJSON($stats);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Obtener precios de criptomonedas
     */
    public function getCryptoPrices()
    {
        try {
            $prices = $this->blockchainManager->getCryptoPrices();
            
            return $this->response->setJSON($prices);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Exportar datos de blockchain
     */
    public function export()
    {
        try {
            $type = $this->request->getPost('type') ?? 'transactions';
            $format = $this->request->getPost('format') ?? 'csv';
            
            switch ($type) {
                case 'transactions':
                    $data = $this->exportTransactions($format);
                    break;
                case 'nfts':
                    $data = $this->exportNFTs($format);
                    break;
                case 'tokens':
                    $data = $this->exportTokenTransactions($format);
                    break;
                default:
                    throw new \Exception("Unknown export type: $type");
            }
            
            return $this->response->setJSON([
                'success' => true,
                'download_url' => $data['url'],
                'filename' => $data['filename']
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getRecentTransactions(): array
    {
        return $this->db->table('crypto_transactions')
                       ->orderBy('created_at', 'DESC')
                       ->limit(10)
                       ->get()
                       ->getResultArray();
    }
    
    private function getPendingTransactions(): array
    {
        return $this->db->table('crypto_transactions')
                       ->where('status', 'pending')
                       ->orderBy('created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getConfirmedTransactions(): array
    {
        return $this->db->table('crypto_transactions')
                       ->where('status', 'confirmed')
                       ->orderBy('created_at', 'DESC')
                       ->limit(20)
                       ->get()
                       ->getResultArray();
    }
    
    private function getAllNFTs(): array
    {
        return $this->db->table('product_nfts pn')
                       ->select('pn.*, p.name as product_name')
                       ->join('products p', 'p.id = pn.product_id', 'left')
                       ->orderBy('pn.created_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
    
    private function getNFTStats(): array
    {
        $total = $this->db->table('product_nfts')->countAllResults();
        $minted = $this->db->table('product_nfts')->where('status', 'minted')->countAllResults();
        $sold = $this->db->table('product_nfts')->where('status', 'sold')->countAllResults();
        
        return [
            'total' => $total,
            'minted' => $minted,
            'sold' => $sold,
            'pending' => $total - $minted
        ];
    }
    
    private function getEligibleProducts(): array
    {
        return $this->db->table('products')
                       ->where('is_active', 1)
                       ->where('price >=', 100) // Solo productos premium
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getTokenStats(): array
    {
        $totalSupply = $this->db->table('loyalty_token_transactions')
                               ->where('type', 'earned')
                               ->selectSum('amount')
                               ->get()
                               ->getRowArray()['amount'] ?? 0;
        
        $totalRedeemed = $this->db->table('loyalty_token_transactions')
                                 ->where('type', 'redeemed')
                                 ->selectSum('amount')
                                 ->get()
                                 ->getRowArray()['amount'] ?? 0;
        
        $activeHolders = $this->db->table('user_token_balances')
                                 ->where('balance >', 0)
                                 ->countAllResults();
        
        return [
            'total_supply' => $totalSupply,
            'total_redeemed' => abs($totalRedeemed),
            'circulating_supply' => $totalSupply + $totalRedeemed, // $totalRedeemed es negativo
            'active_holders' => $activeHolders
        ];
    }
    
    private function getRecentTokenTransactions(): array
    {
        return $this->db->table('loyalty_token_transactions ltt')
                       ->select('ltt.*, u.name as user_name, u.email as user_email')
                       ->join('users u', 'u.id = ltt.user_id', 'left')
                       ->orderBy('ltt.created_at', 'DESC')
                       ->limit(20)
                       ->get()
                       ->getResultArray();
    }
    
    private function getTopTokenHolders(): array
    {
        return $this->db->table('user_token_balances utb')
                       ->select('utb.*, u.name as user_name, u.email as user_email')
                       ->join('users u', 'u.id = utb.user_id', 'left')
                       ->where('utb.balance >', 0)
                       ->orderBy('utb.balance', 'DESC')
                       ->limit(10)
                       ->get()
                       ->getResultArray();
    }
    
    private function exportTransactions(string $format): array
    {
        $transactions = $this->db->table('crypto_transactions')->get()->getResultArray();
        
        $filename = 'crypto_transactions_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if (!is_dir(WRITEPATH . 'exports/')) {
            mkdir(WRITEPATH . 'exports/', 0755, true);
        }
        
        if ($format === 'csv') {
            $this->exportToCSV($transactions, $filepath);
        } else {
            file_put_contents($filepath, json_encode($transactions, JSON_PRETTY_PRINT));
        }
        
        return [
            'filename' => $filename,
            'url' => base_url('exports/' . $filename)
        ];
    }
    
    private function exportNFTs(string $format): array
    {
        $nfts = $this->getAllNFTs();
        
        $filename = 'nfts_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if ($format === 'csv') {
            $this->exportToCSV($nfts, $filepath);
        } else {
            file_put_contents($filepath, json_encode($nfts, JSON_PRETTY_PRINT));
        }
        
        return [
            'filename' => $filename,
            'url' => base_url('exports/' . $filename)
        ];
    }
    
    private function exportTokenTransactions(string $format): array
    {
        $transactions = $this->getRecentTokenTransactions();
        
        $filename = 'token_transactions_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if ($format === 'csv') {
            $this->exportToCSV($transactions, $filepath);
        } else {
            file_put_contents($filepath, json_encode($transactions, JSON_PRETTY_PRINT));
        }
        
        return [
            'filename' => $filename,
            'url' => base_url('exports/' . $filename)
        ];
    }
    
    private function exportToCSV(array $data, string $filepath): void
    {
        if (empty($data)) return;
        
        $handle = fopen($filepath, 'w');
        
        // Headers
        fputcsv($handle, array_keys($data[0]));
        
        // Data
        foreach ($data as $row) {
            fputcsv($handle, array_values($row));
        }
        
        fclose($handle);
    }
}
