<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-ticket-alt me-2"></i>Dashboard de Cupones
    </h1>
    <div>
        <a href="<?= base_url('admin/coupons/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Crear <PERSON>
        </a>
        <a href="<?= base_url('admin/coupons/bulk-discounts') ?>" class="btn btn-info">
            <i class="fas fa-percentage me-2"></i>Descuentos por Volumen
        </a>
    </div>
</div>

<!-- Estadísticas Principales -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Cupones Activos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $coupon_stats['stats']['active_coupons'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-ticket-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Descuento Total Otorgado
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            Q<?= number_format($coupon_stats['stats']['total_discount_given'] ?? 0, 2) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Tasa de Conversión
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($coupon_stats['stats']['conversion_rate'] ?? 0, 1) ?>%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Cupones Usados (30 días)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count($recent_usage ?? []) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Cupones Más Usados -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Cupones Más Usados</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="<?= base_url('admin/coupons') ?>">Ver Todos</a>
                        <a class="dropdown-item" href="<?= base_url('admin/coupons/stats') ?>">Estadísticas Detalladas</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($coupon_stats['stats']['top_coupons'])): ?>
                    <?php foreach ($coupon_stats['stats']['top_coupons'] as $coupon): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fas fa-ticket-alt text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500"><?= esc($coupon['name']) ?></div>
                                <div class="font-weight-bold"><?= esc($coupon['code']) ?></div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500"><?= $coupon['uses'] ?> usos</div>
                                <div class="font-weight-bold text-success">Q<?= number_format($coupon['total_discount'], 2) ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-ticket-alt fa-3x text-gray-300 mb-3"></i>
                        <p class="text-gray-500">No hay datos de cupones disponibles</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Cupones Activos Recientes -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Cupones Activos</h6>
                <a href="<?= base_url('admin/coupons') ?>" class="btn btn-sm btn-primary">Ver Todos</a>
            </div>
            <div class="card-body">
                <?php if (!empty($active_coupons)): ?>
                    <?php foreach (array_slice($active_coupons, 0, 5) as $coupon): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <?php
                                $badgeClass = 'bg-success';
                                $icon = 'fa-percentage';
                                switch($coupon['type']) {
                                    case 'fixed': $badgeClass = 'bg-info'; $icon = 'fa-dollar-sign'; break;
                                    case 'free_shipping': $badgeClass = 'bg-warning'; $icon = 'fa-truck'; break;
                                    case 'buy_x_get_y': $badgeClass = 'bg-purple'; $icon = 'fa-gift'; break;
                                }
                                ?>
                                <div class="icon-circle <?= $badgeClass ?>">
                                    <i class="fas <?= $icon ?> text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold"><?= esc($coupon['code']) ?></div>
                                <div class="small text-gray-500"><?= esc($coupon['name']) ?></div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold">
                                    <?php if ($coupon['type'] === 'percentage'): ?>
                                        <?= $coupon['value'] ?>%
                                    <?php elseif ($coupon['type'] === 'fixed'): ?>
                                        Q<?= number_format($coupon['value'], 2) ?>
                                    <?php else: ?>
                                        <?= ucfirst(str_replace('_', ' ', $coupon['type'])) ?>
                                    <?php endif; ?>
                                </div>
                                <div class="small text-gray-500">
                                    <?php if ($coupon['valid_until']): ?>
                                        Vence: <?= date('d/m/Y', strtotime($coupon['valid_until'])) ?>
                                    <?php else: ?>
                                        Sin vencimiento
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-ticket-alt fa-3x text-gray-300 mb-3"></i>
                        <p class="text-gray-500">No hay cupones activos</p>
                        <a href="<?= base_url('admin/coupons/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Crear Primer Cupón
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Gráfico de Uso por Tipo -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Uso de Cupones por Tipo</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($coupon_stats['stats']['usage_by_type'])): ?>
                    <canvas id="couponTypeChart" width="100" height="30"></canvas>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                        <p class="text-gray-500">No hay datos suficientes para mostrar el gráfico</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Acciones Rápidas</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/coupons/create') ?>" class="btn btn-primary btn-block">
                            <i class="fas fa-plus mb-2"></i><br>
                            Crear Cupón
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/coupons/bulk-discounts') ?>" class="btn btn-info btn-block">
                            <i class="fas fa-percentage mb-2"></i><br>
                            Descuentos por Volumen
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/coupons/campaigns') ?>" class="btn btn-success btn-block">
                            <i class="fas fa-bullhorn mb-2"></i><br>
                            Campañas Promocionales
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/coupons/stats') ?>" class="btn btn-warning btn-block">
                            <i class="fas fa-chart-line mb-2"></i><br>
                            Estadísticas Detalladas
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Gráfico de uso por tipo de cupón
<?php if (!empty($coupon_stats['stats']['usage_by_type'])): ?>
const ctx = document.getElementById('couponTypeChart').getContext('2d');
const couponTypeChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: [
            <?php foreach ($coupon_stats['stats']['usage_by_type'] as $type): ?>
                '<?= ucfirst(str_replace('_', ' ', $type['type'])) ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($coupon_stats['stats']['usage_by_type'] as $type): ?>
                    <?= $type['uses'] ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ],
            hoverBackgroundColor: [
                '#2e59d9',
                '#17a673',
                '#2c9faf',
                '#dda20a',
                '#c0392b'
            ],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: true,
            position: 'bottom'
        },
        cutoutPercentage: 80,
    },
});
<?php endif; ?>
</script>
<?= $this->endSection() ?>
