<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\SearchOptimizer;
use App\Libraries\SimpleCache;

/**
 * API de Búsqueda Optimizada para cPanel
 * Compatible con hosting compartido
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SearchApiOptimized extends ResourceController
{
    protected $searchOptimizer;
    protected $format = 'json';
    
    public function __construct()
    {
        $this->searchOptimizer = new SearchOptimizer();
    }
    
    /**
     * Búsqueda principal de productos
     * GET /api/search
     * 
     * Parámetros:
     * - q: término de búsqueda
     * - category_id: filtro por categoría
     * - brand_id: filtro por marca
     * - min_price: precio mínimo
     * - max_price: precio máximo
     * - sort_by: ordenamiento (price_asc, price_desc, name, rating, newest, popular)
     * - in_stock: solo productos en stock (1/0)
     * - featured: solo productos destacados (1/0)
     * - min_rating: rating mínimo
     * - page: página (default: 1)
     * - limit: resultados por página (default: 20, max: 50)
     */
    public function index()
    {
        try {
            // Obtener parámetros
            $searchTerm = trim($this->request->getGet('q') ?? '');
            $page = max(1, (int) ($this->request->getGet('page') ?? 1));
            $limit = min(50, max(1, (int) ($this->request->getGet('limit') ?? 20)));
            $offset = ($page - 1) * $limit;
            
            // Construir filtros
            $filters = [
                'category_id' => $this->request->getGet('category_id'),
                'brand_id' => $this->request->getGet('brand_id'),
                'min_price' => $this->request->getGet('min_price'),
                'max_price' => $this->request->getGet('max_price'),
                'sort_by' => $this->request->getGet('sort_by'),
                'in_stock' => $this->request->getGet('in_stock'),
                'featured' => $this->request->getGet('featured'),
                'min_rating' => $this->request->getGet('min_rating')
            ];
            
            // Filtrar valores vacíos
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });
            
            // Realizar búsqueda
            $results = $this->searchOptimizer->search($searchTerm, $filters, $limit, $offset);
            
            // Procesar resultados para la respuesta
            $products = array_map([$this, 'formatProduct'], $results['products']);
            
            // Calcular información de paginación
            $totalPages = ceil($results['total'] / $limit);
            
            return $this->respond([
                'status' => 'success',
                'data' => [
                    'products' => $products,
                    'search_term' => $searchTerm,
                    'filters_applied' => $filters,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $results['total'],
                        'total_pages' => $totalPages,
                        'has_previous' => $page > 1,
                        'has_next' => $page < $totalPages,
                        'previous_page' => $page > 1 ? $page - 1 : null,
                        'next_page' => $page < $totalPages ? $page + 1 : null
                    ]
                ],
                'meta' => [
                    'execution_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 3),
                    'cached' => false // TODO: implementar detección de cache
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en SearchApiOptimized::index: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error interno del servidor',
                'error_code' => 'SEARCH_ERROR'
            ], 500);
        }
    }
    
    /**
     * Autocompletado de búsqueda
     * GET /api/search/suggestions
     * 
     * Parámetros:
     * - q: término parcial (mínimo 2 caracteres)
     * - limit: número de sugerencias (default: 10, max: 20)
     */
    public function suggestions()
    {
        try {
            $term = trim($this->request->getGet('q') ?? '');
            $limit = min(20, max(1, (int) ($this->request->getGet('limit') ?? 10)));
            
            if (strlen($term) < 2) {
                return $this->respond([
                    'status' => 'success',
                    'data' => [
                        'suggestions' => [],
                        'message' => 'Término de búsqueda muy corto (mínimo 2 caracteres)'
                    ]
                ]);
            }
            
            $suggestions = $this->searchOptimizer->getSuggestions($term, $limit);
            
            // Formatear sugerencias
            $formattedSuggestions = array_map(function($item) {
                return [
                    'name' => $item['name'],
                    'slug' => $item['slug'],
                    'price' => (float) $item['price'],
                    'price_formatted' => 'Q' . number_format($item['price'], 2),
                    'image' => $item['featured_image'] ? base_url('uploads/' . $item['featured_image']) : null,
                    'url' => base_url('producto/' . $item['slug'])
                ];
            }, $suggestions);
            
            return $this->respond([
                'status' => 'success',
                'data' => [
                    'suggestions' => $formattedSuggestions,
                    'term' => $term,
                    'count' => count($formattedSuggestions)
                ]
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en SearchApiOptimized::suggestions: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al obtener sugerencias',
                'error_code' => 'SUGGESTIONS_ERROR'
            ], 500);
        }
    }
    
    /**
     * Obtener filtros disponibles para una búsqueda
     * GET /api/search/filters
     * 
     * Parámetros:
     * - q: término de búsqueda (opcional)
     */
    public function filters()
    {
        try {
            $searchTerm = trim($this->request->getGet('q') ?? '');
            
            // Cache de filtros
            $cacheKey = 'search_filters_' . md5($searchTerm);
            
            $filters = SimpleCache::remember($cacheKey, function() use ($searchTerm) {
                
                $db = \Config\Database::connect();
                
                // Base query para productos que coinciden con la búsqueda
                $baseWhere = "p.is_active = 1 AND p.deleted_at IS NULL";
                $params = [];
                
                if (!empty($searchTerm)) {
                    $baseWhere .= " AND MATCH(p.name, p.description, p.sku, p.short_description) AGAINST(? IN BOOLEAN MODE)";
                    $params[] = $searchTerm;
                }
                
                // Obtener categorías disponibles
                $categoriesQuery = $db->query("
                    SELECT c.id, c.name, COUNT(p.id) as product_count
                    FROM categories c
                    INNER JOIN products p ON c.id = p.category_id
                    WHERE {$baseWhere}
                    GROUP BY c.id, c.name
                    ORDER BY c.name ASC
                ", $params);
                
                // Obtener marcas disponibles
                $brandsQuery = $db->query("
                    SELECT b.id, b.name, COUNT(p.id) as product_count
                    FROM brands b
                    INNER JOIN products p ON b.id = p.brand_id
                    WHERE {$baseWhere}
                    GROUP BY b.id, b.name
                    ORDER BY b.name ASC
                ", $params);
                
                // Obtener rango de precios
                $priceQuery = $db->query("
                    SELECT 
                        MIN(COALESCE(p.price_sale, p.price_regular)) as min_price,
                        MAX(COALESCE(p.price_sale, p.price_regular)) as max_price
                    FROM products p
                    WHERE {$baseWhere}
                ", $params);
                
                $priceRange = $priceQuery->getRowArray();
                
                return [
                    'categories' => $categoriesQuery->getResultArray(),
                    'brands' => $brandsQuery->getResultArray(),
                    'price_range' => [
                        'min' => (float) $priceRange['min_price'],
                        'max' => (float) $priceRange['max_price']
                    ],
                    'sort_options' => [
                        ['value' => 'relevance', 'label' => 'Más relevante'],
                        ['value' => 'price_asc', 'label' => 'Precio: menor a mayor'],
                        ['value' => 'price_desc', 'label' => 'Precio: mayor a menor'],
                        ['value' => 'name', 'label' => 'Nombre A-Z'],
                        ['value' => 'rating', 'label' => 'Mejor calificados'],
                        ['value' => 'newest', 'label' => 'Más recientes'],
                        ['value' => 'popular', 'label' => 'Más populares']
                    ]
                ];
                
            }, 3600); // Cache por 1 hora
            
            return $this->respond([
                'status' => 'success',
                'data' => $filters
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error en SearchApiOptimized::filters: ' . $e->getMessage());
            
            return $this->fail([
                'status' => 'error',
                'message' => 'Error al obtener filtros',
                'error_code' => 'FILTERS_ERROR'
            ], 500);
        }
    }
    
    /**
     * Formatear producto para la respuesta
     * 
     * @param array $product Datos del producto
     * @return array Producto formateado
     */
    private function formatProduct(array $product): array
    {
        $finalPrice = $product['price_sale'] ?? $product['price_regular'];
        $hasDiscount = !empty($product['price_sale']) && $product['price_sale'] < $product['price_regular'];
        
        return [
            'id' => (int) $product['id'],
            'name' => $product['name'],
            'slug' => $product['slug'],
            'sku' => $product['sku'],
            'short_description' => $product['short_description'],
            'category' => [
                'id' => (int) $product['category_id'],
                'name' => $product['category_name']
            ],
            'brand' => [
                'id' => (int) $product['brand_id'],
                'name' => $product['brand_name']
            ],
            'price' => [
                'regular' => (float) $product['price_regular'],
                'sale' => $product['price_sale'] ? (float) $product['price_sale'] : null,
                'final' => (float) $finalPrice,
                'formatted' => 'Q' . number_format($finalPrice, 2),
                'has_discount' => $hasDiscount,
                'discount_percentage' => $hasDiscount ? 
                    round((($product['price_regular'] - $product['price_sale']) / $product['price_regular']) * 100) : 0
            ],
            'stock' => [
                'quantity' => (int) $product['stock_quantity'],
                'status' => $product['stock_status'],
                'in_stock' => $product['stock_quantity'] > 0 && $product['stock_status'] === 'in_stock'
            ],
            'rating' => [
                'average' => (float) $product['rating_average'],
                'count' => (int) $product['rating_count']
            ],
            'image' => $product['featured_image'] ? base_url('uploads/' . $product['featured_image']) : null,
            'url' => base_url('producto/' . $product['slug']),
            'is_featured' => (bool) $product['is_featured'],
            'relevance_score' => isset($product['relevance_score']) ? (float) $product['relevance_score'] : 0
        ];
    }
}
