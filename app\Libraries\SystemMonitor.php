<?php

namespace App\Libraries;

use App\Libraries\WhatsAppNotifier;

/**
 * Sistema de Monitoreo y Alertas
 * Monitorea el rendimiento del sistema y envía alertas
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class SystemMonitor
{
    private $db;
    private $whatsapp;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->whatsapp = new WhatsAppNotifier();
        
        $this->config = [
            'enabled' => env('SYSTEM_MONITORING_ENABLED', true),
            'alert_thresholds' => [
                'response_time_ms' => env('ALERT_RESPONSE_TIME_MS', 5000),
                'memory_usage_percent' => env('ALERT_MEMORY_USAGE_PERCENT', 80),
                'disk_usage_percent' => env('ALERT_DISK_USAGE_PERCENT', 85),
                'error_rate_percent' => env('ALERT_ERROR_RATE_PERCENT', 5),
                'database_connections' => env('ALERT_DB_CONNECTIONS', 80)
            ],
            'notification_channels' => [
                'whatsapp' => env('MONITORING_WHATSAPP_ENABLED', true),
                'email' => env('MONITORING_EMAIL_ENABLED', false),
                'slack' => env('MONITORING_SLACK_ENABLED', false)
            ],
            'admin_contacts' => [
                'whatsapp' => env('ADMIN_WHATSAPP', '+50212345678'),
                'email' => env('ADMIN_EMAIL', '<EMAIL>'),
                'slack_webhook' => env('SLACK_WEBHOOK_URL', '')
            ],
            'check_interval' => env('MONITORING_CHECK_INTERVAL', 300), // 5 minutos
            'alert_cooldown' => env('MONITORING_ALERT_COOLDOWN', 1800) // 30 minutos
        ];
    }
    
    /**
     * Ejecutar monitoreo completo del sistema
     */
    public function runSystemCheck(): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'System monitoring disabled'];
        }
        
        $startTime = microtime(true);
        
        $checks = [
            'database' => $this->checkDatabase(),
            'performance' => $this->checkPerformance(),
            'disk_space' => $this->checkDiskSpace(),
            'memory_usage' => $this->checkMemoryUsage(),
            'error_rates' => $this->checkErrorRates(),
            'services' => $this->checkServices(),
            'security' => $this->checkSecurity()
        ];
        
        $alerts = [];
        $criticalIssues = 0;
        $warnings = 0;
        
        foreach ($checks as $checkName => $result) {
            if (isset($result['alerts'])) {
                foreach ($result['alerts'] as $alert) {
                    $alerts[] = array_merge($alert, ['check' => $checkName]);
                    
                    if ($alert['severity'] === 'critical') {
                        $criticalIssues++;
                    } elseif ($alert['severity'] === 'warning') {
                        $warnings++;
                    }
                }
            }
        }
        
        // Enviar alertas si es necesario
        if (!empty($alerts)) {
            $this->processAlerts($alerts);
        }
        
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        
        $summary = [
            'timestamp' => date('Y-m-d H:i:s'),
            'duration_ms' => $duration,
            'checks' => $checks,
            'alerts' => $alerts,
            'critical_issues' => $criticalIssues,
            'warnings' => $warnings,
            'overall_status' => $criticalIssues > 0 ? 'critical' : ($warnings > 0 ? 'warning' : 'healthy')
        ];
        
        // Log resultado
        $this->logMonitoringResult($summary);
        
        return $summary;
    }
    
    /**
     * Verificar estado de la base de datos
     */
    private function checkDatabase(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            $startTime = microtime(true);
            
            // Test de conectividad
            $this->db->query('SELECT 1');
            $connectionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $result['metrics']['connection_time_ms'] = $connectionTime;
            
            // Verificar conexiones activas
            $connections = $this->db->query("SHOW STATUS LIKE 'Threads_connected'")->getRowArray();
            $maxConnections = $this->db->query("SHOW VARIABLES LIKE 'max_connections'")->getRowArray();
            
            $activeConnections = (int) ($connections['Value'] ?? 0);
            $maxConnectionsLimit = (int) ($maxConnections['Value'] ?? 100);
            $connectionUsagePercent = ($activeConnections / $maxConnectionsLimit) * 100;
            
            $result['metrics']['active_connections'] = $activeConnections;
            $result['metrics']['max_connections'] = $maxConnectionsLimit;
            $result['metrics']['connection_usage_percent'] = round($connectionUsagePercent, 2);
            
            // Verificar consultas lentas
            $slowQueries = $this->db->query("SHOW STATUS LIKE 'Slow_queries'")->getRowArray();
            $result['metrics']['slow_queries'] = (int) ($slowQueries['Value'] ?? 0);
            
            // Verificar tamaño de base de datos
            $dbSize = $this->db->query("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            ")->getRowArray();
            
            $result['metrics']['database_size_mb'] = (float) ($dbSize['size_mb'] ?? 0);
            
            // Alertas
            if ($connectionTime > 1000) {
                $result['alerts'][] = [
                    'type' => 'performance',
                    'severity' => 'warning',
                    'message' => "Database connection time is high: {$connectionTime}ms",
                    'metric' => 'connection_time_ms',
                    'value' => $connectionTime,
                    'threshold' => 1000
                ];
            }
            
            if ($connectionUsagePercent > $this->config['alert_thresholds']['database_connections']) {
                $result['alerts'][] = [
                    'type' => 'performance',
                    'severity' => 'critical',
                    'message' => "Database connection usage is high: {$connectionUsagePercent}%",
                    'metric' => 'connection_usage_percent',
                    'value' => $connectionUsagePercent,
                    'threshold' => $this->config['alert_thresholds']['database_connections']
                ];
                $result['status'] = 'critical';
            }
            
        } catch (\Exception $e) {
            $result['status'] = 'critical';
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'critical',
                'message' => "Database connection failed: " . $e->getMessage(),
                'metric' => 'connection_status',
                'value' => 'failed'
            ];
        }
        
        return $result;
    }
    
    /**
     * Verificar rendimiento del sistema
     */
    private function checkPerformance(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            // Tiempo de respuesta promedio (últimas 24 horas)
            $avgResponseTime = $this->getAverageResponseTime();
            $result['metrics']['avg_response_time_ms'] = $avgResponseTime;
            
            // Carga del servidor
            if (function_exists('sys_getloadavg')) {
                $load = sys_getloadavg();
                $result['metrics']['server_load_1min'] = $load[0];
                $result['metrics']['server_load_5min'] = $load[1];
                $result['metrics']['server_load_15min'] = $load[2];
            }
            
            // Tiempo de actividad
            if (file_exists('/proc/uptime')) {
                $uptime = file_get_contents('/proc/uptime');
                $uptimeSeconds = (float) explode(' ', $uptime)[0];
                $result['metrics']['uptime_hours'] = round($uptimeSeconds / 3600, 2);
            }
            
            // Alertas de rendimiento
            if ($avgResponseTime > $this->config['alert_thresholds']['response_time_ms']) {
                $result['alerts'][] = [
                    'type' => 'performance',
                    'severity' => 'warning',
                    'message' => "Average response time is high: {$avgResponseTime}ms",
                    'metric' => 'avg_response_time_ms',
                    'value' => $avgResponseTime,
                    'threshold' => $this->config['alert_thresholds']['response_time_ms']
                ];
                $result['status'] = 'warning';
            }
            
            if (isset($load) && $load[0] > 2.0) {
                $result['alerts'][] = [
                    'type' => 'performance',
                    'severity' => 'warning',
                    'message' => "Server load is high: {$load[0]}",
                    'metric' => 'server_load_1min',
                    'value' => $load[0],
                    'threshold' => 2.0
                ];
                $result['status'] = 'warning';
            }
            
        } catch (\Exception $e) {
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'warning',
                'message' => "Performance check failed: " . $e->getMessage()
            ];
        }
        
        return $result;
    }
    
    /**
     * Verificar espacio en disco
     */
    private function checkDiskSpace(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            $rootPath = ROOTPATH;
            $totalBytes = disk_total_space($rootPath);
            $freeBytes = disk_free_space($rootPath);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = ($usedBytes / $totalBytes) * 100;
            
            $result['metrics']['total_space_gb'] = round($totalBytes / (1024**3), 2);
            $result['metrics']['free_space_gb'] = round($freeBytes / (1024**3), 2);
            $result['metrics']['used_space_gb'] = round($usedBytes / (1024**3), 2);
            $result['metrics']['usage_percent'] = round($usagePercent, 2);
            
            // Alertas de espacio en disco
            if ($usagePercent > $this->config['alert_thresholds']['disk_usage_percent']) {
                $severity = $usagePercent > 95 ? 'critical' : 'warning';
                
                $result['alerts'][] = [
                    'type' => 'system',
                    'severity' => $severity,
                    'message' => "Disk usage is high: {$usagePercent}%",
                    'metric' => 'disk_usage_percent',
                    'value' => $usagePercent,
                    'threshold' => $this->config['alert_thresholds']['disk_usage_percent']
                ];
                
                $result['status'] = $severity;
            }
            
        } catch (\Exception $e) {
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'warning',
                'message' => "Disk space check failed: " . $e->getMessage()
            ];
        }
        
        return $result;
    }
    
    /**
     * Verificar uso de memoria
     */
    private function checkMemoryUsage(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            $memoryLimit = ini_get('memory_limit');
            $memoryUsage = memory_get_usage(true);
            $peakMemoryUsage = memory_get_peak_usage(true);
            
            // Convertir memory_limit a bytes
            $memoryLimitBytes = $this->convertToBytes($memoryLimit);
            $memoryUsagePercent = ($memoryUsage / $memoryLimitBytes) * 100;
            
            $result['metrics']['memory_limit_mb'] = round($memoryLimitBytes / (1024**2), 2);
            $result['metrics']['memory_usage_mb'] = round($memoryUsage / (1024**2), 2);
            $result['metrics']['peak_memory_usage_mb'] = round($peakMemoryUsage / (1024**2), 2);
            $result['metrics']['memory_usage_percent'] = round($memoryUsagePercent, 2);
            
            // Verificar memoria del sistema (Linux)
            if (file_exists('/proc/meminfo')) {
                $meminfo = file_get_contents('/proc/meminfo');
                preg_match('/MemTotal:\s+(\d+) kB/', $meminfo, $totalMatch);
                preg_match('/MemAvailable:\s+(\d+) kB/', $meminfo, $availableMatch);
                
                if ($totalMatch && $availableMatch) {
                    $totalSystemMemory = (int) $totalMatch[1] * 1024;
                    $availableSystemMemory = (int) $availableMatch[1] * 1024;
                    $usedSystemMemory = $totalSystemMemory - $availableSystemMemory;
                    $systemMemoryUsagePercent = ($usedSystemMemory / $totalSystemMemory) * 100;
                    
                    $result['metrics']['system_memory_total_gb'] = round($totalSystemMemory / (1024**3), 2);
                    $result['metrics']['system_memory_usage_percent'] = round($systemMemoryUsagePercent, 2);
                }
            }
            
            // Alertas de memoria
            if ($memoryUsagePercent > $this->config['alert_thresholds']['memory_usage_percent']) {
                $result['alerts'][] = [
                    'type' => 'performance',
                    'severity' => 'warning',
                    'message' => "PHP memory usage is high: {$memoryUsagePercent}%",
                    'metric' => 'memory_usage_percent',
                    'value' => $memoryUsagePercent,
                    'threshold' => $this->config['alert_thresholds']['memory_usage_percent']
                ];
                $result['status'] = 'warning';
            }
            
        } catch (\Exception $e) {
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'warning',
                'message' => "Memory usage check failed: " . $e->getMessage()
            ];
        }
        
        return $result;
    }
    
    /**
     * Verificar tasas de error
     */
    private function checkErrorRates(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            // Contar errores en logs (últimas 24 horas)
            $logPath = WRITEPATH . 'logs';
            $errorCount = 0;
            $totalRequests = 0;
            
            if (is_dir($logPath)) {
                $logFiles = glob($logPath . '/log-*.php');
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                $today = date('Y-m-d');
                
                foreach ($logFiles as $logFile) {
                    if (strpos($logFile, $yesterday) !== false || strpos($logFile, $today) !== false) {
                        $content = file_get_contents($logFile);
                        $errorCount += substr_count($content, 'ERROR');
                        $errorCount += substr_count($content, 'CRITICAL');
                        $totalRequests += substr_count($content, 'INFO');
                    }
                }
            }
            
            $errorRate = $totalRequests > 0 ? ($errorCount / $totalRequests) * 100 : 0;
            
            $result['metrics']['error_count_24h'] = $errorCount;
            $result['metrics']['total_requests_24h'] = $totalRequests;
            $result['metrics']['error_rate_percent'] = round($errorRate, 2);
            
            // Alertas de tasa de error
            if ($errorRate > $this->config['alert_thresholds']['error_rate_percent']) {
                $result['alerts'][] = [
                    'type' => 'error',
                    'severity' => 'warning',
                    'message' => "Error rate is high: {$errorRate}%",
                    'metric' => 'error_rate_percent',
                    'value' => $errorRate,
                    'threshold' => $this->config['alert_thresholds']['error_rate_percent']
                ];
                $result['status'] = 'warning';
            }
            
        } catch (\Exception $e) {
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'warning',
                'message' => "Error rate check failed: " . $e->getMessage()
            ];
        }
        
        return $result;
    }
    
    /**
     * Verificar servicios críticos
     */
    private function checkServices(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        $services = [
            'whatsapp_api' => env('WHATSAPP_API_URL', ''),
            'payment_gateway' => env('PAYMENT_GATEWAY_URL', ''),
            'cdn' => env('CDN_URL', ''),
            'analytics' => 'https://www.google-analytics.com'
        ];
        
        foreach ($services as $serviceName => $serviceUrl) {
            if (empty($serviceUrl)) continue;
            
            try {
                $startTime = microtime(true);
                
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => $serviceUrl,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 10,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_SSL_VERIFYPEER => false
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                curl_close($ch);
                
                $result['metrics'][$serviceName] = [
                    'status' => $httpCode >= 200 && $httpCode < 400 ? 'up' : 'down',
                    'response_time_ms' => $responseTime,
                    'http_code' => $httpCode
                ];
                
                if ($httpCode < 200 || $httpCode >= 400) {
                    $result['alerts'][] = [
                        'type' => 'service',
                        'severity' => 'critical',
                        'message' => "Service {$serviceName} is down (HTTP {$httpCode})",
                        'metric' => $serviceName . '_status',
                        'value' => 'down'
                    ];
                    $result['status'] = 'critical';
                }
                
            } catch (\Exception $e) {
                $result['metrics'][$serviceName] = [
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                
                $result['alerts'][] = [
                    'type' => 'service',
                    'severity' => 'critical',
                    'message' => "Service {$serviceName} check failed: " . $e->getMessage(),
                    'metric' => $serviceName . '_status',
                    'value' => 'error'
                ];
                $result['status'] = 'critical';
            }
        }
        
        return $result;
    }
    
    /**
     * Verificar seguridad básica
     */
    private function checkSecurity(): array
    {
        $result = [
            'status' => 'healthy',
            'metrics' => [],
            'alerts' => []
        ];
        
        try {
            // Verificar permisos de archivos críticos
            $criticalFiles = [
                '.env' => ROOTPATH . '.env',
                'writable' => WRITEPATH,
                'public' => FCPATH
            ];
            
            foreach ($criticalFiles as $name => $path) {
                if (file_exists($path)) {
                    $permissions = substr(sprintf('%o', fileperms($path)), -4);
                    $result['metrics'][$name . '_permissions'] = $permissions;
                    
                    // Verificar permisos inseguros
                    if ($name === '.env' && $permissions !== '0600') {
                        $result['alerts'][] = [
                            'type' => 'security',
                            'severity' => 'warning',
                            'message' => ".env file has insecure permissions: {$permissions}",
                            'metric' => 'env_permissions',
                            'value' => $permissions,
                            'recommended' => '0600'
                        ];
                        $result['status'] = 'warning';
                    }
                }
            }
            
            // Verificar configuración PHP
            $phpConfig = [
                'expose_php' => ini_get('expose_php'),
                'display_errors' => ini_get('display_errors'),
                'log_errors' => ini_get('log_errors')
            ];
            
            $result['metrics']['php_config'] = $phpConfig;
            
            if ($phpConfig['expose_php']) {
                $result['alerts'][] = [
                    'type' => 'security',
                    'severity' => 'warning',
                    'message' => "PHP version is exposed in headers",
                    'metric' => 'expose_php',
                    'value' => 'enabled',
                    'recommended' => 'disabled'
                ];
                $result['status'] = 'warning';
            }
            
            if ($phpConfig['display_errors'] && ENVIRONMENT === 'production') {
                $result['alerts'][] = [
                    'type' => 'security',
                    'severity' => 'critical',
                    'message' => "Display errors is enabled in production",
                    'metric' => 'display_errors',
                    'value' => 'enabled',
                    'recommended' => 'disabled'
                ];
                $result['status'] = 'critical';
            }
            
        } catch (\Exception $e) {
            $result['alerts'][] = [
                'type' => 'error',
                'severity' => 'warning',
                'message' => "Security check failed: " . $e->getMessage()
            ];
        }
        
        return $result;
    }
    
    /**
     * Procesar y enviar alertas
     */
    private function processAlerts(array $alerts): void
    {
        foreach ($alerts as $alert) {
            // Verificar cooldown
            if ($this->isAlertInCooldown($alert)) {
                continue;
            }
            
            // Enviar alerta
            $this->sendAlert($alert);
            
            // Registrar alerta en BD
            $this->logAlert($alert);
        }
    }
    
    /**
     * Verificar si la alerta está en cooldown
     */
    private function isAlertInCooldown(array $alert): bool
    {
        try {
            $cooldownTime = date('Y-m-d H:i:s', time() - $this->config['alert_cooldown']);
            
            $recent = $this->db->table('system_alerts')
                             ->where('alert_type', $alert['type'])
                             ->where('title', $alert['message'])
                             ->where('created_at >', $cooldownTime)
                             ->countAllResults();
            
            return $recent > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Enviar alerta
     */
    private function sendAlert(array $alert): void
    {
        try {
            $message = $this->formatAlertMessage($alert);
            
            // WhatsApp
            if ($this->config['notification_channels']['whatsapp'] && 
                !empty($this->config['admin_contacts']['whatsapp'])) {
                
                $this->whatsapp->sendCustomMessage(
                    $this->config['admin_contacts']['whatsapp'],
                    $message
                );
            }
            
            // Email (implementar si es necesario)
            if ($this->config['notification_channels']['email'] && 
                !empty($this->config['admin_contacts']['email'])) {
                // Implementar envío de email
            }
            
            // Slack (implementar si es necesario)
            if ($this->config['notification_channels']['slack'] && 
                !empty($this->config['admin_contacts']['slack_webhook'])) {
                // Implementar envío a Slack
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Failed to send system alert: ' . $e->getMessage());
        }
    }
    
    /**
     * Formatear mensaje de alerta
     */
    private function formatAlertMessage(array $alert): string
    {
        $severityEmojis = [
            'critical' => '🚨',
            'warning' => '⚠️',
            'info' => 'ℹ️'
        ];
        
        $emoji = $severityEmojis[$alert['severity']] ?? '📊';
        
        $message = "{$emoji} **ALERTA DEL SISTEMA**\n\n";
        $message .= "**Severidad:** " . strtoupper($alert['severity']) . "\n";
        $message .= "**Tipo:** " . ucfirst($alert['type']) . "\n";
        $message .= "**Mensaje:** {$alert['message']}\n";
        
        if (isset($alert['metric'])) {
            $message .= "**Métrica:** {$alert['metric']}\n";
        }
        
        if (isset($alert['value'])) {
            $message .= "**Valor actual:** {$alert['value']}\n";
        }
        
        if (isset($alert['threshold'])) {
            $message .= "**Umbral:** {$alert['threshold']}\n";
        }
        
        $message .= "\n**Timestamp:** " . date('Y-m-d H:i:s') . "\n";
        $message .= "**Servidor:** " . gethostname() . "\n";
        
        return $message;
    }
    
    /**
     * Registrar alerta en base de datos
     */
    private function logAlert(array $alert): void
    {
        try {
            $this->db->table('system_alerts')->insert([
                'alert_type' => $alert['type'],
                'severity' => $alert['severity'],
                'title' => $alert['message'],
                'message' => $this->formatAlertMessage($alert),
                'data' => json_encode($alert),
                'notification_sent' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log system alert: ' . $e->getMessage());
        }
    }
    
    /**
     * Log resultado del monitoreo
     */
    private function logMonitoringResult(array $result): void
    {
        try {
            $this->db->table('system_metrics')->insert([
                'date' => date('Y-m-d'),
                'metric_name' => 'system_health_check',
                'metric_value' => $result['critical_issues'] + $result['warnings'],
                'metric_unit' => 'issues',
                'category' => 'monitoring',
                'metadata' => json_encode([
                    'duration_ms' => $result['duration_ms'],
                    'overall_status' => $result['overall_status'],
                    'critical_issues' => $result['critical_issues'],
                    'warnings' => $result['warnings']
                ]),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log monitoring result: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener tiempo de respuesta promedio
     */
    private function getAverageResponseTime(): float
    {
        // Mock data - implementar con métricas reales
        return 250.5;
    }
    
    /**
     * Convertir string de memoria a bytes
     */
    private function convertToBytes(string $memoryLimit): int
    {
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }
    
    /**
     * Obtener estadísticas de monitoreo
     */
    public function getMonitoringStats(int $days = 7): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $alertStats = $this->db->table('system_alerts')
                                 ->select('
                                     alert_type,
                                     severity,
                                     COUNT(*) as count
                                 ')
                                 ->where('created_at >=', $startDate)
                                 ->groupBy('alert_type, severity')
                                 ->get()
                                 ->getResultArray();
            
            $healthChecks = $this->db->table('system_metrics')
                                   ->select('
                                       DATE(created_at) as date,
                                       JSON_EXTRACT(metadata, "$.overall_status") as status,
                                       COUNT(*) as checks
                                   ')
                                   ->where('metric_name', 'system_health_check')
                                   ->where('created_at >=', $startDate)
                                   ->groupBy('DATE(created_at), JSON_EXTRACT(metadata, "$.overall_status")')
                                   ->get()
                                   ->getResultArray();
            
            return [
                'period_days' => $days,
                'alert_stats' => $alertStats,
                'health_checks' => $healthChecks,
                'total_alerts' => array_sum(array_column($alertStats, 'count')),
                'total_checks' => array_sum(array_column($healthChecks, 'checks'))
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
