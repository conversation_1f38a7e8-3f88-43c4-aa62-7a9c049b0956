<?php

namespace App\Controllers\Api;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;

/**
 * API Controller para Productos Frontend
 *
 * Maneja la obtención de productos para el frontend de la tienda
 */
class ProductController extends Controller
{
    use ResponseTrait;

    protected $format = 'json';

    /**
     * GET /api/products
     * Obtener lista de productos
     */
    public function index()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $limit = $this->request->getGet('limit') ?? 12;
            $category = $this->request->getGet('category');
            $search = $this->request->getGet('search');
            $sort = $this->request->getGet('sort') ?? 'name';
            $order = $this->request->getGet('order') ?? 'asc';

            $db = \Config\Database::connect();
            $builder = $db->table('products p');

            // Join con categorías y marcas
            $builder->select('p.*, c.name as category_name, c.slug as category_slug')
                   ->join('categories c', 'c.id = p.category_id', 'left')
                   ->where('p.is_active', 1)
                   ->where('p.deleted_at IS NULL');

            // Aplicar filtros
            if ($category) {
                $builder->where('p.category_id', $category);
            }

            if ($search) {
                $builder->groupStart()
                       ->like('p.name', $search)
                       ->orLike('p.short_description', $search)
                       ->orLike('p.description', $search)
                       ->groupEnd();
            }

            // Contar total para paginación
            $totalProducts = $builder->countAllResults(false);

            // Aplicar ordenamiento
            $validSorts = ['name', 'price_regular', 'created_at'];
            if (in_array($sort, $validSorts)) {
                $builder->orderBy('p.' . $sort, $order);
            } else {
                $builder->orderBy('p.is_featured', 'DESC')
                       ->orderBy('p.created_at', 'DESC');
            }

            // Aplicar paginación
            $offset = ($page - 1) * $limit;
            $builder->limit($limit, $offset);

            $products = $builder->get()->getResultArray();

            // Formatear productos
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['stock'] = $product['stock_quantity'];
                $product['featured'] = (bool)$product['is_featured'];

                // Calcular descuento si hay precio de oferta
                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            // Obtener categorías para filtros
            $categories = $this->getCategories();

            return $this->respond([
                'success' => true,
                'data' => array_values($products),
                'pagination' => [
                    'current_page' => (int)$page,
                    'per_page' => (int)$limit,
                    'total' => $totalProducts,
                    'total_pages' => ceil($totalProducts / $limit)
                ],
                'filters' => [
                    'categories' => $categories
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductController::index: ' . $e->getMessage());
            return $this->failServerError('Error interno del servidor');
        }
    }

    /**
     * GET /api/products/{id}
     * Obtener producto específico
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de producto requerido');
            }

            $db = \Config\Database::connect();

            // Obtener producto con información de categoría
            $product = $db->table('products p')
                         ->select('p.*, c.name as category_name, c.slug as category_slug')
                         ->join('categories c', 'c.id = p.category_id', 'left')
                         ->where('p.id', $id)
                         ->where('p.is_active', 1)
                         ->where('p.deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if (!$product) {
                return $this->failNotFound('Producto no encontrado');
            }

            // Formatear producto
            $product['price'] = $product['price_regular'];
            $product['sale_price'] = $product['price_sale'];
            $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
            $product['stock'] = $product['stock_quantity'];
            $product['featured'] = (bool)$product['is_featured'];

            // Calcular descuento si hay precio de oferta
            if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
            }

            // Obtener productos relacionados de la misma categoría
            $relatedProducts = $db->table('products p')
                                 ->select('p.*, c.name as category_name')
                                 ->join('categories c', 'c.id = p.category_id', 'left')
                                 ->where('p.category_id', $product['category_id'])
                                 ->where('p.id !=', $id)
                                 ->where('p.is_active', 1)
                                 ->where('p.deleted_at IS NULL')
                                 ->limit(4)
                                 ->get()
                                 ->getResultArray();

            // Formatear productos relacionados
            foreach ($relatedProducts as &$relatedProduct) {
                $relatedProduct['price'] = $relatedProduct['price_regular'];
                $relatedProduct['sale_price'] = $relatedProduct['price_sale'];
                $relatedProduct['image'] = $relatedProduct['featured_image'] ? base_url($relatedProduct['featured_image']) : null;
                $relatedProduct['stock'] = $relatedProduct['stock_quantity'];
            }

            return $this->respond([
                'success' => true,
                'data' => $product,
                'related_products' => $relatedProducts
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductController::show: ' . $e->getMessage());
            return $this->failServerError('Error interno del servidor');
        }
    }

    /**
     * GET /api/products/featured
     * Obtener productos destacados
     */
    public function featured()
    {
        try {
            $limit = $this->request->getGet('limit') ?? 8;

            $db = \Config\Database::connect();

            $products = $db->table('products p')
                          ->select('p.*, c.name as category_name, c.slug as category_slug')
                          ->join('categories c', 'c.id = p.category_id', 'left')
                          ->where('p.is_active', 1)
                          ->where('p.is_featured', 1)
                          ->where('p.deleted_at IS NULL')
                          ->orderBy('p.created_at', 'DESC')
                          ->limit($limit)
                          ->get()
                          ->getResultArray();

            // Formatear productos
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['stock'] = $product['stock_quantity'];
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $this->respond([
                'success' => true,
                'data' => array_values($products)
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductController::featured: ' . $e->getMessage());
            return $this->failServerError('Error interno del servidor');
        }
    }

    /**
     * GET /api/products/search
     * Búsqueda avanzada de productos
     */
    public function search()
    {
        try {
            $query = $this->request->getGet('q');
            $category = $this->request->getGet('category');
            $minPrice = $this->request->getGet('min_price');
            $maxPrice = $this->request->getGet('max_price');
            $brand = $this->request->getGet('brand');

            if (!$query) {
                return $this->failValidationError('Término de búsqueda requerido');
            }

            $db = \Config\Database::connect();
            $builder = $db->table('products p');

            $builder->select('p.*, c.name as category_name, c.slug as category_slug')
                   ->join('categories c', 'c.id = p.category_id', 'left')
                   ->where('p.is_active', 1)
                   ->where('p.deleted_at IS NULL');

            // Filtrar por término de búsqueda
            $builder->groupStart()
                   ->like('p.name', $query)
                   ->orLike('p.short_description', $query)
                   ->orLike('p.description', $query)
                   ->orLike('p.sku', $query)
                   ->groupEnd();

            // Aplicar filtros adicionales
            if ($category) {
                $builder->where('p.category_id', $category);
            }

            if ($minPrice) {
                $builder->where('p.price_regular >=', $minPrice);
            }

            if ($maxPrice) {
                $builder->where('p.price_regular <=', $maxPrice);
            }

            $results = $builder->orderBy('p.is_featured', 'DESC')
                              ->orderBy('p.name', 'ASC')
                              ->get()
                              ->getResultArray();

            // Formatear productos
            foreach ($results as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['stock'] = $product['stock_quantity'];
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $this->respond([
                'success' => true,
                'data' => array_values($results),
                'total' => count($results),
                'query' => $query
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en ProductController::search: ' . $e->getMessage());
            return $this->failServerError('Error interno del servidor');
        }
    }

    /**
     * Obtener productos de ejemplo
     */
    private function getExampleProducts()
    {
        return [
            [
                'id' => 1,
                'name' => 'iPhone 15 Pro',
                'slug' => 'iphone-15-pro',
                'short_description' => 'El iPhone más avanzado con chip A17 Pro',
                'description' => 'iPhone 15 Pro con pantalla Super Retina XDR de 6.1 pulgadas, chip A17 Pro, sistema de cámaras Pro y Action Button.',
                'price' => 8999.00,
                'sale_price' => 8499.00,
                'sku' => 'IPH15PRO-128',
                'stock' => 25,
                'category_id' => 1,
                'category_name' => 'Smartphones',
                'brand' => 'Apple',
                'featured' => true,
                'image' => 'https://via.placeholder.com/400x400/007bff/ffffff?text=iPhone+15+Pro',
                'gallery' => [
                    'https://via.placeholder.com/400x400/007bff/ffffff?text=iPhone+15+Pro+1',
                    'https://via.placeholder.com/400x400/007bff/ffffff?text=iPhone+15+Pro+2'
                ],
                'specifications' => [
                    'Pantalla' => '6.1" Super Retina XDR',
                    'Procesador' => 'A17 Pro',
                    'Almacenamiento' => '128GB',
                    'Cámara' => '48MP + 12MP + 12MP',
                    'Batería' => 'Hasta 23 horas de video'
                ],
                'rating' => 4.8,
                'reviews_count' => 156,
                'created_at' => '2024-01-15 10:00:00'
            ],
            [
                'id' => 2,
                'name' => 'Samsung Galaxy S24 Ultra',
                'slug' => 'samsung-galaxy-s24-ultra',
                'short_description' => 'El Galaxy más potente con S Pen integrado',
                'description' => 'Samsung Galaxy S24 Ultra con pantalla Dynamic AMOLED 2X de 6.8 pulgadas, procesador Snapdragon 8 Gen 3 y S Pen integrado.',
                'price' => 7999.00,
                'sale_price' => 7499.00,
                'sku' => 'SAM-S24U-256',
                'stock' => 18,
                'category_id' => 1,
                'category_name' => 'Smartphones',
                'brand' => 'Samsung',
                'featured' => true,
                'image' => 'https://via.placeholder.com/400x400/28a745/ffffff?text=Galaxy+S24+Ultra',
                'gallery' => [
                    'https://via.placeholder.com/400x400/28a745/ffffff?text=Galaxy+S24+Ultra+1',
                    'https://via.placeholder.com/400x400/28a745/ffffff?text=Galaxy+S24+Ultra+2'
                ],
                'specifications' => [
                    'Pantalla' => '6.8" Dynamic AMOLED 2X',
                    'Procesador' => 'Snapdragon 8 Gen 3',
                    'Almacenamiento' => '256GB',
                    'Cámara' => '200MP + 50MP + 12MP + 10MP',
                    'Batería' => '5000mAh'
                ],
                'rating' => 4.7,
                'reviews_count' => 89,
                'created_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 3,
                'name' => 'Xiaomi 14 Pro',
                'slug' => 'xiaomi-14-pro',
                'short_description' => 'Potencia y elegancia en un solo dispositivo',
                'description' => 'Xiaomi 14 Pro con pantalla AMOLED de 6.73 pulgadas, procesador Snapdragon 8 Gen 3 y cámara Leica.',
                'price' => 4999.00,
                'sale_price' => null,
                'sku' => 'XIA-14PRO-512',
                'stock' => 32,
                'category_id' => 1,
                'category_name' => 'Smartphones',
                'brand' => 'Xiaomi',
                'featured' => false,
                'image' => 'https://via.placeholder.com/400x400/ffc107/000000?text=Xiaomi+14+Pro',
                'gallery' => [
                    'https://via.placeholder.com/400x400/ffc107/000000?text=Xiaomi+14+Pro+1'
                ],
                'specifications' => [
                    'Pantalla' => '6.73" AMOLED',
                    'Procesador' => 'Snapdragon 8 Gen 3',
                    'Almacenamiento' => '512GB',
                    'Cámara' => '50MP Leica + 50MP + 50MP',
                    'Batería' => '4880mAh'
                ],
                'rating' => 4.6,
                'reviews_count' => 45,
                'created_at' => '2024-02-01 09:15:00'
            ]
        ];
    }

    /**
     * Obtener categorías de la base de datos
     */
    private function getCategories()
    {
        try {
            $db = \Config\Database::connect();

            $categories = $db->table('categories')
                            ->select('id, name, slug, icon')
                            ->where('is_active', 1)
                            ->orderBy('sort_order', 'ASC')
                            ->orderBy('name', 'ASC')
                            ->get()
                            ->getResultArray();

            return $categories;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo categorías: ' . $e->getMessage());
            return [];
        }
    }
}
