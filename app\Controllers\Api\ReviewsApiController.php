<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use App\Models\ProductReviewModel;

class ReviewsApiController extends ResourceController
{
    protected $modelName = 'App\Models\ProductReviewModel';
    protected $format = 'json';

    public function __construct()
    {
        // Enable CORS for API
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');

        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit(0);
        }
    }

    /**
     * Get reviews for a product
     * GET /api/reviews/product/{product_id}
     */
    public function getProductReviews($productId = null)
    {
        if (!$productId) {
            return $this->failValidationError('Product ID is required');
        }

        try {
            $page = $this->request->getGet('page') ?? 1;
            $limit = $this->request->getGet('limit') ?? 10;
            $offset = ($page - 1) * $limit;

            $db = \Config\Database::connect();

            // Get reviews
            $reviews = $db->table('product_reviews')
                         ->select('*')
                         ->where('product_id', $productId)
                         ->where('is_approved', 1)
                         ->where('deleted_at IS NULL')
                         ->orderBy('is_featured', 'DESC')
                         ->orderBy('created_at', 'DESC')
                         ->limit($limit, $offset)
                         ->get()
                         ->getResultArray();

            // Get rating statistics
            $stats = $db->table('product_reviews')
                       ->select('
                           AVG(rating) as average_rating,
                           COUNT(*) as total_reviews,
                           SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as rating_5,
                           SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as rating_4,
                           SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as rating_3,
                           SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as rating_2,
                           SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as rating_1
                       ')
                       ->where('product_id', $productId)
                       ->where('is_approved', 1)
                       ->where('deleted_at IS NULL')
                       ->get()
                       ->getRowArray();

            if ($stats) {
                $stats['average_rating'] = round($stats['average_rating'], 1);

                // Calculate percentages
                if ($stats['total_reviews'] > 0) {
                    for ($i = 1; $i <= 5; $i++) {
                        $stats["rating_{$i}_percent"] = round(($stats["rating_{$i}"] / $stats['total_reviews']) * 100, 1);
                    }
                } else {
                    for ($i = 1; $i <= 5; $i++) {
                        $stats["rating_{$i}_percent"] = 0;
                    }
                }
            }

            // Format reviews
            foreach ($reviews as &$review) {
                $review['rating_stars'] = str_repeat('★', $review['rating']) . str_repeat('☆', 5 - $review['rating']);
                $review['created_at_formatted'] = date('d/m/Y', strtotime($review['created_at']));
                $review['display_name'] = $review['customer_name'];
            }

            return $this->respond([
                'status' => 'success',
                'data' => [
                    'reviews' => $reviews,
                    'stats' => $stats,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $stats['total_reviews'] ?? 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->respond([
                'status' => 'error',
                'message' => 'Error getting product reviews: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Create a new review
     * POST /api/reviews
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true) ?? $this->request->getPost();

            // Validaciones de seguridad adicionales
            if (empty($data)) {
                return $this->failValidationError('No data received');
            }

            // Validar campos requeridos
            $requiredFields = ['product_id', 'rating', 'title', 'comment', 'customer_name', 'customer_email'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty(trim($data[$field]))) {
                    return $this->failValidationError("Campo requerido faltante: {$field}");
                }
            }

            // Validaciones específicas
            if (!is_numeric($data['product_id']) || $data['product_id'] <= 0) {
                return $this->failValidationError('ID de producto inválido');
            }

            if (!is_numeric($data['rating']) || $data['rating'] < 1 || $data['rating'] > 5) {
                return $this->failValidationError('Rating debe ser entre 1 y 5');
            }

            if (!filter_var($data['customer_email'], FILTER_VALIDATE_EMAIL)) {
                return $this->failValidationError('Email inválido');
            }

            // Validar longitud de campos
            if (strlen($data['title']) > 200) {
                return $this->failValidationError('Título muy largo (máximo 200 caracteres)');
            }

            if (strlen($data['comment']) > 2000) {
                return $this->failValidationError('Comentario muy largo (máximo 2000 caracteres)');
            }

            if (strlen($data['customer_name']) > 100) {
                return $this->failValidationError('Nombre muy largo (máximo 100 caracteres)');
            }

            // Verificar que el producto existe
            $db = \Config\Database::connect();
            $product = $db->table('products')
                         ->where('id', $data['product_id'])
                         ->where('is_active', 1)
                         ->where('deleted_at IS NULL')
                         ->get()
                         ->getRowArray();

            if (!$product) {
                return $this->failValidationError('Producto no encontrado');
            }

            // Verificar duplicados por IP y email en las últimas 24 horas
            $clientIp = $this->request->getIPAddress();
            $recentReview = $db->table('product_reviews')
                              ->where('product_id', $data['product_id'])
                              ->where('customer_email', $data['customer_email'])
                              ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                              ->get()
                              ->getRowArray();

            if ($recentReview) {
                return $this->failValidationError('Ya has enviado una reseña para este producto recientemente');
            }

            // Filtrar contenido sospechoso
            $suspiciousPatterns = [
                '/\b(viagra|cialis|casino|poker|loan|credit|debt)\b/i',
                '/\b(http|https|www\.)\b/i',
                '/\b(\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4})\b/', // Números de tarjeta
                '/\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b/' // Emails en el contenido
            ];

            $isSpam = false;
            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $data['title'] . ' ' . $data['comment'])) {
                    $isSpam = true;
                    break;
                }
            }

            // Generate UUID manually
            $uuid = sprintf(
                '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0xffff)
            );

            // Limpiar y sanitizar datos
            $cleanData = [
                'uuid' => $uuid,
                'product_id' => (int) $data['product_id'],
                'customer_name' => trim(strip_tags($data['customer_name'])),
                'customer_email' => trim(strtolower($data['customer_email'])),
                'rating' => (int) $data['rating'],
                'title' => trim(strip_tags($data['title'])),
                'comment' => trim(strip_tags($data['comment'])),
                'client_ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'is_approved' => 0,
                'is_verified_purchase' => 0,
                'is_featured' => 0,
                'is_spam' => $isSpam ? 1 : 0,
                'helpful_count' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Insertar reseña
            $result = $db->table('product_reviews')->insert($cleanData);

            if ($result) {
                // Log de seguridad
                log_message('info', "Nueva reseña creada: Producto {$cleanData['product_id']}, Email {$cleanData['customer_email']}, IP {$cleanData['client_ip']}");

                return $this->respondCreated([
                    'status' => 'success',
                    'message' => 'Reseña enviada exitosamente. Será publicada después de la moderación.',
                    'data' => ['id' => $db->insertID()]
                ]);
            } else {
                return $this->failServerError('Error al guardar la reseña');
            }

        } catch (\Exception $e) {
            return $this->respond([
                'status' => 'error',
                'message' => 'Error creating review: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Get pending reviews for admin
     * GET /api/reviews/pending
     */
    public function pending()
    {
        try {
            $reviewModel = new ProductReviewModel();
            
            $page = $this->request->getGet('page') ?? 1;
            $limit = $this->request->getGet('limit') ?? 20;
            $offset = ($page - 1) * $limit;

            $reviews = $reviewModel->getPendingReviews($limit, $offset);
            
            // Format reviews
            foreach ($reviews as &$review) {
                $review['rating_stars'] = str_repeat('★', $review['rating']) . str_repeat('☆', 5 - $review['rating']);
                $review['created_at_formatted'] = date('d/m/Y H:i', strtotime($review['created_at']));
                $review['display_name'] = $review['user_name'] ?? $review['customer_name'];
            }

            return $this->respond([
                'status' => 'success',
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting pending reviews: ' . $e->getMessage());
            return $this->failServerError('Error retrieving pending reviews');
        }
    }

    /**
     * Approve a review
     * PUT /api/reviews/{id}/approve
     */
    public function approve($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Review ID is required');
        }

        try {
            $reviewModel = new ProductReviewModel();
            
            if ($reviewModel->approveReview($id)) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Review approved successfully'
                ]);
            } else {
                return $this->failNotFound('Review not found');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error approving review: ' . $e->getMessage());
            return $this->failServerError('Error approving review');
        }
    }

    /**
     * Reject a review
     * DELETE /api/reviews/{id}
     */
    public function delete($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Review ID is required');
        }

        try {
            $reviewModel = new ProductReviewModel();
            
            if ($reviewModel->rejectReview($id)) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Review rejected successfully'
                ]);
            } else {
                return $this->failNotFound('Review not found');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error rejecting review: ' . $e->getMessage());
            return $this->failServerError('Error rejecting review');
        }
    }

    /**
     * Add admin response to review
     * PUT /api/reviews/{id}/response
     */
    public function addResponse($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Review ID is required');
        }

        try {
            $reviewModel = new ProductReviewModel();
            
            $data = $this->request->getJSON(true) ?? $this->request->getPost();
            $response = $data['response'] ?? '';
            
            if (empty($response)) {
                return $this->failValidationError('Response is required');
            }

            if ($reviewModel->addAdminResponse($id, $response)) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Response added successfully'
                ]);
            } else {
                return $this->failNotFound('Review not found');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error adding admin response: ' . $e->getMessage());
            return $this->failServerError('Error adding response');
        }
    }

    /**
     * Toggle featured status
     * PUT /api/reviews/{id}/featured
     */
    public function toggleFeatured($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Review ID is required');
        }

        try {
            $reviewModel = new ProductReviewModel();
            
            if ($reviewModel->toggleFeatured($id)) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Featured status updated successfully'
                ]);
            } else {
                return $this->failNotFound('Review not found');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error toggling featured status: ' . $e->getMessage());
            return $this->failServerError('Error updating featured status');
        }
    }
}
