<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class FixCurrency extends BaseController
{
    public function index()
    {
        $db = \Config\Database::connect();
        
        $output = "🔄 Verificando configuración de tasa de cambio...\n";
        
        try {
            // Verificar si existe la configuración de tasa de cambio
            $existing = $db->table('system_settings')
                          ->where('setting_key', 'exchange_rate_usd_to_gtq')
                          ->get()
                          ->getRowArray();
            
            if ($existing) {
                $output .= "✅ Configuración de tasa de cambio encontrada: {$existing['setting_value']}\n";
                
                // Actualizar a la tasa correcta (7.75 como estaba originalmente)
                $db->table('system_settings')
                   ->where('setting_key', 'exchange_rate_usd_to_gtq')
                   ->update([
                       'setting_value' => '7.75',
                       'updated_at' => date('Y-m-d H:i:s')
                   ]);
                
                $output .= "✅ Tasa actualizada a 7.75 GTQ por USD\n";
            } else {
                $output .= "❌ Configuración no encontrada, creando...\n";
                
                // Crear la configuración
                $db->table('system_settings')->insert([
                    'setting_key' => 'exchange_rate_usd_to_gtq',
                    'setting_value' => '7.75',
                    'setting_group' => 'currency',
                    'display_name' => 'Tipo de Cambio USD → GTQ',
                    'description' => 'Tasa de cambio de dólares a quetzales',
                    'setting_type' => 'number',
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                $output .= "✅ Configuración creada con tasa 7.75 GTQ por USD\n";
            }
            
            // Verificar otras configuraciones de moneda necesarias
            $currencySettings = [
                'default_currency' => 'GTQ',
                'show_currency_conversion' => '1'
            ];
            
            foreach ($currencySettings as $key => $defaultValue) {
                $existing = $db->table('system_settings')
                              ->where('setting_key', $key)
                              ->get()
                              ->getRowArray();
                
                if (!$existing) {
                    $db->table('system_settings')->insert([
                        'setting_key' => $key,
                        'setting_value' => $defaultValue,
                        'setting_group' => 'currency',
                        'display_name' => ucfirst(str_replace('_', ' ', $key)),
                        'description' => 'Configuración de moneda',
                        'setting_type' => $key === 'show_currency_conversion' ? 'checkbox' : 'select',
                        'is_active' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    $output .= "✅ Configuración {$key} creada\n";
                }
            }
            
            // Mostrar configuración actual
            $output .= "\n📋 Configuración actual de monedas:\n";
            $allCurrencySettings = $db->table('system_settings')
                                     ->where('setting_group', 'currency')
                                     ->get()
                                     ->getResultArray();
            
            foreach ($allCurrencySettings as $setting) {
                $output .= "   {$setting['setting_key']}: {$setting['setting_value']}\n";
            }
            
            $output .= "\n🎉 ¡Configuración de tasa de cambio arreglada!\n";
            
        } catch (Exception $e) {
            $output .= "❌ Error: " . $e->getMessage() . "\n";
        }
        
        // Mostrar output como texto plano
        return $this->response->setContentType('text/plain')->setBody($output);
    }
    
    public function test()
    {
        helper('currency');
        
        $output = "🧪 Probando conversiones con configuración del admin...\n\n";
        
        // Probar diferentes conversiones
        $tests = [
            ['USD', 'GTQ', 100],
            ['GTQ', 'USD', 775],
            ['USD', 'GTQ', 1],
            ['GTQ', 'USD', 7.75],
        ];
        
        foreach ($tests as $test) {
            $fromCurrency = $test[0];
            $toCurrency = $test[1];
            $amount = $test[2];
            
            $rate = get_cached_exchange_rate($fromCurrency, $toCurrency);
            $converted = convert_currency($amount, $fromCurrency, $toCurrency);
            
            $output .= "💰 {$amount} {$fromCurrency} → {$toCurrency}\n";
            $output .= "   Tasa: {$rate}\n";
            $output .= "   Resultado: {$converted} {$toCurrency}\n\n";
        }
        
        return $this->response->setContentType('text/plain')->setBody($output);
    }
}
