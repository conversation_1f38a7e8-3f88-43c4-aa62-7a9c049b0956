<?php

// Script para probar la configuración de WhatsApp
require_once 'vendor/autoload.php';

echo "🧪 PROBANDO CONFIGURACIÓN DE WHATSAPP...\n\n";

try {
    // Configurar CodeIgniter
    $paths = new Config\Paths();
    $bootstrap = \CodeIgniter\Boot::bootWeb($paths);
    $app = $bootstrap->getApp();

    // Obtener instancia de la base de datos
    $db = \Config\Database::connect();
    
    // Verificar conexión
    echo "🔗 Verificando conexión a la base de datos...\n";
    $dbTest = $db->query("SELECT 1 as test")->getRowArray();
    if (!$dbTest) {
        throw new Exception('No se puede conectar a la base de datos');
    }
    echo "✅ Conexión a la base de datos OK\n\n";
    
    // Insertar configuración de WhatsApp si no existe
    echo "⚙️ Configurando número de grupo de WhatsApp...\n";
    
    // Verificar si existe en system_settings
    $existingSystemSettings = $db->query("
        SELECT setting_value FROM system_settings 
        WHERE setting_key = 'whatsapp_alerts_group' 
        LIMIT 1
    ")->getRowArray();
    
    if (!$existingSystemSettings) {
        // Insertar en system_settings
        $db->query("
            INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_group, setting_description, is_public, created_at, updated_at)
            VALUES ('whatsapp_alerts_group', '120363416393766854', 'text', 'notifications', 'Número del grupo de WhatsApp para alertas automáticas del sistema', 0, NOW(), NOW())
        ");
        echo "✅ Configuración agregada a system_settings\n";
    } else {
        echo "✅ Configuración ya existe en system_settings: {$existingSystemSettings['setting_value']}\n";
    }
    
    // Verificar si existe tabla settings
    $settingsTableExists = $db->query("
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
            AND table_name = 'settings'
    ")->getRowArray();
    
    if ($settingsTableExists && $settingsTableExists['count'] > 0) {
        $existingSettings = $db->query("
            SELECT value FROM settings 
            WHERE `key` = 'whatsapp_alerts_group' 
            LIMIT 1
        ")->getRowArray();
        
        if (!$existingSettings) {
            $db->query("
                INSERT INTO settings (`key`, `value`, description, created_at, updated_at)
                VALUES ('whatsapp_alerts_group', '120363416393766854', 'Número del grupo de WhatsApp para alertas automáticas', NOW(), NOW())
            ");
            echo "✅ Configuración agregada a settings\n";
        } else {
            echo "✅ Configuración ya existe en settings: {$existingSettings['value']}\n";
        }
    } else {
        echo "⚠️ Tabla settings no existe\n";
    }
    
    // Probar el CronService
    echo "\n🔧 Probando CronService...\n";
    $cronService = new \App\Libraries\CronService();
    
    // Usar reflexión para acceder al método privado
    $reflection = new ReflectionClass($cronService);
    $method = $reflection->getMethod('getWhatsAppGroupNumber');
    $method->setAccessible(true);
    
    $groupNumber = $method->invoke($cronService);
    echo "📱 Número de grupo obtenido: $groupNumber\n";
    
    if ($groupNumber === '120363416393766854') {
        echo "✅ Configuración correcta!\n";
    } else {
        echo "⚠️ Número diferente al esperado\n";
    }
    
    // Probar el servicio de WhatsApp
    echo "\n📤 Probando servicio de WhatsApp...\n";
    $whatsappService = new \App\Services\WhatsAppService();
    
    // Verificar que el servicio existe
    if (class_exists('\App\Services\WhatsAppService')) {
        echo "✅ Servicio de WhatsApp disponible\n";
        
        // Probar envío de mensaje de prueba (sin enviar realmente)
        $testMessage = "🧪 Mensaje de prueba del sistema de alertas MrCell\n\nEste es un mensaje de prueba para verificar la configuración.\n\n🤖 Sistema de alertas automáticas";
        
        echo "📝 Mensaje de prueba preparado:\n";
        echo "   Destinatario: $groupNumber\n";
        echo "   Longitud del mensaje: " . strlen($testMessage) . " caracteres\n";
        
        // Solo mostrar que está listo, no enviar realmente
        echo "✅ Listo para enviar (no se envía en modo prueba)\n";
        
    } else {
        echo "❌ Servicio de WhatsApp no encontrado\n";
    }
    
    echo "\n🎉 CONFIGURACIÓN COMPLETADA\n";
    echo "📱 Número de grupo: 120363416393766854\n";
    echo "⚙️ Editable desde: /admin/settings?tab=notifications\n";
    echo "🔄 El cron enviará alertas a este grupo cada 12 horas\n";
    
} catch (Exception $e) {
    echo "\n❌ ERROR: {$e->getMessage()}\n";
    echo "Archivo: {$e->getFile()}:{$e->getLine()}\n";
}

echo "\n";
