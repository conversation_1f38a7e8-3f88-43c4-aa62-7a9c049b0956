<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1><i class="fas fa-edit me-2"></i>Editar Punto de Recogida</h1>
        <p class="text-muted mb-0">Para: <?= esc($paymentMethod['name']) ?></p>
    </div>
    <div>
        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Editar Punto de Recogida</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="locationName" class="form-label">Nombre del Punto <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="locationName" name="name" required
                                       value="<?= esc($pickupLocation['name']) ?>"
                                       placeholder="Ej: Bodega Central - Zona 1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="deliveryFee" class="form-label">Costo de Entrega (Q)</label>
                                <input type="number" class="form-control" id="deliveryFee" name="delivery_fee" 
                                       min="0" step="0.01" value="<?= esc($pickupLocation['delivery_fee']) ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Dirección Completa <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address" name="address" rows="2" required
                                  placeholder="Ej: Av. Elena 15-45, Zona 1, Ciudad de Guatemala"><?= esc($pickupLocation['address']) ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Teléfono de Contacto</label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="<?= esc($pickupLocation['phone']) ?>"
                                       placeholder="Ej: +502 2345-6789">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="schedule" class="form-label">Horario de Atención</label>
                                <input type="text" class="form-control" id="schedule" name="schedule"
                                       value="<?= esc($pickupLocation['schedule']) ?>"
                                       placeholder="Ej: Lunes a Viernes: 8:00 AM - 6:00 PM">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="coverageZones" class="form-label">Zonas de Cobertura</label>
                        <textarea class="form-control" id="coverageZones" name="coverage_zones" rows="2"
                                  placeholder="Ej: Zona 1, Zona 2, Zona 3, Zona 4, Zona 5"><?= esc($pickupLocation['coverage_zones']) ?></textarea>
                        <small class="form-text text-muted">
                            Separar las zonas con comas. Estas son las áreas donde se puede entregar desde este punto.
                        </small>
                    </div>

                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instrucciones Especiales</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="3"
                                  placeholder="Ej: Traer documento de identidad. Pago en efectivo únicamente."><?= esc($pickupLocation['instructions']) ?></textarea>
                        <small class="form-text text-muted">
                            Instrucciones adicionales para el cliente al recoger en este punto
                        </small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Actualizar Punto
                        </button>
                        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
                            Cancelar
                        </a>
                        <button type="button" class="btn btn-danger ms-auto" 
                                onclick="confirmDelete(<?= $pickupLocation['id'] ?>)">
                            <i class="fas fa-trash me-2"></i>Eliminar Punto
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Información de ayuda -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Consejos para Puntos de Recogida</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Nombre descriptivo:</strong> Incluye la zona o referencia conocida</li>
                    <li><strong>Dirección completa:</strong> Debe ser fácil de encontrar para los clientes</li>
                    <li><strong>Horario claro:</strong> Especifica días y horas de atención</li>
                    <li><strong>Zonas de cobertura:</strong> Define claramente qué áreas cubre este punto</li>
                    <li><strong>Costo de entrega:</strong> Puede ser Q0.00 si no hay costo adicional</li>
                    <li><strong>Instrucciones:</strong> Incluye requisitos como documentos o formas de pago</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('¿Estás seguro de eliminar este punto de recogida? Esta acción no se puede deshacer.')) {
        window.location.href = '/admin/payment-methods/delete-pickup-location/' + id;
    }
}
</script>
<?= $this->endSection() ?>
