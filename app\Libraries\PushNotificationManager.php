<?php

namespace App\Libraries;

/**
 * Sistema de Notificaciones Push para Móviles
 * Gestiona notificaciones web push y PWA
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class PushNotificationManager
{
    private $db;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->config = [
            'enabled' => env('PUSH_NOTIFICATIONS_ENABLED', true),
            'vapid_public_key' => env('VAPID_PUBLIC_KEY', ''),
            'vapid_private_key' => env('VAPID_PRIVATE_KEY', ''),
            'vapid_subject' => env('VAPID_SUBJECT', 'mailto:<EMAIL>'),
            'max_notifications_per_day' => env('MAX_PUSH_NOTIFICATIONS_PER_DAY', 5),
            'notification_ttl' => env('PUSH_NOTIFICATION_TTL', 86400), // 24 horas
            'batch_size' => env('PUSH_BATCH_SIZE', 100)
        ];
    }
    
    /**
     * Suscribir usuario a notificaciones push
     */
    public function subscribe(int $userId, array $subscription): array
    {
        try {
            // Validar subscription
            if (!$this->validateSubscription($subscription)) {
                return ['success' => false, 'error' => 'Invalid subscription data'];
            }
            
            // Verificar si ya existe
            $existing = $this->db->table('push_subscriptions')
                                ->where('user_id', $userId)
                                ->where('endpoint', $subscription['endpoint'])
                                ->get()
                                ->getRowArray();
            
            if ($existing) {
                // Actualizar subscription existente
                $this->db->table('push_subscriptions')
                        ->where('id', $existing['id'])
                        ->update([
                            'p256dh_key' => $subscription['keys']['p256dh'],
                            'auth_key' => $subscription['keys']['auth'],
                            'is_active' => 1,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                
                return ['success' => true, 'subscription_id' => $existing['id']];
            }
            
            // Crear nueva subscription
            $subscriptionData = [
                'user_id' => $userId,
                'endpoint' => $subscription['endpoint'],
                'p256dh_key' => $subscription['keys']['p256dh'],
                'auth_key' => $subscription['keys']['auth'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $subscriptionId = $this->db->table('push_subscriptions')->insert($subscriptionData, true);
            
            if ($subscriptionId) {
                // Enviar notificación de bienvenida
                $this->sendWelcomeNotification($userId);
                
                return ['success' => true, 'subscription_id' => $subscriptionId];
            } else {
                return ['success' => false, 'error' => 'Failed to save subscription'];
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Push subscription error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Internal server error'];
        }
    }
    
    /**
     * Cancelar suscripción
     */
    public function unsubscribe(int $userId, string $endpoint = null): array
    {
        try {
            $builder = $this->db->table('push_subscriptions');
            $builder->where('user_id', $userId);
            
            if ($endpoint) {
                $builder->where('endpoint', $endpoint);
            }
            
            $updated = $builder->update(['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')]);
            
            return ['success' => $updated > 0];
            
        } catch (\Exception $e) {
            log_message('error', 'Push unsubscribe error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Internal server error'];
        }
    }
    
    /**
     * Enviar notificación a usuario específico
     */
    public function sendToUser(int $userId, array $notification): array
    {
        try {
            // Verificar límite diario
            if (!$this->canSendNotification($userId)) {
                return ['success' => false, 'error' => 'Daily notification limit reached'];
            }
            
            // Obtener subscriptions activas del usuario
            $subscriptions = $this->db->table('push_subscriptions')
                                   ->where('user_id', $userId)
                                   ->where('is_active', 1)
                                   ->get()
                                   ->getResultArray();
            
            if (empty($subscriptions)) {
                return ['success' => false, 'error' => 'No active subscriptions found'];
            }
            
            $results = [];
            foreach ($subscriptions as $subscription) {
                $result = $this->sendNotification($subscription, $notification);
                $results[] = $result;
                
                // Log notification
                $this->logNotification($userId, $subscription['id'], $notification, $result['success']);
            }
            
            $successCount = count(array_filter($results, function($r) { return $r['success']; }));
            
            return [
                'success' => $successCount > 0,
                'sent_count' => $successCount,
                'total_subscriptions' => count($subscriptions),
                'results' => $results
            ];
            
        } catch (\Exception $e) {
            log_message('error', 'Push send to user error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Internal server error'];
        }
    }
    
    /**
     * Enviar notificación masiva
     */
    public function sendBulkNotification(array $userIds, array $notification): array
    {
        try {
            $results = [
                'total_users' => count($userIds),
                'successful_sends' => 0,
                'failed_sends' => 0,
                'details' => []
            ];
            
            // Procesar en lotes
            $batches = array_chunk($userIds, $this->config['batch_size']);
            
            foreach ($batches as $batch) {
                foreach ($batch as $userId) {
                    $result = $this->sendToUser($userId, $notification);
                    
                    if ($result['success']) {
                        $results['successful_sends']++;
                    } else {
                        $results['failed_sends']++;
                    }
                    
                    $results['details'][$userId] = $result;
                    
                    // Pequeña pausa para evitar sobrecarga
                    usleep(10000); // 10ms
                }
            }
            
            return $results;
            
        } catch (\Exception $e) {
            log_message('error', 'Bulk push notification error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Internal server error'];
        }
    }
    
    /**
     * Enviar notificación de precio
     */
    public function sendPriceAlert(int $userId, array $product, float $oldPrice, float $newPrice): array
    {
        $savings = $oldPrice - $newPrice;
        $percentage = round(($savings / $oldPrice) * 100, 1);
        
        $notification = [
            'title' => '🔥 ¡Alerta de Precio!',
            'body' => "{$product['name']} bajó {$percentage}% - Ahorra Q" . number_format($savings, 2),
            'icon' => base_url('assets/images/icons/price-alert.png'),
            'badge' => base_url('assets/images/icons/badge.png'),
            'image' => base_url('uploads/' . ($product['featured_image'] ?? 'no-image.jpg')),
            'data' => [
                'type' => 'price_alert',
                'product_id' => $product['id'],
                'product_slug' => $product['slug'],
                'url' => base_url('producto/' . $product['slug']),
                'old_price' => $oldPrice,
                'new_price' => $newPrice,
                'savings' => $savings,
                'percentage' => $percentage
            ],
            'actions' => [
                [
                    'action' => 'view',
                    'title' => 'Ver Producto',
                    'icon' => base_url('assets/images/icons/view.png')
                ],
                [
                    'action' => 'buy',
                    'title' => 'Comprar Ahora',
                    'icon' => base_url('assets/images/icons/cart.png')
                ]
            ],
            'requireInteraction' => true,
            'tag' => 'price-alert-' . $product['id']
        ];
        
        return $this->sendToUser($userId, $notification);
    }
    
    /**
     * Enviar notificación de stock disponible
     */
    public function sendStockAlert(int $userId, array $product): array
    {
        $notification = [
            'title' => '📦 ¡Producto Disponible!',
            'body' => "{$product['name']} ya está en stock - ¡Cómpralo antes de que se agote!",
            'icon' => base_url('assets/images/icons/stock-alert.png'),
            'badge' => base_url('assets/images/icons/badge.png'),
            'image' => base_url('uploads/' . ($product['featured_image'] ?? 'no-image.jpg')),
            'data' => [
                'type' => 'stock_alert',
                'product_id' => $product['id'],
                'product_slug' => $product['slug'],
                'url' => base_url('producto/' . $product['slug']),
                'stock_quantity' => $product['stock_quantity']
            ],
            'actions' => [
                [
                    'action' => 'view',
                    'title' => 'Ver Producto',
                    'icon' => base_url('assets/images/icons/view.png')
                ],
                [
                    'action' => 'buy',
                    'title' => 'Comprar Ahora',
                    'icon' => base_url('assets/images/icons/cart.png')
                ]
            ],
            'requireInteraction' => true,
            'tag' => 'stock-alert-' . $product['id']
        ];
        
        return $this->sendToUser($userId, $notification);
    }
    
    /**
     * Enviar notificación de pedido
     */
    public function sendOrderNotification(int $userId, array $order, string $status): array
    {
        $statusMessages = [
            'confirmed' => ['🎉 ¡Pedido Confirmado!', 'Tu pedido #{order_number} ha sido confirmado'],
            'processing' => ['⚡ Preparando tu Pedido', 'Tu pedido #{order_number} está siendo preparado'],
            'shipped' => ['🚚 ¡Pedido Enviado!', 'Tu pedido #{order_number} está en camino'],
            'delivered' => ['✅ ¡Pedido Entregado!', 'Tu pedido #{order_number} ha sido entregado']
        ];
        
        $message = $statusMessages[$status] ?? ['📋 Actualización de Pedido', 'Tu pedido #{order_number} ha sido actualizado'];
        
        $notification = [
            'title' => $message[0],
            'body' => str_replace('{order_number}', $order['order_number'], $message[1]),
            'icon' => base_url('assets/images/icons/order-' . $status . '.png'),
            'badge' => base_url('assets/images/icons/badge.png'),
            'data' => [
                'type' => 'order_update',
                'order_id' => $order['id'],
                'order_number' => $order['order_number'],
                'status' => $status,
                'url' => base_url('cuenta/pedidos/' . $order['id'])
            ],
            'actions' => [
                [
                    'action' => 'view',
                    'title' => 'Ver Pedido',
                    'icon' => base_url('assets/images/icons/view.png')
                ]
            ],
            'tag' => 'order-' . $order['id']
        ];
        
        if ($status === 'shipped' && !empty($order['tracking_number'])) {
            $notification['actions'][] = [
                'action' => 'track',
                'title' => 'Rastrear Envío',
                'icon' => base_url('assets/images/icons/track.png')
            ];
        }
        
        return $this->sendToUser($userId, $notification);
    }
    
    /**
     * Enviar notificación de bienvenida
     */
    private function sendWelcomeNotification(int $userId): array
    {
        $notification = [
            'title' => '🎉 ¡Bienvenido a MrCell!',
            'body' => 'Gracias por activar las notificaciones. Te mantendremos informado de ofertas especiales.',
            'icon' => base_url('assets/images/icons/welcome.png'),
            'badge' => base_url('assets/images/icons/badge.png'),
            'data' => [
                'type' => 'welcome',
                'url' => base_url()
            ],
            'actions' => [
                [
                    'action' => 'explore',
                    'title' => 'Explorar Productos',
                    'icon' => base_url('assets/images/icons/explore.png')
                ]
            ],
            'tag' => 'welcome-' . $userId
        ];
        
        return $this->sendToUser($userId, $notification);
    }
    
    /**
     * Enviar notificación real usando Web Push Protocol
     */
    private function sendNotification(array $subscription, array $notification): array
    {
        try {
            if (!$this->config['enabled']) {
                return ['success' => false, 'error' => 'Push notifications disabled'];
            }
            
            // Preparar payload
            $payload = json_encode($notification);
            
            // Generar headers VAPID
            $vapidHeaders = $this->generateVapidHeaders($subscription['endpoint']);
            
            // Encriptar payload
            $encryptedPayload = $this->encryptPayload($payload, $subscription['p256dh_key'], $subscription['auth_key']);
            
            // Enviar notificación
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $subscription['endpoint'],
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $encryptedPayload['ciphertext'],
                CURLOPT_HTTPHEADER => array_merge($vapidHeaders, [
                    'Content-Type: application/octet-stream',
                    'Content-Encoding: aes128gcm',
                    'Content-Length: ' . strlen($encryptedPayload['ciphertext']),
                    'TTL: ' . $this->config['notification_ttl']
                ]),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode >= 200 && $httpCode < 300) {
                return ['success' => true, 'http_code' => $httpCode];
            } else {
                // Manejar errores específicos
                if ($httpCode === 410) {
                    // Subscription expirada, desactivar
                    $this->deactivateSubscription($subscription['id']);
                }
                
                return ['success' => false, 'http_code' => $httpCode, 'response' => $response];
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Push notification send error: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Validar datos de subscription
     */
    private function validateSubscription(array $subscription): bool
    {
        return isset($subscription['endpoint']) &&
               isset($subscription['keys']['p256dh']) &&
               isset($subscription['keys']['auth']) &&
               !empty($subscription['endpoint']) &&
               !empty($subscription['keys']['p256dh']) &&
               !empty($subscription['keys']['auth']);
    }
    
    /**
     * Verificar si puede enviar notificación
     */
    private function canSendNotification(int $userId): bool
    {
        $today = date('Y-m-d');
        
        $count = $this->db->table('push_notification_log')
                         ->where('user_id', $userId)
                         ->where('DATE(created_at)', $today)
                         ->countAllResults();
        
        return $count < $this->config['max_notifications_per_day'];
    }
    
    /**
     * Registrar notificación enviada
     */
    private function logNotification(int $userId, int $subscriptionId, array $notification, bool $success): void
    {
        try {
            $this->db->table('push_notification_log')->insert([
                'user_id' => $userId,
                'subscription_id' => $subscriptionId,
                'notification_type' => $notification['data']['type'] ?? 'general',
                'title' => $notification['title'],
                'body' => $notification['body'],
                'success' => $success ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging push notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Desactivar subscription
     */
    private function deactivateSubscription(int $subscriptionId): void
    {
        $this->db->table('push_subscriptions')
                ->where('id', $subscriptionId)
                ->update(['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Generar headers VAPID (implementación simplificada)
     */
    private function generateVapidHeaders(string $endpoint): array
    {
        // Esta es una implementación simplificada
        // En producción, usar una librería como web-push-php
        return [
            'Authorization: vapid t=' . base64_encode('dummy_jwt_token') . ', k=' . $this->config['vapid_public_key']
        ];
    }
    
    /**
     * Encriptar payload (implementación simplificada)
     */
    private function encryptPayload(string $payload, string $p256dhKey, string $authKey): array
    {
        // Esta es una implementación simplificada
        // En producción, usar encriptación AES128GCM real
        return [
            'ciphertext' => base64_encode($payload),
            'salt' => random_bytes(16),
            'key' => random_bytes(16)
        ];
    }
    
    /**
     * Obtener estadísticas de notificaciones push
     */
    public function getStats(int $days = 7): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $stats = [
                'period_days' => $days,
                'total_sent' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'active_subscriptions' => 0,
                'notification_types' => []
            ];
            
            // Total de notificaciones enviadas
            $builder = $this->db->table('push_notification_log');
            $builder->select('success, notification_type, COUNT(*) as count');
            $builder->where('created_at >=', $startDate);
            $builder->groupBy('success, notification_type');
            
            $results = $builder->get()->getResultArray();
            
            foreach ($results as $result) {
                $stats['total_sent'] += $result['count'];
                
                if ($result['success']) {
                    $stats['successful_sends'] += $result['count'];
                } else {
                    $stats['failed_sends'] += $result['count'];
                }
                
                $stats['notification_types'][$result['notification_type']] = 
                    ($stats['notification_types'][$result['notification_type']] ?? 0) + $result['count'];
            }
            
            // Subscriptions activas
            $stats['active_subscriptions'] = $this->db->table('push_subscriptions')
                                                   ->where('is_active', 1)
                                                   ->countAllResults();
            
            return $stats;
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Verificar si está habilitado
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] && 
               !empty($this->config['vapid_public_key']) && 
               !empty($this->config['vapid_private_key']);
    }
    
    /**
     * Obtener configuración pública
     */
    public function getPublicConfig(): array
    {
        return [
            'enabled' => $this->isEnabled(),
            'vapid_public_key' => $this->config['vapid_public_key']
        ];
    }
}
