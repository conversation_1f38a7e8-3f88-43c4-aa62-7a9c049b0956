<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-shopping-cart me-2"></i>Gestión de Pedidos</h1>
        <div>
            <button class="btn btn-outline-primary me-2">
                <i class="fas fa-download me-2"></i>Exportar
            </button>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOrderModal">
                <i class="fas fa-plus me-2"></i>Nuevo Pedido
            </button>
        </div>
    </div>
</div>

<!-- Estadísticas de Pedidos -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $order_stats['pending'] ?? 0 ?></h3>
                        <small class="text-muted">Pendientes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $order_stats['processing'] ?? 0 ?></h3>
                        <small class="text-muted">Procesando</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-primary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $order_stats['shipped'] ?? 0 ?></h3>
                        <small class="text-muted">Enviados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-success text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-check"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $order_stats['delivered'] ?? 0 ?></h3>
                        <small class="text-muted">Completados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="stats-icon bg-danger text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-times"></i>
                    </div>
                    <div>
                        <h3 class="mb-0"><?= $order_stats['cancelled'] ?? 0 ?></h3>
                        <small class="text-muted">Cancelados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tabla de Pedidos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Pedidos</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="ordersTable">
                <thead>
                    <tr>
                        <th>Pedido</th>
                        <th>Cliente</th>
                        <th>Total</th>
                        <th>Estado</th>
                        <th>Fecha</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($orders)): ?>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <strong>#<?= esc($order['order_number']) ?></strong><br>
                                    <small class="text-muted"><?= esc($order['items_count'] ?? 0) ?> producto<?= ($order['items_count'] ?? 0) != 1 ? 's' : '' ?></small>
                                </td>
                                <td>
                                    <strong><?= esc($order['customer_name']) ?></strong><br>
                                    <small class="text-muted"><?= esc($order['customer_email']) ?></small>
                                </td>
                                <td>Q<?= number_format($order['total_amount'], 2) ?></td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-secondary';
                                    $statusText = 'Desconocido';

                                    switch ($order['status']) {
                                        case 'pending':
                                            $statusClass = 'bg-warning';
                                            $statusText = 'Pendiente';
                                            break;
                                        case 'processing':
                                            $statusClass = 'bg-info';
                                            $statusText = 'Procesando';
                                            break;
                                        case 'shipped':
                                            $statusClass = 'bg-primary';
                                            $statusText = 'Enviado';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'bg-success';
                                            $statusText = 'Entregado';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-danger';
                                            $statusText = 'Cancelado';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <?= date('d/m/Y', strtotime($order['created_at'])) ?><br>
                                    <small class="text-muted"><?= date('H:i', strtotime($order['created_at'])) ?></small>
                                </td>
                                <td>
                                    <a href="/admin/orders/view/<?= $order['id'] ?>" class="btn btn-sm btn-outline-info" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/admin/orders/edit/<?= $order['id'] ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                No hay pedidos para mostrar
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para gestión de pedidos
    console.log('Gestión de pedidos cargada');
</script>
<?= $this->endSection() ?>