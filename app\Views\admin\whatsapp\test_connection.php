<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-plug me-2"></i>Probar Conexión WhatsApp</h1>
        <a href="/admin/whatsapp" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Volver
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-vial me-2"></i>Enviar Mensaje de Prueba</h5>
            </div>
            <div class="card-body">
                <form id="testForm">
                    <div class="mb-3">
                        <label for="phoneNumber" class="form-label">Número de Teléfono</label>
                        <input type="text" class="form-control" id="phoneNumber" name="phone_number" 
                               placeholder="Ej: 50212345678" required>
                        <div class="form-text">
                            Ingresa el número sin espacios, guiones ni el símbolo +
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="testMessage" class="form-label">Mensaje de Prueba</label>
                        <textarea class="form-control" id="testMessage" rows="4" readonly>Mensaje de prueba desde MrCell Admin - <?= date('Y-m-d H:i:s') ?></textarea>
                        <div class="form-text">
                            Este mensaje se enviará para verificar la conexión
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary" id="sendTestBtn">
                        <i class="fas fa-paper-plane me-2"></i>Enviar Mensaje de Prueba
                    </button>
                </form>

                <!-- Resultado -->
                <div id="testResult" class="mt-4" style="display: none;">
                    <div class="alert" id="resultAlert">
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Estado de Configuración -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Estado de Configuración</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Servicio WhatsApp:</strong><br>
                    <?php if (isset($settings['enabled']) && $settings['enabled'] === '1'): ?>
                        <span class="badge bg-success">Habilitado</span>
                    <?php else: ?>
                        <span class="badge bg-danger">Deshabilitado</span>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <strong>URL de API:</strong><br>
                    <code class="small"><?= esc(substr($settings['api_url'] ?? '', 0, 40)) ?>...</code>
                </div>

                <div class="mb-3">
                    <strong>API Key:</strong><br>
                    <code class="small">
                        <?= !empty($settings['api_key']) ? str_repeat('*', 20) . substr($settings['api_key'], -4) : 'No configurada' ?>
                    </code>
                </div>

                <div class="mb-3">
                    <strong>Device Token:</strong><br>
                    <code class="small"><?= esc($settings['device_token'] ?? 'No configurado') ?></code>
                </div>

                <hr>

                <div class="text-center">
                    <a href="/admin/whatsapp/settings" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>Configurar
                    </a>
                </div>
            </div>
        </div>

        <!-- Información de la API -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>Información de la API</h6>
            </div>
            <div class="card-body">
                <h6>Formato del Número</h6>
                <p class="small text-muted">
                    El número debe incluir el código de país sin el símbolo +
                </p>
                <ul class="small text-muted">
                    <li><strong>Guatemala:</strong> 50212345678</li>
                    <li><strong>México:</strong> 5215512345678</li>
                    <li><strong>USA:</strong> 15551234567</li>
                </ul>

                <hr>

                <h6>Tipos de Respuesta</h6>
                <ul class="small text-muted">
                    <li><strong>Éxito:</strong> status: true</li>
                    <li><strong>Error:</strong> status: false</li>
                </ul>

                <hr>

                <h6>Códigos de Error Comunes</h6>
                <ul class="small text-muted">
                    <li><strong>401:</strong> API Key inválida</li>
                    <li><strong>400:</strong> Datos incorrectos</li>
                    <li><strong>500:</strong> Error del servidor</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.getElementById('testForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const phoneNumber = document.getElementById('phoneNumber').value;
    const sendBtn = document.getElementById('sendTestBtn');
    const resultDiv = document.getElementById('testResult');
    const resultAlert = document.getElementById('resultAlert');
    const resultContent = document.getElementById('resultContent');
    
    if (!phoneNumber) {
        alert('Por favor ingresa un número de teléfono');
        return;
    }
    
    // Validar formato básico del número
    if (!/^\d{8,15}$/.test(phoneNumber)) {
        alert('El número debe contener solo dígitos y tener entre 8 y 15 caracteres');
        return;
    }
    
    // Deshabilitar botón y mostrar loading
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
    
    // Ocultar resultado anterior
    resultDiv.style.display = 'none';
    
    // Enviar petición
    fetch('/admin/whatsapp/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: phoneNumber
        })
    })
    .then(response => response.json())
    .then(data => {
        // Mostrar resultado
        resultDiv.style.display = 'block';
        
        if (data.success) {
            resultAlert.className = 'alert alert-success';
            resultContent.innerHTML = `
                <h6><i class="fas fa-check-circle me-2"></i>¡Mensaje enviado exitosamente!</h6>
                <p class="mb-2"><strong>Número:</strong> ${phoneNumber}</p>
                ${data.message_id ? `<p class="mb-2"><strong>ID del mensaje:</strong> ${data.message_id}</p>` : ''}
                <p class="mb-0"><strong>Hora:</strong> ${new Date().toLocaleString()}</p>
                ${data.response ? `
                    <hr>
                    <h6>Respuesta de la API:</h6>
                    <pre class="small bg-light p-2 rounded">${JSON.stringify(data.response, null, 2)}</pre>
                ` : ''}
            `;
        } else {
            resultAlert.className = 'alert alert-danger';
            resultContent.innerHTML = `
                <h6><i class="fas fa-exclamation-circle me-2"></i>Error al enviar mensaje</h6>
                <p class="mb-2"><strong>Error:</strong> ${data.error}</p>
                <p class="mb-0"><strong>Hora:</strong> ${new Date().toLocaleString()}</p>
                ${data.response ? `
                    <hr>
                    <h6>Respuesta de la API:</h6>
                    <pre class="small bg-light p-2 rounded">${JSON.stringify(data.response, null, 2)}</pre>
                ` : ''}
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.style.display = 'block';
        resultAlert.className = 'alert alert-danger';
        resultContent.innerHTML = `
            <h6><i class="fas fa-exclamation-circle me-2"></i>Error de conexión</h6>
            <p class="mb-0">No se pudo conectar con el servidor. Verifica tu conexión a internet.</p>
        `;
    })
    .finally(() => {
        // Rehabilitar botón
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Enviar Mensaje de Prueba';
    });
});

// Validación en tiempo real del número
document.getElementById('phoneNumber').addEventListener('input', function() {
    const value = this.value;
    const isValid = /^\d{0,15}$/.test(value);
    
    if (!isValid && value.length > 0) {
        this.classList.add('is-invalid');
        if (!this.nextElementSibling || !this.nextElementSibling.classList.contains('invalid-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Solo se permiten números (sin espacios ni símbolos)';
            this.parentNode.appendChild(feedback);
        }
    } else {
        this.classList.remove('is-invalid');
        const feedback = this.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
});

// Limpiar caracteres no numéricos automáticamente
document.getElementById('phoneNumber').addEventListener('keypress', function(e) {
    if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
        e.preventDefault();
    }
});
</script>
<?= $this->endSection() ?>
