<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DefaultSettingsSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            [
                'setting_key' => 'site_logo',
                'setting_value' => 'logo.jpg',
                'setting_group' => 'general',
                'display_name' => 'Logo del Sitio',
                'description' => 'Logo principal del sitio web',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'setting_key' => 'site_favicon',
                'setting_value' => 'favicon.ico',
                'setting_group' => 'general',
                'display_name' => 'Favicon del Sitio',
                'description' => 'Icono del sitio web',
                'setting_type' => 'text',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'setting_key' => 'site_keywords',
                'setting_value' => 'celulares Guatemala, smartphones, accesorios móviles, tecnología, MrCell',
                'setting_group' => 'general',
                'display_name' => 'Palabras Clave',
                'description' => 'Palabras clave para SEO',
                'setting_type' => 'textarea',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($settings as $setting) {
            // Verificar si ya existe
            $existing = $this->db->table('system_settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();

            if (!$existing) {
                $this->db->table('system_settings')->insert($setting);
                echo "✅ Configuración '{$setting['setting_key']}' agregada\n";
            } else {
                echo "⚠️  Configuración '{$setting['setting_key']}' ya existe\n";
            }
        }
    }
}
