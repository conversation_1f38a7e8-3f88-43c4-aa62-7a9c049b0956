<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ShippingPackageTypeModel;
use App\Models\ShippingZoneModel;

class ShippingApiController extends BaseController
{
    protected $packageTypeModel;
    protected $zoneModel;

    public function __construct()
    {
        $this->packageTypeModel = new ShippingPackageTypeModel();
        $this->zoneModel = new ShippingZoneModel();
    }

    /**
     * Verificar autenticación de administrador
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'No autorizado'
            ])->setStatusCode(401);
        }
        return true;
    }

    /**
     * Calcular costo de envío basado en dimensiones y peso
     */
    public function calculateShipping()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $input = $this->request->getJSON(true);
            
            $length = floatval($input['length'] ?? 0);
            $width = floatval($input['width'] ?? 0);
            $height = floatval($input['height'] ?? 0);
            $weight = floatval($input['weight'] ?? 0);

            if ($length <= 0 || $width <= 0 || $height <= 0 || $weight <= 0) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Todas las dimensiones y el peso deben ser mayores a 0'
                ]);
            }

            // Determinar el tipo de paquete apropiado
            $packageType = $this->packageTypeModel->determinePackageType($length, $width, $height, $weight);

            if (!$packageType) {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'No se encontró un tipo de paquete apropiado para estas dimensiones'
                ]);
            }

            // Calcular costo base (sin distancia adicional)
            $baseCost = $this->packageTypeModel->calculateShippingCost($packageType['id'], 0, 0);

            return $this->response->setJSON([
                'success' => true,
                'package_type' => $packageType['name'],
                'package_type_id' => $packageType['id'],
                'cost' => $baseCost,
                'details' => [
                    'max_dimensions' => [
                        'length' => $packageType['max_length_cm'],
                        'width' => $packageType['max_width_cm'],
                        'height' => $packageType['max_height_cm']
                    ],
                    'max_weight' => $packageType['max_weight_lbs'],
                    'base_cost' => $packageType['base_cost'],
                    'cost_per_km' => $packageType['cost_per_km']
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al calcular envío: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Obtener todos los tipos de paquetes
     */
    public function getPackageTypes()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $packageTypes = $this->packageTypeModel->getActivePackageTypes();

            return $this->response->setJSON([
                'success' => true,
                'data' => $packageTypes
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener tipos de paquetes: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Crear nuevo tipo de paquete
     */
    public function createPackageType()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $input = $this->request->getJSON(true);

            $data = [
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? '',
                'max_length_cm' => floatval($input['max_length_cm'] ?? 0),
                'max_width_cm' => floatval($input['max_width_cm'] ?? 0),
                'max_height_cm' => floatval($input['max_height_cm'] ?? 0),
                'max_weight_lbs' => floatval($input['max_weight_lbs'] ?? 0),
                'min_weight_lbs' => floatval($input['min_weight_lbs'] ?? 0),
                'base_cost' => floatval($input['base_cost'] ?? 0),
                'cost_per_km' => floatval($input['cost_per_km'] ?? 0),
                'sort_order' => intval($input['sort_order'] ?? 0),
                'is_active' => 1
            ];

            if ($this->packageTypeModel->insert($data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Tipo de paquete creado correctamente',
                    'id' => $this->packageTypeModel->getInsertID()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al crear tipo de paquete',
                    'validation_errors' => $this->packageTypeModel->errors()
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al crear tipo de paquete: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Actualizar tipo de paquete
     */
    public function updatePackageType($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $input = $this->request->getJSON(true);

            $data = [
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? '',
                'max_length_cm' => floatval($input['max_length_cm'] ?? 0),
                'max_width_cm' => floatval($input['max_width_cm'] ?? 0),
                'max_height_cm' => floatval($input['max_height_cm'] ?? 0),
                'max_weight_lbs' => floatval($input['max_weight_lbs'] ?? 0),
                'min_weight_lbs' => floatval($input['min_weight_lbs'] ?? 0),
                'base_cost' => floatval($input['base_cost'] ?? 0),
                'cost_per_km' => floatval($input['cost_per_km'] ?? 0),
                'sort_order' => intval($input['sort_order'] ?? 0)
            ];

            if ($this->packageTypeModel->update($id, $data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Tipo de paquete actualizado correctamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al actualizar tipo de paquete',
                    'validation_errors' => $this->packageTypeModel->errors()
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al actualizar tipo de paquete: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Eliminar tipo de paquete
     */
    public function deletePackageType($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            // En lugar de eliminar, desactivar
            if ($this->packageTypeModel->update($id, ['is_active' => 0])) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Tipo de paquete eliminado correctamente'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'error' => 'Error al eliminar tipo de paquete'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al eliminar tipo de paquete: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Obtener estadísticas de envíos
     */
    public function getShippingStats()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) return $authCheck;

        try {
            $packageStats = $this->packageTypeModel->getUsageStats();
            $zoneStats = $this->zoneModel->getZoneUsageStats();

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'package_types' => $packageStats,
                    'zones' => $zoneStats
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Error al obtener estadísticas: ' . $e->getMessage()
            ]);
        }
    }
}
