<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddExternalIdToOrders extends Migration
{
    public function up()
    {
        // Agregar campo external_id para almacenar IDs externos como los de Recurrente
        $fields = [
            'external_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'ID externo para integraciones (ej: Recurrente)',
                'after' => 'order_number'
            ]
        ];

        $this->forge->addColumn('orders', $fields);
        
        // Agregar índice para búsquedas rápidas
        $this->forge->addKey('external_id');
    }

    public function down()
    {
        // Eliminar índice primero
        $this->db->query('DROP INDEX external_id ON orders');
        
        // Eliminar campo
        $this->forge->dropColumn('orders', 'external_id');
    }
}
