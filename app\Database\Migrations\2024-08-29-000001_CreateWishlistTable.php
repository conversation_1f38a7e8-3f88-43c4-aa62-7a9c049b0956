<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateWishlistTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'product_id'], false, true); // Unique key
        $this->forge->addKey('user_id');
        $this->forge->addKey('product_id');

        $this->forge->createTable('wishlist');

        // Add foreign key constraints if tables exist
        try {
            $this->db->query('ALTER TABLE wishlist ADD CONSTRAINT fk_wishlist_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE');
            $this->db->query('ALTER TABLE wishlist ADD CONSTRAINT fk_wishlist_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE');
        } catch (\Exception $e) {
            // Foreign keys might fail if referenced tables don't exist yet
            log_message('info', 'Wishlist foreign keys not added: ' . $e->getMessage());
        }
    }

    public function down()
    {
        $this->forge->dropTable('wishlist');
    }
}
