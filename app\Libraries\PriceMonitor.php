<?php

namespace App\Libraries;

use App\Libraries\WhatsAppNotifier;
use App\Libraries\EmailNotifier;
use App\Libraries\SimpleCache;

/**
 * Monitor de Precios y Alertas Automáticas
 * Sistema inteligente para detectar cambios de precio y enviar notificaciones
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class PriceMonitor
{
    private $db;
    private $whatsapp;
    private $email;
    private $cache;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->whatsapp = new WhatsAppNotifier();
        $this->email = new EmailNotifier();
        $this->cache = new SimpleCache();
        
        $this->config = [
            'enabled' => env('PRICE_MONITOR_ENABLED', true),
            'check_interval' => env('PRICE_CHECK_INTERVAL', 3600), // 1 hora
            'min_price_change' => env('MIN_PRICE_CHANGE', 5.00), // Q5.00 mínimo
            'min_percentage_change' => env('MIN_PERCENTAGE_CHANGE', 5), // 5% mínimo
            'max_notifications_per_day' => env('MAX_NOTIFICATIONS_PER_DAY', 10),
            'notification_cooldown' => env('NOTIFICATION_COOLDOWN', 86400), // 24 horas
            'batch_size' => env('PRICE_MONITOR_BATCH_SIZE', 100),
            'enable_whatsapp' => env('PRICE_MONITOR_WHATSAPP', true),
            'enable_email' => env('PRICE_MONITOR_EMAIL', true),
            'enable_stock_alerts' => env('STOCK_ALERTS_ENABLED', true)
        ];
    }
    
    /**
     * Ejecutar monitoreo completo de precios
     * 
     * @return array Resultado del monitoreo
     */
    public function runPriceMonitoring(): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'message' => 'Price monitoring disabled'];
        }
        
        $startTime = microtime(true);
        $results = [
            'products_checked' => 0,
            'price_changes_detected' => 0,
            'notifications_sent' => 0,
            'errors' => 0,
            'details' => []
        ];
        
        try {
            // Obtener productos con alertas activas
            $productsWithAlerts = $this->getProductsWithActiveAlerts();
            
            if (empty($productsWithAlerts)) {
                return array_merge($results, [
                    'success' => true,
                    'message' => 'No products with active alerts found',
                    'execution_time' => microtime(true) - $startTime
                ]);
            }
            
            // Procesar en lotes
            $batches = array_chunk($productsWithAlerts, $this->config['batch_size']);
            
            foreach ($batches as $batch) {
                $batchResults = $this->processPriceBatch($batch);
                
                $results['products_checked'] += $batchResults['products_checked'];
                $results['price_changes_detected'] += $batchResults['price_changes_detected'];
                $results['notifications_sent'] += $batchResults['notifications_sent'];
                $results['errors'] += $batchResults['errors'];
                $results['details'] = array_merge($results['details'], $batchResults['details']);
            }
            
            // Monitorear stock si está habilitado
            if ($this->config['enable_stock_alerts']) {
                $stockResults = $this->monitorStockChanges();
                $results['stock_alerts_sent'] = $stockResults['notifications_sent'];
                $results['details'] = array_merge($results['details'], $stockResults['details']);
            }
            
            // Limpiar notificaciones antiguas
            $this->cleanupOldNotifications();
            
            $results['success'] = true;
            $results['execution_time'] = microtime(true) - $startTime;
            
            // Log del resultado
            log_message('info', 'Price monitoring completed: ' . json_encode($results));
            
            return $results;
            
        } catch (\Exception $e) {
            log_message('error', 'Price monitoring error: ' . $e->getMessage());
            
            return array_merge($results, [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => microtime(true) - $startTime
            ]);
        }
    }
    
    /**
     * Obtener productos con alertas activas
     */
    private function getProductsWithActiveAlerts(): array
    {
        $builder = $this->db->table('wishlist w');
        $builder->select('w.*, p.*, u.id as user_id, u.email, u.phone, u.first_name, u.name,
                         p.price_regular as current_regular_price, 
                         p.price_sale as current_sale_price');
        $builder->join('products p', 'w.product_id = p.id');
        $builder->join('users u', 'w.user_id = u.id');
        $builder->where('w.notification_enabled', 1);
        $builder->where('w.price_alert_threshold IS NOT NULL');
        $builder->where('p.is_active', 1);
        $builder->where('p.deleted_at IS NULL');
        
        // Solo productos que no han sido notificados recientemente
        $cooldownTime = date('Y-m-d H:i:s', time() - $this->config['notification_cooldown']);
        $builder->where('(w.last_notification_at IS NULL OR w.last_notification_at < ?)', $cooldownTime);
        
        return $builder->get()->getResultArray();
    }
    
    /**
     * Procesar lote de productos
     */
    private function processPriceBatch(array $products): array
    {
        $results = [
            'products_checked' => 0,
            'price_changes_detected' => 0,
            'notifications_sent' => 0,
            'errors' => 0,
            'details' => []
        ];
        
        foreach ($products as $product) {
            try {
                $results['products_checked']++;
                
                // Obtener precio histórico
                $historicalPrice = $this->getHistoricalPrice($product['product_id']);
                
                // Calcular precio actual
                $currentPrice = $product['current_sale_price'] ?? $product['current_regular_price'];
                $alertThreshold = $product['price_alert_threshold'];
                
                // Verificar si debe enviar alerta
                if ($this->shouldSendPriceAlert($currentPrice, $historicalPrice, $alertThreshold)) {
                    $notificationResult = $this->sendPriceAlertNotifications($product, $historicalPrice, $currentPrice);
                    
                    if ($notificationResult['success']) {
                        $results['price_changes_detected']++;
                        $results['notifications_sent'] += $notificationResult['notifications_sent'];
                        
                        // Actualizar timestamp de última notificación
                        $this->updateLastNotificationTime($product['id']);
                        
                        // Guardar en historial de precios
                        $this->savePriceHistory($product['product_id'], $historicalPrice, $currentPrice);
                    }
                    
                    $results['details'][] = [
                        'product_id' => $product['product_id'],
                        'product_name' => $product['name'],
                        'user_id' => $product['user_id'],
                        'old_price' => $historicalPrice,
                        'new_price' => $currentPrice,
                        'threshold' => $alertThreshold,
                        'notification_result' => $notificationResult
                    ];
                }
                
            } catch (\Exception $e) {
                $results['errors']++;
                $results['details'][] = [
                    'product_id' => $product['product_id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ];
                
                log_message('error', 'Error processing product in price monitor: ' . $e->getMessage());
            }
        }
        
        return $results;
    }
    
    /**
     * Verificar si debe enviar alerta de precio
     */
    private function shouldSendPriceAlert(float $currentPrice, float $historicalPrice, float $threshold): bool
    {
        // No enviar si no hay precio histórico
        if ($historicalPrice <= 0) {
            return false;
        }
        
        // Verificar si el precio actual está por debajo del umbral
        if ($currentPrice > $threshold) {
            return false;
        }
        
        // Verificar cambio mínimo absoluto
        $priceChange = $historicalPrice - $currentPrice;
        if ($priceChange < $this->config['min_price_change']) {
            return false;
        }
        
        // Verificar cambio mínimo porcentual
        $percentageChange = ($priceChange / $historicalPrice) * 100;
        if ($percentageChange < $this->config['min_percentage_change']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Enviar notificaciones de alerta de precio
     */
    private function sendPriceAlertNotifications(array $product, float $oldPrice, float $newPrice): array
    {
        $results = [
            'success' => false,
            'notifications_sent' => 0,
            'whatsapp_sent' => false,
            'email_sent' => false,
            'errors' => []
        ];
        
        $user = [
            'id' => $product['user_id'],
            'email' => $product['email'],
            'phone' => $product['phone'],
            'first_name' => $product['first_name'],
            'name' => $product['name']
        ];
        
        $productData = [
            'id' => $product['product_id'],
            'name' => $product['name'],
            'slug' => $product['slug'],
            'featured_image' => $product['featured_image']
        ];
        
        // Verificar límite diario de notificaciones
        if (!$this->canSendNotification($user['id'])) {
            $results['errors'][] = 'Daily notification limit reached for user ' . $user['id'];
            return $results;
        }
        
        // Enviar por WhatsApp
        if ($this->config['enable_whatsapp'] && $this->whatsapp->isEnabled()) {
            try {
                $whatsappResult = $this->whatsapp->sendPriceAlert($user, $productData, $oldPrice, $newPrice);
                
                if ($whatsappResult['success']) {
                    $results['whatsapp_sent'] = true;
                    $results['notifications_sent']++;
                } else {
                    $results['errors'][] = 'WhatsApp: ' . ($whatsappResult['error'] ?? 'Unknown error');
                }
            } catch (\Exception $e) {
                $results['errors'][] = 'WhatsApp exception: ' . $e->getMessage();
            }
        }
        
        // Enviar por Email
        if ($this->config['enable_email'] && $this->email->isEnabled()) {
            try {
                $emailResult = $this->email->sendPriceAlert($user, $productData, $oldPrice, $newPrice);
                
                if ($emailResult['success']) {
                    $results['email_sent'] = true;
                    $results['notifications_sent']++;
                } else {
                    $results['errors'][] = 'Email: ' . ($emailResult['error'] ?? 'Unknown error');
                }
            } catch (\Exception $e) {
                $results['errors'][] = 'Email exception: ' . $e->getMessage();
            }
        }
        
        // Marcar como exitoso si al menos una notificación se envió
        $results['success'] = $results['notifications_sent'] > 0;
        
        // Registrar notificación enviada
        if ($results['success']) {
            $this->logNotificationSent($user['id'], $product['product_id'], 'price_alert');
        }
        
        return $results;
    }
    
    /**
     * Monitorear cambios de stock
     */
    private function monitorStockChanges(): array
    {
        $results = [
            'notifications_sent' => 0,
            'details' => []
        ];
        
        try {
            // Obtener productos que volvieron a tener stock
            $builder = $this->db->table('wishlist w');
            $builder->select('w.*, p.*, u.id as user_id, u.email, u.phone, u.first_name, u.name');
            $builder->join('products p', 'w.product_id = p.id');
            $builder->join('users u', 'w.user_id = u.id');
            $builder->where('w.notification_enabled', 1);
            $builder->where('p.stock_quantity >', 0);
            $builder->where('p.stock_status', 'in_stock');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');
            
            // Solo productos que estaban sin stock recientemente
            $builder->where('w.out_of_stock_notified', 1);
            
            $productsBackInStock = $builder->get()->getResultArray();
            
            foreach ($productsBackInStock as $product) {
                $user = [
                    'id' => $product['user_id'],
                    'email' => $product['email'],
                    'phone' => $product['phone'],
                    'first_name' => $product['first_name'],
                    'name' => $product['name']
                ];
                
                $productData = [
                    'id' => $product['product_id'],
                    'name' => $product['name'],
                    'slug' => $product['slug'],
                    'featured_image' => $product['featured_image'],
                    'stock_quantity' => $product['stock_quantity'],
                    'price_sale' => $product['price_sale'],
                    'price_regular' => $product['price_regular']
                ];
                
                // Enviar notificaciones
                $notificationsSent = 0;
                
                if ($this->config['enable_whatsapp'] && $this->whatsapp->isEnabled()) {
                    $whatsappResult = $this->whatsapp->sendBackInStockAlert($user, $productData);
                    if ($whatsappResult['success']) {
                        $notificationsSent++;
                    }
                }
                
                if ($this->config['enable_email'] && $this->email->isEnabled()) {
                    $emailResult = $this->email->sendBackInStockAlert($user, $productData);
                    if ($emailResult['success']) {
                        $notificationsSent++;
                    }
                }
                
                if ($notificationsSent > 0) {
                    $results['notifications_sent'] += $notificationsSent;
                    
                    // Marcar como notificado
                    $this->db->table('wishlist')
                           ->where('id', $product['id'])
                           ->update(['out_of_stock_notified' => 0]);
                    
                    $results['details'][] = [
                        'type' => 'back_in_stock',
                        'product_id' => $product['product_id'],
                        'product_name' => $product['name'],
                        'user_id' => $product['user_id'],
                        'notifications_sent' => $notificationsSent
                    ];
                }
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Stock monitoring error: ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Obtener precio histórico
     */
    private function getHistoricalPrice(int $productId): float
    {
        // Intentar obtener del cache primero
        $cacheKey = "historical_price_{$productId}";
        $cached = $this->cache->get($cacheKey);
        
        if ($cached !== null) {
            return (float) $cached;
        }
        
        // Obtener de la base de datos
        $builder = $this->db->table('price_history');
        $builder->select('price');
        $builder->where('product_id', $productId);
        $builder->orderBy('created_at', 'DESC');
        $builder->limit(1);
        
        $result = $builder->get()->getRowArray();
        
        if ($result) {
            $price = (float) $result['price'];
            $this->cache->set($cacheKey, $price, 3600); // Cache por 1 hora
            return $price;
        }
        
        // Si no hay historial, usar precio actual
        $product = $this->db->table('products')
                           ->select('price_sale, price_regular')
                           ->where('id', $productId)
                           ->get()
                           ->getRowArray();
        
        if ($product) {
            $price = (float) ($product['price_sale'] ?? $product['price_regular']);
            $this->cache->set($cacheKey, $price, 3600);
            return $price;
        }
        
        return 0.0;
    }
    
    /**
     * Guardar historial de precios
     */
    private function savePriceHistory(int $productId, float $oldPrice, float $newPrice): void
    {
        try {
            $this->db->table('price_history')->insert([
                'product_id' => $productId,
                'old_price' => $oldPrice,
                'new_price' => $newPrice,
                'price_change' => $oldPrice - $newPrice,
                'percentage_change' => $oldPrice > 0 ? (($oldPrice - $newPrice) / $oldPrice) * 100 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Limpiar cache
            $this->cache->delete("historical_price_{$productId}");
            
        } catch (\Exception $e) {
            log_message('error', 'Error saving price history: ' . $e->getMessage());
        }
    }
    
    /**
     * Verificar si puede enviar notificación
     */
    private function canSendNotification(int $userId): bool
    {
        $today = date('Y-m-d');
        
        $count = $this->db->table('notification_log')
                         ->where('user_id', $userId)
                         ->where('DATE(created_at)', $today)
                         ->countAllResults();
        
        return $count < $this->config['max_notifications_per_day'];
    }
    
    /**
     * Registrar notificación enviada
     */
    private function logNotificationSent(int $userId, int $productId, string $type): void
    {
        try {
            $this->db->table('notification_log')->insert([
                'user_id' => $userId,
                'product_id' => $productId,
                'notification_type' => $type,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Actualizar timestamp de última notificación
     */
    private function updateLastNotificationTime(int $wishlistId): void
    {
        $this->db->table('wishlist')
                ->where('id', $wishlistId)
                ->update(['last_notification_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Limpiar notificaciones antiguas
     */
    private function cleanupOldNotifications(): void
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime('-30 days'));
            
            $this->db->table('notification_log')
                    ->where('created_at <', $cutoffDate)
                    ->delete();
                    
        } catch (\Exception $e) {
            log_message('error', 'Error cleaning up notifications: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas del monitor
     */
    public function getStats(int $days = 7): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $stats = [
                'period_days' => $days,
                'total_notifications' => 0,
                'price_alerts' => 0,
                'stock_alerts' => 0,
                'unique_users' => 0,
                'unique_products' => 0,
                'avg_notifications_per_day' => 0
            ];
            
            // Obtener estadísticas de notificaciones
            $builder = $this->db->table('notification_log');
            $builder->select('notification_type, COUNT(*) as count, COUNT(DISTINCT user_id) as unique_users, COUNT(DISTINCT product_id) as unique_products');
            $builder->where('created_at >=', $startDate);
            $builder->groupBy('notification_type');
            
            $results = $builder->get()->getResultArray();
            
            foreach ($results as $result) {
                $stats['total_notifications'] += $result['count'];
                
                if ($result['notification_type'] === 'price_alert') {
                    $stats['price_alerts'] = $result['count'];
                } elseif ($result['notification_type'] === 'stock_alert') {
                    $stats['stock_alerts'] = $result['count'];
                }
                
                $stats['unique_users'] = max($stats['unique_users'], $result['unique_users']);
                $stats['unique_products'] = max($stats['unique_products'], $result['unique_products']);
            }
            
            $stats['avg_notifications_per_day'] = $days > 0 ? round($stats['total_notifications'] / $days, 2) : 0;
            
            return $stats;
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Verificar si está habilitado
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'];
    }
    
    /**
     * Obtener configuración
     */
    public function getConfig(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'check_interval' => $this->config['check_interval'],
            'min_price_change' => $this->config['min_price_change'],
            'min_percentage_change' => $this->config['min_percentage_change'],
            'whatsapp_enabled' => $this->config['enable_whatsapp'] && $this->whatsapp->isEnabled(),
            'email_enabled' => $this->config['enable_email'] && $this->email->isEnabled()
        ];
    }
}
