<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddShippingOptionsToProducts extends Migration
{
    public function up()
    {
        $fields = [
            'shipping_available' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => '1 = Disponible para envío, 0 = Solo en tienda'
            ],
            'pickup_only' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '1 = Solo retiro en tienda, 0 = Permite envío'
            ],
            'shipping_weight_kg' => [
                'type' => 'DECIMAL',
                'constraint' => '10,3',
                'null' => true,
                'comment' => 'Peso en kilogramos para cálculo de envío'
            ],
            'shipping_length_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Largo en centímetros'
            ],
            'shipping_width_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Ancho en centímetros'
            ],
            'shipping_height_cm' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Alto en centímetros'
            ],
            'shipping_category' => [
                'type' => 'ENUM',
                'constraint' => ['small', 'medium', 'large', 'heavy', 'fragile'],
                'default' => 'small',
                'comment' => 'Categoría de envío para determinar método'
            ],
            'requires_special_handling' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '1 = Requiere manejo especial, 0 = Envío normal'
            ],
            'shipping_restrictions' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Restricciones especiales de envío (JSON)'
            ]
        ];

        $this->forge->addColumn('products', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('products', [
            'shipping_available',
            'pickup_only',
            'shipping_weight_kg',
            'shipping_length_cm',
            'shipping_width_cm',
            'shipping_height_cm',
            'shipping_category',
            'requires_special_handling',
            'shipping_restrictions'
        ]);
    }
}
