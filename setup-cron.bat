@echo off
echo 🚀 Configurando tarea programada de alertas del sistema MrCell...

REM Obtener la ruta actual del proyecto
set PROJECT_PATH=%cd%

REM Buscar PHP en el PATH
where php >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ PHP no encontrado en el PATH
    echo 💡 Asegúrate de que PHP esté instalado y agregado al PATH
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('where php') do set PHP_PATH=%%i

echo 📁 Ruta del proyecto: %PROJECT_PATH%
echo 🐘 Ruta de PHP: %PHP_PATH%

REM Crear el comando de la tarea
set TASK_NAME=MrCell_Alertas_Sistema
set TASK_COMMAND="%PHP_PATH%" spark cron:alerts

echo ⚙️ Creando tarea programada: %TASK_NAME%

REM Eliminar tarea existente si existe
schtasks /delete /tn "%TASK_NAME%" /f >nul 2>nul

REM Crear nueva tarea programada (cada 12 horas)
schtasks /create /tn "%TASK_NAME%" /tr "cmd /c cd /d \"%PROJECT_PATH%\" && %TASK_COMMAND% >> \"%PROJECT_PATH%\writable\logs\cron.log\" 2>&1" /sc daily /mo 1 /st 08:00 /f

if %errorlevel% equ 0 (
    echo ✅ Tarea programada creada exitosamente
    echo 📋 La tarea se ejecutará diariamente a las 8:00 AM
    echo 📝 Los logs se guardarán en: %PROJECT_PATH%\writable\logs\cron.log
    
    REM Crear segunda tarea para las 8:00 PM
    schtasks /create /tn "%TASK_NAME%_PM" /tr "cmd /c cd /d \"%PROJECT_PATH%\" && %TASK_COMMAND% >> \"%PROJECT_PATH%\writable\logs\cron.log\" 2>&1" /sc daily /mo 1 /st 20:00 /f
    
    if %errorlevel% equ 0 (
        echo ✅ Segunda tarea programada creada para las 8:00 PM
        echo 📋 Ahora el sistema enviará alertas cada 12 horas (8:00 AM y 8:00 PM)
    )
) else (
    echo ❌ Error creando la tarea programada
    echo 💡 Ejecuta este script como administrador
)

echo.
echo 📋 Tareas programadas actuales relacionadas con MrCell:
schtasks /query /tn "*MrCell*" 2>nul

echo.
echo 🧪 Para probar manualmente, ejecuta:
echo    php spark cron:alerts --test --force
echo.
echo 📤 Para enviar alertas reales, ejecuta:
echo    php spark cron:alerts --force

pause
