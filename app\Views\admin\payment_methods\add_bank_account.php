<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1><i class="fas fa-university me-2"></i>Agregar Cuenta Bancaria</h1>
        <p class="text-muted mb-0">Para: <?= esc($paymentMethod['name']) ?></p>
    </div>
    <div>
        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Nueva Cuenta Bancaria</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bankName" class="form-label">Nombre del Banco <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="bankName" name="bank_name" required
                                       placeholder="Ej: Banco Industrial">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountNumber" class="form-label">Número de Cuenta <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="accountNumber" name="account_number" required
                                       placeholder="Ej: **********">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountType" class="form-label">Tipo de Cuenta</label>
                                <select class="form-select" id="accountType" name="account_type">
                                    <option value="monetaria">Monetaria</option>
                                    <option value="ahorro">Ahorro</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountHolderId" class="form-label">NIT/DPI del Titular</label>
                                <input type="text" class="form-control" id="accountHolderId" name="account_holder_id"
                                       placeholder="Ej: ********-9">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="accountHolder" class="form-label">Titular de la Cuenta <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="accountHolder" name="account_holder" required
                               placeholder="Ej: MrCell Guatemala S.A.">
                    </div>

                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instrucciones Especiales</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="3"
                                  placeholder="Ej: Enviar comprobante por WhatsApp al +502 2345-6789"></textarea>
                        <small class="form-text text-muted">
                            Instrucciones adicionales para el cliente al usar esta cuenta
                        </small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Cuenta
                        </button>
                        <a href="/admin/payment-methods/view/<?= $paymentMethod['id'] ?>" class="btn btn-secondary">
                            Cancelar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Información de ayuda -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Información Importante</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Asegúrate de que la información de la cuenta sea correcta</li>
                    <li>El titular de la cuenta debe coincidir con el nombre registrado en el banco</li>
                    <li>Las instrucciones aparecerán al cliente cuando seleccione este método de pago</li>
                    <li>Puedes agregar múltiples cuentas del mismo banco o de bancos diferentes</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
