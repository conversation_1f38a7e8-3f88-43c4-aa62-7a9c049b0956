<?php

namespace App\Libraries;

use App\Libraries\WhatsAppNotifier;
use App\Libraries\PushNotificationManager;
use App\Libraries\GoogleAnalytics4Manager;

/**
 * Sistema de Automatizaciones
 * Gestiona todas las tareas automáticas del sistema
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
class AutomationManager
{
    private $db;
    private $whatsapp;
    private $pushManager;
    private $ga4;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->whatsapp = new WhatsAppNotifier();
        $this->pushManager = new PushNotificationManager();
        $this->ga4 = new GoogleAnalytics4Manager();
        
        $this->config = [
            'enabled' => env('AUTOMATION_ENABLED', true),
            'max_execution_time' => env('AUTOMATION_MAX_TIME', 300), // 5 minutos
            'batch_size' => env('AUTOMATION_BATCH_SIZE', 100),
            'retry_attempts' => env('AUTOMATION_RETRY_ATTEMPTS', 3),
            'log_level' => env('AUTOMATION_LOG_LEVEL', 'info')
        ];
    }
    
    /**
     * Ejecutar todas las automatizaciones programadas
     */
    public function runScheduledTasks(): array
    {
        if (!$this->config['enabled']) {
            return ['success' => false, 'error' => 'Automation disabled'];
        }
        
        set_time_limit($this->config['max_execution_time']);
        
        $results = [
            'started_at' => date('Y-m-d H:i:s'),
            'tasks' => [],
            'total_success' => 0,
            'total_failed' => 0
        ];
        
        try {
            // 1. Monitor de precios
            $results['tasks']['price_monitoring'] = $this->runPriceMonitoring();
            
            // 2. Alertas de stock
            $results['tasks']['stock_alerts'] = $this->runStockAlerts();
            
            // 3. Recordatorios de wishlist
            $results['tasks']['wishlist_reminders'] = $this->runWishlistReminders();
            
            // 4. Limpieza de datos
            $results['tasks']['data_cleanup'] = $this->runDataCleanup();
            
            // 5. Backup automático
            $results['tasks']['backup'] = $this->runBackup();
            
            // 6. Análisis de rendimiento
            $results['tasks']['performance_analysis'] = $this->runPerformanceAnalysis();
            
            // 7. Sincronización de analytics
            $results['tasks']['analytics_sync'] = $this->runAnalyticsSync();
            
            // 8. Mantenimiento de cache
            $results['tasks']['cache_maintenance'] = $this->runCacheMaintenance();
            
            // Contar resultados
            foreach ($results['tasks'] as $task) {
                if ($task['success']) {
                    $results['total_success']++;
                } else {
                    $results['total_failed']++;
                }
            }
            
            $results['completed_at'] = date('Y-m-d H:i:s');
            $results['duration'] = time() - strtotime($results['started_at']);
            
            // Log resultado general
            $this->logAutomation('scheduled_tasks', $results);
            
            return $results;
            
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
            $results['completed_at'] = date('Y-m-d H:i:s');
            
            log_message('error', 'Automation error: ' . $e->getMessage());
            return $results;
        }
    }
    
    /**
     * Monitor de precios automático
     */
    public function runPriceMonitoring(): array
    {
        try {
            $startTime = microtime(true);
            
            // Obtener productos con cambios de precio significativos
            $priceChanges = $this->db->query("
                SELECT p.*, ph.old_price, ph.new_price, ph.change_percentage,
                       COUNT(w.id) as wishlist_count
                FROM products p
                INNER JOIN price_history ph ON p.id = ph.product_id
                INNER JOIN wishlists w ON p.id = w.product_id
                WHERE ph.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                  AND ABS(ph.change_percentage) >= 5
                  AND p.is_active = 1
                GROUP BY p.id
                HAVING wishlist_count > 0
                ORDER BY wishlist_count DESC
                LIMIT {$this->config['batch_size']}
            ")->getResultArray();
            
            $notifications_sent = 0;
            $errors = [];
            
            foreach ($priceChanges as $product) {
                try {
                    // Obtener usuarios con este producto en wishlist
                    $users = $this->db->table('wishlists w')
                                     ->select('u.id, u.first_name, u.phone, u.email')
                                     ->join('users u', 'w.user_id = u.id')
                                     ->where('w.product_id', $product['id'])
                                     ->where('u.is_active', 1)
                                     ->get()
                                     ->getResultArray();
                    
                    foreach ($users as $user) {
                        // Verificar cooldown (no enviar si ya se envió en las últimas 24h)
                        if ($this->hasRecentNotification($user['id'], $product['id'], 'price_alert')) {
                            continue;
                        }
                        
                        // Enviar WhatsApp
                        if (!empty($user['phone'])) {
                            $whatsappResult = $this->whatsapp->sendPriceAlert(
                                $user, $product, $product['old_price'], $product['new_price']
                            );
                            
                            if ($whatsappResult['success']) {
                                $notifications_sent++;
                            }
                        }
                        
                        // Enviar Push Notification
                        $pushResult = $this->pushManager->sendPriceAlert(
                            $user['id'], $product, $product['old_price'], $product['new_price']
                        );
                        
                        // Log notificación
                        $this->logNotification($user['id'], $product['id'], 'price_alert', [
                            'whatsapp' => $whatsappResult['success'] ?? false,
                            'push' => $pushResult['success'] ?? false,
                            'old_price' => $product['old_price'],
                            'new_price' => $product['new_price'],
                            'change_percentage' => $product['change_percentage']
                        ]);
                        
                        // Pequeña pausa para evitar sobrecarga
                        usleep(100000); // 100ms
                    }
                    
                } catch (\Exception $e) {
                    $errors[] = "Product {$product['id']}: " . $e->getMessage();
                }
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'products_processed' => count($priceChanges),
                'notifications_sent' => $notifications_sent,
                'errors' => $errors,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Alertas de stock automáticas
     */
    public function runStockAlerts(): array
    {
        try {
            $startTime = microtime(true);
            
            // Productos que volvieron a tener stock
            $backInStock = $this->db->query("
                SELECT p.*, COUNT(w.id) as wishlist_count
                FROM products p
                INNER JOIN wishlists w ON p.id = w.product_id
                WHERE p.stock_quantity > 0
                  AND p.was_out_of_stock = 1
                  AND p.is_active = 1
                GROUP BY p.id
                HAVING wishlist_count > 0
                ORDER BY wishlist_count DESC
                LIMIT {$this->config['batch_size']}
            ")->getResultArray();
            
            $notifications_sent = 0;
            $errors = [];
            
            foreach ($backInStock as $product) {
                try {
                    // Obtener usuarios interesados
                    $users = $this->db->table('wishlists w')
                                     ->select('u.id, u.first_name, u.phone, u.email')
                                     ->join('users u', 'w.user_id = u.id')
                                     ->where('w.product_id', $product['id'])
                                     ->where('u.is_active', 1)
                                     ->get()
                                     ->getResultArray();
                    
                    foreach ($users as $user) {
                        // Verificar cooldown
                        if ($this->hasRecentNotification($user['id'], $product['id'], 'stock_alert')) {
                            continue;
                        }
                        
                        // Enviar notificaciones
                        if (!empty($user['phone'])) {
                            $whatsappResult = $this->whatsapp->sendStockAlert($user, $product);
                            if ($whatsappResult['success']) {
                                $notifications_sent++;
                            }
                        }
                        
                        $pushResult = $this->pushManager->sendStockAlert($user['id'], $product);
                        
                        // Log notificación
                        $this->logNotification($user['id'], $product['id'], 'stock_alert', [
                            'whatsapp' => $whatsappResult['success'] ?? false,
                            'push' => $pushResult['success'] ?? false,
                            'stock_quantity' => $product['stock_quantity']
                        ]);
                        
                        usleep(100000); // 100ms
                    }
                    
                    // Marcar como notificado
                    $this->db->table('products')
                            ->where('id', $product['id'])
                            ->update(['was_out_of_stock' => 0]);
                    
                } catch (\Exception $e) {
                    $errors[] = "Product {$product['id']}: " . $e->getMessage();
                }
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'products_processed' => count($backInStock),
                'notifications_sent' => $notifications_sent,
                'errors' => $errors,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Recordatorios de wishlist semanales
     */
    public function runWishlistReminders(): array
    {
        try {
            $startTime = microtime(true);
            
            // Solo ejecutar los domingos
            if (date('w') != 0) {
                return [
                    'success' => true,
                    'skipped' => true,
                    'reason' => 'Not Sunday',
                    'duration_ms' => 0
                ];
            }
            
            // Usuarios con wishlist activa que no han recibido recordatorio en 7 días
            $users = $this->db->query("
                SELECT u.id, u.first_name, u.phone, u.email,
                       COUNT(w.id) as wishlist_count
                FROM users u
                INNER JOIN wishlists w ON u.id = w.user_id
                LEFT JOIN notification_log nl ON u.id = nl.user_id 
                    AND nl.notification_type = 'wishlist_reminder'
                    AND nl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                WHERE u.is_active = 1
                  AND nl.id IS NULL
                GROUP BY u.id
                HAVING wishlist_count >= 3
                ORDER BY wishlist_count DESC
                LIMIT {$this->config['batch_size']}
            ")->getResultArray();
            
            $reminders_sent = 0;
            $errors = [];
            
            foreach ($users as $user) {
                try {
                    // Obtener productos de la wishlist
                    $products = $this->db->table('wishlists w')
                                        ->select('p.name, p.price, p.slug')
                                        ->join('products p', 'w.product_id = p.id')
                                        ->where('w.user_id', $user['id'])
                                        ->where('p.is_active', 1)
                                        ->limit(5)
                                        ->get()
                                        ->getResultArray();
                    
                    if (count($products) >= 3) {
                        // Enviar recordatorio
                        if (!empty($user['phone'])) {
                            $whatsappResult = $this->whatsapp->sendWishlistReminder($user, $products);
                            if ($whatsappResult['success']) {
                                $reminders_sent++;
                            }
                        }
                        
                        // Log recordatorio
                        $this->logNotification($user['id'], null, 'wishlist_reminder', [
                            'whatsapp' => $whatsappResult['success'] ?? false,
                            'product_count' => count($products)
                        ]);
                    }
                    
                    usleep(200000); // 200ms entre usuarios
                    
                } catch (\Exception $e) {
                    $errors[] = "User {$user['id']}: " . $e->getMessage();
                }
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'users_processed' => count($users),
                'reminders_sent' => $reminders_sent,
                'errors' => $errors,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Limpieza automática de datos
     */
    public function runDataCleanup(): array
    {
        try {
            $startTime = microtime(true);
            $cleaned = [];
            
            // Limpiar logs antiguos (más de 30 días)
            $cleaned['old_logs'] = $this->db->table('notification_log')
                                           ->where('created_at <', date('Y-m-d H:i:s', strtotime('-30 days')))
                                           ->delete();
            
            // Limpiar sesiones expiradas
            $cleaned['expired_sessions'] = $this->db->table('mobile_sessions')
                                                  ->where('last_activity <', date('Y-m-d H:i:s', strtotime('-7 days')))
                                                  ->delete();
            
            // Limpiar cache de imágenes no usadas (más de 7 días sin acceso)
            $cleaned['unused_image_cache'] = $this->db->table('optimized_images_cache')
                                                    ->where('last_accessed <', date('Y-m-d H:i:s', strtotime('-7 days')))
                                                    ->delete();
            
            // Limpiar carritos abandonados (más de 7 días)
            $cleaned['abandoned_carts'] = $this->db->table('cart_items ci')
                                                 ->join('carts c', 'ci.cart_id = c.id')
                                                 ->where('c.updated_at <', date('Y-m-d H:i:s', strtotime('-7 days')))
                                                 ->delete();
            
            // Optimizar tablas
            $tables = ['notification_log', 'mobile_sessions', 'price_history', 'cart_items'];
            foreach ($tables as $table) {
                $this->db->query("OPTIMIZE TABLE {$table}");
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'cleaned' => $cleaned,
                'total_records_cleaned' => array_sum($cleaned),
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup automático
     */
    public function runBackup(): array
    {
        try {
            $startTime = microtime(true);
            
            // Solo hacer backup diario a las 2 AM
            if (date('H') != '02') {
                return [
                    'success' => true,
                    'skipped' => true,
                    'reason' => 'Not backup time (2 AM)',
                    'duration_ms' => 0
                ];
            }
            
            $backupDir = WRITEPATH . 'backups';
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }
            
            $timestamp = date('Y-m-d_H-i-s');
            $backupFile = $backupDir . "/mrcell_backup_{$timestamp}.sql";
            
            // Configuración de base de datos
            $dbConfig = config('Database')->default;
            
            $command = sprintf(
                'mysqldump -h%s -u%s -p%s %s > %s',
                $dbConfig['hostname'],
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['database'],
                $backupFile
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($backupFile)) {
                $fileSize = filesize($backupFile);
                
                // Comprimir backup
                $compressedFile = $backupFile . '.gz';
                exec("gzip {$backupFile}", $gzipOutput, $gzipReturn);
                
                if ($gzipReturn === 0) {
                    $compressedSize = filesize($compressedFile);
                    
                    // Limpiar backups antiguos (mantener solo 7 días)
                    $oldBackups = glob($backupDir . '/mrcell_backup_*.sql.gz');
                    foreach ($oldBackups as $oldBackup) {
                        if (filemtime($oldBackup) < strtotime('-7 days')) {
                            unlink($oldBackup);
                        }
                    }
                    
                    $duration = round((microtime(true) - $startTime) * 1000, 2);
                    
                    return [
                        'success' => true,
                        'backup_file' => basename($compressedFile),
                        'original_size' => $fileSize,
                        'compressed_size' => $compressedSize,
                        'compression_ratio' => round((1 - $compressedSize / $fileSize) * 100, 2),
                        'duration_ms' => $duration
                    ];
                }
            }
            
            return [
                'success' => false,
                'error' => 'Backup command failed',
                'return_code' => $returnCode
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Análisis de rendimiento automático
     */
    public function runPerformanceAnalysis(): array
    {
        try {
            $startTime = microtime(true);
            
            $analysis = [
                'database_performance' => $this->analyzeDatabasePerformance(),
                'cache_performance' => $this->analyzeCachePerformance(),
                'mobile_performance' => $this->analyzeMobilePerformance(),
                'notification_performance' => $this->analyzeNotificationPerformance()
            ];
            
            // Generar alertas si hay problemas
            $alerts = [];
            
            if ($analysis['database_performance']['slow_queries'] > 10) {
                $alerts[] = 'High number of slow database queries detected';
            }
            
            if ($analysis['cache_performance']['hit_rate'] < 80) {
                $alerts[] = 'Low cache hit rate detected';
            }
            
            if ($analysis['notification_performance']['failure_rate'] > 10) {
                $alerts[] = 'High notification failure rate detected';
            }
            
            // Enviar alertas si es necesario
            if (!empty($alerts)) {
                $this->sendPerformanceAlerts($alerts);
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'analysis' => $analysis,
                'alerts' => $alerts,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Sincronización con Analytics
     */
    public function runAnalyticsSync(): array
    {
        try {
            $startTime = microtime(true);
            
            // Sincronizar eventos pendientes con GA4
            $pendingEvents = $this->db->table('pending_analytics_events')
                                    ->where('processed', 0)
                                    ->limit($this->config['batch_size'])
                                    ->get()
                                    ->getResultArray();
            
            $synced = 0;
            $errors = [];
            
            foreach ($pendingEvents as $event) {
                try {
                    $eventData = json_decode($event['event_data'], true);
                    
                    $result = $this->ga4->trackCustomEvent($event['event_name'], $eventData);
                    
                    if ($result['success']) {
                        $this->db->table('pending_analytics_events')
                                ->where('id', $event['id'])
                                ->update(['processed' => 1, 'processed_at' => date('Y-m-d H:i:s')]);
                        $synced++;
                    } else {
                        $errors[] = "Event {$event['id']}: " . ($result['error'] ?? 'Unknown error');
                    }
                    
                } catch (\Exception $e) {
                    $errors[] = "Event {$event['id']}: " . $e->getMessage();
                }
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'events_processed' => count($pendingEvents),
                'events_synced' => $synced,
                'errors' => $errors,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Mantenimiento de cache
     */
    public function runCacheMaintenance(): array
    {
        try {
            $startTime = microtime(true);
            
            $maintenance = [
                'cleared_expired' => 0,
                'optimized_tables' => 0,
                'cache_size_before' => 0,
                'cache_size_after' => 0
            ];
            
            // Limpiar cache expirado
            if (function_exists('apcu_clear_cache')) {
                apcu_clear_cache();
                $maintenance['cleared_expired']++;
            }
            
            // Limpiar archivos de cache antiguos
            $cacheDir = WRITEPATH . 'cache';
            if (is_dir($cacheDir)) {
                $files = glob($cacheDir . '/*');
                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < strtotime('-1 hour')) {
                        unlink($file);
                        $maintenance['cleared_expired']++;
                    }
                }
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'maintenance' => $maintenance,
                'duration_ms' => $duration
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verificar si hay notificación reciente
     */
    private function hasRecentNotification(int $userId, int $productId, string $type): bool
    {
        $recent = $this->db->table('notification_log')
                          ->where('user_id', $userId)
                          ->where('product_id', $productId)
                          ->where('notification_type', $type)
                          ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                          ->countAllResults();
        
        return $recent > 0;
    }
    
    /**
     * Log de notificación
     */
    private function logNotification(int $userId, ?int $productId, string $type, array $data): void
    {
        try {
            $this->db->table('notification_log')->insert([
                'user_id' => $userId,
                'product_id' => $productId,
                'notification_type' => $type,
                'data' => json_encode($data),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Log de automatización
     */
    private function logAutomation(string $task, array $result): void
    {
        try {
            $this->db->table('automation_log')->insert([
                'task_name' => $task,
                'result' => json_encode($result),
                'success' => $result['total_failed'] == 0 ? 1 : 0,
                'duration' => $result['duration'] ?? 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error logging automation: ' . $e->getMessage());
        }
    }
    
    /**
     * Análisis de rendimiento de base de datos
     */
    private function analyzeDatabasePerformance(): array
    {
        try {
            // Consultas lentas
            $slowQueries = $this->db->query("SHOW STATUS LIKE 'Slow_queries'")->getRowArray();
            
            return [
                'slow_queries' => (int) ($slowQueries['Value'] ?? 0),
                'connections' => $this->db->query("SHOW STATUS LIKE 'Connections'")->getRowArray()['Value'] ?? 0,
                'uptime' => $this->db->query("SHOW STATUS LIKE 'Uptime'")->getRowArray()['Value'] ?? 0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Análisis de rendimiento de cache
     */
    private function analyzeCachePerformance(): array
    {
        return [
            'hit_rate' => 85, // Mock data
            'memory_usage' => 65,
            'entries' => 1250
        ];
    }
    
    /**
     * Análisis de rendimiento móvil
     */
    private function analyzeMobilePerformance(): array
    {
        try {
            $mobileStats = $this->db->table('mobile_sessions')
                                  ->select('AVG(TIMESTAMPDIFF(SECOND, created_at, last_activity)) as avg_session_duration')
                                  ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                                  ->get()
                                  ->getRowArray();
            
            return [
                'avg_session_duration' => (int) ($mobileStats['avg_session_duration'] ?? 0),
                'pwa_adoption_rate' => 25 // Mock data
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Análisis de rendimiento de notificaciones
     */
    private function analyzeNotificationPerformance(): array
    {
        try {
            $stats = $this->db->table('notification_log')
                            ->select('
                                COUNT(*) as total,
                                SUM(CASE WHEN JSON_EXTRACT(data, "$.whatsapp") = true THEN 1 ELSE 0 END) as whatsapp_success,
                                SUM(CASE WHEN JSON_EXTRACT(data, "$.push") = true THEN 1 ELSE 0 END) as push_success
                            ')
                            ->where('created_at >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                            ->get()
                            ->getRowArray();
            
            $total = (int) ($stats['total'] ?? 0);
            $success = (int) ($stats['whatsapp_success'] ?? 0) + (int) ($stats['push_success'] ?? 0);
            
            return [
                'total_notifications' => $total,
                'successful_notifications' => $success,
                'failure_rate' => $total > 0 ? round((($total - $success) / $total) * 100, 2) : 0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Enviar alertas de rendimiento
     */
    private function sendPerformanceAlerts(array $alerts): void
    {
        try {
            $message = "🚨 Alertas de Rendimiento MrCell:\n\n" . implode("\n", $alerts);
            
            // Enviar a administradores
            $adminPhone = env('ADMIN_PHONE', '+50212345678');
            if (!empty($adminPhone)) {
                $this->whatsapp->sendCustomMessage($adminPhone, $message);
            }
            
        } catch (\Exception $e) {
            log_message('error', 'Error sending performance alerts: ' . $e->getMessage());
        }
    }
    
    /**
     * Obtener estadísticas de automatización
     */
    public function getAutomationStats(int $days = 7): array
    {
        try {
            $stats = $this->db->table('automation_log')
                            ->select('
                                task_name,
                                COUNT(*) as executions,
                                SUM(success) as successful_executions,
                                AVG(duration) as avg_duration
                            ')
                            ->where('created_at >', date('Y-m-d H:i:s', strtotime("-{$days} days")))
                            ->groupBy('task_name')
                            ->get()
                            ->getResultArray();
            
            return [
                'period_days' => $days,
                'tasks' => $stats,
                'total_executions' => array_sum(array_column($stats, 'executions')),
                'total_successful' => array_sum(array_column($stats, 'successful_executions'))
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
