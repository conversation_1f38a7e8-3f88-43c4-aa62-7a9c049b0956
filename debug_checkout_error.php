<?php

/**
 * Script para diagnosticar el error 500 en el checkout
 */

echo "=== DIAGNÓSTICO DE ERROR EN CHECKOUT ===\n";
echo "Fecha: " . date('Y-m-d H:i:s') . "\n";
echo "=======================================\n\n";

// Configuración de base de datos
$dbConfig = [
    'hostname' => '**************',
    'username' => 'mayansourcecom_mrcell',
    'password' => 'Clairo!23',
    'database' => 'mayansourcecom_mrcell',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "✅ Conexión a base de datos exitosa\n\n";

    // 1. Verificar tabla payment_methods
    echo "1️⃣ Verificando tabla payment_methods...\n";
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM payment_methods");
        $result = $stmt->fetch();
        echo "   ✅ Tabla payment_methods existe - Registros: {$result['count']}\n";
        
        // Mostrar métodos de pago activos
        $stmt = $db->query("
            SELECT id, name, slug, is_active 
            FROM payment_methods 
            ORDER BY sort_order, name
        ");
        $methods = $stmt->fetchAll();
        
        echo "   📋 Métodos de pago:\n";
        foreach ($methods as $method) {
            $status = $method['is_active'] ? '✅' : '❌';
            echo "      {$status} {$method['name']} ({$method['slug']})\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error con tabla payment_methods: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 2. Verificar tabla system_settings
    echo "2️⃣ Verificando tabla system_settings...\n";
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_group = 'taxes'");
        $result = $stmt->fetch();
        echo "   ✅ Configuración de impuestos - Registros: {$result['count']}\n";
        
        // Mostrar configuración de impuestos
        $stmt = $db->query("
            SELECT setting_key, setting_value, setting_type, is_active
            FROM system_settings 
            WHERE setting_group = 'taxes'
            ORDER BY setting_key
        ");
        $taxSettings = $stmt->fetchAll();
        
        echo "   📋 Configuración de impuestos:\n";
        foreach ($taxSettings as $setting) {
            $status = $setting['is_active'] ? '✅' : '❌';
            echo "      {$status} {$setting['setting_key']}: {$setting['setting_value']} ({$setting['setting_type']})\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error con configuración de impuestos: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 3. Verificar tabla products
    echo "3️⃣ Verificando tabla products...\n";
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1 AND deleted_at IS NULL");
        $result = $stmt->fetch();
        echo "   ✅ Productos activos: {$result['count']}\n";
        
        // Verificar algunos productos
        $stmt = $db->query("
            SELECT id, name, price_regular, price_sale 
            FROM products 
            WHERE is_active = 1 AND deleted_at IS NULL
            LIMIT 3
        ");
        $products = $stmt->fetchAll();
        
        echo "   📋 Productos de muestra:\n";
        foreach ($products as $product) {
            $price = !empty($product['price_sale']) && $product['price_sale'] > 0 
                ? $product['price_sale'] 
                : $product['price_regular'];
            echo "      ID: {$product['id']} | {$product['name']} | Q" . number_format($price, 2) . "\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error con tabla products: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 4. Verificar tablas de direcciones
    echo "4️⃣ Verificando tablas de direcciones...\n";
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'user_addresses'");
        $result = $stmt->fetch();
        if ($result) {
            echo "   ✅ Tabla user_addresses existe\n";
        } else {
            echo "   ❌ Tabla user_addresses NO existe\n";
        }
        
        $stmt = $db->query("SHOW TABLES LIKE 'shipping_zones'");
        $result = $stmt->fetch();
        if ($result) {
            echo "   ✅ Tabla shipping_zones existe\n";
        } else {
            echo "   ❌ Tabla shipping_zones NO existe\n";
        }
        
        $stmt = $db->query("SHOW TABLES LIKE 'shipping_package_types'");
        $result = $stmt->fetch();
        if ($result) {
            echo "   ✅ Tabla shipping_package_types existe\n";
        } else {
            echo "   ❌ Tabla shipping_package_types NO existe\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error verificando tablas de envío: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 5. Simular cálculo de totales
    echo "5️⃣ Simulando cálculo de totales del carrito...\n";
    try {
        // Simular items del carrito
        $cartItems = [
            [
                'product_id' => 1,
                'name' => 'Producto de Prueba',
                'price' => 100.00,
                'quantity' => 1,
                'subtotal' => 100.00
            ]
        ];
        
        $subtotal = 0;
        foreach ($cartItems as $item) {
            $subtotal += $item['subtotal'];
        }
        
        echo "   ✅ Subtotal calculado: Q" . number_format($subtotal, 2) . "\n";
        
        // Simular configuración de impuestos
        $taxSettings = [
            'tax_enabled' => false,
            'tax_rate' => 0
        ];
        
        $tax = 0;
        if ($taxSettings['tax_enabled']) {
            $tax = $subtotal * ($taxSettings['tax_rate'] / 100);
        }
        
        $total = $subtotal + $tax;
        
        echo "   ✅ Impuesto: Q" . number_format($tax, 2) . "\n";
        echo "   ✅ Total: Q" . number_format($total, 2) . "\n";
        
    } catch (Exception $e) {
        echo "   ❌ Error simulando cálculo: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 6. Verificar configuración de Recurrente
    echo "6️⃣ Verificando configuración de Recurrente...\n";
    try {
        $stmt = $db->query("
            SELECT setting_key, setting_value, is_active
            FROM system_settings 
            WHERE setting_key IN ('recurrente_enabled', 'recurrente_public_key', 'recurrente_secret_key')
        ");
        $recurrenteSettings = $stmt->fetchAll();
        
        if (empty($recurrenteSettings)) {
            echo "   ⚠️  No hay configuración de Recurrente\n";
        } else {
            echo "   📋 Configuración de Recurrente:\n";
            foreach ($recurrenteSettings as $setting) {
                $status = $setting['is_active'] ? '✅' : '❌';
                $value = $setting['setting_key'] === 'recurrente_secret_key' 
                    ? str_repeat('*', strlen($setting['setting_value'])) 
                    : $setting['setting_value'];
                echo "      {$status} {$setting['setting_key']}: {$value}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error verificando Recurrente: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // 7. Probar consulta de métodos de pago con filtro
    echo "7️⃣ Probando consulta de métodos de pago con filtro...\n";
    try {
        $cartTotal = 100.00;
        $cartCurrency = 'GTQ';
        
        $paymentMethods = $db->query("
            SELECT id, name, slug, description, type, icon, instructions
            FROM payment_methods
            WHERE is_active = 1
            ORDER BY sort_order, name
        ")->fetchAll();
        
        echo "   ✅ Métodos de pago obtenidos: " . count($paymentMethods) . "\n";
        
        // Aplicar filtro de Recurrente
        foreach ($paymentMethods as $key => $method) {
            if ($method['slug'] === 'recurrente') {
                $maxPriceUSD = 15000;
                $amountInUSD = $cartTotal;
                
                if ($cartCurrency === 'GTQ') {
                    $amountInUSD = $cartTotal / 7.8;
                }
                
                $isEligible = $amountInUSD <= $maxPriceUSD;
                echo "   📊 Recurrente elegible: " . ($isEligible ? 'SÍ' : 'NO') . " (${amountInUSD} USD)\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error probando métodos de pago: " . $e->getMessage() . "\n";
    }

    echo "\n🎉 Diagnóstico completado.\n";

} catch (Exception $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "\n";
    exit(1);
}
