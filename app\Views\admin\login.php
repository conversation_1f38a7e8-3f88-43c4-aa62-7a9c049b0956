<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Login Administrativo - MrCell' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* <PERSON><PERSON>a <PERSON> - <PERSON>, Negro, Blanco, Gris */
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --success-color: #059669;        /* Verde para éxito */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris medio claro */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Negro gris */
            --gray-900: #111827;             /* Negro */
        }

        body {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--gray-800) 50%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .login-container {
            background: var(--white-color);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            position: relative;
            z-index: 1;
            border: 1px solid var(--gray-200);
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white-color);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        }
        
        .login-header .admin-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            position: relative;
            z-index: 1;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .login-body {
            padding: 40px 30px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background-color: var(--white-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
            background-color: var(--white-color);
        }

        .form-floating > label {
            color: var(--gray-500);
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            color: var(--white-color);
        }

        .btn-login:hover::before {
            left: 100%;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
            padding: 15px 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--primary-dark));
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, var(--info-color), #0ea5e9);
            color: var(--white-color);
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
        }

        .back-to-site {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-site a {
            color: var(--gray-500);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .back-to-site a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="admin-logo">
                <img src="/logo.jpg" alt="MrCell Logo" style="width: 40px; height: 40px; object-fit: contain;">
            </div>
            <h3 class="mb-0">Panel Administrativo</h3>
            <p class="mb-0">MrCell Guatemala</p>
        </div>
        
        <div class="login-body">
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>
            
            <form action="/admin/authenticate" method="POST">
                <?= csrf_field() ?>
                
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                    <label for="email">
                        <i class="fas fa-envelope me-2"></i>Email
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Contraseña" required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>Contraseña
                    </label>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Recordar sesión
                    </label>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                    </button>
                </div>
            </form>
            
            <div class="back-to-site">
                <a href="<?= base_url() ?>">
                    <i class="fas fa-arrow-left me-2"></i>Volver al sitio web
                </a>
            </div>
        </div>
    </div>
    
    <!-- Credenciales de prueba (solo para desarrollo) -->
    <div class="position-fixed bottom-0 start-0 p-3" style="z-index: 1000;">
        <div class="card" style="max-width: 300px;">
            <div class="card-header bg-warning text-dark">
                <small><strong>Credenciales de Prueba</strong></small>
            </div>
            <div class="card-body p-2">
                <small>
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>Password:</strong> 123456<br>
                    <em>o</em><br>
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>Password:</strong> Clairo!23
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus en el campo email
        document.getElementById('email').focus();
        
        // Validación del formulario
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('Por favor, completa todos los campos');
                return false;
            }
            
            // Mostrar loading
            const submitBtn = document.querySelector('.btn-login');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Iniciando sesión...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
