<?php
/**
 * Script de Cron Simplificado para Alertas del Sistema MrCell
 * Versión simplificada que no depende de la inicialización completa de CodeIgniter
 */

// Configurar zona horaria
date_default_timezone_set('America/Guatemala');

// Configurar límites para hosting compartido
ini_set('max_execution_time', 300); // 5 minutos máximo
ini_set('memory_limit', '128M');

// Detectar rutas del proyecto
$projectRoot = dirname(__DIR__); // Subir un nivel desde /public
$logFile = $projectRoot . '/writable/logs/cron-alerts-simple.log';
$logDir = dirname($logFile);

if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry; // También mostrar en consola
}

logMessage("🚀 Iniciando cron simplificado de alertas del sistema MrCell");

try {
    // Cargar configuración de base de datos desde .env
    $envFile = $projectRoot . '/.env';
    if (!file_exists($envFile)) {
        throw new Exception('Archivo .env no encontrado');
    }

    $envContent = file_get_contents($envFile);
    $envLines = explode("\n", $envContent);
    $config = [];
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) continue;
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B'\"");
            $config[$key] = $value;
        }
    }

    // Conectar a la base de datos
    $host = $config['database.default.hostname'] ?? 'localhost';
    $database = $config['database.default.database'] ?? '';
    $username = $config['database.default.username'] ?? '';
    $password = $config['database.default.password'] ?? '';
    $port = $config['database.default.port'] ?? 3306;

    if (empty($database) || empty($username)) {
        throw new Exception('Configuración de base de datos incompleta');
    }

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    logMessage("✅ Conexión a la base de datos establecida");

    // Crear tabla de cron_executions si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cron_executions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_name VARCHAR(100) NOT NULL,
            status ENUM('success', 'error') DEFAULT 'success',
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_task_name (task_name),
            INDEX idx_created_at (created_at)
        )
    ");

    // Verificar si debe ejecutarse (cada 12 horas)
    $stmt = $pdo->query("
        SELECT MAX(created_at) as last_run 
        FROM cron_executions 
        WHERE task_name = 'scheduled_alerts'
    ");
    $lastExecution = $stmt->fetch();

    $shouldRun = true;
    if ($lastExecution && $lastExecution['last_run']) {
        $lastRun = new DateTime($lastExecution['last_run']);
        $now = new DateTime();
        $diff = $now->diff($lastRun);
        
        // Solo ejecutar si han pasado más de 12 horas
        if ($diff->h < 12 && $diff->days == 0) {
            //$shouldRun = false;
            logMessage("⏭️ Cron ya ejecutado en las últimas 12 horas, saltando ejecución");
        }
    }

    if (!$shouldRun) {
        logMessage("✅ Cron completado (sin ejecución necesaria)");
        exit(0);
    }

    logMessage("🔍 Iniciando verificación de alertas...");

    // Obtener configuración de alertas
    $stmt = $pdo->query("
        SELECT setting_key, setting_value FROM system_settings 
        WHERE setting_key IN ('whatsapp_alerts_group', 'whatsapp_alerts_is_group') 
    ");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    $groupNumber = $settings['whatsapp_alerts_group'] ?? '120363416393766854';
    $isGroup = ($settings['whatsapp_alerts_is_group'] ?? '1') == '1';
    
    // Si es grupo, agregar @g.us al final
    if ($isGroup && !str_ends_with($groupNumber, '@g.us')) {
        $groupNumber .= '@g.us';
    }
    
    logMessage("📱 Número configurado: $groupNumber" . ($isGroup ? ' (Grupo)' : ' (Individual)'));

    $alerts = [];

    // 1. Verificar productos con bajo stock
    logMessage("📦 Verificando productos con bajo stock...");
    $stmt = $pdo->query("
        SELECT id, name, sku, stock_quantity, stock_min
        FROM products 
        WHERE stock_quantity <= stock_min 
            AND stock_quantity >= 0
            AND is_active = 1 
            AND deleted_at IS NULL
        ORDER BY stock_quantity ASC
        LIMIT 15
    ");
    $lowStockProducts = $stmt->fetchAll();

    if (!empty($lowStockProducts)) {
        $message = "🔴 *PRODUCTOS CON BAJO STOCK*\n\n";
        foreach ($lowStockProducts as $product) {
            $message .= "• *{$product['name']}* (SKU: {$product['sku']})\n";
            $message .= "  Stock: {$product['stock_quantity']} | Mínimo: {$product['stock_min']}\n\n";
        }
        
        $alerts[] = [
            'type' => 'low_stock',
            'message' => $message,
            'count' => count($lowStockProducts)
        ];
        
        logMessage("⚠️ Encontrados " . count($lowStockProducts) . " productos con bajo stock");
    }

    // 2. Verificar productos próximos a caducar
    logMessage("⏰ Verificando productos próximos a caducar...");
    $stmt = $pdo->query("
        SELECT id, name, sku, expiration_date, stock_quantity,
               DATEDIFF(expiration_date, CURDATE()) as days_until_expiration
        FROM products 
        WHERE has_expiration = 1 
            AND is_active = 1 
            AND deleted_at IS NULL
            AND expiration_date IS NOT NULL
            AND (
                expiration_date <= CURDATE() 
                OR DATEDIFF(expiration_date, CURDATE()) <= COALESCE(expiration_alert_days, 7)
            )
        ORDER BY expiration_date ASC
        LIMIT 15
    ");
    $expiringProducts = $stmt->fetchAll();

    if (!empty($expiringProducts)) {
        $expired = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] < 0);
        $expiresToday = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] == 0);
        $expiresSoon = array_filter($expiringProducts, fn($p) => $p['days_until_expiration'] > 0);

        $message = "⏰ *PRODUCTOS POR CADUCAR*\n\n";

        if (!empty($expired)) {
            $message .= "🔴 *CADUCADOS:*\n";
            foreach ($expired as $product) {
                $message .= "• *{$product['name']}* - " . date('d/m/Y', strtotime($product['expiration_date'])) . "\n";
            }
            $message .= "\n";
        }

        if (!empty($expiresToday)) {
            $message .= "🟡 *CADUCAN HOY:*\n";
            foreach ($expiresToday as $product) {
                $message .= "• *{$product['name']}*\n";
            }
            $message .= "\n";
        }

        if (!empty($expiresSoon)) {
            $message .= "🟠 *PRÓXIMOS A CADUCAR:*\n";
            foreach ($expiresSoon as $product) {
                $message .= "• *{$product['name']}* - {$product['days_until_expiration']} días\n";
            }
        }

        $alerts[] = [
            'type' => 'expiration',
            'message' => $message,
            'count' => count($expiringProducts)
        ];

        logMessage("⚠️ Encontrados " . count($expiringProducts) . " productos próximos a caducar");
    }

    // 3. Verificar pedidos pendientes
    logMessage("📋 Verificando pedidos pendientes...");
    $stmt = $pdo->query("
        SELECT 
            COUNT(CASE WHEN status = 'pending' AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'shipped' AND updated_at <= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 END) as shipped_count
        FROM orders
    ");
    $pendingOrders = $stmt->fetch();

    $pendingCount = $pendingOrders['pending_count'] ?? 0;
    $shippedCount = $pendingOrders['shipped_count'] ?? 0;

    if ($pendingCount > 0 || $shippedCount > 0) {
        $message = "📦 *PEDIDOS PENDIENTES*\n\n";
        
        if ($pendingCount > 0) {
            $message .= "🔴 *Pendientes (>24h):* $pendingCount\n";
        }
        
        if ($shippedCount > 0) {
            $message .= "🚚 *Enviados (>3 días):* $shippedCount\n";
        }

        $alerts[] = [
            'type' => 'orders',
            'message' => $message,
            'pending_count' => $pendingCount,
            'shipped_count' => $shippedCount
        ];

        logMessage("⚠️ Encontrados $pendingCount pedidos pendientes y $shippedCount enviados");
    }

    // Enviar notificaciones si hay alertas
    if (!empty($alerts)) {
        logMessage("📤 Preparando envío de " . count($alerts) . " alertas...");

        // Crear mensaje consolidado
        $fullMessage = "🚨 *ALERTAS DEL SISTEMA MRCELL*\n";
        $fullMessage .= "📅 " . date('d/m/Y H:i:s') . "\n\n";

        foreach ($alerts as $alert) {
            $fullMessage .= $alert['message'] . "\n";
            $fullMessage .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
        }

        $fullMessage .= "💻 *Panel de Administración:*\n";
        $fullMessage .= "https://mrcell.com.gt/admin/dashboard\n\n";
        $fullMessage .= "🤖 _Mensaje automático del sistema_";

        // Enviar usando cURL directamente
        $success = sendWhatsAppMessage($groupNumber, $fullMessage);
        
        if ($success) {
            logMessage("✅ Alertas enviadas exitosamente al número: $groupNumber");
        } else {
            logMessage("❌ Error enviando alertas");
        }
    } else {
        logMessage("✅ No se encontraron alertas que enviar");
    }

    // Registrar ejecución del cron
    $stmt = $pdo->prepare("
        INSERT INTO cron_executions (task_name, status, message, created_at) 
        VALUES ('scheduled_alerts', 'success', ?, NOW())
    ");
    $stmt->execute(['Alertas procesadas: ' . count($alerts)]);

    logMessage("✅ Cron de alertas completado exitosamente");

} catch (Exception $e) {
    $errorMsg = "❌ Error en cron de alertas: " . $e->getMessage();
    logMessage($errorMsg);
    logMessage("📍 Archivo: " . $e->getFile() . ":" . $e->getLine());
    
    // Registrar error en la base de datos si es posible
    try {
        if (isset($pdo)) {
            $stmt = $pdo->prepare("
                INSERT INTO cron_executions (task_name, status, message, created_at) 
                VALUES ('scheduled_alerts', 'error', ?, NOW())
            ");
            $stmt->execute([$e->getMessage()]);
        }
    } catch (Exception $dbError) {
        logMessage("❌ No se pudo registrar error en BD: " . $dbError->getMessage());
    }
    
    exit(1);
}

/**
 * Función para enviar mensaje de WhatsApp usando cURL
 */
function sendWhatsAppMessage($phoneNumber, $message) {
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://167.114.111.52/api/sendMessage',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode([
            'phone' => $phoneNumber,
            'messageType' => 1,
            'token' => '2ZZXYU6Q',
            'chat' => $message
        ]),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'X-API-Key: msa_662507a99485d3fb883fca2e283bd598127c525ba62f4a9d763c8e85de7f4819'
        ),
    ));

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($httpCode === 200) {
        $responseData = json_decode($response, true);
        return isset($responseData['status']) && $responseData['status'] === true;
    }

    return false;
}

logMessage("🏁 Script de cron finalizado");
?>
