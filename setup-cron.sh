#!/bin/bash

# Script para configurar el cron de alertas del sistema MrCell
# Ejecutar cada 12 horas

echo "🚀 Configurando cron de alertas del sistema MrCell..."

# Obtener la ruta actual del proyecto
PROJECT_PATH=$(pwd)
PHP_PATH=$(which php)

echo "📁 Ruta del proyecto: $PROJECT_PATH"
echo "🐘 Ruta de PHP: $PHP_PATH"

# Crear el comando del cron
CRON_COMMAND="0 */12 * * * cd $PROJECT_PATH && $PHP_PATH spark cron:alerts >> $PROJECT_PATH/writable/logs/cron.log 2>&1"

echo "⚙️ Comando del cron: $CRON_COMMAND"

# Verificar si el cron ya existe
if crontab -l 2>/dev/null | grep -q "cron:alerts"; then
    echo "⚠️ El cron ya existe. Actualizando..."
    # Remover cron existente
    crontab -l 2>/dev/null | grep -v "cron:alerts" | crontab -
fi

# Agregar el nuevo cron
(crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -

echo "✅ Cron configurado exitosamente"
echo "📋 El sistema enviará alertas cada 12 horas"
echo "📝 Los logs se guardarán en: $PROJECT_PATH/writable/logs/cron.log"

# Mostrar crons actuales
echo ""
echo "📋 Crons actuales:"
crontab -l

echo ""
echo "🧪 Para probar manualmente, ejecuta:"
echo "   php spark cron:alerts --test --force"
echo ""
echo "📤 Para enviar alertas reales, ejecuta:"
echo "   php spark cron:alerts --force"
