<?php

namespace App\Libraries;

/**
 * Gestor de Configuración Dinámico
 * Sistema completo de configuración con cache y validación
 */
class ConfigManager
{
    private $db;
    private $cache;
    private $logger;
    private $config;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
        $this->cache = new AdvancedCache();
        $this->logger = new AdvancedLogger();
        
        $this->config = [
            'cache_ttl' => env('CONFIG_CACHE_TTL', 3600),
            'cache_prefix' => 'config:',
            'validation_enabled' => env('CONFIG_VALIDATION_ENABLED', true),
            'audit_enabled' => env('CONFIG_AUDIT_ENABLED', true),
            'encryption_enabled' => env('CONFIG_ENCRYPTION_ENABLED', false),
            'backup_enabled' => env('CONFIG_BACKUP_ENABLED', true)
        ];
        
        $this->createConfigTables();
    }
    
    /**
     * Obtener configuración
     */
    public function get(string $key, $default = null, bool $useCache = true)
    {
        try {
            // Intentar obtener del cache primero
            if ($useCache) {
                $cacheKey = $this->config['cache_prefix'] . $key;
                $cached = $this->cache->get($cacheKey);
                
                if ($cached !== null) {
                    return $this->decryptValue($cached);
                }
            }
            
            // Obtener de la base de datos
            $result = $this->db->table('system_config')
                              ->where('config_key', $key)
                              ->where('is_active', 1)
                              ->get()
                              ->getRowArray();
            
            if ($result) {
                $value = $this->parseValue($result['config_value'], $result['data_type']);
                
                // Guardar en cache
                if ($useCache) {
                    $this->cache->set($cacheKey, $this->encryptValue($value), $this->config['cache_ttl']);
                }
                
                return $value;
            }
            
            return $default;
            
        } catch (\Exception $e) {
            $this->logger->error("Config get error: " . $e->getMessage(), ['key' => $key]);
            return $default;
        }
    }
    
    /**
     * Establecer configuración
     */
    public function set(string $key, $value, string $description = '', int $userId = null): bool
    {
        try {
            $dataType = $this->detectDataType($value);
            $stringValue = $this->stringifyValue($value, $dataType);
            
            // Validar valor si está habilitado
            if ($this->config['validation_enabled']) {
                if (!$this->validateValue($key, $value)) {
                    throw new \Exception("Invalid value for config key: $key");
                }
            }
            
            // Crear backup si está habilitado
            if ($this->config['backup_enabled']) {
                $this->backupConfig($key);
            }
            
            // Verificar si la configuración existe
            $existing = $this->db->table('system_config')
                                ->where('config_key', $key)
                                ->get()
                                ->getRowArray();
            
            $data = [
                'config_value' => $stringValue,
                'data_type' => $dataType,
                'description' => $description,
                'updated_by' => $userId,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if ($existing) {
                // Actualizar existente
                $result = $this->db->table('system_config')
                                  ->where('config_key', $key)
                                  ->update($data);
            } else {
                // Crear nuevo
                $data['config_key'] = $key;
                $data['created_by'] = $userId;
                $data['created_at'] = date('Y-m-d H:i:s');
                $data['is_active'] = 1;
                
                $result = $this->db->table('system_config')->insert($data);
            }
            
            if ($result) {
                // Limpiar cache
                $this->clearCache($key);
                
                // Auditoría
                if ($this->config['audit_enabled']) {
                    $this->auditConfigChange($key, $existing['config_value'] ?? null, $stringValue, $userId);
                }
                
                $this->logger->info("Config updated: $key", [
                    'user_id' => $userId,
                    'data_type' => $dataType
                ]);
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logger->error("Config set error: " . $e->getMessage(), [
                'key' => $key,
                'user_id' => $userId
            ]);
            return false;
        }
    }
    
    /**
     * Eliminar configuración
     */
    public function delete(string $key, int $userId = null): bool
    {
        try {
            // Crear backup antes de eliminar
            if ($this->config['backup_enabled']) {
                $this->backupConfig($key);
            }
            
            $result = $this->db->table('system_config')
                              ->where('config_key', $key)
                              ->update([
                                  'is_active' => 0,
                                  'deleted_by' => $userId,
                                  'deleted_at' => date('Y-m-d H:i:s')
                              ]);
            
            if ($result) {
                // Limpiar cache
                $this->clearCache($key);
                
                // Auditoría
                if ($this->config['audit_enabled']) {
                    $this->auditConfigChange($key, 'EXISTS', 'DELETED', $userId);
                }
                
                $this->logger->info("Config deleted: $key", ['user_id' => $userId]);
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logger->error("Config delete error: " . $e->getMessage(), [
                'key' => $key,
                'user_id' => $userId
            ]);
            return false;
        }
    }
    
    /**
     * Obtener múltiples configuraciones
     */
    public function getMultiple(array $keys, bool $useCache = true): array
    {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->get($key, null, $useCache);
        }
        
        return $results;
    }
    
    /**
     * Establecer múltiples configuraciones
     */
    public function setMultiple(array $configs, int $userId = null): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($configs as $key => $data) {
            $value = $data['value'] ?? $data;
            $description = $data['description'] ?? '';
            
            if ($this->set($key, $value, $description, $userId)) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Failed to set: $key";
            }
        }
        
        return $results;
    }
    
    /**
     * Obtener configuraciones por grupo
     */
    public function getGroup(string $group, bool $useCache = true): array
    {
        try {
            $cacheKey = $this->config['cache_prefix'] . "group:$group";
            
            if ($useCache) {
                $cached = $this->cache->get($cacheKey);
                if ($cached !== null) {
                    return $cached;
                }
            }
            
            $results = $this->db->table('system_config')
                               ->like('config_key', $group . '.', 'after')
                               ->where('is_active', 1)
                               ->get()
                               ->getResultArray();
            
            $configs = [];
            foreach ($results as $row) {
                $key = str_replace($group . '.', '', $row['config_key']);
                $configs[$key] = $this->parseValue($row['config_value'], $row['data_type']);
            }
            
            if ($useCache) {
                $this->cache->set($cacheKey, $configs, $this->config['cache_ttl']);
            }
            
            return $configs;
            
        } catch (\Exception $e) {
            $this->logger->error("Config get group error: " . $e->getMessage(), ['group' => $group]);
            return [];
        }
    }
    
    /**
     * Configuraciones del sitio
     */
    public function getSiteConfig(): array
    {
        return $this->getGroup('site');
    }
    
    /**
     * Configuraciones de email
     */
    public function getEmailConfig(): array
    {
        return $this->getGroup('email');
    }
    
    /**
     * Configuraciones de pago
     */
    public function getPaymentConfig(): array
    {
        return $this->getGroup('payment');
    }
    
    /**
     * Configuraciones de notificaciones
     */
    public function getNotificationConfig(): array
    {
        return $this->getGroup('notification');
    }
    
    /**
     * Configuraciones de automatización
     */
    public function getAutomationConfig(): array
    {
        return $this->getGroup('automation');
    }
    
    /**
     * Validar configuración
     */
    public function validate(string $key, $value): array
    {
        $validation = [
            'valid' => true,
            'errors' => []
        ];
        
        try {
            // Validaciones específicas por clave
            $validators = $this->getValidators();
            
            if (isset($validators[$key])) {
                foreach ($validators[$key] as $rule) {
                    if (!$this->applyValidationRule($value, $rule)) {
                        $validation['valid'] = false;
                        $validation['errors'][] = "Validation failed for rule: {$rule['type']}";
                    }
                }
            }
            
            // Validaciones generales por tipo de dato
            $dataType = $this->detectDataType($value);
            if (!$this->validateDataType($value, $dataType)) {
                $validation['valid'] = false;
                $validation['errors'][] = "Invalid data type: $dataType";
            }
            
        } catch (\Exception $e) {
            $validation['valid'] = false;
            $validation['errors'][] = $e->getMessage();
        }
        
        return $validation;
    }
    
    /**
     * Exportar configuraciones
     */
    public function export(array $keys = [], string $format = 'json'): array
    {
        try {
            $builder = $this->db->table('system_config')
                               ->where('is_active', 1);
            
            if (!empty($keys)) {
                $builder->whereIn('config_key', $keys);
            }
            
            $results = $builder->get()->getResultArray();
            
            $configs = [];
            foreach ($results as $row) {
                $configs[$row['config_key']] = [
                    'value' => $this->parseValue($row['config_value'], $row['data_type']),
                    'type' => $row['data_type'],
                    'description' => $row['description'],
                    'updated_at' => $row['updated_at']
                ];
            }
            
            $filename = 'config_export_' . date('Y-m-d_H-i-s') . '.' . $format;
            $filepath = WRITEPATH . 'exports/' . $filename;
            
            // Crear directorio si no existe
            if (!is_dir(dirname($filepath))) {
                mkdir(dirname($filepath), 0755, true);
            }
            
            switch ($format) {
                case 'json':
                    file_put_contents($filepath, json_encode($configs, JSON_PRETTY_PRINT));
                    break;
                case 'php':
                    $phpContent = "<?php\n\nreturn " . var_export($configs, true) . ";\n";
                    file_put_contents($filepath, $phpContent);
                    break;
                default:
                    throw new \Exception("Unsupported export format: $format");
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'count' => count($configs)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Importar configuraciones
     */
    public function import(string $filepath, int $userId = null, bool $overwrite = false): array
    {
        try {
            if (!file_exists($filepath)) {
                throw new \Exception("Import file not found: $filepath");
            }
            
            $extension = pathinfo($filepath, PATHINFO_EXTENSION);
            
            switch ($extension) {
                case 'json':
                    $configs = json_decode(file_get_contents($filepath), true);
                    break;
                case 'php':
                    $configs = include $filepath;
                    break;
                default:
                    throw new \Exception("Unsupported import format: $extension");
            }
            
            $results = [
                'imported' => 0,
                'skipped' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            foreach ($configs as $key => $data) {
                try {
                    // Verificar si existe y si debe sobrescribir
                    if (!$overwrite && $this->get($key) !== null) {
                        $results['skipped']++;
                        continue;
                    }
                    
                    $value = $data['value'] ?? $data;
                    $description = $data['description'] ?? '';
                    
                    if ($this->set($key, $value, $description, $userId)) {
                        $results['imported']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to import: $key";
                    }
                    
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Error importing $key: " . $e->getMessage();
                }
            }
            
            $this->logger->info("Config import completed", array_merge($results, [
                'user_id' => $userId,
                'file' => $filepath
            ]));
            
            return array_merge(['success' => true], $results);
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener historial de cambios
     */
    public function getHistory(string $key = null, int $limit = 50): array
    {
        try {
            $builder = $this->db->table('config_audit')
                               ->orderBy('created_at', 'DESC')
                               ->limit($limit);
            
            if ($key) {
                $builder->where('config_key', $key);
            }
            
            return [
                'success' => true,
                'history' => $builder->get()->getResultArray()
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Restaurar configuración desde backup
     */
    public function restore(string $key, string $backupId, int $userId = null): bool
    {
        try {
            $backup = $this->db->table('config_backups')
                              ->where('id', $backupId)
                              ->where('config_key', $key)
                              ->get()
                              ->getRowArray();
            
            if (!$backup) {
                throw new \Exception("Backup not found: $backupId");
            }
            
            $value = $this->parseValue($backup['config_value'], $backup['data_type']);
            
            return $this->set($key, $value, "Restored from backup $backupId", $userId);
            
        } catch (\Exception $e) {
            $this->logger->error("Config restore error: " . $e->getMessage(), [
                'key' => $key,
                'backup_id' => $backupId,
                'user_id' => $userId
            ]);
            return false;
        }
    }
    
    /**
     * Limpiar cache de configuración
     */
    public function clearCache(string $key = null): bool
    {
        try {
            if ($key) {
                $cacheKey = $this->config['cache_prefix'] . $key;
                return $this->cache->delete($cacheKey);
            } else {
                return $this->cache->clearByPattern($this->config['cache_prefix'] . '*') > 0;
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Config cache clear error: " . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }
    
    /**
     * Detectar tipo de dato
     */
    private function detectDataType($value): string
    {
        if (is_bool($value)) return 'boolean';
        if (is_int($value)) return 'integer';
        if (is_float($value)) return 'float';
        if (is_array($value)) return 'array';
        if (is_object($value)) return 'object';
        return 'string';
    }
    
    /**
     * Convertir valor a string
     */
    private function stringifyValue($value, string $dataType): string
    {
        switch ($dataType) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'array':
            case 'object':
                return json_encode($value);
            default:
                return (string)$value;
        }
    }
    
    /**
     * Parsear valor desde string
     */
    private function parseValue(string $value, string $dataType)
    {
        switch ($dataType) {
            case 'boolean':
                return $value === '1' || strtolower($value) === 'true';
            case 'integer':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'array':
            case 'object':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * Validar valor
     */
    private function validateValue(string $key, $value): bool
    {
        $validation = $this->validate($key, $value);
        return $validation['valid'];
    }
    
    /**
     * Obtener validadores
     */
    private function getValidators(): array
    {
        return [
            'site.name' => [
                ['type' => 'required'],
                ['type' => 'max_length', 'value' => 100]
            ],
            'site.email' => [
                ['type' => 'required'],
                ['type' => 'email']
            ],
            'site.phone' => [
                ['type' => 'phone']
            ],
            'email.smtp_port' => [
                ['type' => 'integer'],
                ['type' => 'range', 'min' => 1, 'max' => 65535]
            ],
            'payment.tax_rate' => [
                ['type' => 'numeric'],
                ['type' => 'range', 'min' => 0, 'max' => 100]
            ]
        ];
    }
    
    /**
     * Aplicar regla de validación
     */
    private function applyValidationRule($value, array $rule): bool
    {
        switch ($rule['type']) {
            case 'required':
                return !empty($value);
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'integer':
                return is_int($value) || ctype_digit($value);
            case 'numeric':
                return is_numeric($value);
            case 'max_length':
                return strlen($value) <= $rule['value'];
            case 'range':
                return $value >= $rule['min'] && $value <= $rule['max'];
            case 'phone':
                return preg_match('/^[\+]?[0-9\-\(\)\s]+$/', $value);
            default:
                return true;
        }
    }
    
    /**
     * Validar tipo de dato
     */
    private function validateDataType($value, string $expectedType): bool
    {
        $actualType = $this->detectDataType($value);
        return $actualType === $expectedType;
    }
    
    /**
     * Crear backup de configuración
     */
    private function backupConfig(string $key): void
    {
        try {
            $current = $this->db->table('system_config')
                               ->where('config_key', $key)
                               ->where('is_active', 1)
                               ->get()
                               ->getRowArray();
            
            if ($current) {
                $this->db->table('config_backups')->insert([
                    'config_key' => $key,
                    'config_value' => $current['config_value'],
                    'data_type' => $current['data_type'],
                    'backup_date' => date('Y-m-d H:i:s'),
                    'created_by' => $current['updated_by']
                ]);
            }
            
        } catch (\Exception $e) {
            $this->logger->warning("Config backup failed: " . $e->getMessage(), ['key' => $key]);
        }
    }
    
    /**
     * Auditar cambio de configuración
     */
    private function auditConfigChange(string $key, ?string $oldValue, string $newValue, ?int $userId): void
    {
        try {
            $this->db->table('config_audit')->insert([
                'config_key' => $key,
                'old_value' => $oldValue,
                'new_value' => $newValue,
                'changed_by' => $userId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->warning("Config audit failed: " . $e->getMessage(), ['key' => $key]);
        }
    }
    
    /**
     * Encriptar valor si está habilitado
     */
    private function encryptValue($value)
    {
        if ($this->config['encryption_enabled'] && is_string($value)) {
            // Implementar encriptación
            return $value;
        }
        
        return $value;
    }
    
    /**
     * Desencriptar valor si está habilitado
     */
    private function decryptValue($value)
    {
        if ($this->config['encryption_enabled'] && is_string($value)) {
            // Implementar desencriptación
            return $value;
        }
        
        return $value;
    }
    
    /**
     * Crear tablas de configuración
     */
    private function createConfigTables(): void
    {
        try {
            // Tabla principal de configuración
            $this->db->query("
                CREATE TABLE IF NOT EXISTS system_config (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    config_key VARCHAR(255) NOT NULL UNIQUE,
                    config_value TEXT,
                    data_type VARCHAR(50) DEFAULT 'string',
                    description TEXT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_by INT,
                    updated_by INT,
                    deleted_by INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    deleted_at TIMESTAMP NULL,
                    INDEX idx_config_key (config_key),
                    INDEX idx_is_active (is_active)
                )
            ");
            
            // Tabla de auditoría
            $this->db->query("
                CREATE TABLE IF NOT EXISTS config_audit (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    config_key VARCHAR(255) NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    changed_by INT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_config_key (config_key),
                    INDEX idx_created_at (created_at)
                )
            ");
            
            // Tabla de backups
            $this->db->query("
                CREATE TABLE IF NOT EXISTS config_backups (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    config_key VARCHAR(255) NOT NULL,
                    config_value TEXT,
                    data_type VARCHAR(50),
                    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INT,
                    INDEX idx_config_key (config_key),
                    INDEX idx_backup_date (backup_date)
                )
            ");
            
        } catch (\Exception $e) {
            $this->logger->error("Config tables creation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Obtener configuración actual
     */
    public function getConfig(): array
    {
        return [
            'cache_enabled' => $this->config['cache_ttl'] > 0,
            'validation_enabled' => $this->config['validation_enabled'],
            'audit_enabled' => $this->config['audit_enabled'],
            'encryption_enabled' => $this->config['encryption_enabled'],
            'backup_enabled' => $this->config['backup_enabled']
        ];
    }
}
