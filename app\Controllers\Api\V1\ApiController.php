<?php

namespace App\Controllers\Api\V1;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\ApiManager;

/**
 * Controlador de API V1
 * Controlador principal para todas las rutas de API
 */
class ApiController extends ResourceController
{
    protected $format = 'json';
    private $apiManager;
    
    public function __construct()
    {
        $this->apiManager = new ApiManager();
    }
    
    /**
     * Manejar todas las rutas de API
     */
    public function handleRequest($endpoint = null)
    {
        try {
            $method = $this->request->getMethod();
            $data = $this->getRequestData();
            $headers = $this->getRequestHeaders();
            
            // Procesar request a través del ApiManager
            $response = $this->apiManager->processRequest($endpoint, $method, $data, $headers);
            
            // Establecer código de respuesta HTTP
            $this->response->setStatusCode($response['status'] ?? 200);
            
            // Agregar headers de rate limiting si están disponibles
            if (isset($response['rate_limit_headers'])) {
                foreach ($response['rate_limit_headers'] as $header => $value) {
                    $this->response->setHeader($header, $value);
                }
            }
            
            return $this->respond($response);
            
        } catch (\Exception $e) {
            return $this->fail('Internal server error', 500);
        }
    }
    
    /**
     * Endpoint de productos
     */
    public function products()
    {
        return $this->handleRequest('products');
    }
    
    /**
     * Endpoint de órdenes
     */
    public function orders()
    {
        return $this->handleRequest('orders');
    }
    
    /**
     * Endpoint de usuarios
     */
    public function users()
    {
        return $this->handleRequest('users');
    }
    
    /**
     * Endpoint de categorías
     */
    public function categories()
    {
        return $this->handleRequest('categories');
    }
    
    /**
     * Endpoint de estadísticas
     */
    public function stats()
    {
        return $this->handleRequest('stats');
    }
    
    /**
     * Documentación de la API
     */
    public function documentation()
    {
        $docs = $this->apiManager->generateDocumentation();
        
        if (!$docs['success']) {
            return $this->fail($docs['error'], 503);
        }
        
        return $this->respond($docs['documentation']);
    }
    
    /**
     * Estado de la API
     */
    public function status()
    {
        $status = [
            'success' => true,
            'status' => 'operational',
            'version' => $this->apiManager->getConfig()['version'],
            'timestamp' => date('c'),
            'uptime' => $this->getApiUptime(),
            'endpoints' => [
                'products' => 'operational',
                'orders' => 'operational',
                'users' => 'operational',
                'categories' => 'operational',
                'stats' => 'operational'
            ]
        ];
        
        return $this->respond($status);
    }
    
    /**
     * Información de la API
     */
    public function info()
    {
        $config = $this->apiManager->getConfig();
        
        $info = [
            'success' => true,
            'api' => [
                'name' => 'MrCell Guatemala API',
                'version' => $config['version'],
                'description' => 'API REST completa para el sistema de ecommerce',
                'base_url' => base_url('api/v1'),
                'documentation_url' => base_url('api/v1/docs'),
                'status_url' => base_url('api/v1/status')
            ],
            'features' => [
                'authentication' => $config['auth_required'],
                'rate_limiting' => true,
                'cors' => $config['cors_enabled'],
                'caching' => $config['cache_responses'],
                'documentation' => $config['documentation_enabled']
            ],
            'limits' => [
                'rate_limit' => $config['rate_limit'] . ' requests/hour',
                'max_page_size' => 100,
                'default_page_size' => 20
            ]
        ];
        
        return $this->respond($info);
    }
    
    /**
     * Generar nueva API key
     */
    public function generateKey()
    {
        // Solo permitir POST
        if ($this->request->getMethod() !== 'POST') {
            return $this->fail('Method not allowed', 405);
        }
        
        $userId = $this->request->getPost('user_id');
        $name = $this->request->getPost('name');
        $permissions = $this->request->getPost('permissions') ?? [];
        
        if (empty($userId) || empty($name)) {
            return $this->fail('user_id and name are required', 400);
        }
        
        $result = $this->apiManager->generateApiKey($userId, $name, $permissions);
        
        if (!$result['success']) {
            return $this->fail($result['error'], 500);
        }
        
        return $this->respondCreated($result);
    }
    
    /**
     * Revocar API key
     */
    public function revokeKey()
    {
        // Solo permitir DELETE
        if ($this->request->getMethod() !== 'DELETE') {
            return $this->fail('Method not allowed', 405);
        }
        
        $apiKey = $this->request->getPost('api_key');
        
        if (empty($apiKey)) {
            return $this->fail('api_key is required', 400);
        }
        
        $result = $this->apiManager->revokeApiKey($apiKey);
        
        if (!$result) {
            return $this->fail('Failed to revoke API key', 500);
        }
        
        return $this->respond([
            'success' => true,
            'message' => 'API key revoked successfully'
        ]);
    }
    
    /**
     * Estadísticas de uso de la API
     */
    public function usage()
    {
        $days = (int)($this->request->getGet('days') ?? 7);
        $stats = $this->apiManager->getApiStats($days);
        
        if (!$stats['success']) {
            return $this->fail($stats['error'], 500);
        }
        
        return $this->respond($stats);
    }
    
    /**
     * Obtener datos del request
     */
    private function getRequestData(): array
    {
        $method = $this->request->getMethod();
        
        switch (strtoupper($method)) {
            case 'GET':
                return $this->request->getGet() ?? [];
            case 'POST':
            case 'PUT':
            case 'PATCH':
                $json = $this->request->getJSON(true);
                $post = $this->request->getPost() ?? [];
                return array_merge($post, $json ?? []);
            case 'DELETE':
                return $this->request->getJSON(true) ?? [];
            default:
                return [];
        }
    }
    
    /**
     * Obtener headers del request
     */
    private function getRequestHeaders(): array
    {
        $headers = [];
        
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('HTTP_', '', $key);
                $header = str_replace('_', '-', $header);
                $header = ucwords(strtolower($header), '-');
                $headers[$header] = $value;
            }
        }
        
        // Headers específicos importantes
        if (isset($_SERVER['HTTP_X_API_KEY'])) {
            $headers['X-API-Key'] = $_SERVER['HTTP_X_API_KEY'];
        }
        
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
        }
        
        return $headers;
    }
    
    /**
     * Obtener uptime de la API
     */
    private function getApiUptime(): string
    {
        // Simular uptime
        return '99.9% (30 days)';
    }
    
    /**
     * Manejar errores de la API
     */
    protected function failValidationErrors($errors)
    {
        return $this->fail([
            'error' => 'Validation failed',
            'details' => $errors
        ], 400);
    }
    
    /**
     * Respuesta personalizada con metadata
     */
    protected function respondWithMetadata($data, $metadata = [])
    {
        $response = [
            'success' => true,
            'data' => $data,
            'metadata' => array_merge([
                'timestamp' => date('c'),
                'version' => $this->apiManager->getConfig()['version']
            ], $metadata)
        ];
        
        return $this->respond($response);
    }
}
