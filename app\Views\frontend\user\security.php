<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .dashboard-sidebar {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 0.5rem;
    }

    .sidebar-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--primary-light);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .dashboard-card {
        background: var(--white-color);
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .security-section {
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .security-section h5 {
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .security-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .security-item:last-child {
        border-bottom: none;
    }

    .security-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .status-badge.active {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-badge.inactive {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .password-strength {
        margin-top: 0.5rem;
    }

    .strength-bar {
        height: 4px;
        border-radius: 2px;
        background: var(--gray-200);
        overflow: hidden;
    }

    .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
    }

    .strength-weak { background: #dc3545; width: 25%; }
    .strength-fair { background: #ffc107; width: 50%; }
    .strength-good { background: #17a2b8; width: 75%; }
    .strength-strong { background: #28a745; width: 100%; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-lock me-2"></i>Seguridad</h1>
                <p class="mb-0 mt-2 opacity-75">Configuración de seguridad de tu cuenta</p>
            </div>
            <div class="col-md-6 text-md-end">
                <img src="/logo.jpg" alt="MrCell" style="max-height: 60px; filter: brightness(0) invert(1);">
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Inicio</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('cuenta') ?>">Mi Cuenta</a></li>
                    <li class="breadcrumb-item active">Seguridad</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mt-3 mb-1"><?= $user['first_name'] ?? 'Usuario' ?> <?= $user['last_name'] ?? '' ?></h5>
                    <small class="text-muted"><?= $user['email'] ?? '<EMAIL>' ?></small>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href="<?= base_url('cuenta') ?>"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><a href="<?= base_url('cuenta/pedidos') ?>"><i class="fas fa-shopping-bag me-2"></i>Mis Pedidos</a></li>
                    <li><a href="<?= base_url('cuenta/wishlist') ?>"><i class="fas fa-heart me-2"></i>Lista de Deseos</a></li>
                    <li><a href="<?= base_url('cuenta/direcciones') ?>"><i class="fas fa-map-marker-alt me-2"></i>Direcciones</a></li>
                    <li><a href="<?= base_url('cuenta/perfil') ?>"><i class="fas fa-user-edit me-2"></i>Mi Perfil</a></li>
                    <li><a href="<?= base_url('cuenta/seguridad') ?>" class="active"><i class="fas fa-lock me-2"></i>Seguridad</a></li>
                    <li><a href="<?= base_url('logout') ?>" class="text-danger"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Password Section -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fas fa-key me-2"></i>Contraseña</h5>
                    <div class="security-item">
                        <div>
                            <strong>Cambiar contraseña</strong>
                            <p class="text-muted mb-0">Actualiza tu contraseña regularmente para mantener tu cuenta segura</p>
                        </div>
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            Cambiar
                        </button>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Última actualización</strong>
                            <p class="text-muted mb-0">Hace 3 meses</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge inactive">Actualizar recomendado</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fas fa-mobile-alt me-2"></i>Autenticación de Dos Factores</h5>
                    <div class="security-item">
                        <div>
                            <strong>SMS</strong>
                            <p class="text-muted mb-0">Recibe códigos de verificación por SMS</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge inactive">Inactivo</span>
                            <button class="btn btn-outline-success btn-sm ms-2">Activar</button>
                        </div>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Email</strong>
                            <p class="text-muted mb-0">Recibe códigos de verificación por email</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge active">Activo</span>
                            <button class="btn btn-outline-danger btn-sm ms-2">Desactivar</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Activity -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fas fa-history me-2"></i>Actividad de Inicio de Sesión</h5>
                    <div class="security-item">
                        <div>
                            <strong>Último acceso</strong>
                            <p class="text-muted mb-0">Hoy a las 14:30 desde Guatemala, Guatemala</p>
                        </div>
                        <div class="security-status">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Dispositivos activos</strong>
                            <p class="text-muted mb-0">2 dispositivos con sesión activa</p>
                        </div>
                        <button class="btn btn-outline-primary btn-sm">Ver detalles</button>
                    </div>
                </div>
            </div>

            <!-- Privacy Settings -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fas fa-shield-alt me-2"></i>Privacidad</h5>
                    <div class="security-item">
                        <div>
                            <strong>Perfil público</strong>
                            <p class="text-muted mb-0">Controla qué información es visible para otros usuarios</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="publicProfile">
                        </div>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Notificaciones por email</strong>
                            <p class="text-muted mb-0">Recibe actualizaciones sobre tu cuenta por email</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- WhatsApp Notifications -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fab fa-whatsapp me-2"></i>Notificaciones WhatsApp</h5>
                    <div class="security-item">
                        <div>
                            <strong>Estado del teléfono</strong>
                            <p class="text-muted mb-0" id="phoneStatusText">Verificando estado...</p>
                        </div>
                        <div class="security-status" id="phoneStatusActions">
                            <span class="status-badge" id="phoneStatusBadge">Verificando...</span>
                            <button class="btn btn-outline-primary btn-sm ms-2" id="verifyPhoneBtn" style="display: none;">
                                <i class="fas fa-mobile-alt me-1"></i>Verificar
                            </button>
                        </div>
                    </div>
                    <div class="security-item" id="notificationToggleSection" style="display: none;">
                        <div>
                            <strong>Notificaciones por WhatsApp</strong>
                            <p class="text-muted mb-0">Recibe actualizaciones de pedidos y promociones por WhatsApp</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="whatsappNotifications">
                        </div>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Tipos de notificaciones</strong>
                            <p class="text-muted mb-0">Configura qué notificaciones deseas recibir</p>
                        </div>
                        <button class="btn btn-outline-info btn-sm" id="configureNotificationsBtn" disabled>
                            <i class="fas fa-cog me-1"></i>Configurar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="dashboard-card">
                <div class="security-section">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Acciones de Cuenta</h5>
                    <div class="security-item">
                        <div>
                            <strong>Descargar mis datos</strong>
                            <p class="text-muted mb-0">Obtén una copia de toda tu información personal</p>
                        </div>
                        <button class="btn btn-outline-info btn-sm">Descargar</button>
                    </div>
                    <div class="security-item">
                        <div>
                            <strong>Eliminar cuenta</strong>
                            <p class="text-muted mb-0">Elimina permanentemente tu cuenta y todos tus datos</p>
                        </div>
                        <button class="btn btn-outline-danger btn-sm">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cambiar Contraseña</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">Contraseña actual</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nueva contraseña</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <small class="text-muted" id="strengthText">Ingresa una contraseña</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Confirmar nueva contraseña</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">Cambiar Contraseña</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Password strength checker
document.getElementById('newPassword').addEventListener('input', function() {
    const password = this.value;
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    
    let strength = 0;
    let text = '';
    let className = '';
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    switch (strength) {
        case 0:
        case 1:
            text = 'Muy débil';
            className = 'strength-weak';
            break;
        case 2:
            text = 'Débil';
            className = 'strength-weak';
            break;
        case 3:
            text = 'Regular';
            className = 'strength-fair';
            break;
        case 4:
            text = 'Buena';
            className = 'strength-good';
            break;
        case 5:
            text = 'Muy fuerte';
            className = 'strength-strong';
            break;
    }
    
    strengthFill.className = 'strength-fill ' + className;
    strengthText.textContent = text;
});

function changePassword() {
    // TODO: Implementar cambio de contraseña
    alert('Funcionalidad de cambio de contraseña en desarrollo');
}

// WhatsApp Notifications Management
document.addEventListener('DOMContentLoaded', function() {
    checkPhoneVerificationStatus();

    // Event listeners
    document.getElementById('verifyPhoneBtn').addEventListener('click', showPhoneVerificationModal);
    document.getElementById('whatsappNotifications').addEventListener('change', toggleWhatsAppNotifications);
});

function checkPhoneVerificationStatus() {
    fetch('<?= base_url('phone-verification/status') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePhoneVerificationUI(data);
            } else {
                console.error('Error checking phone status:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function updatePhoneVerificationUI(data) {
    const phoneStatusText = document.getElementById('phoneStatusText');
    const phoneStatusBadge = document.getElementById('phoneStatusBadge');
    const verifyPhoneBtn = document.getElementById('verifyPhoneBtn');
    const notificationToggleSection = document.getElementById('notificationToggleSection');
    const whatsappNotifications = document.getElementById('whatsappNotifications');
    const configureNotificationsBtn = document.getElementById('configureNotificationsBtn');

    if (data.phone_verified) {
        phoneStatusText.textContent = `Teléfono verificado: ${data.phone}`;
        phoneStatusBadge.textContent = 'Verificado';
        phoneStatusBadge.className = 'status-badge active';
        verifyPhoneBtn.style.display = 'none';
        notificationToggleSection.style.display = 'flex';
        whatsappNotifications.checked = data.whatsapp_notifications_enabled;
        configureNotificationsBtn.disabled = false;
    } else {
        phoneStatusText.textContent = data.phone ? `Teléfono sin verificar: ${data.phone}` : 'No hay teléfono registrado';
        phoneStatusBadge.textContent = 'Sin verificar';
        phoneStatusBadge.className = 'status-badge inactive';
        verifyPhoneBtn.style.display = 'inline-block';
        notificationToggleSection.style.display = 'none';
        configureNotificationsBtn.disabled = true;
    }
}

function showPhoneVerificationModal() {
    fetch('<?= base_url('phone-verification/modal') ?>')
        .then(response => response.text())
        .then(html => {
            // Crear modal dinámicamente
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = html;
            document.body.appendChild(modalContainer);

            // Mostrar modal
            const modal = new bootstrap.Modal(modalContainer.querySelector('#phoneVerificationModal'));
            modal.show();

            // Limpiar cuando se cierre
            modalContainer.querySelector('#phoneVerificationModal').addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modalContainer);
                checkPhoneVerificationStatus(); // Actualizar estado
            });
        })
        .catch(error => {
            console.error('Error loading verification modal:', error);
            alert('Error cargando modal de verificación');
        });
}

function toggleWhatsAppNotifications() {
    const enabled = document.getElementById('whatsappNotifications').checked;

    fetch('<?= base_url('phone-verification/toggle-notifications') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'enabled=' + enabled
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Mostrar mensaje de éxito
            showAlert('success', data.message);
        } else {
            // Revertir el switch si hay error
            document.getElementById('whatsappNotifications').checked = !enabled;
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        // Revertir el switch si hay error
        document.getElementById('whatsappNotifications').checked = !enabled;
        showAlert('error', 'Error de conexión');
    });
}

function showAlert(type, message) {
    // Crear alerta temporal
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remover después de 5 segundos
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Función global para actualizar estado desde el modal
function updatePhoneVerificationStatus(verified) {
    if (verified) {
        checkPhoneVerificationStatus();
    }
}
</script>
<?= $this->endSection() ?>
