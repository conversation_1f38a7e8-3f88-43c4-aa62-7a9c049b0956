<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\ProductSyncService;

class SyncSingleProduct extends BaseCommand
{
    protected $group       = 'Recurrente';
    protected $name        = 'recurrente:sync-single';
    protected $description = 'Sincronizar un producto específico con Recurrente';
    protected $usage       = 'recurrente:sync-single [product_id]';
    protected $arguments   = [
        'product_id' => 'ID del producto a sincronizar'
    ];

    public function run(array $params)
    {
        $productId = $params[0] ?? null;
        
        if (!$productId) {
            CLI::error('Debe especificar un ID de producto');
            return;
        }

        CLI::write("=== Sincronizando Producto ID: {$productId} ===", 'yellow');
        CLI::newLine();

        $productSyncService = new ProductSyncService();

        try {
            // Verificar si Recurrente está habilitado
            $recurrenteService = new \App\Services\RecurrenteService();
            if (!$recurrenteService->isEnabled()) {
                CLI::error('Recurrente no está habilitado');
                return;
            }

            CLI::write('✓ Recurrente está habilitado', 'green');

            // Obtener el producto
            $productModel = new \App\Models\ProductModel();
            $product = $productModel->find($productId);

            if (!$product) {
                CLI::error("Producto {$productId} no encontrado");
                return;
            }

            CLI::write("Producto encontrado: {$product['name']}", 'cyan');
            CLI::write("SKU: {$product['sku']}", 'white');
            CLI::write("Precio: {$product['price_regular']}", 'white');
            CLI::write("Estado actual: " . ($product['recurrente_sync_status'] ?? 'pending'), 'white');
            CLI::newLine();

            // Sincronizar
            if (empty($product['recurrente_product_id'])) {
                CLI::write('Creando producto en Recurrente...', 'cyan');
                $result = $productSyncService->syncProductCreate($productId);
                $action = 'creado';
            } else {
                CLI::write('Actualizando producto en Recurrente...', 'cyan');
                $result = $productSyncService->syncProductUpdate($productId);
                $action = 'actualizado';
            }

            if ($result) {
                CLI::write("✓ Producto {$action} exitosamente en Recurrente", 'green');
            } else {
                CLI::write("✗ Error sincronizando producto", 'red');
            }

        } catch (\Exception $e) {
            CLI::error("Error: " . $e->getMessage());
        }
    }
}
