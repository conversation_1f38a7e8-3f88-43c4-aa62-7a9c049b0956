<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AutomationManager;
use App\Libraries\SystemMonitor;

/**
 * Controlador para Cron Jobs vía Web
 * Reemplaza los comandos CLI para hosting compartido
 */
class CronController extends BaseController
{
    private $automationManager;
    private $systemMonitor;
    
    public function __construct()
    {
        $this->automationManager = new AutomationManager();
        $this->systemMonitor = new SystemMonitor();
    }
    
    /**
     * Ejecutar todas las automatizaciones
     */
    public function runAll()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runScheduledTasks();
        }, 'Automatizaciones Completas');
    }
    
    /**
     * Ejecutar monitor de precios
     */
    public function runPrices()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runPriceMonitoring();
        }, 'Monitor de Precios');
    }
    
    /**
     * Ejecutar alertas de stock
     */
    public function runStock()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runStockAlerts();
        }, 'Alertas de Stock');
    }
    
    /**
     * Ejecutar recordatorios de wishlist
     */
    public function runWishlist()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runWishlistReminders();
        }, 'Recordatorios de Wishlist');
    }
    
    /**
     * Ejecutar limpieza de datos
     */
    public function runCleanup()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runDataCleanup();
        }, 'Limpieza de Datos');
    }
    
    /**
     * Ejecutar backup
     */
    public function runBackup()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runBackup();
        }, 'Backup Automático');
    }
    
    /**
     * Ejecutar análisis de rendimiento
     */
    public function runPerformance()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runPerformanceAnalysis();
        }, 'Análisis de Rendimiento');
    }
    
    /**
     * Ejecutar sincronización de analytics
     */
    public function runAnalytics()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runAnalyticsSync();
        }, 'Sincronización de Analytics');
    }
    
    /**
     * Ejecutar mantenimiento de cache
     */
    public function runCache()
    {
        return $this->executeTask(function() {
            return $this->automationManager->runCacheMaintenance();
        }, 'Mantenimiento de Cache');
    }
    
    /**
     * Ejecutar verificación del sistema
     */
    public function runSystemCheck()
    {
        return $this->executeTask(function() {
            return $this->systemMonitor->runSystemCheck();
        }, 'Verificación del Sistema');
    }
    
    /**
     * Ejecutar tarea con manejo de errores y logging
     */
    private function executeTask(callable $task, string $taskName): string
    {
        $startTime = microtime(true);
        $output = [];
        
        try {
            // Configurar tiempo de ejecución
            set_time_limit(300); // 5 minutos
            ini_set('memory_limit', '256M');
            
            $output[] = "🚀 Iniciando: {$taskName}";
            $output[] = "⏰ Hora: " . date('Y-m-d H:i:s');
            $output[] = "🖥️  Servidor: " . ($_SERVER['SERVER_NAME'] ?? 'localhost');
            $output[] = "";
            
            // Ejecutar tarea
            $result = $task();
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            if (isset($result['success']) && $result['success'] === false) {
                $output[] = "❌ Error: " . ($result['error'] ?? 'Error desconocido');
                $status = 'ERROR';
            } else {
                $output[] = "✅ {$taskName} completada exitosamente";
                $status = 'SUCCESS';
                
                // Mostrar detalles específicos
                if (isset($result['total_success'])) {
                    $output[] = "📊 Tareas exitosas: " . $result['total_success'];
                }
                
                if (isset($result['total_failed']) && $result['total_failed'] > 0) {
                    $output[] = "⚠️  Tareas fallidas: " . $result['total_failed'];
                }
                
                if (isset($result['products_processed'])) {
                    $output[] = "📦 Productos procesados: " . $result['products_processed'];
                }
                
                if (isset($result['notifications_sent'])) {
                    $output[] = "📱 Notificaciones enviadas: " . $result['notifications_sent'];
                }
                
                if (isset($result['cleaned']['total_records_cleaned'])) {
                    $output[] = "🗑️  Registros limpiados: " . $result['cleaned']['total_records_cleaned'];
                }
                
                if (isset($result['backup_file'])) {
                    $output[] = "💾 Archivo de backup: " . $result['backup_file'];
                }
                
                if (isset($result['critical_issues'])) {
                    $output[] = "🚨 Problemas críticos: " . $result['critical_issues'];
                    $output[] = "⚠️  Advertencias: " . $result['warnings'];
                }
            }
            
            $output[] = "";
            $output[] = "⏱️  Duración: {$duration}ms";
            $output[] = "🧠 Memoria usada: " . $this->formatBytes(memory_get_peak_usage(true));
            $output[] = "📅 Completado: " . date('Y-m-d H:i:s');
            
            // Log en base de datos si es posible
            $this->logExecution($taskName, $result, $duration, $status);
            
        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            $output[] = "❌ EXCEPCIÓN: " . $e->getMessage();
            $output[] = "📍 Archivo: " . $e->getFile() . ":" . $e->getLine();
            $output[] = "⏱️  Duración: {$duration}ms";
            $output[] = "📅 Error en: " . date('Y-m-d H:i:s');
            
            $this->logExecution($taskName, ['error' => $e->getMessage()], $duration, 'EXCEPTION');
        }
        
        // Retornar output como texto plano
        return implode("\n", $output);
    }
    
    /**
     * Log de ejecución en base de datos
     */
    private function logExecution(string $taskName, array $result, float $duration, string $status): void
    {
        try {
            $db = \Config\Database::connect();
            
            $db->table('automation_log')->insert([
                'task_name' => strtolower(str_replace(' ', '_', $taskName)),
                'result' => json_encode($result),
                'success' => $status === 'SUCCESS' ? 1 : 0,
                'duration' => $duration / 1000, // Convertir a segundos
                'memory_usage' => memory_get_peak_usage(true),
                'error_message' => $status !== 'SUCCESS' ? ($result['error'] ?? 'Error desconocido') : null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            // Ignorar errores de logging para no interrumpir la tarea principal
            error_log("Error logging automation: " . $e->getMessage());
        }
    }
    
    /**
     * Formatear bytes a formato legible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Endpoint de estado para verificar que el sistema funciona
     */
    public function status()
    {
        $status = [
            'system' => 'MrCell Automation System',
            'version' => '1.0',
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => $_SERVER['SERVER_NAME'] ?? 'localhost',
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'timezone' => date_default_timezone_get()
        ];
        
        // Verificar conexión a base de datos
        try {
            $db = \Config\Database::connect();
            $db->query('SELECT 1');
            $status['database'] = 'Connected';
        } catch (\Exception $e) {
            $status['database'] = 'Error: ' . $e->getMessage();
        }
        
        // Verificar última ejecución
        try {
            $db = \Config\Database::connect();
            $lastExecution = $db->table('automation_log')
                               ->orderBy('created_at', 'DESC')
                               ->limit(1)
                               ->get()
                               ->getRowArray();
            
            if ($lastExecution) {
                $status['last_execution'] = [
                    'task' => $lastExecution['task_name'],
                    'success' => (bool) $lastExecution['success'],
                    'created_at' => $lastExecution['created_at']
                ];
            } else {
                $status['last_execution'] = 'None';
            }
        } catch (\Exception $e) {
            $status['last_execution'] = 'Error: ' . $e->getMessage();
        }
        
        // Retornar como JSON si se solicita, sino como texto
        if ($this->request->getHeaderLine('Accept') === 'application/json') {
            return $this->response->setJSON($status);
        }
        
        $output = [];
        $output[] = "🤖 MrCell Automation System Status";
        $output[] = "===================================";
        $output[] = "";
        
        foreach ($status as $key => $value) {
            if (is_array($value)) {
                $output[] = ucfirst($key) . ":";
                foreach ($value as $subKey => $subValue) {
                    $output[] = "  " . ucfirst($subKey) . ": " . $subValue;
                }
            } else {
                $output[] = ucfirst($key) . ": " . $value;
            }
        }
        
        return implode("\n", $output);
    }
    
    /**
     * Página de ayuda con todas las URLs disponibles
     */
    public function help()
    {
        $baseUrl = base_url();
        
        $endpoints = [
            'status' => 'Estado del sistema',
            'run-all' => 'Ejecutar todas las automatizaciones',
            'run-prices' => 'Monitor de precios',
            'run-stock' => 'Alertas de stock',
            'run-wishlist' => 'Recordatorios de wishlist',
            'run-cleanup' => 'Limpieza de datos',
            'run-backup' => 'Backup automático',
            'run-performance' => 'Análisis de rendimiento',
            'run-analytics' => 'Sincronización de analytics',
            'run-cache' => 'Mantenimiento de cache',
            'run-system-check' => 'Verificación del sistema'
        ];
        
        $output = [];
        $output[] = "🤖 MrCell Automation System - Endpoints Disponibles";
        $output[] = "===================================================";
        $output[] = "";
        $output[] = "📋 URLs para Cron Jobs:";
        $output[] = "";
        
        foreach ($endpoints as $endpoint => $description) {
            $url = $baseUrl . "cron/" . $endpoint;
            $output[] = "• {$description}:";
            $output[] = "  {$url}";
            $output[] = "";
        }
        
        $output[] = "⚙️  Comando para cPanel:";
        $output[] = '  wget -q -O - "URL_DE_LA_TAREA"';
        $output[] = "";
        $output[] = "📅 Horarios Recomendados:";
        $output[] = "  */5 * * * *   - run-system-check (cada 5 minutos)";
        $output[] = "  */15 * * * *  - run-stock (cada 15 minutos)";
        $output[] = "  */30 * * * *  - run-prices (cada 30 minutos)";
        $output[] = "  0 * * * *     - run-all (cada hora)";
        $output[] = "  0 2 * * *     - run-backup (diario 2 AM)";
        $output[] = "  0 3 * * *     - run-cleanup (diario 3 AM)";
        $output[] = "  0 10 * * 0    - run-wishlist (domingos 10 AM)";
        
        return implode("\n", $output);
    }
}
