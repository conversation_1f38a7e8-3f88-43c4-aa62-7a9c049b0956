<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateReviewsTable extends Migration
{
    public function up()
    {
        // Tabla de reseñas de productos
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'uuid' => [
                'type' => 'VARCHAR',
                'constraint' => 36,
                'unique' => true,
            ],
            'product_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'customer_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'customer_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'rating' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'comment' => 'Rating from 1 to 5',
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'comment' => [
                'type' => 'TEXT',
            ],
            'is_verified_purchase' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'True if user bought the product',
            ],
            'is_approved' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Admin approval status',
            ],
            'is_featured' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Featured review',
            ],
            'helpful_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
            ],
            'admin_response' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Admin response to review',
            ],
            'admin_response_date' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('uuid');
        $this->forge->addKey('product_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('rating');
        $this->forge->addKey('is_approved');
        $this->forge->addKey('created_at');
        
        // Foreign keys
        $this->forge->addForeignKey('product_id', 'products', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('product_reviews');

        // Tabla para votos útiles de reseñas
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'review_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
            ],
            'is_helpful' => [
                'type' => 'BOOLEAN',
                'comment' => 'True for helpful, false for not helpful',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['review_id', 'user_id']);
        $this->forge->addKey(['review_id', 'ip_address']);
        
        // Foreign keys
        $this->forge->addForeignKey('review_id', 'product_reviews', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('review_helpful_votes');
    }

    public function down()
    {
        $this->forge->dropTable('review_helpful_votes');
        $this->forge->dropTable('product_reviews');
    }
}
