<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateStoredProcedures extends Migration
{
    public function up()
    {
        // Actualizar stored procedure sp_get_public_product_details para incluir currency
        $sql = "
        DROP PROCEDURE IF EXISTS sp_get_public_product_details;
        
        CREATE PROCEDURE sp_get_public_product_details(
            IN p_product_id INT,
            IN p_product_slug VARCHAR(255)
        )
        BEGIN
            DECLARE v_product_id INT DEFAULT p_product_id;

            -- Si se proporciona slug, obtener ID
            IF p_product_slug IS NOT NULL AND p_product_slug != '' THEN
                SELECT id INTO v_product_id
                FROM products
                WHERE slug = p_product_slug
                  AND deleted_at IS NULL
                  AND is_active = 1
                LIMIT 1;
            END IF;

            -- Obtener detalles del producto
            SELECT
                p.id,
                p.uuid,
                p.name,
                p.slug,
                p.description,
                p.short_description,
                p.sku,
                p.price_regular,
                p.price_sale,
                p.currency,
                CASE
                    WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN p.price_sale
                    ELSE p.price_regular
                END as final_price,
                CASE
                    WHEN p.price_sale IS NOT NULL AND p.price_sale > 0 THEN
                        ROUND(((p.price_regular - p.price_sale) / p.price_regular) * 100, 0)
                    ELSE 0
                END as discount_percentage,
                p.stock_quantity,
                p.weight,
                p.dimensions,
                p.dimension_length,
                p.dimension_width,
                p.dimension_height,
                p.dimension_unit,
                p.attributes,
                p.is_featured,
                0 as views_count,
                0.0 as rating_average,
                0 as rating_count,
                p.meta_title,
                p.meta_description,
                p.created_at,
                p.updated_at,
                c.id as category_id,
                c.name as category_name,
                c.slug as category_slug,
                c.description as category_description,
                b.id as brand_id,
                b.name as brand_name,
                b.slug as brand_slug,
                -- Estado de stock
                CASE
                    WHEN p.stock_quantity = 0 THEN 'out_of_stock'
                    WHEN p.stock_quantity <= 5 THEN 'low_stock'
                    ELSE 'in_stock'
                END as stock_status,
                -- Breadcrumb de categoría (simplificado)
                c.name as category_breadcrumb,
                -- Imagen principal directa del campo featured_image
                p.featured_image
            FROM products p
            LEFT JOIN categories c ON c.id = p.category_id
            LEFT JOIN brands b ON b.id = p.brand_id
            WHERE p.id = v_product_id
              AND p.deleted_at IS NULL
              AND p.is_active = 1
            LIMIT 1;
        END;
        ";
        
        $this->db->query($sql);
    }

    public function down()
    {
        // Revertir cambios si es necesario
        $sql = "DROP PROCEDURE IF EXISTS sp_get_public_product_details";
        $this->db->query($sql);
    }
}
