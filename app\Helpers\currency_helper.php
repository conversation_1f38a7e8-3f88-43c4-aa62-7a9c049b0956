<?php

if (!function_exists('format_currency')) {
    /**
     * Formatear precio con símbolo de moneda
     */
    function format_currency($amount, $currency = 'GTQ', $showSymbol = true)
    {
        // Configuración temporal hasta resolver el conflicto
        $currencyInfo = [
            'GTQ' => ['symbol' => 'Q', 'decimal_places' => 2, 'position' => 'before'],
            'USD' => ['symbol' => '$', 'decimal_places' => 2, 'position' => 'before'],
            'EUR' => ['symbol' => '€', 'decimal_places' => 2, 'position' => 'before']
        ];
        $currencyInfo = $currencyInfo[$currency] ?? $currencyInfo['GTQ'];

        $decimalPlaces = $currencyInfo['decimal_places'] ?? 2;
        $formattedAmount = number_format($amount, $decimalPlaces, '.', ',');

        if (!$showSymbol) {
            return $formattedAmount;
        }

        $symbol = $currencyInfo['symbol'] ?? 'Q';
        $position = $currencyInfo['position'] ?? 'before';

        if ($position === 'after') {
            return $formattedAmount . ' ' . $symbol;
        } else {
            return $symbol . ' ' . $formattedAmount;
        }
    }
}

if (!function_exists('convert_currency')) {
    /**
     * Convertir entre monedas
     */
    function convert_currency($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        // Usar tasas de cambio con caché
        $rate = get_cached_exchange_rate($fromCurrency, $toCurrency);
        return round($amount * $rate, 2);
    }
}

if (!function_exists('get_currency_config')) {
    /**
     * Obtener configuración completa de monedas
     */
    function get_currency_config()
    {
        return [
            'default_currency' => 'GTQ',
            'show_conversion' => true,
            'supported_currencies' => [
                'GTQ' => ['name' => 'Quetzal', 'symbol' => 'Q'],
                'USD' => ['name' => 'Dólar', 'symbol' => '$'],
                'EUR' => ['name' => 'Euro', 'symbol' => '€']
            ]
        ];
    }
}

if (!function_exists('format_price_with_conversion')) {
    /**
     * Formatear precio con conversión automática
     */
    function format_price_with_conversion($amount, $originalCurrency, $showConversion = null)
    {
        $config = get_currency_config();
        
        if ($showConversion === null) {
            $showConversion = $config['show_conversion'];
        }

        $originalFormatted = format_currency($amount, $originalCurrency);

        if (!$showConversion || $originalCurrency === $config['default_currency']) {
            return $originalFormatted;
        }

        // Mostrar conversión
        $targetCurrency = $originalCurrency === 'GTQ' ? 'USD' : 'GTQ';
        $convertedAmount = convert_currency($amount, $originalCurrency, $targetCurrency);
        $convertedFormatted = format_currency($convertedAmount, $targetCurrency);

        return $originalFormatted . ' <span class="text-muted">(' . $convertedFormatted . ')</span>';
    }
}

if (!function_exists('get_product_prices')) {
    /**
     * Obtener precios de producto con conversiones
     */
    function get_product_prices($product)
    {
        $config = get_currency_config();
        $currency = $product['currency'] ?? 'GTQ';
        
        $prices = [
            'original_currency' => $currency,
            'price_regular' => $product['price_regular'],
            'price_sale' => $product['price_sale'] ?? null,
            'final_price' => !empty($product['price_sale']) ? $product['price_sale'] : $product['price_regular']
        ];

        // Agregar conversiones si está habilitado
        if ($config['show_conversion']) {
            $targetCurrency = $currency === 'GTQ' ? 'USD' : 'GTQ';
            
            $prices['price_regular_' . strtolower($targetCurrency)] = convert_currency(
                $product['price_regular'], 
                $currency, 
                $targetCurrency
            );
            
            if (!empty($product['price_sale'])) {
                $prices['price_sale_' . strtolower($targetCurrency)] = convert_currency(
                    $product['price_sale'], 
                    $currency, 
                    $targetCurrency
                );
            }
            
            $prices['final_price_' . strtolower($targetCurrency)] = convert_currency(
                $prices['final_price'], 
                $currency, 
                $targetCurrency
            );
        }

        return $prices;
    }
}

if (!function_exists('display_product_price')) {
    /**
     * Mostrar precio de producto formateado con conversión
     */
    function display_product_price($product, $priceType = 'final', $showConversion = true)
    {
        $prices = get_product_prices($product);
        $currency = $prices['original_currency'];
        
        $priceField = $priceType === 'regular' ? 'price_regular' : 
                     ($priceType === 'sale' ? 'price_sale' : 'final_price');
        
        $amount = $prices[$priceField];
        
        if (empty($amount)) {
            return '';
        }

        return format_price_with_conversion($amount, $currency, $showConversion);
    }
}

if (!function_exists('calculate_discount_percentage')) {
    /**
     * Calcular porcentaje de descuento
     */
    function calculate_discount_percentage($regularPrice, $salePrice)
    {
        if (empty($salePrice) || $salePrice >= $regularPrice) {
            return 0;
        }

        return round((($regularPrice - $salePrice) / $regularPrice) * 100);
    }
}

if (!function_exists('get_currency_symbol')) {
    /**
     * Obtener símbolo de moneda
     */
    function get_currency_symbol($currency)
    {
        $symbols = [
            'GTQ' => 'Q',
            'USD' => '$',
            'EUR' => '€'
        ];
        return $symbols[$currency] ?? 'Q';
    }
}

if (!function_exists('get_exchange_rate')) {
    /**
     * Obtener tipo de cambio actual
     */
    function get_exchange_rate()
    {
        return get_admin_exchange_rate();
    }
}

if (!function_exists('get_cached_exchange_rate')) {
    /**
     * Obtener tasa de cambio desde la configuración del admin
     */
    function get_cached_exchange_rate($fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        // Obtener la tasa USD → GTQ desde la configuración del admin
        $exchangeRate = get_admin_exchange_rate();

        // USD → GTQ: multiplicar
        if ($fromCurrency === 'USD' && $toCurrency === 'GTQ') {
            return $exchangeRate;
        }

        // GTQ → USD: dividir
        if ($fromCurrency === 'GTQ' && $toCurrency === 'USD') {
            return 1.0 / $exchangeRate;
        }

        // Para otras monedas, usar tasa por defecto
        return 1.0;
    }
}

if (!function_exists('get_admin_exchange_rate')) {
    /**
     * Obtener tasa de cambio desde system_settings
     */
    function get_admin_exchange_rate()
    {
        try {
            $db = \Config\Database::connect();

            $query = $db->table('system_settings')
                        ->where('setting_key', 'exchange_rate_usd_to_gtq')
                        ->get();

            $setting = $query->getRowArray();
            return $setting ? (float) $setting['setting_value'] : 7.75;

        } catch (\Exception $e) {
            return 7.75; // Fallback
        }
    }
}



if (!function_exists('clear_currency_cache')) {
    /**
     * Limpiar caché de tasas de cambio (versión simplificada)
     */
    function clear_currency_cache($fromCurrency = null, $toCurrency = null)
    {
        // Función simplificada - no hace nada por ahora
        return true;
    }
}
