<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Panel Administrativo - MrCell' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Admin Modals CSS -->
    <link href="/assets/css/admin-modals.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --secondary-color: #6b7280;      /* Gris medio */
            --success-color: #059669;        /* Verde para éxito */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris claro medio */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Casi negro */
            --gray-900: #111827;             /* Negro */
            --sidebar-bg: linear-gradient(135deg, var(--gray-900), var(--gray-800));
            --sidebar-width: 250px;
        }
        
        /* Font Awesome 6 Fix */
        .fas {
            font-weight: 900 !important;
        }

        .far {
            font-weight: 400 !important;
        }

        .fab {
            font-weight: 400 !important;
        }

        /* Fallback para iconos que no cargan */
        .fas.fa-eye::before { content: "👁" !important; }
        .fas.fa-edit::before { content: "✏" !important; }
        .fas.fa-copy::before { content: "📋" !important; }
        .fas.fa-trash::before { content: "🗑" !important; }
        .fas.fa-plus::before { content: "+" !important; }
        .fas.fa-search::before { content: "🔍" !important; }
        .fas.fa-times::before { content: "✕" !important; }
        .fas.fa-save::before { content: "💾" !important; }

        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--gray-800);
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Scrollbar personalizado para el sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .sidebar-header {
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header img {
            height: 40px;
            width: auto;
            margin-bottom: 10px;
            border-radius: 8px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            color: var(--white-color);
        }

        .sidebar-header small {
            opacity: 0.8;
            color: var(--gray-300);
        }
        
        .user-info {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li a {
            display: block;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-left-color: #fff;
            transform: translateX(5px);
        }
        
        .sidebar-menu li a i {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .top-navbar {
            background: var(--white-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 30px;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--gray-200);
        }
        
        .content-wrapper {
            padding: 0 30px 30px;
        }
        
        .page-header {
            margin-bottom: 30px;
        }
        
        .page-header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .page-header .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }
        
        .card {
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            background-color: var(--white-color);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.1);
            border-color: var(--primary-light);
        }

        .card-header {
            background: var(--white-color);
            border-bottom: 1px solid var(--gray-200);
            font-weight: 600;
            color: var(--gray-800);
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            color: var(--white-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white-color);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: #5a32a3;
            border-color: #5a32a3;
        }
        
        /* Overlay para móviles */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 220px;
            }

            .sidebar-menu li a {
                padding: 12px 15px;
                font-size: 14px;
            }

            .content-wrapper {
                padding: 0 20px 20px;
            }
        }

        @media (max-width: 992px) {
            :root {
                --sidebar-width: 200px;
            }

            .sidebar-header h4 {
                font-size: 1.1rem;
            }

            .sidebar-menu li a {
                padding: 10px 12px;
                font-size: 13px;
            }

            .sidebar-menu li a i {
                width: 18px;
                margin-right: 8px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 280px;
                max-width: 85vw;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-toggle {
                display: block !important;
            }

            .top-navbar {
                padding: 10px 15px;
                margin-bottom: 20px;
            }

            .content-wrapper {
                padding: 0 15px 15px;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            /* Mejorar el menú en móviles */
            .sidebar-menu li a {
                padding: 15px 20px;
                font-size: 14px;
            }

            .sidebar-menu li a i {
                width: 20px;
                margin-right: 12px;
            }
        }

        @media (max-width: 576px) {
            .sidebar {
                width: 100vw;
                max-width: none;
            }

            .top-navbar {
                padding: 8px 10px;
            }

            .top-navbar h2 {
                font-size: 1.2rem;
            }

            .content-wrapper {
                padding: 0 10px 10px;
            }

            .sidebar-header {
                padding: 15px;
            }

            .sidebar-header h4 {
                font-size: 1rem;
            }

            .user-info {
                padding: 10px 15px;
            }
        }

        .mobile-toggle {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="/logo.jpg" alt="MrCell Logo">
            <h4>MrCell Admin</h4>
            <small>Panel de Administración</small>
        </div>
        
        <?php if (session()->get('admin_logged_in')): ?>
        <div class="user-info">
            <div><strong>Bienvenido, Admin</strong></div>
            <small>Administrador</small>
        </div>
        <?php endif; ?>
        
        <ul class="sidebar-menu">
            <li><a href="/admin" class="<?= (current_url() == base_url('admin') || current_url() == base_url('admin/dashboard')) ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i>Dashboard
            </a></li>
            <li><a href="/admin/products" class="<?= strpos(current_url(), 'admin/products') !== false ? 'active' : '' ?>">
                <i class="fas fa-box"></i>Productos
            </a></li>
            <li><a href="/admin/orders" class="<?= strpos(current_url(), 'admin/orders') !== false ? 'active' : '' ?>">
                <i class="fas fa-shopping-cart"></i>Pedidos
            </a></li>
            <li><a href="/admin/users" class="<?= strpos(current_url(), 'admin/users') !== false ? 'active' : '' ?>">
                <i class="fas fa-users"></i>Usuarios
            </a></li>
            <li><a href="/admin/categories" class="<?= strpos(current_url(), 'admin/categories') !== false ? 'active' : '' ?>">
                <i class="fas fa-tags"></i>Categorías
            </a></li>
            <li><a href="/admin/brands" class="<?= strpos(current_url(), 'admin/brands') !== false ? 'active' : '' ?>">
                <i class="fas fa-award"></i>Marcas
            </a></li>
            <li><a href="/admin/inventory" class="<?= strpos(current_url(), 'admin/inventory') !== false ? 'active' : '' ?>">
                <i class="fas fa-warehouse"></i>Inventario
            </a></li>
            <li><a href="/admin/shipping" class="<?= strpos(current_url(), 'admin/shipping') !== false ? 'active' : '' ?>">
                <i class="fas fa-truck"></i>Envíos
            </a></li>
            <li><a href="/admin/reports" class="<?= strpos(current_url(), 'admin/reports') !== false ? 'active' : '' ?>">
                <i class="fas fa-chart-bar"></i>Reportes
            </a></li>
            <li><a href="/admin/notifications" class="<?= strpos(current_url(), 'admin/notifications') !== false ? 'active' : '' ?>">
                <i class="fas fa-bell"></i>Notificaciones
            </a></li>
            <li><a href="/admin/reviews" class="<?= strpos(current_url(), 'admin/reviews') !== false ? 'active' : '' ?>">
                <i class="fas fa-star"></i>Reseñas
            </a></li>
            <li><a href="/admin/contact" class="<?= strpos(current_url(), 'admin/contact') !== false ? 'active' : '' ?>">
                <i class="fas fa-envelope"></i>Mensajes de Contacto
            </a></li>
            <li><a href="/admin/whatsapp" class="<?= strpos(current_url(), 'admin/whatsapp') !== false ? 'active' : '' ?>">
                <i class="fab fa-whatsapp"></i>WhatsApp
            </a></li>
            <li><a href="/admin/payment-methods" class="<?= strpos(current_url(), 'admin/payment-methods') !== false ? 'active' : '' ?>">
                <i class="fas fa-credit-card"></i>Métodos de Pago
            </a></li>
            <li><a href="/admin/settings" class="<?= strpos(current_url(), 'admin/settings') !== false ? 'active' : '' ?>">
                <i class="fas fa-cog"></i>Configuración
            </a></li>
            <li><hr style="border-color: rgba(255,255,255,0.2); margin: 10px 20px;"></li>
            <li><a href="/admin/logout">
                <i class="fas fa-sign-out-alt"></i>Cerrar Sesión
            </a></li>
        </ul>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-secondary mobile-toggle me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-0"><?= $page_title ?? 'Panel Administrativo' ?></h2>
                    <small class="text-muted">Gestión del sistema MrCell</small>
                </div>
            </div>
            
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i>Admin
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                        <i class="fas fa-user me-2"></i>Mi Perfil
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?= base_url('admin/logout') ?>">
                        <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                    </a></li>
                </ul>
            </div>
        </div>
        
        <div class="content-wrapper">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Content will be inserted here -->
            <?= $this->renderSection('content') ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');

            // Prevenir scroll del body cuando el sidebar está abierto en móviles
            if (window.innerWidth <= 768) {
                document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
            }
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        // Cerrar sidebar al hacer clic en un enlace en móviles
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarLinks = document.querySelectorAll('.sidebar-menu a');

            sidebarLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        closeSidebar();
                    }
                });
            });

            // Cerrar sidebar al redimensionar ventana
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeSidebar();
                }
            });

            // Cerrar sidebar con tecla Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeSidebar();
                }
            });
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                }
            });
        }, 5000);

        // =====================================================
        // SISTEMA DE MANTENIMIENTO DE SESIÓN ACTIVA
        // =====================================================

        let sessionKeepAliveInterval;
        let userActivityTimeout;
        let lastActivityTime = Date.now();

        // Función para mantener la sesión activa
        function keepSessionAlive() {
            fetch('<?= base_url('admin/keep-alive') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('✅ Sesión mantenida activa:', new Date().toLocaleTimeString());
                    lastActivityTime = Date.now();
                } else {
                    console.warn('⚠️ Error manteniendo sesión:', data.message);
                    // Si hay error de autenticación, redirigir al login
                    if (response.status === 401) {
                        window.location.href = '<?= base_url('admin/login') ?>';
                    }
                }
            })
            .catch(error => {
                console.error('❌ Error en keep-alive:', error);
            });
        }

        // Detectar actividad del usuario
        function resetActivityTimer() {
            lastActivityTime = Date.now();

            // Limpiar timeout anterior
            if (userActivityTimeout) {
                clearTimeout(userActivityTimeout);
            }

            // Programar próximo keep-alive si hay actividad reciente
            userActivityTimeout = setTimeout(() => {
                const timeSinceActivity = Date.now() - lastActivityTime;
                // Si ha habido actividad en los últimos 2 minutos, mantener sesión
                if (timeSinceActivity < 120000) { // 2 minutos
                    keepSessionAlive();
                }
            }, 90000); // Verificar cada 1.5 minutos
        }

        // Eventos que indican actividad del usuario
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

        activityEvents.forEach(event => {
            document.addEventListener(event, resetActivityTimer, true);
        });

        // Inicializar sistema de keep-alive
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 Sistema de mantenimiento de sesión iniciado');

            // Primer keep-alive inmediato
            resetActivityTimer();

            // Keep-alive periódico cada 5 minutos como respaldo
            sessionKeepAliveInterval = setInterval(() => {
                const timeSinceActivity = Date.now() - lastActivityTime;
                // Solo si ha habido actividad en los últimos 10 minutos
                if (timeSinceActivity < 600000) { // 10 minutos
                    keepSessionAlive();
                }
            }, 300000); // 5 minutos
        });

        // Limpiar intervalos al salir de la página
        window.addEventListener('beforeunload', function() {
            if (sessionKeepAliveInterval) {
                clearInterval(sessionKeepAliveInterval);
            }
            if (userActivityTimeout) {
                clearTimeout(userActivityTimeout);
            }
        });

        // Detectar cuando la pestaña se vuelve visible para reactivar sesión
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('👁️ Pestaña visible, verificando sesión...');
                resetActivityTimer();
            }
        });
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
