<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use Config\Frontend as FrontendConfig;

/**
 * Controlador Frontend
 * 
 * Maneja la visualización del frontend de la tienda
 * con sistema de plantillas múltiples
 */
class Frontend extends Controller
{
    protected $frontendConfig;
    protected $data = [];

    public function __construct()
    {
        $this->frontendConfig = new FrontendConfig();
        $this->initializeData();
    }

    /**
     * Inicializar datos comunes para todas las vistas
     */
    private function initializeData()
    {
        $this->data = [
            'site_name' => $this->frontendConfig->siteName,
            'site_description' => $this->frontendConfig->siteDescription,
            'site_keywords' => $this->frontendConfig->siteKeywords,
            'contact_email' => $this->frontendConfig->contactEmail,
            'contact_phone' => $this->frontendConfig->contactPhone,
            'social_media' => $this->frontendConfig->socialMedia,
            'features' => $this->frontendConfig->features,
            'currency' => $this->frontendConfig->currency,
            'base_url' => base_url(),
            'current_url' => current_url(),
            'cart_count' => $this->getCartCount(),
            'categories' => $this->getCategories(),
            'featured_products' => $this->getFeaturedProducts(4)
        ];
    }

    /**
     * Página principal de la tienda
     */
    public function index()
    {
        // Cargar productos destacados
        $this->data['featured_products'] = $this->getFeaturedProducts();
        $this->data['categories'] = $this->getCategories();
        $this->data['banners'] = $this->getBanners();
        $this->data['page_title'] = 'Inicio';

        return view('frontend/pages/home_index3', $this->data);
    }

    /**
     * Página de la tienda/catálogo moderna con APIs
     */
    public function shop($filter = null)
    {
        try {
            log_message('debug', 'Frontend::shop() - Iniciando método shop');

            $db = \Config\Database::connect();

            // Obtener filtros de la URL
            $categoryFilter = $this->request->getGet('category');
            $searchFilter = $this->request->getGet('search');
            $priceMin = $this->request->getGet('price_min');
            $priceMax = $this->request->getGet('price_max');
            $sortBy = $this->request->getGet('sort_by') ?? 'newest';

            // Parámetros de paginación - resetear a página 1 si hay filtros nuevos
            $page = (int) ($this->request->getGet('page') ?? 1);

            // Si hay filtros aplicados y no se especifica página, ir a página 1
            if (($categoryFilter || $searchFilter || $priceMin || $priceMax) && !$this->request->getGet('page')) {
                $page = 1;
            }

            $limit = 20; // 20 productos por página
            $offset = ($page - 1) * $limit;

            log_message('debug', 'Frontend::shop() - Filtros: category=' . $categoryFilter . ', search=' . $searchFilter . ', page=' . $page);

            // Obtener total de productos para paginación (con filtros aplicados)
            $totalProducts = $this->getTotalProductsWithFilters($categoryFilter, $searchFilter, $priceMin, $priceMax);
            $totalPages = ceil($totalProducts / $limit);

            // Usar stored procedure para obtener productos con paginación
            $products = $this->getProductsWithPagination($categoryFilter, $searchFilter, $priceMin, $priceMax, $sortBy, $limit, $offset);

            log_message('debug', 'Frontend::shop() - Productos obtenidos: ' . count($products) . ' de ' . $totalProducts . ' total');

        // Agregar datos requeridos por la vista
        $this->data['products'] = $products;
        $this->data['categories'] = $this->getCategories();
        $this->data['selected_category'] = $categoryFilter;
        $this->data['search_term'] = $searchFilter;
        $this->data['price_min'] = $priceMin;
        $this->data['price_max'] = $priceMax;
        $this->data['sort_by'] = $sortBy;

        // Datos de paginación reales
        $this->data['pagination'] = [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_products' => $totalProducts,
            'per_page' => $limit,
            'has_previous' => $page > 1,
            'has_next' => $page < $totalPages
        ];

        // Metadatos requeridos
        $this->data['title'] = 'Tienda - MrCell Guatemala';
        $this->data['meta_description'] = 'Encuentra los mejores celulares y accesorios en MrCell. Precios competitivos y la mejor calidad en Guatemala.';
        $this->data['meta_keywords'] = 'celulares, smartphones, accesorios, tecnología, Guatemala, MrCell';

        // Título dinámico basado en filtros
        $pageTitle = 'Tienda';
        if ($categoryFilter) {
            $categoryName = $this->getCategoryName($categoryFilter);
            if ($categoryName) {
                $pageTitle = 'Tienda - ' . $categoryName;
            }
        }
        if ($searchFilter) {
            $pageTitle = 'Búsqueda: ' . $searchFilter;
        }

        $this->data['page_title'] = $pageTitle;

        return view('frontend/shop', $this->data);

        } catch (\Exception $e) {
            log_message('error', 'Error en Frontend::shop: ' . $e->getMessage());

            // En caso de error, mostrar página con productos vacíos
            $this->data['products'] = [];
            $this->data['pagination'] = [
                'current_page' => 1,
                'total_pages' => 1,
                'total_products' => 0,
                'per_page' => 50,
                'has_previous' => false,
                'has_next' => false
            ];
            $this->data['title'] = 'Tienda - MrCell Guatemala';
            $this->data['meta_description'] = 'Encuentra los mejores celulares y accesorios en MrCell.';
            $this->data['meta_keywords'] = 'celulares, smartphones, accesorios, tecnología, Guatemala, MrCell';
            $this->data['page_title'] = 'Tienda';
            $this->data['error_message'] = 'Error al cargar productos: ' . $e->getMessage();

            return view('frontend/shop', $this->data);
        }
    }

    /**
     * Carrito de compras
     */
    public function cart()
    {
        $cartItems = $this->getCartItems();
        log_message('debug', 'Cart items for view: ' . json_encode($cartItems));

        $this->data['cart_items'] = $cartItems;
        $this->data['page_title'] = 'Carrito de Compras';

        return view('frontend/cart_modern', $this->data);
    }

    /**
     * Checkout
     */
    public function checkout()
    {
        // Obtener session ID del frontend si está disponible
        $request = $this->request ?? \Config\Services::request();
        $sessionId = $request->getHeaderLine('X-Session-ID') ?? session_id();
        
        log_message('debug', 'Checkout session ID: ' . $sessionId);

        $cartItems = $this->getCartItems();
        
        if (empty($cartItems)) {
            return redirect()->to('/carrito')->with('error', 'Tu carrito está vacío');
        }

        $this->data['cart_items'] = $cartItems;
        $this->data['page_title'] = 'Finalizar Compra';

        // Get tax settings
        $this->data['tax_settings'] = $this->getTaxSettings();

        return view('frontend/checkout_new', $this->data);
    }

    private function getTaxSettings()
    {
        $db = \Config\Database::connect();

        try {
            $settings = $db->query("
                SELECT setting_key, setting_value, setting_type
                FROM system_settings
                WHERE setting_group = 'taxes' AND is_active = 1
            ")->getResultArray();

            $taxSettings = [];
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];

                // Cast values based on type
                if ($setting['setting_type'] === 'checkbox') {
                    $value = (bool) $value;
                } elseif ($setting['setting_type'] === 'number') {
                    $value = (float) $value;
                }

                $taxSettings[$setting['setting_key']] = $value;
            }

            return $taxSettings;

        } catch (\Exception $e) {
            log_message('error', 'Error getting tax settings: ' . $e->getMessage());
            return [
                'tax_enabled' => false,
                'tax_rate' => 0,
                'tax_name' => 'IVA',
                'tax_included_in_price' => false
            ];
        }
    }

    /**
     * Página de blog
     */
    public function blog()
    {
        $this->data['page_title'] = 'Blog';
        return view('frontend/blog', $this->data);
    }

    /**
     * Página de cuenta de usuario
     */
    public function account()
    {
        $this->data['page_title'] = 'Mi Cuenta';
        return view('frontend/account', $this->data);
    }

    /**
     * Página de wishlist
     */
    public function wishlist()
    {
        // Verificar si el usuario está logueado
        if (!session()->get('user_id')) {
            return redirect()->to('/login')->with('message', 'Debes iniciar sesión para ver tu lista de deseos');
        }

        $this->data['page_title'] = 'Lista de Deseos';
        $this->data['page_description'] = 'Gestiona tus productos favoritos y recibe notificaciones de precios';

        // Obtener estadísticas básicas para la vista
        $wishlistModel = new \App\Models\WishlistModel();
        $userId = session()->get('user_id');

        $this->data['wishlist_stats'] = $wishlistModel->getWishlistStats($userId);
        $this->data['user_id'] = $userId;

        return view('frontend/wishlist_modern', $this->data);
    }

    /**
     * Página de login
     */
    public function login()
    {
        $this->data['page_title'] = 'Iniciar Sesión';
        return view('frontend/login', $this->data);
    }

    /**
     * Página de registro
     */
    public function register()
    {
        $this->data['page_title'] = 'Registrarse';
        return view('frontend/register', $this->data);
    }

    /**
     * Página de recuperación de contraseña
     */
    public function forgotPassword()
    {
        $this->data['page_title'] = 'Recuperar Contraseña';
        return view('frontend/forgot_password', $this->data);
    }

    /**
     * Página de reseteo de contraseña
     */
    public function resetPassword()
    {
        $this->data['page_title'] = 'Restablecer Contraseña';
        return view('frontend/reset_password', $this->data);
    }

    // ========================================
    // MÉTODOS AUXILIARES
    // ========================================

    /**
     * Obtener productos destacados
     */
    private function getFeaturedProducts($limit = 8)
    {
        try {
            $db = \Config\Database::connect();

            $products = $db->table('products p')
                          ->select('p.*, c.name as category_name, c.slug as category_slug, b.name as brand_name, b.slug as brand_slug')
                          ->join('categories c', 'c.id = p.category_id', 'left')
                          ->join('brands b', 'b.id = p.brand_id', 'left')
                          ->where('p.is_active', 1)
                          ->where('p.is_featured', 1)
                          ->where('p.deleted_at IS NULL')
                          ->orderBy('p.created_at', 'DESC')
                          ->limit($limit)
                          ->get()
                          ->getResultArray();

            // Formatear productos
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $products;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo productos destacados: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener todos los productos para la tienda
     */
    private function getAllProducts($limit = 50)
    {
        try {
            $db = \Config\Database::connect();

            $products = $db->table('products p')
                          ->select('p.*, c.name as category_name, c.slug as category_slug, b.name as brand_name, b.slug as brand_slug')
                          ->join('categories c', 'c.id = p.category_id', 'left')
                          ->join('brands b', 'b.id = p.brand_id', 'left')
                          ->where('p.is_active', 1)
                          ->where('p.deleted_at IS NULL')
                          ->orderBy('p.is_featured', 'DESC')
                          ->orderBy('p.created_at', 'DESC')
                          ->limit($limit)
                          ->get()
                          ->getResultArray();

            // Formatear productos
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $products;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo productos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener categorías
     */
    private function getCategories()
    {
        try {
            $db = \Config\Database::connect();

            // Obtener todas las categorías activas
            $categories = $db->table('categories')
                            ->select('id, name, slug, icon, description, parent_id')
                            ->where('is_active', 1)
                            ->orderBy('sort_order', 'ASC')
                            ->orderBy('name', 'ASC')
                            ->get()
                            ->getResultArray();

            // Organizar en estructura jerárquica
            return $this->buildCategoryTree($categories);

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo categorías: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Construir árbol de categorías
     */
    private function buildCategoryTree($categories, $parentId = null, $level = 0)
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['level'] = $level;
                $category['children'] = $this->buildCategoryTree($categories, $category['id'], $level + 1);
                $tree[] = $category;
            }
        }

        return $tree;
    }

    /**
     * Obtener marcas
     */
    private function getBrands()
    {
        try {
            $db = \Config\Database::connect();

            $brands = $db->table('brands')
                        ->select('id, name, slug, logo')
                        ->where('is_active', 1)
                        ->orderBy('sort_order', 'ASC')
                        ->orderBy('name', 'ASC')
                        ->get()
                        ->getResultArray();

            return $brands;

        } catch (\Exception $e) {
            log_message('error', 'Error obteniendo marcas: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener banners
     */
    private function getBanners()
    {
        return [
            [
                'title' => 'iPhone 15 Pro',
                'subtitle' => 'Disponible ahora',
                'image' => 'banner1.jpg'
            ]
        ];
    }

    /**
     * Obtener cantidad de items en carrito
     */
    private function getCartCount()
    {
        $session = session();
        $cart = $session->get('cart') ?? [];
        return count($cart);
    }

    /**
     * Obtener items del carrito
     */
    private function getCartItems()
    {
        $session = session();
        return $session->get('cart') ?? [];
    }

    /**
     * Obtener total del carrito
     */
    private function getCartTotal()
    {
        $items = $this->getCartItems();
        $total = 0;
        foreach ($items as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        return $total;
    }

    /**
     * Obtener cantidad de items en wishlist
     */
    private function getWishlistCount()
    {
        $session = session();
        $wishlist = $session->get('wishlist') ?? [];
        return count($wishlist);
    }

    /**
     * Obtener items del wishlist
     */
    private function getWishlistItems()
    {
        $session = session();
        return $session->get('wishlist') ?? [];
    }

    /**
     * Página de contacto
     */
    public function contact()
    {
        $this->data['title'] = 'Contacto - ' . $this->frontendConfig->siteName;
        $this->data['page_title'] = 'Contacto';
        $this->data['contact_info'] = [
            'email' => $this->frontendConfig->contactEmail,
            'phone' => $this->frontendConfig->contactPhone,
            'whatsapp' => $this->frontendConfig->contactWhatsapp,
            'address' => $this->frontendConfig->contactAddress,
            'business_hours' => $this->frontendConfig->businessHours,
            'social_media' => $this->frontendConfig->socialMedia
        ];

        return view('frontend/contact', $this->data);
    }

    /**
     * Procesar formulario de contacto
     */
    public function sendContact()
    {
        $validation = \Config\Services::validation();

        $validation->setRules([
            'name' => 'required|min_length[2]|max_length[100]|alpha_space',
            'email' => 'required|valid_email|max_length[255]',
            'subject' => 'required|min_length[5]|max_length[200]',
            'message' => 'required|min_length[10]|max_length[2000]',
            'phone' => 'permit_empty|max_length[20]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        try {
            // Cargar modelo de mensajes de contacto
            $contactModel = new \App\Models\ContactMessageModel();

            // Preparar datos para guardar
            $contactData = [
                'name' => trim(strip_tags($this->request->getPost('name'))),
                'email' => trim(strtolower($this->request->getPost('email'))),
                'subject' => trim(strip_tags($this->request->getPost('subject'))),
                'message' => trim(strip_tags($this->request->getPost('message'))),
                'phone' => trim($this->request->getPost('phone')) ?: null,
                'ip_address' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'pending',
                'priority' => 'normal'
            ];

            // Guardar mensaje en la base de datos
            $messageId = $contactModel->insert($contactData);

            if ($messageId) {
                // Log del mensaje de contacto
                log_message('info', "Nuevo mensaje de contacto: ID {$messageId}, Email {$contactData['email']}, IP {$contactData['ip_address']}");

                // Aquí se podría enviar notificación por email al admin
                // $this->sendContactNotificationToAdmin($contactData);

                return redirect()->back()->with('success', 'Mensaje enviado correctamente. Te contactaremos pronto.');
            } else {
                throw new \Exception('Error al guardar el mensaje');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al procesar mensaje de contacto: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error al enviar el mensaje. Inténtalo de nuevo.');
        }
    }

    /**
     * Obtener productos filtrados
     */
    private function getFilteredProducts($categoryFilter = null, $searchFilter = null, $priceMin = null, $priceMax = null, $limit = 20)
    {
        try {
            $db = \Config\Database::connect();

            // Si no hay filtros, usar el método getAllProducts existente
            if (!$categoryFilter && !$searchFilter && !$priceMin && !$priceMax) {
                log_message('debug', 'getFilteredProducts: Sin filtros, usando getAllProducts');
                return $this->getAllProducts($limit);
            }

            log_message('debug', 'getFilteredProducts: Aplicando filtros');

            // Construir query con filtros usando los nombres de columna correctos
            $builder = $db->table('products p');
            $builder->select('p.*, c.name as category_name, c.slug as category_slug, b.name as brand_name, b.slug as brand_slug');
            $builder->join('categories c', 'c.id = p.category_id', 'left');
            $builder->join('brands b', 'b.id = p.brand_id', 'left');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');

            // Filtro por categoría
            if ($categoryFilter) {
                $builder->groupStart();
                $builder->where('c.id', $categoryFilter);
                $builder->orWhere('c.slug', $categoryFilter);
                $builder->groupEnd();
                log_message('debug', 'getFilteredProducts: Filtro categoría aplicado: ' . $categoryFilter);
            }

            // Filtro por búsqueda
            if ($searchFilter) {
                $builder->groupStart();
                $builder->like('p.name', $searchFilter);
                $builder->orLike('p.description', $searchFilter);
                $builder->orLike('p.sku', $searchFilter);
                $builder->groupEnd();
                log_message('debug', 'getFilteredProducts: Filtro búsqueda aplicado: ' . $searchFilter);
            }

            // Filtro por precio mínimo
            if ($priceMin !== null && is_numeric($priceMin)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) >=', $priceMin);
                log_message('debug', 'getFilteredProducts: Filtro precio mínimo aplicado: ' . $priceMin);
            }

            // Filtro por precio máximo
            if ($priceMax !== null && is_numeric($priceMax)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) <=', $priceMax);
                log_message('debug', 'getFilteredProducts: Filtro precio máximo aplicado: ' . $priceMax);
            }

            $builder->orderBy('p.is_featured', 'DESC');
            $builder->orderBy('p.created_at', 'DESC');
            $builder->limit($limit);

            $products = $builder->get()->getResultArray();

            log_message('debug', 'getFilteredProducts: Productos encontrados: ' . count($products));

            // Formatear productos igual que getAllProducts
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $products;

        } catch (\Exception $e) {
            log_message('error', 'Error al obtener productos filtrados: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener nombre de categoría por ID o slug
     */
    private function getCategoryName($categoryId)
    {
        $db = \Config\Database::connect();

        try {
            $result = $db->query("SELECT name FROM categories WHERE (id = ? OR slug = ?) AND is_active = 1 LIMIT 1",
                                [$categoryId, $categoryId]);
            $category = $result->getRowArray();
            return $category ? $category['name'] : null;

        } catch (\Exception $e) {
            log_message('error', 'Error al obtener nombre de categoría: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener total de productos con filtros aplicados
     */
    private function getTotalProductsWithFilters($categoryFilter = null, $searchFilter = null, $priceMin = null, $priceMax = null)
    {
        try {
            $db = \Config\Database::connect();

            $builder = $db->table('products p');
            $builder->select('COUNT(*) as total');
            $builder->join('categories c', 'c.id = p.category_id', 'left');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');

            // Aplicar filtros
            if ($categoryFilter) {
                if (is_numeric($categoryFilter)) {
                    $builder->where('c.id', $categoryFilter);
                } else {
                    $builder->where('c.slug', $categoryFilter);
                }
            }

            if ($searchFilter) {
                $builder->groupStart();
                $builder->like('p.name', $searchFilter);
                $builder->orLike('p.description', $searchFilter);
                $builder->orLike('p.sku', $searchFilter);
                $builder->groupEnd();
            }

            if ($priceMin !== null && is_numeric($priceMin)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) >=', $priceMin);
            }

            if ($priceMax !== null && is_numeric($priceMax)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) <=', $priceMax);
            }

            $result = $builder->get()->getRowArray();
            return (int) $result['total'];

        } catch (\Exception $e) {
            log_message('error', 'Error al obtener total de productos: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Obtener productos con paginación usando stored procedure cuando sea posible
     */
    private function getProductsWithPagination($categoryFilter = null, $searchFilter = null, $priceMin = null, $priceMax = null, $sortBy = 'newest', $limit = 20, $offset = 0)
    {
        try {
            $db = \Config\Database::connect();

            // Si no hay filtros complejos, usar el stored procedure optimizado
            if (!$searchFilter && !$priceMin && !$priceMax) {
                log_message('debug', 'getProductsWithPagination: Usando stored procedure');

                // Convertir categoryFilter a ID si es necesario
                $categoryId = null;
                if ($categoryFilter) {
                    if (is_numeric($categoryFilter)) {
                        $categoryId = (int) $categoryFilter;
                    } else {
                        // Es un slug, convertir a ID
                        $categoryResult = $db->query("SELECT id FROM categories WHERE slug = ? AND is_active = 1 LIMIT 1", [$categoryFilter]);
                        $categoryData = $categoryResult->getRowArray();
                        $categoryId = $categoryData ? (int) $categoryData['id'] : null;
                    }
                }

                log_message('debug', 'getProductsWithPagination: categoryId=' . $categoryId . ', limit=' . $limit . ', offset=' . $offset);

                $query = $db->query("CALL sp_get_public_product_catalog(?, ?, ?, ?, ?, ?, ?)", [
                    $categoryId, null, null, null, $sortBy, $limit, $offset
                ]);

                $products = $query->getResultArray();

                log_message('debug', 'getProductsWithPagination: SP devolvió ' . count($products) . ' productos');

                // Obtener ratings y datos adicionales
                $this->enrichProductData($products);

                return $products;
            }

            // Para filtros complejos, usar query builder
            log_message('debug', 'getProductsWithPagination: Usando query builder para filtros complejos');

            $builder = $db->table('products p');
            $builder->select('p.*, c.name as category_name, c.slug as category_slug, b.name as brand_name, b.slug as brand_slug');
            $builder->join('categories c', 'c.id = p.category_id', 'left');
            $builder->join('brands b', 'b.id = p.brand_id', 'left');
            $builder->where('p.is_active', 1);
            $builder->where('p.deleted_at IS NULL');

            // Aplicar filtros
            if ($categoryFilter) {
                if (is_numeric($categoryFilter)) {
                    $builder->where('c.id', $categoryFilter);
                } else {
                    $builder->where('c.slug', $categoryFilter);
                }
            }

            if ($searchFilter) {
                $builder->groupStart();
                $builder->like('p.name', $searchFilter);
                $builder->orLike('p.description', $searchFilter);
                $builder->orLike('p.sku', $searchFilter);
                $builder->groupEnd();
            }

            if ($priceMin !== null && is_numeric($priceMin)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) >=', $priceMin);
            }

            if ($priceMax !== null && is_numeric($priceMax)) {
                $builder->where('COALESCE(p.price_sale, p.price_regular) <=', $priceMax);
            }

            // Aplicar ordenamiento
            switch ($sortBy) {
                case 'price_low':
                    $builder->orderBy('COALESCE(p.price_sale, p.price_regular)', 'ASC');
                    break;
                case 'price_high':
                    $builder->orderBy('COALESCE(p.price_sale, p.price_regular)', 'DESC');
                    break;
                case 'name':
                    $builder->orderBy('p.name', 'ASC');
                    break;
                case 'rating':
                    $builder->orderBy('p.rating_average', 'DESC');
                    break;
                case 'newest':
                default:
                    $builder->orderBy('p.is_featured', 'DESC');
                    $builder->orderBy('p.created_at', 'DESC');
                    break;
            }

            $builder->limit($limit, $offset);
            $products = $builder->get()->getResultArray();

            // Formatear productos
            foreach ($products as &$product) {
                $product['price'] = $product['price_regular'];
                $product['sale_price'] = $product['price_sale'];
                $product['image'] = $product['featured_image'] ? base_url($product['featured_image']) : null;
                $product['featured'] = (bool)$product['is_featured'];

                if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                    $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
                }
            }

            return $products;

        } catch (\Exception $e) {
            log_message('error', 'Error al obtener productos con paginación: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Enriquecer datos de productos obtenidos del stored procedure
     */
    private function enrichProductData(&$products)
    {
        if (empty($products)) return;

        try {
            $db = \Config\Database::connect();

            // Obtener IDs de productos
            $productIds = array_column($products, 'id');

            // Obtener ratings actualizados
            $ratings = [];
            if (!empty($productIds)) {
                $ratingsQuery = $db->table('products')
                                  ->select('id, rating_average, rating_count')
                                  ->whereIn('id', $productIds)
                                  ->get();
                foreach ($ratingsQuery->getResultArray() as $rating) {
                    $ratings[$rating['id']] = $rating;
                }
            }

            // Obtener datos adicionales (category_id, brand_id, currency)
            $additionalData = [];
            if (!empty($productIds)) {
                $additionalQuery = $db->table('products p')
                                     ->select('p.id, p.category_id, p.brand_id, p.currency')
                                     ->whereIn('p.id', $productIds)
                                     ->get();
                foreach ($additionalQuery->getResultArray() as $data) {
                    $additionalData[$data['id']] = $data;
                }
            }

            // Enriquecer productos
            foreach ($products as &$product) {
                $productId = $product['id'];

                // Agregar ratings
                if (isset($ratings[$productId])) {
                    $product['rating_average'] = $ratings[$productId]['rating_average'];
                    $product['rating_count'] = $ratings[$productId]['rating_count'];
                }

                // Agregar datos adicionales
                if (isset($additionalData[$productId])) {
                    $product['category_id'] = $additionalData[$productId]['category_id'];
                    $product['brand_id'] = $additionalData[$productId]['brand_id'];
                    $product['currency'] = $additionalData[$productId]['currency'];
                }
            }

        } catch (\Exception $e) {
            log_message('error', 'Error al enriquecer datos de productos: ' . $e->getMessage());
        }
    }
}
