<?php

namespace App\Services;

class CountryService
{
    /**
     * Lista de países con códigos telefónicos
     */
    public static function getCountries()
    {
        return [
            'GT' => [
                'name' => 'Guatemala',
                'code' => '502',
                'flag' => '🇬🇹',
                'format' => '#### ####'
            ],
            'AT' => [
                'name' => 'Austria',
                'code' => '43',
                'flag' => '🇦🇹',
                'format' => '### ### ####'
            ],
            'US' => [
                'name' => 'Estados Unidos',
                'code' => '1',
                'flag' => '🇺🇸',
                'format' => '(###) ###-####'
            ],
            'MX' => [
                'name' => 'México',
                'code' => '52',
                'flag' => '🇲🇽',
                'format' => '## #### ####'
            ],
            'ES' => [
                'name' => 'España',
                'code' => '34',
                'flag' => '🇪🇸',
                'format' => '### ### ###'
            ],
            'CR' => [
                'name' => 'Costa Rica',
                'code' => '506',
                'flag' => '🇨🇷',
                'format' => '#### ####'
            ],
            'SV' => [
                'name' => 'El Salvador',
                'code' => '503',
                'flag' => '🇸🇻',
                'format' => '#### ####'
            ],
            'HN' => [
                'name' => 'Honduras',
                'code' => '504',
                'flag' => '🇭🇳',
                'format' => '#### ####'
            ],
            'NI' => [
                'name' => 'Nicaragua',
                'code' => '505',
                'flag' => '🇳🇮',
                'format' => '#### ####'
            ],
            'PA' => [
                'name' => 'Panamá',
                'code' => '507',
                'flag' => '🇵🇦',
                'format' => '#### ####'
            ],
            'BZ' => [
                'name' => 'Belice',
                'code' => '501',
                'flag' => '🇧🇿',
                'format' => '###-####'
            ],
            'DE' => [
                'name' => 'Alemania',
                'code' => '49',
                'flag' => '🇩🇪',
                'format' => '### ### ####'
            ],
            'FR' => [
                'name' => 'Francia',
                'code' => '33',
                'flag' => '🇫🇷',
                'format' => '## ## ## ## ##'
            ],
            'IT' => [
                'name' => 'Italia',
                'code' => '39',
                'flag' => '🇮🇹',
                'format' => '### ### ####'
            ],
            'GB' => [
                'name' => 'Reino Unido',
                'code' => '44',
                'flag' => '🇬🇧',
                'format' => '#### ### ####'
            ],
            'CA' => [
                'name' => 'Canadá',
                'code' => '1',
                'flag' => '🇨🇦',
                'format' => '(###) ###-####'
            ],
            'AR' => [
                'name' => 'Argentina',
                'code' => '54',
                'flag' => '🇦🇷',
                'format' => '## #### ####'
            ],
            'CO' => [
                'name' => 'Colombia',
                'code' => '57',
                'flag' => '🇨🇴',
                'format' => '### ### ####'
            ],
            'PE' => [
                'name' => 'Perú',
                'code' => '51',
                'flag' => '🇵🇪',
                'format' => '### ### ###'
            ],
            'CL' => [
                'name' => 'Chile',
                'code' => '56',
                'flag' => '🇨🇱',
                'format' => '# #### ####'
            ]
        ];
    }

    /**
     * Detectar país por IP usando múltiples servicios
     */
    public static function detectCountryByIP($ip = null)
    {
        try {
            // Si no se proporciona IP, usar la IP del cliente
            if (!$ip) {
                $ip = self::getRealClientIP();
            }

            // Para desarrollo local, intentar detectar IP real primero
            if (self::isLocalIP($ip)) {
                // Intentar obtener IP pública real
                $realIP = self::getPublicIP();
                if ($realIP && !self::isLocalIP($realIP)) {
                    $ip = $realIP;
                } else {
                    // Si no se puede obtener IP pública, usar Guatemala por defecto
                    return 'GT';
                }
            }

            // Intentar múltiples servicios de geolocalización
            $countryCode = self::tryMultipleGeoServices($ip);

            if ($countryCode) {
                // Verificar si el país está en nuestra lista
                $countries = self::getCountries();
                if (isset($countries[$countryCode])) {
                    return $countryCode;
                }
            }

            // Fallback a Guatemala si no se puede detectar
            return 'GT';

        } catch (\Exception $e) {
            // En caso de error, retornar Guatemala por defecto
            return 'GT';
        }
    }

    /**
     * Obtener la IP real del cliente
     */
    private static function getRealClientIP()
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Proxy/Load Balancer
            'HTTP_X_REAL_IP',            // Nginx
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Verificar si una IP es local/privada
     */
    private static function isLocalIP($ip)
    {
        return $ip === '127.0.0.1' ||
               $ip === '::1' ||
               strpos($ip, '192.168.') === 0 ||
               strpos($ip, '10.') === 0 ||
               strpos($ip, '172.') === 0 ||
               !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }

    /**
     * Obtener IP pública real (para desarrollo)
     */
    private static function getPublicIP()
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 3,
                    'user_agent' => 'MrCell-Guatemala/1.0'
                ]
            ]);

            $ip = @file_get_contents('https://api.ipify.org', false, $context);
            if ($ip && filter_var($ip, FILTER_VALIDATE_IP)) {
                return trim($ip);
            }
        } catch (\Exception $e) {
            // Ignorar errores
        }

        return null;
    }

    /**
     * Intentar múltiples servicios de geolocalización
     */
    private static function tryMultipleGeoServices($ip)
    {
        $services = [
            "http://ipapi.co/{$ip}/country/",
            "https://ipapi.co/{$ip}/country/",
            "http://ip-api.com/line/{$ip}?fields=countryCode",
            "https://ipinfo.io/{$ip}/country"
        ];

        foreach ($services as $url) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 3,
                        'user_agent' => 'MrCell-Guatemala/1.0'
                    ]
                ]);

                $response = @file_get_contents($url, false, $context);

                if ($response) {
                    $countryCode = strtoupper(trim($response));
                    if (strlen($countryCode) === 2 && ctype_alpha($countryCode)) {
                        return $countryCode;
                    }
                }
            } catch (\Exception $e) {
                continue;
            }
        }

        return null;
    }

    /**
     * Obtener información de un país por código
     */
    public static function getCountryInfo($countryCode)
    {
        $countries = self::getCountries();
        return $countries[$countryCode] ?? $countries['GT'];
    }

    /**
     * Formatear número de teléfono según el país
     */
    public static function formatPhoneNumber($phone, $countryCode)
    {
        $country = self::getCountryInfo($countryCode);
        $cleanPhone = preg_replace('/\D/', '', $phone);
        
        // Remover código de país si está presente
        if (str_starts_with($cleanPhone, $country['code'])) {
            $cleanPhone = substr($cleanPhone, strlen($country['code']));
        }

        // Aplicar formato según el país
        $format = $country['format'];
        $formatted = '';
        $phoneIndex = 0;

        for ($i = 0; $i < strlen($format) && $phoneIndex < strlen($cleanPhone); $i++) {
            if ($format[$i] === '#') {
                $formatted .= $cleanPhone[$phoneIndex];
                $phoneIndex++;
            } else {
                $formatted .= $format[$i];
            }
        }

        return $formatted;
    }

    /**
     * Debug completo de la detección de país
     */
    public static function debugCountryDetection()
    {
        $debug = [];

        // Información de IP
        $debug['raw_remote_addr'] = $_SERVER['REMOTE_ADDR'] ?? 'N/A';
        $debug['http_cf_connecting_ip'] = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? 'N/A';
        $debug['http_x_forwarded_for'] = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'N/A';
        $debug['http_x_real_ip'] = $_SERVER['HTTP_X_REAL_IP'] ?? 'N/A';

        // IP detectada
        $detectedIP = self::getRealClientIP();
        $debug['detected_client_ip'] = $detectedIP;
        $debug['is_local_ip'] = self::isLocalIP($detectedIP) ? 'YES' : 'NO';

        // Si es IP local, intentar obtener IP pública
        if (self::isLocalIP($detectedIP)) {
            $publicIP = self::getPublicIP();
            $debug['public_ip_lookup'] = $publicIP ?? 'FAILED';
            $finalIP = $publicIP && !self::isLocalIP($publicIP) ? $publicIP : $detectedIP;
        } else {
            $debug['public_ip_lookup'] = 'NOT_NEEDED';
            $finalIP = $detectedIP;
        }

        $debug['final_ip_used'] = $finalIP;

        // Intentar servicios de geolocalización
        $geoResults = [];
        $services = [
            'ipapi.co (http)' => "http://ipapi.co/{$finalIP}/country/",
            'ipapi.co (https)' => "https://ipapi.co/{$finalIP}/country/",
            'ip-api.com' => "http://ip-api.com/line/{$finalIP}?fields=countryCode",
            'ipinfo.io' => "https://ipinfo.io/{$finalIP}/country"
        ];

        foreach ($services as $name => $url) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'user_agent' => 'MrCell-Guatemala/1.0'
                    ]
                ]);

                $start = microtime(true);
                $response = @file_get_contents($url, false, $context);
                $time = round((microtime(true) - $start) * 1000, 2);

                if ($response !== false) {
                    $countryCode = strtoupper(trim($response));
                    $geoResults[$name] = [
                        'response' => $countryCode,
                        'time_ms' => $time,
                        'valid' => strlen($countryCode) === 2 && ctype_alpha($countryCode) ? 'YES' : 'NO'
                    ];
                } else {
                    $geoResults[$name] = [
                        'response' => 'FAILED',
                        'time_ms' => $time,
                        'valid' => 'NO'
                    ];
                }
            } catch (\Exception $e) {
                $geoResults[$name] = [
                    'response' => 'ERROR: ' . $e->getMessage(),
                    'time_ms' => 0,
                    'valid' => 'NO'
                ];
            }
        }

        $debug['geo_services_results'] = $geoResults;

        // Resultado final
        $finalCountryCode = self::detectCountryByIP();
        $debug['final_country_code'] = $finalCountryCode;

        // Verificar si está en nuestra lista
        $countries = self::getCountries();
        $debug['country_in_our_list'] = isset($countries[$finalCountryCode]) ? 'YES' : 'NO';

        // Información adicional del servidor
        $debug['server_info'] = [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'N/A',
            'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'N/A',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'N/A',
            'request_time' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME'] ?? time())
        ];

        return $debug;
    }
}
