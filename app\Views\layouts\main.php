<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? site_name() . ' - ' . site_description() ?></title>
    <meta name="description" content="<?= $description ?? site_description() ?>">
    <meta name="keywords" content="<?= $keywords ?? get_setting('site_keywords', 'celulares Guatemala, smartphones, accesorios móviles, tecnología, MrCell') ?>">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#dc2626">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="MrCell">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="MrCell">
    <meta name="msapplication-TileColor" content="#dc2626">
    <meta name="msapplication-TileImage" content="/logo.jpg">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Icons -->
    <link rel="icon" type="image/jpeg" href="/logo.jpg">
    <link rel="apple-touch-icon" href="/logo.jpg">
    <link rel="apple-touch-icon" sizes="152x152" href="/logo.jpg">
    <link rel="apple-touch-icon" sizes="180x180" href="/logo.jpg">
    <link rel="apple-touch-icon" sizes="167x167" href="/logo.jpg">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Estilos globales del frontend -->
    <style>
        :root {
            --primary-color: #dc2626;        /* Rojo principal */
            --primary-dark: #991b1b;         /* Rojo oscuro */
            --primary-light: #fca5a5;        /* Rojo claro */
            --secondary-color: #6b7280;      /* Gris medio */
            --success-color: #059669;        /* Verde para éxito */
            --warning-color: #d97706;        /* Naranja para advertencias */
            --danger-color: #dc2626;         /* Rojo para peligro */
            --info-color: #0891b2;           /* Azul para información */
            --light-color: #f9fafb;          /* Gris muy claro */
            --dark-color: #111827;           /* Negro/gris muy oscuro */
            --white-color: #ffffff;          /* Blanco puro */
            --gray-100: #f3f4f6;             /* Gris muy claro */
            --gray-200: #e5e7eb;             /* Gris claro */
            --gray-300: #d1d5db;             /* Gris claro medio */
            --gray-400: #9ca3af;             /* Gris medio */
            --gray-500: #6b7280;             /* Gris medio oscuro */
            --gray-600: #4b5563;             /* Gris oscuro */
            --gray-700: #374151;             /* Gris muy oscuro */
            --gray-800: #1f2937;             /* Casi negro */
            --gray-900: #111827;             /* Negro */
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--white-color);
        }

        /* Header Styles */
        .navbar {
            background-color: var(--white-color) !important;
            border-bottom: 2px solid var(--gray-200);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 40px;
            width: auto;
            margin-right: 0.5rem;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--gray-700) !important;
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary-color) !important;
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--primary-color) !important;
            font-weight: 600;
        }

        .navbar-nav .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background-color: var(--primary-color);
        }

        /* Product Card Styles */
        .product-card {
            transition: all 0.3s ease;
            border: 1px solid var(--gray-200);
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            height: 100%;
            background-color: var(--white-color);
            border-radius: 12px;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
            border-color: var(--primary-light);
        }

        .product-image {
            height: 200px;
            object-fit: cover;
            width: 100%;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.3s ease;
            font-weight: 600;
            border-radius: 8px;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background-color: transparent;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white-color);
            transform: translateY(-1px);
        }

        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
            color: var(--gray-200);
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .footer a {
            color: var(--gray-400);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer a:hover {
            color: var(--primary-color);
            transform: translateX(3px);
        }

        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: var(--gray-700);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background-color: var(--primary-color);
            transform: translateY(-2px);
        }

        /* Cart Badge */
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--primary-color);
            color: var(--white-color);
            border-radius: 50%;
            width: 22px;
            height: 22px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.25rem;
            }
            
            .product-card {
                margin-bottom: 1rem;
            }
        }
    </style>
    
    <!-- Estilos específicos de página -->
    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Header/Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?= base_url(site_logo()) ?>" alt="<?= site_name() ?> Logo">
                <?= site_name() ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Inicio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/tienda">Tienda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/categories">Categorías</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">Contacto</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="/carrito">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-badge" id="cart-count">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/cuenta">
                            <i class="fas fa-user"></i> Cuenta
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="<?= base_url(site_logo()) ?>" alt="<?= site_name() ?> Logo" style="height: 40px; width: auto; margin-right: 0.5rem;">
                        <h5 class="mb-0"><?= site_name() ?></h5>
                    </div>
                    <p><?= site_description() ?></p>
                    <div class="social-links">
                        <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                        <a href="https://wa.me/<?= str_replace(['+', ' ', '-'], '', contact_phone()) ?>" class="me-3"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                
                <div class="col-md-2 mb-4">
                    <h5>Tienda</h5>
                    <ul class="list-unstyled">
                        <li><a href="/tienda">Todos los productos</a></li>
                        <li><a href="/categories/smartphones">Smartphones</a></li>
                        <li><a href="/categories/accesorios">Accesorios</a></li>
                        <li><a href="/categories/tablets">Tablets</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2 mb-4">
                    <h5>Ayuda</h5>
                    <ul class="list-unstyled">
                        <li><a href="/contact">Contacto</a></li>
                        <li><a href="/shipping">Envíos</a></li>
                        <li><a href="/returns">Devoluciones</a></li>
                        <li><a href="/faq">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="col-md-4 mb-4">
                    <h5>Contacto</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i><?= get_setting('contact_address', 'Guatemala, Guatemala') ?></p>
                    <p><i class="fas fa-phone me-2"></i><?= contact_phone() ?></p>
                    <p><i class="fas fa-envelope me-2"></i><?= contact_email() ?></p>
                </div>
            </div>
            
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?= date('Y') ?> <?= site_name() ?>. Todos los derechos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="/privacy" class="me-3">Privacidad</a>
                    <a href="/terms">Términos</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Scripts específicos de página -->
    <?= $this->renderSection('scripts') ?>
    
    <!-- Script global del carrito -->
    <script>
        // Funcionalidad global del carrito
        async function updateCartCount() {
            try {
                const response = await fetch('<?= base_url('api/cart/count') ?>');
                const data = await response.json();

                if (data.status === 'success') {
                    const count = data.count || 0;

                    // Actualizar todos los elementos del contador del carrito
                    const cartCountElements = document.querySelectorAll('#cart-count, .cart-count, .cart-badge');
                    cartCountElements.forEach(element => {
                        element.textContent = count;
                        // Mostrar/ocultar el badge según si hay productos
                        if (count > 0) {
                            element.style.display = 'flex';
                        } else {
                            element.style.display = 'none';
                        }
                    });
                }
            } catch (error) {
                console.error('Error al actualizar contador del carrito:', error);
            }
        }

        // Función global para actualizar el contador (para usar desde otras páginas)
        window.updateCartCount = updateCartCount;

        // Inicializar al cargar la página
        document.addEventListener('DOMContentLoaded', updateCartCount);
    </script>

    <!-- PWA Scripts -->
    <script src="/assets/js/pwa-install.js"></script>

    <!-- PWA Install Banner -->
    <div id="pwa-install-banner" class="pwa-install-banner" style="display: none;">
        <div class="pwa-banner-content">
            <div class="pwa-banner-icon">
                <img src="/icon-192x192.png" alt="MrCell" width="40" height="40">
            </div>
            <div class="pwa-banner-text">
                <strong>Instalar MrCell</strong>
                <p>Instala nuestra app para una mejor experiencia</p>
            </div>
            <div class="pwa-banner-actions">
                <button onclick="installPWA()" class="btn btn-primary btn-sm">Instalar</button>
                <button onclick="dismissPWA()" class="btn btn-outline-secondary btn-sm">Ahora no</button>
            </div>
        </div>
    </div>

    <!-- PWA Banner Styles -->
    <style>
        .pwa-install-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ddd;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 15px;
        }

        .pwa-banner-content {
            display: flex;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            gap: 15px;
        }

        .pwa-banner-icon img {
            border-radius: 8px;
        }

        .pwa-banner-text {
            flex: 1;
        }

        .pwa-banner-text p {
            margin: 0;
            font-size: 0.9em;
            color: #666;
        }

        .pwa-banner-actions {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .pwa-banner-content {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .pwa-banner-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <!-- Modal del carrito -->
    <?= $this->include('components/cart_modal') ?>
</body>
</html>
