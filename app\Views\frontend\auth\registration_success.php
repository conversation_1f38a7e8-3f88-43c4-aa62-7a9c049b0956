<?= $this->extend('layouts/main') ?>

<?= $this->section('styles') ?>
<style>
    .success-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: -2rem -15px 0;
        padding: 2rem 15px;
    }

    .success-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
        max-width: 500px;
        width: 100%;
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        animation: pulse 2s infinite;
    }

    .success-icon i {
        font-size: 2.5rem;
        color: white;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .success-title {
        color: #333;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .success-subtitle {
        color: #666;
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .verification-notice {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .verification-notice h6 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .verification-notice p {
        color: #6c757d;
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .btn-verify {
        background: linear-gradient(135deg, #25D366, #128C7E);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-verify:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(37, 211, 102, 0.3);
        color: white;
    }

    .btn-skip {
        background: transparent;
        border: 2px solid #dee2e6;
        color: #6c757d;
        padding: 0.75rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-skip:hover {
        border-color: #adb5bd;
        color: #495057;
    }

    .whatsapp-icon {
        color: #25D366;
        margin-right: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="success-container">
    <div class="success-card">
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        
        <h1 class="success-title">¡Bienvenido a MrCell!</h1>
        <p class="success-subtitle">
            Tu cuenta ha sido creada exitosamente. Ahora puedes disfrutar de todos nuestros productos y servicios.
        </p>

        <div class="verification-notice">
            <h6><i class="fab fa-whatsapp whatsapp-icon"></i>Verificación de WhatsApp</h6>
            <p>
                Para recibir notificaciones importantes sobre tus pedidos por WhatsApp, 
                necesitamos verificar tu número de teléfono: <strong><?= $user_phone ?? 'No disponible' ?></strong>
            </p>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-verify" id="verifyNowBtn">
                <i class="fab fa-whatsapp me-2"></i>Verificar Ahora
            </button>
            <button type="button" class="btn btn-skip" id="skipVerificationBtn">
                Verificar Después
            </button>
        </div>

        <div class="mt-4">
            <small class="text-muted">
                Puedes verificar tu teléfono más tarde desde tu panel de usuario en 
                <strong>Cuenta > Seguridad</strong>
            </small>
        </div>
    </div>
</div>

<!-- Incluir modal de verificación -->
<div id="verificationModalContainer"></div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const verifyNowBtn = document.getElementById('verifyNowBtn');
    const skipVerificationBtn = document.getElementById('skipVerificationBtn');

    // Verificar ahora
    verifyNowBtn.addEventListener('click', function() {
        showPhoneVerificationModal();
    });

    // Saltar verificación
    skipVerificationBtn.addEventListener('click', function() {
        window.location.href = '<?= base_url('cuenta') ?>';
    });

    // Auto-mostrar modal si está configurado
    <?php if (isset($show_verification_modal) && $show_verification_modal): ?>
    setTimeout(() => {
        showPhoneVerificationModal();
    }, 1000);
    <?php endif; ?>
});

function showPhoneVerificationModal() {
    const verifyBtn = document.getElementById('verifyNowBtn');
    const originalText = verifyBtn.innerHTML;
    verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Cargando...';
    verifyBtn.disabled = true;

    fetch('<?= base_url('phone-verification/modal') ?>')
        .then(response => response.text())
        .then(html => {
            // Insertar modal en el contenedor
            const container = document.getElementById('verificationModalContainer');
            container.innerHTML = html;
            
            // Mostrar modal
            const modal = new bootstrap.Modal(container.querySelector('#phoneVerificationModal'));
            modal.show();
            
            // Limpiar cuando se cierre
            container.querySelector('#phoneVerificationModal').addEventListener('hidden.bs.modal', function() {
                container.innerHTML = '';
                
                // Preguntar si quiere ir al panel de usuario
                if (confirm('¿Deseas ir a tu panel de usuario ahora?')) {
                    window.location.href = '<?= base_url('cuenta') ?>';
                }
            });
        })
        .catch(error => {
            console.error('Error loading verification modal:', error);
            alert('Error cargando modal de verificación. Puedes verificar tu teléfono más tarde desde tu cuenta.');
            window.location.href = '<?= base_url('cuenta') ?>';
        })
        .finally(() => {
            verifyBtn.innerHTML = originalText;
            verifyBtn.disabled = false;
        });
}

// Función global para actualizar estado desde el modal
function updatePhoneVerificationStatus(verified) {
    if (verified) {
        // Mostrar mensaje de éxito y redirigir
        setTimeout(() => {
            alert('¡Teléfono verificado exitosamente! Ahora recibirás notificaciones por WhatsApp.');
            window.location.href = '<?= base_url('cuenta') ?>';
        }, 1000);
    }
}
</script>
<?= $this->endSection() ?>
