-- Agregar columnas de Recurrente a la tabla products
ALTER TABLE products 
ADD COLUMN recurrente_product_id VARCHAR(100) NULL COMMENT 'ID del producto en Recurrente' AFTER uuid,
ADD COLUMN recurrente_synced_at DATETIME NULL COMMENT 'Última fecha de sincronización con Recurrente' AFTER recurrente_product_id,
ADD COLUMN recurrente_sync_status ENUM('pending', 'synced', 'error', 'disabled') DEFAULT 'pending' COMMENT 'Estado de sincronización con Recurrente' AFTER recurrente_synced_at;

-- Agregar índices
ALTER TABLE products ADD INDEX idx_recurrente_product_id (recurrente_product_id);
ALTER TABLE products ADD INDEX idx_recurrente_sync_status (recurrente_sync_status);

-- Verificar que las columnas se agregaron correctamente
DESCRIBE products;
