<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class NotificationsApiSP extends ResourceController
{
    use ResponseTrait;

    protected $db;
    protected $format = 'json';

    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * Crear nueva notificación
     * POST /api/notifications
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'user_id' => 'permit_empty|integer',
                'type' => 'required|in_list[info,success,warning,error,stock_alert,order_alert,system]',
                'title' => 'required|max_length[255]',
                'message' => 'required|string',
                'data' => 'permit_empty',
                'action_url' => 'permit_empty|max_length[500]',
                'action_text' => 'permit_empty|max_length[100]',
                'is_global' => 'permit_empty|in_list[0,1]',
                'priority' => 'permit_empty|in_list[low,normal,high,urgent]',
                'expires_at' => 'permit_empty|valid_date'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Convertir data a JSON si es string
            $jsonData = null;
            if (!empty($data['data'])) {
                $jsonData = is_string($data['data']) ? $data['data'] : json_encode($data['data']);
            }

            // Llamar SP para crear notificación
            $query = $this->db->query("CALL sp_create_notification(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @notification_id, @result)", [
                $data['user_id'] ?? null,
                $data['type'],
                $data['title'],
                $data['message'],
                $jsonData,
                $data['action_url'] ?? null,
                $data['action_text'] ?? null,
                $data['is_global'] ?? 0,
                $data['priority'] ?? 'normal',
                $data['expires_at'] ?? null
            ]);

            // Obtener resultados
            $resultQuery = $this->db->query("SELECT @notification_id as notification_id, @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'data' => ['notification_id' => $result['notification_id']],
                    'message' => 'Notificación creada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::create: ' . $e->getMessage());
            return $this->failServerError('Error al crear notificación');
        }
    }

    /**
     * Obtener notificaciones del usuario
     * GET /api/notifications
     */
    public function index()
    {
        try {
            $userId = $this->request->getGet('user_id') ?? session()->get('user_id');
            $isRead = $this->request->getGet('is_read');
            $type = $this->request->getGet('type');
            $limit = (int) ($this->request->getGet('limit') ?? 50);
            $offset = (int) ($this->request->getGet('offset') ?? 0);

            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            // Convertir string a boolean
            if ($isRead !== null) {
                $isRead = $isRead === 'true' ? 1 : 0;
            }

            $query = $this->db->query("CALL sp_get_user_notifications(?, ?, ?, ?, ?)", [
                $userId, $isRead, $type, $limit, $offset
            ]);

            $notifications = $query->getResultArray();

            // Procesar datos JSON
            foreach ($notifications as &$notification) {
                if (!empty($notification['data'])) {
                    $notification['data'] = json_decode($notification['data'], true);
                }
            }

            return $this->respond([
                'status' => 'success',
                'data' => $notifications,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'total' => count($notifications)
                ],
                'message' => 'Notificaciones obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::index: ' . $e->getMessage());
            return $this->failServerError('Error al obtener notificaciones');
        }
    }

    /**
     * Marcar notificación como leída
     * PUT /api/notifications/{id}/read
     */
    public function markAsRead($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de notificación requerido');
            }

            $userId = $this->request->getJSON(true)['user_id'] ?? session()->get('user_id');

            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            // Llamar SP para marcar como leída
            $query = $this->db->query("CALL sp_mark_notification_read(?, ?, @result)", [
                $id, $userId
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Notificación marcada como leída'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::markAsRead: ' . $e->getMessage());
            return $this->failServerError('Error al marcar notificación como leída');
        }
    }

    /**
     * Marcar todas las notificaciones como leídas
     * PUT /api/notifications/read-all
     */
    public function markAllAsRead()
    {
        try {
            $userId = $this->request->getJSON(true)['user_id'] ?? session()->get('user_id');

            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            // Llamar SP para marcar todas como leídas
            $query = $this->db->query("CALL sp_mark_all_notifications_read(?, @result)", [$userId]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'Todas las notificaciones marcadas como leídas'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::markAllAsRead: ' . $e->getMessage());
            return $this->failServerError('Error al marcar todas las notificaciones como leídas');
        }
    }

    /**
     * Eliminar notificación
     * DELETE /api/notifications/{id}
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->failValidationError('ID de notificación requerido');
            }

            $userId = session()->get('user_id');

            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            // Llamar SP para eliminar
            $query = $this->db->query("CALL sp_delete_notification(?, ?, @result)", [
                $id, $userId
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondDeleted([
                    'status' => 'success',
                    'message' => 'Notificación eliminada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::delete: ' . $e->getMessage());
            return $this->failServerError('Error al eliminar notificación');
        }
    }

    /**
     * Obtener estadísticas de notificaciones
     * GET /api/notifications/stats
     */
    public function getStats()
    {
        try {
            $userId = $this->request->getGet('user_id') ?? session()->get('user_id');

            if (!$userId) {
                return $this->failValidationError('ID de usuario requerido');
            }

            $query = $this->db->query("CALL sp_get_notification_stats(?)", [$userId]);
            $stats = $query->getRowArray();

            return $this->respond([
                'status' => 'success',
                'data' => $stats,
                'message' => 'Estadísticas obtenidas correctamente'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::getStats: ' . $e->getMessage());
            return $this->failServerError('Error al obtener estadísticas');
        }
    }

    /**
     * Crear notificación de stock bajo
     * POST /api/notifications/stock-alert
     */
    public function createStockAlert()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'product_id' => 'required|integer',
                'current_stock' => 'required|integer'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para crear alerta de stock
            $query = $this->db->query("CALL sp_create_stock_alert_notification(?, ?, @result)", [
                $data['product_id'], $data['current_stock']
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => 'Alerta de stock creada correctamente'
                ]);
            } else {
                return $this->respond([
                    'status' => 'info',
                    'message' => $result['result']
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::createStockAlert: ' . $e->getMessage());
            return $this->failServerError('Error al crear alerta de stock');
        }
    }

    /**
     * Crear notificación de nuevo pedido
     * POST /api/notifications/order-alert
     */
    public function createOrderAlert()
    {
        try {
            $data = $this->request->getJSON(true);

            // Validaciones
            $validation = \Config\Services::validation();
            $validation->setRules([
                'order_id' => 'required|integer'
            ]);

            if (!$validation->run($data)) {
                return $this->failValidationErrors($validation->getErrors());
            }

            // Llamar SP para crear notificación de pedido
            $query = $this->db->query("CALL sp_create_order_notification(?, @result)", [
                $data['order_id']
            ]);

            // Obtener resultado
            $resultQuery = $this->db->query("SELECT @result as result");
            $result = $resultQuery->getRowArray();

            if (strpos($result['result'], 'SUCCESS') === 0) {
                return $this->respondCreated([
                    'status' => 'success',
                    'message' => 'Notificación de pedido creada correctamente'
                ]);
            } else {
                return $this->failValidationError($result['result']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error en NotificationsApiSP::createOrderAlert: ' . $e->getMessage());
            return $this->failServerError('Error al crear notificación de pedido');
        }
    }
}
