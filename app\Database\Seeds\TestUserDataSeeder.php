<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TestUserDataSeeder extends Seeder
{
    public function run()
    {
        try {
            // Buscar el usuario de prueba
            $user = $this->db->query("
                SELECT id FROM users 
                WHERE email = '<EMAIL>' 
                LIMIT 1
            ")->getRowArray();

            if (!$user) {
                echo "❌ Usuario de prueba no encontrado\n";
                return;
            }

            $userId = $user['id'];
            echo "👤 Usuario encontrado: ID {$userId}\n";

            // Crear algunos pedidos de prueba
            $orders = [
                [
                    'customer_id' => $userId,
                    'customer_name' => 'Enghelbert Calderon',
                    'customer_email' => '<EMAIL>',
                    'order_number' => 'MRC-' . date('Y') . '-TEST-001',
                    'status' => 'delivered',
                    'total' => 1250.00,
                    'subtotal' => 1250.00,
                    'payment_method' => 'card',
                    'payment_status' => 'paid',
                    'fulfillment_status' => 'fulfilled',
                    'source' => 'web',
                    'priority' => 'normal',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-30 days')),
                    'updated_at' => date('Y-m-d H:i:s', strtotime('-25 days'))
                ],
                [
                    'customer_id' => $userId,
                    'customer_name' => 'Enghelbert Calderon',
                    'customer_email' => '<EMAIL>',
                    'order_number' => 'MRC-' . date('Y') . '-TEST-002',
                    'status' => 'shipped',
                    'total' => 850.00,
                    'subtotal' => 850.00,
                    'payment_method' => 'transfer',
                    'payment_status' => 'paid',
                    'fulfillment_status' => 'partial',
                    'source' => 'web',
                    'priority' => 'normal',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-15 days')),
                    'updated_at' => date('Y-m-d H:i:s', strtotime('-10 days'))
                ]
            ];

            foreach ($orders as $order) {
                // Verificar si ya existe
                $existing = $this->db->query("
                    SELECT id FROM orders 
                    WHERE order_number = ? 
                    LIMIT 1
                ", [$order['order_number']])->getRowArray();

                if (!$existing) {
                    $this->db->table('orders')->insert($order);
                    $orderId = $this->db->insertID();
                    echo "📦 Pedido creado: {$order['order_number']} (ID: {$orderId})\n";

                    // Agregar algunos items al pedido
                    $products = $this->db->query("
                        SELECT id, name, price_sale 
                        FROM products 
                        WHERE is_active = 1 
                        LIMIT 3
                    ")->getResultArray();

                    foreach ($products as $index => $product) {
                        if ($index >= 2) break; // Solo 2 productos por pedido

                        $quantity = rand(1, 2);
                        $orderItem = [
                            'order_id' => $orderId,
                            'product_id' => $product['id'],
                            'product_name' => $product['name'],
                            'quantity' => $quantity,
                            'price' => $product['price_sale'],
                            'subtotal' => $product['price_sale'] * $quantity,
                            'created_at' => $order['created_at'],
                            'updated_at' => $order['updated_at']
                        ];

                        $this->db->table('order_items')->insert($orderItem);
                    }
                } else {
                    echo "⚠️  Pedido {$order['order_number']} ya existe\n";
                }
            }

            // Agregar algunos productos a wishlist
            $products = $this->db->query("
                SELECT id FROM products 
                WHERE is_active = 1 
                ORDER BY RAND() 
                LIMIT 5
            ")->getResultArray();

            foreach ($products as $product) {
                $existing = $this->db->query("
                    SELECT id FROM wishlist 
                    WHERE user_id = ? AND product_id = ?
                ", [$userId, $product['id']])->getRowArray();

                if (!$existing) {
                    $this->db->table('wishlist')->insert([
                        'user_id' => $userId,
                        'product_id' => $product['id'],
                        'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
                    ]);
                    echo "❤️ Producto agregado a wishlist: {$product['id']}\n";
                }
            }

            // Crear tabla de puntos de lealtad si no existe
            $this->db->query("
                CREATE TABLE IF NOT EXISTS user_loyalty_points (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    points INT NOT NULL,
                    reason VARCHAR(255),
                    status ENUM('active', 'used', 'expired') DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");

            // Agregar algunos puntos de lealtad
            $loyaltyPoints = [
                ['points' => 125, 'reason' => 'Compra pedido MRC-2025-001'],
                ['points' => 85, 'reason' => 'Compra pedido MRC-2025-002'],
                ['points' => 50, 'reason' => 'Registro en el sitio'],
                ['points' => 25, 'reason' => 'Primera compra bonus']
            ];

            foreach ($loyaltyPoints as $point) {
                $this->db->table('user_loyalty_points')->insert([
                    'user_id' => $userId,
                    'points' => $point['points'],
                    'reason' => $point['reason'],
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
                ]);
            }

            echo "🎯 Puntos de lealtad agregados: " . array_sum(array_column($loyaltyPoints, 'points')) . "\n";

            echo "\n✅ Datos de prueba creados exitosamente\n";

        } catch (\Exception $e) {
            echo "❌ Error creando datos de prueba: " . $e->getMessage() . "\n";
        }
    }
}
