<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-award me-2"></i>Gestión de Marcas</h1>
        <a href="/admin/brands/create" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nueva Marca
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Marcas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Logo</th>
                                <th>Nombre</th>
                                <th>Descripción</th>
                                <th>Productos</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($brands)): ?>
                                <?php foreach ($brands as $brand): ?>
                                <tr>
                                    <td><?= $brand['id'] ?></td>
                                    <td>
                                        <?php if (!empty($brand['logo'])): ?>
                                            <img src="/<?= esc($brand['logo']) ?>" alt="<?= esc($brand['name']) ?>" 
                                                 class="brand-logo" style="width: 40px; height: 40px; object-fit: contain;">
                                        <?php else: ?>
                                            <div class="brand-placeholder">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= esc($brand['name']) ?></strong>
                                        <?php if (!empty($brand['website'])): ?>
                                            <br><small><a href="<?= esc($brand['website']) ?>" target="_blank" class="text-muted">
                                                <i class="fas fa-external-link-alt me-1"></i><?= esc($brand['website']) ?>
                                            </a></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= esc($brand['description']) ?>
                                        <?php if (!empty($brand['email'])): ?>
                                            <br><small class="text-muted">
                                                <i class="fas fa-envelope me-1"></i><?= esc($brand['email']) ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= $brand['products_count'] ?></span>
                                    </td>
                                    <td>
                                        <?php if ($brand['is_active']): ?>
                                            <span class="badge bg-success">Activa</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactiva</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/admin/brands/edit/<?= $brand['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/admin/brands/toggle-status/<?= $brand['id'] ?>" 
                                               class="btn btn-sm btn-outline-<?= $brand['is_active'] ? 'warning' : 'success' ?>" 
                                               title="<?= $brand['is_active'] ? 'Desactivar' : 'Activar' ?>">
                                                <i class="fas fa-<?= $brand['is_active'] ? 'eye-slash' : 'eye' ?>"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" title="Eliminar" 
                                                    onclick="deleteBrand(<?= $brand['id'] ?>, '<?= esc($brand['name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-award fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No hay marcas registradas</h5>
                                        <p class="text-muted">Agrega tu primera marca para comenzar</p>
                                        <a href="/admin/brands/create" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Nueva Marca
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación de Eliminación -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirmar Eliminación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3">¿Estás seguro de que deseas eliminar la marca?</h6>
                <p class="text-muted mb-0" id="brandNameToDelete"></p>
                <small class="text-muted">Esta acción no se puede deshacer.</small>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>Eliminar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Carga -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content border-0 shadow">
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
                <h6 class="mb-2">Procesando...</h6>
                <p class="text-muted mb-0 small">Por favor espere un momento</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="resultModalLabel">
                    <i id="resultIcon" class="me-2"></i>
                    <span id="resultTitle"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i id="resultMainIcon" style="font-size: 3rem;"></i>
                </div>
                <p id="resultMessage" class="mb-0"></p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="resultOkBtn">
                    <i class="fas fa-check me-2"></i>Entendido
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Funcionalidad para gestión de marcas
    console.log('Gestión de marcas cargada');

    let brandToDelete = null;

    function deleteBrand(id, name) {
        // Guardar datos de la marca a eliminar
        brandToDelete = { id, name };
        
        // Mostrar el nombre en el modal
        document.getElementById('brandNameToDelete').innerHTML = `<strong>"${name}"</strong>`;
        
        // Mostrar modal de confirmación
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Manejar confirmación de eliminación
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!brandToDelete) return;

        // Cerrar modal de confirmación
        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        deleteModal.hide();

        // Mostrar modal de carga
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Realizar la eliminación
        fetch(`/admin/brands/delete/${brandToDelete.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Cerrar modal de carga
            loadingModal.hide();

            // Mostrar resultado
            showResultModal(data.success, data.message);

            // Si fue exitoso, recargar después de cerrar el modal
            if (data.success) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Cerrar modal de carga
            loadingModal.hide();
            
            // Mostrar error
            showResultModal(false, 'Error al eliminar la marca. Por favor, inténtalo de nuevo.');
        })
        .finally(() => {
            brandToDelete = null;
        });
    });

    function showResultModal(success, message) {
        const resultModal = document.getElementById('resultModal');
        const resultIcon = document.getElementById('resultIcon');
        const resultTitle = document.getElementById('resultTitle');
        const resultMainIcon = document.getElementById('resultMainIcon');
        const resultMessage = document.getElementById('resultMessage');
        const resultOkBtn = document.getElementById('resultOkBtn');

        if (success) {
            // Éxito
            resultIcon.className = 'fas fa-check-circle text-success me-2';
            resultTitle.textContent = 'Eliminación Exitosa';
            resultMainIcon.className = 'fas fa-check-circle text-success';
            resultMessage.textContent = message;
            resultOkBtn.className = 'btn btn-success';
            resultOkBtn.innerHTML = '<i class="fas fa-check me-2"></i>Perfecto';
        } else {
            // Error
            resultIcon.className = 'fas fa-exclamation-circle text-danger me-2';
            resultTitle.textContent = 'Error en la Eliminación';
            resultMainIcon.className = 'fas fa-exclamation-circle text-danger';
            resultMessage.textContent = message;
            resultOkBtn.className = 'btn btn-danger';
            resultOkBtn.innerHTML = '<i class="fas fa-times me-2"></i>Entendido';
        }

        const modal = new bootstrap.Modal(resultModal);
        modal.show();
    }
</script>

<style>
.brand-logo {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 4px;
}

.brand-placeholder {
    width: 40px;
    height: 40px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
<?= $this->endSection() ?>
