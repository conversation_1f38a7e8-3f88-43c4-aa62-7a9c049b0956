<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <!-- Fondo -->
    <rect width="100%" height="100%" fill="#2196F3"/>

    <!-- Gradiente sutil -->
    <defs>
        <linearGradient id="gradcelulares" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#2196F3;stop-opacity:0.8" />
        </linearGradient>
    </defs>
    <rect width="100%" height="100%" fill="url(#gradcelulares)"/>

    <!-- Icono -->
    <g transform="translate(176,110)">
        <svg width="48" height="48" viewBox="0 0 24 24">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z" fill="#FFFFFF" fill-opacity="0.9"/>
        </svg>
    </g>

    <!-- Texto -->
    <text x="200" y="180"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#FFFFFF"
          fill-opacity="0.95">
        Celulares
    </text>

    <!-- Sombra del texto -->
    <text x="201" y="181"
          font-family="Arial, sans-serif"
          font-size="24"
          font-weight="bold"
          text-anchor="middle"
          fill="#2196F3"
          fill-opacity="0.3">
        Celulares
    </text>
</svg>