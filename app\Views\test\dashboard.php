<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-body {
            padding: 30px;
        }
        
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .test-result.pass {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-result.fail {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: #e9ecef;
        }
        
        .progress-ring .progress {
            stroke: #28a745;
            stroke-dasharray: 314;
            stroke-dashoffset: 314;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-card">
            <div class="test-header">
                <h1><i class="fas fa-vial"></i> Sistema de Pruebas</h1>
                <h3>MrCell Guatemala - Verificación Completa</h3>
                <p class="mb-0">Verificando que todas las funcionalidades estén operativas</p>
            </div>
        </div>
        
        <!-- Estado del Sistema -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-database fa-3x <?= $system_status['database'] ? 'text-success' : 'text-danger' ?> mb-3"></i>
                    <h5>Base de Datos</h5>
                    <p class="mb-0">
                        <?php if ($system_status['database']): ?>
                            <span class="badge bg-success">Conectada</span><br>
                            <small><?= $system_status['tables_count'] ?? 0 ?> tablas</small>
                        <?php else: ?>
                            <span class="badge bg-danger">Error</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                    <h5>Configuraciones</h5>
                    <p class="mb-0">
                        <span class="badge bg-primary"><?= $system_status['settings_count'] ?? 0 ?></span><br>
                        <small>configuraciones</small>
                    </p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-comments fa-3x text-info mb-3"></i>
                    <h5>Templates</h5>
                    <p class="mb-0">
                        <span class="badge bg-info"><?= $system_status['templates_count'] ?? 0 ?></span><br>
                        <small>templates WhatsApp</small>
                    </p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-robot fa-3x text-warning mb-3"></i>
                    <h5>Automatizaciones</h5>
                    <p class="mb-0">
                        <span class="badge bg-warning">Listas</span><br>
                        <small>para ejecutar</small>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Panel de Control de Pruebas -->
        <div class="test-card">
            <div class="test-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fas fa-play-circle"></i> Panel de Control</h4>
                        <p>Ejecuta pruebas completas del sistema para verificar que todo funcione correctamente.</p>
                        
                        <div class="mb-3">
                            <button class="btn btn-primary btn-lg" id="run-tests" onclick="runAllTests()">
                                <i class="fas fa-play"></i> Ejecutar Todas las Pruebas
                            </button>
                            
                            <button class="btn btn-success btn-lg ms-2" onclick="goToAdmin()">
                                <i class="fas fa-tachometer-alt"></i> Ir al Panel de Admin
                            </button>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Categorías de Pruebas:</h6>
                            <ul class="mb-0">
                                <?php foreach ($test_categories as $key => $name): ?>
                                    <li><strong><?= $name ?>:</strong> Verificación de componentes <?= strtolower($name) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-center">
                        <div id="progress-container" style="display: none;">
                            <h5>Progreso de Pruebas</h5>
                            <div class="progress-ring">
                                <svg width="120" height="120">
                                    <circle class="background" cx="60" cy="60" r="50"></circle>
                                    <circle class="progress" cx="60" cy="60" r="50" id="progress-circle"></circle>
                                </svg>
                            </div>
                            <div id="progress-text" class="mt-2">
                                <h4 id="progress-percentage">0%</h4>
                                <p id="progress-status">Preparando...</p>
                            </div>
                        </div>
                        
                        <div id="loading-container" style="display: none;">
                            <div class="spinner mb-3"></div>
                            <p>Ejecutando pruebas...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Resultados de Pruebas -->
        <div id="test-results" style="display: none;">
            <div class="test-card">
                <div class="test-body">
                    <h4><i class="fas fa-chart-bar"></i> Resultados de las Pruebas</h4>
                    
                    <!-- Resumen -->
                    <div class="row mb-4" id="test-summary">
                        <!-- Se llenará dinámicamente -->
                    </div>
                    
                    <!-- Detalles por Categoría -->
                    <div id="test-details">
                        <!-- Se llenará dinámicamente -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Enlaces Rápidos -->
        <div class="test-card">
            <div class="test-body">
                <h4><i class="fas fa-link"></i> Enlaces Rápidos</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="list-group">
                            <a href="<?= base_url('admin/automation') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt"></i> Panel de Administración
                            </a>
                            <a href="<?= base_url('cron/status') ?>" class="list-group-item list-group-item-action" target="_blank">
                                <i class="fas fa-heartbeat"></i> Estado del Sistema
                            </a>
                            <a href="<?= base_url('cron/help') ?>" class="list-group-item list-group-item-action" target="_blank">
                                <i class="fas fa-question-circle"></i> Ayuda de Cron Jobs
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="list-group">
                            <a href="<?= base_url('admin/automation/cron-jobs') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-clock"></i> Configurar Cron Jobs
                            </a>
                            <a href="<?= base_url('admin/automation/logs') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-alt"></i> Ver Logs
                            </a>
                            <a href="<?= base_url('admin/automation/settings') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-cog"></i> Configuraciones
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function runAllTests() {
            // Mostrar loading
            document.getElementById('run-tests').disabled = true;
            document.getElementById('run-tests').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Ejecutando...';
            document.getElementById('loading-container').style.display = 'block';
            document.getElementById('progress-container').style.display = 'block';
            document.getElementById('test-results').style.display = 'none';
            
            // Ejecutar pruebas
            fetch('<?= base_url('test/run-all-tests') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading-container').style.display = 'none';
                
                if (data.success) {
                    displayTestResults(data.results);
                } else {
                    showError(data.error);
                }
                
                // Restaurar botón
                document.getElementById('run-tests').disabled = false;
                document.getElementById('run-tests').innerHTML = '<i class="fas fa-play"></i> Ejecutar Todas las Pruebas';
            })
            .catch(error => {
                document.getElementById('loading-container').style.display = 'none';
                showError('Error de conexión: ' + error.message);
                
                // Restaurar botón
                document.getElementById('run-tests').disabled = false;
                document.getElementById('run-tests').innerHTML = '<i class="fas fa-play"></i> Ejecutar Todas las Pruebas';
            });
        }
        
        function displayTestResults(results) {
            const summary = results.summary;
            const successRate = summary.success_rate;
            
            // Actualizar progreso circular
            const circle = document.getElementById('progress-circle');
            const circumference = 2 * Math.PI * 50;
            const offset = circumference - (successRate / 100) * circumference;
            circle.style.strokeDashoffset = offset;
            
            document.getElementById('progress-percentage').textContent = successRate + '%';
            document.getElementById('progress-status').textContent = 
                summary.passed_tests + '/' + summary.total_tests + ' pruebas exitosas';
            
            // Mostrar resumen
            const summaryHtml = `
                <div class="col-md-3">
                    <div class="status-card text-center">
                        <h3 class="text-success">${summary.passed_tests}</h3>
                        <p>Pruebas Exitosas</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card text-center">
                        <h3 class="text-danger">${summary.failed_tests}</h3>
                        <p>Pruebas Fallidas</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card text-center">
                        <h3 class="text-primary">${summary.total_tests}</h3>
                        <p>Total de Pruebas</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card text-center">
                        <h3 class="text-info">${successRate}%</h3>
                        <p>Tasa de Éxito</p>
                    </div>
                </div>
            `;
            
            document.getElementById('test-summary').innerHTML = summaryHtml;
            
            // Mostrar detalles por categoría
            let detailsHtml = '';
            
            Object.keys(results).forEach(key => {
                if (key !== 'summary') {
                    const category = results[key];
                    detailsHtml += `
                        <div class="mb-4">
                            <h5><i class="fas fa-folder"></i> ${category.category}</h5>
                            <div class="ms-3">
                    `;
                    
                    category.tests.forEach(test => {
                        detailsHtml += `
                            <div class="test-result ${test.status}">
                                <strong>${test.name}:</strong> ${test.message}
                            </div>
                        `;
                    });
                    
                    detailsHtml += '</div></div>';
                }
            });
            
            document.getElementById('test-details').innerHTML = detailsHtml;
            document.getElementById('test-results').style.display = 'block';
        }
        
        function showError(error) {
            document.getElementById('test-results').innerHTML = `
                <div class="test-card">
                    <div class="test-body">
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle"></i> Error en las Pruebas</h5>
                            <p>${error}</p>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('test-results').style.display = 'block';
        }
        
        function goToAdmin() {
            window.location.href = '<?= base_url('admin/automation') ?>';
        }
    </script>
</body>
</html>
