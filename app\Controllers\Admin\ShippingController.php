<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\ShippingManager;
use App\Libraries\AdvancedLogger;

/**
 * Controlador de Logística y Envíos
 * Panel de administración para gestión de envíos guatemaltecos
 */
class ShippingController extends BaseController
{
    private $shippingManager;
    protected $logger;
    protected $db;

    public function __construct()
    {
        $this->shippingManager = new ShippingManager();
        $this->logger = new AdvancedLogger();
        $this->db = \Config\Database::connect();
    }
    
    /**
     * Dashboard de envíos
     */
    public function index()
    {
        $data = [
            'title' => 'Gestión de Envíos - MrCell Guatemala',
            'shipping_stats' => $this->shippingManager->getShippingStats(),
            'pending_shipments' => $this->getPendingShipments(),
            'recent_deliveries' => $this->getRecentDeliveries(),
            'companies_performance' => $this->getCompaniesPerformance()
        ];
        
        return view('admin/shipping/dashboard', $data);
    }
    
    /**
     * Lista de envíos
     */
    public function shipments()
    {
        $filters = [
            'status' => $this->request->getGet('status'),
            'company' => $this->request->getGet('company'),
            'department' => $this->request->getGet('department'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'limit' => 50
        ];
        
        $shipments = $this->getAllShipments($filters);
        
        $data = [
            'title' => 'Lista de Envíos - MrCell Guatemala',
            'shipments' => $shipments,
            'filters' => $filters,
            'companies' => $this->getShippingCompanies(),
            'departments' => $this->getGuatemalaDepartments(),
            'statuses' => ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed', 'returned']
        ];
        
        return view('admin/shipping/shipments', $data);
    }
    
    /**
     * Crear envío
     */
    public function create($orderId = null)
    {
        if ($orderId) {
            $order = $this->getOrderById($orderId);
            if (!$order) {
                session()->setFlashdata('error', 'Orden no encontrada');
                return redirect()->to('/admin/orders');
            }
        }
        
        $data = [
            'title' => 'Crear Envío - MrCell Guatemala',
            'order' => $order ?? null,
            'companies' => $this->getShippingCompanies(),
            'departments' => $this->getGuatemalaDepartments(),
            'shipping_rates' => $this->shippingManager->getShippingRates()
        ];
        
        return view('admin/shipping/create', $data);
    }
    
    /**
     * Guardar envío
     */
    public function store()
    {
        try {
            $shipmentData = [
                'order_id' => (int)$this->request->getPost('order_id'),
                'shipping_company' => $this->request->getPost('shipping_company'),
                'service_type' => $this->request->getPost('service_type'),
                'recipient_name' => $this->request->getPost('recipient_name'),
                'recipient_phone' => $this->request->getPost('recipient_phone'),
                'recipient_email' => $this->request->getPost('recipient_email'),
                'destination_department' => $this->request->getPost('destination_department'),
                'destination_municipality' => $this->request->getPost('destination_municipality'),
                'destination_address' => $this->request->getPost('destination_address'),
                'destination_reference' => $this->request->getPost('destination_reference'),
                'weight' => (float)$this->request->getPost('weight'),
                'declared_value' => (float)$this->request->getPost('declared_value'),
                'shipping_cost' => (float)$this->request->getPost('shipping_cost'),
                'insurance_cost' => (float)$this->request->getPost('insurance_cost'),
                'cod_fee' => (float)$this->request->getPost('cod_fee'),
                'cash_on_delivery' => $this->request->getPost('cash_on_delivery') ? 1 : 0,
                'cod_amount' => (float)$this->request->getPost('cod_amount'),
                'special_instructions' => $this->request->getPost('special_instructions')
            ];
            
            $result = $this->shippingManager->createShipment($shipmentData);
            
            if ($result['success']) {
                session()->setFlashdata('success', 'Envío creado exitosamente. Número de seguimiento: ' . $result['tracking_number']);
                return redirect()->to('/admin/shipping/shipments');
            } else {
                session()->setFlashdata('error', $result['error']);
                return redirect()->back()->withInput();
            }
            
        } catch (\Exception $e) {
            session()->setFlashdata('error', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }
    
    /**
     * Ver detalles de envío
     */
    public function view($shipmentId)
    {
        $shipment = $this->getShipmentById($shipmentId);
        
        if (!$shipment) {
            session()->setFlashdata('error', 'Envío no encontrado');
            return redirect()->to('/admin/shipping/shipments');
        }
        
        $trackingInfo = $this->shippingManager->trackShipment($shipment['tracking_number']);
        
        $data = [
            'title' => 'Detalles del Envío - MrCell Guatemala',
            'shipment' => $shipment,
            'tracking_info' => $trackingInfo,
            'order' => $this->getOrderById($shipment['order_id'])
        ];
        
        return view('admin/shipping/view', $data);
    }
    
    /**
     * Actualizar estado de envío
     */
    public function updateStatus()
    {
        try {
            $shipmentId = (int)$this->request->getPost('shipment_id');
            $status = $this->request->getPost('status');
            $notes = $this->request->getPost('notes');
            
            $result = $this->shippingManager->updateShipmentStatus($shipmentId, $status, $notes);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Gestión de tarifas
     */
    public function rates()
    {
        $rates = $this->shippingManager->getShippingRates();
        
        $data = [
            'title' => 'Tarifas de Envío - MrCell Guatemala',
            'rates' => $rates['rates'] ?? [],
            'companies' => $this->getShippingCompanies(),
            'departments' => $this->getGuatemalaDepartments()
        ];
        
        return view('admin/shipping/rates', $data);
    }
    
    /**
     * Actualizar tarifa
     */
    public function updateRate()
    {
        try {
            $rateId = (int)$this->request->getPost('rate_id');
            $rateData = [
                'base_cost' => (float)$this->request->getPost('base_cost'),
                'cost_per_kg' => (float)$this->request->getPost('cost_per_kg'),
                'max_weight' => (float)$this->request->getPost('max_weight'),
                'estimated_days_min' => (int)$this->request->getPost('estimated_days_min'),
                'estimated_days_max' => (int)$this->request->getPost('estimated_days_max'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];
            
            $result = $this->shippingManager->updateShippingRate($rateId, $rateData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Crear nueva tarifa
     */
    public function createRate()
    {
        try {
            $rateData = [
                'company_id' => (int)$this->request->getPost('company_id'),
                'department_id' => (int)$this->request->getPost('department_id'),
                'service_type' => $this->request->getPost('service_type'),
                'base_cost' => (float)$this->request->getPost('base_cost'),
                'cost_per_kg' => (float)$this->request->getPost('cost_per_kg'),
                'max_weight' => (float)$this->request->getPost('max_weight'),
                'estimated_days_min' => (int)$this->request->getPost('estimated_days_min'),
                'estimated_days_max' => (int)$this->request->getPost('estimated_days_max'),
                'is_active' => 1
            ];
            
            $rateId = $this->db->table('shipping_rates')->insert($rateData);
            
            if ($rateId) {
                return $this->response->setJSON([
                    'success' => true,
                    'rate_id' => $rateId
                ]);
            }
            
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Failed to create rate'
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Calcular costo de envío
     */
    public function calculateCost()
    {
        try {
            $orderData = [
                'destination' => [
                    'department' => $this->request->getPost('department')
                ],
                'weight' => (float)$this->request->getPost('weight'),
                'value' => (float)$this->request->getPost('value')
            ];
            
            $result = $this->shippingManager->calculateShippingCost($orderData);
            
            return $this->response->setJSON($result);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Estadísticas de envíos
     */
    public function stats()
    {
        try {
            $days = (int)($this->request->getGet('days') ?? 30);
            $stats = $this->shippingManager->getShippingStats($days);
            
            return $this->response->setJSON($stats);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Exportar datos de envíos
     */
    public function export()
    {
        try {
            $type = $this->request->getPost('type') ?? 'shipments';
            $format = $this->request->getPost('format') ?? 'csv';
            
            switch ($type) {
                case 'shipments':
                    $data = $this->getAllShipments(['limit' => 1000]);
                    break;
                case 'rates':
                    $data = $this->shippingManager->getShippingRates()['rates'] ?? [];
                    break;
                default:
                    throw new \Exception("Unknown export type: $type");
            }
            
            $filename = $this->exportData($data, $type, $format);
            
            return $this->response->setJSON([
                'success' => true,
                'download_url' => base_url('exports/' . $filename),
                'filename' => $filename
            ]);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Métodos privados auxiliares
     */
    private function getPendingShipments(): array
    {
        try {
            return $this->db->table('shipments s')
                           ->select('s.*, o.total as order_total')
                           ->join('orders o', 'o.id = s.order_id', 'left')
                           ->where('s.status', 'pending')
                           ->orderBy('s.created_at', 'DESC')
                           ->limit(10)
                           ->get()
                           ->getResultArray();
        } catch (\Exception $e) {
            // Tabla shipments no existe, devolver array vacío
            return [];
        }
    }
    
    private function getRecentDeliveries(): array
    {
        try {
            return $this->db->table('shipments s')
                           ->select('s.*, o.total as order_total')
                           ->join('orders o', 'o.id = s.order_id', 'left')
                           ->where('s.status', 'delivered')
                           ->orderBy('s.delivered_at', 'DESC')
                           ->limit(10)
                           ->get()
                           ->getResultArray();
        } catch (\Exception $e) {
            // Tabla shipments no existe, devolver array vacío
            return [];
        }
    }
    
    private function getCompaniesPerformance(): array
    {
        try {
            return $this->db->query("
                SELECT
                    sc.name,
                    COUNT(s.id) as total_shipments,
                    SUM(CASE WHEN s.status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    AVG(CASE WHEN s.status = 'delivered' THEN DATEDIFF(s.delivered_at, s.created_at) END) as avg_delivery_days,
                    AVG(s.shipping_cost) as avg_cost
                FROM shipping_companies sc
                LEFT JOIN shipments s ON s.shipping_company = sc.code
                WHERE s.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY sc.id, sc.name
                ORDER BY total_shipments DESC
            ")->getResultArray();
        } catch (\Exception $e) {
            // Tablas de shipping no existen, devolver array vacío
            return [];
        }
    }
    
    private function getAllShipments(array $filters = []): array
    {
        $builder = $this->db->table('shipments s')
                           ->select('s.*, o.total as order_total, u.name as customer_name, sc.name as company_name')
                           ->join('orders o', 'o.id = s.order_id', 'left')
                           ->join('users u', 'u.id = o.user_id', 'left')
                           ->join('shipping_companies sc', 'sc.code = s.shipping_company', 'left');
        
        // Aplicar filtros
        if (!empty($filters['status'])) {
            $builder->where('s.status', $filters['status']);
        }
        
        if (!empty($filters['company'])) {
            $builder->where('s.shipping_company', $filters['company']);
        }
        
        if (!empty($filters['department'])) {
            $builder->where('s.destination_department', $filters['department']);
        }
        
        if (!empty($filters['date_from'])) {
            $builder->where('s.created_at >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('s.created_at <=', $filters['date_to']);
        }
        
        return $builder->orderBy('s.created_at', 'DESC')
                      ->limit($filters['limit'] ?? 50)
                      ->get()
                      ->getResultArray();
    }
    
    private function getShipmentById(int $shipmentId): ?array
    {
        return $this->db->table('shipments s')
                       ->select('s.*, o.total as order_total, u.name as customer_name, sc.name as company_name')
                       ->join('orders o', 'o.id = s.order_id', 'left')
                       ->join('users u', 'u.id = o.user_id', 'left')
                       ->join('shipping_companies sc', 'sc.code = s.shipping_company', 'left')
                       ->where('s.id', $shipmentId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getOrderById(int $orderId): ?array
    {
        return $this->db->table('orders o')
                       ->select('o.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone')
                       ->join('users u', 'u.id = o.user_id', 'left')
                       ->where('o.id', $orderId)
                       ->get()
                       ->getRowArray();
    }
    
    private function getShippingCompanies(): array
    {
        return $this->db->table('shipping_companies')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function getGuatemalaDepartments(): array
    {
        return $this->db->table('guatemala_departments')
                       ->where('is_active', 1)
                       ->orderBy('name')
                       ->get()
                       ->getResultArray();
    }
    
    private function exportData(array $data, string $type, string $format): string
    {
        $filename = "shipping_{$type}_" . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = WRITEPATH . 'exports/' . $filename;
        
        if (!is_dir(WRITEPATH . 'exports/')) {
            mkdir(WRITEPATH . 'exports/', 0755, true);
        }
        
        if ($format === 'csv') {
            $this->exportToCSV($data, $filepath);
        } else {
            file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
        }
        
        return $filename;
    }
    
    private function exportToCSV(array $data, string $filepath): void
    {
        if (empty($data)) return;
        
        $handle = fopen($filepath, 'w');
        
        // Headers
        fputcsv($handle, array_keys($data[0]));
        
        // Data
        foreach ($data as $row) {
            fputcsv($handle, array_values($row));
        }
        
        fclose($handle);
    }
}
