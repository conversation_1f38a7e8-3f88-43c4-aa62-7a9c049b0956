<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fab fa-facebook me-2"></i>Configuración de Social Manager
    </h1>
    <div>
        <a href="<?= base_url('admin/social/scheduled') ?>" class="btn btn-info">
            <i class="fas fa-calendar me-2"></i>Publicaciones Programadas
        </a>
        <button type="button" class="btn btn-success" onclick="testConnection()">
            <i class="fas fa-plug me-2"></i>Probar Conexión
        </button>
    </div>
</div>

<!-- Alertas -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check me-2"></i>
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Resultado de prueba de conexión -->
<?php if (!empty($test_connection_result)): ?>
    <div class="alert alert-<?= $test_connection_result['success'] ? 'success' : 'danger' ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $test_connection_result['success'] ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
        <strong>Prueba de Conexión:</strong> <?= esc($test_connection_result['message']) ?>
        <?php if (!empty($test_connection_result['details'])): ?>
            <br><small><?= esc($test_connection_result['details']) ?></small>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Publicaciones
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_posts'] ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-share-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pendientes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['pending_posts'] ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Publicadas
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['published_posts'] ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Fallidas
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['failed_posts'] ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form action="<?= base_url('admin/social/config/update') ?>" method="POST" id="socialConfigForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Configuración Principal -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>Configuración de API
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="api_url" class="form-label">URL de la API <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="api_url" name="api_url" 
                               value="<?= old('api_url', $config['api_url'] ?? 'https://socialmanager.mayansource.com/api') ?>" 
                               required placeholder="https://socialmanager.mayansource.com/api">
                        <small class="form-text text-muted">URL base de la API de Social Manager</small>
                    </div>

                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" 
                                   value="<?= old('api_key', $config['api_key'] ?? '') ?>" 
                                   required placeholder="Tu API Key de socialmanager.mayansource.com">
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('api_key')">
                                <i class="fas fa-eye" id="api_key_icon"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">Clave de API proporcionada por socialmanager.mayansource.com</small>
                    </div>
                </div>
            </div>

            <!-- Plataformas Sociales -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fab fa-facebook me-2"></i>Plataformas Habilitadas
                    </h6>
                </div>
                <div class="card-body">
                    <?php 
                    $enabledPlatforms = $config ? json_decode($config['enabled_platforms'], true) : [];
                    $platforms = [
                        'facebook' => ['name' => 'Facebook', 'icon' => 'fab fa-facebook', 'color' => 'primary'],
                        'instagram' => ['name' => 'Instagram', 'icon' => 'fab fa-instagram', 'color' => 'danger'],
                        'twitter' => ['name' => 'Twitter/X', 'icon' => 'fab fa-twitter', 'color' => 'info'],
                        'linkedin' => ['name' => 'LinkedIn', 'icon' => 'fab fa-linkedin', 'color' => 'primary'],
                        'tiktok' => ['name' => 'TikTok', 'icon' => 'fab fa-tiktok', 'color' => 'dark'],
                        'youtube' => ['name' => 'YouTube', 'icon' => 'fab fa-youtube', 'color' => 'danger']
                    ];
                    ?>
                    
                    <div class="row">
                        <?php foreach ($platforms as $key => $platform): ?>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="platform_<?= $key ?>" name="enabled_platforms[]" 
                                           value="<?= $key ?>" 
                                           <?= in_array($key, $enabledPlatforms) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="platform_<?= $key ?>">
                                        <i class="<?= $platform['icon'] ?> text-<?= $platform['color'] ?> me-2"></i>
                                        <?= $platform['name'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Configuración de Contenido -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>Configuración de Contenido
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="default_hashtags" class="form-label">Hashtags Predeterminados</label>
                        <textarea class="form-control" id="default_hashtags" name="default_hashtags" 
                                  rows="3" placeholder="#MrCell #Guatemala #Tecnologia #Celulares"><?= old('default_hashtags', $config['default_hashtags'] ?? '') ?></textarea>
                        <small class="form-text text-muted">Hashtags que se agregarán automáticamente a las publicaciones</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_new_product" class="form-label">Plantilla - Nuevo Producto</label>
                                <textarea class="form-control" id="template_new_product" name="template_new_product" 
                                          rows="3" placeholder="¡Nuevo producto disponible! {product_name} por solo Q{price}"><?= old('template_new_product', $config['content_templates']['new_product'] ?? '') ?></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_promotion" class="form-label">Plantilla - Promoción</label>
                                <textarea class="form-control" id="template_promotion" name="template_promotion" 
                                          rows="3" placeholder="🔥 ¡Oferta especial! {promotion_name} - {discount}% de descuento"><?= old('template_promotion', $config['content_templates']['promotion'] ?? '') ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel Lateral -->
        <div class="col-lg-4">
            <!-- Opciones de Publicación -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-toggle-on me-2"></i>Opciones de Publicación
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" <?= old('is_active', $config['is_active'] ?? 0) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                <strong>Activar Social Manager</strong>
                            </label>
                        </div>
                        <small class="form-text text-muted">Habilitar la funcionalidad de publicación automática</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="auto_publish" name="auto_publish" 
                                   value="1" <?= old('auto_publish', $config['auto_publish'] ?? 0) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="auto_publish">
                                Publicación Automática
                            </label>
                        </div>
                        <small class="form-text text-muted">Publicar automáticamente según horarios programados</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="publish_new_products" name="publish_new_products" 
                                   value="1" <?= old('publish_new_products', $config['publish_new_products'] ?? 0) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="publish_new_products">
                                Publicar Nuevos Productos
                            </label>
                        </div>
                        <small class="form-text text-muted">Publicar automáticamente cuando se agreguen productos</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="publish_promotions" name="publish_promotions" 
                                   value="1" <?= old('publish_promotions', $config['publish_promotions'] ?? 1) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="publish_promotions">
                                Publicar Promociones
                            </label>
                        </div>
                        <small class="form-text text-muted">Publicar automáticamente las promociones activas</small>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Configuración
                        </button>
                        <button type="button" class="btn btn-success" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i>Probar Conexión
                        </button>
                    </div>
                </div>
            </div>

            <!-- Estado Actual -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Estado Actual
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($config)): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Estado:</span>
                                <span class="badge bg-<?= $config['is_active'] ? 'success' : 'secondary' ?>">
                                    <?= $config['is_active'] ? 'Activo' : 'Inactivo' ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Auto-publicar:</span>
                                <span class="badge bg-<?= $config['auto_publish'] ? 'success' : 'secondary' ?>">
                                    <?= $config['auto_publish'] ? 'Activo' : 'Inactivo' ?>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Plataformas:</span>
                                <span class="badge bg-info">
                                    <?= count(json_decode($config['enabled_platforms'], true) ?: []) ?>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Última actualización:</span>
                                <small class="text-muted">
                                    <?= date('d/m/Y H:i', strtotime($config['updated_at'] ?? $config['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <p class="text-muted">No hay configuración guardada</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Ayuda -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-question-circle me-2"></i>Ayuda
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Configuración Inicial:</h6>
                        <ul class="list-unstyled small">
                            <li>1. Obtén tu API Key de socialmanager.mayansource.com</li>
                            <li>2. Selecciona las plataformas a usar</li>
                            <li>3. Configura plantillas de contenido</li>
                            <li>4. Activa la publicación automática</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary">Variables disponibles:</h6>
                        <ul class="list-unstyled small">
                            <li><code>{product_name}</code> - Nombre del producto</li>
                            <li><code>{price}</code> - Precio del producto</li>
                            <li><code>{promotion_name}</code> - Nombre de la promoción</li>
                            <li><code>{discount}</code> - Porcentaje de descuento</li>
                        </ul>
                    </div>

                    <a href="https://socialmanager.mayansource.com/docs" target="_blank" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-external-link-alt me-2"></i>Documentación
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Mostrar/ocultar contraseña
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Probar conexión
function testConnection() {
    const form = document.getElementById('socialConfigForm');
    const formData = new FormData(form);
    
    // Cambiar la acción del formulario temporalmente
    const originalAction = form.action;
    form.action = '<?= base_url('admin/social/config/test-connection') ?>';
    
    // Mostrar indicador de carga
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Probando...';
    button.disabled = true;
    
    // Enviar formulario
    form.submit();
}

// Validación del formulario
document.getElementById('socialConfigForm').addEventListener('submit', function(e) {
    const apiUrl = document.getElementById('api_url').value;
    const apiKey = document.getElementById('api_key').value;
    const platforms = document.querySelectorAll('input[name="enabled_platforms[]"]:checked');
    
    if (!apiUrl || !apiKey) {
        e.preventDefault();
        alert('Por favor completa la URL de API y API Key');
        return;
    }
    
    if (platforms.length === 0) {
        e.preventDefault();
        alert('Por favor selecciona al menos una plataforma social');
        return;
    }
});

// Auto-guardar configuración cada 5 minutos si hay cambios
let hasChanges = false;
const formInputs = document.querySelectorAll('#socialConfigForm input, #socialConfigForm select, #socialConfigForm textarea');

formInputs.forEach(input => {
    input.addEventListener('change', () => {
        hasChanges = true;
    });
});

// Advertir sobre cambios no guardados
window.addEventListener('beforeunload', function(e) {
    if (hasChanges) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Marcar como guardado al enviar
document.getElementById('socialConfigForm').addEventListener('submit', function() {
    hasChanges = false;
});
</script>
<?= $this->endSection() ?>
