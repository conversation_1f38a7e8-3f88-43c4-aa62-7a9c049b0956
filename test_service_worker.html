<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Service Worker - Mr<PERSON>ell</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Service Worker - MrCell</h1>
        
        <div class="test-section info">
            <h3>📊 Estado del Service Worker</h3>
            <p id="sw-status">Verificando...</p>
            <button onclick="checkServiceWorker()">Verificar Estado</button>
        </div>

        <div class="test-section">
            <h3>🌐 Pruebas de Peticiones</h3>
            <button onclick="testGETRequest()">Probar GET (debe cachear)</button>
            <button onclick="testPOSTRequest()">Probar POST (no debe cachear)</button>
            <button onclick="testPaymentRequest()">Simular Pago POST</button>
            <div id="request-results" class="log"></div>
        </div>

        <div class="test-section">
            <h3>💾 Estado del Cache</h3>
            <button onclick="checkCache()">Verificar Cache</button>
            <button onclick="clearCache()">Limpiar Cache</button>
            <div id="cache-results" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📱 Funcionalidades PWA</h3>
            <button onclick="testOfflineMode()">Simular Modo Offline</button>
            <button onclick="testPushNotification()">Test Push Notification</button>
            <div id="pwa-results" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📋 Log de Eventos</h3>
            <button onclick="clearLog()">Limpiar Log</button>
            <div id="event-log" class="log"></div>
        </div>
    </div>

    <script>
        let eventLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            eventLog.push(logEntry);
            
            const logElement = document.getElementById('event-log');
            logElement.innerHTML = eventLog.slice(-20).join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            eventLog = [];
            document.getElementById('event-log').innerHTML = '';
        }

        async function checkServiceWorker() {
            const statusElement = document.getElementById('sw-status');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    
                    if (registration) {
                        const sw = registration.active || registration.waiting || registration.installing;
                        statusElement.innerHTML = `
                            ✅ Service Worker registrado<br>
                            Estado: ${sw ? sw.state : 'unknown'}<br>
                            Scope: ${registration.scope}<br>
                            Script: ${sw ? sw.scriptURL : 'N/A'}
                        `;
                        statusElement.parentElement.className = 'test-section success';
                        log('Service Worker verificado exitosamente');
                    } else {
                        statusElement.innerHTML = '❌ Service Worker no registrado';
                        statusElement.parentElement.className = 'test-section error';
                        log('Service Worker no encontrado');
                    }
                } catch (error) {
                    statusElement.innerHTML = `❌ Error: ${error.message}`;
                    statusElement.parentElement.className = 'test-section error';
                    log(`Error verificando Service Worker: ${error.message}`);
                }
            } else {
                statusElement.innerHTML = '❌ Service Worker no soportado';
                statusElement.parentElement.className = 'test-section error';
                log('Service Worker no soportado en este navegador');
            }
        }

        async function testGETRequest() {
            const resultsElement = document.getElementById('request-results');
            log('Iniciando prueba GET...');
            
            try {
                const response = await fetch('/manifest.json');
                const data = await response.json();
                
                resultsElement.innerHTML += `
✅ GET Request exitoso:
   Status: ${response.status}
   Cached: ${response.headers.get('cache-control') || 'N/A'}
   Data: ${JSON.stringify(data, null, 2).substring(0, 100)}...

`;
                log(`GET request exitoso: ${response.status}`);
            } catch (error) {
                resultsElement.innerHTML += `❌ GET Request falló: ${error.message}\n\n`;
                log(`GET request falló: ${error.message}`);
            }
        }

        async function testPOSTRequest() {
            const resultsElement = document.getElementById('request-results');
            log('Iniciando prueba POST...');
            
            try {
                const response = await fetch('/api/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ test: 'data' })
                });
                
                resultsElement.innerHTML += `
✅ POST Request completado:
   Status: ${response.status}
   Method: POST (no debe ser cacheado)
   Response: ${response.statusText}

`;
                log(`POST request completado: ${response.status}`);
            } catch (error) {
                resultsElement.innerHTML += `❌ POST Request falló: ${error.message}\n\n`;
                log(`POST request falló: ${error.message}`);
            }
        }

        async function testPaymentRequest() {
            const resultsElement = document.getElementById('request-results');
            log('Simulando petición de pago...');
            
            try {
                const response = await fetch('/checkout/process-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        payment_method: 'recurrente',
                        amount: 100.00,
                        test: true 
                    })
                });
                
                resultsElement.innerHTML += `
🔒 Payment Request simulado:
   Status: ${response.status}
   Method: POST (pago - no debe ser cacheado)
   URL: /checkout/process-order
   Sin errores de Service Worker: ✅

`;
                log(`Payment request simulado: ${response.status} - Sin errores SW`);
            } catch (error) {
                resultsElement.innerHTML += `❌ Payment Request falló: ${error.message}\n\n`;
                log(`Payment request falló: ${error.message}`);
            }
        }

        async function checkCache() {
            const resultsElement = document.getElementById('cache-results');
            log('Verificando estado del cache...');
            
            try {
                const cacheNames = await caches.keys();
                resultsElement.innerHTML = `Caches disponibles:\n${cacheNames.join('\n')}\n\n`;
                
                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();
                    resultsElement.innerHTML += `${cacheName}: ${keys.length} recursos\n`;
                    
                    keys.slice(0, 5).forEach(request => {
                        resultsElement.innerHTML += `  - ${request.method} ${request.url}\n`;
                    });
                    
                    if (keys.length > 5) {
                        resultsElement.innerHTML += `  ... y ${keys.length - 5} más\n`;
                    }
                    resultsElement.innerHTML += '\n';
                }
                
                log(`Cache verificado: ${cacheNames.length} caches encontrados`);
            } catch (error) {
                resultsElement.innerHTML = `Error verificando cache: ${error.message}`;
                log(`Error verificando cache: ${error.message}`);
            }
        }

        async function clearCache() {
            const resultsElement = document.getElementById('cache-results');
            log('Limpiando cache...');
            
            try {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                resultsElement.innerHTML = `✅ Cache limpiado: ${cacheNames.length} caches eliminados\n`;
                log(`Cache limpiado: ${cacheNames.length} caches eliminados`);
            } catch (error) {
                resultsElement.innerHTML = `❌ Error limpiando cache: ${error.message}`;
                log(`Error limpiando cache: ${error.message}`);
            }
        }

        async function testOfflineMode() {
            const resultsElement = document.getElementById('pwa-results');
            log('Simulando modo offline...');
            
            // Simular desconexión
            resultsElement.innerHTML = `
🔌 Simulando modo offline...
📱 En una PWA real, el Service Worker manejaría:
   - Mostrar página offline para navegación
   - Cachear respuestas para peticiones GET
   - Devolver errores apropiados para POST
   - Sincronizar datos cuando vuelva la conexión

✅ Service Worker configurado para manejar offline
`;
            log('Modo offline simulado - SW configurado correctamente');
        }

        async function testPushNotification() {
            const resultsElement = document.getElementById('pwa-results');
            log('Probando notificaciones push...');
            
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                
                if (permission === 'granted') {
                    new Notification('🧪 Test MrCell', {
                        body: 'Service Worker funcionando correctamente',
                        icon: '/icon-192x192.png'
                    });
                    
                    resultsElement.innerHTML += `✅ Notificación enviada (permisos: ${permission})\n`;
                    log('Notificación push enviada exitosamente');
                } else {
                    resultsElement.innerHTML += `⚠️ Permisos de notificación: ${permission}\n`;
                    log(`Permisos de notificación: ${permission}`);
                }
            } else {
                resultsElement.innerHTML += `❌ Notificaciones no soportadas\n`;
                log('Notificaciones no soportadas en este navegador');
            }
        }

        // Inicializar al cargar la página
        document.addEventListener('DOMContentLoaded', () => {
            log('Test Service Worker iniciado');
            checkServiceWorker();
        });
    </script>
</body>
</html>
