/* ===================================
   ADMIN MODALS STYLES
   =================================== */

/* Modal de Confirmación de Eliminación */
#deleteModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#deleteModal .modal-header {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

#deleteModal .modal-title {
    font-weight: 600;
    color: #e53e3e;
}

#deleteModal .modal-body {
    padding: 2rem 1.5rem;
}

#deleteModal .fa-trash-alt {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Modal de Carga */
#loadingModal .modal-content {
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

#loadingModal .spinner-border {
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Modal de Resultado */
#resultModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#resultModal .modal-header.success {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
}

#resultModal .modal-header.error {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

#resultModal .modal-header {
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

#resultModal .modal-body {
    padding: 2rem 1.5rem;
}

/* Animaciones para iconos de resultado */
#resultModal .fa-check-circle {
    animation: successBounce 0.6s ease-out;
    color: #38a169 !important;
}

#resultModal .fa-exclamation-circle {
    animation: errorShake 0.6s ease-out;
    color: #e53e3e !important;
}

@keyframes successBounce {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* Efectos de hover para botones */
.modal-footer .btn {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.modal-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-footer .btn-danger:hover {
    background-color: #c53030;
    border-color: #c53030;
}

.modal-footer .btn-success:hover {
    background-color: #2f855a;
    border-color: #2f855a;
}

.modal-footer .btn-secondary:hover {
    background-color: #4a5568;
    border-color: #4a5568;
}

/* Overlay personalizado */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-body {
        padding: 1.5rem 1rem !important;
    }
    
    .modal-header {
        padding: 1rem !important;
    }
    
    .modal-footer {
        padding: 1rem !important;
    }
}

/* Loading dots animation */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Smooth transitions */
.modal.fade .modal-dialog {
    transition: transform 0.4s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Custom scrollbar for modal content */
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
